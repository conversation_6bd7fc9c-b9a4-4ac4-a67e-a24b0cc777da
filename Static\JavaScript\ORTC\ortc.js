!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).TRTC=t()}(this,(function(){function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?e(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(i,a){var o=e.apply(t,n);function s(e){r(o,i,a,s,c,"next",e)}function c(e){r(o,i,a,s,c,"throw",e)}s(void 0)}))}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}function d(e){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){return(l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function h(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function p(e,t,n){return(p=h()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&l(i,n.prototype),i}).apply(null,arguments)}function f(e){var t="function"==typeof Map?new Map:void 0;return(f=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return p(e,arguments,d(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),l(r,e)})(e)}function m(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function _(e){var t=h();return function(){var n,r=d(e);if(t){var i=d(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return m(this,n)}}function v(e,t,n){return(v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=d(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,a=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(c){s=!0,i=c}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return a}(e,t)||S(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e){return function(e){if(Array.isArray(e))return k(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||S(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){if(e){if("string"==typeof e)return k(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?k(e,t):void 0}}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function b(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=S(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function I(e,t,n,r,i){var a={};return Object.keys(r).forEach((function(e){a[e]=r[e]})),a.enumerable=!!a.enumerable,a.configurable=!!a.configurable,("value"in a||a.initializer)&&(a.writable=!0),a=n.slice().reverse().reduce((function(n,r){return r(e,t,n)||n}),a),i&&void 0!==a.initializer&&(a.value=a.initializer?a.initializer.call(i):void 0,a.initializer=void 0),void 0===a.initializer&&(Object.defineProperty(e,t,a),a=null),a}var R="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function T(e,t){return e(t={exports:{}},t.exports),t.exports}var E,w,C=function(e){return e&&e.Math==Math&&e},A=C("object"==typeof globalThis&&globalThis)||C("object"==typeof window&&window)||C("object"==typeof self&&self)||C("object"==typeof R&&R)||function(){return this}()||Function("return this")(),P=function(e){try{return!!e()}catch(t){return!0}},x=!P((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),D={}.propertyIsEnumerable,N=Object.getOwnPropertyDescriptor,L={f:N&&!D.call({1:2},1)?function(e){var t=N(this,e);return!!t&&t.enumerable}:D},O=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},M={}.toString,V=function(e){return M.call(e).slice(8,-1)},U="".split,F=P((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==V(e)?U.call(e,""):Object(e)}:Object,j=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},B=function(e){return F(j(e))},H=function(e){return"function"==typeof e},G=function(e){return"object"==typeof e?null!==e:H(e)},J=function(e){return H(e)?e:void 0},z=function(e,t){return arguments.length<2?J(A[e]):A[e]&&A[e][t]},W=z("navigator","userAgent")||"",q=A.process,K=A.Deno,Q=q&&q.versions||K&&K.version,X=Q&&Q.v8;X?w=(E=X.split("."))[0]<4?1:E[0]+E[1]:W&&(!(E=W.match(/Edge\/(\d+)/))||E[1]>=74)&&(E=W.match(/Chrome\/(\d+)/))&&(w=E[1]);var $=w&&+w,Y=!!Object.getOwnPropertySymbols&&!P((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&$&&$<41})),Z=Y&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ee=Z?function(e){return"symbol"==typeof e}:function(e){var t=z("Symbol");return H(t)&&Object(e)instanceof t},te=function(e){try{return String(e)}catch(t){return"Object"}},ne=function(e){if(H(e))return e;throw TypeError(te(e)+" is not a function")},re=function(e,t){var n=e[t];return null==n?void 0:ne(n)},ie=function(e,t){try{Object.defineProperty(A,e,{value:t,configurable:!0,writable:!0})}catch(n){A[e]=t}return t},ae=A["__core-js_shared__"]||ie("__core-js_shared__",{}),oe=T((function(e){(e.exports=function(e,t){return ae[e]||(ae[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.18.2",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),se=function(e){return Object(j(e))},ce={}.hasOwnProperty,ue=Object.hasOwn||function(e,t){return ce.call(se(e),t)},de=0,le=Math.random(),he=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++de+le).toString(36)},pe=oe("wks"),fe=A.Symbol,me=Z?fe:fe&&fe.withoutSetter||he,_e=function(e){return ue(pe,e)&&(Y||"string"==typeof pe[e])||(Y&&ue(fe,e)?pe[e]=fe[e]:pe[e]=me("Symbol."+e)),pe[e]},ve=_e("toPrimitive"),ge=function(e,t){if(!G(e)||ee(e))return e;var n,r=re(e,ve);if(r){if(void 0===t&&(t="default"),n=r.call(e,t),!G(n)||ee(n))return n;throw TypeError("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var n,r;if("string"===t&&H(n=e.toString)&&!G(r=n.call(e)))return r;if(H(n=e.valueOf)&&!G(r=n.call(e)))return r;if("string"!==t&&H(n=e.toString)&&!G(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}(e,t)},ye=function(e){var t=ge(e,"string");return ee(t)?t:String(t)},Se=A.document,ke=G(Se)&&G(Se.createElement),be=function(e){return ke?Se.createElement(e):{}},Ie=!x&&!P((function(){return 7!=Object.defineProperty(be("div"),"a",{get:function(){return 7}}).a})),Re=Object.getOwnPropertyDescriptor,Te={f:x?Re:function(e,t){if(e=B(e),t=ye(t),Ie)try{return Re(e,t)}catch(n){}if(ue(e,t))return O(!L.f.call(e,t),e[t])}},Ee=function(e){if(G(e))return e;throw TypeError(String(e)+" is not an object")},we=Object.defineProperty,Ce={f:x?we:function(e,t,n){if(Ee(e),t=ye(t),Ee(n),Ie)try{return we(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},Ae=x?function(e,t,n){return Ce.f(e,t,O(1,n))}:function(e,t,n){return e[t]=n,e},Pe=Function.toString;H(ae.inspectSource)||(ae.inspectSource=function(e){return Pe.call(e)});var xe,De,Ne,Le=ae.inspectSource,Oe=A.WeakMap,Me=H(Oe)&&/native code/.test(Le(Oe)),Ve=oe("keys"),Ue=function(e){return Ve[e]||(Ve[e]=he(e))},Fe={},je=A.WeakMap;if(Me||ae.state){var Be=ae.state||(ae.state=new je),He=Be.get,Ge=Be.has,Je=Be.set;xe=function(e,t){if(Ge.call(Be,e))throw new TypeError("Object already initialized");return t.facade=e,Je.call(Be,e,t),t},De=function(e){return He.call(Be,e)||{}},Ne=function(e){return Ge.call(Be,e)}}else{var ze=Ue("state");Fe[ze]=!0,xe=function(e,t){if(ue(e,ze))throw new TypeError("Object already initialized");return t.facade=e,Ae(e,ze,t),t},De=function(e){return ue(e,ze)?e[ze]:{}},Ne=function(e){return ue(e,ze)}}var We={set:xe,get:De,has:Ne,enforce:function(e){return Ne(e)?De(e):xe(e,{})},getterFor:function(e){return function(t){var n;if(!G(t)||(n=De(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},qe=Function.prototype,Ke=x&&Object.getOwnPropertyDescriptor,Qe=ue(qe,"name"),Xe={EXISTS:Qe,PROPER:Qe&&"something"===function(){}.name,CONFIGURABLE:Qe&&(!x||x&&Ke(qe,"name").configurable)},$e=T((function(e){var t=Xe.CONFIGURABLE,n=We.get,r=We.enforce,i=String(String).split("String");(e.exports=function(e,n,a,o){var s,c=!!o&&!!o.unsafe,u=!!o&&!!o.enumerable,d=!!o&&!!o.noTargetGet,l=o&&void 0!==o.name?o.name:n;H(a)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ue(a,"name")||t&&a.name!==l)&&Ae(a,"name",l),(s=r(a)).source||(s.source=i.join("string"==typeof l?l:""))),e!==A?(c?!d&&e[n]&&(u=!0):delete e[n],u?e[n]=a:Ae(e,n,a)):u?e[n]=a:ie(n,a)})(Function.prototype,"toString",(function(){return H(this)&&n(this).source||Le(this)}))})),Ye=Math.ceil,Ze=Math.floor,et=function(e){var t=+e;return t!=t||0===t?0:(t>0?Ze:Ye)(t)},tt=Math.max,nt=Math.min,rt=function(e,t){var n=et(e);return n<0?tt(n+t,0):nt(n,t)},it=Math.min,at=function(e){return e>0?it(et(e),9007199254740991):0},ot=function(e){return at(e.length)},st=function(e){return function(t,n,r){var i,a=B(t),o=ot(a),s=rt(r,o);if(e&&n!=n){for(;o>s;)if((i=a[s++])!=i)return!0}else for(;o>s;s++)if((e||s in a)&&a[s]===n)return e||s||0;return!e&&-1}},ct={includes:st(!0),indexOf:st(!1)},ut=ct.indexOf,dt=function(e,t){var n,r=B(e),i=0,a=[];for(n in r)!ue(Fe,n)&&ue(r,n)&&a.push(n);for(;t.length>i;)ue(r,n=t[i++])&&(~ut(a,n)||a.push(n));return a},lt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ht=lt.concat("length","prototype"),pt={f:Object.getOwnPropertyNames||function(e){return dt(e,ht)}},ft={f:Object.getOwnPropertySymbols},mt=z("Reflect","ownKeys")||function(e){var t=pt.f(Ee(e)),n=ft.f;return n?t.concat(n(e)):t},_t=function(e,t){for(var n=mt(t),r=Ce.f,i=Te.f,a=0;a<n.length;a++){var o=n[a];ue(e,o)||r(e,o,i(t,o))}},vt=/#|\.prototype\./,gt=function(e,t){var n=St[yt(e)];return n==bt||n!=kt&&(H(t)?P(t):!!t)},yt=gt.normalize=function(e){return String(e).replace(vt,".").toLowerCase()},St=gt.data={},kt=gt.NATIVE="N",bt=gt.POLYFILL="P",It=gt,Rt=Te.f,Tt=function(e,t){var n,r,i,a,o,s=e.target,c=e.global,u=e.stat;if(n=c?A:u?A[s]||ie(s,{}):(A[s]||{}).prototype)for(r in t){if(a=t[r],i=e.noTargetGet?(o=Rt(n,r))&&o.value:n[r],!It(c?r:s+(u?".":"#")+r,e.forced)&&void 0!==i){if(typeof a==typeof i)continue;_t(a,i)}(e.sham||i&&i.sham)&&Ae(a,"sham",!0),$e(n,r,a,e)}},Et=function(e,t,n){if(ne(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},wt=Array.isArray||function(e){return"Array"==V(e)},Ct={};Ct[_e("toStringTag")]="z";var At="[object z]"===String(Ct),Pt=_e("toStringTag"),xt="Arguments"==V(function(){return arguments}()),Dt=At?V:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),Pt))?n:xt?V(t):"Object"==(r=V(t))&&H(t.callee)?"Arguments":r},Nt=[],Lt=z("Reflect","construct"),Ot=/^\s*(?:class|function)\b/,Mt=Ot.exec,Vt=!Ot.exec((function(){})),Ut=function(e){if(!H(e))return!1;try{return Lt(Object,Nt,e),!0}catch(t){return!1}},Ft=!Lt||P((function(){var e;return Ut(Ut.call)||!Ut(Object)||!Ut((function(){e=!0}))||e}))?function(e){if(!H(e))return!1;switch(Dt(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Vt||!!Mt.call(Ot,Le(e))}:Ut,jt=_e("species"),Bt=function(e,t){return new(function(e){var t;return wt(e)&&(t=e.constructor,(Ft(t)&&(t===Array||wt(t.prototype))||G(t)&&null===(t=t[jt]))&&(t=void 0)),void 0===t?Array:t}(e))(0===t?0:t)},Ht=[].push,Gt=function(e){var t=1==e,n=2==e,r=3==e,i=4==e,a=6==e,o=7==e,s=5==e||a;return function(c,u,d,l){for(var h,p,f=se(c),m=F(f),_=Et(u,d,3),v=ot(m),g=0,y=l||Bt,S=t?y(c,v):n||o?y(c,0):void 0;v>g;g++)if((s||g in m)&&(p=_(h=m[g],g,f),e))if(t)S[g]=p;else if(p)switch(e){case 3:return!0;case 5:return h;case 6:return g;case 2:Ht.call(S,h)}else switch(e){case 4:return!1;case 7:Ht.call(S,h)}return a?-1:r||i?i:S}},Jt={forEach:Gt(0),map:Gt(1),filter:Gt(2),some:Gt(3),every:Gt(4),find:Gt(5),findIndex:Gt(6),filterReject:Gt(7)},zt=_e("species"),Wt=function(e){return $>=51||!P((function(){var t=[];return(t.constructor={})[zt]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},qt=Jt.map,Kt=Wt("map");Tt({target:"Array",proto:!0,forced:!Kt},{map:function(e){return qt(this,e,arguments.length>1?arguments[1]:void 0)}});var Qt=Jt.filter,Xt=Wt("filter");Tt({target:"Array",proto:!0,forced:!Xt},{filter:function(e){return Qt(this,e,arguments.length>1?arguments[1]:void 0)}});var $t=Te.f,Yt=P((function(){$t(1)}));Tt({target:"Object",stat:!0,forced:!x||Yt,sham:!x},{getOwnPropertyDescriptor:function(e,t){return $t(B(e),t)}});T((function(e){var t=function(e){var t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",o=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(E){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof l?t:l,a=Object.create(i.prototype),o=new I(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(i,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw a;return T()}for(n.method=i,n.arg=a;;){var o=n.delegate;if(o){var s=S(o,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=u(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,o),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(E){return{type:"throw",arg:E}}}e.wrap=c;var d={};function l(){}function h(){}function p(){}var f={};s(f,i,(function(){return this}));var m=Object.getPrototypeOf,_=m&&m(m(R([])));_&&_!==t&&n.call(_,i)&&(f=_);var v=p.prototype=l.prototype=Object.create(f);function g(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){var r;this._invoke=function(i,a){function o(){return new t((function(r,o){!function r(i,a,o,s){var c=u(e[i],e,a);if("throw"!==c.type){var d=c.arg,l=d.value;return l&&"object"==typeof l&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(l).then((function(e){d.value=e,o(d)}),(function(e){return r("throw",e,o,s)}))}s(c.arg)}(i,a,r,o)}))}return r=r?r.then(o,o):o()}}function S(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var r=u(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function b(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function R(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=p,s(v,"constructor",p),s(p,"constructor",h),h.displayName=s(p,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,s(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},g(y.prototype),s(y.prototype,a,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,r,i,a){void 0===a&&(a=Promise);var o=new y(c(t,n,r,i),a);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},g(v),s(v,o,"Generator"),s(v,i,(function(){return this})),s(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=R,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return o.type="throw",o.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),b(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;b(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:R(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}(e.exports);try{regeneratorRuntime=t}catch(n){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}));let Zt=!0,en=!0;function tn(e,t,n){const r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)}function nn(e,t,n){if(!e.RTCPeerConnection)return;const r=e.RTCPeerConnection.prototype,i=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return i.apply(this,arguments);const a=e=>{const t=n(e);t&&(r.handleEvent?r.handleEvent(t):r(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(r,a),i.apply(this,[e,a])};const a=r.removeEventListener;r.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[t])return a.apply(this,arguments);if(!this._eventMap[t].has(n))return a.apply(this,arguments);const r=this._eventMap[t].get(n);return this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,a.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function rn(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Zt=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function an(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(en=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function on(){if("object"==typeof window){if(Zt)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function sn(e,t){en&&console.warn(e+" is deprecated, please use "+t+" instead.")}function cn(e){return"[object Object]"===Object.prototype.toString.call(e)}function un(e){return cn(e)?Object.keys(e).reduce((function(t,n){const r=cn(e[n]),i=r?un(e[n]):e[n],a=r&&!Object.keys(i).length;return void 0===i||a?t:Object.assign(t,{[n]:i})}),{}):e}function dn(e,t,n){const r=n?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const a=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&a.push(e)}),a.forEach(t=>{e.forEach(n=>{n.type===r&&n.trackId===t.id&&function e(t,n,r){n&&!r.has(n.id)&&(r.set(n.id,n),Object.keys(n).forEach(i=>{i.endsWith("Id")?e(t,t.get(n[i]),r):i.endsWith("Ids")&&n[i].forEach(n=>{e(t,t.get(n),r)})}))}(e,n,i)})}),i}const ln=on;function hn(e,t){const n=e&&e.navigator;if(!n.mediaDevices)return;const r=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const r="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[i("min",n)]=r.ideal,t.optional.push(e),e={},e[i("max",n)]=r.ideal,t.optional.push(e)):(e[i("",n)]=r.ideal,t.optional.push(e))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",n)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,n)]=r[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"==typeof e.video){let a=e.video.facingMode;a=a&&("object"==typeof a?a:{ideal:a});const o=t.version<66;if(a&&("user"===a.exact||"environment"===a.exact||"user"===a.ideal||"environment"===a.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||o)){let t;if(delete e.video.facingMode,"environment"===a.exact||"environment"===a.ideal?t=["back","rear"]:"user"!==a.exact&&"user"!==a.ideal||(t=["front"]),t)return n.mediaDevices.enumerateDevices().then(n=>{let o=(n=n.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return!o&&n.length&&t.includes("back")&&(o=n[n.length-1]),o&&(e.video.deviceId=a.exact?{exact:o.deviceId}:{ideal:o.deviceId}),e.video=r(e.video),ln("chrome: "+JSON.stringify(e)),i(e)})}e.video=r(e.video)}return ln("chrome: "+JSON.stringify(e)),i(e)},a=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(n.getUserMedia=function(e,t,r){i(e,e=>{n.webkitGetUserMedia(e,t,e=>{r&&r(a(e))})})}.bind(n),n.mediaDevices.getUserMedia){const e=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(t){return i(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(a(e))))}}}function pn(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function fn(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.track.id):{track:n.track};const i=new Event("track");i.track=n.track,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.id):{track:n};const i=new Event("track");i.track=n,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else nn(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function mn(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){let i=n.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{const t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function _n(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,r]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach(e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{n[t]=e.stat(t)}),t[n.id]=n}),t},a=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};if(arguments.length>=2){const r=function(e){n(a(i(e)))};return t.apply(this,[r,e])}return new Promise((e,n)=>{t.apply(this,[function(t){e(a(i(t)))},n])}).then(n,r)}}function vn(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>dn(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),nn(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>dn(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,r;return this.getSenders().forEach(n=>{n.track===e&&(t?r=!0:t=n)}),this.getReceivers().forEach(t=>(t.track===e&&(n?r=!0:n=t),t.track===e)),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function gn(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();n.apply(this,arguments);const r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),i.apply(this,arguments)}}function yn(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return gn(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}r.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function a(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(i.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:n})}function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(r.id,"g"),i.id)}),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const r=[].slice.call(arguments,1);if(1!==r.length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find(e=>e.track===t);if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const a=this._streams[n.id];if(a)a.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const r=new e.MediaStream([t]);this._streams[n.id]=r,this._reverseStreams[r.id]=n,this.addStream(r)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=a(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then(e=>a(this,e))}};e.RTCPeerConnection.prototype[t]=r[t]}));const s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=o(this,arguments[0]),s.apply(this,arguments)):s.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:a(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(n=>{this._streams[n].getTracks().find(t=>e.track===t)&&(t=this._streams[n])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Sn(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]}))}function kn(e,t){nn(e,"negotiationneeded",e=>{const n=e.target;if(!(t.version<72||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e})}var bn=Object.freeze({__proto__:null,shimMediaStream:pn,shimOnTrack:fn,shimGetSendersWithDtmf:mn,shimGetStats:_n,shimSenderReceiverGetStats:vn,shimAddTrackRemoveTrackWithNative:gn,shimAddTrackRemoveTrack:yn,shimPeerConnection:Sn,fixNegotiationNeeded:kn,shimGetUserMedia:hn,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(t=>{const r=n.video&&n.video.width,i=n.video&&n.video.height,a=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:a||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(n)})}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});var In=T((function(e){var t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((function(e){return e.trim()}))},t.splitSections=function(e){return e.split("\nm=").map((function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"}))},t.getDescription=function(e){var n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){var n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter((function(e){return 0===e.indexOf(n)}))},t.parseCandidate=function(e){for(var t,n={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:n[t[r]]=t[r+1]}return n},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,n={},r=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<r.length;i++)n[(t=r[i].trim().split("="))[0].trim()]=t[1];return n},t.writeFmtp=function(e){var t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var r=[];Object.keys(e.parameters).forEach((function(t){e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)})),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((function(e){return parseInt(e,10)}))}},t.getMid=function(e){var n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach((function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),n},t.parseCryptoLine=function(e){var t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;var t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,n){return t.matchPrefix(e+n,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,n){var r=t.matchPrefix(e+n,"a=ice-ufrag:")[0],i=t.matchPrefix(e+n,"a=ice-pwd:")[0];return r&&i?{usernameFragment:r.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" "),i=3;i<r.length;i++){var a=r[i],o=t.matchPrefix(e,"a=rtpmap:"+a+" ")[0];if(o){var s=t.parseRtpMap(o),c=t.matchPrefix(e,"a=fmtp:"+a+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+a+" ").map(t.parseRtcpFb),n.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach((function(e){n.headerExtensions.push(t.parseExtmap(e))})),n},t.writeRtpDescription=function(e,n){var r="";r+="m="+e+" ",r+=n.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=n.codecs.map((function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType})).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach((function(e){r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)}));var i=0;return n.codecs.forEach((function(e){e.maxptime>i&&(i=e.maxptime)})),i>0&&(r+="a=maxptime:"+i+"\r\n"),r+="a=rtcp-mux\r\n",n.headerExtensions&&n.headerExtensions.forEach((function(e){r+=t.writeExtmap(e)})),r},t.parseRtpEncodingParameters=function(e){var n,r=[],i=t.parseRtpParameters(e),a=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute})),c=s.length>0&&s[0].ssrc,u=t.matchPrefix(e,"a=ssrc-group:FID").map((function(e){return e.substr(17).split(" ").map((function(e){return parseInt(e,10)}))}));u.length>0&&u[0].length>1&&u[0][0]===c&&(n=u[0][1]),i.codecs.forEach((function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&n&&(t.rtx={ssrc:n}),r.push(t),a&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&c&&r.push({ssrc:c});var d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substr(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substr(5),10)*.95-16e3:void 0,r.forEach((function(e){e.maxBitrate=d}))),r},t.parseRtcpParameters=function(e){var n={},r=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute}))[0];r&&(n.cname=r.value,n.ssrc=r.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=i.length>0,n.compound=0===i.length;var a=t.matchPrefix(e,"a=rtcp-mux");return n.mux=a.length>0,n},t.parseMsid=function(e){var n,r=t.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(n=r[0].substr(7).split(" "))[0],track:n[1]};var i=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"msid"===e.attribute}));return i.length>0?{stream:(n=i[0].value.split(" "))[0],track:n[1]}:void 0},t.parseSctpDescription=function(e){var n,r=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(n=parseInt(i[0].substr(19),10)),isNaN(n)&&(n=65536);var a=t.matchPrefix(e,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substr(12),10),protocol:r.fmt,maxMessageSize:n};if(t.matchPrefix(e,"a=sctpmap:").length>0){var o=t.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(o[0],10),protocol:o[1],maxMessageSize:n}}},t.writeSctpDescription=function(e,t){var n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,n,r){var i=void 0!==n?n:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,n,r,i){var a=t.writeRtpDescription(e.kind,n);if(a+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),a+="a=mid:"+e.mid+"\r\n",e.direction?a+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";a+="a="+o,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+o,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+o,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),a},t.getDirection=function(e,n){for(var r=t.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substr(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var n=t.splitLines(e)[0].substr(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){var n=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var n=t.splitLines(e),r=0;r<n.length;r++)if(n[r].length<2||"="!==n[r].charAt(1))return!1;return!0},e.exports=t}));function Rn(e,t,n,r,i){var a=In.writeRtpDescription(e.kind,t);if(a+=In.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=In.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":i||"active"),a+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=o;var s="msid:"+(r?r.id:"-")+" "+o+"\r\n";a+="a="+s,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+In.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+In.localCName+"\r\n"),a}function Tn(e,t){var n={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(e,t){e=parseInt(e,10);for(var n=0;n<t.length;n++)if(t[n].payloadType===e||t[n].preferredPayloadType===e)return t[n]},i=function(e,t,n,i){var a=r(e.parameters.apt,n),o=r(t.parameters.apt,i);return a&&o&&a.name.toLowerCase()===o.name.toLowerCase()};return e.codecs.forEach((function(r){for(var a=0;a<t.codecs.length;a++){var o=t.codecs[a];if(r.name.toLowerCase()===o.name.toLowerCase()&&r.clockRate===o.clockRate){if("rtx"===r.name.toLowerCase()&&r.parameters&&o.parameters.apt&&!i(r,o,e.codecs,t.codecs))continue;(o=JSON.parse(JSON.stringify(o))).numChannels=Math.min(r.numChannels,o.numChannels),n.codecs.push(o),o.rtcpFeedback=o.rtcpFeedback.filter((function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1}));break}}})),e.headerExtensions.forEach((function(e){for(var r=0;r<t.headerExtensions.length;r++){var i=t.headerExtensions[r];if(e.uri===i.uri){n.headerExtensions.push(i);break}}})),n}function En(e,t,n){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(n)}function wn(e,t){var n=e.getRemoteCandidates().find((function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type}));return n||e.addRemoteCandidate(t),!n}function Cn(e,t){var n=new Error(t);return n.name=e,n.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],n}var An=function(e,t){function n(t,n){n.addTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function r(t,n,r,i){var a=new Event("track");a.track=n,a.receiver=r,a.transceiver={receiver:r},a.streams=i,e.setTimeout((function(){t._dispatchEvent("track",a)}))}var i=function(n){var r=this,i=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach((function(e){r[e]=i[e].bind(i)})),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",n=JSON.parse(JSON.stringify(n||{})),this.usingBundle="max-bundle"===n.bundlePolicy,"negotiate"===n.rtcpMuxPolicy)throw Cn("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(n.rtcpMuxPolicy||(n.rtcpMuxPolicy="require"),n.iceTransportPolicy){case"all":case"relay":break;default:n.iceTransportPolicy="all"}switch(n.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:n.bundlePolicy="balanced"}if(n.iceServers=function(e,t){var n=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var r=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var i="string"==typeof r;return i&&(r=[r]),r=r.filter((function(e){return 0===e.indexOf("turn:")&&-1!==e.indexOf("transport=udp")&&-1===e.indexOf("turn:[")&&!n?(n=!0,!0):0===e.indexOf("stun:")&&t>=14393&&-1===e.indexOf("?transport=udp")})),delete e.url,e.urls=i?r[0]:r,!!r.length}}))}(n.iceServers||[],t),this._iceGatherers=[],n.iceCandidatePoolSize)for(var a=n.iceCandidatePoolSize;a>0;a--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:n.iceServers,gatherPolicy:n.iceTransportPolicy}));else n.iceCandidatePoolSize=0;this._config=n,this.transceivers=[],this._sdpSessionId=In.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};Object.defineProperty(i.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(i.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),i.prototype.onicecandidate=null,i.prototype.onaddstream=null,i.prototype.ontrack=null,i.prototype.onremovestream=null,i.prototype.onsignalingstatechange=null,i.prototype.oniceconnectionstatechange=null,i.prototype.onconnectionstatechange=null,i.prototype.onicegatheringstatechange=null,i.prototype.onnegotiationneeded=null,i.prototype.ondatachannel=null,i.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},i.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},i.prototype.getConfiguration=function(){return this._config},i.prototype.getLocalStreams=function(){return this.localStreams},i.prototype.getRemoteStreams=function(){return this.remoteStreams},i.prototype._createTransceiver=function(e,t){var n=this.transceivers.length>0,r={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&n)r.iceTransport=this.transceivers[0].iceTransport,r.dtlsTransport=this.transceivers[0].dtlsTransport;else{var i=this._createIceAndDtlsTransports();r.iceTransport=i.iceTransport,r.dtlsTransport=i.dtlsTransport}return t||this.transceivers.push(r),r},i.prototype.addTrack=function(t,n){if(this._isClosed)throw Cn("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find((function(e){return e.track===t})))throw Cn("InvalidAccessError","Track already exists.");for(var i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(r=this.transceivers[i]);return r||(r=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(n)&&this.localStreams.push(n),r.track=t,r.stream=n,r.rtpSender=new e.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},i.prototype.addStream=function(e){var n=this;if(t>=15025)e.getTracks().forEach((function(t){n.addTrack(t,e)}));else{var r=e.clone();e.getTracks().forEach((function(e,t){var n=r.getTracks()[t];e.addEventListener("enabled",(function(e){n.enabled=e.enabled}))})),r.getTracks().forEach((function(e){n.addTrack(e,r)}))}},i.prototype.removeTrack=function(t){if(this._isClosed)throw Cn("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var n=this.transceivers.find((function(e){return e.rtpSender===t}));if(!n)throw Cn("InvalidAccessError","Sender was not created by this connection.");var r=n.stream;n.rtpSender.stop(),n.rtpSender=null,n.track=null,n.stream=null,-1===this.transceivers.map((function(e){return e.stream})).indexOf(r)&&this.localStreams.indexOf(r)>-1&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},i.prototype.removeStream=function(e){var t=this;e.getTracks().forEach((function(e){var n=t.getSenders().find((function(t){return t.track===e}));n&&t.removeTrack(n)}))},i.prototype.getSenders=function(){return this.transceivers.filter((function(e){return!!e.rtpSender})).map((function(e){return e.rtpSender}))},i.prototype.getReceivers=function(){return this.transceivers.filter((function(e){return!!e.rtpReceiver})).map((function(e){return e.rtpReceiver}))},i.prototype._createIceGatherer=function(t,n){var r=this;if(n&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var i=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var n=!e.candidate||0===Object.keys(e.candidate).length;i.state=n?"completed":"gathering",null!==r.transceivers[t].bufferedCandidateEvents&&r.transceivers[t].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),i},i.prototype._gather=function(t,n){var r=this,i=this.transceivers[n].iceGatherer;if(!i.onlocalcandidate){var a=this.transceivers[n].bufferedCandidateEvents;this.transceivers[n].bufferedCandidateEvents=null,i.removeEventListener("localcandidate",this.transceivers[n].bufferCandidates),i.onlocalcandidate=function(e){if(!(r.usingBundle&&n>0)){var a=new Event("icecandidate");a.candidate={sdpMid:t,sdpMLineIndex:n};var o=e.candidate,s=!o||0===Object.keys(o).length;if(s)"new"!==i.state&&"gathering"!==i.state||(i.state="completed");else{"new"===i.state&&(i.state="gathering"),o.component=1,o.ufrag=i.getLocalParameters().usernameFragment;var c=In.writeCandidate(o);a.candidate=Object.assign(a.candidate,In.parseCandidate(c)),a.candidate.candidate=c,a.candidate.toJSON=function(){return{candidate:a.candidate.candidate,sdpMid:a.candidate.sdpMid,sdpMLineIndex:a.candidate.sdpMLineIndex,usernameFragment:a.candidate.usernameFragment}}}var u=In.getMediaSections(r._localDescription.sdp);u[a.candidate.sdpMLineIndex]+=s?"a=end-of-candidates\r\n":"a="+a.candidate.candidate+"\r\n",r._localDescription.sdp=In.getDescription(r._localDescription.sdp)+u.join("");var d=r.transceivers.every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}));"gathering"!==r.iceGatheringState&&(r.iceGatheringState="gathering",r._emitGatheringStateChange()),s||r._dispatchEvent("icecandidate",a),d&&(r._dispatchEvent("icecandidate",new Event("icecandidate")),r.iceGatheringState="complete",r._emitGatheringStateChange())}},e.setTimeout((function(){a.forEach((function(e){i.onlocalcandidate(e)}))}),0)}},i.prototype._createIceAndDtlsTransports=function(){var t=this,n=new e.RTCIceTransport(null);n.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()};var r=new e.RTCDtlsTransport(n);return r.ondtlsstatechange=function(){t._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:n,dtlsTransport:r}},i.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var n=this.transceivers[e].iceTransport;n&&(delete n.onicestatechange,delete this.transceivers[e].iceTransport);var r=this.transceivers[e].dtlsTransport;r&&(delete r.ondtlsstatechange,delete r.onerror,delete this.transceivers[e].dtlsTransport)},i.prototype._transceive=function(e,n,r){var i=Tn(e.localCapabilities,e.remoteCapabilities);n&&e.rtpSender&&(i.encodings=e.sendEncodingParameters,i.rtcp={cname:In.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(i.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(i)),r&&e.rtpReceiver&&i.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach((function(e){delete e.rtx})),e.recvEncodingParameters.length?i.encodings=e.recvEncodingParameters:i.encodings=[{}],i.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(i.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(i.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(i))},i.prototype.setLocalDescription=function(e){var t,n,r=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(Cn("TypeError",'Unsupported type "'+e.type+'"'));if(!En("setLocalDescription",e.type,r.signalingState)||r._isClosed)return Promise.reject(Cn("InvalidStateError","Can not set local "+e.type+" in state "+r.signalingState));if("offer"===e.type)t=In.splitSections(e.sdp),n=t.shift(),t.forEach((function(e,t){var n=In.parseRtpParameters(e);r.transceivers[t].localCapabilities=n})),r.transceivers.forEach((function(e,t){r._gather(e.mid,t)}));else if("answer"===e.type){t=In.splitSections(r._remoteDescription.sdp),n=t.shift();var i=In.matchPrefix(n,"a=ice-lite").length>0;t.forEach((function(e,t){var a=r.transceivers[t],o=a.iceGatherer,s=a.iceTransport,c=a.dtlsTransport,u=a.localCapabilities,d=a.remoteCapabilities;if(!(In.isRejected(e)&&0===In.matchPrefix(e,"a=bundle-only").length)&&!a.rejected){var l=In.getIceParameters(e,n),h=In.getDtlsParameters(e,n);i&&(h.role="server"),r.usingBundle&&0!==t||(r._gather(a.mid,t),"new"===s.state&&s.start(o,l,i?"controlling":"controlled"),"new"===c.state&&c.start(h));var p=Tn(u,d);r._transceive(a,p.codecs.length>0,!1)}}))}return r._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?r._updateSignalingState("have-local-offer"):r._updateSignalingState("stable"),Promise.resolve()},i.prototype.setRemoteDescription=function(i){var a=this;if(-1===["offer","answer"].indexOf(i.type))return Promise.reject(Cn("TypeError",'Unsupported type "'+i.type+'"'));if(!En("setRemoteDescription",i.type,a.signalingState)||a._isClosed)return Promise.reject(Cn("InvalidStateError","Can not set remote "+i.type+" in state "+a.signalingState));var o={};a.remoteStreams.forEach((function(e){o[e.id]=e}));var s=[],c=In.splitSections(i.sdp),u=c.shift(),d=In.matchPrefix(u,"a=ice-lite").length>0,l=In.matchPrefix(u,"a=group:BUNDLE ").length>0;a.usingBundle=l;var h=In.matchPrefix(u,"a=ice-options:")[0];return a.canTrickleIceCandidates=!!h&&h.substr(14).split(" ").indexOf("trickle")>=0,c.forEach((function(r,c){var h=In.splitLines(r),p=In.getKind(r),f=In.isRejected(r)&&0===In.matchPrefix(r,"a=bundle-only").length,m=h[0].substr(2).split(" ")[2],_=In.getDirection(r,u),v=In.parseMsid(r),g=In.getMid(r)||In.generateIdentifier();if(f||"application"===p&&("DTLS/SCTP"===m||"UDP/DTLS/SCTP"===m))a.transceivers[c]={mid:g,kind:p,protocol:m,rejected:!0};else{var y,S,k,b,I,R,T,E,w;!f&&a.transceivers[c]&&a.transceivers[c].rejected&&(a.transceivers[c]=a._createTransceiver(p,!0));var C,A,P=In.parseRtpParameters(r);f||(C=In.getIceParameters(r,u),(A=In.getDtlsParameters(r,u)).role="client"),T=In.parseRtpEncodingParameters(r);var x=In.parseRtcpParameters(r),D=In.matchPrefix(r,"a=end-of-candidates",u).length>0,N=In.matchPrefix(r,"a=candidate:").map((function(e){return In.parseCandidate(e)})).filter((function(e){return 1===e.component}));if(("offer"===i.type||"answer"===i.type)&&!f&&l&&c>0&&a.transceivers[c]&&(a._disposeIceAndDtlsTransports(c),a.transceivers[c].iceGatherer=a.transceivers[0].iceGatherer,a.transceivers[c].iceTransport=a.transceivers[0].iceTransport,a.transceivers[c].dtlsTransport=a.transceivers[0].dtlsTransport,a.transceivers[c].rtpSender&&a.transceivers[c].rtpSender.setTransport(a.transceivers[0].dtlsTransport),a.transceivers[c].rtpReceiver&&a.transceivers[c].rtpReceiver.setTransport(a.transceivers[0].dtlsTransport)),"offer"!==i.type||f){if("answer"===i.type&&!f){S=(y=a.transceivers[c]).iceGatherer,k=y.iceTransport,b=y.dtlsTransport,I=y.rtpReceiver,R=y.sendEncodingParameters,E=y.localCapabilities,a.transceivers[c].recvEncodingParameters=T,a.transceivers[c].remoteCapabilities=P,a.transceivers[c].rtcpParameters=x,N.length&&"new"===k.state&&(!d&&!D||l&&0!==c?N.forEach((function(e){wn(y.iceTransport,e)})):k.setRemoteCandidates(N)),l&&0!==c||("new"===k.state&&k.start(S,C,"controlling"),"new"===b.state&&b.start(A)),!Tn(y.localCapabilities,y.remoteCapabilities).codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&y.sendEncodingParameters[0].rtx&&delete y.sendEncodingParameters[0].rtx,a._transceive(y,"sendrecv"===_||"recvonly"===_,"sendrecv"===_||"sendonly"===_),!I||"sendrecv"!==_&&"sendonly"!==_?delete y.rtpReceiver:(w=I.track,v?(o[v.stream]||(o[v.stream]=new e.MediaStream),n(w,o[v.stream]),s.push([w,I,o[v.stream]])):(o.default||(o.default=new e.MediaStream),n(w,o.default),s.push([w,I,o.default])))}}else{(y=a.transceivers[c]||a._createTransceiver(p)).mid=g,y.iceGatherer||(y.iceGatherer=a._createIceGatherer(c,l)),N.length&&"new"===y.iceTransport.state&&(!D||l&&0!==c?N.forEach((function(e){wn(y.iceTransport,e)})):y.iceTransport.setRemoteCandidates(N)),E=e.RTCRtpReceiver.getCapabilities(p),t<15019&&(E.codecs=E.codecs.filter((function(e){return"rtx"!==e.name}))),R=y.sendEncodingParameters||[{ssrc:1001*(2*c+2)}];var L,O=!1;if("sendrecv"===_||"sendonly"===_){if(O=!y.rtpReceiver,I=y.rtpReceiver||new e.RTCRtpReceiver(y.dtlsTransport,p),O)w=I.track,v&&"-"===v.stream||(v?(o[v.stream]||(o[v.stream]=new e.MediaStream,Object.defineProperty(o[v.stream],"id",{get:function(){return v.stream}})),Object.defineProperty(w,"id",{get:function(){return v.track}}),L=o[v.stream]):(o.default||(o.default=new e.MediaStream),L=o.default)),L&&(n(w,L),y.associatedRemoteMediaStreams.push(L)),s.push([w,I,L])}else y.rtpReceiver&&y.rtpReceiver.track&&(y.associatedRemoteMediaStreams.forEach((function(t){var n=t.getTracks().find((function(e){return e.id===y.rtpReceiver.track.id}));n&&function(t,n){n.removeTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:t}))}(n,t)})),y.associatedRemoteMediaStreams=[]);y.localCapabilities=E,y.remoteCapabilities=P,y.rtpReceiver=I,y.rtcpParameters=x,y.sendEncodingParameters=R,y.recvEncodingParameters=T,a._transceive(a.transceivers[c],!1,O)}}})),void 0===a._dtlsRole&&(a._dtlsRole="offer"===i.type?"active":"passive"),a._remoteDescription={type:i.type,sdp:i.sdp},"offer"===i.type?a._updateSignalingState("have-remote-offer"):a._updateSignalingState("stable"),Object.keys(o).forEach((function(t){var n=o[t];if(n.getTracks().length){if(-1===a.remoteStreams.indexOf(n)){a.remoteStreams.push(n);var i=new Event("addstream");i.stream=n,e.setTimeout((function(){a._dispatchEvent("addstream",i)}))}s.forEach((function(e){var t=e[0],i=e[1];n.id===e[2].id&&r(a,t,i,[n])}))}})),s.forEach((function(e){e[2]||r(a,e[0],e[1],[])})),e.setTimeout((function(){a&&a.transceivers&&a.transceivers.forEach((function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))}))}),4e3),Promise.resolve()},i.prototype.close=function(){this.transceivers.forEach((function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()})),this._isClosed=!0,this._updateSignalingState("closed")},i.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},i.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout((function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}}),0))},i.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++})),e="new",t.failed>0?e="failed":t.checking>0?e="checking":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0?e="connected":t.completed>0&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var n=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",n)}},i.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)})),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0&&(e="connected"),e!==this.connectionState){this.connectionState=e;var n=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",n)}},i.prototype.createOffer=function(){var n=this;if(n._isClosed)return Promise.reject(Cn("InvalidStateError","Can not call createOffer after close"));var r=n.transceivers.filter((function(e){return"audio"===e.kind})).length,i=n.transceivers.filter((function(e){return"video"===e.kind})).length,a=arguments[0];if(a){if(a.mandatory||a.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==a.offerToReceiveAudio&&(r=!0===a.offerToReceiveAudio?1:!1===a.offerToReceiveAudio?0:a.offerToReceiveAudio),void 0!==a.offerToReceiveVideo&&(i=!0===a.offerToReceiveVideo?1:!1===a.offerToReceiveVideo?0:a.offerToReceiveVideo)}for(n.transceivers.forEach((function(e){"audio"===e.kind?--r<0&&(e.wantReceive=!1):"video"===e.kind&&--i<0&&(e.wantReceive=!1)}));r>0||i>0;)r>0&&(n._createTransceiver("audio"),r--),i>0&&(n._createTransceiver("video"),i--);var o=In.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.transceivers.forEach((function(r,i){var a=r.track,o=r.kind,s=r.mid||In.generateIdentifier();r.mid=s,r.iceGatherer||(r.iceGatherer=n._createIceGatherer(i,n.usingBundle));var c=e.RTCRtpSender.getCapabilities(o);t<15019&&(c.codecs=c.codecs.filter((function(e){return"rtx"!==e.name}))),c.codecs.forEach((function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),r.remoteCapabilities&&r.remoteCapabilities.codecs&&r.remoteCapabilities.codecs.forEach((function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)}))})),c.headerExtensions.forEach((function(e){(r.remoteCapabilities&&r.remoteCapabilities.headerExtensions||[]).forEach((function(t){e.uri===t.uri&&(e.id=t.id)}))}));var u=r.sendEncodingParameters||[{ssrc:1001*(2*i+1)}];a&&t>=15019&&"video"===o&&!u[0].rtx&&(u[0].rtx={ssrc:u[0].ssrc+1}),r.wantReceive&&(r.rtpReceiver=new e.RTCRtpReceiver(r.dtlsTransport,o)),r.localCapabilities=c,r.sendEncodingParameters=u})),"max-compat"!==n._config.bundlePolicy&&(o+="a=group:BUNDLE "+n.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),o+="a=ice-options:trickle\r\n",n.transceivers.forEach((function(e,t){o+=Rn(e,e.localCapabilities,"offer",e.stream,n._dtlsRole),o+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===n.iceGatheringState||0!==t&&n.usingBundle||(e.iceGatherer.getLocalCandidates().forEach((function(e){e.component=1,o+="a="+In.writeCandidate(e)+"\r\n"})),"completed"===e.iceGatherer.state&&(o+="a=end-of-candidates\r\n"))}));var s=new e.RTCSessionDescription({type:"offer",sdp:o});return Promise.resolve(s)},i.prototype.createAnswer=function(){var n=this;if(n._isClosed)return Promise.reject(Cn("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==n.signalingState&&"have-local-pranswer"!==n.signalingState)return Promise.reject(Cn("InvalidStateError","Can not call createAnswer in signalingState "+n.signalingState));var r=In.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.usingBundle&&(r+="a=group:BUNDLE "+n.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),r+="a=ice-options:trickle\r\n";var i=In.getMediaSections(n._remoteDescription.sdp).length;n.transceivers.forEach((function(e,a){if(!(a+1>i)){if(e.rejected)return"application"===e.kind?"DTLS/SCTP"===e.protocol?r+="m=application 0 DTLS/SCTP 5000\r\n":r+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?r+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(r+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),void(r+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n");var o;if(e.stream)"audio"===e.kind?o=e.stream.getAudioTracks()[0]:"video"===e.kind&&(o=e.stream.getVideoTracks()[0]),o&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1});var s=Tn(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,r+=Rn(e,s,"answer",e.stream,n._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(r+="a=rtcp-rsize\r\n")}}));var a=new e.RTCSessionDescription({type:"answer",sdp:r});return Promise.resolve(a)},i.prototype.addIceCandidate=function(e){var t,n=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise((function(r,i){if(!n._remoteDescription)return i(Cn("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var a=e.sdpMLineIndex;if(e.sdpMid)for(var o=0;o<n.transceivers.length;o++)if(n.transceivers[o].mid===e.sdpMid){a=o;break}var s=n.transceivers[a];if(!s)return i(Cn("OperationError","Can not add ICE candidate"));if(s.rejected)return r();var c=Object.keys(e.candidate).length>0?In.parseCandidate(e.candidate):{};if("tcp"===c.protocol&&(0===c.port||9===c.port))return r();if(c.component&&1!==c.component)return r();if((0===a||a>0&&s.iceTransport!==n.transceivers[0].iceTransport)&&!wn(s.iceTransport,c))return i(Cn("OperationError","Can not add ICE candidate"));var u=e.candidate.trim();0===u.indexOf("a=")&&(u=u.substr(2)),(t=In.getMediaSections(n._remoteDescription.sdp))[a]+="a="+(c.type?u:"end-of-candidates")+"\r\n",n._remoteDescription.sdp=In.getDescription(n._remoteDescription.sdp)+t.join("")}else for(var d=0;d<n.transceivers.length&&(n.transceivers[d].rejected||(n.transceivers[d].iceTransport.addRemoteCandidate({}),(t=In.getMediaSections(n._remoteDescription.sdp))[d]+="a=end-of-candidates\r\n",n._remoteDescription.sdp=In.getDescription(n._remoteDescription.sdp)+t.join(""),!n.usingBundle));d++);r()}))},i.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var n=null;if(this.transceivers.forEach((function(e){e.rtpSender&&e.rtpSender.track===t?n=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(n=e.rtpReceiver)})),!n)throw Cn("InvalidAccessError","Invalid selector.");return n.getStats()}var r=[];return this.transceivers.forEach((function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach((function(t){e[t]&&r.push(e[t].getStats())}))})),Promise.all(r).then((function(e){var t=new Map;return e.forEach((function(e){e.forEach((function(e){t.set(e.id,e)}))})),t}))};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach((function(t){var n=e[t];if(n&&n.prototype&&n.prototype.getStats){var r=n.prototype.getStats;n.prototype.getStats=function(){return r.apply(this).then((function(e){var t=new Map;return Object.keys(e).forEach((function(n){var r;e[n].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(r=e[n]).type]||r.type,t.set(n,e[n])})),t}))}}}));var a=["createOffer","createAnswer"];return a.forEach((function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then((function(t){"function"==typeof e[0]&&e[0].apply(null,[t])}),(function(t){"function"==typeof e[1]&&e[1].apply(null,[t])})):t.apply(this,arguments)}})),(a=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach((function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then((function(){"function"==typeof e[1]&&e[1].apply(null)}),(function(t){"function"==typeof e[2]&&e[2].apply(null,[t])})):t.apply(this,arguments)}})),["getStats"].forEach((function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then((function(){"function"==typeof e[1]&&e[1].apply(null)})):t.apply(this,arguments)}})),i};function Pn(e){const t=e&&e.navigator,n=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return n(e).catch(e=>Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString(){return this.name}}}(e)))}}function xn(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}function Dn(e,t){if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){const t=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set(e){t.set.call(this,e);const n=new Event("enabled");n.enabled=e,this.dispatchEvent(n)}})}e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)&&Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);const n=An(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=function(e,t){let n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(e=>{if(e&&(e.urls||e.url)){let t=e.urls||e.url;e.url&&!e.urls&&sn("RTCIceServer.url","RTCIceServer.urls");const r="string"==typeof t;return r&&(t=[t]),t=t.filter(e=>{if(0===e.indexOf("stun:"))return!1;const t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!n?(n=!0,!0):t&&!n}),delete e.url,e.urls=r?t[0]:t,!!t.length}})}(e.iceServers,t.version),on("ICE servers after filtering:",e.iceServers)),new n(e)},e.RTCPeerConnection.prototype=n.prototype}function Nn(e){e.RTCRtpSender&&!("replaceTrack"in e.RTCRtpSender.prototype)&&(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}var Ln=Object.freeze({__proto__:null,shimPeerConnection:Dn,shimReplaceTrack:Nn,shimGetUserMedia:Pn,shimGetDisplayMedia:xn});function On(e,t){const n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){sn("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,r)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},r&&r.prototype.getSettings){const t=r.prototype.getSettings;r.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(r&&r.prototype.applyConstraints){const t=r.prototype.applyConstraints;r.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function Mn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Vn(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]}));const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,a]=arguments;return r.apply(this,[e||null]).then(e=>{if(t.version<53&&!i)try{e.forEach(e=>{e.type=n[e.type]||e.type})}catch(r){if("TypeError"!==r.name)throw r;e.forEach((t,r)=>{e.set(r,Object.assign({},t,{type:n[t.type]||t.type}))})}return e}).then(i,a)}}function Un(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Fn(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),nn(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function jn(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){sn("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function Bn(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function Hn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];const e=arguments[1],n=e&&"sendEncodings"in e;n&&e.sendEncodings.forEach(e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=t.apply(this,arguments);if(n){const{sender:t}=r,n=t.getParameters();(!("encodings"in n)||1===n.encodings.length&&0===Object.keys(n.encodings[0]).length)&&(n.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(n).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return r})}function Gn(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function Jn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function zn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}var Wn=Object.freeze({__proto__:null,shimOnTrack:Mn,shimPeerConnection:Vn,shimSenderGetStats:Un,shimReceiverGetStats:Fn,shimRemoveStream:jn,shimRTCDataChannel:Bn,shimAddTransceiver:Hn,shimGetParameters:Gn,shimCreateOffer:Jn,shimCreateAnswer:zn,shimGetUserMedia:On,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}});function shimLocalStreamsAPI(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(n=>t.call(this,n,e)),e.getVideoTracks().forEach(n=>t.call(this,n,e))},e.RTCPeerConnection.prototype.addTrack=function(e,...n){return n&&n.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})})}}function shimRemoteStreamsAPI(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)})}),t.apply(e,arguments)}}}function qn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,i=t.setLocalDescription,a=t.setRemoteDescription,o=t.addIceCandidate;t.createOffer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,n){const r=i.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=s,s=function(e,t,n){const r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=s,s=function(e,t,n){const r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=s}function Kn(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(Qn(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function Qn(e){return e&&void 0!==e.video?Object.assign({},e,{video:un(e.video)}):e}function Xn(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let r=e.iceServers[n];!r.hasOwnProperty("urls")&&r.hasOwnProperty("url")?(sn("RTCIceServer.url","RTCIceServer.urls"),r=JSON.parse(JSON.stringify(r)),r.urls=r.url,delete r.url,t.push(r)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function $n(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Yn(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video")}return t.apply(this,arguments)}}function Zn(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var er=Object.freeze({__proto__:null,shimLocalStreamsAPI:shimLocalStreamsAPI,shimRemoteStreamsAPI:shimRemoteStreamsAPI,shimCallbacksAPI:qn,shimGetUserMedia:Kn,shimConstraints:Qn,shimRTCIceServerUrls:Xn,shimTrackEventTransceiver:$n,shimCreateOfferLegacy:Yn,shimAudioContext:Zn});function tr(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const n=new t(e),r=In.parseCandidate(e.candidate),i=Object.assign(n,r);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,nn(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function nr(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=function(e){if(!e||!e.sdp)return!1;const t=In.splitSections(e.sdp);return t.shift(),t.some(e=>{const t=In.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},r=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n},i=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n},a=function(e,n){let r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);const i=In.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?r=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r},o=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const e=r(arguments[0]),t=i(e),n=a(arguments[0],e);let o;o=0===t&&0===n?Number.POSITIVE_INFINITY:0===t||0===n?Math.max(t,n):Math.min(t,n);const s={};Object.defineProperty(s,"maxMessageSize",{get:()=>o}),this._sctp=s}return o.apply(this,arguments)}}function rr(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const r=arguments[0],i=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},nn(e,"datachannel",e=>(t(e.channel,e.target),e))}function ir(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function ar(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const n=t.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:n}):t.sdp=n}return n.apply(this,arguments)}}function or(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}var sr=Object.freeze({__proto__:null,shimRTCIceCandidate:tr,shimMaxMessageSize:nr,shimSendThrowTypeError:rr,shimConnectionState:ir,removeExtmapAllowMixed:ar,shimAddIceCandidateNullOrEmpty:or});!function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0}){const n=on,r=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:n}=e;if(n.mozGetUserMedia)t.browser="firefox",t.version=tn(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=tn(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(n.mediaDevices&&n.userAgent.match(/Edge\/(\d+).(\d+)$/))t.browser="edge",t.version=tn(n.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=tn(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),i={browserDetails:r,commonShim:sr,extractVersion:tn,disableLog:rn,disableWarnings:an};switch(r.browser){case"chrome":if(!bn||!Sn||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),i;if(null===r.version)return n("Chrome shim can not determine version, not shimming."),i;n("adapter.js shimming chrome."),i.browserShim=bn,or(e,r),hn(e,r),pn(e),Sn(e,r),fn(e),yn(e,r),mn(e),_n(e),vn(e),kn(e,r),tr(e),ir(e),nr(e,r),rr(e),ar(e,r);break;case"firefox":if(!Wn||!Vn||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),i;n("adapter.js shimming firefox."),i.browserShim=Wn,or(e,r),On(e,r),Vn(e,r),Mn(e),jn(e),Un(e),Fn(e),Bn(e),Hn(e),Gn(e),Jn(e),zn(e),tr(e),ir(e),nr(e,r),rr(e);break;case"edge":if(!Ln||!Dn||!t.shimEdge)return n("MS edge shim is not included in this adapter release."),i;n("adapter.js shimming edge."),i.browserShim=Ln,Pn(e),xn(e),Dn(e,r),Nn(e),nr(e,r),rr(e);break;case"safari":if(!er||!t.shimSafari)return n("Safari shim is not included in this adapter release."),i;n("adapter.js shimming safari."),i.browserShim=er,or(e,r),Xn(e),Yn(e),qn(e),shimLocalStreamsAPI(e),shimRemoteStreamsAPI(e),$n(e),Kn(e),Zn(e),tr(e),nr(e,r),rr(e),ar(e,r);break;default:n("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});var cr=[].slice,ur=/MSIE .\./.test(W),dr=function(e){return function(t,n){var r=arguments.length>2,i=r?cr.call(arguments,2):void 0;return e(r?function(){(H(t)?t:Function(t)).apply(this,i)}:t,n)}};Tt({global:!0,bind:!0,forced:ur},{setTimeout:dr(A.setTimeout),setInterval:dr(A.setInterval)});var lr,hr=Object.keys||function(e){return dt(e,lt)},pr=x?Object.defineProperties:function(e,t){Ee(e);for(var n,r=hr(t),i=r.length,a=0;i>a;)Ce.f(e,n=r[a++],t[n]);return e},fr=z("document","documentElement"),mr=Ue("IE_PROTO"),_r=function(){},vr=function(e){return"<script>"+e+"<\/script>"},gr=function(e){e.write(vr("")),e.close();var t=e.parentWindow.Object;return e=null,t},yr=function(){try{lr=new ActiveXObject("htmlfile")}catch(r){}var e,t;yr="undefined"!=typeof document?document.domain&&lr?gr(lr):((t=be("iframe")).style.display="none",fr.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(vr("document.F=Object")),e.close(),e.F):gr(lr);for(var n=lt.length;n--;)delete yr.prototype[lt[n]];return yr()};Fe[mr]=!0;var Sr=Object.create||function(e,t){var n;return null!==e?(_r.prototype=Ee(e),n=new _r,_r.prototype=null,n[mr]=e):n=yr(),void 0===t?n:pr(n,t)},kr=_e("unscopables"),br=Array.prototype;null==br[kr]&&Ce.f(br,kr,{configurable:!0,value:Sr(null)});var Ir,Rr,Tr,Er=function(e){br[kr][e]=!0},wr={},Cr=!P((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Ar=Ue("IE_PROTO"),Pr=Object.prototype,xr=Cr?Object.getPrototypeOf:function(e){var t=se(e);if(ue(t,Ar))return t[Ar];var n=t.constructor;return H(n)&&t instanceof n?n.prototype:t instanceof Object?Pr:null},Dr=_e("iterator"),Nr=!1;[].keys&&("next"in(Tr=[].keys())?(Rr=xr(xr(Tr)))!==Object.prototype&&(Ir=Rr):Nr=!0),(null==Ir||P((function(){var e={};return Ir[Dr].call(e)!==e})))&&(Ir={}),H(Ir[Dr])||$e(Ir,Dr,(function(){return this}));var Lr={IteratorPrototype:Ir,BUGGY_SAFARI_ITERATORS:Nr},Or=Ce.f,Mr=_e("toStringTag"),Vr=function(e,t,n){e&&!ue(e=n?e:e.prototype,Mr)&&Or(e,Mr,{configurable:!0,value:t})},Ur=Lr.IteratorPrototype,Fr=function(){return this},jr=function(e,t,n){var r=t+" Iterator";return e.prototype=Sr(Ur,{next:O(1,n)}),Vr(e,r,!1),wr[r]=Fr,e},Br=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return Ee(n),function(e){if("object"==typeof e||H(e))return e;throw TypeError("Can't set "+String(e)+" as a prototype")}(r),t?e.call(n,r):n.__proto__=r,n}}():void 0),Hr=Xe.PROPER,Gr=Xe.CONFIGURABLE,Jr=Lr.IteratorPrototype,zr=Lr.BUGGY_SAFARI_ITERATORS,Wr=_e("iterator"),qr=function(){return this},Kr=function(e,t,n,r,i,a,o){jr(n,t,r);var s,c,u,d=function(e){if(e===i&&m)return m;if(!zr&&e in p)return p[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},l=t+" Iterator",h=!1,p=e.prototype,f=p[Wr]||p["@@iterator"]||i&&p[i],m=!zr&&f||d(i),_="Array"==t&&p.entries||f;if(_&&(s=xr(_.call(new e)))!==Object.prototype&&s.next&&(xr(s)!==Jr&&(Br?Br(s,Jr):H(s[Wr])||$e(s,Wr,qr)),Vr(s,l,!0)),Hr&&"values"==i&&f&&"values"!==f.name&&(Gr?Ae(p,"name","values"):(h=!0,m=function(){return f.call(this)})),i)if(c={values:d("values"),keys:a?m:d("keys"),entries:d("entries")},o)for(u in c)(zr||h||!(u in p))&&$e(p,u,c[u]);else Tt({target:t,proto:!0,forced:zr||h},c);return p[Wr]!==m&&$e(p,Wr,m,{name:i}),wr[t]=m,c},Qr=We.set,Xr=We.getterFor("Array Iterator"),$r=Kr(Array,"Array",(function(e,t){Qr(this,{type:"Array Iterator",target:B(e),index:0,kind:t})}),(function(){var e=Xr(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");wr.Arguments=wr.Array,Er("keys"),Er("values"),Er("entries");var Yr=pt.f,Zr={}.toString,ei="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],ti={f:function(e){return ei&&"[object Window]"==Zr.call(e)?function(e){try{return Yr(e)}catch(t){return ei.slice()}}(e):Yr(B(e))}},ni=!P((function(){return Object.isExtensible(Object.preventExtensions({}))})),ri=T((function(e){var t=Ce.f,n=!1,r=he("meta"),i=0,a=Object.isExtensible||function(){return!0},o=function(e){t(e,r,{value:{objectID:"O"+i++,weakData:{}}})},s=e.exports={enable:function(){s.enable=function(){},n=!0;var e=pt.f,t=[].splice,i={};i[r]=1,e(i).length&&(pt.f=function(n){for(var i=e(n),a=0,o=i.length;a<o;a++)if(i[a]===r){t.call(i,a,1);break}return i},Tt({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:ti.f}))},fastKey:function(e,t){if(!G(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!ue(e,r)){if(!a(e))return"F";if(!t)return"E";o(e)}return e[r].objectID},getWeakData:function(e,t){if(!ue(e,r)){if(!a(e))return!0;if(!t)return!1;o(e)}return e[r].weakData},onFreeze:function(e){return ni&&n&&a(e)&&!ue(e,r)&&o(e),e}};Fe[r]=!0})),ii=(ri.enable,ri.fastKey,ri.getWeakData,ri.onFreeze,_e("iterator")),ai=Array.prototype,oi=_e("iterator"),si=function(e){if(null!=e)return re(e,oi)||re(e,"@@iterator")||wr[Dt(e)]},ci=function(e,t){var n=arguments.length<2?si(e):t;if(ne(n))return Ee(n.call(e));throw TypeError(String(e)+" is not iterable")},ui=function(e,t,n){var r,i;Ee(e);try{if(!(r=re(e,"return"))){if("throw"===t)throw n;return n}r=r.call(e)}catch(a){i=!0,r=a}if("throw"===t)throw n;if(i)throw r;return Ee(r),n},di=function(e,t){this.stopped=e,this.result=t},li=function(e,t,n){var r,i,a,o,s,c,u,d,l=n&&n.that,h=!(!n||!n.AS_ENTRIES),p=!(!n||!n.IS_ITERATOR),f=!(!n||!n.INTERRUPTED),m=Et(t,l,1+h+f),_=function(e){return r&&ui(r,"normal",e),new di(!0,e)},v=function(e){return h?(Ee(e),f?m(e[0],e[1],_):m(e[0],e[1])):f?m(e,_):m(e)};if(p)r=e;else{if(!(i=si(e)))throw TypeError(String(e)+" is not iterable");if(void 0!==(d=i)&&(wr.Array===d||ai[ii]===d)){for(a=0,o=ot(e);o>a;a++)if((s=v(e[a]))&&s instanceof di)return s;return new di(!1)}r=ci(e,i)}for(c=r.next;!(u=c.call(r)).done;){try{s=v(u.value)}catch(g){ui(r,"throw",g)}if("object"==typeof s&&s&&s instanceof di)return s}return new di(!1)},hi=function(e,t,n){if(e instanceof t)return e;throw TypeError("Incorrect "+(n?n+" ":"")+"invocation")},pi=_e("iterator"),fi=!1;try{var mi=0,_i={next:function(){return{done:!!mi++}},return:function(){fi=!0}};_i[pi]=function(){return this},Array.from(_i,(function(){throw 2}))}catch(mk){}var vi=function(e,t){if(!t&&!fi)return!1;var n=!1;try{var r={};r[pi]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(mk){}return n},gi=function(e,t,n){var r,i;return Br&&H(r=t.constructor)&&r!==n&&G(i=r.prototype)&&i!==n.prototype&&Br(e,i),e},yi=function(e,t,n){var r=-1!==e.indexOf("Map"),i=-1!==e.indexOf("Weak"),a=r?"set":"add",o=A[e],s=o&&o.prototype,c=o,u={},d=function(e){var t=s[e];$e(s,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(i&&!G(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return i&&!G(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(i&&!G(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(It(e,!H(o)||!(i||s.forEach&&!P((function(){(new o).entries().next()})))))c=n.getConstructor(t,e,r,a),ri.enable();else if(It(e,!0)){var l=new c,h=l[a](i?{}:-0,1)!=l,p=P((function(){l.has(1)})),f=vi((function(e){new o(e)})),m=!i&&P((function(){for(var e=new o,t=5;t--;)e[a](t,t);return!e.has(-0)}));f||((c=t((function(t,n){hi(t,c,e);var i=gi(new o,t,c);return null!=n&&li(n,i[a],{that:i,AS_ENTRIES:r}),i}))).prototype=s,s.constructor=c),(p||m)&&(d("delete"),d("has"),r&&d("get")),(m||h)&&d(a),i&&s.clear&&delete s.clear}return u[e]=c,Tt({global:!0,forced:c!=o},u),Vr(c,e),i||n.setStrong(c,e,r),c},Si=function(e,t,n){for(var r in t)$e(e,r,t[r],n);return e},ki=_e("species"),bi=function(e){var t=z(e),n=Ce.f;x&&t&&!t[ki]&&n(t,ki,{configurable:!0,get:function(){return this}})},Ii=Ce.f,Ri=ri.fastKey,Ti=We.set,Ei=We.getterFor,wi={getConstructor:function(e,t,n,r){var i=e((function(e,a){hi(e,i,t),Ti(e,{type:t,index:Sr(null),first:void 0,last:void 0,size:0}),x||(e.size=0),null!=a&&li(a,e[r],{that:e,AS_ENTRIES:n})})),a=Ei(t),o=function(e,t,n){var r,i,o=a(e),c=s(e,t);return c?c.value=n:(o.last=c={index:i=Ri(t,!0),key:t,value:n,previous:r=o.last,next:void 0,removed:!1},o.first||(o.first=c),r&&(r.next=c),x?o.size++:e.size++,"F"!==i&&(o.index[i]=c)),e},s=function(e,t){var n,r=a(e),i=Ri(t);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key==t)return n};return Si(i.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,x?e.size=0:this.size=0},delete:function(e){var t=a(this),n=s(this,e);if(n){var r=n.next,i=n.previous;delete t.index[n.index],n.removed=!0,i&&(i.next=r),r&&(r.previous=i),t.first==n&&(t.first=r),t.last==n&&(t.last=i),x?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),r=Et(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!s(this,e)}}),Si(i.prototype,n?{get:function(e){var t=s(this,e);return t&&t.value},set:function(e,t){return o(this,0===e?0:e,t)}}:{add:function(e){return o(this,e=0===e?0:e,e)}}),x&&Ii(i.prototype,"size",{get:function(){return a(this).size}}),i},setStrong:function(e,t,n){var r=t+" Iterator",i=Ei(t),a=Ei(r);Kr(e,t,(function(e,t){Ti(this,{type:r,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=a(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),bi(t)}},Ci=(yi("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),wi),At?{}.toString:function(){return"[object "+Dt(this)+"]"});At||$e(Object.prototype,"toString",Ci,{unsafe:!0});var Ai=function(e){if("Symbol"===Dt(e))throw TypeError("Cannot convert a Symbol value to a string");return String(e)},Pi=function(e){return function(t,n){var r,i,a=Ai(j(t)),o=et(n),s=a.length;return o<0||o>=s?e?"":void 0:(r=a.charCodeAt(o))<55296||r>56319||o+1===s||(i=a.charCodeAt(o+1))<56320||i>57343?e?a.charAt(o):r:e?a.slice(o,o+2):i-56320+(r-55296<<10)+65536}},xi={codeAt:Pi(!1),charAt:Pi(!0)},Di=xi.charAt,Ni=We.set,Li=We.getterFor("String Iterator");Kr(String,"String",(function(e){Ni(this,{type:"String Iterator",string:Ai(e),index:0})}),(function(){var e,t=Li(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=Di(n,r),t.index+=e.length,{value:e,done:!1})}));var Oi={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Mi=be("span").classList,Vi=Mi&&Mi.constructor&&Mi.constructor.prototype,Ui=Vi===Object.prototype?void 0:Vi,Fi=_e("iterator"),ji=_e("toStringTag"),Bi=$r.values,Hi=function(e,t){if(e){if(e[Fi]!==Bi)try{Ae(e,Fi,Bi)}catch(mk){e[Fi]=Bi}if(e[ji]||Ae(e,ji,t),Oi[t])for(var n in $r)if(e[n]!==$r[n])try{Ae(e,n,$r[n])}catch(mk){e[n]=$r[n]}}};for(var Gi in Oi)Hi(A[Gi]&&A[Gi].prototype,Gi);Hi(Ui,"DOMTokenList");var Ji=function(e,t,n){var r=ye(t);r in e?Ce.f(e,r,O(0,n)):e[r]=n},zi=Wt("splice"),Wi=Math.max,qi=Math.min;Tt({target:"Array",proto:!0,forced:!zi},{splice:function(e,t){var n,r,i,a,o,s,c=se(this),u=ot(c),d=rt(e,u),l=arguments.length;if(0===l?n=r=0:1===l?(n=0,r=u-d):(n=l-2,r=qi(Wi(et(t),0),u-d)),u+n-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(i=Bt(c,r),a=0;a<r;a++)(o=d+a)in c&&Ji(i,a,c[o]);if(i.length=r,n<r){for(a=d;a<u-r;a++)s=a+n,(o=a+r)in c?c[s]=c[o]:delete c[s];for(a=u;a>u-r+n;a--)delete c[a-1]}else if(n>r)for(a=u-r;a>d;a--)s=a+n-1,(o=a+r-1)in c?c[s]=c[o]:delete c[s];for(a=0;a<n;a++)c[a+d]=arguments[a+2];return c.length=u-r+n,i}});var Ki=function(e,t){var n=[][e];return!!n&&P((function(){n.call(null,t||function(){throw 1},1)}))},Qi=Jt.forEach,Xi=Ki("forEach")?[].forEach:function(e){return Qi(this,e,arguments.length>1?arguments[1]:void 0)};Tt({target:"Array",proto:!0,forced:[].forEach!=Xi},{forEach:Xi});var $i=function(e){if(e&&e.forEach!==Xi)try{Ae(e,"forEach",Xi)}catch(mk){e.forEach=Xi}};for(var Yi in Oi)Oi[Yi]&&$i(A[Yi]&&A[Yi].prototype);$i(Ui);var Zi=[].join,ea=F!=Object,ta=Ki("join",",");Tt({target:"Array",proto:!0,forced:ea||!ta},{join:function(e){return Zi.call(B(this),void 0===e?",":e)}});var na=_e("isConcatSpreadable"),ra=$>=51||!P((function(){var e=[];return e[na]=!1,e.concat()[0]!==e})),ia=Wt("concat"),aa=function(e){if(!G(e))return!1;var t=e[na];return void 0!==t?!!t:wt(e)};Tt({target:"Array",proto:!0,forced:!ra||!ia},{concat:function(e){var t,n,r,i,a,o=se(this),s=Bt(o,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(aa(a=-1===t?o:arguments[t])){if(c+(i=ot(a))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<i;n++,c++)n in a&&Ji(s,c,a[n])}else{if(c>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Ji(s,c++,a)}return s.length=c,s}});var oa=Date.prototype,sa=oa.toString,ca=oa.getTime;"Invalid Date"!=String(new Date(NaN))&&$e(oa,"toString",(function(){var e=ca.call(this);return e==e?sa.call(this):"Invalid Date"}));var ua=function(){var e=Ee(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t},da=A.RegExp,la={UNSUPPORTED_Y:P((function(){var e=da("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:P((function(){var e=da("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},ha=A.RegExp,pa=P((function(){var e=ha(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),fa=A.RegExp,ma=P((function(){var e=fa("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),_a=We.get,va=RegExp.prototype.exec,ga=oe("native-string-replace",String.prototype.replace),ya=va,Sa=function(){var e=/a/,t=/b*/g;return va.call(e,"a"),va.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),ka=la.UNSUPPORTED_Y||la.BROKEN_CARET,ba=void 0!==/()??/.exec("")[1];(Sa||ba||ka||pa||ma)&&(ya=function(e){var t,n,r,i,a,o,s,c=this,u=_a(c),d=Ai(e),l=u.raw;if(l)return l.lastIndex=c.lastIndex,t=ya.call(l,d),c.lastIndex=l.lastIndex,t;var h=u.groups,p=ka&&c.sticky,f=ua.call(c),m=c.source,_=0,v=d;if(p&&(-1===(f=f.replace("y","")).indexOf("g")&&(f+="g"),v=d.slice(c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==d.charAt(c.lastIndex-1))&&(m="(?: "+m+")",v=" "+v,_++),n=new RegExp("^(?:"+m+")",f)),ba&&(n=new RegExp("^"+m+"$(?!\\s)",f)),Sa&&(r=c.lastIndex),i=va.call(p?n:c,v),p?i?(i.input=i.input.slice(_),i[0]=i[0].slice(_),i.index=c.lastIndex,c.lastIndex+=i[0].length):c.lastIndex=0:Sa&&i&&(c.lastIndex=c.global?i.index+i[0].length:r),ba&&i&&i.length>1&&ga.call(i[0],n,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(i[a]=void 0)})),i&&h)for(i.groups=o=Sr(null),a=0;a<h.length;a++)o[(s=h[a])[0]]=i[s[1]];return i});var Ia=ya;Tt({target:"RegExp",proto:!0,forced:/./.exec!==Ia},{exec:Ia});var Ra=_e("species"),Ta=RegExp.prototype,Ea=function(e,t,n,r){var i=_e(e),a=!P((function(){var t={};return t[i]=function(){return 7},7!=""[e](t)})),o=a&&!P((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[Ra]=function(){return n},n.flags="",n[i]=/./[i]),n.exec=function(){return t=!0,null},n[i](""),!t}));if(!a||!o||n){var s=/./[i],c=t(i,""[e],(function(e,t,n,r,i){var o=t.exec;return o===Ia||o===Ta.exec?a&&!i?{done:!0,value:s.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}}));$e(String.prototype,e,c[0]),$e(Ta,i,c[1])}r&&Ae(Ta[i],"sham",!0)},wa=xi.charAt,Ca=function(e,t,n){return t+(n?wa(e,t).length:1)},Aa=Math.floor,Pa="".replace,xa=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Da=/\$([$&'`]|\d{1,2})/g,Na=function(e,t,n,r,i,a){var o=n+e.length,s=r.length,c=Da;return void 0!==i&&(i=se(i),c=xa),Pa.call(a,c,(function(a,c){var u;switch(c.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,n);case"'":return t.slice(o);case"<":u=i[c.slice(1,-1)];break;default:var d=+c;if(0===d)return a;if(d>s){var l=Aa(d/10);return 0===l?a:l<=s?void 0===r[l-1]?c.charAt(1):r[l-1]+c.charAt(1):a}u=r[d-1]}return void 0===u?"":u}))},La=function(e,t){var n=e.exec;if(H(n)){var r=n.call(e,t);return null!==r&&Ee(r),r}if("RegExp"===V(e))return Ia.call(e,t);throw TypeError("RegExp#exec called on incompatible receiver")},Oa=_e("replace"),Ma=Math.max,Va=Math.min,Ua="$0"==="a".replace(/./,"$0"),Fa=!!/./[Oa]&&""===/./[Oa]("a","$0");Ea("replace",(function(e,t,n){var r=Fa?"$":"$0";return[function(e,n){var r=j(this),i=null==e?void 0:re(e,Oa);return i?i.call(e,r,n):t.call(Ai(r),e,n)},function(e,i){var a=Ee(this),o=Ai(e);if("string"==typeof i&&-1===i.indexOf(r)&&-1===i.indexOf("$<")){var s=n(t,a,o,i);if(s.done)return s.value}var c=H(i);c||(i=Ai(i));var u=a.global;if(u){var d=a.unicode;a.lastIndex=0}for(var l=[];;){var h=La(a,o);if(null===h)break;if(l.push(h),!u)break;""===Ai(h[0])&&(a.lastIndex=Ca(o,at(a.lastIndex),d))}for(var p,f="",m=0,_=0;_<l.length;_++){h=l[_];for(var v=Ai(h[0]),g=Ma(Va(et(h.index),o.length),0),y=[],S=1;S<h.length;S++)y.push(void 0===(p=h[S])?p:String(p));var k=h.groups;if(c){var b=[v].concat(y,g,o);void 0!==k&&b.push(k);var I=Ai(i.apply(void 0,b))}else I=Na(v,o,g,y,k,i);g>=m&&(f+=o.slice(m,g)+I,m=g+v.length)}return f+o.slice(m)}]}),!!P((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!Ua||Fa);var ja=_e("iterator"),Ba=!P((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,r){t.delete("b"),n+=r+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[ja]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),Ha=z("fetch"),Ga=z("Request"),Ja=Ga&&Ga.prototype,za=z("Headers"),Wa=_e("iterator"),qa=We.set,Ka=We.getterFor("URLSearchParams"),Qa=We.getterFor("URLSearchParamsIterator"),Xa=/\+/g,$a=Array(4),Ya=function(e){return $a[e-1]||($a[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},Za=function(e){try{return decodeURIComponent(e)}catch(mk){return e}},eo=function(e){var t=e.replace(Xa," "),n=4;try{return decodeURIComponent(t)}catch(mk){for(;n;)t=t.replace(Ya(n--),Za);return t}},to=/[!'()~]|%20/g,no={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},ro=function(e){return no[e]},io=function(e){return encodeURIComponent(e).replace(to,ro)},ao=function(e,t){if(t)for(var n,r,i=t.split("&"),a=0;a<i.length;)(n=i[a++]).length&&(r=n.split("="),e.push({key:eo(r.shift()),value:eo(r.join("="))}))},oo=function(e){this.entries.length=0,ao(this.entries,e)},so=function(e,t){if(e<t)throw TypeError("Not enough arguments")},co=jr((function(e,t){qa(this,{type:"URLSearchParamsIterator",iterator:ci(Ka(e).entries),kind:t})}),"Iterator",(function(){var e=Qa(this),t=e.kind,n=e.iterator.next(),r=n.value;return n.done||(n.value="keys"===t?r.key:"values"===t?r.value:[r.key,r.value]),n})),uo=function(){hi(this,uo,"URLSearchParams");var e,t,n,r,i,a,o,s,c,u=arguments.length>0?arguments[0]:void 0,d=this,l=[];if(qa(d,{type:"URLSearchParams",entries:l,updateURL:function(){},updateSearchParams:oo}),void 0!==u)if(G(u))if(e=si(u))for(n=(t=ci(u,e)).next;!(r=n.call(t)).done;){if((o=(a=(i=ci(Ee(r.value))).next).call(i)).done||(s=a.call(i)).done||!a.call(i).done)throw TypeError("Expected sequence with length 2");l.push({key:Ai(o.value),value:Ai(s.value)})}else for(c in u)ue(u,c)&&l.push({key:c,value:Ai(u[c])});else ao(l,"string"==typeof u?"?"===u.charAt(0)?u.slice(1):u:Ai(u))},lo=uo.prototype;if(Si(lo,{append:function(e,t){so(arguments.length,2);var n=Ka(this);n.entries.push({key:Ai(e),value:Ai(t)}),n.updateURL()},delete:function(e){so(arguments.length,1);for(var t=Ka(this),n=t.entries,r=Ai(e),i=0;i<n.length;)n[i].key===r?n.splice(i,1):i++;t.updateURL()},get:function(e){so(arguments.length,1);for(var t=Ka(this).entries,n=Ai(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){so(arguments.length,1);for(var t=Ka(this).entries,n=Ai(e),r=[],i=0;i<t.length;i++)t[i].key===n&&r.push(t[i].value);return r},has:function(e){so(arguments.length,1);for(var t=Ka(this).entries,n=Ai(e),r=0;r<t.length;)if(t[r++].key===n)return!0;return!1},set:function(e,t){so(arguments.length,1);for(var n,r=Ka(this),i=r.entries,a=!1,o=Ai(e),s=Ai(t),c=0;c<i.length;c++)(n=i[c]).key===o&&(a?i.splice(c--,1):(a=!0,n.value=s));a||i.push({key:o,value:s}),r.updateURL()},sort:function(){var e,t,n,r=Ka(this),i=r.entries,a=i.slice();for(i.length=0,n=0;n<a.length;n++){for(e=a[n],t=0;t<n;t++)if(i[t].key>e.key){i.splice(t,0,e);break}t===n&&i.push(e)}r.updateURL()},forEach:function(e){for(var t,n=Ka(this).entries,r=Et(e,arguments.length>1?arguments[1]:void 0,3),i=0;i<n.length;)r((t=n[i++]).value,t.key,this)},keys:function(){return new co(this,"keys")},values:function(){return new co(this,"values")},entries:function(){return new co(this,"entries")}},{enumerable:!0}),$e(lo,Wa,lo.entries,{name:"entries"}),$e(lo,"toString",(function(){for(var e,t=Ka(this).entries,n=[],r=0;r<t.length;)e=t[r++],n.push(io(e.key)+"="+io(e.value));return n.join("&")}),{enumerable:!0}),Vr(uo,"URLSearchParams"),Tt({global:!0,forced:!Ba},{URLSearchParams:uo}),!Ba&&H(za)){var ho=function(e){if(G(e)){var t,n=e.body;if("URLSearchParams"===Dt(n))return(t=e.headers?new za(e.headers):new za).has("content-type")||t.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),Sr(e,{body:O(0,String(n)),headers:O(0,t)})}return e};if(H(Ha)&&Tt({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return Ha(e,arguments.length>1?ho(arguments[1]):{})}}),H(Ga)){var po=function(e){return hi(this,po,"Request"),new Ga(e,arguments.length>1?ho(arguments[1]):{})};Ja.constructor=po,po.prototype=Ja,Tt({global:!0,forced:!0},{Request:po})}}var fo=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};Ea("search",(function(e,t,n){return[function(t){var n=j(this),r=null==t?void 0:re(t,e);return r?r.call(t,n):new RegExp(t)[e](Ai(n))},function(e){var r=Ee(this),i=Ai(e),a=n(t,r,i);if(a.done)return a.value;var o=r.lastIndex;fo(o,0)||(r.lastIndex=0);var s=La(r,i);return fo(r.lastIndex,o)||(r.lastIndex=o),null===s?-1:s.index}]}));(new Date).getTime();var mo=0,_o=function(){return(new Date).getTime()+mo},vo=function(){var e=new Date;return e.setTime(_o()),e.toLocaleString()};Ea("match",(function(e,t,n){return[function(t){var n=j(this),r=null==t?void 0:re(t,e);return r?r.call(t,n):new RegExp(t)[e](Ai(n))},function(e){var r=Ee(this),i=Ai(e),a=n(t,r,i);if(a.done)return a.value;if(!r.global)return La(r,i);var o=r.unicode;r.lastIndex=0;for(var s,c=[],u=0;null!==(s=La(r,i));){var d=Ai(s[0]);c[u]=d,""===d&&(r.lastIndex=Ca(i,at(r.lastIndex),o)),u++}return 0===u?null:c}]}));var go=_e("match"),yo=function(e){var t;return G(e)&&(void 0!==(t=e[go])?!!t:"RegExp"==V(e))},So=Ce.f,ko=pt.f,bo=We.enforce,Io=_e("match"),Ro=A.RegExp,To=Ro.prototype,Eo=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,wo=/a/g,Co=/a/g,Ao=new Ro(wo)!==wo,Po=la.UNSUPPORTED_Y,xo=x&&(!Ao||Po||pa||ma||P((function(){return Co[Io]=!1,Ro(wo)!=wo||Ro(Co)==Co||"/a/i"!=Ro(wo,"i")})));if(It("RegExp",xo)){for(var Do=function(e,t){var n,r,i,a,o,s,c=this instanceof Do,u=yo(e),d=void 0===t,l=[],h=e;if(!c&&u&&d&&e.constructor===Do)return e;if((u||e instanceof Do)&&(e=e.source,d&&(t="flags"in h?h.flags:ua.call(h))),e=void 0===e?"":Ai(e),t=void 0===t?"":Ai(t),h=e,pa&&"dotAll"in wo&&(r=!!t&&t.indexOf("s")>-1)&&(t=t.replace(/s/g,"")),n=t,Po&&"sticky"in wo&&(i=!!t&&t.indexOf("y")>-1)&&(t=t.replace(/y/g,"")),ma&&(e=(a=function(e){for(var t,n=e.length,r=0,i="",a=[],o={},s=!1,c=!1,u=0,d="";r<=n;r++){if("\\"===(t=e.charAt(r)))t+=e.charAt(++r);else if("]"===t)s=!1;else if(!s)switch(!0){case"["===t:s=!0;break;case"("===t:Eo.test(e.slice(r+1))&&(r+=2,c=!0),i+=t,u++;continue;case">"===t&&c:if(""===d||ue(o,d))throw new SyntaxError("Invalid capture group name");o[d]=!0,a.push([d,u]),c=!1,d="";continue}c?d+=t:i+=t}return[i,a]}(e))[0],l=a[1]),o=gi(Ro(e,t),c?this:To,Do),(r||i||l.length)&&(s=bo(o),r&&(s.dotAll=!0,s.raw=Do(function(e){for(var t,n=e.length,r=0,i="",a=!1;r<=n;r++)"\\"!==(t=e.charAt(r))?a||"."!==t?("["===t?a=!0:"]"===t&&(a=!1),i+=t):i+="[\\s\\S]":i+=t+e.charAt(++r);return i}(e),n)),i&&(s.sticky=!0),l.length&&(s.groups=l)),e!==h)try{Ae(o,"source",""===h?"(?:)":h)}catch(mk){}return o},No=function(e){e in Do||So(Do,e,{configurable:!0,get:function(){return Ro[e]},set:function(t){Ro[e]=t}})},Lo=ko(Ro),Oo=0;Lo.length>Oo;)No(Lo[Oo++]);To.constructor=Do,Do.prototype=To,$e(A,"RegExp",Do)}bi("RegExp");var Mo=Xe.PROPER,Vo=RegExp.prototype,Uo=Vo.toString,Fo=P((function(){return"/a/b"!=Uo.call({source:"a",flags:"b"})})),jo=Mo&&"toString"!=Uo.name;(Fo||jo)&&$e(RegExp.prototype,"toString",(function(){var e=Ee(this),t=Ai(e.source),n=e.flags;return"/"+t+"/"+Ai(void 0===n&&e instanceof RegExp&&!("flags"in Vo)?ua.call(e):n)}),{unsafe:!0});var Bo=ct.indexOf,Ho=[].indexOf,Go=!!Ho&&1/[1].indexOf(1,-0)<0,Jo=Ki("indexOf");Tt({target:"Array",proto:!0,forced:Go||!Jo},{indexOf:function(e){return Go?Ho.apply(this,arguments)||0:Bo(this,e,arguments.length>1?arguments[1]:void 0)}});var zo=P((function(){xr(1)}));Tt({target:"Object",stat:!0,forced:zo,sham:!Cr},{getPrototypeOf:function(e){return xr(se(e))}});var Wo=z("Reflect","apply"),qo=Function.apply,Ko=!P((function(){Wo((function(){}))}));Tt({target:"Reflect",stat:!0,forced:Ko},{apply:function(e,t,n){return ne(e),Ee(n),Wo?Wo(e,t,n):qo.call(e,t,n)}});var Qo=Xe.EXISTS,Xo=Ce.f,$o=Function.prototype,Yo=$o.toString,Zo=/^\s*function ([^ (]*)/;x&&!Qo&&Xo($o,"name",{configurable:!0,get:function(){try{return Yo.call(this).match(Zo)[1]}catch(mk){return""}}}),Tt({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var es=_e("species"),ts=function(e,t){var n,r=Ee(e).constructor;return void 0===r||null==(n=Ee(r)[es])?t:function(e){if(Ft(e))return e;throw TypeError(te(e)+" is not a constructor")}(n)},ns=la.UNSUPPORTED_Y,rs=[].push,is=Math.min;Ea("split",(function(e,t,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var r=Ai(j(this)),i=void 0===n?4294967295:n>>>0;if(0===i)return[];if(void 0===e)return[r];if(!yo(e))return t.call(r,e,i);for(var a,o,s,c=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),d=0,l=new RegExp(e.source,u+"g");(a=Ia.call(l,r))&&!((o=l.lastIndex)>d&&(c.push(r.slice(d,a.index)),a.length>1&&a.index<r.length&&rs.apply(c,a.slice(1)),s=a[0].length,d=o,c.length>=i));)l.lastIndex===a.index&&l.lastIndex++;return d===r.length?!s&&l.test("")||c.push(""):c.push(r.slice(d)),c.length>i?c.slice(0,i):c}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var i=j(this),a=null==t?void 0:re(t,e);return a?a.call(t,i,n):r.call(Ai(i),t,n)},function(e,i){var a=Ee(this),o=Ai(e),s=n(r,a,o,i,r!==t);if(s.done)return s.value;var c=ts(a,RegExp),u=a.unicode,d=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(ns?"g":"y"),l=new c(ns?"^(?:"+a.source+")":a,d),h=void 0===i?4294967295:i>>>0;if(0===h)return[];if(0===o.length)return null===La(l,o)?[o]:[];for(var p=0,f=0,m=[];f<o.length;){l.lastIndex=ns?0:f;var _,v=La(l,ns?o.slice(f):o);if(null===v||(_=is(at(l.lastIndex+(ns?f:0)),o.length))===p)f=Ca(o,f,u);else{if(m.push(o.slice(p,f)),m.length===h)return m;for(var g=1;g<=v.length-1;g++)if(m.push(v[g]),m.length===h)return m;f=p=_}}return m.push(o.slice(p)),m}]}),!!P((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),ns);var as=1..valueOf,os=function(e){return as.call(e)},ss="\t\n\v\f\r                　\u2028\u2029\ufeff",cs="["+ss+"]",us=RegExp("^"+cs+cs+"*"),ds=RegExp(cs+cs+"*$"),ls=function(e){return function(t){var n=Ai(j(t));return 1&e&&(n=n.replace(us,"")),2&e&&(n=n.replace(ds,"")),n}},hs={start:ls(1),end:ls(2),trim:ls(3)},ps=pt.f,fs=Te.f,ms=Ce.f,_s=hs.trim,vs=A.Number,gs=vs.prototype,ys=function(e){var t=ge(e,"number");return"bigint"==typeof t?t:Ss(t)},Ss=function(e){var t,n,r,i,a,o,s,c,u=ge(e,"number");if(ee(u))throw TypeError("Cannot convert a Symbol value to a number");if("string"==typeof u&&u.length>2)if(43===(t=(u=_s(u)).charCodeAt(0))||45===t){if(88===(n=u.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(u.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+u}for(o=(a=u.slice(2)).length,s=0;s<o;s++)if((c=a.charCodeAt(s))<48||c>i)return NaN;return parseInt(a,r)}return+u};if(It("Number",!vs(" 0o1")||!vs("0b1")||vs("+0x1"))){for(var ks,bs=function(e){var t=arguments.length<1?0:vs(ys(e)),n=this;return n instanceof bs&&P((function(){os(n)}))?gi(Object(t),n,bs):t},Is=x?ps(vs):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),Rs=0;Is.length>Rs;Rs++)ue(vs,ks=Is[Rs])&&!ue(bs,ks)&&ms(bs,ks,fs(vs,ks));bs.prototype=gs,gs.constructor=bs,$e(A,"Number",bs)}var Ts=hs.trim,Es=A.parseFloat,ws=A.Symbol,Cs=ws&&ws.iterator,As=1/Es(ss+"-0")!=-Infinity||Cs&&!P((function(){Es(Object(Cs))}))?function(e){var t=Ts(Ai(e)),n=Es(t);return 0===n&&"-"==t.charAt(0)?-0:n}:Es;Tt({global:!0,forced:parseFloat!=As},{parseFloat:As});var Ps,xs=window.navigator&&window.navigator.userAgent||"",Ds=/AppleWebKit\/([\d.]+)/i.exec(xs),Ns=(Ds&&parseFloat(Ds.pop()),/iPad/i.test(xs)),Ls=navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/Macintosh/.test(xs),Os=/iPhone/i.test(xs)&&!Ns,Ms=/iPod/i.test(xs),Vs=Os||Ns||Ms||Ls,Us="15.1"===function(){if(Ls)return Nc;if(Vs){var e=xs.match(/OS (\d+)_(\d+)/i);if(e&&e[1]){var t=e[1];return e[2]&&(t+=".".concat(e[2])),t}}return null}(),Fs=/Android/i.test(xs),js=Fs&&function(){var e=xs.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);return t&&n?parseFloat(e[1]+"."+e[2]):t||null}(),Bs=(Fs&&/webkit/i.test(xs),/Firefox/i.test(xs)),Hs=Bs&&function(){var e=xs.match(/Firefox\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Gs=/Edge\//i.test(xs),Js=Gs&&function(){var e=xs.match(/Edge\/(\d+)/i);if(e&&e[1])return e[1]}(),zs=/Edg\//i.test(xs),Ws=zs&&function(){var e=xs.match(/Edg\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),qs=/SogouMobileBrowser\//i.test(xs),Ks=qs&&function(){var e=xs.match(/SogouMobileBrowser\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Qs=/MetaSr\s/i.test(xs),Xs=Qs&&function(){var e=xs.match(/MetaSr(\s\d+(\.\d+)+)/);return e&&e[1]?parseFloat(e[1]):null}(),$s=/TBS\/\d+/i.test(xs),Ys=$s&&function(){var e=xs.match(/TBS\/(\d+)/i);if(e&&e[1])return e[1]}(),Zs=/XWEB\/\d+/i.test(xs),ec=Zs&&function(){var e=xs.match(/XWEB\/(\d+)/i);if(e&&e[1])return e[1]}(),tc=(/MSIE\s8\.0/.test(xs),/MSIE\/\d+/i.test(xs)&&function(){var e=/MSIE\s(\d+)\.\d/.exec(xs),t=e&&parseFloat(e[1]);!t&&/Trident\/7.0/i.test(xs)&&/rv:11.0/.test(xs)&&(t=11)}(),/(micromessenger|webbrowser)/i.test(xs)),nc=tc&&function(){var e=xs.match(/MicroMessenger\/(\d+)/i);if(e&&e[1])return e[1]}(),rc=!$s&&/MQQBrowser\/\d+/i.test(xs)&&/COVC\/\d+/i.test(xs),ic=!$s&&/MQQBrowser\/\d+/i.test(xs)&&!/COVC\/\d+/i.test(xs),ac=(ic||rc)&&function(){var e=xs.match(/ MQQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),oc=!$s&&/ QQBrowser\/\d+/i.test(xs),sc=oc&&function(){var e=xs.match(/ QQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),cc=!$s&&/QQBrowserLite\/\d+/i.test(xs),uc=cc&&function(){var e=xs.match(/QQBrowserLite\/([\d.]+)/);return e&&e[1]?e[1]:null}(),dc=!$s&&/MQBHD\/\d+/i.test(xs),lc=dc&&function(){var e=xs.match(/MQBHD\/([\d.]+)/);return e&&e[1]?e[1]:null}(),hc=/Windows/i.test(xs),pc=!Vs&&/MAC OS X/i.test(xs),fc=!Fs&&/Linux/i.test(xs),mc=(/MicroMessenger/i.test(xs),/UCBrowser/i.test(xs)),_c=(/Electron/i.test(xs),/MiuiBrowser/i.test(xs)),vc=_c&&function(){var e=xs.match(/MiuiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),gc=/HuaweiBrowser/i.test(xs),yc=/Huawei/i.test(xs),Sc=gc&&function(){var e=xs.match(/HuaweiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),kc=/SamsungBrowser/i.test(xs),bc=kc&&function(){var e=xs.match(/SamsungBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Ic=/HeyTapBrowser/i.test(xs),Rc=Ic&&function(){var e=xs.match(/HeyTapBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Tc=/VivoBrowser/i.test(xs),Ec=Tc&&function(){var e=xs.match(/VivoBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),wc=function(){var e=xs.match(/Chrome\/(\d+)/);return e&&e[1]?Number(e[1]):null},Cc=/Chrome/i.test(xs),Ac=!Gs&&!Qs&&!qs&&!$s&&!Zs&&!zs&&!oc&&!_c&&!gc&&!kc&&!Ic&&!Tc&&/Chrome/i.test(xs),Pc=Ac&&wc(),xc=Ac&&function(){var e=xs.match(/Chrome\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Dc=!Cc&&!ic&&!rc&&!cc&&!dc&&/Safari/i.test(xs),Nc=Dc&&function(){var e=xs.match(/Version\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Lc=/Android.*(wv|.0.0.0)/.test(xs),Oc=Ac?"Chrome/"+xc:Dc?"Safari/"+Nc:"NotSupportedBrowser",Mc="file:"===location.protocol||"localhost"===location.hostname||"127.0.0.1"===location.hostname,Vc=function(){if(Ul(Ps))try{Ps=window.localStorage}catch(mk){pf.warn(mk),Ps=!1}return Ps},Uc=new Map([[Bs,["Firefox",Hs]],[zs,["Edg",Ws]],[Ac,["Chrome",xc]],[Dc,["Safari",Nc]],[$s,["TBS",Ys]],[Zs,["XWEB",ec]],[tc&&Os,["WeChat",nc]],[oc,["QQ(Win)",sc]],[ic,["QQ(Mobile)",ac]],[rc,["QQ(Mobile X5)",ac]],[cc,["QQ(Mac)",uc]],[dc,["QQ(iPad)",lc]],[_c,["MI",vc]],[gc,["HW",Sc]],[kc,["Samsung",bc]],[Ic,["OPPO",Rc]],[Tc,["VIVO",Ec]],[Gs,["EDGE",Js]],[qs,["SogouMobile",Ks]],[Qs,["Sogou",Xs]]]);function Fc(){var e="unknown",t="unknown";return Uc.get(!0)&&(e=Uc.get(!0)[0],t=Uc.get(!0)[1]),{name:e,version:t}}var jc=P((function(){hr(1)}));Tt({target:"Object",stat:!0,forced:jc},{keys:function(e){return hr(se(e))}});var Bc="wss://qcloud.rtc.qq.com",Hc="wss://bk.rtc.qq.com",Gc="wss://trtc.rtc.qq.com",Jc="wss://webrtc.qq.com",zc="qcloud",Wc="trtc",qc="webrtc",Kc="",Qc=function(e){return Kc=e},Xc=1,$c=2,Yc=20,Zc=21,eu="5Y2wZK8nANNAoVw6dSAHVjNxrD1ObBM2kBPV",tu="224d130c-7b5c-415b-aaa2-79c2eb5a6df2",nu=2,ru="DISCONNECTED",iu="CONNECTING",au="RECONNECTING",ou="CONNECTED",su="join",cu="delta-join",uu="rejoin",du="leave",lu="delta-leave",hu="publish",pu="delta-publish",fu="unpublish",mu="subscribe",_u="unsubscribe",vu="uplink-connection",gu="uplink-reconnection",yu="downlink-connection",Su="downlink-reconnection",ku="setLocalDescription",bu="setRemoteDescription",Iu="iceConnectionState",Ru="stream-initialize",Tu="websocketConnectionState",Eu="websocketReconnectionState",wu="update-stream",Cu="recover-subscription",Au="start-mix-transcode",Pu="stop-mix-transcode",xu="player-error",Du="unsubscribe",Nu="subscribe_change",Lu={MANUAL:"manual",PRESET_LAYOUT:"preset-layout"},Ou={REMOTE:"$PLACE_HOLDER_REMOTE$"},Mu={IT_AUDIO_VIDEO:0,IT_PICTURE:2,IT_CANVAS:3,IT_PURE_AUDIO:4,IT_PURE_VIDEO:5},Vu="string",Uu="number",Fu="boolean",ju="array",Bu="object",Hu="canvas",Gu="audio",Ju="video",zu="auxiliary",Wu="user",qu="environment",Ku="mute",Qu="unmute",Xu="ended",$u="playing",Yu="pause",Zu="error",ed="loadeddata",td="audioinput",nd="videoinput",rd="add",id="remove",ad={unknown:0,wifi:1,"3g":2,"2g":3,"4g":4,wired:5},od=-1,sd=0,cd=1,ud="https://schedule.rtc.qq.com/api/v1/config",dd="TRTC",ld="Client",hd="LocalStream",pd="RemoteStream",fd="Stream",md="https://web.sdk.qcloud.com/trtc/webrtc/doc",_d="".concat(md,"/zh-cn/"),vd={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,NONE:5},gd=Object.keys(vd),yd=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},Sd=Object.prototype.toString;function kd(e){return"[object Array]"===Sd.call(e)}function bd(e){return void 0===e}function Id(e){return null!==e&&"object"==typeof e}function Rd(e){return"[object Function]"===Sd.call(e)}function Td(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),kd(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var Ed={isArray:kd,isArrayBuffer:function(e){return"[object ArrayBuffer]"===Sd.call(e)},isBuffer:function(e){return null!==e&&!bd(e)&&null!==e.constructor&&!bd(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:Id,isUndefined:bd,isDate:function(e){return"[object Date]"===Sd.call(e)},isFile:function(e){return"[object File]"===Sd.call(e)},isBlob:function(e){return"[object Blob]"===Sd.call(e)},isFunction:Rd,isStream:function(e){return Id(e)&&Rd(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:Td,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)Td(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]="object"==typeof n?e({},n):n}for(var r=0,i=arguments.length;r<i;r++)Td(arguments[r],n);return t},extend:function(e,t,n){return Td(t,(function(t,r){e[r]=n&&"function"==typeof t?yd(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}};function wd(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var Cd=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(Ed.isURLSearchParams(t))r=t.toString();else{var i=[];Ed.forEach(t,(function(e,t){null!=e&&(Ed.isArray(e)?t+="[]":e=[e],Ed.forEach(e,(function(e){Ed.isDate(e)?e=e.toISOString():Ed.isObject(e)&&(e=JSON.stringify(e)),i.push(wd(t)+"="+wd(e))})))})),r=i.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function Ad(){this.handlers=[]}Ad.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Ad.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ad.prototype.forEach=function(e){Ed.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var Pd=Ad,xd=function(e,t,n){return Ed.forEach(n,(function(n){e=n(e,t)})),e},Dd=function(e){return!(!e||!e.__CANCEL__)},Nd="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Ld(){throw new Error("setTimeout has not been defined")}function Od(){throw new Error("clearTimeout has not been defined")}var Md=Ld,Vd=Od;function Ud(e){if(Md===setTimeout)return setTimeout(e,0);if((Md===Ld||!Md)&&setTimeout)return Md=setTimeout,setTimeout(e,0);try{return Md(e,0)}catch(t){try{return Md.call(null,e,0)}catch(t){return Md.call(this,e,0)}}}"function"==typeof Nd.setTimeout&&(Md=setTimeout),"function"==typeof Nd.clearTimeout&&(Vd=clearTimeout);var Fd,jd=[],Bd=!1,Hd=-1;function Gd(){Bd&&Fd&&(Bd=!1,Fd.length?jd=Fd.concat(jd):Hd=-1,jd.length&&Jd())}function Jd(){if(!Bd){var e=Ud(Gd);Bd=!0;for(var t=jd.length;t;){for(Fd=jd,jd=[];++Hd<t;)Fd&&Fd[Hd].run();Hd=-1,t=jd.length}Fd=null,Bd=!1,function(e){if(Vd===clearTimeout)return clearTimeout(e);if((Vd===Od||!Vd)&&clearTimeout)return Vd=clearTimeout,clearTimeout(e);try{Vd(e)}catch(t){try{return Vd.call(null,e)}catch(t){return Vd.call(this,e)}}}(e)}}function zd(e,t){this.fun=e,this.array=t}zd.prototype.run=function(){this.fun.apply(null,this.array)};function Wd(){}var qd=Wd,Kd=Wd,Qd=Wd,Xd=Wd,$d=Wd,Yd=Wd,Zd=Wd;var el=Nd.performance||{},tl=el.now||el.mozNow||el.msNow||el.oNow||el.webkitNow||function(){return(new Date).getTime()};var nl=new Date;var rl={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];jd.push(new zd(e,t)),1!==jd.length||Bd||Ud(Jd)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:qd,addListener:Kd,once:Qd,off:Xd,removeListener:$d,removeAllListeners:Yd,emit:Zd,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*tl.call(el),n=Math.floor(t),r=Math.floor(t%1*1e9);return e&&(n-=e[0],(r-=e[1])<0&&(n--,r+=1e9)),[n,r]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-nl)/1e3}},il=function(e,t){Ed.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},al=function(e,t,n,r,i){return function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}(new Error(e),t,n,r,i)},ol=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],sl=Ed.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=Ed.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},cl=Ed.isStandardBrowserEnv()?{write:function(e,t,n,r,i,a){var o=[];o.push(e+"="+encodeURIComponent(t)),Ed.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),Ed.isString(r)&&o.push("path="+r),Ed.isString(i)&&o.push("domain="+i),!0===a&&o.push("secure"),document.cookie=o.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},ul=function(e){return new Promise((function(t,n){var r=e.data,i=e.headers;Ed.isFormData(r)&&delete i["Content-Type"];var a=new XMLHttpRequest;if(e.auth){var o=e.auth.username||"",s=e.auth.password||"";i.Authorization="Basic "+btoa(o+":"+s)}var c,u,d=(c=e.baseURL,u=e.url,c&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(u)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(c,u):u);if(a.open(e.method.toUpperCase(),Cd(d,e.params,e.paramsSerializer),!0),a.timeout=e.timeout,a.onreadystatechange=function(){if(a&&4===a.readyState&&(0!==a.status||a.responseURL&&0===a.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in a?function(e){var t,n,r,i={};return e?(Ed.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=Ed.trim(e.substr(0,r)).toLowerCase(),n=Ed.trim(e.substr(r+1)),t){if(i[t]&&ol.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([n]):i[t]?i[t]+", "+n:n}})),i):i}(a.getAllResponseHeaders()):null,i={data:e.responseType&&"text"!==e.responseType?a.response:a.responseText,status:a.status,statusText:a.statusText,headers:r,config:e,request:a};!function(e,t,n){var r=n.config.validateStatus;!r||r(n.status)?e(n):t(al("Request failed with status code "+n.status,n.config,null,n.request,n))}(t,n,i),a=null}},a.onabort=function(){a&&(n(al("Request aborted",e,"ECONNABORTED",a)),a=null)},a.onerror=function(){n(al("Network Error",e,null,a)),a=null},a.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(al(t,e,"ECONNABORTED",a)),a=null},Ed.isStandardBrowserEnv()){var l=cl,h=(e.withCredentials||sl(d))&&e.xsrfCookieName?l.read(e.xsrfCookieName):void 0;h&&(i[e.xsrfHeaderName]=h)}if("setRequestHeader"in a&&Ed.forEach(i,(function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete i[t]:a.setRequestHeader(t,e)})),Ed.isUndefined(e.withCredentials)||(a.withCredentials=!!e.withCredentials),e.responseType)try{a.responseType=e.responseType}catch(p){if("json"!==e.responseType)throw p}"function"==typeof e.onDownloadProgress&&a.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&a.upload&&a.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){a&&(a.abort(),n(e),a=null)})),void 0===r&&(r=null),a.send(r)}))},dl={"Content-Type":"application/x-www-form-urlencoded"};function ll(e,t){!Ed.isUndefined(e)&&Ed.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var hl={adapter:function(){var e;return("undefined"!=typeof XMLHttpRequest||void 0!==rl&&"[object process]"===Object.prototype.toString.call(rl))&&(e=ul),e}(),transformRequest:[function(e,t){return il(t,"Accept"),il(t,"Content-Type"),Ed.isFormData(e)||Ed.isArrayBuffer(e)||Ed.isBuffer(e)||Ed.isStream(e)||Ed.isFile(e)||Ed.isBlob(e)?e:Ed.isArrayBufferView(e)?e.buffer:Ed.isURLSearchParams(e)?(ll(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):Ed.isObject(e)?(ll(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};Ed.forEach(["delete","get","head"],(function(e){hl.headers[e]={}})),Ed.forEach(["post","put","patch"],(function(e){hl.headers[e]=Ed.merge(dl)}));var pl=hl;function fl(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var ml=function(e){return fl(e),e.headers=e.headers||{},e.data=xd(e.data,e.headers,e.transformRequest),e.headers=Ed.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),Ed.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||pl.adapter)(e).then((function(t){return fl(e),t.data=xd(t.data,t.headers,e.transformResponse),t}),(function(t){return Dd(t)||(fl(e),t&&t.response&&(t.response.data=xd(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},_l=function(e,t){t=t||{};var n={},r=["url","method","params","data"],i=["headers","auth","proxy"],a=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Ed.forEach(r,(function(e){void 0!==t[e]&&(n[e]=t[e])})),Ed.forEach(i,(function(r){Ed.isObject(t[r])?n[r]=Ed.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:Ed.isObject(e[r])?n[r]=Ed.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])})),Ed.forEach(a,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}));var o=r.concat(i).concat(a),s=Object.keys(t).filter((function(e){return-1===o.indexOf(e)}));return Ed.forEach(s,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])})),n};function vl(e){this.defaults=e,this.interceptors={request:new Pd,response:new Pd}}vl.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=_l(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[ml,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},vl.prototype.getUri=function(e){return e=_l(this.defaults,e),Cd(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Ed.forEach(["delete","get","head","options"],(function(e){vl.prototype[e]=function(t,n){return this.request(Ed.merge(n||{},{method:e,url:t}))}})),Ed.forEach(["post","put","patch"],(function(e){vl.prototype[e]=function(t,n,r){return this.request(Ed.merge(r||{},{method:e,url:t,data:n}))}}));var gl=vl;function yl(e){this.message=e}yl.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},yl.prototype.__CANCEL__=!0;var Sl=yl;function kl(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new Sl(e),t(n.reason))}))}kl.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},kl.source=function(){var e;return{token:new kl((function(t){e=t})),cancel:e}};var bl=kl;function Il(e){var t=new gl(e),n=yd(gl.prototype.request,t);return Ed.extend(n,gl.prototype,t),Ed.extend(n,t),n}var Rl=Il(pl);Rl.Axios=gl,Rl.create=function(e){return Il(_l(Rl.defaults,e))},Rl.Cancel=Sl,Rl.CancelToken=bl,Rl.isCancel=Dd,Rl.all=function(e){return Promise.all(e)},Rl.spread=function(e){return function(t){return e.apply(null,t)}};var Tl=Rl,El=Rl;Tl.default=El;var wl=Tl,Cl={INVALID_PARAMETER:4096,INVALID_OPERATION:4097,NOT_SUPPORTED:4098,DEVICE_NOT_FOUND:4099,SIGNAL_CHANNEL_SETUP_FAILED:16385,SIGNAL_CHANNEL_ERROR:16386,ICE_TRANSPORT_ERROR:16387,JOIN_ROOM_FAILED:16388,CREATE_OFFER_FAILED:16389,SIGNAL_CHANNEL_RECONNECTION_FAILED:16390,UPLINK_RECONNECTION_FAILED:16391,DOWNLINK_RECONNECTION_FAILED:16392,CLIENT_BANNED:16448,SERVER_TIMEOUT:16449,SUBSCRIPTION_TIMEOUT:16450,PLAY_NOT_ALLOWED:16451,DEVICE_AUTO_RECOVER_FAILED:16452,START_PUBLISH_CDN_FAILED:16453,STOP_PUBLISH_CDN_FAILED:16454,START_MIX_TRANSCODE_FAILED:16455,STOP_MIX_TRANSCODE_FAILED:16456,NOT_SUPPORTED_H264:16457,SWITCH_ROLE_FAILED:16458,API_CALL_TIMEOUT:16459,UNKNOWN:65535},Al=function(e){u(n,e);var t=_(n);function n(e){var r,i=e.message,o=e.code,s=void 0===o?Cl.UNKNOWN:o,c=e.extraCode,u=void 0===c?0:c;return a(this,n),(r=t.call(this,i+" <".concat(function(e){for(var t in Cl)if(Cl[t]===e)return t;return"UNKNOWN"}(s)," 0x").concat(s.toString(16),">"))).code_=s,r.extraCode_=u,r.name="RtcError",r.message_=i,r}return s(n,[{key:"getCode",value:function(){return this.code_}},{key:"getExtraCode",value:function(){return this.extraCode_}}]),n}(f(Error)),Pl=new(function(){function e(){a(this,e);var t=Fc(),n=t.name,r=t.version;this.configs_={sdkAppId:"",userId:"",version:"4.11.11",env:zc,browserVersion:n+r,ua:navigator.userAgent}}return s(e,[{key:"setConfig",value:function(e){var t=e.sdkAppId,n=e.env,r=e.userId;t!==this.configs_.sdkAppId&&(this.configs_.sdkAppId=t),this.configs_.env=n,this.configs_.userId=r}},{key:"logEvent",value:function(e){if(!Mc){var n=t(t(t({},e),this.configs_),{},{userId:e.userId||this.configs_.userId});Ul(n.code)&&(n.code="failed"===n.result?Cl.UNKNOWN:0),wl.post(Nl(),JSON.stringify(n)).catch((function(){}))}}},{key:"logSuccessEvent",value:function(e){Mc||(this.logEvent(t(t({},e),{},{result:"success"})),this.configs_.env===zc&&this.uploadEventToKibana(t(t({},e),{},{result:"success"})))}},{key:"logFailedEvent",value:function(e){if(!Mc){var n=e.eventType,r=e.code,i=e.error,a={userId:e.userId,eventType:n,result:"failed",code:r||(i instanceof Al?i.getExtraCode()||i.getCode():Cl.UNKNOWN)};this.logEvent(a),this.configs_.env===zc&&this.uploadEventToKibana(t(t({},a),{},{error:i}))}}},{key:"uploadEventToKibana",value:function(e){var t="stat-".concat(e.eventType,"-").concat(e.result);"delta-join"!==e.eventType&&"delta-leave"!==e.eventType&&"delta-publish"!==e.eventType||(t="".concat(e.eventType,":").concat(e.delta)),this.uploadEvent({log:t,userId:e.userId}),"failed"===e.result&&(t="stat-".concat(e.eventType,"-").concat(e.result,"-").concat(e.code),this.uploadEvent({log:t,userId:e.userId,error:e.error}))}},{key:"uploadEvent",value:function(e){var t=e.log,n=e.userId,r=e.error,i={timestamp:vo(),sdkAppId:this.configs_.sdkAppId,userId:n||this.configs_.userId,version:this.configs_.version,log:t};r&&(i.errorInfo=r.message),wl.post(Dl(),JSON.stringify(i)).catch((function(){}))}}]),e}()),xl=function(){return function(e){var t=window.location.search.match(new RegExp("(\\?|&)"+e+"=([^&]*)(&|$)"));return t?decodeURIComponent(t[2]):""}("trtc_env")},Dl=function(){return"".concat(Kc||"https://yun.tim.qq.com","/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_log")},Nl=function(){return"".concat(Kc||"https://yun.tim.qq.com","/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_event")};function Ll(){var e=navigator.userAgent,t=navigator.connection,n=e.match(/NetType\/\w+/)?e.match(/NetType\/\w+/)[0]:"";"3gnet"===(n=n.toLowerCase().replace("nettype/",""))&&(n="3g");var r=t&&t.type&&t.type.toLowerCase(),i=t&&t.effectiveType&&t.effectiveType.toLowerCase();"slow-2"===i&&(i="2g");var a=n||"unknown";if(r)switch(r){case"cellular":case"wimax":a=i||"unknown";break;case"wifi":a="wifi";break;case"ethernet":a="wired";break;case"none":case"other":case"unknown":a="unknown"}return pf.info("networkType:",a),a}var Ol=function(e){if(!e||"object"!==n(e)||"[object Object]"!=Object.prototype.toString.call(e))return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.toString.call(r)===Function.prototype.toString.call(Object)};function Ml(e){var t=Math.round(e/2)+1;return t>6?13e3:1e3*function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return t<=1?r:e(t-1,r,n+r)}(t)}var Vl=function(e){return"function"==typeof e},Ul=function(e){return void 0===e},Fl=function(e){return"string"==typeof e},jl=function(e){return"number"==typeof e},Bl=function(e){return"boolean"==typeof e},Hl=function(e){return Gl(e)==="MediaStreamTrack".toLowerCase()};function Gl(e){return Reflect.apply(Object.prototype.toString,e,[]).replace(/^\[object\s(\w+)\]$/,"$1").toLowerCase()}function Jl(e){var t={};return t.urls="turn:".concat(e.url),Ul(e.username)||Ul(e.credential)||(t.username=e.username,t.credential=e.credential,t.credentialType="password",Ul(e.credentialType)||(t.credentialType=e.credentialType)),t}function zl(){return performance&&performance.now?Math.floor(performance.now()):Date.now()}function Wl(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"big";if(!Fl(e))return 0;var n=e.split(".");return"big"===t?(Number(n[0])<<24|Number(n[1])<<16|Number(n[2])<<8|Number(n[3]))>>>0:(Number(n[3])<<24|Number(n[2])<<16|Number(n[1])<<8|Number(n[0]))>>>0}function ql(e){return Kl.apply(this,arguments)}function Kl(){return(Kl=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n={userId:String(t.userId),sdkAppId:String(t.sdkAppId),isStrGroupId:t.useStringRoomId,groupId:String(t.roomId),sdkVersion:"4.11.11",userSig:String(t.userSig)},r=zl(),e.next=5,wl.post(ud,n,{timeout:1500});case 5:if(i=e.sent,Pl.uploadEvent({log:"stat-schedule-delta:".concat(zl()-r),userId:t.userId}),0!==i.data.code){e.next=9;break}return e.abrupt("return",i.data.data);case 9:pf.info("schedule failed: "+i.data.msg),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),pf.info("schedule failed: "+e.t0);case 15:case"end":return e.stop()}}),e,null,[[0,12]])})))).apply(this,arguments)}var Ql,Xl,$l,Yl,Zl,eh,th=function(){var e=navigator.language||navigator.userLanguage;return"zh"===(e=e.substr(0,2))},nh=(Ql=!1,Xl=document.visibilityState,function(){document.visibilityState!==Xl&&pf.info("visibility change: ".concat(document.visibilityState)),Ql||(document.addEventListener("visibilitychange",(function(){pf.info("visibility change: "+document.visibilityState),Xl=document.visibilityState})),Ql=!0)}),rh=T((function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,r,a,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,a||e,o),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,a=r.length,o=new Array(a);i<a;i++)o[i]=r[i].fn;return o},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,a,o){var s=n?n+e:e;if(!this._events[s])return!1;var c,u,d=this._events[s],l=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),l){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,r),!0;case 4:return d.fn.call(d.context,t,r,i),!0;case 5:return d.fn.call(d.context,t,r,i,a),!0;case 6:return d.fn.call(d.context,t,r,i,a,o),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];d.fn.apply(d.context,c)}else{var h,p=d.length;for(u=0;u<p;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),l){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,r);break;case 4:d[u].fn.call(d[u].context,t,r,i);break;default:if(!c)for(h=1,c=new Array(l-1);h<l;h++)c[h-1]=arguments[h];d[u].fn.apply(d[u].context,c)}}return!0},s.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||o(this,a);else{for(var c=0,u=[],d=s.length;c<d;c++)(s[c].fn!==t||i&&!s[c].once||r&&s[c].context!==r)&&u.push(s[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&o(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s})),ih=new rh,ah=1,oh=2,sh=3,ch=4,uh=5,dh=20,lh=21,hh=22,ph=23,fh=24,mh=27,_h=28,vh=29,gh=30,yh=31,Sh=100,kh=101,bh=102,Ih=103,Rh=110,Th=111,Eh=112,wh=113,Ch=114,Ah=115,Ph=116,xh=120,Dh=121,Nh=122,Lh=123,Oh=130,Mh=131,Vh=132,Uh=133,Fh=134,jh=135,Bh=136,Hh=137,Gh=200,Jh=201,zh=300,Wh=301,qh=A.Promise,Kh=/(?:ipad|iphone|ipod).*applewebkit/i.test(W),Qh="process"==V(A.process),Xh=A.setImmediate,$h=A.clearImmediate,Yh=A.process,Zh=A.MessageChannel,ep=A.Dispatch,tp=0,np={};try{$l=A.location}catch(mk){}var rp=function(e){if(np.hasOwnProperty(e)){var t=np[e];delete np[e],t()}},ip=function(e){return function(){rp(e)}},ap=function(e){rp(e.data)},op=function(e){A.postMessage(String(e),$l.protocol+"//"+$l.host)};Xh&&$h||(Xh=function(e){for(var t=[],n=arguments.length,r=1;n>r;)t.push(arguments[r++]);return np[++tp]=function(){(H(e)?e:Function(e)).apply(void 0,t)},Yl(tp),tp},$h=function(e){delete np[e]},Qh?Yl=function(e){Yh.nextTick(ip(e))}:ep&&ep.now?Yl=function(e){ep.now(ip(e))}:Zh&&!Kh?(eh=(Zl=new Zh).port2,Zl.port1.onmessage=ap,Yl=Et(eh.postMessage,eh,1)):A.addEventListener&&H(A.postMessage)&&!A.importScripts&&$l&&"file:"!==$l.protocol&&!P(op)?(Yl=op,A.addEventListener("message",ap,!1)):Yl="onreadystatechange"in be("script")?function(e){fr.appendChild(be("script")).onreadystatechange=function(){fr.removeChild(this),rp(e)}}:function(e){setTimeout(ip(e),0)});var sp,cp,up,dp,lp,hp,pp,fp,mp={set:Xh,clear:$h},_p=/ipad|iphone|ipod/i.test(W)&&void 0!==A.Pebble,vp=/web0s(?!.*chrome)/i.test(W),gp=Te.f,yp=mp.set,Sp=A.MutationObserver||A.WebKitMutationObserver,kp=A.document,bp=A.process,Ip=A.Promise,Rp=gp(A,"queueMicrotask"),Tp=Rp&&Rp.value;Tp||(sp=function(){var e,t;for(Qh&&(e=bp.domain)&&e.exit();cp;){t=cp.fn,cp=cp.next;try{t()}catch(mk){throw cp?dp():up=void 0,mk}}up=void 0,e&&e.enter()},Kh||Qh||vp||!Sp||!kp?!_p&&Ip&&Ip.resolve?((pp=Ip.resolve(void 0)).constructor=Ip,fp=pp.then,dp=function(){fp.call(pp,sp)}):dp=Qh?function(){bp.nextTick(sp)}:function(){yp.call(A,sp)}:(lp=!0,hp=kp.createTextNode(""),new Sp(sp).observe(hp,{characterData:!0}),dp=function(){hp.data=lp=!lp}));var Ep,wp,Cp,Ap,Pp=Tp||function(e){var t={fn:e,next:void 0};up&&(up.next=t),cp||(cp=t,dp()),up=t},xp=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=ne(t),this.reject=ne(n)},Dp={f:function(e){return new xp(e)}},Np=function(e){try{return{error:!1,value:e()}}catch(mk){return{error:!0,value:mk}}},Lp="object"==typeof window,Op=mp.set,Mp=_e("species"),Vp="Promise",Up=We.get,Fp=We.set,jp=We.getterFor(Vp),Bp=qh&&qh.prototype,Hp=qh,Gp=Bp,Jp=A.TypeError,zp=A.document,Wp=A.process,qp=Dp.f,Kp=qp,Qp=!!(zp&&zp.createEvent&&A.dispatchEvent),Xp=H(A.PromiseRejectionEvent),$p=!1,Yp=It(Vp,(function(){var e=Le(Hp),t=e!==String(Hp);if(!t&&66===$)return!0;if($>=51&&/native code/.test(e))return!1;var n=new Hp((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};return(n.constructor={})[Mp]=r,!($p=n.then((function(){}))instanceof r)||!t&&Lp&&!Xp})),Zp=Yp||!vi((function(e){Hp.all(e).catch((function(){}))})),ef=function(e){var t;return!(!G(e)||!H(t=e.then))&&t},tf=function(e,t){if(!e.notified){e.notified=!0;var n=e.reactions;Pp((function(){for(var r=e.value,i=1==e.state,a=0;n.length>a;){var o,s,c,u=n[a++],d=i?u.ok:u.fail,l=u.resolve,h=u.reject,p=u.domain;try{d?(i||(2===e.rejection&&of(e),e.rejection=1),!0===d?o=r:(p&&p.enter(),o=d(r),p&&(p.exit(),c=!0)),o===u.promise?h(Jp("Promise-chain cycle")):(s=ef(o))?s.call(o,l,h):l(o)):h(r)}catch(mk){p&&!c&&p.exit(),h(mk)}}e.reactions=[],e.notified=!1,t&&!e.rejection&&rf(e)}))}},nf=function(e,t,n){var r,i;Qp?((r=zp.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),A.dispatchEvent(r)):r={promise:t,reason:n},!Xp&&(i=A["on"+e])?i(r):"unhandledrejection"===e&&function(e,t){var n=A.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},rf=function(e){Op.call(A,(function(){var t,n=e.facade,r=e.value;if(af(e)&&(t=Np((function(){Qh?Wp.emit("unhandledRejection",r,n):nf("unhandledrejection",n,r)})),e.rejection=Qh||af(e)?2:1,t.error))throw t.value}))},af=function(e){return 1!==e.rejection&&!e.parent},of=function(e){Op.call(A,(function(){var t=e.facade;Qh?Wp.emit("rejectionHandled",t):nf("rejectionhandled",t,e.value)}))},sf=function(e,t,n){return function(r){e(t,r,n)}},cf=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,tf(e,!0))},uf=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw Jp("Promise can't be resolved itself");var r=ef(t);r?Pp((function(){var n={done:!1};try{r.call(t,sf(uf,n,e),sf(cf,n,e))}catch(mk){cf(n,mk,e)}})):(e.value=t,e.state=1,tf(e,!1))}catch(mk){cf({done:!1},mk,e)}}};if(Yp&&(Gp=(Hp=function(e){hi(this,Hp,Vp),ne(e),Ep.call(this);var t=Up(this);try{e(sf(uf,t),sf(cf,t))}catch(mk){cf(t,mk)}}).prototype,(Ep=function(e){Fp(this,{type:Vp,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Si(Gp,{then:function(e,t){var n=jp(this),r=qp(ts(this,Hp));return r.ok=!H(e)||e,r.fail=H(t)&&t,r.domain=Qh?Wp.domain:void 0,n.parent=!0,n.reactions.push(r),0!=n.state&&tf(n,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),wp=function(){var e=new Ep,t=Up(e);this.promise=e,this.resolve=sf(uf,t),this.reject=sf(cf,t)},Dp.f=qp=function(e){return e===Hp||e===Cp?new wp(e):Kp(e)},H(qh)&&Bp!==Object.prototype)){Ap=Bp.then,$p||($e(Bp,"then",(function(e,t){var n=this;return new Hp((function(e,t){Ap.call(n,e,t)})).then(e,t)}),{unsafe:!0}),$e(Bp,"catch",Gp.catch,{unsafe:!0}));try{delete Bp.constructor}catch(mk){}Br&&Br(Bp,Gp)}Tt({global:!0,wrap:!0,forced:Yp},{Promise:Hp}),Vr(Hp,Vp,!1),bi(Vp),Cp=z(Vp),Tt({target:Vp,stat:!0,forced:Yp},{reject:function(e){var t=qp(this);return t.reject.call(void 0,e),t.promise}}),Tt({target:Vp,stat:!0,forced:Yp},{resolve:function(e){return function(e,t){if(Ee(e),G(t)&&t.constructor===e)return t;var n=Dp.f(e);return(0,n.resolve)(t),n.promise}(this,e)}}),Tt({target:Vp,stat:!0,forced:Zp},{all:function(e){var t=this,n=qp(t),r=n.resolve,i=n.reject,a=Np((function(){var n=ne(t.resolve),a=[],o=0,s=1;li(e,(function(e){var c=o++,u=!1;a.push(void 0),s++,n.call(t,e).then((function(e){u||(u=!0,a[c]=e,--s||r(a))}),i)})),--s||r(a)}));return a.error&&i(a.value),n.promise},race:function(e){var t=this,n=qp(t),r=n.reject,i=Np((function(){var i=ne(t.resolve);li(e,(function(e){i.call(t,e).then(n.resolve,r)}))}));return i.error&&r(i.value),n.promise}});function df(e){var t=e.retryFunction,n=e.settings,r=e.onError,a=e.onRetrying,o=e.context;return function(){for(var e=this,s=arguments.length,c=new Array(s),u=0;u<s;u++)c[u]=arguments[u];var d=n.retries||5,l=0,h=-1,p=0,f=function(){var s=i(regeneratorRuntime.mark((function i(s,u){var m,_,v,g;return regeneratorRuntime.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,m=o||e,i.next=4,t.apply(m,c);case 4:_=i.sent,l=0,s(_),i.next=14;break;case 9:i.prev=9,i.t0=i.catch(0),v=function(){clearTimeout(h),l=0,p=2,u(i.t0)},g=function(){2!==p&&l<d?(l++,p=1,Vl(a)&&a(l,v),h=setTimeout((function(){h=-1,f(s,u)}),n.timeout||1e3)):v()},r(i.t0,g,u);case 14:case"end":return i.stop()}}),i,null,[[0,9]])})));return function(e,t){return s.apply(this,arguments)}}();return new Promise(f)}}var lf=function e(t){a(this,e),this.log=t.log,this.level=t.level,this.userId=t.userId,this.sdkAppId=t.sdkAppId,this.forAllJoinedClients=t.forAllJoinedClients,this.uploaded=!1},hf=function(){function e(t){a(this,e),this.id_=t.id,this.userId_=t.userId,this.sdkAppId_=t.sdkAppId,this.type_=t.type,this.isLocal_=!Bl(t.isLocal)||t.isLocal}return s(e,[{key:"setUserId",value:function(e){this.userId_=e}},{key:"setSdkAppId",value:function(e){this.sdkAppId_=e}},{key:"log",value:function(e,t){pf.log({log:"[".concat(this.isLocal_?"":"*").concat(this.id_,"] ").concat(this.type_?this.type_+" ":"").concat(t),level:e,forAllJoinedClients:Ul(this.userId_),userId:this.userId_,sdkAppId:this.sdkAppId_})}},{key:"info",value:function(e){this.log(vd.INFO,e)}},{key:"debug",value:function(e){this.log(vd.DEBUG,e)}},{key:"warn",value:function(e){this.log(vd.WARN,e)}},{key:"error",value:function(e){this.log(vd.ERROR,e)}}]),e}(),pf=new(function(){function e(){var t=this;a(this,e),this.clients_=[],this.queue_=[],this.timeoutId_=-1,this.logLevel_=vd.DEBUG,this.logLevelToUpload_=vd.INFO,this.enableUploadLog_=!0,this.startUpload(),this.checkURLParam(),ih.on(gh,(function(e){var n=e.client;return t.clients_.push(n)})),ih.on(yh,(function(e){e&&Ol(e.config)&&gd[e.config.logLevelToUpload]&&(t.logLevelToUpload_=e.config.logLevelToUpload)}))}var t,n;return s(e,[{key:"startUpload",value:(n=i(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.upload();case 3:e.next=7;break;case 5:e.prev=5,e.t0=e.catch(0);case 7:this.timeoutId_=setTimeout((function(){return t.startUpload()}),2e3);case 8:case"end":return e.stop()}}),e,this,[[0,5]])}))),function(){return n.apply(this,arguments)})},{key:"stopUpload",value:function(){-1!==this.timeoutId_&&(clearTimeout(this.timeoutId_),this.timeoutId_=-1)}},{key:"getLogsToUpload",value:function(){var e=this,t={map:new Map,splicedQueue:[]};if(this.queue_[0].forAllJoinedClients&&0===this.clients_.length)return t;for(var n=0,r=function(){if(50===n)return"break";var r=e.queue_[n];r.forAllJoinedClients?e.clients_.forEach((function(e){if(e.getIsJoined()){var n=e.getUserId(),i=e.getSDKAppId();if(t.map.has(n))t.map.get(n).logs.push(r);else t.map.set(n,{userId:n,sdkAppId:i,logs:[r]})}})):t.map.has(r.userId)?t.map.get(r.userId).logs.push(r):t.map.set(r.userId,{userId:r.userId,sdkAppId:r.sdkAppId,logs:[r]})};n<this.queue_.length;n++){if("break"===r())break}return t.map.size>0&&(t.splicedQueue=this.queue_.splice(0,n)),t}},{key:"upload",value:(t=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==this.queue_.length){e.next=2;break}return e.abrupt("return");case 2:if(t=this.getLogsToUpload(),n=t.map,r=t.splicedQueue,0!==n.size){e.next=5;break}return e.abrupt("return");case 5:e.prev=5,i=y(n.values()),a=0;case 8:if(!(a<i.length)){e.next=16;break}return o=i[a],s=o.userId,c=o.sdkAppId,u=o.logs,e.next=12,this.uploadLogWithRetry(JSON.stringify({timestamp:vo(),sdkAppId:String(c),userId:s,version:"4.11.11",log:u.map((function(e){return e.log})).join("\n")}));case 12:u.forEach((function(e){return e.uploaded=!0}));case 13:a++,e.next=8;break;case 16:e.next=20;break;case 18:e.prev=18,e.t0=e.catch(5);case 20:(d=r.filter((function(e){return!e.uploaded}))).length>0&&(this.queue_=d.concat(this.queue_));case 22:case"end":return e.stop()}}),e,this,[[5,18]])}))),function(){return t.apply(this,arguments)})},{key:"uploadLogWithRetry",value:function(e){return df({retryFunction:function(){return wl.post(Dl(),e,{timeout:5e3})},settings:{retries:3,timeout:1e3},onError:function(e,t){t()}})()}},{key:"getPrefix",value:function(e){var t=new Date;return t.setTime(_o()),"[".concat(t.toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1"),":").concat(t.getMilliseconds(),"] <").concat(gd[e],">")}},{key:"getLogLevel",value:function(){return this.logLevel_}},{key:"setLogLevel",value:function(e){Ul(gd[e])||(this.logLevel_=e)}},{key:"enableUploadLog",value:function(){this.enableUploadLog_=!0}},{key:"disableUploadLog",value:function(){this.enableUploadLog_=!1}},{key:"log",value:function(e){var t=e.log,n=e.level,r=e.forAllJoinedClients,i=void 0===r||r,a=e.userId,o=e.sdkAppId;if(t="".concat(this.getPrefix(n)," ").concat(t),this.enableUploadLog_&&n>=this.logLevelToUpload_&&this.queue_.push(new lf({log:t,level:n,userId:a,sdkAppId:o,forAllJoinedClients:i})),!(n<this.logLevel_))switch(n){case vd.ERROR:console.error(t);break;case vd.WARN:console.warn(t);break;case vd.INFO:console.log(t);break;case vd.DEBUG:console.debug(t)}}},{key:"debug",value:function(e){this.log({log:e,level:vd.DEBUG})}},{key:"info",value:function(e){this.log({log:e,level:vd.INFO})}},{key:"warn",value:function(e){this.log({log:e,level:vd.WARN})}},{key:"error",value:function(e){this.log({log:e,level:vd.ERROR})}},{key:"createLogger",value:function(e){return new hf(e)}},{key:"checkURLParam",value:function(){var e=new URLSearchParams(location.search).get("logLevelToUpload");gd[e]&&(this.logLevelToUpload_=e)}}]),e}()),ff=!0,mf=Math.floor,_f=Number.isInteger||function(e){return!G(e)&&isFinite(e)&&mf(e)===e};Tt({target:"Number",stat:!0},{isInteger:_f});var vf,gf=function(e){if(yo(e))throw TypeError("The method doesn't accept regular expressions");return e},yf=_e("match"),Sf=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[yf]=!1,"/./"[e](t)}catch(r){}}return!1},kf=Te.f,bf="".startsWith,If=Math.min,Rf=Sf("startsWith"),Tf=!(Rf||(vf=kf(String.prototype,"startsWith"),!vf||vf.writable));Tt({target:"String",proto:!0,forced:!Tf&&!Rf},{startsWith:function(e){var t=Ai(j(this));gf(e);var n=at(If(arguments.length>1?arguments[1]:void 0,t.length)),r=Ai(e);return bf?bf.call(t,r,n):t.slice(n,n+r.length)===r}}),Tt({target:"Array",stat:!0},{isArray:wt});var Ef=hs.trim,wf=A.parseInt,Cf=A.Symbol,Af=Cf&&Cf.iterator,Pf=/^[+-]?0[Xx]/,xf=8!==wf(ss+"08")||22!==wf(ss+"0x16")||Af&&!P((function(){wf(Object(Af))}))?function(e,t){var n=Ef(Ai(e));return wf(n,t>>>0||(Pf.test(n)?16:10))}:wf;Tt({global:!0,forced:parseInt!=xf},{parseInt:xf});var Df=[].slice,Nf={},Lf=function(e,t,n){if(!(t in Nf)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";Nf[t]=Function("C,a","return new C("+r.join(",")+")")}return Nf[t](e,n)},Of=Function.bind||function(e){var t=ne(this),n=Df.call(arguments,1),r=function(){var i=n.concat(Df.call(arguments));return this instanceof r?Lf(t,i.length,i):t.apply(e,i)};return G(t.prototype)&&(r.prototype=t.prototype),r};Tt({target:"Function",proto:!0},{bind:Of});var Mf=Jt.findIndex,Vf=!0;"findIndex"in[]&&Array(1).findIndex((function(){Vf=!1})),Tt({target:"Array",proto:!0,forced:Vf},{findIndex:function(e){return Mf(this,e,arguments.length>1?arguments[1]:void 0)}}),Er("findIndex");var Uf=L.f,Ff=function(e){return function(t){for(var n,r=B(t),i=hr(r),a=i.length,o=0,s=[];a>o;)n=i[o++],x&&!Uf.call(r,n)||s.push(e?[n,r[n]]:r[n]);return s}},jf={entries:Ff(!0),values:Ff(!1)}.values;Tt({target:"Object",stat:!0},{values:function(e){return jf(e)}});var Bf=ct.includes;Tt({target:"Array",proto:!0},{includes:function(e){return Bf(this,e,arguments.length>1?arguments[1]:void 0)}}),Er("includes"),Tt({target:"String",proto:!0,forced:!Sf("includes")},{includes:function(e){return!!~Ai(j(this)).indexOf(Ai(gf(e)),arguments.length>1?arguments[1]:void 0)}});var Hf="connection-state-changed",Gf="connected",Jf="error",zf="DISCONNECTED",Wf="CONNECTING",qf="RECONNECTING",Kf="CONNECTED",Qf={ON_PUBLISH_RESPONSE:2,NEW_ICE_CANDIDATE:4,CLINET_BANNED:8,CHANNEL_SETUP_SUCCESS:19,CHANNEL_SETUP_FAILED:80,REBUILD_SESSION_RESULT:514,JOIN_ROOM_RESULT:20,PEER_JOIN:4134,PEER_LEAVE:4135,STREAM_ADDED:16,STREAM_REMOVED:18,UPDATE_REMOTE_SDP:48,UPDATE_AUDIO_SSRC:50,UPDATE_VIDEO_SSRC:52,UPLINK_NETWORK_STATS:22,UPDATE_REMOTE_MUTE_STAT:23,CLOSE_PEER_ACK:10,SUBSCRIBE_ACK:26,PONG:775,PUBLISH_RESULT:4098,UNPUBLISH_RESULT:4100,SUBSCRIBE_RESULT:4102,UNSUBSCRIBE_RESULT:4104,SUBSCRIBE_CHANGE_RESULT:4106,UPDATE_OFFER_RESULT:4128,REMOTE_STREAM_UPDATE:4130,START_PUBLISH_TENCENT_CDN_RES:1286,STOP_PUBLISH_TENCENT_CDN_RES:1288,START_PUBLISH_GIVEN_CDN_RES:777,STOP_PUBLISH_GIVEN_CDN_RES:779,START_MIX_TRANSCODE_RES:781,STOP_MIX_TRANSCODE_RES:783,USER_LIST_RES:4137,SWITCH_ROLE_RES:4110},Xf=[Qf.UPDATE_REMOTE_MUTE_STAT,Qf.UPLINK_NETWORK_STATS,Qf.PONG,Qf.REMOTE_STREAM_UPDATE,Qf.USER_LIST_RES],$f={ON_PUBLISH_RESPONSE:"on-publish-response",NEW_ICE_CANDIDATE:"new-ice-candidate",CLINET_BANNED:"client-banned",CHANNEL_SETUP_SUCCESS:"channel-setup-success",CHANNEL_SETUP_FAILED:"channel-setup-failed",REBUILD_SESSION_RESULT:"channel-reconnect-result",JOIN_ROOM_RESULT:"join-room-result",PEER_JOIN:"peer-join",PEER_LEAVE:"peer-leave",STREAM_ADDED:"stream-added",STREAM_REMOVED:"stream-removed",UPDATE_REMOTE_SDP:"update-remote-sdp",UPDATE_AUDIO_SSRC:"update-audio-ssrc",UPDATE_VIDEO_SSRC:"update-video-ssrc",UPLINK_NETWORK_STATS:"uplink-network-stats",UPDATE_REMOTE_MUTE_STAT:"update-remote-mute-stat",CLOSE_PEER_ACK:"close-peer-ack",SUBSCRIBE_ACK:"subscribe-ack",REQUEST_REBUILD_SESSION:"request-rebuild-session",CLIENT_REJOIN:"client-rejoin",PONG:"pong",PUBLISH_RESULT:"publish-result",UNPUBLISH_RESULT:"unpublish-result",SUBSCRIBE_RESULT:"subscribe-result",SUBSCRIBE_CHANGE_RESULT:"subscribe-change-result",UNSUBSCRIBE_RESULT:"unsubscribe-result",UPDATE_OFFER_RESULT:"update-offer-result",REMOTE_STREAM_UPDATE:"remote-stream-update",START_PUBLISH_TENCENT_CDN_RES:"start-publish-tencent-cdn-res",STOP_PUBLISH_TENCENT_CDN_RES:"stop-publish-tencent-cdn-res",START_PUBLISH_GIVEN_CDN_RES:"start-publish-given-cdn-res",STOP_PUBLISH_GIVEN_CDN_RES:"stop-publish-given-cdn-res",START_MIX_TRANSCODE_RES:"start-mix-transcode-res",STOP_MIX_TRANSCODE_RES:"stop-mix-transcode-res",USER_LIST_RES:"user-list-res",SWITCH_ROLE_RES:"switch_role_res"},Yf="on_update_track",Zf="on_create_room",em="on_quit_room",tm="on_quality_report",nm="on_rebuild_session",rm="on_mute_uplink",im="on_constraints_config",am="ping",om="on_publish",sm="on_unpublish",cm="on_sub",um="on_unsub",dm="on_sub_change",lm="on_start_publishing",hm="on_stop_publishing",pm="on_start_push_user_cdn",fm="on_stop_push_user_cdn",mm="on_start_mcu_mix",_m="on_stop_mcu_mix",vm="on_get_user_list",gm="on_switch_role",ym="on_change_video_type",Sm=32768,km=32769,bm=32770,Im=32771,Rm=32772,Tm=32773,Em=32774,wm=32775,Cm=32777,Am=32778,Pm=32779,xm=32780,Dm=32781,Nm=32782,Lm=32783,Om=32784,Mm=32785,Vm=32786,Um=32787,Fm=32788,jm=32789,Bm=32790,Hm=32791,Gm=32792,Jm=32793,zm=32794,Wm=32795,qm=32796,Km=32797,Qm=32798,Xm=32799,$m=32800,Ym=32801,Zm=32802,e_=new Map,t_=function(e,t){var n=e_.get(e);n||(e_.set(e,[]),n=e_.get(e)),n.push(t)},n_=Object.prototype.hasOwnProperty;function r_(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(Ol(e))switch(Object.prototype.toString.call(e)){case"[object File]":case"[object Map]":case"[object Set]":return 0===e.size;case"[object Object]":for(var t in e)if(n_.call(e,t))return!1;return!0}return!1}var i_=/"/g,a_=function(e,t,n,r){var i=Ai(j(e)),a="<"+t;return""!==n&&(a+=" "+n+'="'+Ai(r).replace(i_,"&quot;")+'"'),a+">"+i+"</"+t+">"},o_=function(e){return P((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3}))};Tt({target:"String",proto:!0,forced:o_("link")},{link:function(e){return a_(this,"a","href",e)}});var s_="AVOID_REPEATED_CALL",c_="INVALID_PARAMETER_REQUIRED",u_="INVALID_PARAMETER_TYPE",d_="INVALID_PARAMETER_EMPTY",l_="INVALID_PARAMETER_INSTANCE",h_="INVALID_PARAMETER_RANGE",p_="API_CALL_TIMEOUT",f_="SIGNAL_CHANNEL_RECONNECTION_FAILED",m_="SIGNAL_CHANNEL_SETUP_FAILED",__="ERROR_MESSAGE",v_="SUBSCRIPTION_TIMEOUT",g_="EXCHANGE_SDP_TIMEOUT",y_="DOWNLINK_RECONNECTION_FAILED",S_="EXCHANGE_SDP_FAILED",k_="UPLINK_RECONNECTION_FAILED",b_="AUDIO",I_="VIDEO",R_="INVALID_RECORDID",T_="INVALID_PURE_AUDIO",E_="INVALID_STREAMID",w_="INVALID_USER_DEFINE_RECORDID",C_="INVALID_USER_DEFINE_PUSH_ARGS",A_="INVALID_PROXY",P_="INVALID_JOIN",x_="INVALID_ROOMID_STRING",D_="INVALID_ROOMID_INTEGER",N_="INVALID_SIGNAL_CHANNEL",L_="JOIN_ROOM_TIMEOUT",O_="JOIN_ROOM_FAILED",M_="REJOIN_ROOM_FAILED",V_="INVALID_LEAVE",U_="INVALID_PUBLISH",F_="INVALID_UNPUBLISH",j_="INVALID_AUDIENCE",B_="INVALID_INITIALIZE",H_="INVALID_DUPLICATE_PUBLISHING",G_="INVALID_REMOTE_STREAM",J_="SUBSCRIBE_FAILED",z_="INVALID_ROLE",W_="INVALID_OPERATION_SWITCH_ROLE",q_="SWITCH_ROLE_TIMEOUT",K_="SWITCH_ROLE_FAILED",Q_="CLIENT_BANNED",X_="INVALID_OPERATION_START_PUBLISH_CDN",$_="INVALID_OPERATION_STOP_PUBLISH_CDN",Y_="INVALID_STREAM_ID",Z_="START_PUBLISH_CDN_FAILED",ev="STOP_PUBLISH_CDN_FAILED",tv="START_MIX_TRANSCODE",nv="STOP_MIX_TRANSCODE",rv="INVALID_AUDIO_VOLUME",iv="ENABLE_SMALL_STREAM_PUBLISHED",av="DISABLE_SMALL_STREAM_PUBLISHED",ov="NOT_SUPPORTED_SMALL_STREAM",sv="INVALID_SMALL_STREAM_PROFILE",cv="INVALID_PARAMETER_REMOTE_STREAM",uv="REMOTE_NOT_PUBLISH_SMALL_STREAM",dv="INVALID_SWITCH_DEVICE",lv="INVALID_SWITCH_DEVICE_PUBLISHING",hv="INVALID_REPLACE_TRACK",pv="INVALID_INITIALIZE_LOCAL_STREAM",fv="INVALID_ADD_TRACK_REPETITIVE",mv="INVALID_ADD_TRACK_REMOVING",_v="INVALID_ADD_TRACK_PUBLISHING",vv="INVALID_STREAM_INITIALIZED",gv="INVALID_ADD_TRACK_NUMBER",yv="INVALID_REMOVE_AUDIO_TRACK",Sv="INVALID_REMOVE_AUDIO_ADDING",kv="INVALID_REMOVE_AUDIO_ON",bv="INVALID_REMOVE_TRACK_PUBLISHING",Iv="INVALID_REMOVE_TRACK_NOT_PUBLISHING",Rv="INVALID_REMOVE_TRACK_NUMBER",Tv="INVALID_REMOVE_TRACK_NOT_PUBLISHED",Ev="START_MIX_TRANSCODE_FAILED",wv="STOP_MIX_TRANSCODE_FAILED",Cv="MIX_TRANSCODE_NOT_STARTED",Av="CANNOT_LESS_THAN_ZERO",Pv="MIX_PARAMS_VIDEO_FRAMERATE",xv="MIX_PARAMS_VIDEO_GOP",Dv="MIX_PARAMS_AUDIO_BITRATE",Nv="MIX_PARAMS_USER_Z_ORDER",Lv="MIX_PARAMS_NOT_SELF",Ov="MIX_PARAMS_USER_STREAM",Mv="INVALID_PLAY",Vv="INVALID_CREATE_STREAM_SOURCE",Uv="INVALID_CREATE_STREAM_SCREEN",Fv="INVALID_CREATE_STREAM_AUDIO",jv="INVALID_CREATE_STREAM_SCREEN_AUDIO",Bv="NOT_SUPPORTED_HTTP",Hv="NOT_SUPPORTED_WEBRTC",Gv="NOT_SUPPORTED_PROFILE",Jv="NOT_SUPPORTED_H264ENCODE",zv="NOT_SUPPORTED_H264DECODE",Wv="NOT_SUPPORTED_REPLACE_TRACK",qv="NOT_SUPPORTED_CAPTURE",Kv="MICROPHONE_NOT_FOUND",Qv="CAMERA_NOT_FOUND",Xv={AVOID_REPEATED_CALL:function(e){return"previous ".concat(e.name,"() is ongoing, please avoid repeated calls.")},INVALID_PARAMETER_REQUIRED:function(e){var t=e.key,n=e.rule,r=e.fnName,i=e.value;return"'".concat(t||n.name,"' is a required param when calling ").concat(r,"(), received: ").concat(i,".")},INVALID_PARAMETER_TYPE:function(e){var t=e.key,n=e.rule,r=e.fnName,i=e.value,a="".concat(t||n.name),o="";return o=Array.isArray(n.type)?n.type.join("|"):n.type,"'".concat(a,"' must be type of ").concat(o," when calling ").concat(r,"(), received type: ").concat(Gl(i),".")},INVALID_PARAMETER_EMPTY:function(e){var t=e.key,n=e.rule,r=e.fnName,i=e.value;return"'".concat(t||n.name,"' cannot be '").concat(i,"' when calling ").concat(r,"().")},INVALID_PARAMETER_INSTANCE:function(e){var t=e.key,n=e.rule,r=e.fnName,i=e.value,a="".concat(t||n.name),o="".concat(n.instanceOf.name||n.instanceOf);return"'".concat(a,"' must be instanceof ").concat(o," when calling ").concat(r,"(), received type: ").concat(Gl(i),".")},INVALID_PARAMETER_RANGE:function(e){var t=e.key,n=e.rule,r=e.fnName,i=e.value;return"'".concat(t||n.name,"' must be one of ").concat(n.values.join("|")," when calling ").concat(r,"(), received: ").concat(i,".")},API_CALL_TIMEOUT:function(e){return"".concat(e.commandDesc||e.command," timeout observed.")},SIGNAL_CHANNEL_RECONNECTION_FAILED:"signal channel reconnection failed, please check your network.",SIGNAL_CHANNEL_SETUP_FAILED:function(e){return"SignalChannel setup failure: (errorCode: ".concat(e.errorCode,", errorMsg: ").concat(e.errorMsg," }).")},ERROR_MESSAGE:function(e){var t="".concat(e.type," failed");return e.message&&(t="".concat(t,": ").concat(e.message,".")),t},SUBSCRIPTION_TIMEOUT:"remote server does not respond to the subscription.",EXCHANGE_SDP_TIMEOUT:"exchange sdp timeout.",DOWNLINK_RECONNECTION_FAILED:"downlink reconnection failed, please check your network and re-join room.",EXCHANGE_SDP_FAILED:function(e){return"exchange sdp failed ".concat(e.errMsg,".")},UPDATE_OFFER_TIMEOUT:"update offer timeout observed.",UPLINK_RECONNECTION_FAILED:"uplink reconnection failed, please check your network and publish again.",AUDIO:function(e){return e.error.toString()+" <audio>"},VIDEO:function(e){return e.error.toString()+" <video>"},INVALID_RECORDID:"recordId must be an integer number.",INVALID_PURE_AUDIO:"pureAudioPushMode must be 1 or 2.",INVALID_STREAMID:"streamId must be a sting literal within 64 bytes, and not be empty.",INVALID_USER_DEFINE_RECORDID:"userDefineRecordId must be a sting literal contains (a-zA-Z),(0-9), underline and hyphen, within 64 bytes, and not be empty.",INVALID_USER_DEFINE_PUSH_ARGS:"userDefinePushArgs must be a sting literal within 256 bytes, and not be empty.",INVALID_PROXY:'proxy server url must start with "wss://".',INVALID_JOIN:"duplicate join() called.",INVALID_ROOMID_STRING:function(e){return"'".concat(e,"' must be validate string when useStringRoomId is true.")},INVALID_ROOMID_INTEGER:function(e){return"'".concat(e,"' must be an integer between [1, 4294967294] when useStringRoomId is false.")},INVALID_SIGNAL_CHANNEL:"SignalChannel is not ready yet.",JOIN_ROOM_TIMEOUT:"join room timeout.",JOIN_ROOM_FAILED:function(e){return"Failed to join room - "+e.error},REJOIN_ROOM_FAILED:function(e){return"reJoin room: ".concat(e.roomId," failed, please check your network.")},INVALID_LEAVE:"please call leave() before destroy().",INVALID_PUBLISH:"please call join() before publish().",INVALID_UNPUBLISH:"stream has not been published yet.",INVALID_AUDIENCE:"no permission to publish() under live/".concat("audience",', please call switchRole("').concat("anchor",'") firstly before publish().'),INVALID_INITIALIZE:"cannot publish stream because stream is not initialized or is switching device.",INVALID_DUPLICATE_PUBLISHING:"duplicate publishing, please unpublish and then re-publish.",INVALID_SUBSCRIBE_UNDEFINED:"stream is undefined or null.",INVALID_SUBSCRIBE_LOCAL:"stream cannot be LocalStream.",INVALID_REMOTE_STREAM:"remoteStream does not exist because it has been unpublished by remote peer.",SUBSCRIBE_FAILED:function(e){return"failed to subscribe stream, reason: ".concat(e.message,".")},INVALID_ROLE:"switchRole can only be called in live mode.",INVALID_PARAMETER_SWITCH_ROLE:"role could only be set to a value as ".concat("anchor"," or ").concat("audience","."),INVALID_OPERATION_SWITCH_ROLE:"please call join() before switchRole().",SWITCH_ROLE_TIMEOUT:"switchRole timeout.",SWITCH_ROLE_FAILED:function(e){return"switchRole failed, errCode: ".concat(e.errCode," errMsg: ").concat(e.errMsg,".")},CLIENT_BANNED:function(e){return"client was banned because of "+e.reason+"."},INVALID_OPERATION_START_PUBLISH_CDN:"please call publish() before startPublishCDNStream().",INVALID_OPERATION_STOP_PUBLISH_CDN:"please call startPublishCDNStream() before stopPublishCDNStream().",START_PUBLISH_CDN_FAILED:function(e){return"startPublishCDNStream failed, errMsg: ".concat(e.message,".")},STOP_PUBLISH_CDN_FAILED:function(e){return"stopPublishCDNStream failed, errMsg: ".concat(e.message,".")},INVALID_STREAM_ID:function(e){return"'".concat(e,"' can only consist of uppercase and lowercase english letters (a-zA-Z), numbers (0-9), hyphens and underscores.")},START_MIX_TRANSCODE:"please call startMixTranscode() after join().",STOP_MIX_TRANSCODE:"please call stopMixTranscode() after startMixTranscode().",INVALID_AUDIO_VOLUME:"interval must be a number.",ENABLE_SMALL_STREAM_PUBLISHED:"Cannot enable small stream after localStream published.",DISABLE_SMALL_STREAM_PUBLISHED:"Cannot disable small stream after localStream published.",NOT_SUPPORTED_SMALL_STREAM:"your browser does not support opening small stream.",INVALID_SMALL_STREAM_PROFILE:"small stream profile is invalid.",INVALID_PARAMETER_REMOTE_STREAM:"remoteStream is invalid.",REMOTE_NOT_PUBLISH_SMALL_STREAM:"remote peer does not publish small stream.",INVALID_SWITCH_DEVICE:"cannot switch device on current stream.",INVALID_SWITCH_DEVICE_PUBLISHING:"cannot switch device when publishing localStream.",INVALID_REPLACE_TRACK:"cannot replace track when publishing localStream.",INVALID_INITIALIZE_LOCAL_STREAM:"local stream has not initialized yet.",INVALID_ADD_TRACK_REPETITIVE:"previous addTrack is ongoing, please avoid repetitive execution.",INVALID_ADD_TRACK_REMOVING:"cannot add track when a track is removing.",INVALID_ADD_TRACK_PUBLISHING:"cannot add track when publishing localStream.",INVALID_STREAM_INITIALIZED:"your local stream haven't been initialized yet.",INVALID_ADD_TRACK_NUMBER:"a Stream has at most one audio track and one video track.",INVALID_REMOVE_AUDIO_TRACK:"remove audio track is not supported.",INVALID_REMOVE_AUDIO_ADDING:"cannot remove track when a track is adding.",INVALID_REMOVE_AUDIO_ON:"previous removeTrack is ongoing, please avoid repetitive execution.",INVALID_REMOVE_TRACK_PUBLISHING:"cannot remove track when publishing localStream.",INVALID_REMOVE_TRACK_NOT_PUBLISHING:"the track to be removed is not being publishing.",INVALID_REMOVE_TRACK_NUMBER:"remove the only video track is not supported, please use replaceTrack or muteVideo.",INVALID_REMOVE_TRACK_NOT_PUBLISHED:function(e){return"try to replace ".concat(e.kind," track but there's no previous ").concat(e.kind," being published.")},START_MIX_TRANSCODE_FAILED:function(e){return"startMixTranscode failed, errMsg: ".concat(e.message,".")},STOP_MIX_TRANSCODE_FAILED:function(e){return"stopMixTranscode failed, errMsg: ".concat(e.message,".")},MIX_TRANSCODE_NOT_STARTED:"mixTranscode has not been started.",CANNOT_LESS_THAN_ZERO:function(e){var t=e.key,n=e.rule,r=e.fnName;e.value;return"'".concat(t||n.name,"' cannot be less than 0 when calling ").concat(r,"().")},MIX_PARAMS_VIDEO_FRAMERATE:"'config.videoFramerate' should be an integer between 0 and 30, excluding 0.",MIX_PARAMS_VIDEO_GOP:"'config.videoGOP' should be an integer between 1 and 8.",MIX_PARAMS_AUDIO_BITRATE:"'config.audioBitrate' should be an integer between 32 and 192.",MIX_PARAMS_USER_Z_ORDER:function(e){return"'".concat(e,"' is required and must be between 1 and 15.")},MIX_PARAMS_NOT_SELF:"'config.mixUsers' must contain self.",MIX_PARAMS_USER_STREAM:"'config.videoWidth' and 'config.videoHeight' of output stream should be contain all mix stream.",INVALID_PLAY:"duplicate play() call observed, please stop() firstly.",INVALID_USERID:"userId cannot be all spaces.",INVALID_CREATE_STREAM_SOURCE:"LocalStream must be created by createStream() with either audio/video or audioSource/videoSource, but can not be mixed with audio/video and audioSource/videoSource.",INVALID_CREATE_STREAM_SCREEN:"screen/video cannot be both true.",INVALID_CREATE_STREAM_AUDIO:"audio/screenAudio cannot be both true.",INVALID_CREATE_STREAM_SCREEN_AUDIO:"when screen is true, screenAudio can be configured.",NOT_SUPPORTED_HTTP:"not supported in http protocol, please use https protocol.",NOT_SUPPORTED_WEBRTC:"your browser does NOT support WebRTC!",NOT_SUPPORTED_PROFILE:"your browser does not support setVideoProfile.",NOT_SUPPORTED_MEDIA:"your browser does not support navigator.mediaDevices.",NOT_SUPPORTED_H264ENCODE:"your device does not support H.264 encoding.",NOT_SUPPORTED_H264DECODE:"your device does not support H.264 decoding.",NOT_SUPPORTED_REPLACE_TRACK:"replaceTrack is not supported in this browser, please use switchDevice or addTrack instead.",NOT_SUPPORTED_CAPTURE:"captureScreen is not supported, please use chrome.",MICROPHONE_NOT_FOUND:"no microphone detected, please check your microphone and the configuration on TRTC.createStream.",CAMERA_NOT_FOUND:"no camera detected, please check your camera and the configuration on TRTC.createStream."},$v=_d+"module-ErrorCode.html",Yv=function(){if(!Vc())return!1;var e=localStorage.getItem("trtc_error_assistance");e&&!function(e){var t=e.saveTime&&(new Date).getTime()-e.saveTime>=6048e5,n=!e.saveVersion||"4.11.11"!==e.saveVersion;return t||n}(JSON.parse(e))||(pf.log("request error info"),function(){var e=new XMLHttpRequest;if(e.open("GET","https://web.sdk.qcloud.com/trtc/webrtc/download/error-message/0.0.1/script.js",!1),e.send(null),4===e.readyState&&200===e.status){var t=document.createElement("script");t.type="text/javascript",t.text=e.responseText,document.body.appendChild(t),localStorage.setItem("trtc_error_assistance",JSON.stringify({message:e.responseText,saveTime:(new Date).getTime(),saveVersion:"4.11.11"})),document.body.removeChild(t)}}())};function Zv(e){var t=e.key,n=e.data,r=e.link,i="",a="",o="";Vl(Xv[t])?i=Xv[t](n):Fl(Xv[t])&&(i=Xv[t]);var s=function(){if(window.TRTC_ERROR_INFO&&window.TRTC_ERROR_LINK)return{TRTC_ERROR_INFO:window.TRTC_ERROR_INFO,TRTC_ERROR_LINK:window.TRTC_ERROR_LINK};var e=localStorage.getItem("trtc_error_assistance");if(e){e=JSON.parse(e);var t=document.createElement("script");t.type="text/javascript",t.text=e.message,document.body.appendChild(t);var n=window.TRTC_ERROR_INFO,r=window.TRTC_ERROR_LINK;return document.body.removeChild(t),{TRTC_ERROR_INFO:n,TRTC_ERROR_LINK:r}}return{}}(),c=s.TRTC_ERROR_INFO,u=s.TRTC_ERROR_LINK;c&&c[t]&&(Vl(c[t])?a=c[t](n):Fl(c[t])&&(a=c[t])),r?o=_d+"".concat(r.className,".html#").concat(r.className===dd?".":"").concat(r.fnName):u&&u[t]&&(o=_d+u[t]);var d=i;return d+=" "+a,d+=a?o?"查看文档："+o:"查看文档："+$v:o?"Refer to："+o:"Refer to："+$v}var eg=function(){function e(t){a(this,e),this.client_=t.client,this.sdkAppId_=t.sdkAppId,this.userId_=t.userId,this.userSig_=t.userSig,this.url_=t.url,this.backupUrl_=t.backupUrl,this.version_=t.version,this.urlWithParam_="".concat(this.url_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.backupUrlWithParam_="".concat(this.backupUrl_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.isConnected_=!1,this.isConnecting_=!1,this.socketInUse_=null,this.socket_=null,this.backupSocket_=null,this.backupTimer_=-1,this.signalInfo_={},this.currentState_=zf,this.reconnectionCount_=0,this.reconnectionTimer_=-1,this.seq_=0,this.log_=pf.createLogger({id:"ws|"+this.userId_,userId:this.userId_,sdkAppId:this.sdkAppId_}),this.pingPongTimeoutId_=-1,this.pingTimeoutId_=-1,this.emitter_=new rh}var t;return s(e,[{key:"connect",value:function(){var e=this;this.log_.info("connect to url: ".concat(this.urlWithParam_)),this.emitter_.emit(Hf,{prevState:this.currentState_,state:Wf}),this.currentState_=Wf,this.socket_=new WebSocket(this.urlWithParam_),this.bindSocket(this.socket_),this.backupTimer_=setTimeout((function(){e.isConnected_||(e.log_.info("trying to connect to backupUrl"),e.tryConnectBackup())}),5e3)}},{key:"tryConnectBackup",value:function(){this.backupSocket_||(this.unbindAndCloseSocket("main"),this.log_.debug("try to connect to url: ".concat(this.backupUrlWithParam_)),this.backupSocket_=new WebSocket(this.backupUrlWithParam_),this.bindSocket(this.backupSocket_))}},{key:"bindSocket",value:function(e){e.onopen=this.onopen.bind(this),e.onclose=this.onclose.bind(this),e.onerror=this.onerror.bind(this),e.onmessage=this.onmessage.bind(this)}},{key:"unbindSocket",value:function(e){e.onopen=function(){},e.onclose=function(){},e.onerror=function(){},e.onmessage=function(){}}},{key:"unbindAndCloseSocket",value:function(e){if("main"===e){if(this.socket_){this.unbindSocket(this.socket_);try{this.socket_.close(1e3)}catch(mk){}this.socket_=null}}else if(this.backupSocket_){this.unbindSocket(this.backupSocket_);try{this.backupSocket_.close(1e3)}catch(mk){}this.backupSocket_=null}}},{key:"clearBackupTimer",value:function(){-1!==this.backupTimer_&&(clearTimeout(this.backupTimer_),this.backupTimer_=-1)}},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"onopen",value:function(e){if(!this.isConnected_){this.isConnected_=!0,this.isConnecting_=!1,this.clearBackupTimer(),e.target===this.socket_?(this.unbindAndCloseSocket("backup"),this.socketInUse_=this.socket_):(this.unbindAndCloseSocket("main"),this.socketInUse_=this.backupSocket_);var t=e.target.url;this.log_.info("websocket[".concat(t,"] is connected")),this.emitter_.emit(Hf,{prevState:this.currentState_,state:Kf}),this.currentState_===Wf?this.addSignalEvent(Hm,"signal channel is connected"):this.currentState_===qf&&this.addSignalEvent(Wm,"signal channel reconnect success"),this.currentState_=Kf,this.emitter_.emit(Gf)}}},{key:"onclose",value:function(e){var t=e.target.url,n=e.target===this.socketInUse_;this.log_.info("websocket[".concat(t," InUse: ").concat(n,"] is closed with code: ").concat(e.code)),e.target===this.socketInUse_&&(this.isConnected_=!1,e.wasClean&&1e3===e.code?(this.emitter_.emit(Hf,{prevState:this.currentState_,state:zf}),this.currentState_=zf,this.addSignalEvent(Bm,"signal channel is disconnected")):(this.log_.warn("onclose code:".concat(e.code," reason:").concat(e.reason)),this.log_.warn("close current websocket and schedule a reconnect timeout"),this.socketInUse_.onclose=function(){},this.socketInUse_.close(4011),this.socket_=this.backupSocket_=this.socketInUse_=null,this.reconnect("main")))}},{key:"onerror",value:function(e){var t=e.target.url;this.log_.error("websocket[".concat(t,"] error observed")),this.isConnected_?e.target===this.socketInUse_&&(this.isConnected_=!1,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup"),this.socketInUse_=null,this.reconnect("main")):(this.isReconnecting_||Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Tu,code:Cl.UNKNOWN}),e.target==this.socket_?(this.unbindAndCloseSocket("main"),this.reconnect("backup")):(this.unbindAndCloseSocket("backup"),this.reconnect("main"))),this.isConnecting_=!1,this.isConnected_=!1}},{key:"onmessage",value:function(e){var t=this;if(this.isConnected_){var n=JSON.parse(e.data),r=n.cmd,i=n.content,a=Object.values(Qf),o=Object.keys(Qf)[a.indexOf(r)],s=$f[o];if(!Xf.includes(r)){var c=e.target==this.socket_?this.url_:this.backupUrl_;if(this.log_.debug("websocket[".concat(c,"] received message: ").concat(e.data)),this.log_.info("Received event: [ ".concat(s||"unknown cmd: "+r," ]")),(s===$f.UPDATE_REMOTE_SDP||s===$f.UPDATE_AUDIO_SSRC||s===$f.UPDATE_VIDEO_SSRC)&&i.offersdp)try{var u=JSON.parse(i.offersdp),d=u.audiossrc,l=u.videossrc,h=u.rtxssrc;this.log_.info("ssrc info in offersdp: [ audiossrc: ".concat(d," videossrc: ").concat(l," rtxssrc: ").concat(h," ]"))}catch(m){}}switch(r){case Qf.CHANNEL_SETUP_SUCCESS:this.signalInfo_.relayIp=i.relayip,this.signalInfo_.relayInnerIp=i.innerip,this.signalInfo_.signalIp=i.signalip,this.signalInfo_.localIp=i.localip,this.signalInfo_.dataPort=i.dataport,this.signalInfo_.stunPort=i.stunport,this.signalInfo_.checkSigSeq=i.checkSigSeq,this.signalInfo_.socketId=i.socketid,this.signalInfo_.tinyId=i.tinyid,this.signalInfo_.openId=i.openid,this.signalInfo_.stunPortList=i.stunportList,!i.stunportList||i.stunportList.length<=0?this.signalInfo_.stunServers="stun:"+i.relayip+":"+i.stunport:(this.signalInfo_.stunServers=[],i.stunportList.forEach((function(e){var n="stun:"+i.relayip+":"+e;t.signalInfo_.stunServers.push(n)}))),i.cgiurl&&(this.signalInfo_.logCgiUrl=i.cgiurl),i.svrTime&&function(e){mo=e-(new Date).getTime();var t=new Date;t.setTime(e),pf.info("baseTime from server: "+t+" offset: "+mo)}(i.svrTime),this.log_.info("ChannelSetup Success: signalIp:".concat(i.signalip," relayIp:").concat(i.relayip," clientIp:").concat(i.localip," checkSigSeq:").concat(i.checkSigSeq)),this.log_.info("start ping pong"),this.startPingPong(),this.isReconnecting_&&(this.reconnectionCount_=0,this.clearReconnectionTimer(),1===i.rc&&this.emitter_.emit($f.REQUEST_REBUILD_SESSION,{signalInfo:this.signalInfo_})),this.emitter_.emit(s,{signalInfo:this.signalInfo_});break;case Qf.REBUILD_SESSION_RESULT:0===i.result?(this.log_.info("reconnect - rebuild session succeeded"),this.client_.checkConnectionsToReconnect()):(this.emitter_.emit($f.CLIENT_REJOIN),this.log_.error("reconnect - rebuild session failed: ".concat(JSON.stringify(i))));break;case Qf.CHANNEL_SETUP_FAILED:if(!this.isReconnecting_){var p="sdkAppId invalid",f="";Ul(i.errorCode)||(p=i.errorCode,f=i.errorMsg);var m=new Al({code:Cl.SIGNAL_CHANNEL_SETUP_FAILED,extraCode:p,message:Zv({key:m_,data:{errorCode:p,errorMsg:f}})});this.close(),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Tu,error:m}),this.emitter_.emit(Jf,m)}break;default:this.emitter_.emit(s,{data:n})}}}},{key:"addSignalEvent",value:function(e,t){t_(this.userId_,{eventId:e,eventDesc:t,timestamp:_o(),userId:this.userId_,tinyId:this.signalInfo_.tinyId})}},{key:"reconnect",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"main";if(this.isConnecting_||-1!==this.reconnectionTimer_)this.log_.info("signal channel is reconnecting, ignoring current reconnection");else{if(this.reconnectionCount_>=30){this.log_.warn("SDK has tried reconnect signal channel for ".concat(30," times, but all failed. please check your network"));var n=new Al({code:Cl.SIGNAL_CHANNEL_RECONNECTION_FAILED,message:Zv({key:f_})});return Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Eu,error:n}),this.addSignalEvent(qm,"signal channel reconnect fail"),void this.emitter_.emit(Jf,n)}this.isConnecting_=!0,this.reconnectionCount_++,this.currentState_!==qf&&(this.emitter_.emit(Hf,{prevState:this.currentState_,state:qf}),this.currentState_=qf,this.addSignalEvent(zm,"signal channel is reconnecting")),this.log_.warn("reconnecting to ".concat(t," signal channel [").concat(this.reconnectionCount_,"/").concat(30,"]"));var r=this.getReconnectionUrl(t);"main"===t?(this.socket_=new WebSocket(r),this.bindSocket(this.socket_)):(this.backupSocket_=new WebSocket(r),this.bindSocket(this.backupSocket_));var i=Ml(this.reconnectionCount_);this.reconnectionTimer_=setTimeout((function(){e.log_.warn("reconnect ".concat(t," signal channel timeout(").concat(i/1e3,"s), close and try again")),e.isConnecting_=!1,e.clearReconnectionTimer(),e.unbindAndCloseSocket(t),e.reconnect("main"===t?"backup":"main")}),i)}}},{key:"isConnected",value:function(){return this.isConnected_}},{key:"isReconnecting_",get:function(){return-1!==this.reconnectionTimer_}},{key:"getReconnectionUrl",value:function(e){var t="main"===e?this.urlWithParam_:this.backupUrlWithParam_;return r_(this.signalInfo_)||-1!==t.indexOf("&rc=1")||(t+="&iip="+this.signalInfo_.relayInnerIp+"&dp="+this.signalInfo_.dataPort+"&oip="+this.signalInfo_.relayIp+"&sp="+this.signalInfo_.stunPort+"&rc=1"),t}},{key:"send",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);return r.data=t,void 0!==n&&(r.srctinyid=n),this.socketInUse_.send(JSON.stringify(r)),r.seq}}},{key:"sendWithoutUA",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e,!1);return r.data=t,void 0!==n&&(r.srctinyid=n),this.socketInUse_.send(JSON.stringify(r)),r.seq}}},{key:"sendWithReport",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);return r.data=t,r.report=n,this.socketInUse_.send(JSON.stringify(r)),r.seq}}},{key:"sendWaitForResponse",value:function(e){var t=this,n=e.command,r=e.data,i=e.timeout,a=void 0===i?5e3:i,o=e.responseCommand,s=e.commandDesc,c=e.retry,u=void 0===c?0:c,d=0;return new Promise((function(e,i){var c,l,h=function(){if(d<u)return d++,t.log_.warn("".concat(s," timeout observed, retrying [").concat(d,"/").concat(u,"]")),void f();t.off(o,p);var e=new Al({code:Cl.API_CALL_TIMEOUT,message:Zv({key:p_,data:{commandDesc:s,command:n}})});t.log_.warn(e),i(e)},p=function n(r){r.data.seq===l&&(clearTimeout(c),t.off(o,n),e(r))},f=function(){c=setTimeout(h,a),l=t.send(n,r,0)};t.on(o,p),f()}))}},{key:"startPingPong",value:(t=i(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,-1===this.pingPongTimeoutId_){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,this.ping();case 5:this.pingPongTimeoutId_=setTimeout((function(){t.pingPongTimeoutId_=-1,t.startPingPong()}),1e4),this.client_.isRelayMaybeFailed()&&this.emitter_.emit($f.CLIENT_REJOIN),e.next=14;break;case 9:e.prev=9,e.t0=e.catch(0),this.log_.warn("ping-pong failed, start signal reconnection"),this.close(),this.reconnect("main");case 14:case"end":return e.stop()}}),e,this,[[0,9]])}))),function(){return t.apply(this,arguments)})},{key:"stopPingPong",value:function(){this.log_.info("stop ping pong"),clearTimeout(this.pingTimeoutId_),clearTimeout(this.pingPongTimeoutId_),this.pingTimeoutId_=-1,this.pingPongTimeoutId_=-1}},{key:"ping",value:function(){var e=this;return new Promise((function(t,n){if(-1!==e.pingTimeoutId_)return t();e.sendWithoutUA(am),e.once($f.PONG,(function(){clearTimeout(e.pingTimeoutId_),e.pingTimeoutId_=-1,t()})),e.pingTimeoutId_=setTimeout((function(){e.pingTimeoutId_=-1,n()}),1e4)}))}},{key:"createSendMessage",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n={tag_key:e,data:"",openid:this.userId_,tinyid:this.signalInfo_.tinyId,version:this.version_,seq:++this.seq_};return t&&(n.ua=navigator.userAgent),n}},{key:"getCurrentState",value:function(){return this.currentState_}},{key:"getSocketId",value:function(){return this.signalInfo_.socketId}},{key:"close",value:function(){this.log_.info("close SignalChannel"),this.clearBackupTimer(),this.clearReconnectionTimer(),this.stopPingPong(),this.isConnecting_=!1,this.isConnected_=!1,this.socketInUse_=null,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup")}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"removeListener",value:function(e,t,n){this.emitter_.removeListener(e,t,n)}},{key:"once",value:function(e,t,n){this.emitter_.once(e,t,n)}},{key:"off",value:function(e,t,n){this.emitter_.off(e,t,n)}}]),e}(),tg=Jt.find,ng=!0;"find"in[]&&Array(1).find((function(){ng=!1})),Tt({target:"Array",proto:!0,forced:ng},{find:function(e){return tg(this,e,arguments.length>1?arguments[1]:void 0)}}),Er("find");yi("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),wi);var rg=T((function(e){var t=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":""}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(t).forEach((function(e){t[e].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}))})),ig=(rg.v,rg.o,rg.s,rg.i,rg.u,rg.e,rg.p,rg.z,rg.r,rg.t,rg.c,rg.b,rg.m,rg.a,T((function(e,t){var n=function(e){return String(Number(e))===e?Number(e):e},r=function(e,t,r){var i=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:i&&!t[e.name]&&(t[e.name]={});var a=e.push?{}:i?t[e.name]:t;!function(e,t,r,i){if(i&&!r)t[i]=n(e[1]);else for(var a=0;a<r.length;a+=1)null!=e[a+1]&&(t[r[a]]=n(e[a+1]))}(r.match(e.reg),a,e.names,e.name),e.push&&t[e.push].push(a)},i=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},n=[],a=t;return e.split(/(\r\n|\r|\n)/).filter(i).forEach((function(e){var t=e[0],i=e.slice(2);"m"===t&&(n.push({rtp:[],fmtp:[]}),a=n[n.length-1]);for(var o=0;o<(rg[t]||[]).length;o+=1){var s=rg[t][o];if(s.reg.test(i))return r(s,a,i)}})),t.media=n,t};var a=function(e,t){var r=t.split(/=(.+)/,2);return 2===r.length?e[r[0]]=n(r[1]):1===r.length&&t.length>1&&(e[r[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(a,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],r=e.split(" ").map(n),i=0;i<r.length;i+=3)t.push({component:r[i],ip:r[i+1],port:r[i+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(a,{})}))},t.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var t,r=!1;return"~"!==e[0]?t=n(e):(t=n(e.substring(1,e.length)),r=!0),{scid:t,paused:r}}))}))}}))),ag=(ig.parse,ig.parseParams,ig.parseFmtpConfig,ig.parsePayloads,ig.parseRemoteCandidates,ig.parseImageAttributes,ig.parseSimulcastStreamList,/%[sdv%]/g),og=function(e){var t=1,n=arguments,r=n.length;return e.replace(ag,(function(e){if(t>=r)return e;var i=n[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(i);case"%d":return Number(i);case"%v":return""}}))},sg=function(e,t,n){var r=[e+"="+(t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format)];if(t.names)for(var i=0;i<t.names.length;i+=1){var a=t.names[i];t.name?r.push(n[t.name][a]):r.push(n[t.names[i]])}else r.push(n[t.name]);return og.apply(null,r)},cg=["v","o","s","i","u","e","p","c","b","t","r","z","a"],ug=["i","c","b","a"],dg={write:function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var n=t.outerOrder||cg,r=t.innerOrder||ug,i=[];return n.forEach((function(t){rg[t].forEach((function(n){n.name in e&&null!=e[n.name]?i.push(sg(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){i.push(sg(t,n,e))}))}))})),e.media.forEach((function(e){i.push(sg("m",rg.m[0],e)),r.forEach((function(t){rg[t].forEach((function(n){n.name in e&&null!=e[n.name]?i.push(sg(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){i.push(sg(t,n,e))}))}))}))})),i.join("\r\n")+"\r\n"},parse:ig.parse,parseParams:ig.parseParams,parseFmtpConfig:ig.parseFmtpConfig,parsePayloads:ig.parsePayloads,parseRemoteCandidates:ig.parseRemoteCandidates,parseImageAttributes:ig.parseImageAttributes,parseSimulcastStreamList:ig.parseSimulcastStreamList},lg=function(e){return dg.parse(e)},hg=function(e){return dg.write(e)},pg=function(e){var t=lg(e);return t.media.forEach((function(e){e.type===Gu&&e.fmtp.forEach((function(e){e.config+=";sprop-stereo=1;stereo=1"}))})),hg(t)};function fg(e){var t=lg(e);return t.media.forEach((function(e){if(e.type===Ju){var t=new Set;e.rtp.forEach((function(e){var n=e.payload;return"H264"===e.codec&&t.add(n)})),e.fmtp.forEach((function(e){var n=e.payload,r=e.config.match(/apt=(\d+)/);r&&r[1]&&t.has(Number(r[1]))&&t.add(n)}));var n=function(e){var n=e.payload;return!t.has(n)};e.rtp=e.rtp.filter(n),e.rtcpFb=e.rtcpFb.filter(n),e.fmtp=e.fmtp.filter(n),e.payloads=e.payloads.split(" ").filter((function(e){return!t.has(Number(e))})).join(" ")}})),hg(t)}var mg,_g=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))},vg=new(function(){function e(){a(this,e),this.intervalMap_=new Map}return s(e,[{key:"setInterval",value:function(e){function t(t,n){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){var n=this,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!window||!window.requestAnimationFrame)return setInterval(e,t);var i=_g(),a=zl(),o=a;this.intervalMap_.set(i,{rafId:null,timeoutId:null,onVisibilityChange:null});var s=function s(){if(r&&document.hidden){e();var c=setTimeout(s,t);n.setTimeoutId(i,c),a=zl(),o=a}else{(o=zl())-a>=t&&(a=o,e());var u=requestAnimationFrame(s);n.setRafId(i,u)}},c=requestAnimationFrame(s);if(this.setRafId(i,c),r){var u=function(){if(document.hidden){var e=zl()-a;if(e>=t)s();else{var r=setTimeout(s,t-e);n.setTimeoutId(i,r)}}};document.addEventListener("visibilitychange",u),this.setOnVisibilityChange(i,u)}return i}))},{key:"clearInterval",value:function(e){if(this.intervalMap_.has(e)){var t=this.intervalMap_.get(e),n=t.rafId,r=t.timeoutId,i=t.onVisibilityChange;cancelAnimationFrame(n),clearTimeout(r),document.removeEventListener("visibilitychange",i),this.intervalMap_.delete(e)}}},{key:"setTimeoutId",value:function(e,t){if(this.intervalMap_.has(e)){var n=this.intervalMap_.get(e);n.timeoutId&&clearTimeout(n.timeoutId),n.timeoutId=t}}},{key:"setRafId",value:function(e,t){if(this.intervalMap_.has(e)){var n=this.intervalMap_.get(e);n.rafId&&cancelAnimationFrame(n.rafId),n.rafId=t}}},{key:"setOnVisibilityChange",value:function(e,t){this.intervalMap_.has(e)&&(this.intervalMap_.get(e).onVisibilityChange=t)}}]),e}()),gg=new(function(){function e(){a(this,e),this.prefix_="TRTC",this.queue_=new Map,this.intervalId_=vg.setInterval(this.doFlush.bind(this),2e4),this.checkStorage()}return s(e,[{key:"getRealKey",value:function(e){return"".concat(this.prefix_,"_").concat(e)}},{key:"checkStorage",value:function(){var e=this;Vc()&&Object.keys(localStorage).filter((function(t){if(t.startsWith(e.prefix_)){var n=JSON.parse(localStorage.getItem(t));if(n&&n.expiresIn<Date.now())return!0}return!1})).forEach((function(e){return localStorage.removeItem(e)}))}},{key:"doFlush",value:function(){if(Vc())try{var e,t=b(this.queue_);try{for(t.s();!(e=t.n()).done;){var n=g(e.value,2),r=n[0],i=n[1];localStorage.setItem(r,JSON.stringify(i))}}catch(a){t.e(a)}finally{t.f()}}catch(mk){pf.warn(mk)}}},{key:"getItem",value:function(e){if(!Vc())return null;try{var t=JSON.parse(localStorage.getItem(this.getRealKey(e)));return t&&t.expiresIn>=Date.now()?t.value:null}catch(mk){pf.warn(mk)}}},{key:"setItem",value:function(e,t){if(Vc())try{var n={expiresIn:Date.now()+6048e5,value:t};this.queue_.set(this.getRealKey(e),n)}catch(mk){pf.warn(mk)}}},{key:"deleteItem",value:function(e){if(!Vc())return!1;try{return e=this.getRealKey(e),this.queue_.delete(e),localStorage.removeItem(e),!0}catch(mk){return pf.warn(mk),!1}}},{key:"clear",value:function(){if(Vc())try{localStorage.clear()}catch(mk){pf.warn(mk)}}}]),e}()),yg={result:!1,detail:{isBrowserSupported:!1,isWebRTCSupported:!1,isMediaDevicesSupported:!1,isH264EncodeSupported:!1,isVp8EncodeSupported:!1,isH264DecodeSupported:!1,isVp8DecodeSupported:!1}};(mg=gg.getItem("checkResult"))&&mg.ua===navigator.userAgent&&(yg=mg.checkResult,Cg());var Sg=function(){return!mc&&!Gs&&(!(zs&&Ws<80)&&!(Bs&&Hs<56))},kg=function(){return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter((function(e){return e in window})).length>0},bg=function(){if(!navigator.mediaDevices)return!1;var e=["getUserMedia","enumerateDevices"];return e.filter((function(e){return e in navigator.mediaDevices})).length===e.length},Ig=function(){var e=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!yg.detail.isH264EncodeSupported&&!yg.detail.isVp8EncodeSupported){e.next=2;break}return e.abrupt("return",{isH264EncodeSupported:yg.detail.isH264EncodeSupported,isVp8EncodeSupported:yg.detail.isVp8EncodeSupported});case 2:return t="",n=!1,r=!1,e.prev=5,i=new RTCPeerConnection,(a=document.createElement("canvas")).getContext("2d"),o=a.captureStream(0),i.addTrack(o.getVideoTracks()[0],o),e.next=13,i.createOffer();case 13:return-1!==(t=e.sent).sdp.toLowerCase().indexOf("h264")&&(n=!0),-1!==t.sdp.toLowerCase().indexOf("vp8")&&(r=!0),i.close(),yg.detail.isH264EncodeSupported=n,yg.detail.isVp8EncodeSupported=r,e.abrupt("return",{isH264EncodeSupported:yg.detail.isH264EncodeSupported,isVp8EncodeSupported:yg.detail.isVp8EncodeSupported});case 22:return e.prev=22,e.t0=e.catch(5),e.abrupt("return",{isH264EncodeSupported:!1,isVp8EncodeSupported:!1});case 25:case"end":return e.stop()}}),e,null,[[5,22]])})));return function(){return e.apply(this,arguments)}}();function Rg(){return Tg.apply(this,arguments)}function Tg(){return(Tg=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(){var e=i(regeneratorRuntime.mark((function e(t){var n,r,a,o,s,c,u,d,l,h,p,f,m,_;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={encode:!1,decode:!1},r=null,e.prev=2,a=document.createElement("canvas"),o=a.getContext("2d"),a.width=640,a.height=480,s=setInterval((function(){o.fillText("test",Math.floor(640*Math.random()),Math.floor(480*Math.random()))}),33),c=-1,u=-1,r=function(){clearInterval(c),clearInterval(s),clearTimeout(u),l.close(),h.close(),d.getTracks().forEach((function(e){return e.stop()}))},u=setTimeout((function(){r(),t(n)}),3e3),d=a.captureStream(),l=new RTCPeerConnection({}),h=new RTCPeerConnection({offerToReceiveAudio:!0,offerToReceiveVideo:!0}),l.addEventListener("icecandidate",(function(e){return h.addIceCandidate(e.candidate)})),h.addEventListener("icecandidate",(function(e){return l.addIceCandidate(e.candidate)})),l.addTrack(d.getVideoTracks()[0],d),e.next=20,l.createOffer();case 20:return p=e.sent,e.next=23,l.setLocalDescription(p);case 23:return e.next=25,h.setRemoteDescription(p);case 25:return e.next=27,h.createAnswer();case 27:return f=e.sent,m=lg(f.sdp),_=m.media[0].rtp.findIndex((function(e){return"H264"===e.codec})),m.media[0].rtp=[m.media[0].rtp[_]],m.media[0].fmtp=m.media[0].fmtp.filter((function(e){return e.payload===m.media[0].rtp[0].payload})),m.media[0].rtcpFb=m.media[0].rtcpFb.filter((function(e){return e.payload===m.media[0].rtp[0].payload})),f.sdp=hg(m),e.next=36,h.setLocalDescription(f);case 36:return e.next=38,l.setRemoteDescription(f);case 38:c=setInterval(i(regeneratorRuntime.mark((function e(){var i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.encode&&n.decode&&(r(),t(n)),e.next=3,l.getStats();case 3:return i=e.sent,e.next=6,h.getStats();case 6:a=e.sent,n.encode||i.forEach((function(e){"outbound-rtp"===e.type&&e.mediaType===Ju&&e.framesEncoded>0&&(n.encode=!0)})),n.decode||a.forEach((function(e){"inbound-rtp"===e.type&&e.mediaType===Ju&&e.framesDecoded>0&&(n.decode=!0)}));case 9:case"end":return e.stop()}}),e)}))),500),e.next=46;break;case 41:e.prev=41,e.t0=e.catch(2),r(),pf.warn(e.t0),t(n);case 46:case"end":return e.stop()}}),e,null,[[2,41]])})));return function(t){return e.apply(this,arguments)}}()));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Eg=function(){var e=i(regeneratorRuntime.mark((function e(){var t,n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!yg.detail.isH264DecodeSupported&&!yg.detail.isVp8DecodeSupported){e.next=2;break}return e.abrupt("return",{isH264DecodeSupported:yg.detail.isH264DecodeSupported,isVp8DecodeSupported:yg.detail.isVp8DecodeSupported});case 2:return t="",n=!1,r=!1,e.prev=5,i=new RTCPeerConnection,e.next=9,i.createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1});case 9:return-1!==(t=e.sent).sdp.toLowerCase().indexOf("h264")&&(n=!0),-1!==t.sdp.toLowerCase().indexOf("vp8")&&(r=!0),i.close(),e.abrupt("return",{isH264DecodeSupported:n,isVp8DecodeSupported:r});case 16:return e.prev=16,e.t0=e.catch(5),e.abrupt("return",{isH264DecodeSupported:!1,isVp8DecodeSupported:!1});case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),wg=function(e,t){var n=null;return function(){if(n)return n;for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return(n=e.apply(t||this,i)).then((function(e){return n=null,e})).catch((function(e){throw n=null,e})),n}}(function(){var e=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d,l,h;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!yg.result){e.next=2;break}return e.abrupt("return",yg);case 2:return t=Sg(),n=kg(),r=bg(),e.next=7,Ig();case 7:return i=e.sent,a=i.isH264EncodeSupported,o=i.isVp8EncodeSupported,e.next=12,Eg();case 12:if(s=e.sent,c=s.isH264DecodeSupported,u=s.isVp8DecodeSupported,!(a&&c&&(Ic||Tc||Lc)&&wc()<79)){e.next=23;break}return e.next=18,Rg();case 18:d=e.sent,l=d.encode,h=d.decode,a=l,c=h;case 23:return yg.result=t&&n&&r&&(a||o)&&(c||u),yg.detail.isBrowserSupported=t,yg.detail.isWebRTCSupported=n,yg.detail.isMediaDevicesSupported=r,yg.detail.isH264EncodeSupported=a,yg.detail.isVp8EncodeSupported=o,yg.detail.isH264DecodeSupported=c,yg.detail.isVp8DecodeSupported=u,Cg(),gg.setItem("checkResult",{ua:navigator.userAgent,checkResult:yg}),e.abrupt("return",yg);case 34:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}());function Cg(){Object.keys(yg.detail).findIndex((function(e){return!yg.detail[e]}))>=0&&pf.error("".concat(navigator.userAgent," ").concat(JSON.stringify(yg.detail)))}var Ag=function(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getDisplayMedia)},Pg=function(){return"RTCPeerConnection"in window&&"getReceivers"in window.RTCPeerConnection.prototype},xg=function(){return"RTCPeerConnection"in window&&"getSenders"in window.RTCPeerConnection.prototype},Dg=function(){return"RTCPeerConnection"in window&&"getTransceivers"in window.RTCPeerConnection.prototype},Ng=function(){return"RTCRtpTransceiver"in window&&"stop"in window.RTCRtpTransceiver.prototype},Lg=function(){return"RTCRtpSender"in window&&"setParameters"in window.RTCRtpSender.prototype&&xg()},Og=function(){return!!Ul(navigator.mediaDevices)&&(pf.error(Xv.NOT_SUPPORTED_MEDIA),!0)},Mg=function(){return"http:"===location.protocol&&!Mc&&(pf.error(Xv.NOT_SUPPORTED_HTTP),!0)},Vg=function(e){return!("candidate-pair"!==e.type||!e.nominated||"in-progress"!==e.state&&"succeeded"!==e.state)&&!(Bl(e.selected)&&!e.selected)},Ug=new Map([[Fs,"Android"],[Vs,"iOS"],[hc,"Windows"],[pc,"MacOS"],[fc,"Linux"]]),Fg=function(){var e="unknown";return Ug.get(!0)&&(e=Ug.get(!0)),e};function jg(){var e="";screen.width&&(e+=(screen.width?screen.width*window.devicePixelRatio:"")+" * "+(screen.height?screen.height*window.devicePixelRatio:""));return e}function Bg(){var e=!1;return(navigator.getUserMedia||navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)&&(e=!0),e}function Hg(){for(var e={isSupported:!1},t=["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"],n=0;n<t.length;n++)if(t[n]in window){e.isSupported=!0;break}return e.isSupported}function Gg(){return!tc&&!Vs&&!(!Sg()||!("captureStream"in HTMLCanvasElement.prototype))}wg();var Jg="stream-added",zg="stream-removed",Wg="stream-updated",qg="stream-subscribed",Kg="error",Qg="connection-state-changed",Xg="stream-added",$g="stream-removed",Yg="stream-updated",Zg="stream-subscribed",ey="connection-state-changed",ty="peer-join",ny="peer-leave",ry="mute-audio",iy="mute-video",ay="unmute-audio",oy="unmute-video",sy="client-banned",cy="network-quality",uy="audio-volume",dy="error",ly="player-state-changed",hy="screen-sharing-stopped",py="connection-state-changed",fy="error",my="player-state-changed",_y=function(){function e(t){a(this,e);var n=t.getUserId();this.log_=pf.createLogger({id:n,userId:n,sdkAppId:t.getSDKAppId()}),this.prevReport_={},this.prevEncoderImplementation_="",this.prevQualityLimitationReason_="",this.prevDecoderImplementationMap_=new Map}var t,n,r,o;return s(e,[{key:"getSenderStats",value:(o=i(regeneratorRuntime.mark((function e(t){var n,r,i,a=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n={audio:{bytesSent:0,packetsSent:0,audioLevel:0,totalAudioEnergy:0},video:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,fpsCapture:0,smallFramesEncoded:0,smallFPSCapture:0,smallFramesSent:0},rtt:0},r=t.getPeerConnection(),i=t.getSSRC(),!r){e.next=14;break}return e.prev=4,e.next=7,r.getStats();case 7:e.sent.forEach((function(e){if("outbound-rtp"===e.type)if(e.mediaType===Ju){if(!Bs&&Ul(e.trackId))return;e.ssrc!==i.video||Ul(e.encoderImplementation)||a.prevEncoderImplementation_===e.encoderImplementation||(a.log_.info("encoderImplementation change to ".concat(e.encoderImplementation)),a.prevEncoderImplementation_=e.encoderImplementation),e.ssrc!==i.video||Ul(e.qualityLimitationReason)||a.prevQualityLimitationReason_===e.qualityLimitationReason||(a.log_.info("qualityLimitationReason change to ".concat(e.qualityLimitationReason)),a.prevQualityLimitationReason_=e.qualityLimitationReason);var r=t.getSSRC();e.ssrc===r.video?(n.video.bytesSent=e.bytesSent,n.video.packetsSent=e.packetsSent,n.video.framesEncoded=e.framesEncoded):(n.video.smallBytesSent=e.bytesSent,n.video.smallFramesEncoded=e.framesEncoded)}else e.mediaType===Gu&&(n.audio.bytesSent=e.bytesSent,n.audio.packetsSent=e.packetsSent);else"candidate-pair"===e.type?Vg(e)&&jl(e.currentRoundTripTime)&&(n.rtt=Math.floor(1e3*e.currentRoundTripTime)):"track"===e.type?(Ul(e.frameWidth)||(e.trackIdentifier===t.getLocalStreamVideoTrackId()?(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight,n.video.framesSent=e.framesSent):(n.video.smallFrameWidth=e.frameWidth,n.video.smallFrameHeight=e.frameHeight,n.video.smallFramesSent=e.framesSent)),Ul(e.audioLevel)||(n.audio.audioLevel=e.audioLevel||0)):"media-source"===e.type&&(e.kind===Gu?(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0):e.kind===Ju&&(e.trackIdentifier===t.getLocalStreamVideoTrackId()?n.video.fpsCapture=e.framesPerSecond:n.video.smallFPSCapture=e.framesPerSecond))})),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),this.log_.warn("failed to getStats on sender connection");case 14:return e.abrupt("return",n);case 15:case"end":return e.stop()}}),e,this,[[4,11]])}))),function(e){return o.apply(this,arguments)})},{key:"getReceiverStats",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n={tinyId:t.getTinyId(),userId:t.getUserId(),rtt:0,hasAudio:!1,hasVideo:!1,hasAuxiliary:!1,audio:{bytesReceived:0,packetsReceived:0,packetsLost:0,jitter:0,audioLevel:0,totalAudioEnergy:0},video:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesReceived:0,framesDecoded:0,frameWidth:0,frameHeight:0},auxiliary:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesReceived:0,framesDecoded:0,frameWidth:0,frameHeight:0}},!(r=t.getPeerConnection())){e.next=13;break}return e.prev=3,e.next=6,r.getStats();case 6:e.sent.forEach((function(e){if("inbound-rtp"===e.type){if(e.mediaType===Gu)n.audio.packetsReceived=e.packetsReceived,n.audio.bytesReceived=e.bytesReceived,n.audio.packetsLost=e.packetsLost,n.audio.jitter=e.jitter,n.hasAudio=!0;else if(e.mediaType===Ju){if(Bs&&0===e.bytesReceived)return;var r=t.getSSRC();e.ssrc===r.video&&(n.video.packetsReceived=e.packetsReceived,n.video.bytesReceived=e.bytesReceived,n.video.packetsLost=e.packetsLost,n.video.framesReceived=e.framesReceived,n.video.framesDecoded=e.framesDecoded,n.video.fpsDecoded=e.framesPerSecond,n.hasVideo=!0,!e.decoderImplementation||i.prevDecoderImplementationMap_.has(n.userId)&&i.prevDecoderImplementationMap_.get(n.userId)===e.decoderImplementation||(pf.info("[".concat(n.userId,"] decoderImplementation change to ").concat(e.decoderImplementation)),i.prevDecoderImplementationMap_.set(n.userId,e.decoderImplementation))),e.ssrc===r.auxiliary&&(n.auxiliary.packetsReceived=e.packetsReceived,n.auxiliary.bytesReceived=e.bytesReceived,n.auxiliary.packetsLost=e.packetsLost,n.auxiliary.framesReceived=e.framesReceived,n.auxiliary.framesDecoded=e.framesDecoded,n.auxiliary.fpsDecoded=e.framesPerSecond,n.hasAuxiliary=!0)}}else"track"===e.type?(Ul(e.frameWidth)||(e.trackIdentifier===t.getMainStreamVideoTrackId()&&(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight),e.trackIdentifier===t.getAuxStreamVideoTrackId()&&(n.auxiliary.frameWidth=e.frameWidth,n.auxiliary.frameHeight=e.frameHeight)),e.kind===Gu&&(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0)):"candidate-pair"===e.type&&Vg(e)&&jl(e.currentRoundTripTime)&&(n.rtt=Math.floor(1e3*e.currentRoundTripTime))})),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),this.log_.warn("failed to getStats on receiver connection");case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}}),e,this,[[3,10]])}))),function(e){return r.apply(this,arguments)})},{key:"getStats",value:(n=i(regeneratorRuntime.mark((function e(t,n){var r,i,a,o,s,c,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r={},!t){e.next=5;break}return e.next=4,this.getSenderStats(t);case 4:r=e.sent;case 5:i=[],a=b(n),e.prev=7,a.s();case 9:if((o=a.n()).done){e.next=17;break}return(s=g(o.value,2))[0],c=s[1],e.next=13,this.getReceiverStats(c);case 13:u=e.sent,i.push(u);case 15:e.next=9;break;case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(7),a.e(e.t0);case 22:return e.prev=22,a.f(),e.finish(22);case 25:return e.abrupt("return",{senderStats:r,receiverStats:i});case 26:case"end":return e.stop()}}),e,this,[[7,19,22,25]])}))),function(e,t){return n.apply(this,arguments)})},{key:"prepareReport",value:function(e){var t=e.stats,n=e.report,r=e.freezeMap;if(!r_(t.senderStats)&&(n.uint32_delay=t.senderStats.rtt,n.RTTReportState.uint32_delay=t.senderStats.rtt,n.AudioReportState.sentAudioLevel=t.senderStats.audio.audioLevel,n.AudioReportState.sentAudioEnergy=t.senderStats.audio.totalAudioEnergy,n.AudioReportState.uint32_audio_enc_pkg_br=t.senderStats.audio.bytesSent,n.VideoReportState.uint32_video_snd_br=t.senderStats.video.bytesSent,n.VideoReportState.uint32_send_total_pkg=t.senderStats.video.packetsSent,n.VideoReportState.VideoEncState[0].uint32_enc_width=t.senderStats.video.frameWidth,n.VideoReportState.VideoEncState[0].uint32_enc_height=t.senderStats.video.frameHeight,n.VideoReportState.VideoEncState[0].uint32_enc_fps=t.senderStats.video.framesEncoded,n.VideoReportState.VideoEncState[0].uint32_capture_fps=t.senderStats.video.fpsCapture,n.VideoReportState.VideoEncState[0].uint32_send_fps=t.senderStats.video.framesSent,t.senderStats.video.smallBytesSent)){n.VideoReportState.uint32_small_video_snd_br=t.senderStats.video.smallBytesSent;var i={uint32_enc_width:t.senderStats.video.smallFrameWidth||0,uint32_enc_height:t.senderStats.video.smallFrameHeight||0,uint32_enc_fps:t.senderStats.video.smallFramesEncoded||0,uint32_capture_fps:t.senderStats.video.smallFPSCapture||0,uint32_send_fps:t.senderStats.video.smallFramesSent||0};n.VideoReportState.VideoEncState.push(i)}t.receiverStats.forEach((function(e){var t=e.userId;if(n.RTTReportState.RTTDecState.push({uint32_delay:e.rtt,uint64_sender_uin:e.tinyId}),e.hasAudio&&(n.AudioReportState.AudioDecState.push({uint32_audio_delay:0,uint32_audio_jitter:e.audio.jitter,uint32_audio_real_recv_pkg:e.audio.packetsReceived,uint32_audio_flow:e.audio.bytesReceived,uint32_audio_real_recv_br:0,uint64_sender_uin:e.tinyId,userId:e.userId,packetsLost:e.audio.packetsLost,totalPacketsLost:e.audio.packetsLost,audioLevel:e.audio.audioLevel,audioEnergy:e.audio.totalAudioEnergy}),n.AudioReportState.uint32_audio_real_recv_pkg+=e.audio.packetsReceived,n.AudioReportState.uint32_audio_flow+=e.audio.bytesReceived,n.uint32_real_num+=e.audio.packetsReceived),e.hasVideo){var i=r.get(t+"_main"),a=i?i.duration:0;n.VideoReportState.VideoDecState.push({uint32_video_recv_fps:e.video.framesReceived,uint32_video_dec_fps:e.video.fpsDecoded,uint32_video_recv_br:e.video.bytesReceived,uint32_video_real_recv_pkg:e.video.packetsReceived,uint32_dec_height:e.video.frameHeight,uint32_dec_width:e.video.frameWidth,uint32_video_jitter:0,uint64_sender_uin:e.tinyId,userId:e.userId,packetsLost:e.video.packetsLost,totalPacketsLost:e.video.packetsLost,uint32_video_strtype:0,int32_video_freeze_ms:a}),n.VideoReportState.uint32_video_total_real_recv_pkg+=e.video.packetsReceived,n.VideoReportState.uint32_video_rcv_br+=e.video.bytesReceived}if(e.hasAuxiliary){var o=r.get(t+"_auxiliary"),s=o?o.duration:0;n.VideoReportState.VideoDecState.push({uint32_video_recv_fps:e.auxiliary.framesReceived,uint32_video_dec_fps:e.auxiliary.fpsDecoded,uint32_video_recv_br:e.auxiliary.bytesReceived,uint32_video_real_recv_pkg:e.auxiliary.packetsReceived,uint32_dec_height:e.auxiliary.frameHeight,uint32_dec_width:e.auxiliary.frameWidth,uint32_video_jitter:0,uint64_sender_uin:e.tinyId,userId:e.userId,packetsLost:e.auxiliary.packetsLost,totalPacketsLost:e.auxiliary.packetsLost,uint32_video_strtype:2,int32_video_freeze_ms:s})}})),n.uint64_end_utime=(new Date).getTime();var a=this.prevReport_;if(this.prevReport_=JSON.parse(JSON.stringify(n)),r_(a))n.AudioReportState.uint32_audio_enc_pkg_br=8*n.AudioReportState.uint32_audio_enc_pkg_br/2,n.VideoReportState.uint32_video_rcv_br=8*n.VideoReportState.uint32_video_rcv_br/2,n.VideoReportState.uint32_video_snd_br=8*n.VideoReportState.uint32_video_snd_br/2,n.VideoReportState.uint32_small_video_snd_br&&(n.VideoReportState.uint32_small_video_snd_br=8*n.VideoReportState.uint32_small_video_snd_br/2),n.VideoReportState.VideoDecState.forEach((function(e){e.uint32_video_recv_br=8*e.uint32_video_recv_br/2,n.uint32_total_send_bps=n.AudioReportState.uint32_audio_enc_pkg_br+n.VideoReportState.uint32_video_snd_br}));else{if(n.uint64_begine_utime=a.uint64_end_utime,n.uint32_real_num-=a.uint32_real_num,n.uint32_real_num<=0&&(n.uint32_real_num=0),n.AudioReportState.uint32_audio_real_recv_pkg-=a.AudioReportState.uint32_audio_real_recv_pkg,n.AudioReportState.uint32_audio_real_recv_pkg<=0&&(n.AudioReportState.uint32_audio_real_recv_pkg=0),n.AudioReportState.uint32_audio_enc_pkg_br-=a.AudioReportState.uint32_audio_enc_pkg_br,n.AudioReportState.uint32_audio_enc_pkg_br<=0&&(n.AudioReportState.uint32_audio_enc_pkg_br=0),n.AudioReportState.uint32_audio_enc_pkg_br=8*n.AudioReportState.uint32_audio_enc_pkg_br/2,n.VideoReportState.uint32_video_snd_br-=a.VideoReportState.uint32_video_snd_br,n.VideoReportState.uint32_video_snd_br<=0&&(n.VideoReportState.uint32_video_snd_br=0),n.VideoReportState.uint32_video_snd_br=8*n.VideoReportState.uint32_video_snd_br/2,n.VideoReportState.uint32_small_video_snd_br&&(n.VideoReportState.uint32_small_video_snd_br-=a.VideoReportState.uint32_small_video_snd_br,n.VideoReportState.uint32_small_video_snd_br<=0&&(n.VideoReportState.uint32_small_video_snd_br=0),n.VideoReportState.uint32_small_video_snd_br=8*n.VideoReportState.uint32_small_video_snd_br/2),n.AudioReportState.uint32_audio_flow-=a.AudioReportState.uint32_audio_flow,n.AudioReportState.uint32_audio_flow<=0&&(n.AudioReportState.uint32_audio_flow=0),n.VideoReportState.uint32_send_total_pkg-=a.VideoReportState.uint32_send_total_pkg,n.VideoReportState.uint32_send_total_pkg<=0&&(n.VideoReportState.uint32_send_total_pkg=0),n.VideoReportState.uint32_video_rcv_br-=a.VideoReportState.uint32_video_rcv_br,n.VideoReportState.uint32_video_rcv_br<=0&&(n.VideoReportState.uint32_video_rcv_br=0),n.VideoReportState.uint32_video_rcv_br=8*n.VideoReportState.uint32_video_rcv_br/2,n.VideoReportState.uint32_video_total_real_recv_pkg-=a.VideoReportState.uint32_video_total_real_recv_pkg,n.VideoReportState.uint32_video_total_real_recv_pkg<=0&&(n.VideoReportState.uint32_video_total_real_recv_pkg=0),n.VideoReportState.VideoEncState[0].uint32_enc_fps-=a.VideoReportState.VideoEncState[0].uint32_enc_fps,n.VideoReportState.VideoEncState[0].uint32_enc_fps<0&&(n.VideoReportState.VideoEncState[0].uint32_enc_fps=0),n.VideoReportState.VideoEncState[0].uint32_enc_fps=n.VideoReportState.VideoEncState[0].uint32_enc_fps/2,n.VideoReportState.VideoEncState[0].uint32_send_fps-=a.VideoReportState.VideoEncState[0].uint32_send_fps,n.VideoReportState.VideoEncState[0].uint32_send_fps<0&&(n.VideoReportState.VideoEncState[0].uint32_send_fps=0),n.VideoReportState.VideoEncState[0].uint32_send_fps=n.VideoReportState.VideoEncState[0].uint32_send_fps/2,n.VideoReportState.VideoEncState[1]){var o=0,s=0;a.VideoReportState.VideoEncState[1]&&(o=a.VideoReportState.VideoEncState[1].uint32_enc_fps,s=a.VideoReportState.VideoEncState[1].uint32_enc_fps),n.VideoReportState.VideoEncState[1].uint32_enc_fps-=o,n.VideoReportState.VideoEncState[1].uint32_enc_fps<0&&(n.VideoReportState.VideoEncState[1].uint32_enc_fps=0),n.VideoReportState.VideoEncState[1].uint32_enc_fps=n.VideoReportState.VideoEncState[1].uint32_enc_fps/2,n.VideoReportState.VideoEncState[1].uint32_send_fps-=s,n.VideoReportState.VideoEncState[1].uint32_send_fps<0&&(n.VideoReportState.VideoEncState[1].uint32_send_fps=0),n.VideoReportState.VideoEncState[1].uint32_send_fps=n.VideoReportState.VideoEncState[1].uint32_send_fps/2}for(var c=n.VideoReportState.VideoDecState.length,u=0;u<c;u++){for(var d=n.VideoReportState.VideoDecState[u],l=d.uint64_sender_uin,h=d.uint32_video_strtype,p=d.uint32_video_real_recv_pkg,f=d.uint32_video_recv_br,m=d.uint32_video_recv_fps,_=0;_<a.VideoReportState.VideoDecState.length;_++){var v=a.VideoReportState.VideoDecState[_];if(v.uint64_sender_uin===l&&v.uint32_video_strtype===h){d.packetsLost=d.totalPacketsLost-v.totalPacketsLost,(p-=v.uint32_video_real_recv_pkg)<=0&&(p=0),(f-=v.uint32_video_recv_br)<=0&&(f=0),(m-=v.uint32_video_recv_fps)<0&&(m=0);break}}n.VideoReportState.VideoDecState[u].uint32_video_real_recv_pkg=p,n.VideoReportState.VideoDecState[u].uint32_video_recv_br=8*f/2,n.VideoReportState.VideoDecState[u].uint32_video_recv_fps=m/2}c=n.AudioReportState.AudioDecState.length;for(var g=0;g<c;g++){for(var y=n.AudioReportState.AudioDecState[g],S=y.uint32_audio_real_recv_pkg,k=y.uint32_audio_flow,b=y.uint64_sender_uin,I=0;I<a.AudioReportState.AudioDecState.length;I++){var R=a.AudioReportState.AudioDecState[I];if(R.uint64_sender_uin===b){y.packetsLost=y.totalPacketsLost-R.totalPacketsLost,(S-=R.uint32_audio_real_recv_pkg)<=0&&(S=0),(k-=R.uint32_audio_flow)<=0&&(k=0);break}}n.AudioReportState.AudioDecState[g].uint32_audio_real_recv_pkg=S,n.AudioReportState.AudioDecState[g].uint32_audio_flow=k,n.AudioReportState.AudioDecState[g].uint32_audio_real_recv_br=8*k/2}n.AudioReportState.uint32_audio_real_recv_br=8*n.AudioReportState.uint32_audio_flow/2,n.uint32_real_num=n.AudioReportState.uint32_audio_real_recv_pkg+n.VideoReportState.uint32_video_total_real_recv_pkg,n.uint32_total_send_bps=n.AudioReportState.uint32_audio_enc_pkg_br+n.VideoReportState.uint32_video_snd_br,n.uint32_total_recv_bps=n.AudioReportState.uint32_audio_real_recv_br+n.VideoReportState.uint32_video_rcv_br}return n}},{key:"getStatsReport",value:(t=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.uplinkConnection,r=t.downlinkConnections,i=t.freezeMap,a={uint64_begine_utime:(new Date).getTime(),uint64_end_utime:0,uint32_real_num:0,uint32_delay:0,uint32_CPU_curfreq:0,uint32_total_send_bps:0,uint32_total_recv_bps:0,AudioReportState:{uint32_audio_enc_pkg_br:0,uint32_audio_real_recv_pkg:0,uint32_audio_flow:0,uint32_audio_real_recv_br:0,uint32_audio_delay:0,uint32_audio_jitter:0,uint32_microphone_status:1,sentAudioLevel:0,sentAudioEnergy:0,AudioDecState:[]},VideoReportState:{uint32_video_delay:0,uint32_video_snd_br:0,uint32_video_total_real_recv_pkg:0,uint32_video_rcv_br:0,uint32_send_total_pkg:0,VideoEncState:[{uint32_enc_width:0,uint32_enc_height:0,uint32_capture_fps:0,uint32_enc_fps:0,uint32_send_fps:0}],VideoDecState:[]},RTTReportState:{uint32_delay:0,RTTDecState:[]}},e.next=4,this.getStats(n,r);case 4:return o=e.sent,this.prepareReport({stats:o,report:a,freezeMap:i}),e.abrupt("return",a);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})}]),e}(),vy=function(){function e(t){var n=t.signalChannel,r=t.connections,i=t.client;a(this,e),this.client_=i,this.signalChannel_=n,this.connections_=r,this.client_=i,this.log_=pf.createLogger({id:"q|"+this.client_.getUserId(),userId:this.client_.getUserId(),sdkAppId:this.client_.getSDKAppId()}),this.uplinkConnection_=null,this.uplinkNetworkQuality_=0,this.uplinkRTT_=0,this.uplinkLoss_=0,this.downlinkNetworkQuality_=0,this.downlinkRTT_=0,this.downlinkLoss_=0,this.downlinkPrevStatMap_=new Map,this.downlinkLossAndRTTMap_=new Map,this.interval_=-1,this.emitter_=new rh,this.initialize()}var t,n;return s(e,[{key:"uplinkNetworkQuality",get:function(){return this.uplinkNetworkQuality_},set:function(e){e!==this.uplinkNetworkQuality_&&this.log_.info("uplink network quality change ".concat(this.uplinkNetworkQuality," -> ").concat(e,", rtt: ").concat(this.uplinkRTT_,", loss: ").concat(this.uplinkLoss_)),this.uplinkNetworkQuality_=e}},{key:"downlinkNetworkQuality",get:function(){return this.downlinkNetworkQuality_},set:function(e){if(e!==this.downlinkNetworkQuality_){var t=this.getAverageLossAndRTT(y(this.downlinkLossAndRTTMap_.values())),n=t.rtt,r=t.loss;this.log_.info("downlink network quality change ".concat(this.downlinkNetworkQuality," -> ").concat(e,", rtt: ").concat(n,", loss: ").concat(r))}this.downlinkNetworkQuality_=e}},{key:"initialize",value:function(){var e=this;this.signalChannel_.on($f.UPLINK_NETWORK_STATS,(function(t){e.handleUplinkNetworkQuality(t)})),this.signalChannel_.on(Hf,this.handleSignalConnectionStateChange.bind(this)),this.start()}},{key:"handleUplinkNetworkQuality",value:function(e){if(!this.uplinkConnection_)return this.uplinkNetworkQuality=0,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var t=this.uplinkConnection_.getPeerConnection();if(t&&this.isPeerConnectionDisconnected(t))return this.uplinkNetworkQuality=6,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var n=e.data.content;if(0===n.result){var r=n.expectAudPkg+n.expectVidPkg,i=n.recvAudPkg+n.recvVidPkg,a=r-i;if(0===r&&0===i)return;this.uplinkLoss_=a<=0?0:Math.round(a/r*100),this.uplinkRTT_=n.rtt,this.uplinkNetworkQuality=this.getNetworkQuality(this.uplinkLoss_,this.uplinkRTT_)}}},{key:"handleDownlinkNetworkQuality",value:(n=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d,l,h,p,f,m,_=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.connections_&&0!==this.connections_.size){e.next=3;break}return this.downlinkNetworkQuality=0,e.abrupt("return");case 3:if(t=y(this.connections_.values()),n=t.filter((function(e){return e.getPeerConnection()&&"connected"===e.getPeerConnection().connectionState})),t.filter((function(e){return e.getPeerConnection()&&_.isPeerConnectionDisconnected(e.getPeerConnection())})).length!==t.length){e.next=9;break}return this.downlinkNetworkQuality=6,e.abrupt("return");case 9:r=0;case 10:if(!(r<n.length)){e.next=31;break}return i=n[r].getPeerConnection(),e.next=14,this.getStat(i);case 14:if(a=e.sent,o=a.rtt,s=a.totalPacketsLost,c=a.totalPacketsReceived,this.downlinkPrevStatMap_.has(i)){e.next=21;break}return this.downlinkPrevStatMap_.set(i,{totalPacketsLost:s,totalPacketsReceived:c}),e.abrupt("continue",28);case 21:u=0,d=this.downlinkPrevStatMap_.get(i),l=s-d.totalPacketsLost,h=c-d.totalPacketsReceived,u=l<=0||h<0?0:Math.round(l/(l+h)*100),this.downlinkPrevStatMap_.set(i,{totalPacketsLost:s,totalPacketsReceived:c}),this.downlinkLossAndRTTMap_.set(i,{rtt:o,loss:u,userId:n[r].getUserId()});case 28:r++,e.next=10;break;case 31:if(y(this.downlinkPrevStatMap_.keys()).forEach((function(e){_.isPeerConnectionDisconnected(e)&&(_.downlinkPrevStatMap_.delete(e),_.downlinkLossAndRTTMap_.delete(e))})),0!==this.downlinkLossAndRTTMap_.size){e.next=34;break}return e.abrupt("return");case 34:p=this.getAverageLossAndRTT(y(this.downlinkLossAndRTTMap_.values())),f=p.rtt,m=p.loss,this.downlinkRTT_=f,this.downlinkLoss_=m,this.downlinkNetworkQuality=this.getNetworkQuality(m,f);case 38:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"getStat",value:(t=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n={rtt:0,totalPacketsLost:0,totalPacketsReceived:0},t&&Pg()){e.next=3;break}return e.abrupt("return",n);case 3:r=t.getReceivers(),e.prev=4,i=0;case 6:if(!(i<r.length)){e.next=15;break}return a=r[i],e.next=10,a.getStats();case 10:e.sent.forEach((function(e){"candidate-pair"===e.type&&jl(e.currentRoundTripTime)&&(n.rtt=Math.round(1e3*e.currentRoundTripTime)),"inbound-rtp"!==e.type||e.mediaType!==Gu&&e.mediaType!==Ju||(n.totalPacketsLost+=e.packetsLost,n.totalPacketsReceived+=e.packetsReceived)}));case 12:i++,e.next=6;break;case 15:return e.abrupt("return",n);case 18:return e.prev=18,e.t0=e.catch(4),e.abrupt("return",n);case 21:case"end":return e.stop()}}),e,null,[[4,18]])}))),function(e){return t.apply(this,arguments)})},{key:"getAverageLossAndRTT",value:function(e){var t={rtt:0,loss:0};return Array.isArray(e)&&e.length>0&&(e.forEach((function(e){t.rtt+=e.rtt,t.loss+=e.loss})),Object.keys(t).forEach((function(n){t[n]=Math.round(t[n]/e.length)}))),t}},{key:"getNetworkQuality",value:function(e,t){return e>50||t>500?5:e>30||t>350?4:e>20||t>200?3:e>10||t>100?2:e>=0||t>=0?1:0}},{key:"handleSignalConnectionStateChange",value:function(e){e.state===zf?(this.uplinkRTT_=0,this.uplinkLoss_=0,this.uplinkNetworkQuality=6):e.state===Kf&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"handleUplinkConnectionStateChange",value:function(e){var t=e.state;t===ru?(this.uplinkLoss_=0,this.uplinkRTT_=0,this.uplinkNetworkQuality=6):t===ou&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"isPeerConnectionDisconnected",value:function(e){return!(!e||"disconnected"!==e.connectionState&&"failed"!==e.connectionState&&"closed"!==e.connectionState)}},{key:"setUplinkConnection",value:function(e){this.uplinkConnection_=e,this.uplinkConnection_?this.uplinkConnection_.on(Qg,this.handleUplinkConnectionStateChange.bind(this)):(this.uplinkNetworkQuality=0,this.uplinkRTT_=0,this.uplinkLoss_=0)}},{key:"start",value:function(){var e=this;-1===this.interval_?(this.log_.info("start network quality calculating"),this.interval_=vg.setInterval((function(){e.handleDownlinkNetworkQuality(),ih.emit(zh,{client:e.client_,uplinkNetworkQuality:e.uplinkNetworkQuality,downlinkNetworkQuality:e.downlinkNetworkQuality,uplinkRTT:e.uplinkRTT_,uplinkLoss:e.uplinkLoss_,downlinkRTT:e.downlinkRTT_,downlinkLoss:e.downlinkLoss_,downlinkLossAndRTTMap:e.downlinkLossAndRTTMap_}),e.emitter_.emit(cy,{uplinkNetworkQuality:e.uplinkNetworkQuality,downlinkNetworkQuality:e.downlinkNetworkQuality,uplinkRTT:e.uplinkRTT_,uplinkLoss:e.uplinkLoss_,downlinkRTT:e.downlinkRTT_,downlinkLoss:e.downlinkLoss_})}),2e3)):this.log_.info("network quality calculating is already started")}},{key:"stop",value:function(){this.log_.info("stop network quality calculating"),-1!==this.interval_&&(vg.clearInterval(this.interval_),this.interval_=-1)}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),gy=function(){function e(t){a(this,e),this.log_=pf.createLogger({id:t.client.getUserId(),userId:t.client.getUserId(),sdkAppId:t.client.getSDKAppId()}),this.localStream_=null,this.prevDevices_=[],this.initialize()}var t,n,r;return s(e,[{key:"initialize",value:(r=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:navigator.mediaDevices&&navigator.mediaDevices.addEventListener("devicechange",this.onDeviceChange.bind(this));case 1:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"onDeviceChange",value:(n=i(regeneratorRuntime.mark((function e(){var t,n,r,i=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.localStream_&&this.localStream_.getMediaStream()&&!this.localStream_.getScreen()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,fk.getDevices();case 4:t=e.sent,n=t.filter((function(e){return i.prevDevices_.findIndex((function(t){var n=t.deviceId;return e.deviceId===n}))<0})),r=this.prevDevices_.filter((function(e){return t.findIndex((function(t){var n=t.deviceId;return e.deviceId===n}))<0})),n.length>0&&this.handleDeviceAdded(this.prevDevices_,n),r.length>0&&this.handleDeviceRemoved(t,r),this.prevDevices_=t;case 10:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"setLocalStream",value:(t=i(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=4;break}return e.next=3,fk.getDevices();case 3:this.prevDevices_=e.sent;case 4:this.localStream_=t;case 5:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"handleDeviceAdded",value:function(e,t){this.log_.warn("devicesAdded: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=t.filter((function(e){return e.kind===nd})),r=t.filter((function(e){return e.kind===td})),i=e.filter((function(e){return e.kind===nd})),a=e.filter((function(e){return e.kind===td})),o=n.length>0&&0===i.length&&this.localStream_.getVideo(),s=r.length>0&&0===a.length&&this.localStream_.getAudio();if(s&&o)return this.log_.info("new microphone and camera detected, but there was no device before."),void this.localStream_.updateStream({audio:!0,video:!0,cameraId:n[0].deviceId,microphoneId:r[0].deviceId});o&&(this.log_.info("new camera detected, but there was no camera before."),this.localStream_.updateStream({audio:!1,video:!0,cameraId:n[0].deviceId})),s&&(this.log_.info("new microphone detected, but there was no microphone before."),this.localStream_.updateStream({audio:!0,video:!1,microphoneId:r[0].deviceId}))}},{key:"handleDeviceRemoved",value:function(e,t){this.log_.warn("devicesRemoved: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=!1,r=!1,i=this.localStream_.getCameraId(),a=this.localStream_.getMicrophoneId();if("default"===a){var o=this.localStream_.getMicrophoneGroupId(),s=e.filter((function(e){return"default"===e.deviceId&&e.kind===td}))[0];s&&s.groupId!==o&&(r=!0)}if(t.forEach((function(e){var t=e.deviceId;i.length>0&&t===i?n=!0:a.length>0&&t===a&&(r=!0)})),n&&r)return this.log_.warn("current camera and microphone in use is lost, cameraId: ".concat(i,", microphoneId: ").concat(a)),void((this.localStream_.getAudio()||this.localStream_.getVideo())&&this.localStream_.updateStream({video:!0,audio:!0}));n&&(this.log_.warn("current camera in use is lost, deviceId: ".concat(i)),this.localStream_.getVideo()&&this.localStream_.updateStream({video:!0,audio:!1})),r&&(this.log_.warn("current microphone in use is lost, deviceId: ".concat(a)),this.localStream_.getAudio()&&this.localStream_.updateStream({video:!1,audio:!0}))}}]),e}(),yy=function(e){var t=Ai(j(this)),n="",r=et(e);if(r<0||Infinity==r)throw RangeError("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},Sy=1..toFixed,ky=Math.floor,by=function(e,t,n){return 0===t?n:t%2==1?by(e,t-1,n*e):by(e*e,t/2,n)},Iy=function(e,t,n){for(var r=-1,i=n;++r<6;)i+=t*e[r],e[r]=i%1e7,i=ky(i/1e7)},Ry=function(e,t){for(var n=6,r=0;--n>=0;)r+=e[n],e[n]=ky(r/t),r=r%t*1e7},Ty=function(e){for(var t=6,n="";--t>=0;)if(""!==n||0===t||0!==e[t]){var r=String(e[t]);n=""===n?r:n+yy.call("0",7-r.length)+r}return n},Ey=Sy&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!P((function(){Sy.call({})}));Tt({target:"Number",proto:!0,forced:Ey},{toFixed:function(e){var t,n,r,i,a=os(this),o=et(e),s=[0,0,0,0,0,0],c="",u="0";if(o<0||o>20)throw RangeError("Incorrect fraction digits");if(a!=a)return"NaN";if(a<=-1e21||a>=1e21)return String(a);if(a<0&&(c="-",a=-a),a>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(a*by(2,69,1))-69)<0?a*by(2,-t,1):a/by(2,t,1),n*=4503599627370496,(t=52-t)>0){for(Iy(s,0,n),r=o;r>=7;)Iy(s,1e7,0),r-=7;for(Iy(s,by(10,r,1),0),r=t-1;r>=23;)Ry(s,1<<23),r-=23;Ry(s,1<<r),Iy(s,1,1),Ry(s,2),u=Ty(s)}else Iy(s,0,n),Iy(s,1<<-t,0),u=Ty(s)+yy.call("0",o);return u=o>0?c+((i=u.length)<=o?"0."+yy.call("0",o-i)+u:u.slice(0,i-o)+"."+u.slice(i-o)):c+u}});var wy,Cy=window.AudioContext||window.webkitAudioContext,Ay=null,Py=function(){function e(){var t=this;a(this,e),Ay||(Ay=new Cy),this.context_=Ay,this.instant_=0,this.slow_=0,this.clip_=0,this.script_=this.context_.createScriptProcessor(2048,1,1),this.script_.onaudioprocess=function(e){var n,r=e.inputBuffer.getChannelData(0),i=0,a=0;for(n=0;n<r.length;++n)i+=r[n]*r[n],Math.abs(r[n])>.99&&(a+=1);t.instant_=Math.sqrt(i/r.length),t.slow_=.95*t.slow_+.05*t.instant_,t.clip_=a/r.length}}return s(e,[{key:"connectToSource",value:function(e,t){try{var n=new MediaStream;n.addTrack(e),this.mic_=this.context_.createMediaStreamSource(n),this.mic_.connect(this.script_),this.script_.connect(this.context_.destination),Ul(t)||t(null)}catch(mk){pf.error("soundMeter connectToSource error: "+mk),Ul(t)||t(mk)}}},{key:"stop",value:function(){this.mic_.disconnect(),this.script_.disconnect()}},{key:"resume",value:function(){this.context_&&this.context_.resume()}},{key:"getVolume",value:function(){return this.instant_.toFixed(2)}}]),e}(),xy=function(){function e(t){a(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.outputDeviceId_=t.outputDeviceId,this.volume_=t.volume,this.emitter_=new rh,this.initializeElement(),this.state_="NONE",this.soundMeter_=null}var t,n,r,o;return s(e,[{key:"isPlaying",get:function(){return"PLAYING"===this.state_}},{key:"initializeElement",value:function(){var e=new MediaStream;e.addTrack(this.track_);var t=document.createElement(Gu);t.srcObject=e,t.muted=this.muted_,t.setAttribute("id","audio_".concat(this.stream_.getId())),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.div_.appendChild(t),this.element_=t,this.handleEvents()}},{key:"play",value:(o=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.outputDeviceId_){e.next=3;break}return e.next=3,this.element_.setSinkId(this.outputDeviceId_);case 3:return this.setVolume(this.volume_),e.prev=4,e.next=7,this.element_.play();case 7:e.next=15;break;case 9:if(e.prev=9,e.t0=e.catch(4),this.log_.warn("<audio> play() error: "+e.t0),!(t=e.t0.toString()+" <audio>").startsWith("NotAllowedError")){e.next=15;break}throw new Al({code:Cl.PLAY_NOT_ALLOWED,message:t});case 15:case"end":return e.stop()}}),e,this,[[4,9]])}))),function(){return o.apply(this,arguments)})},{key:"handleEvents",value:function(){this.handleElementEvent=this.handleElementEvent.bind(this),this.handleTrackEvent=this.handleTrackEvent.bind(this),this.element_.addEventListener($u,this.handleElementEvent),this.element_.addEventListener(Xu,this.handleElementEvent),this.element_.addEventListener(Yu,this.handleElementEvent),this.element_.addEventListener(Zu,this.handleElementEvent),this.track_.addEventListener(Xu,this.handleTrackEvent),this.track_.addEventListener(Ku,this.handleTrackEvent),this.track_.addEventListener(Qu,this.handleTrackEvent),this.track_.readyState===Xu&&this.handleTrackEvent({type:Xu}),this.track_.muted&&this.handleTrackEvent({type:Ku})}},{key:"handleElementEvent",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o,s=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.type,e.t0=n,e.next=e.t0===$u?4:e.t0===Xu?9:e.t0===Yu?12:e.t0===Zu?16:27;break;case 4:return this.log_.info("stream - audio player is starting playing"),this.state_="PLAYING",ih.emit(Ih,{stream:this.stream_}),this.emitter_.emit(my,{state:this.state_,reason:$u}),e.abrupt("break",27);case 9:return this.log_.info("stream - audio player is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(my,{state:this.state_,reason:Xu})),e.abrupt("break",27);case 12:return this.log_.info("stream - audio player is paused"),this.state_="PAUSED",this.emitter_.emit(my,{state:this.state_,reason:Yu}),e.abrupt("break",27);case 16:if(!this.element_||!this.element_.error){e.next=26;break}return r="".concat(Fg(),"/").concat(Fc().name,"/").concat(Fc().version),e.next=20,fk.getSpeakers();case 20:i=e.sent,a=i[0].label,(o=i.find((function(e){return e.deviceId===s.outputDeviceId_})))&&(a=o.label),this.log_.error("stream - audio player error observed. code: ".concat(this.element_.error.code," message: ").concat(this.element_.error.message," deviceInfo: ").concat(r," speaker: ").concat(a)),Pl.uploadEvent("stat-".concat(this.stream_.getType(),"-audio-").concat(xu,"-").concat(this.element_.error.code,"-").concat(r,"-").concat(a),this.element_.error);case 26:return e.abrupt("break",27);case 27:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"handleTrackEvent",value:function(e){var t=e.type;switch(t){case Xu:this.log_.info("stream - audio player track is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(my,{state:this.state_,reason:Xu})),ih.emit(Hh,{stream:this.stream_,type:t});break;case Ku:this.log_.info("stream - audio track is unable to provide media output"),this.stream_.isRemote()||nh(),"PAUSED"!==this.state_&&(this.state_="PAUSED",this.emitter_.emit(my,{state:this.state_,reason:Ku})),ih.emit(Bh,{stream:this.stream_,type:t});break;case Qu:this.log_.info("stream - audio track is able to provide media output"),"PAUSED"===this.state_&&(this.state_="PLAYING",this.emitter_.emit(my,{state:this.state_,reason:Qu}))}}},{key:"unbindEvents",value:function(){this.element_&&(this.element_.removeEventListener($u,this.handleElementEvent),this.element_.removeEventListener(Xu,this.handleElementEvent),this.element_.removeEventListener(Yu,this.handleElementEvent),this.element_.removeEventListener(Zu,this.handleElementEvent)),this.track_&&(this.track_.removeEventListener(Xu,this.handleTrackEvent),this.track_.removeEventListener(Ku,this.handleTrackEvent),this.track_.removeEventListener(Qu,this.handleTrackEvent))}},{key:"setSinkId",value:(n=i(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.outputDeviceId_===t){e.next=4;break}return e.next=3,this.element_.setSinkId(t);case 3:this.outputDeviceId_=t;case 4:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"setVolume",value:function(e){this.log_.info("stream - audioElement setVolume to : ".concat(e)),this.element_.volume=e}},{key:"getAudioLevel",value:function(){return this.soundMeter_||(this.soundMeter_=new Py,this.soundMeter_.connectToSource(this.track_)),this.soundMeter_.getVolume()}},{key:"stop",value:function(){this.unbindEvents(),this.div_.removeChild(this.element_),this.element_.srcObject=null,this.soundMeter_&&(this.soundMeter_.stop(),this.soundMeter_=null),this.element_=null}},{key:"resume",value:(t=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.soundMeter_&&this.soundMeter_.resume(),e.next=4,this.element_.play();case 4:e.next=12;break;case 6:if(e.prev=6,e.t0=e.catch(0),this.log_.warn("<audio> play() error: "+e.t0),!(t=Zv({key:b_,data:{error:e.t0}})).startsWith("NotAllowedError")){e.next=12;break}throw new Al({code:Cl.PLAY_NOT_ALLOWED,message:t});case 12:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return t.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),Dy=Xe.PROPER,Ny=hs.trim;Tt({target:"String",proto:!0,forced:(wy="trim",P((function(){return!!ss[wy]()||"​᠎"!=="​᠎"[wy]()||Dy&&ss[wy].name!==wy})))},{trim:function(){return Ny(this)}});var Ly="".concat("trtc_autoplay","_mask"),Oy="".concat("trtc_autoplay","_wrapper"),My="".concat("trtc_autoplay","_header"),Vy="".concat("trtc_autoplay","_content"),Uy="".concat("trtc_autoplay","_action_wrapper"),Fy="".concat("trtc_autoplay","_question"),jy="".concat("trtc_autoplay","_collapse"),By="".concat("trtc_autoplay","_action_confirm"),Hy="".concat("trtc_autoplay","_detail"),Gy=!1,Jy=function(){return!!document.querySelector(".".concat(Oy))},zy="".concat(md,"/").concat(th()?"zh-cn":"en","/tutorial-21-advanced-auto-play-policy.html"),Wy="<br><a href='".concat(zy,"' target='_blank'>").concat(th()?"其他方案？":"Any other solution?","</a>"),qy="".concat(th()?"浏览器自动播放策略：在用户与页面产生交互（点击、触摸）之前，浏览器禁止播放有声媒体。该弹窗用于帮助用户恢复音视频播放。".concat(Wy):"Autoplay Policy: Before user interacts with the web page (clicking, touching), page will not be allowed to play media with sound. This Dialog is used to help users resume playback. ".concat(Wy)),Ky=function(){function e(){if(a(this,e),this.dialogNode_=null,this.bodyPosition_="",this.content="音视频播放被浏览器拦截，请点击“恢复播放”。",th()||(this.content='Media playback failed. Click the "Resume" to resume playback.'),!Gy){var t=document.createElement("style");t.innerHTML=".".concat(Ly,"{position:fixed;top:0;left:0;right:0;bottom:0;width:100vw;height:100vh;display:flex;justify-content:center;align-items:center;background:rgba(0,0,0,0.5);z-index:1500;}.").concat(Ly," div:not(.").concat(Uy,"){display:block !important;}.").concat(Oy,"{padding:14px;background:#fff;border-radius:3px;box-shadow:0px 3px 15px #434343;border:1px solid #d1cfcf;max-width:500px;}.").concat(Oy," a{color:").concat("#2473E8",";}.").concat(My,"{overflow:hidden;text-overflow:ellipsis;font-size:16px;font-weight:600;}.").concat(Vy,"{margin:8px 0;}.").concat(Uy,"{width:100%;display:flex !important;align-items:center;justify-content:right;float:right;}.").concat(jy,"{margin-right:auto;cursor:pointer}.").concat(Fy,"{height:100%;line-height:16px;cursor:pointer;}.").concat(By,"{margin-left:8px;color:#fff;background:").concat("#2473E8",";padding:4px 12px;outline:none;border:1px solid;border-radius:3px;font-weight:bold;}.").concat(By,":hover{opacity:0.9;}.").concat(jy,",.").concat(By,",.").concat(Vy,",.").concat(Fy,"{font-size:14px;}@media screen and (max-width:750px){.").concat(Oy,"{width:80vw;}}"),document.head.appendChild(t),Gy=!0}this.showDetail_=!1,this.isCollapseClicked_=!1,this.isQuestionClicked_=!1,this.addDiaLog()}return s(e,[{key:"createDiaLog",value:function(){var e=document.createElement("template");e.innerHTML='<div class="'.concat(Ly,"\"><div class='").concat(Oy,"'><div class='").concat(My,"'>").concat(location.host,"</div><div class='").concat(Vy,"'>").concat(this.content,"</div><div class='").concat(Hy,'\' style="visibility:hidden;width:100%;height:0;font-size:12px;color:gray;">').concat(qy,"</div><div class='").concat(Uy,"'></div></div></div>").trim();var t=document.createElement("button");t.className=By,t.innerText=th()?"恢复播放":"Resume",t.onclick=this.onConfirm.bind(this);var n=document.createElement("div");n.className=Fy,n.innerHTML='<?xml version="1.0" encoding="UTF-8"?>\n    <svg class="icon" width="18" height="18" p-id="2030" t="1639646523624" version="1.1" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">\n    <path d="m464 784.35c0 26.51 21.49 48 48 48s48-21.49 48-48-21.49-48-48-48-48 21.49-48 48z" p-id="2031"/>\n    <path d="m512 960c-247.04 0-448-200.96-448-448s200.96-448 448-448 448 200.96 448 448-200.96 448-448 448zm0-831.71c-211.58 0-383.71 172.13-383.71 383.71 0 211.55 172.13 383.71 383.71 383.71 211.55 0 383.71-172.16 383.71-383.71 0-211.58-172.16-383.71-383.71-383.71z" p-id="2032"/>\n    <path d="m512 673.7c-17.665 0-32.001-14.336-32.001-31.999v-54.112c0-52.353 40-92.352 75.328-127.65 25.887-25.92 52.672-52.672 52.672-74.017 0-53.343-43.072-96.735-95.999-96.735-53.823 0-95.999 41.536-95.999 94.559 0 17.665-14.336 31.999-32.001 31.999s-32.001-14.336-32.001-31.999c0-87.424 71.775-158.56 160-158.56s160 72.095 160 160.74c0 47.904-36.32 84.192-71.424 119.3-27.84 27.776-56.576 56.512-56.576 82.336v54.112c0 17.665-14.336 32.032-32.001 32.032z" p-id="2033"/>\n    </svg>\n    ',n.onclick=this.onQuestionClick.bind(this);var r=document.createElement("div");r.className=jy,r.innerText="".concat(th()?"详情 >":"Detail >"),r.onclick=this.onCollapseClick.bind(this);var i=e.content.firstChild,a=i.querySelector(".".concat(Uy));return a.appendChild(r),a.appendChild(n),a.appendChild(t),i}},{key:"addDiaLog",value:function(){Jy()||(this.dialogNode_=this.createDiaLog(),document.body.appendChild(this.dialogNode_),this.dialogNode_.onclick=this.onConfirm.bind(this),this.dialogNode_.querySelector(".".concat(Oy)).onclick=function(e){return e.stopPropagation()},this.bodyPosition_=document.body.style.position,document.body.style.position="fixed",pf.warn("show autoplay dialog"))}},{key:"deleteDiaLog",value:function(){this.dialogNode_&&(document.body.removeChild(this.dialogNode_),document.body.style.position=this.bodyPosition_,this.dialogNode_=null)}},{key:"onConfirm",value:function(){pf.warn("confirm clicked, try resume stream"),ih.emit(Wh),this.deleteDiaLog()}},{key:"onCollapseClick",value:function(){var e=this.dialogNode_.querySelector(".".concat(Hy));e.style.visibility="".concat(this.showDetail_?"hidden":"visible"),e.style.height="".concat(this.showDetail_?0:"fit-content"),this.showDetail_=!this.showDetail_,this.isCollapseClicked_||Pl.uploadEvent({log:"dialog-1 "}),this.isCollapseClicked_=!0}},{key:"onQuestionClick",value:function(){window.open(zy,"_blank"),this.isQuestionClicked_||Pl.uploadEvent({log:"dialog-2 "}),this.isQuestionClicked_=!0}}]),e}(),Qy=function(){function e(t){a(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.objectFit_=t.objectFit,this.mirror_=t.mirror,this.emitter_=new rh,this.initializeElement(),this.state_="NONE",this.pausedRetryCount_=5}var t,n;return s(e,[{key:"isPlaying",get:function(){return"PLAYING"===this.state_}},{key:"initializeElement",value:function(){var e=new MediaStream;e.addTrack(this.track_);var t=document.createElement(Ju);t.srcObject=e,t.muted=!0;var n="width: 100%; height: 100%; object-fit: ".concat(this.objectFit_,";");this.mirror_&&(n+="transform: rotateY(180deg);"),t.setAttribute("id","video_".concat(this.stream_.getId())),t.setAttribute("style",n),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.div_&&this.div_.appendChild(t),this.element_=t,this.handleEvents()}},{key:"setRect",value:function(e){var t=e.width,n=e.height;this.element_&&(this.element_.style.width=t+"px",this.element_.style.height=n+"px")}},{key:"play",value:(n=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<video> play() error: "+e.t0),!(t=e.t0.toString()+" <video>").startsWith("NotAllowedError")){e.next=11;break}throw new Al({code:Cl.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}}),e,this,[[0,5]])}))),function(){return n.apply(this,arguments)})},{key:"handleEvents",value:function(){this.handleElementEvent=this.handleElementEvent.bind(this),this.handleTrackEvent=this.handleTrackEvent.bind(this),this.element_.addEventListener($u,this.handleElementEvent),this.element_.addEventListener(Xu,this.handleElementEvent),this.element_.addEventListener(Yu,this.handleElementEvent),this.element_.addEventListener(Zu,this.handleElementEvent),this.element_.addEventListener(ed,this.handleElementEvent),this.track_.addEventListener(Xu,this.handleTrackEvent),this.track_.addEventListener(Ku,this.handleTrackEvent),this.track_.addEventListener(Qu,this.handleTrackEvent),this.track_.readyState===Xu&&this.handleTrackEvent({type:Xu}),this.track_.muted&&this.handleTrackEvent({type:Ku})}},{key:"handleElementEvent",value:function(e){switch(e.type){case $u:this.log_.info("stream - video player is starting playing"),this.state_="PLAYING",ih.emit(bh,{stream:this.stream_}),this.emitter_.emit(my,{state:this.state_,reason:$u});break;case Xu:this.log_.info("stream - video player is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(my,{state:this.state_,reason:Xu}));break;case Yu:this.log_.info("stream - video player is paused"),this.state_="PAUSED",this.emitter_.emit(my,{state:this.state_,reason:Yu}),this.pausedRetryCount_>0&&!Jy()&&(this.log_.info("auto resume when video paused"),this.resume(),this.pausedRetryCount_--);break;case Zu:if(this.element_&&this.element_.error){var t="".concat(Fg(),"/").concat(Fc().name,"/").concat(Fc().version);this.log_.error("stream - video player error observed. code: ".concat(this.element_.error.code," message: ").concat(this.element_.error.message," deviceInfo: ").concat(t)),Pl.uploadEvent("stat-".concat(this.stream_.getType(),"-video-").concat(xu,"-").concat(this.element_.error.code,"-").concat(t),this.element_.error)}break;case ed:ih.emit(Uh,{stream:this.stream_})}}},{key:"handleTrackEvent",value:function(e){var t=e.type;switch(t){case Xu:this.log_.info("stream - video player track is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(my,{state:this.state_,reason:Xu})),ih.emit(jh,{stream:this.stream_,type:t});break;case Ku:this.log_.info("stream - video track is unable to provide media output"),this.stream_.isRemote()||nh(),"PAUSED"!==this.state_&&(this.state_="PAUSED",this.emitter_.emit(my,{state:this.state_,reason:Ku})),ih.emit(Oh,{stream:this.stream_,type:t});break;case Qu:this.log_.info("stream - video track is able to provide media output"),"PAUSED"===this.state_&&(this.state_="PLAYING",this.emitter_.emit(my,{state:this.state_,reason:Qu}),ih.emit(Mh,{stream:this.stream_}))}}},{key:"unbindEvents",value:function(){this.element_&&(this.element_.removeEventListener($u,this.handleElementEvent),this.element_.removeEventListener(Xu,this.handleElementEvent),this.element_.removeEventListener(Yu,this.handleElementEvent),this.element_.removeEventListener(Zu,this.handleElementEvent),this.element_.removeEventListener(ed,this.handleElementEvent)),this.track_&&(this.track_.removeEventListener(Xu,this.handleTrackEvent),this.track_.removeEventListener(Ku,this.handleTrackEvent),this.track_.removeEventListener(Qu,this.handleTrackEvent))}},{key:"stop",value:function(){this.unbindEvents(),this.div_&&this.div_.removeChild(this.element_),this.element_.srcObject=null,this.element_=null}},{key:"resume",value:(t=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<video> play() error: "+e.t0),!(t=Zv({key:I_,data:{error:e.t0}})).startsWith("NotAllowedError")){e.next=11;break}throw new Al({code:Cl.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}}),e,this,[[0,5]])}))),function(){return t.apply(this,arguments)})},{key:"getVideoFrame",value:function(){var e=document.createElement("canvas");return e.width=this.element_.videoWidth,e.height=this.element_.videoHeight,e.getContext("2d").drawImage(this.element_,0,0),e.toDataURL("image/png")}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"getElement",value:function(){return this.element_?this.element_:null}}]),e}(),Xy=function(){function e(t){a(this,e),this.userId_=t.userId,this.tinyId_=t.tinyId,this.client_=t.client,this.sdpSemantics_=t.client.getSdpSemantics(),this.isUplink_=t.isUplink,this.log_=pf.createLogger({id:"n|"+this.userId_,userId:this.client_.getUserId(),sdkAppId:this.client_.getSDKAppId(),isLocal:this.isUplink_}),this.signalChannel_=t.signalChannel,this.peerConnection_=null,this.connectTimer_=-1,this.isErrorObserved_=!1,this.emitter_=new rh,this.currentState_=ru,this.waitForPeerConnectionConnectedPromise_=null,this.isReconnecting_=!1,this.reconnectionCount_=0,this.reconnectionTimer_=-1,this.isFirstConnection_=!0}var t;return s(e,[{key:"initialize",value:function(){var e={iceServers:this.client_.getIceServers(),iceTransportPolicy:this.client_.getIceTransportPolicy(),sdpSemantics:this.sdpSemantics_,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};this.peerConnection_=new RTCPeerConnection(e),this.peerConnection_.onconnectionstatechange=this.onConnectionStateChange.bind(this)}},{key:"close",value:function(){this.log_.info("closing connection"),this.closePeerConnection()}},{key:"closePeerConnection",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.peerConnection_&&(this.peerConnection_.onconnectionstatechange=function(){},this.peerConnection_.close(),this.peerConnection_=null,e&&this.emitConnectionStateChangedEvent(ru))}},{key:"getDTLSTransportState",value:function(){if(!this.peerConnection_)return"unknown";var e=null;if(this.isUplink_){if(!xg()||0===this.peerConnection_.getSenders().length)return"unknown";e=this.peerConnection_.getSenders()[0].transport}else{if(!Pg()||0===this.peerConnection_.getReceivers().length)return"unknown";e=this.peerConnection_.getReceivers()[0].transport}return e?e.state:"unknown"}},{key:"onConnectionStateChange",value:function(e){var t=this.peerConnection_.iceConnectionState,n=this.getDTLSTransportState();if(this.log_.info("onConnectionStateChange() connectionState: "+e.target.connectionState),this.log_.info("ICE Transport state: ".concat(t,", DTLS Transport state: ").concat(n)),"connecting"===e.target.connectionState&&this.emitConnectionStateChangedEvent(iu),"failed"===e.target.connectionState||"closed"===e.target.connectionState){var r="".concat(this.isUplink_?"uplink":"downlink"," ICE/DTLS Transport connection ").concat(e.target.connectionState,". ICE Transport state: ").concat(t,", DTLS Transport state: ").concat(n),i=new Al({message:r,code:Cl.ICE_TRANSPORT_ERROR});Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Iu,error:i}),this.emitConnectionStateChangedEvent(ru),this.isErrorObserved_||this.emitter_.emit(Kg,i)}if(("connected"===e.target.connectionState||"completed"===e.target.connectionState)&&(this.logSelectedCandidate(),Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:Iu}),this.emitConnectionStateChangedEvent(ou),!this.isUplink_&&!this.sentSubscriptionAfterConnected_&&this.pendingSubscription_.length>0)){this.log_.info("send pending subscription after RTCPeerConnection is connected");var a=this.pendingSubscription_[0];this.doSendSubscription(a.data,a.stream,a.type),this.sentSubscriptionAfterConnected_=!0}}},{key:"emitConnectionStateChangedEvent",value:function(e){e!==this.currentState_&&(this.currentState_===au&&e===iu||(ih.emit(Gh,{client:this.client_,connection:this,prevState:this.currentState_,state:e}),this.emitter_.emit(Qg,{prevState:this.currentState_,state:e}),this.currentState_=e))}},{key:"hitTest",value:function(e){return(0===e||"0"===e)&&this.isUplink_||e===this.tinyId_}},{key:"addEventInternal",value:function(e,t){var n=this.client_.getUserId(),r={eventId:e,eventDesc:t,timestamp:_o(),userId:n,tinyId:this.client_.getTinyId()};this.isUplink_||(r.remoteUserId=this.userId_,r.remoteTinyId=this.tinyId_),t_(n,r)}},{key:"getPeerConnection",value:function(){return this.peerConnection_}},{key:"getClient",value:function(){return this.client_}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"logSelectedCandidate",value:(t=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.peerConnection_){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,this.peerConnection_.getStats();case 4:t=e.sent,n=b(t),e.prev=6,n.s();case 8:if((r=n.n()).done){e.next=18;break}if((i=g(r.value,2))[0],a=i[1],!Vg(a)){e.next=16;break}return o=t.get(a.localCandidateId),s=t.get(a.remoteCandidateId),o&&this.log_.info("local candidate: ".concat(o.candidateType," ").concat(o.protocol,":").concat(o.ip||o.address,":").concat(o.port," ").concat(o.networkType||""," ").concat("relay"===o.candidateType?"relayProtocol:"+o.relayProtocol:"")),s&&this.log_.info("remote candidate: ".concat(s.candidateType," ").concat(s.protocol,":").concat(s.ip||s.address,":").concat(s.port)),e.abrupt("break",18);case 16:e.next=8;break;case 18:e.next=23;break;case 20:e.prev=20,e.t0=e.catch(6),n.e(e.t0);case 23:return e.prev=23,n.f(),e.finish(23);case 26:case"end":return e.stop()}}),e,this,[[6,20,23,26]])}))),function(){return t.apply(this,arguments)})},{key:"getCurrentState",value:function(){return this.currentState_}},{key:"waitForPeerConnectionConnected",value:function(){var e=this;return this.waitForPeerConnectionConnectedPromise_||(this.waitForPeerConnectionConnectedPromise_=new Promise((function(t,n){if(e.currentState_===ou)return t();var r=-1,i=function n(i){i.state===ou&&(clearTimeout(r),e.emitter_.off(Qg,n,e),t())};r=setTimeout((function(){e.emitter_.off(Qg,i,e),n(new Al({code:Cl.API_CALL_TIMEOUT,message:"connection timeout"}))}),1e4),e.emitter_.on(Qg,i,e)})),this.waitForPeerConnectionConnectedPromise_=this.waitForPeerConnectionConnectedPromise_.then((function(t){return e.waitForPeerConnectionConnectedPromise_=null,t})).catch((function(t){throw e.waitForPeerConnectionConnectedPromise_=null,t}))),this.waitForPeerConnectionConnectedPromise_}},{key:"getReconnectionCount",value:function(){return this.reconnectionCount_}},{key:"startReconnection",value:function(){this.isReconnecting_=!0,this.emitConnectionStateChangedEvent(au),this.reconnect(),this.addEventInternal(this.isUplink_?Km:$m,"".concat(this.isUplink_?"uplink":"downlink","-connection is reconnecting"))}},{key:"stopReconnection",value:function(){this.log_.info("stop reconnection"),this.isReconnecting_=!1,this.reconnectionCount_=0,this.clearReconnectionTimer(),this.signalChannel_.off(Gf,this.reconnect,this)}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"off",value:function(e,t,n){this.emitter_.off(e,t,n)}}]),e}();function $y(e,t,n,r){if(this.useStringRoomId_){if(!(Fl(e)&&/^[A-Za-z\d\s!#$%&()+\-:;<=.>?@[\]^_{}|~,]{1,64}$/.test(e)))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:x_,data:t,link:{className:r,fnName:n}})})}else if(!(jl(e)&&/^[1-9]\d*$/.test(String(e))&&e<4294967295))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:D_,data:t,link:{className:r,fnName:n}})})}function Yy(e,t,n,r){if(!/^[A-Za-z\d_-]*$/.test(e))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Y_,data:t,link:{className:r,fnName:n}})})}var Zy,eS,tS={TRTC:{createClient:{name:"clientConfig",required:!0,type:Bu,properties:{sdkAppId:{required:!0,type:Uu,allowEmpty:!1},userId:{required:!0,type:Vu,allowEmpty:!1},userSig:{required:!0,type:Vu,allowEmpty:!1},mode:{required:!0,type:Vu,values:["rtc","live"]},useStringRoomId:{type:Fu},autoSubscribe:{type:Fu},enableAutoPlayDialog:{type:Fu},streamId:{type:Vu},userDefineRecordId:{type:Vu},pureAudioPushMode:{type:Uu,values:[1,2]}}},createStream:{name:"streamConfig",required:!0,type:Bu,properties:{userId:{type:Vu},audio:{type:Fu},video:{type:Fu},screen:{type:Fu},screenAudio:{type:Fu},microphoneId:{type:Vu},cameraId:{type:Vu},facingMode:{type:Vu,values:[Wu,qu]},audioSource:{instanceOf:MediaStreamTrack},videoSource:{instanceOf:MediaStreamTrack},mirror:{type:Fu}},validate:function(e){if(!Ul(e.screen)&&e.screen&&Ul(e.audio)&&(e.audio=!1),!(Ul(e.audio)&&Ul(e.video)||Ul(e.audioSource)&&Ul(e.videoSource)))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Vv})});if(!Ul(e.screen)&&!0===e.screen&&!0===e.video)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Uv})});if(e.audio&&e.screenAudio)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Fv})});if(!0!==e.screen&&!0===e.screenAudio)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:jv})});if(!Ul(e.screen)&&!0===e.screen&&!this.isScreenShareSupported())throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:qv})})}}},CLIENT:{join:{name:"options",required:!0,properties:{roomId:{required:!0,type:[Uu,Vu],allowEmpty:!1,validate:$y},role:{type:[Vu],values:["anchor","audience"]}}},publish:{name:"stream",required:!0,instanceOf:hd,validate:function(e){if(!this.isJoined_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:U_})});if("live"===this.mode_&&"audience"===this.role_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:j_})});if(this.localStream_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:H_})});if(!e.getIsReadyToPublish())throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:B_})});if(this.notPublishWithoutH264Supported_)throw new Al({code:Cl.NOT_SUPPORTED_H264,message:Zv({key:Jv})})}},unpublish:{name:"stream",required:!0,instanceOf:hd,validate:function(e){if(e!==this.localStream_)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:F_})})}},subscribe:[{name:"stream",required:!0,instanceOf:pd,validate:function(e){if(!e.getConnection())throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:G_})});if(this.notSubscribeWithoutH264Supported_)throw new Al({code:Cl.NOT_SUPPORTED_H264,message:Zv({key:zv})})}},{name:"options",type:Bu,properties:{audio:{type:Fu},video:{type:Fu}}}],unsubscribe:{name:"stream",required:!0,instanceOf:pd,validate:function(e){if(!e.getConnection())throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:G_})})}},switchRole:{name:"role",required:!0,values:["anchor","audience"],validate:function(){if("live"!==this.mode_)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:z_})});if(!this.isJoined_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:W_})})}},startPublishCDNStream:{name:"options",required:!1,properties:{streamId:{type:Vu,validate:Yy},appId:{type:Uu,allowEmpty:!1},bizId:{type:Uu,allowEmpty:!1},url:{type:Vu,allowEmpty:!1}}},startMixTranscode:{name:"config",required:!0,type:Bu,properties:{mode:{type:Vu,values:["preset-layout","manual"]},streamId:{type:Vu,validate:Yy},videoWidth:{type:Uu,notLessThanZero:!0},videoHeight:{type:Uu,notLessThanZero:!0},videoBitrate:{type:Uu,notLessThanZero:!0,allowEmpty:!1},videoFramerate:{type:Uu,validate:function(e,t,n,r){if(e<=0||e>30)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Pv,link:{className:r,fnName:n}})})}},videoGop:{type:Uu,validate:function(e,t,n,r){if(e<1||e>8)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:xv,link:{className:r,fnName:n}})})}},audioSampleRate:{type:Uu,notLessThanZero:!0},audioBitrate:{type:Uu,validate:function(e,t,n,r){if(e<32||e>192)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Dv,link:{className:r,fnName:n}})})}},audioChannels:{type:Uu,values:[1,2]},backgroundColor:{type:Uu},backgroundImage:{type:Vu},mixUsers:{required:!0,type:ju,arrayItem:{require:!0,type:Bu,properties:{userId:{required:!0,type:Vu},roomId:{type:[Vu,Uu],validate:$y},pureAudio:{type:Fu},width:{type:Uu,notLessThanZero:!0},height:{type:Uu,notLessThanZero:!0},locationX:{type:Uu,notLessThanZero:!0},locationY:{type:Uu,notLessThanZero:!0},zOrder:{type:Uu},streamType:{type:Vu,values:["main","auxiliary"]}}}}},validate:function(e,t,n,r,i){var a=0,o=0,s=e.mixUsers,c=[];if(s.forEach((function(e,t){if(c.push(e.userId),!e.pureAudio){if(!e.zOrder||e.zOrder<1||e>15)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Nv,data:"config.mixUsers[".concat(t,"].zOrder"),link:{className:r,fnName:n}})});e.width+e.locationX>a&&(a=e.width+e.locationX),e.height+e.locationY>o&&(o=e.height+e.locationY)}})),c.indexOf(this.getUserId())<0)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Lv,link:{className:r,fnName:n}})});if(e.videoWidth<a||e.videoHeight<o)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Ov,link:{className:r,fnName:n}})})}}},LOCAL_STREAM:{switchDevice:[{name:"type",required:!0,type:Vu,values:[Gu,Ju]},{name:"deviceId",required:!0,type:Vu,validate:function(){if(this.screen_&&!this.audio_||this.audioSource_||this.videoSource_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:dv})});if(this.publishState_===sd)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:lv})})}}]},STREAM:{play:[{name:"elementId",required:!0,type:[Vu,"HTMLDivElement"],validate:function(){if(this.isPlaying_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Mv})})}},{name:"options",type:Bu,properties:{objectFit:{type:Vu,values:["contain","cover"]},muted:{type:Fu}}}]}};function nS(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n,r){var i=r.value;return r.value=function(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return iS.call(this,t,r,n,this.name_),i.apply(this,r)},r}}function rS(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n,r){var a=r.value;return r.value=i(regeneratorRuntime.mark((function e(){var r,i,o,s=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(r=s.length,i=new Array(r),o=0;o<r;o++)i[o]=s[o];return iS.call(this,t,i,n,this.name_),e.abrupt("return",a.apply(this,i));case 3:case"end":return e.stop()}}),e,this)}))),r}}function iS(e,t,n,r){try{for(var i=0;i<e.length;i++)aS.call(this,{rule:e[i],value:t[i],key:e[i].name,fnName:n,className:r})}catch(mk){throw pf.error(mk),mk}}function aS(e){var t=this,n=e.rule,r=e.value,i=e.key,a=e.fnName,o=e.className;if(Ul(r)){if(n.required)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:c_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})})}else{if(Array.isArray(n.type)){if(!n.type.map((function(e){return e.toLowerCase()})).includes(Gl(r)))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:u_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})})}else if(!Ul(n.type)&&Gl(r)!==n.type)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:u_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})});if(!1===n.allowEmpty){var s=jl(r)&&(0===r||Number.isNaN(r)),c=Fl(r)&&""===r.trim();if(s||c)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:d_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})})}if(n.notLessThanZero&&jl(r)&&r<0)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Av,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})});if(Fl(n.instanceOf)){if(!r||r.name_!==n.instanceOf)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:l_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})})}else if(Vl(n.instanceOf)&&!(r instanceof n.instanceOf))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:l_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})});if(n.values&&!n.values.includes(r))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:h_,data:{key:i,rule:n,fnName:a,value:r},link:{className:o,fnName:a}})});var u=n.properties;Ol(u)&&"object"===Gl(r)&&Object.keys(u).forEach((function(e){aS.call(t,{rule:u[e],value:r&&r[e],key:"".concat(i,".").concat(e),fnName:a,className:o})}));var d=n.arrayItem;Ol(d)&&function(e){return"array"===Gl(e)}(r)&&r.forEach((function(e,n){aS.call(t,{rule:d,value:e,key:"".concat(i,"[").concat(n,"]"),fnName:a,className:o})})),Vl(n.validate)&&n.validate.call(this,r,i,a,o,this)}}Tt({target:"Number",stat:!0},{isNaN:function(e){return e!=e}});var oS=(Zy=rS.apply(void 0,y(tS.STREAM.play)),I((eS=function(){function e(t){a(this,e),this.name_=fd,this.userId_=t.userId,this.isRemote_=t.isRemote,this.type_=t.type,this.log_=pf.createLogger({id:"s".concat(t.seq?t.seq:"","|").concat(this.userId_),userId:Ul(t.client)?void 0:t.client.getUserId(),sdkAppId:Ul(t.client)?void 0:t.client.getSDKAppId(),isLocal:!this.isRemote_,type:this.isRemote_?this.type_:""}),this.mirror_=!1,this.isRemote_||(this.mirror_=!0),Ul(t.mirror)||(this.mirror_=t.mirror),this.client_=null,Ul(t.client)||(this.client_=t.client),this.mediaStream_=null,this.div_=null,this.isPlaying_=!1,this.connection_=null,this.audioPlayer_=null,this.videoPlayer_=null,this.muted_=!1,this.objectFit_="cover",this.id_=_g(),this.audioOutputDeviceId_=0,this.audioVolume_=1,this.emitter_=new rh,this.connectionState_=ru,this.installEvents()}var r,o,c,u,d;return s(e,[{key:"installEvents",value:function(){ih.on(Wh,this.restartPlayback,this)}},{key:"uninstallEvents",value:function(){ih.off(Wh,this.restartPlayback,this)}},{key:"getType",value:function(){return this.type_}},{key:"getLogger",value:function(){return this.log_}},{key:"isSubscribed",get:function(){return"main"===this.type_&&this.connection_.isMainStreamSubscribed||"auxiliary"===this.type_&&this.connection_.isAuxStreamSubscribed}},{key:"isMainVideoSubscribed",get:function(){var e=this.getSubscribedState();return"main"===this.type_&&e&&e.video}},{key:"isMainAudioSubscribed",get:function(){var e=this.getSubscribedState();return"main"===this.type_&&e&&e.audio}},{key:"isAuxVideoSubscribed",get:function(){var e=this.getSubscribedState();return"auxiliary"===this.type_&&e&&e.auxiliary}},{key:"emitConnectionStateChanged",value:function(e){e.state!==this.connectionState_&&(e.state!==ru&&this.isRemote_&&!this.isSubscribed||(this.emitter_.emit(py,e),this.connectionState_=e.state))}},{key:"setConnection",value:function(e){this.connection_!==e&&(e instanceof Xy?(null!==this.connection_&&this.connection_.off(Qg,this.emitConnectionStateChanged,this),e.on(Qg,this.emitConnectionStateChanged,this)):null===e&&this.connection_.off(Qg,this.emitConnectionStateChanged,this),this.connection_=e)}},{key:"getConnection",value:function(){return this.connection_}},{key:"play",value:(d=i(regeneratorRuntime.mark((function e(t,r){var i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.isPlaying_=!0,this.log_.info("stream start to play with options: ".concat(JSON.stringify(r))),(i=document.createElement("div")).setAttribute("id","player_".concat(this.id_)),i.setAttribute("style","width: 100%; height: 100%; position: relative; background-color: black; overflow: hidden;"),a=t,"object"!==n(t)&&(a=document.getElementById(t)),a.appendChild(i),this.div_=i,this.isRemote_||(this.muted_=!0),r&&!Ul(r.muted)&&(this.muted_=r.muted),this.isRemote_&&"auxiliary"===this.getType()&&(this.objectFit_="contain"),r&&!Ul(r.objectFit)&&(this.objectFit_=r.objectFit),ih.emit(Vh,{stream:this}),e.next=16,Promise.all([this.playAudio(),this.playVideo()]);case 16:ih.emit(Sh,{stream:this});case 17:case"end":return e.stop()}}),e,this)}))),function(e,t){return d.apply(this,arguments)})},{key:"playAudio",value:(u=i(regeneratorRuntime.mark((function e(){var n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.hasAudio()&&(!this.isRemote_||this.isMainAudioSubscribed)){e.next=2;break}return e.abrupt("return");case 2:if(n=this.getAudioTrack(),this.audioPlayer_||!n){e.next=17;break}return this.log_.info("stream - create AudioPlayer and play"),this.audioPlayer_=new xy({stream:this,track:n,div:this.div_,muted:this.muted_,outputDeviceId:this.audioOutputDeviceId_,volume:this.audioVolume_}),this.audioPlayer_.on(my,(function(e){var n={type:Gu,state:e.state,reason:e.reason};ih.emit(kh,t({stream:r},n)),r.emitter_.emit(ly,n)})),e.prev=7,e.next=10,this.audioPlayer_.play();case 10:e.next=17;break;case 12:throw e.prev=12,e.t0=e.catch(7),this.client_&&this.client_.getEnableAutoPlayDialog()&&new Ky,this.emitter_.emit(fy,e.t0),e.t0;case 17:case"end":return e.stop()}}),e,this,[[7,12]])}))),function(){return u.apply(this,arguments)})},{key:"playVideo",value:(c=i(regeneratorRuntime.mark((function e(){var n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.hasVideo()&&(!this.isRemote_||this.isMainVideoSubscribed||this.isAuxVideoSubscribed)){e.next=2;break}return e.abrupt("return");case 2:if(n=this.getVideoTrack(),this.videoPlayer_||!n){e.next=18;break}return ih.emit(Fh,{stream:this}),this.log_.info("stream - create VideoPlayer and play"),this.videoPlayer_=new Qy({stream:this,track:n,div:this.div_,muted:this.muted_,objectFit:this.objectFit_,mirror:this.mirror_}),this.videoPlayer_.on(my,(function(e){var n={type:Ju,state:e.state,reason:e.reason};ih.emit(kh,t({stream:r},n)),r.emitter_.emit(ly,n)})),e.prev=8,e.next=11,this.videoPlayer_.play();case 11:e.next=18;break;case 13:throw e.prev=13,e.t0=e.catch(8),this.client_&&this.client_.getEnableAutoPlayDialog()&&new Ky,this.emitter_.emit(fy,e.t0),e.t0;case 18:case"end":return e.stop()}}),e,this,[[8,13]])}))),function(){return c.apply(this,arguments)})},{key:"stopAudio",value:function(){this.audioPlayer_&&(this.log_.info("stream - stop AudioPlayer"),this.audioPlayer_.stop(),this.audioPlayer_=null)}},{key:"stopVideo",value:function(){this.videoPlayer_&&(this.log_.info("stream - stop VideoPlayer"),this.videoPlayer_.stop(),this.videoPlayer_=null)}},{key:"restartPlayback",value:function(){this.audioPlayer_&&!this.audioPlayer_.isPlaying&&this.restartAudio(),this.videoPlayer_&&!this.videoPlayer_.isPlaying&&this.restartVideo()}},{key:"restartAudio",value:function(){this.isPlaying_&&(this.stopAudio(),this.playAudio().catch((function(e){})))}},{key:"restartVideo",value:function(){this.isPlaying_&&(this.stopVideo(),this.playVideo().catch((function(e){})))}},{key:"stop",value:function(){this.isPlaying_&&(this.isPlaying_=!1,this.stopAudio(),this.stopVideo(),this.div_.parentNode.removeChild(this.div_))}},{key:"resume",value:(o=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isPlaying_){e.next=2;break}return e.abrupt("return");case 2:if(this.log_.info("stream - resume"),!this.audioPlayer_){e.next=6;break}return e.next=6,this.audioPlayer_.resume();case 6:if(!this.videoPlayer_){e.next=9;break}return e.next=9,this.videoPlayer_.resume();case 9:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"close",value:function(){this.isPlaying_&&this.stop(),this.mediaStream_&&(this.mediaStream_.preventEvent=1,this.mediaStream_.getTracks().forEach((function(e){e.stop()})),this.mediaStream_=null),this.uninstallEvents()}},{key:"muteAudio",value:function(){return this.addRemoteEvent(!0,Gu),this.doEnableTrack(Gu,!1)}},{key:"muteVideo",value:function(){return this.addRemoteEvent(!0,Ju),this.doEnableTrack(Ju,!1)}},{key:"unmuteAudio",value:function(){return this.addRemoteEvent(!1,Gu),this.doEnableTrack(Gu,!0)}},{key:"unmuteVideo",value:function(){return this.addRemoteEvent(!1,Ju),this.doEnableTrack(Ju,!0)}},{key:"addRemoteEvent",value:function(e,t){if(this.isRemote_&&this.client_){var n=this.client_.getUserId(),r="".concat(e?Ku:Qu," remote ").concat(t);t_(n,{eventId:t===Gu?e?Mm:Um:e?Om:Vm,eventDesc:r,timestamp:(new Date).getTime(),userId:n,tinyId:this.client_.getTinyId(),remoteUserId:this.userId_,remoteTinyId:this.connection_.getTinyId()})}}},{key:"doEnableTrack",value:function(e,t){var n=!1;return e===Gu?this.mediaStream_.getAudioTracks().forEach((function(e){n=!0,e.enabled=t})):this.mediaStream_.getVideoTracks().forEach((function(e){n=!0,e.enabled=t})),n}},{key:"getId",value:function(){return this.id_}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.connection_?this.connection_.getTinyId():""}},{key:"isPlaying",value:function(){return this.isPlaying_}},{key:"setAudioOutput",value:(r=i(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.audioOutputDeviceId_=t,!this.audioPlayer_){e.next=4;break}return e.next=4,this.audioPlayer_.setSinkId(t);case 4:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"setAudioVolume",value:function(e){this.audioVolume_=e,this.log_.info("setAudioVolume to ".concat(e)),this.audioPlayer_&&this.audioPlayer_.setVolume(e)}},{key:"getAudioLevel",value:function(){var e=0;return this.audioPlayer_&&(e=this.audioPlayer_.getAudioLevel()),e}},{key:"hasAudio",value:function(){if(this.isRemote_){if(!this.connection_)return!1;var e=this.connection_.getTrackState();return"main"===this.type_&&e.audio}return!!this.getAudioTrack()}},{key:"hasVideo",value:function(){if(this.isRemote_){if(!this.connection_)return!1;var e=this.connection_.getTrackState();return"auxiliary"===this.type_?e.auxiliary:e.video}return!!this.getVideoTrack()}},{key:"getSubscribedState",value:function(){return this.isRemote_&&this.connection_?this.connection_.getSubscribeState():null}},{key:"getAudioTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getAudioTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getVideoTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoFrame",value:function(){return this.videoPlayer_?this.videoPlayer_.getVideoFrame():null}},{key:"getMediaStream",value:function(){return this.mediaStream_}},{key:"setMediaStream",value:function(e){e!==this.mediaStream_&&(this.mediaStream_&&this.mediaStream_.getTracks().forEach((function(e){return e.stop()})),this.mediaStream_=e)}},{key:"updateVideoPlayingState",value:function(e){this.isPlaying_&&(e?(this.log_.info("playing state updated, play video"),this.playVideo().catch((function(e){}))):(this.log_.info("playing state updated, stop video"),this.stopVideo()))}},{key:"updateAudioPlayingState",value:function(e){this.isPlaying_&&(e?(this.log_.info("playing state updated, play audio"),this.playAudio().catch((function(e){}))):(this.log_.info("playing state updated, stop audio"),this.stopAudio()))}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"off",value:function(e,t,n){"*"===e?this.emitter_.removeAllListeners():this.emitter_.off(e,t,n)}},{key:"isRemote",value:function(){return this.isRemote_}},{key:"getDiv",value:function(){return this.div_}},{key:"getObjectFit",value:function(){return this.objectFit_}},{key:"getMuted",value:function(){return this.muted_}},{key:"getClient",value:function(){return this.client_}}]),e}()).prototype,"play",[Zy],Object.getOwnPropertyDescriptor(eS.prototype,"play"),eS.prototype),eS),sS=function(e){u(RemoteStream,e);var n=_(RemoteStream);function RemoteStream(e){var r;a(this,RemoteStream);var i={isRemote:!0,type:e.type},o=t(t({},e),i);return(r=n.call(this,o)).name_=pd,r.isInSubscriptionCycle_=!1,r.isStreamAddedEventEmitted_=!1,r.isAbleToCallSubscription_=!0,r}return s(RemoteStream,[{key:"installEvents",value:function(){v(d(RemoteStream.prototype),"installEvents",this).call(this),ih.on(wh,this.handleStreamSubscribed,this),ih.on(Ch,this.handleStreamUnsubscribed,this)}},{key:"uninstallEvents",value:function(){v(d(RemoteStream.prototype),"uninstallEvents",this).call(this),ih.off(wh,this.handleStreamSubscribed,this),ih.off(Ch,this.handleStreamUnsubscribed,this)}},{key:"handleStreamSubscribed",value:function(e){e.client===this.client_&&e.stream===this&&this.connection_.getCurrentState()===ou&&this.emitConnectionStateChanged({prevState:ru,state:ou})}},{key:"handleStreamUnsubscribed",value:function(e){e.client===this.client_&&e.stream===this&&this.emitConnectionStateChanged({prevState:ou,state:ru})}},{key:"getType",value:function(){return v(d(RemoteStream.prototype),"getType",this).call(this)}},{key:"getIsAbleToCallSubscription",value:function(){return this.isAbleToCallSubscription_}},{key:"setIsAbleToCallSubscription",value:function(e){this.isAbleToCallSubscription_=e}},{key:"setInSubscriptionCycle",value:function(e){this.isInSubscriptionCycle_=e}},{key:"isInSubscriptionCycle",value:function(){return this.isInSubscriptionCycle_}},{key:"setIsStreamAddedEventEmitted",value:function(e){this.isStreamAddedEventEmitted_=e}},{key:"getIsStreamAddedEventEmitted",value:function(){return this.isStreamAddedEventEmitted_}},{key:"getAudioTrack",value:function(){return this.connection_&&this.connection_.getTrackState().audio?v(d(RemoteStream.prototype),"getAudioTrack",this).call(this):null}},{key:"getVideoTrack",value:function(){if(!this.connection_)return null;var e=this.connection_.getTrackState();return("main"!==this.type_||e.video)&&("auxiliary"!==this.type_||e.auxiliary)?v(d(RemoteStream.prototype),"getVideoTrack",this).call(this):null}}]),RemoteStream}(oS),cS=function(){function e(t){a(this,e),this.client_=t.client,this.subscribedStreams_=new Map,this.unsubscribedStreams_=new Map,this.subscriptedOptions_=new Map,this.autoRecoveryFlags_=new Map}var t,n,r;return s(e,[{key:"isEnabled",get:function(){return"webrtc"!==this.client_.getEnv()}},{key:"recover",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.getUserId(),r=t.getType(),this.hasAutoRecoveryFlag(n,r)){e.next=5;break}return e.abrupt("return");case 5:if(i=this.getUnsubscribedStream(n,r)?"unsubscribe":"subscribe",e.prev=6,pf.warn("recover() try to recover subscription [".concat(i,"][").concat(n,"][").concat(r,"]")),"subscribe"!==i){e.next=13;break}return e.next=11,this.recoverSubscription(n,t);case 11:e.next=15;break;case 13:return e.next=15,this.recoverUnsubscription(n,t);case 15:Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:Cu}),pf.warn("recover() recover successfully [".concat(i,"][").concat(n,"][").concat(r,"]")),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(6),pf.error("recover() recover failed [".concat(i,"][").concat(n,"][").concat(r,"]"),e.t0),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Cu,error:e.t0});case 23:this.deleteAutoRecoveryFlag(n,r);case 24:case"end":return e.stop()}}),e,this,[[6,19]])}))),function(e){return r.apply(this,arguments)})},{key:"recoverSubscription",value:(n=i(regeneratorRuntime.mark((function e(t,n){var r,i,a,o,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getOptions(t,n.getType()),i=this.getSubscribedStream(t,n.getType()),r&&i){e.next=4;break}return e.abrupt("return");case 4:a=this.getStreamMuteState(i),o=a.isAudioMuted,s=a.isVideoMuted,this.mergeStream(i,n),this.recoverPlayingState(i),o&&i.doEnableTrack(Gu,!1),s&&i.doEnableTrack(Ju,!1);case 9:case"end":return e.stop()}}),e,this)}))),function(e,t){return n.apply(this,arguments)})},{key:"recoverUnsubscription",value:(t=i(regeneratorRuntime.mark((function e(t,n){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getUnsubscribedStream(t,n.getType())){e.next=3;break}return e.abrupt("return");case 3:this.mergeStream(r,n);case 4:case"end":return e.stop()}}),e,this)}))),function(e,n){return t.apply(this,arguments)})},{key:"getStreamMuteState",value:function(e){var t={isAudioMuted:!1,isVideoMuted:!1},n=e.getMediaStream();return n&&(t.isAudioMuted=n.getAudioTracks().map((function(e){return e.enabled})).includes(!1),t.isVideoMuted=n.getVideoTracks().map((function(e){return e.enabled})).includes(!1)),t}},{key:"recoverPlayingState",value:function(e){var t=e.isPlaying(),n=e.getDiv();if(t&&n){var r=n.parentNode;e.stop(),e.play(r,{objectFit:e.getObjectFit(),muted:e.getMuted()})}}},{key:"mergeStream",value:function(e,t){var n=t.getConnection(),r=t.getMediaStream();e.setConnection(n),n.setRemoteStream(r.id,e),e.setMediaStream(r),e.updateAudioPlayingState(t.hasAudio()),e.updateVideoPlayingState(t.hasVideo())}},{key:"addSubscriptionRecord",value:function(e,t,n){var r=t.getType();if(this.subscribedStreams_.has(e))this.subscribedStreams_.get(e).set(r,t);else{var i=new Map;i.set(t.getType(),t),this.subscribedStreams_.set(e,i)}if(this.subscriptedOptions_.has(e))this.subscriptedOptions_.get(e).set(r,n);else{var a=new Map;a.set(t.getType(),n),this.subscriptedOptions_.set(e,a)}this.deleteUnsubscriptionRecord(e,r)}},{key:"addUnsubscriptionRecord",value:function(e,t){if(this.unsubscribedStreams_.has(e))this.unsubscribedStreams_.get(e).set(t.getType(),t);else{var n=new Map;n.set(t.getType(),t),this.unsubscribedStreams_.set(e,n)}this.deleteSubscriptionRecord(e,t.getType())}},{key:"getSubscribedStream",value:function(e,t){return this.subscribedStreams_.has(e)&&this.subscribedStreams_.get(e).has(t)?this.subscribedStreams_.get(e).get(t):null}},{key:"getOptions",value:function(e,t){return this.subscriptedOptions_.has(e)&&this.subscriptedOptions_.get(e).has(t)?this.subscriptedOptions_.get(e).get(t):null}},{key:"getUnsubscribedStream",value:function(e,t){return this.unsubscribedStreams_.has(e)&&this.unsubscribedStreams_.get(e).has(t)?this.unsubscribedStreams_.get(e).get(t):null}},{key:"deleteSubscriptionRecord",value:function(e,t){this.subscribedStreams_.has(e)&&this.subscribedStreams_.get(e).delete(t),this.subscriptedOptions_.has(e)&&this.subscriptedOptions_.get(e).delete(t)}},{key:"deleteUnsubscriptionRecord",value:function(e,t){this.unsubscribedStreams_.has(e)&&this.unsubscribedStreams_.get(e).delete(t)}},{key:"markAllStream",value:function(){for(var e=0,t=y(this.subscribedStreams_.entries());e<t.length;e++)for(var n=g(t[e],2),r=n[0],i=0,a=y(n[1].entries());i<a.length;i++){var o=g(a[i],1)[0];this.setAutoRecoveryFlag(r,o)}for(var s=0,c=y(this.unsubscribedStreams_.entries());s<c.length;s++)for(var u=g(c[s],2),d=u[0],l=0,h=y(u[1].entries());l<h.length;l++){var p=g(h[l],1)[0];this.setAutoRecoveryFlag(d,p)}}},{key:"setAutoRecoveryFlag",value:function(e,t){if(pf.info("setAutoRecoveryFlag() mark [".concat(e,"][").concat(t,"]")),this.autoRecoveryFlags_.has(e))this.autoRecoveryFlags_.get(e).set(t);else{var n=new Map;n.set(t),this.autoRecoveryFlags_.set(e,n)}}},{key:"hasAutoRecoveryFlag",value:function(e,t){return!!this.isEnabled&&(this.autoRecoveryFlags_.has(e)&&this.autoRecoveryFlags_.get(e).has(t))}},{key:"deleteAutoRecoveryFlag",value:function(e,t){this.autoRecoveryFlags_.has(e)&&this.autoRecoveryFlags_.get(e).delete(t)}},{key:"delete",value:function(e){this.unsubscribedStreams_.delete(e),this.subscribedStreams_.delete(e),this.subscriptedOptions_.delete(e),this.autoRecoveryFlags_.delete(e)}}]),e}(),uS=Wt("slice"),dS=_e("species"),lS=[].slice,hS=Math.max;Tt({target:"Array",proto:!0,forced:!uS},{slice:function(e,t){var n,r,i,a=B(this),o=ot(a),s=rt(e,o),c=rt(void 0===t?o:t,o);if(wt(a)&&(n=a.constructor,(Ft(n)&&(n===Array||wt(n.prototype))||G(n)&&null===(n=n[dS]))&&(n=void 0),n===Array||void 0===n))return lS.call(a,s,c);for(r=new(void 0===n?Array:n)(hS(c-s,0)),i=0;s<c;s++,i++)s in a&&Ji(r,i,a[s]);return r.length=i,r}}),Tt({target:"String",proto:!0,forced:o_("small")},{small:function(){return a_(this,"small","","")}});var pS=function(){function e(t){a(this,e),this.player_=t,this.canvas_=document.createElement(Hu),this.canvasCtx_=this.canvas_.getContext("2d")}return s(e,[{key:"setCanvasRect",value:function(e,t){this.canvas_.width=e,this.canvas_.height=t}},{key:"drawVideoToCanvas",value:function(){var e=this.player_.getElement();this.canvasCtx_.drawImage(e,0,0,this.canvas_.width,this.canvas_.height)}},{key:"generateVideoTrackFromCanvasCapture",value:function(e){return this.canvas_.captureStream(e).getVideoTracks()[0]}},{key:"generateStreamFromTrack",value:function(e){var t=new MediaStream;return t.addTrack(e),t}},{key:"destroy",value:function(){this.player_.stop(),this.canvas_=null,this.canvasCtx_=null}},{key:"canvas",get:function(){return this.canvas_}},{key:"canvasCtx",get:function(){return this.canvasCtx_}},{key:"canDrawVideoToCanvas",get:function(){if(this.player_){var e=this.player_.getElement();if(e)return e.readyState===e.HAVE_ENOUGH_DATA}return!1}}]),e}(),fS=function(){function e(t){a(this,e),this.localStream_=t,this.player_=new Qy({stream:t,track:t.getVideoTrack(),muted:!0,objectFit:"cover",mirror:!1}),this.player_.play().then((function(){pf.info("VideoGenerator: play local video success")})).catch((function(){pf.error("VideoGenerator: Failed to play local video")})),this.processor_=new pS(this.player_)}return s(e,[{key:"generateSmallVideoTrack",value:function(e){var t=this.getSmallVideoProfile(e);this.processor_.setCanvasRect(t.width,t.height),this.player_.setRect({width:t.width,height:t.height});var n=this.processor_.generateVideoTrackFromCanvasCapture(t.frameRate);return this.interval_=vg.setInterval(this.render.bind(this),Math.ceil(1e3/t.frameRate)),n}},{key:"render",value:function(){this.processor_.canDrawVideoToCanvas&&this.processor_.drawVideoToCanvas()}},{key:"destroy",value:function(){vg.clearInterval(this.interval_),this.processor_&&this.processor_.destroy()}},{key:"getSmallVideoProfile",value:function(e){var t,n=this.localStream_.getVideoTrack(),r=this.localStream_.getVideoProfile(),i=n.getSettings(),a=i&&i.width&&i.height?{width:i.width,height:i.height}:r,o=a.width*a.height,s=e.width*e.height;return pf.log("big stream resolution: ".concat(a.height,"*").concat(a.width," small stream resolution: ").concat(e.height,"*").concat(e.width," ")),o>s?t=o/s:(pf.warn("Small stream resolution is larger than big stream, which is invalid. big: ".concat(a.width," * ").concat(a.height," small: ").concat(e.width," * ").concat(e.height)),t=o/19200),{width:parseInt(a.width/Math.sqrt(t)),height:parseInt(a.height/Math.sqrt(t)),frameRate:e.frameRate}}}]),e}(),mS={voiceActivityDetection:!1},_S=function(e){u(b,e);var t,n,r,o,c,l,h,p,f,m,g,y,S,k=_(b);function b(e){var t;return a(this,b),(t=k.call(this,e)).localStream_=null,t.exchangeSDPTimeout_=-1,t.smallGenerator_=null,t.isSDPExchanging_=!1,t.ssrc_={audio:0,video:0,small:0},t.canvasTrack_=null,t}return s(b,[{key:"initialize",value:function(){v(d(b.prototype),"initialize",this).call(this),this.installEvents()}},{key:"reset",value:function(){v(d(b.prototype),"close",this).call(this),this.uninstallEvents(),this.clearExchangeSDPTimeout(),this.canvasTrack_=null,this.localStream_&&this.localStream_.clearCanvas()}},{key:"close",value:function(){this.reset(),this.emitConnectionStateChangedEvent(ru),this.smallGenerator_&&(this.smallGenerator_.destroy(),this.smallGenerator_=null)}},{key:"installEvents",value:function(){this.emitter_.on(Kg,this.handleError,this),this.emitter_.on(Qg,this.handleConnectionStateChange,this)}},{key:"uninstallEvents",value:function(){this.emitter_.off(Kg,this.handleError,this),this.emitter_.off(Qg,this.handleConnectionStateChange,this)}},{key:"publish",value:(S=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.localStream_=t,n=t.getMediaStream(),this.log_.info("is publishing stream: ".concat(t.getId())),r=this.localStream_.getAudioTrack(),i=this.localStream_.getVideoTrack(),r&&this.peerConnection_.addTrack(r,n),i&&(Us&&Hl(i)?(this.canvasTrack_=this.localStream_.genCanvasTrack(i),this.peerConnection_.addTrack(this.canvasTrack_,n)):this.peerConnection_.addTrack(i,n),this.client_.getIsEnableSmallStream()&&(this.smallGenerator_=new fS(this.localStream_),a=this.smallGenerator_.generateSmallVideoTrack(this.client_.smallStreamConfig_),this.peerConnection_.addTrack(a,n))),this.updateMediaSettings(n),e.next=10,this.connect();case 10:return e.abrupt("return",t);case 11:case"end":return e.stop()}}),e,this)}))),function(e){return S.apply(this,arguments)})},{key:"updateMediaSettings",value:function(e){var t=this,n=this.client_.getSystemResult().detail,r=n.isH264EncodeSupported,i=n.isVp8EncodeSupported,a="";r?a="H264":i&&(a="VP8");var o={EncVideoCodec:a,EncVideoWidth:0,EncVideoHeight:0,EncVideoBr:"0",EncVideoFps:0,EncAudioCodec:"opus",EncAudioFS:0,EncAudioCh:0,EncAudioBr:"0"};"getSettings"in MediaStreamTrack.prototype?e.getTracks().forEach((function(e){var n=e.getSettings();if(e.kind===Gu){var r=1;n.channelCount&&(r=n.channelCount),o.EncAudioCh=r,o.EncAudioBr="".concat(1e3*t.localStream_.getAudioBitrate()),o.EncAudioFS=n.sampleRate}else e.kind===Ju&&(t.client_.getIsEnableSmallStream()&&(o.EncSmallVideoWidth=t.client_.smallStreamConfig.width,o.EncSmallVideoHeight=t.client_.smallStreamConfig.height,o.EncSmallVideoFps=t.client_.smallStreamConfig.framerate,o.EncSmallVideoBr="".concat(1e3*t.client_.smallStreamConfig.bitrate)),o.EncVideoWidth=n.width,o.EncVideoHeight=n.height,o.EncVideoFps=n.frameRate,o.EncVideoBr="".concat(1e3*t.localStream_.getVideoBitrate()))})):o=this.getMediaSettingsFromProfile(o),this.log_.info("updateMediaSettings: "+JSON.stringify(o)),this.signalChannel_.send(im,o)}},{key:"getMediaSettingsFromProfile",value:function(e){var t=this.localStream_;if(t){if(t.getAudioTrack()){var n=t.getAudioProfile();e.EncAudioCh=n.channelCount,e.EncAudioBr="".concat(1e3*n.bitrate),e.EncAudioFS=n.sampleRate}if(t.getVideoTrack()){var r=t.getVideoProfile();e.EncVideoWidth=r.width,e.EncVideoHeight=r.height,e.EncVideoFps=r.frameRate,e.EncVideoBr="".concat(1e3*r.bitrate)}}return e}},{key:"addTrack",value:(y=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_){e.next=20;break}if(t.kind===Ju&&Us&&Hl(t)&&(t=this.canvasTrack_=this.localStream_.genCanvasTrack(t)),this.log_.info("is adding ".concat(t.kind," track to current published local stream")),!(Dg()&&this.peerConnection_.getTransceivers().findIndex((function(e){return"stopped"===e.direction}))>=0)){e.next=7;break}return this.log_.warn("transceiver is stopping, negotiate sdp first"),e.next=7,this.updateOffer(id,t);case 7:if(!(n=this.peerConnection_.getSenders().find((function(e){return e.track&&e.track.kind===t.kind})))){e.next=14;break}return this.log_.warn("sender already exists, remove sender first"),r=n.track,this.removeSender(n),e.next=14,this.updateOffer(id,r);case 14:return i=this.localStream_.getMediaStream(),this.peerConnection_.addTrack(t,i),this.updateMediaSettings(i),e.next=19,this.updateOffer(rd,t);case 19:t_(this.userId_,{eventId:t.kind===Gu?km:Sm,eventDesc:"add ".concat(t.kind," track to current published stream"),timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_});case 20:case"end":return e.stop()}}),e,this)}))),function(e){return y.apply(this,arguments)})},{key:"isNeedToResetOfferOrder",value:function(){if("plan-b"===this.sdpSemantics_||!this.peerConnection_||!this.peerConnection_.localDescription)return!1;for(var e=this.peerConnection_.localDescription.sdp,t=lg(e),n=0;n<t.media.length;n++)if(0===t.media[n].mid&&t.media[n].type===Ju)return!0;return!1}},{key:"removeSender",value:function(e){var t=null;Dg()&&(t=this.peerConnection_.getTransceivers().find((function(t){return t.sender&&t.sender.track===e.track}))),this.peerConnection_.removeTrack(e),t&&Vl(t.stop)&&(this.log_.info("stop transceiver"),t.stop())}},{key:"removeTrack",value:(g=i(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_||!xg()){e.next=14;break}if(t.kind===Ju&&Us&&this.canvasTrack_&&(t=this.canvasTrack_,this.localStream_.clearCanvas()),this.log_.info("is removing ".concat(t.kind," track from current published local stream")),t.kind!==Ju||!this.isNeedToResetOfferOrder()){e.next=9;break}return this.reset(),this.initialize(),e.next=8,this.publish(this.localStream_);case 8:return e.abrupt("return");case 9:return(n=this.peerConnection_.getSenders().find((function(e){return e.track===t})))&&(this.removeSender(n),this.updateMediaSettings(this.localStream_.getMediaStream())),e.next=13,this.updateOffer(id,t);case 13:t_(this.userId_,{eventId:t.kind===Gu?Im:bm,eventDesc:"remove ".concat(t.kind," track from current published stream"),timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_});case 14:case"end":return e.stop()}}),e,this)}))),function(e){return g.apply(this,arguments)})},{key:"isReplaceTrackAvailable",value:function(){return"RTCRtpSender"in window&&"replaceTrack"in window.RTCRtpSender.prototype}},{key:"replaceTrack",value:(m=i(regeneratorRuntime.mark((function e(t){var n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.kind!==Ju||!Us||!this.canvasTrack_){e.next=2;break}return e.abrupt("return");case 2:if(this.isReplaceTrackAvailable()&&xg()){e.next=4;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Wv})});case 4:if(this.peerConnection_){e.next=6;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:hv})});case 6:if(0!==(n=this.peerConnection_.getSenders()).length){e.next=9;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:hv})});case 9:n.forEach((function(e){e.track&&e.track.kind===t.kind&&(r.log_.info("is replacing ".concat(t.kind," track to current published local stream")),e.replaceTrack(t))})),t_(this.userId_,{eventId:t.kind===Gu?Lm:Nm,eventDesc:"replace ".concat(t.kind," track from current published stream"),timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_});case 11:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"setBandwidth",value:(f=i(regeneratorRuntime.mark((function e(t,n,r){var i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isUplink_){e.next=2;break}return e.abrupt("return",r);case 2:if(Lg()){e.next=4;break}return e.abrupt("return",n===Ju?this.updateVideoBandwidthRestriction(r,t):this.updateAudioBandwidthRestriction(r,t));case 4:if(!(i=this.peerConnection_.getSenders().find((function(e){return e.track&&e.track.kind===n})))){e.next=20;break}return(a=i.getParameters()).encodings&&0!==a.encodings.length||(a.encodings=[{}]),"unlimited"===t?delete a.encodings[0].maxBitrate:a.encodings[0].maxBitrate=1e3*t,e.prev=9,e.next=12,i.setParameters(a);case 12:return this.log_.info(n+" bandwidth was set to "+t+" kbps"),e.abrupt("return",r);case 16:return e.prev=16,e.t0=e.catch(9),this.log_.info("failed to set bandwidth by setting maxBitrate: "+e.t0),e.abrupt("return",n===Ju?this.updateVideoBandwidthRestriction(r,t):this.updateAudioBandwidthRestriction(r,t));case 20:return e.abrupt("return",r);case 21:case"end":return e.stop()}}),e,this,[[9,16]])}))),function(e,t,n){return f.apply(this,arguments)})},{key:"setSmallStreamBandwidth",value:(p=i(regeneratorRuntime.mark((function e(t,n){var r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isUplink_){e.next=2;break}return e.abrupt("return",n);case 2:if(Lg()){e.next=4;break}return e.abrupt("return",this.updateSmallVideoBandwidthRestriction(n,t));case 4:if(!(r=this.peerConnection_.getSenders().filter((function(e){return e.track&&e.track.kind===Ju}))[1])){e.next=20;break}return(i=r.getParameters()).encodings&&0!==i.encodings.length||(i.encodings=[{}]),"unlimited"===t?delete i.encodings[0].maxBitrate:i.encodings[0].maxBitrate=1e3*t,e.prev=9,e.next=12,r.setParameters(i);case 12:return this.log_.info("small stream bandwidth was set to "+t+" kbps"),e.abrupt("return",n);case 16:return e.prev=16,e.t0=e.catch(9),this.log_.info("failed to set small stream bandwidth by setting maxBitrate: "+e.t0),e.abrupt("return",this.updateSmallVideoBandwidthRestriction(n,t));case 20:return e.abrupt("return",n);case 21:case"end":return e.stop()}}),e,this,[[9,16]])}))),function(e,t){return p.apply(this,arguments)})},{key:"updateVideoBandwidthRestriction",value:function(e,t){var n="AS";return Bs&&(n="TIAS",t*=1e3),e=-1===e.indexOf("b="+n+":")?e.replace(/m=video (.*)\r\nc=IN (.*)\r\n/,"m=video $1\r\nc=IN $2\r\nb="+n+":"+t+"\r\n"):e.replace(new RegExp("b="+n+":.*\r\n"),"b="+n+":"+t+"\r\n")}},{key:"updateAudioBandwidthRestriction",value:function(e,t){var n="AS";return Bs&&(n="TIAS",t*=1e3),e=e.replace(/m=audio (.*)\r\nc=IN (.*)\r\n/,"m=audio $1\r\nc=IN $2\r\nb="+n+":"+t+"\r\n")}},{key:"updateSmallVideoBandwidthRestriction",value:function(e,t){var n="AS";Bs&&(n="TIAS",t*=1e3);var r=/m=video (.*)\r\nc=IN (.*)\r\n/g,i=[],a=r.exec(e);for(i.push(a);null!==a;)a=r.exec(e),i.push(a);var o=i[i.length-2],s=e.slice(0,o.index),c=e.slice(o.index);return e=-1===e.indexOf("b="+n+":")?s+(c=c.replace(/m=video (.*)\r\nc=IN (.*)\r\n/,"m=video $1\r\nc=IN $2\r\nb="+n+":"+t+"\r\n")):s+(c=c.replace(new RegExp("b="+n+":.*\r\n"),"b="+n+":"+t+"\r\n"))}},{key:"removeBandwidthRestriction",value:function(e){return e.replace(/b=AS:.*\r\n/,"").replace(/b=TIAS:.*\r\n/,"")}},{key:"removeVideoOrientation",value:function(e){return e.replace(/urn:3gpp:video-orientation/,"")}},{key:"connect",value:(h=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.exchangeSDP();case 3:return e.next=5,this.waitForPeerConnectionConnected();case 5:e.next=11;break;case 7:throw e.prev=7,e.t0=e.catch(0),this.closePeerConnection(!0),e.t0;case 11:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return h.apply(this,arguments)})},{key:"exchangeSDP",value:(l=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.isSDPExchanging_=!0,e.next=4,this.createOffer();case 4:return this.log_.info("createOffer success, sending offer to remote server"),e.next=7,this.doExchangeSDP();case 7:this.isSDPExchanging_=!1,e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(0),this.isSDPExchanging_=!1,e.t0;case 14:case"end":return e.stop()}}),e,this,[[0,10]])}))),function(){return l.apply(this,arguments)})},{key:"createOffer",value:(c=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.peerConnection_.createOffer(mS);case 3:return t=e.sent,e.next=6,this.peerConnection_.setLocalDescription(t);case 6:this.updateSSRC(t.sdp),Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:ku,kind:"offer"}),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(0),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:ku,kind:"offer",error:e.t0}),e.t0;case 14:case"end":return e.stop()}}),e,this,[[0,10]])}))),function(){return c.apply(this,arguments)})},{key:"doExchangeSDP",value:function(){var e=this;return new Promise((function(t,n){e.exchangeSDPTimeout_=setTimeout((function(){e.signalChannel_.off($f.PUBLISH_RESULT,r),e.signalChannel_.off($f.ON_PUBLISH_RESPONSE,r),e.clearExchangeSDPTimeout();var t=new Al({code:Cl.API_CALL_TIMEOUT,message:Zv({key:g_})});n(t)}),5e3);var r=function(){var r=i(regeneratorRuntime.mark((function r(i){return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,e.clearExchangeSDPTimeout(),r.next=4,e.acceptAnswer(i.data.content);case 4:t(),r.next=10;break;case 7:r.prev=7,r.t0=r.catch(0),n(r.t0);case 10:case"end":return r.stop()}}),r,null,[[0,7]])})));return function(e){return r.apply(this,arguments)}}(),a={type:e.peerConnection_.localDescription.type,sdp:e.removeVideoOrientation(e.peerConnection_.localDescription.sdp),screen:e.localStream_.hasScreenTrack()};e.signalChannel_.once($f.PUBLISH_RESULT,r),e.signalChannel_.once($f.ON_PUBLISH_RESPONSE,r),e.log_.debug("sending sdp offer: "+a.sdp),e.signalChannel_.send(om,a,0)}))}},{key:"setSDPDirection",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all",r=lg(e);return r.media.forEach((function(e){"all"!==n&&e.type!==n||(e.direction=t)})),hg(r)}},{key:"updateOffer",value:(o=i(regeneratorRuntime.mark((function e(t,n){var r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.peerConnection_.createOffer(mS);case 3:return r=e.sent,Bs&&(r.sdp=this.setSDPDirection(r.sdp,"sendrecv")),e.next=7,this.peerConnection_.setLocalDescription(r);case 7:return i={action:t,trackId:n.id,kind:n.kind,type:"offer",sdp:this.peerConnection_.localDescription.sdp},this.log_.info("createOffer success, sending updated offer to remote server"),this.log_.debug("updatedOffer: "+i.sdp),e.next=12,this.signalChannel_.sendWaitForResponse({command:Yf,data:i,responseCommand:$f.UPDATE_OFFER_RESULT,timeout:1e4,commandDesc:"update offer"});case 12:return a=e.sent,e.next=15,this.acceptAnswer(a.data.content);case 15:this.updateSSRC(r.sdp),Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:ku,kind:"offer"}),e.next=24;break;case 19:throw e.prev=19,e.t0=e.catch(0),this.log_.error(e.t0),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:ku,kind:"offer",error:e.t0}),e.t0;case 24:case"end":return e.stop()}}),e,this,[[0,19]])}))),function(e,t){return o.apply(this,arguments)})},{key:"acceptAnswer",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(77392!==t.result){e.next=3;break}throw this.log_.error(Xv.NOT_SUPPORTED_H264ENCODE),new Al({code:Cl.NOT_SUPPORTED_H264,message:Zv({key:Jv})});case 3:return n=this.localStream_.getVideoBitrate(),r=this.localStream_.getAudioBitrate(),i=this.removeVideoOrientation(t.sdp),e.prev=6,e.next=9,this.setBandwidth(n,Ju,i);case 9:return i=e.sent,e.next=12,this.setBandwidth(r,Gu,i);case 12:if(i=e.sent,!this.client_.getIsEnableSmallStream()){e.next=18;break}return a=this.client_.smallStreamConfig,e.next=17,this.setSmallStreamBandwidth(a.bitrate,i);case 17:i=e.sent;case 18:return o={type:t.type,sdp:i},e.next=21,this.peerConnection_.setRemoteDescription(o);case 21:this.log_.debug("accepted answer: "+i),Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:bu,kind:"answer"}),e.next=30;break;case 25:throw e.prev=25,e.t0=e.catch(6),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:bu,kind:"answer",error:e.t0}),this.log_.error("failed to accept remote answer "+e.t0),e.t0;case 30:case"end":return e.stop()}}),e,this,[[6,25]])}))),function(e){return r.apply(this,arguments)})},{key:"sendMutedFlag",value:function(e){var t={srctinyid:0,userid:this.userId_,flag:e};this.log_.info("send muted flag: ".concat(e)),this.signalChannel_.send(rm,t)}},{key:"getIsReconnecting",value:function(){return this.isReconnecting_}},{key:"reconnect",value:(n=i(regeneratorRuntime.mark((function e(){var t,n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(-1===this.reconnectionTimer_){e.next=3;break}return this.log_.warn("reconnect() uplink is reconnecting, ignore current reconnection"),e.abrupt("return");case 3:if(!(this.reconnectionCount_>=30)){e.next=12;break}return this.log_.warn("SDK has tried reconnect uplink for ".concat(30," times, but all failed, please check your network")),this.stopReconnection(),t=new Al({code:Cl.UPLINK_RECONNECTION_FAILED,message:Zv({key:k_})}),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:gu,error:t}),this.addEventInternal(Xm,"uplink-connection reconnect fail"),this.emitConnectionStateChangedEvent(ru),this.emitter_.emit(Kg,t),e.abrupt("return");case 12:if(this.signalChannel_.getCurrentState()===Kf){e.next=16;break}return this.log_.warn("reconnect() signal channel is not connected, suspend reconnection until signal is connected"),this.signalChannel_.once(Gf,this.reconnect,this),e.abrupt("return");case 16:if(this.reconnectionCount_++,e.prev=17,this.log_.warn("reconnect() try to reconnect uplink [".concat(this.reconnectionCount_,"/").concat(30,"]")),n=Ml(this.reconnectionCount_),this.reconnectionTimer_=setTimeout((function(){r.log_.warn("reconnect() uplink reconnect timeout(".concat(n/1e3,"s), try again")),r.signalChannel_.off($f.UNPUBLISH_RESULT,r.onUnpublishResult,r),r.clearReconnectionTimer(),r.reconnect()}),n),!(this.isSDPExchanging_||this.peerConnection_&&"connecting"===this.peerConnection_.connectionState)){e.next=23;break}return e.abrupt("return");case 23:this.signalChannel_.send(sm),this.signalChannel_.once($f.UNPUBLISH_RESULT,this.onUnpublishResult,this),e.next=29;break;case 27:e.prev=27,e.t0=e.catch(17);case 29:case"end":return e.stop()}}),e,this,[[17,27]])}))),function(){return n.apply(this,arguments)})},{key:"onUnpublishResult",value:(t=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.reset(),this.initialize(),e.next=5,this.publish(this.localStream_);case 5:e.next=9;break;case 7:e.prev=7,e.t0=e.catch(0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return t.apply(this,arguments)})},{key:"clearExchangeSDPTimeout",value:function(){-1!==this.exchangeSDPTimeout_&&(clearTimeout(this.exchangeSDPTimeout_),this.exchangeSDPTimeout_=-1)}},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"handleError",value:function(e){e.getCode()===Cl.ICE_TRANSPORT_ERROR&&(this.isFirstConnection_&&(this.isFirstConnection_=!1,Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:vu,error:e})),this.isReconnecting_||this.startReconnection())}},{key:"handleConnectionStateChange",value:function(e){e.state===ou&&(this.isFirstConnection_&&(this.isFirstConnection_=!1,Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:vu}),this.addEventInternal(Gm,"uplink-connection is connected")),this.isReconnecting_&&(Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:gu}),this.log_.warn("reconnect() uplink reconnect successfully"),this.addEventInternal(Qm,"uplink-connection reconnect success"),this.stopReconnection()))}},{key:"updateSSRC",value:function(e){var t=this;try{var n=0;lg(e).media.forEach((function(e){if(e.type===Gu){var r=e.ssrcs[0];r&&(t.ssrc_.audio=r.id)}else{var i=e.ssrcs[0];switch(n+=1){case 1:i&&(t.ssrc_.video=i.id);break;case 2:i&&(t.ssrc_.small=i.id)}}}))}catch(mk){}}},{key:"getLocalStreamVideoTrackId",value:function(){if(this.peerConnection_){var e=this.peerConnection_.getSenders().filter((function(e){return e.track&&e.track.kind===Ju}));if(e[0])return e[0].track.id}if(this.localStream_){var t=this.localStream_.getVideoTrack();if(t)return t.id}return""}},{key:"getSSRC",value:function(){return this.ssrc_}}]),b}(Xy),vS=function(e){u(f,e);var t,n,r,o,c,l,h,p=_(f);function f(e){var t;return a(this,f),(t=p.call(this,e)).remoteStreams_=new Map,t.autoSubscribe=e.autoSubscribe,t.trackState_={audio:e.trackState.audio,video:e.trackState.video,auxiliary:e.trackState.auxiliary},t.ssrc_={audio:0,video:0,auxiliary:0},t.subscribeState_={audio:e.autoSubscribe,video:e.autoSubscribe,auxiliary:e.autoSubscribe},t.pendingSubscription_=[],t.pendingStreams_=[],t.subscriptionTimeout_=-1,t.subscriptionRetryCount_=0,t.isSubscriptionPending_=!1,t.sentSubscriptionAfterConnected_=!1,t.isSDPExchanging_=!1,t.installEvents(),t}return s(f,[{key:"isMainStreamSubscribed",get:function(){return(this.subscribeState_.audio||this.subscribeState_.video)&&(this.trackState_.audio||this.trackState_.video)}},{key:"isAuxStreamSubscribed",get:function(){return this.subscribeState_.auxiliary&&this.trackState_.auxiliary}},{key:"initialize",value:function(){v(d(f.prototype),"initialize",this).call(this),this.peerConnection_.ontrack=this.onTrack.bind(this)}},{key:"close",value:function(){var e=this;v(d(f.prototype),"close",this).call(this),this.emitConnectionStateChangedEvent(ru),-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1),this.remoteStreams_.forEach((function(t){var n=t;n.setConnection(null),n.getIsStreamAddedEventEmitted()&&e.emitter_.emit(zg,{stream:n})})),this.remoteStreams_.clear(),this.uninstallEvents()}},{key:"installEvents",value:function(){var e=this;ih.on(Rh,this.onRemoteStreamUpdate,this),this.signalChannel_.on($f.SUBSCRIBE_CHANGE_RESULT,this.onSubscribeChangeResult,this),this.signalChannel_.on($f.UNSUBSCRIBE_RESULT,this.onUnsubscribeResult,this),this.emitter_.on(Kg,(function(t){t.getCode()===Cl.ICE_TRANSPORT_ERROR&&(e.isFirstConnection_&&(e.isFirstConnection_=!1,Pl.logFailedEvent({userId:e.client_.getUserId(),eventType:yu,error:t})),e.isReconnecting_||e.startReconnection())})),this.emitter_.on(Qg,(function(t){t.state===ou&&e.isFirstConnection_&&(e.isFirstConnection_=!1,Pl.logSuccessEvent({userId:e.client_.getUserId(),eventType:yu}),e.addEventInternal(Jm,"downlink-connection is connected"))}))}},{key:"uninstallEvents",value:function(){ih.removeListener(Rh,this.onRemoteStreamUpdate,this),this.signalChannel_.removeListener($f.SUBSCRIBE_CHANGE_RESULT,this.onSubscribeChangeResult,this),this.signalChannel_.removeListener($f.UNSUBSCRIBE_RESULT,this.onUnsubscribeResult,this)}},{key:"onRemoteStreamUpdate",value:function(e){if(this.hitTest(e.tinyId)&&e.client===this.client_){this.updateTrackState(e.action,e.kind);var t=e.kind===zu?tu:eu,n=this.remoteStreams_.get(t);if(!n)return;e.action===rd?this.handleRemoteAddTrack(e.kind,n):this.handleRemoteRemoveTrack(e.kind,n)}}},{key:"handleRemoteAddTrack",value:function(e,t){this.log_.info("remote add ".concat(e," track")),e===Gu?t.updateAudioPlayingState(this.subscribeState_.audio):t.updateVideoPlayingState(e===zu?this.subscribeState_.auxiliary:this.subscribeState_.video),t.getIsStreamAddedEventEmitted()?this.emitter_.emit(Wg,{stream:t}):(this.emitter_.emit(Jg,{stream:t}),this.currentState_===ou&&t.emitConnectionStateChanged({prevState:ru,state:ou}))}},{key:"handleRemoteRemoveTrack",value:function(e,t){t.getIsStreamAddedEventEmitted()&&(this.log_.info("remote remove ".concat(e," track")),e!==zu&&(this.trackState_.audio||this.trackState_.video)||t.isInSubscriptionCycle()?(e===Gu?t.updateAudioPlayingState(!1):t.updateVideoPlayingState(!1),this.emitter_.emit(Wg,{stream:t})):(this.log_.info("remote stream ".concat(t.getType()," removed")),this.currentState_===ou&&t.emitConnectionStateChanged({prevState:ou,state:ru}),this.emitter_.emit(zg,{stream:t})))}},{key:"updateTrackState",value:function(e,t){var n=e===rd;switch(t){case Gu:this.trackState_.audio=n;break;case Ju:this.trackState_.video=n;break;case zu:this.trackState_.auxiliary=n}this.log_.info("trackState updated: ".concat(JSON.stringify(this.trackState_)))}},{key:"onTrack",value:function(e){var t=e.streams[0],n=e.track;if(this.log_.info("ontrack() kind: ".concat(n.kind," id: ").concat(n.id," streamId: ").concat(t.id)),"unified-plan"===this.sdpSemantics_){var r=function(e){var t=dg.parse(e),n={audio:[],video:[]};return t.media.forEach((function(e){if(e.ssrcs){var t=e.ssrcs[0].id>>16&255;if(e.type===Gu)n.audio.push(eu);else if(e.type==Ju){var r=t===nu?eu:tu;n.video.push(r)}}})),n}(this.peerConnection_.remoteDescription.sdp);if(n.kind===Gu){if(0===r.audio.length||t.id!==eu)return void this.log_.debug("skip this invalid audio track")}else if(-1===r.video.indexOf(t.id))return void this.log_.debug("skip this invalid video track: ".concat(n.id,"  msid: ").concat(t.id))}Pl.logEvent({eventType:"ontrack",kind:n.kind});var i=!1,a=this.remoteStreams_.get(t.id),o=t.id===eu?"main":"auxiliary";if(Ul(a)&&((a=new sS({type:o,userId:this.userId_,client:this.client_})).setConnection(this),this.remoteStreams_.set(t.id,a),i=!0),a.setMediaStream(t),n.kind===Gu?a.updateAudioPlayingState(this.subscribeState_.audio):"main"===o?a.updateVideoPlayingState(this.subscribeState_.video):a.updateVideoPlayingState(this.subscribeState_.auxiliary),("auxiliary"!==o||this.trackState_.auxiliary)&&("main"!==o||this.trackState_.audio||this.trackState_.video)){var s=this.client_.getSubscriptionManager();s&&s.hasAutoRecoveryFlag(this.userId_,o)||(i?this.emitter_.emit(Jg,{stream:a}):this.emitter_.emit(Wg,{stream:a}))}}},{key:"addRRTRLine",value:function(e){var t=e.split("\r\n"),n=new Map;t.forEach((function(e,r){/^a=rtcp-fb:/.test(e)&&t[r+1]&&!/^a=rtcp-fb:/.test(t[r+1])&&n.set(r+1,e.match(/^a=rtcp-fb:\d+/)[0]+" rrtr")}));for(var r=y(n),i=0;i<r.length;i++){var a=g(r[i],2),o=a[0],s=a[1];t.splice(o+i,0,s)}return t.join("\r\n")}},{key:"addSPSDescription",value:function(e){var t=lg(e);return t.media.forEach((function(e){e.type===Ju&&e.fmtp.forEach((function(e){e.config+=";sps-pps-idr-in-keyframe=1"}))})),hg(t)}},{key:"removeSDESDescription",value:function(e){var t=["urn:ietf:params:rtp-hdrext:sdes:mid","urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id","urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id"],n=lg(e);return n.media.forEach((function(e){e.ext=e.ext.filter((function(e){return!t.includes(e.uri)}))})),hg(n)}},{key:"isSubscriptionStateNotChanged",value:function(e,t){return"main"===e.getType()?!Ul(t.audio)&&!Ul(t.video)&&t.audio===this.subscribeState_.audio&&t.video===this.subscribeState_.video:"auxiliary"===e.getType()?!Ul(t.video)&&this.subscribeState_.auxiliary===t.video:void 0}},{key:"subscribe",value:(h=i(regeneratorRuntime.mark((function e(t,n){var r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.emitEvent,i=void 0===r||r,a=t.getType(),!this.isSubscriptionStateNotChanged(t,n)){e.next=13;break}if(!this.peerConnection_){e.next=8;break}return e.next=6,this.waitForPeerConnectionConnected();case 6:e.next=11;break;case 8:return this.initialize(),e.next=11,this.connect();case 11:return i&&this.emitter_.emit(qg,{stream:t,result:!0}),e.abrupt("return",t);case 13:if("main"===a?(Ul(n.audio)||(this.subscribeState_.audio=n.audio),Ul(n.video)||(this.subscribeState_.video=n.video),this.addEventInternal(this.subscribeState_.audio?Cm:Pm,this.subscribeState_.audio?"subscribe audio":"unsubscribe audio"),this.addEventInternal(this.subscribeState_.video?Cm:Pm,this.subscribeState_.video?"subscribe video":"unsubscribe video")):Ul(n.video)||(this.subscribeState_.auxiliary=n.video),this.log_.info("subscribe ".concat(a," stream with options ").concat(JSON.stringify(n)," current state: ").concat(JSON.stringify(this.subscribeState_))),!this.peerConnection_&&!this.isSDPExchanging_){e.next=23;break}return o=Nu,this.isMainStreamSubscribed||this.isAuxStreamSubscribed||(o=Du),e.next=20,this.sendSubscription(t,o);case 20:"main"===a?(t.updateAudioPlayingState(this.subscribeState_.audio),t.updateVideoPlayingState(this.subscribeState_.video)):t.updateVideoPlayingState(this.subscribeState_.auxiliary),e.next=26;break;case 23:return this.initialize(),e.next=26,this.connect();case 26:return i&&this.emitter_.emit(qg,{stream:t,result:!0}),e.abrupt("return",t);case 28:case"end":return e.stop()}}),e,this)}))),function(e,t){return h.apply(this,arguments)})},{key:"unsubscribe",value:(l=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("main"!==(n=t.getType())){e.next=9;break}if(this.isMainStreamSubscribed){e.next=5;break}return this.log_.info("main stream already unsubscribed"),e.abrupt("return",t);case 5:this.subscribeState_.audio=!1,this.subscribeState_.video=!1,e.next=13;break;case 9:if(this.isAuxStreamSubscribed){e.next=12;break}return this.log_.info("auxiliary stream already unsubscribed"),e.abrupt("return",t);case 12:this.subscribeState_.auxiliary=!1;case 13:return r=Du,("main"===n&&this.isAuxStreamSubscribed||"auxiliary"===n&&this.isMainStreamSubscribed)&&(r=Nu),this.log_.info("unsubscribe ".concat(n," stream with ").concat(JSON.stringify(this.subscribeState_))),e.next=18,this.sendSubscription(t,r);case 18:return t.updateVideoPlayingState(!1),t.updateAudioPlayingState(!1),r===Du&&((i=t.getMediaStream())&&i.getTracks().forEach((function(e){return i.removeTrack(e)})),this.closePeerConnection(),this.emitConnectionStateChangedEvent(ru)),this.addEventInternal(Pm,"unsubscribe audio"),this.addEventInternal(Am,"unsubscribe video"),e.abrupt("return",t);case 24:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"sendSubscription",value:function(e,t){var n=this;return new Promise((function(r,i){var a={srctinyid:n.tinyId_,userid:n.userId_,audio:n.subscribeState_.audio,bigVideo:n.subscribeState_.video,auxVideo:n.subscribeState_.auxiliary};n.pendingSubscription_.length>0?n.log_.debug("queue the subscription for later handling"):n.doSendSubscription(a,e,t),n.pendingSubscription_.push({stream:e,type:t,data:a,callback:function(e){var a=e.code,o=e.message;if(0===a)r();else{var s=new Al({code:a,message:Zv({key:__,data:{type:t,message:o}})});n.log_.error(s),i(s)}}}),e.setInSubscriptionCycle(!0)}))}},{key:"doSendSubscription",value:function(e,t,n){var r=this;if(!this.peerConnection_||"connected"!==this.peerConnection_.connectionState&&"completed"!==this.peerConnection_.connectionState)return this.log_.debug("try to send subscription [".concat(n,"] when peeConnection connected")),void(this.sentSubscriptionAfterConnected_=!1);t&&this.pendingStreams_.push(t),this.log_.debug("doSendSubscription() send SUBSCRIBE command with data: ".concat(JSON.stringify(e))),n===Nu?this.signalChannel_.send(dm,e):n===Du&&this.signalChannel_.send(um,e),this.isSubscriptionPending_=!0,this.subscriptionTimeout_=setTimeout((function(){if(r.isSubscriptionPending_)if(r.log_.debug("subscription timeout"),r.subscriptionRetryCount_+=1,r.subscriptionRetryCount_<=3){r.log_.debug("resend subscription");var e=r.pendingSubscription_[0].data;r.doSendSubscription(e,t,n)}else r.log_.error(Xv.SUBSCRIPTION_TIMEOUT),r.pendingSubscription_.shift(),r.pendingStreams_.shift(),r.isSubscriptionPending_=!1,r.subscriptionRetryCount_=0,r.emitter_.emit(Kg,new Al({code:Cl.SUBSCRIPTION_TIMEOUT,message:Zv({key:v_})}))}),5e3)}},{key:"onSubscribeChangeResult",value:function(e){var t=e.data.content,n=t.srctinyid;if(this.hitTest(n)){var r=this.pendingSubscription_[0];r&&r.type===Nu&&r.callback({code:t.errCode,message:t.errMsg}),this.sendNextSubscription()}}},{key:"onUnsubscribeResult",value:function(e){var t=e.data.content,n=t.srctinyid;if(this.hitTest(n)){var r=this.pendingSubscription_[0];r&&r.type===Du&&r.callback({code:t.errCode,message:t.errMsg}),this.sendNextSubscription()}}},{key:"connect",value:(c=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.exchangeSDP();case 3:return e.next=5,this.waitForPeerConnectionConnected();case 5:e.next=11;break;case 7:throw e.prev=7,e.t0=e.catch(0),this.closePeerConnection(!0),e.t0;case 11:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return c.apply(this,arguments)})},{key:"exchangeSDP",value:(o=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.isSDPExchanging_=!0,e.next=4,this.createOffer();case 4:return this.log_.info("createOffer success, sending offer to remote server"),t=this.peerConnection_.localDescription,n=t.type,r=t.sdp,i={type:n,sdp:r,userid:this.userId_,srctinyid:this.tinyId_,audio:this.subscribeState_.audio,bigVideo:this.subscribeState_.video,auxVideo:this.subscribeState_.auxiliary},ih.emit(Jh,{client:this.client_,connection:this,userId:this.userId_,tinyId:this.tinyId_,role:Yc,subscribeState:this.subscribeState_,trackState:this.trackState_}),e.next=10,this.signalChannel_.sendWaitForResponse({command:cm,commandDesc:"exchange sdp",data:i,responseCommand:$f.SUBSCRIBE_RESULT});case 10:return a=e.sent,e.next=13,this.onSubscribeResult(a);case 13:this.isSDPExchanging_=!1,e.next=20;break;case 16:throw e.prev=16,e.t0=e.catch(0),this.isSDPExchanging_=!1,e.t0;case 20:case"end":return e.stop()}}),e,this,[[0,16]])}))),function(){return o.apply(this,arguments)})},{key:"createOffer",value:(r=i(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={voiceActivityDetection:!1},"RTCPeerConnection"in window&&"addTransceiver"in window.RTCPeerConnection.prototype&&"unified-plan"===this.sdpSemantics_?(this.peerConnection_.addTransceiver(Gu,{direction:"recvonly"}),this.peerConnection_.addTransceiver(Ju,{direction:"recvonly"}),this.peerConnection_.addTransceiver(Ju,{direction:"recvonly"})):(t.offerToReceiveAudio=!0,t.offerToReceiveVideo=!0),e.next=4,this.peerConnection_.createOffer(t);case 4:return n=e.sent,e.next=7,Eg();case 7:return e.sent.isH264DecodeSupported||(this.log_.warn("remove h264 desc from sdp"),n.sdp=fg(n.sdp)),n.sdp=this.addRRTRLine(n.sdp),n.sdp=this.addSPSDescription(n.sdp),n.sdp=pg(n.sdp),"unified-plan"===this.sdpSemantics_&&(n.sdp=this.removeSDESDescription(n.sdp)),e.next=16,this.peerConnection_.setLocalDescription(n);case 16:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"onSubscribeResult",value:(n=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.data.content||77393!==t.data.content.errCode){e.next=3;break}throw this.log_.error(Xv.NOT_SUPPORTED_H264DECODE),new Al({code:Cl.NOT_SUPPORTED_H264,message:Zv({key:zv})});case 3:if(n=t.data.content,r=n.type,i=n.sdp,a=n.errCode,o=n.errMsg,e.prev=4,0===a){e.next=7;break}throw new Al({code:a,message:Zv({key:S_,data:{errMsg:o}})});case 7:return this.log_.debug("accept remote answer: "+i),e.next=10,this.peerConnection_.setRemoteDescription({type:r,sdp:i});case 10:this.updateSSRC(i),e.next=17;break;case 13:throw e.prev=13,e.t0=e.catch(4),this.log_.error(e.t0),e.t0;case 17:case"end":return e.stop()}}),e,this,[[4,13]])}))),function(e){return n.apply(this,arguments)})},{key:"updateSSRC",value:function(e){var t=this;try{lg(e).media.forEach((function(e){if(e.type===Gu){var n=e.ssrcs.find((function(e){return e.value.includes(eu)}));n&&(t.ssrc_.audio=n.id)}else{var r=e.ssrcs.find((function(e){return e.value.includes(eu)})),i=e.ssrcs.find((function(e){return e.value.includes(tu)}));r&&(t.ssrc_.video=r.id),i&&(t.ssrc_.auxiliary=i.id)}}))}catch(mk){}}},{key:"sendNextSubscription",value:function(){void 0!==this.pendingSubscription_.shift()&&(this.subscriptionRetryCount_=0,this.isSubscriptionPending_=!1,-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1));var e=this.pendingStreams_.shift();if(e&&(this.log_.debug("mark ".concat(e.getType()," stream exit subscription cycle")),e.setInSubscriptionCycle(!1)),this.pendingSubscription_.length>0){var t=this.pendingSubscription_[0];this.log_.info("schedule a pending subscription"),this.doSendSubscription(t.data,t.stream,t.type)}}},{key:"setRemoteStream",value:function(e,t){this.remoteStreams_.set(e,t)}},{key:"getSubscribeState",value:function(){return this.subscribeState_}},{key:"getTrackState",value:function(){return this.trackState_}},{key:"getSSRC",value:function(){return this.ssrc_}},{key:"getMainStream",value:function(){return this.remoteStreams_.get(eu)}},{key:"getAuxStream",value:function(){return this.remoteStreams_.get(tu)}},{key:"getMainStreamVideoTrackId",value:function(){var e=this.getMainStream();if(e){var t=e.getVideoTrack();if(t)return t.id}return""}},{key:"getAuxStreamVideoTrackId",value:function(){var e=this.getAuxStream();if(e){var t=e.getVideoTrack();if(t)return t.id}return""}},{key:"reconnect",value:(t=i(regeneratorRuntime.mark((function e(){var t,n,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(-1===this.reconnectionTimer_){e.next=3;break}return this.log_.warn("reconnect() downlink is reconnecting, ignore current reconnection"),e.abrupt("return");case 3:if(!(this.reconnectionCount_>=30)){e.next=12;break}return this.log_.warn("SDK has tried reconnect downlink [".concat(this.userId_,"] for ").concat(30," times, but all failed, please check your network")),this.stopReconnection(),t=new Al({code:Cl.DOWNLINK_RECONNECTION_FAILED,message:Zv({key:y_})}),Pl.logFailedEvent({userId:this.client_.getUserId(),eventType:Su,error:t}),this.addEventInternal(Zm,"downlink-connection reconnect fail"),this.emitConnectionStateChangedEvent(ru),this.emitter_.emit(Kg,t),e.abrupt("return");case 12:if(this.signalChannel_.getCurrentState()===Kf){e.next=16;break}return this.log_.warn("reconnect() signal channel is not connected, suspend reconnection until signal is connected"),this.signalChannel_.once(Gf,this.reconnect,this),e.abrupt("return");case 16:if(this.reconnectionCount_++,this.log_.warn("reconnect() try to reconnect downlink [".concat(this.reconnectionCount_,"/").concat(30,"]")),n=Ml(this.reconnectionCount_),this.reconnectionTimer_=setTimeout((function(){r.log_.warn("reconnect() downlink [".concat(r.userId_,"] reconnect timeout(").concat(n/1e3,"s), try again")),r.clearReconnectionTimer(),r.reconnect()}),n),!(this.isSDPExchanging_||this.peerConnection_&&"connecting"===this.peerConnection_.connectionState)){e.next=22;break}return e.abrupt("return");case 22:return e.prev=22,this.closePeerConnection(),this.sentSubscriptionAfterConnected_=!1,this.initialize(),e.next=28,this.connect();case 28:this.stopReconnection(),this.log_.warn("reconnect() downlink reconnect successfully"),Pl.logSuccessEvent({userId:this.client_.getUserId(),eventType:Su}),this.addEventInternal(Ym,"downlink-connection reconnect success"),this.recoverSubscription(),e.next=37;break;case 35:e.prev=35,e.t0=e.catch(22);case 37:case"end":return e.stop()}}),e,this,[[22,35]])}))),function(){return t.apply(this,arguments)})},{key:"recoverSubscription",value:function(){var e=this,t=this.client_.getSubscriptionManager();t&&y(this.remoteStreams_.values()).forEach((function(n){t.hasAutoRecoveryFlag(e.userId_,n.getType())&&t.recover(n)}))}},{key:"getIsReconnecting",value:function(){return this.isReconnecting_}},{key:"getSubscribedMainStream",value:function(){var e=null;return this.isMainStreamSubscribed&&(e=this.remoteStreams_.get(eu)),e}},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"startReconnection",value:function(){var e=this.client_.getSubscriptionManager();if(e){var t,n=b(this.remoteStreams_.values());try{for(n.s();!(t=n.n()).done;){var r=t.value,i=r.getType();("main"===i&&(this.trackState_.audio||this.trackState_.video)||"auxiliary"===i&&this.trackState_.auxiliary)&&e.setAutoRecoveryFlag(this.userId_,r.getType())}}catch(a){n.e(a)}finally{n.f()}}v(d(f.prototype),"startReconnection",this).call(this)}},{key:"getCurrentState",value:function(){return this.currentState_}}]),f}(Xy),gS=function(e){return function(t,n,r,i){ne(n);var a=se(t),o=F(a),s=ot(a),c=e?s-1:0,u=e?-1:1;if(r<2)for(;;){if(c in o){i=o[c],c+=u;break}if(c+=u,e?c<0:s<=c)throw TypeError("Reduce of empty array with no initial value")}for(;e?c>=0:s>c;c+=u)c in o&&(i=n(i,o[c],c,a));return i}},yS={left:gS(!1),right:gS(!0)}.left,SS=Ki("reduce");Tt({target:"Array",proto:!0,forced:!SS||!Qh&&$>79&&$<83},{reduce:function(e){return yS(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}});var kS=function(){function e(){a(this,e),this.startTime=0,this.endTime=0,this.start()}return s(e,[{key:"start",value:function(){0===this.startTime&&(this.startTime=zl())}},{key:"stop",value:function(){0===this.endTime&&(this.endTime=zl())}},{key:"getDuration",value:function(){return 0===this.endTime?zl()-this.startTime:this.endTime-this.startTime}}]),e}(),bS=function(){function e(t){a(this,e),this.client_=t.client,this.intervalId_=-1,this.statsCalculator_=t.stats,this.prevStats_=null,this.renderFreezeMap_=new Map,this.remoteStreamMap_=new Map,this.dataFreezeMap_=new Map,this.monitorFreezeData_=new Map}var t,n,r;return s(e,[{key:"installEvents",value:function(){ih.on(Fh,this.handlePlayVideoStart,this),ih.on(Oh,this.onVideoTrackMuted,this),ih.on(Mh,this.onVideoTrackUnmuted,this),ih.on(Ph,this.handleStreamStopped,this),ih.on(Ch,this.handleStreamStopped,this),ih.on(bh,this.handleVideoPlaying,this)}},{key:"uninstallEvents",value:function(){ih.off(Fh,this.handlePlayVideoStart,this),ih.off(Oh,this.onVideoTrackMuted,this),ih.off(Mh,this.onVideoTrackUnmuted,this),ih.off(Ph,this.handleStreamStopped,this),ih.off(Ch,this.handleStreamStopped,this),ih.off(bh,this.handleVideoPlaying,this)}},{key:"start",value:function(){var e=this;-1===this.intervalId_&&(this.installEvents(),this.intervalId_=vg.setInterval(i(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.detectFPS();case 3:t.next=7;break;case 5:t.prev=5,t.t0=t.catch(0);case 7:case"end":return t.stop()}}),t,null,[[0,5]])}))),1e3))}},{key:"stop",value:function(){-1!==this.intervalId_&&(this.uninstallEvents(),vg.clearInterval(this.intervalId_),this.intervalId_=-1,this.renderFreezeMap_.clear(),this.dataFreezeMap_.clear(),this.remoteStreamMap_.clear())}},{key:"onVideoTrackMuted",value:function(e){var t=e.stream;if(t.getClient()===this.client_&&t.isRemote()){var n=t.userId_,r=t.type_,i="".concat(n,"_").concat(r),a=this.dataFreezeMap_.get(i),o=new kS;a?a.durationItemList.push(o):this.dataFreezeMap_.set(i,{userId:n,type:r,durationItemList:[o],isFreezing:function(){var e=this.durationItemList[this.durationItemList.length-1];return e&&0===e.endTime}})}}},{key:"onVideoTrackUnmuted",value:function(e){var t=e.stream;if(t.getClient()===this.client_&&t.isRemote()){var n=t.userId_,r=t.type_,i="".concat(n,"_").concat(r);this.stopDataFreeze({key:i,userId:n,type:r})}}},{key:"handleStreamStopped",value:function(e){var t=e.client,n=e.stream;if(t===this.client_){var r=n.getUserId(),i=n.getType(),a="".concat(r,"_").concat(i);this.stopDataFreeze({key:a,userId:r,type:i})}}},{key:"stopDataFreeze",value:function(e){var t=e.key,n=e.userId,r=e.type,i=this.dataFreezeMap_.get(t);if(i&&i.isFreezing()){var a=i.durationItemList[i.durationItemList.length-1];a.stop();var o=a.getDuration();o>500?(Pl.logEvent({eventType:"videoFrozenCount",delta:o}),this.monitorFreezeData_.set(t,{userId:n,type:r,duration:o})):i.durationItemList.pop()}}},{key:"getTotalDuration",value:function(e){return e.reduce((function(e,t){var n=t.getDuration();return e+Math.min(n,5e3)}),0)}},{key:"getStats",value:(r=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d,l;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.client_.getConnections(),n={},r=b(t),e.prev=3,r.s();case 5:if((i=r.n()).done){e.next=20;break}if(a=g(i.value,2),o=a[0],(s=a[1]).getPeerConnection()){e.next=9;break}return e.abrupt("continue",18);case 9:return c=s.getSubscribeState(),u=s.getTrackState(),e.next=13,this.statsCalculator_.getReceiverStats(s);case 13:d=e.sent,(l={userId:d.userId,tinyId:o,hasVideo:u.video&&c.video,hasAuxiliary:u.auxiliary&&c.auxiliary,video:{framesDecoded:0},auxiliary:{framesDecoded:0}}).hasVideo&&(l.video.framesDecoded=d.video.framesDecoded),l.hasAuxiliary&&(l.auxiliary.framesDecoded=d.auxiliary.framesDecoded),n[d.userId]=l;case 18:e.next=5;break;case 20:e.next=25;break;case 22:e.prev=22,e.t0=e.catch(3),r.e(e.t0);case 25:return e.prev=25,r.f(),e.finish(25);case 28:return e.abrupt("return",n);case 29:case"end":return e.stop()}}),e,this,[[3,22,25,28]])}))),function(){return r.apply(this,arguments)})},{key:"detectFPS",value:(n=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getStats();case 2:if(t=e.sent,this.prevStats_){e.next=6;break}return this.prevStats_=t,e.abrupt("return");case 6:e.t0=regeneratorRuntime.keys(t);case 7:if((e.t1=e.t0()).done){e.next=17;break}if(n=e.t1.value,this.prevStats_[n]){e.next=11;break}return e.abrupt("continue",7);case 11:r=t[n].tinyId,i=this.client_.getMutedStates(),t[n].hasVideo&&this.prevStats_[n].hasVideo&&i.has(r)&&!i.get(r).videoMuted&&(a=t[n].video.framesDecoded-this.prevStats_[n].video.framesDecoded,this.handleRenderFreeze({userId:n,type:"main",fps:a})),t[n].hasAuxiliary&&this.prevStats_[n].hasAuxiliary&&(o=t[n].auxiliary.framesDecoded-this.prevStats_[n].auxiliary.framesDecoded,this.handleRenderFreeze({userId:n,type:"auxiliary",fps:o})),e.next=7;break;case 17:this.prevStats_=t;case 18:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"handleRenderFreeze",value:(t=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o,s,c,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.userId,r=t.fps,i=t.type,a="".concat(n,"_").concat(i),o=this.renderFreezeMap_.get(a),r<=2?(s=zl(),o&&!o.isFreeze&&(o.freezeTimeline.push({startTime:s,endTime:void 0}),o.isFreeze=!0),o||this.renderFreezeMap_.set(a,{userId:n,type:i,isFreeze:!0,freezeTimeline:[{startTime:s,endTime:void 0}],renderFreezeTotal:0})):o&&o.isFreeze&&(o.isFreeze=!1,(c=o.freezeTimeline.pop()).endTime=zl(),u=c.endTime-c.startTime,o.freezeTimeline.push(c),o.renderFreezeTotal+=Math.min(5e3,u));case 4:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"handlePlayVideoStart",value:function(e){var t=e.stream;if(t.getClient()===this.client_&&t.isRemote()&&t.hasVideo()){var n="".concat(t.getUserId(),"_").concat(t.getType());if(this.remoteStreamMap_.has(n))this.remoteStreamMap_.get(n).remoteStream=t;else this.remoteStreamMap_.set(n,{isPlayingFired:!1,remoteStream:t})}}},{key:"handleVideoPlaying",value:function(e){var t=e.stream;if(t.isRemote()&&t.getClient()===this.client_){var n="".concat(t.getUserId(),"_").concat(t.getType());if(this.remoteStreamMap_.has(n))this.remoteStreamMap_.get(n).isPlayingFired=!0}}},{key:"getDataFreezeDuration",value:function(e){var t={dataFreeze:0,count:0},n=this.dataFreezeMap_.get(e);if(n){if(n.isFreezing()){var r=n.durationItemList[n.durationItemList.length-1];r.stop(),r.getDuration()<500&&n.durationItemList.pop()}t.dataFreeze=this.getTotalDuration(n.durationItemList),t.count=n.durationItemList.length}return t}},{key:"getRenderFreezeDuration",value:function(e){var t=this.renderFreezeMap_.get(e),n=0,r=0;if(t)if(t.isFreeze){var i=zl()-t.freezeTimeline[t.freezeTimeline.length-1].startTime;n=t.renderFreezeTotal+Math.min(i,5e3),r=t.freezeTimeline.length}else n=t.renderFreezeTotal;return{renderFreeze:n,count:r}}},{key:"getMonitorFreeze",value:function(){return this.monitorFreezeData_}},{key:"isBlackStream",value:function(e){return!!this.remoteStreamMap_.has(e)&&!this.remoteStreamMap_.get(e).isPlayingFired}},{key:"resetMonitor",value:function(){this.monitorFreezeData_.clear()}}]),e}(),IS=function e(t){a(this,e),this.userId=t.userId,this.tinyId=t.tinyId,this.role=t.role===Yc?"anchor":"audience"},RS=T((function(e){!function(t){function n(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function r(e,t,r,i,a,o){return n((s=n(n(t,e),n(i,o)))<<(c=a)|s>>>32-c,r);var s,c}function i(e,t,n,i,a,o,s){return r(t&n|~t&i,e,t,a,o,s)}function a(e,t,n,i,a,o,s){return r(t&i|n&~i,e,t,a,o,s)}function o(e,t,n,i,a,o,s){return r(t^n^i,e,t,a,o,s)}function s(e,t,n,i,a,o,s){return r(n^(t|~i),e,t,a,o,s)}function c(e,t){var r,c,u,d,l;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var h=1732584193,p=-271733879,f=-1732584194,m=271733878;for(r=0;r<e.length;r+=16)c=h,u=p,d=f,l=m,h=i(h,p,f,m,e[r],7,-680876936),m=i(m,h,p,f,e[r+1],12,-389564586),f=i(f,m,h,p,e[r+2],17,606105819),p=i(p,f,m,h,e[r+3],22,-1044525330),h=i(h,p,f,m,e[r+4],7,-176418897),m=i(m,h,p,f,e[r+5],12,1200080426),f=i(f,m,h,p,e[r+6],17,-1473231341),p=i(p,f,m,h,e[r+7],22,-45705983),h=i(h,p,f,m,e[r+8],7,1770035416),m=i(m,h,p,f,e[r+9],12,-1958414417),f=i(f,m,h,p,e[r+10],17,-42063),p=i(p,f,m,h,e[r+11],22,-1990404162),h=i(h,p,f,m,e[r+12],7,1804603682),m=i(m,h,p,f,e[r+13],12,-40341101),f=i(f,m,h,p,e[r+14],17,-1502002290),h=a(h,p=i(p,f,m,h,e[r+15],22,1236535329),f,m,e[r+1],5,-165796510),m=a(m,h,p,f,e[r+6],9,-1069501632),f=a(f,m,h,p,e[r+11],14,643717713),p=a(p,f,m,h,e[r],20,-373897302),h=a(h,p,f,m,e[r+5],5,-701558691),m=a(m,h,p,f,e[r+10],9,38016083),f=a(f,m,h,p,e[r+15],14,-660478335),p=a(p,f,m,h,e[r+4],20,-405537848),h=a(h,p,f,m,e[r+9],5,568446438),m=a(m,h,p,f,e[r+14],9,-1019803690),f=a(f,m,h,p,e[r+3],14,-187363961),p=a(p,f,m,h,e[r+8],20,1163531501),h=a(h,p,f,m,e[r+13],5,-1444681467),m=a(m,h,p,f,e[r+2],9,-51403784),f=a(f,m,h,p,e[r+7],14,1735328473),h=o(h,p=a(p,f,m,h,e[r+12],20,-1926607734),f,m,e[r+5],4,-378558),m=o(m,h,p,f,e[r+8],11,-2022574463),f=o(f,m,h,p,e[r+11],16,1839030562),p=o(p,f,m,h,e[r+14],23,-35309556),h=o(h,p,f,m,e[r+1],4,-1530992060),m=o(m,h,p,f,e[r+4],11,1272893353),f=o(f,m,h,p,e[r+7],16,-155497632),p=o(p,f,m,h,e[r+10],23,-1094730640),h=o(h,p,f,m,e[r+13],4,681279174),m=o(m,h,p,f,e[r],11,-358537222),f=o(f,m,h,p,e[r+3],16,-722521979),p=o(p,f,m,h,e[r+6],23,76029189),h=o(h,p,f,m,e[r+9],4,-640364487),m=o(m,h,p,f,e[r+12],11,-421815835),f=o(f,m,h,p,e[r+15],16,530742520),h=s(h,p=o(p,f,m,h,e[r+2],23,-995338651),f,m,e[r],6,-198630844),m=s(m,h,p,f,e[r+7],10,1126891415),f=s(f,m,h,p,e[r+14],15,-1416354905),p=s(p,f,m,h,e[r+5],21,-57434055),h=s(h,p,f,m,e[r+12],6,1700485571),m=s(m,h,p,f,e[r+3],10,-1894986606),f=s(f,m,h,p,e[r+10],15,-1051523),p=s(p,f,m,h,e[r+1],21,-2054922799),h=s(h,p,f,m,e[r+8],6,1873313359),m=s(m,h,p,f,e[r+15],10,-30611744),f=s(f,m,h,p,e[r+6],15,-1560198380),p=s(p,f,m,h,e[r+13],21,1309151649),h=s(h,p,f,m,e[r+4],6,-145523070),m=s(m,h,p,f,e[r+11],10,-1120210379),f=s(f,m,h,p,e[r+2],15,718787259),p=s(p,f,m,h,e[r+9],21,-343485551),h=n(h,c),p=n(p,u),f=n(f,d),m=n(m,l);return[h,p,f,m]}function u(e){var t,n="",r=32*e.length;for(t=0;t<r;t+=8)n+=String.fromCharCode(e[t>>5]>>>t%32&255);return n}function d(e){var t,n=[];for(n[(e.length>>2)-1]=void 0,t=0;t<n.length;t+=1)n[t]=0;var r=8*e.length;for(t=0;t<r;t+=8)n[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return n}function l(e){var t,n,r="";for(n=0;n<e.length;n+=1)t=e.charCodeAt(n),r+="0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t);return r}function h(e){return unescape(encodeURIComponent(e))}function p(e){return function(e){return u(c(d(e),8*e.length))}(h(e))}function f(e,t){return function(e,t){var n,r,i=d(e),a=[],o=[];for(a[15]=o[15]=void 0,i.length>16&&(i=c(i,8*e.length)),n=0;n<16;n+=1)a[n]=909522486^i[n],o[n]=1549556828^i[n];return r=c(a.concat(d(t)),512+8*t.length),u(c(o.concat(r),640))}(h(e),h(t))}function m(e,t,n){return t?n?f(t,e):l(f(t,e)):n?p(e):l(p(e))}e.exports?e.exports=m:t.md5=m}(R)})),TS=function(){function e(t){a(this,e),this.client_=t.client,this.signalChannel_=t.signalChannel,this.log_=pf.createLogger({id:"mix|"+this.client_.getUserId(),userId:t.client.getUserId(),sdkAppId:t.client.getSDKAppId()}),this.isMixing_=!1,this.config_=null,this.data_=null,this.remoteStreamMap_=new Map,this.installEvents()}var t,n,r,o;return s(e,[{key:"isPresetLayoutMode",get:function(){return this.config_&&this.config_.mode===Lu.PRESET_LAYOUT}},{key:"installEvents",value:function(){ih.on(wh,this.onStreamSubscribed,this),ih.on(Ch,this.onStreamUnsubscribed,this),this.client_.on("stream-removed",this.onStreamRemoved,this)}},{key:"uninstallEvents",value:function(){ih.off(wh,this.onStreamSubscribed,this),ih.off(Ch,this.onStreamUnsubscribed,this),this.client_.off("stream-removed",this.onStreamRemoved,this)}},{key:"stop",value:function(){this.uninstallEvents()}},{key:"onStreamSubscribed",value:function(e){var t=e.client,n=e.stream;t===this.client_&&(this.remoteStreamMap_.set(n.getId(),{remoteStream:n,isUsed:!1}),this.isMixing_&&this.hasAvailablePlaceHolder()&&this.startMixTranscode(this.config_))}},{key:"onStreamUnsubscribed",value:function(e){var t=e.client,n=e.stream;t===this.client_&&this.onStreamRemoved({stream:n})}},{key:"onStreamRemoved",value:function(e){var t=e.stream;if(this.remoteStreamMap_.has(t.getId())){var n=this.remoteStreamMap_.get(t.getId()).isUsed;this.remoteStreamMap_.delete(t.getId()),this.isMixing_&&this.isPresetLayoutMode&&n&&this.startMixTranscode(this.config_)}}},{key:"startMixTranscode",value:(o=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,this.resetIsUsedFlag(),this.config_=t,n=this.getInputParam(t,this.remoteStreamMap_),r=this.getOutputParam(t),i=this.getOutputSessionId({config:t,roomId:this.client_.getRoomId(),userId:this.client_.getUserId()}),!this.isMixing_||!this.data_||i===this.data_.outputSessionId){e.next=10;break}return this.log_.info("startMixTranscode() streamId changed, stop mixing before start"),e.next=10,this.doStopMixTranscode();case 10:return e.next=12,this.doStartMixTranscode({outputSessionId:i,inputParam:n,outputParam:r});case 12:e.next=18;break;case 14:throw e.prev=14,e.t0=e.catch(0),this.resetIsUsedFlag(),e.t0;case 18:case"end":return e.stop()}}),e,this,[[0,14]])}))),function(e){return o.apply(this,arguments)})},{key:"doStartMixTranscode",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o,s,c,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.outputSessionId,r=t.inputParam,i=t.outputParam,a={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),socketid:this.signalChannel_.getSocketId(),mcuRequestTime:Date.now(),outputSessionId:n,inputParam:r,outputParam:i},this.data_=a,this.log_.info("startMixTranscode: ".concat(JSON.stringify(a))),this.isMixing_=!0,e.prev=5,e.next=8,this.signalChannel_.sendWaitForResponse({command:mm,data:a,timeout:5e3,responseCommand:$f.START_MIX_TRANSCODE_RES,commandDesc:"startMixTranscode"});case 8:if(o=e.sent,s=o.data.content,c=s.errCode,u=s.errMsg,0===c){e.next=15;break}throw-102083===c&&(u="Please enable relayed-push in https://console.cloud.tencent.com/trtc/ and try later, refer to https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-26-advanced-publish-cdn-stream.html"),this.log_.error("startMixTranscode failed, errCode: ".concat(c," errMsg: ").concat(u)),this.isMixing_=!1,new Al({code:Cl.START_MIX_TRANSCODE_FAILED,message:Zv({key:Ev,data:{message:u},link:{className:"Client",fnName:"startMixTranscode"}})});case 15:e.next=21;break;case 17:throw e.prev=17,e.t0=e.catch(5),this.isMixing_=!1,e.t0;case 21:case"end":return e.stop()}}),e,this,[[5,17]])}))),function(e){return r.apply(this,arguments)})},{key:"stopMixTranscode",value:(n=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isMixing_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Cv})});case 2:return e.next=4,this.doStopMixTranscode();case 4:this.resetIsUsedFlag();case 5:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"doStopMixTranscode",value:(t=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),socketid:this.signalChannel_.getSocketId(),userid:this.client_.getUserId(),mcuRequestTime:Date.now(),outPutSessionId:this.data_.outputSessionId,streamType:this.data_.outputParam.streamType},this.log_.info("stopMixTranscode: ".concat(JSON.stringify(t))),e.next=4,this.signalChannel_.sendWaitForResponse({command:_m,data:t,timeout:5e3,responseCommand:$f.STOP_MIX_TRANSCODE_RES,commandDesc:"stopMixTranscode"});case 4:if(n=e.sent,r=n.data.content,i=r.errCode,a=r.errMsg,0!==i){e.next=10;break}this.isMixing_=!1,e.next=12;break;case 10:throw this.log_.error("stopMixTranscode failed, errCode: ".concat(i," errMsg: ").concat(a)),new Al({code:Cl.STOP_MIX_TRANSCODE_FAILED,message:Zv({key:wv,data:{message:a},link:{className:"Client",fnName:"stopMixTranscode"}})});case 12:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"getOutputSessionId",value:function(e){var t=e.config,n=e.userId,r=e.roomId;return Fl(t.streamId)&&t.streamId.length>0?t.streamId:RS("".concat(r,"_").concat(n,"_main"))}},{key:"getInputParam",value:function(e,t){var n=this,r=e.mixUsers.map((function(e){return{userid:e.userId,roomid:String(e.roomId||n.client_.getRoomId()),width:e.width||0,height:e.height||0,locationX:e.locationX||0,locationY:e.locationY||0,zOrder:e.zOrder,streamType:Ul(e.streamType)||"auxiliary"!==e.streamType?0:1,inputType:e.pureAudio?Mu.IT_PURE_AUDIO:Mu.IT_AUDIO_VIDEO}}));return e.mode===Lu.PRESET_LAYOUT&&(r.forEach((function(e){if(e.userid===Ou.REMOTE){var n=y(t.values()).find((function(e){return!e.isUsed}));n&&(e.userid=n.remoteStream.getUserId(),e.streamType="auxiliary"===n.remoteStream.getType()?1:0,n.isUsed=!0)}})),r=r.filter((function(e){return e.userid!==Ou.REMOTE}))),r}},{key:"getOutputParam",value:function(e){var t=e.streamId||"";return{streamId:t,streamType:t.length>0?1:0,videoWidth:Ul(e.videoWidth)?640:e.videoWidth,videoHeight:Ul(e.videoHeight)?480:e.videoHeight,videoBitrate:e.videoBitrate||0,videoFramerate:e.videoFramerate||15,videoGOP:e.videoGOP||2,audioSampleRate:e.audioSampleRate||48e3,audioBitrate:e.audioBitrate||64,audioChannels:e.audioChannels||1,backgroundColor:e.backgroundColor||0,backgroundImage:e.backgroundImage||"",extraInfo:"",VCodec:2,ACodec:0}}},{key:"hasAvailablePlaceHolder",value:function(){return!!this.isPresetLayoutMode&&this.data_.inputParam.length!==this.config_.mixUsers.length}},{key:"resetIsUsedFlag",value:function(){this.remoteStreamMap_.forEach((function(e){return e.isUsed=!1}))}}]),e}(),ES=function(){function e(t){a(this,e),this.client_=t.client,this.signalChannel_=t.signalChannel,this.isPublishingTencentCDN_=!1,this.publishTencentStreamRetryCount_=0,this.publishGivenCDNData_=null,this.isPublishingGivenCDN_=!1}var t,n,r,o,c;return s(e,[{key:"getIsPublishingTencentCDN",value:function(){return this.isPublishingTencentCDN_}},{key:"getIsPublishingGivenCDN",value:function(){return this.isPublishingGivenCDN_}},{key:"startPublishTencentCDN",value:(c=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(n=t.streamId)||(r="".concat(this.client_.getRoomId(),"_").concat(this.client_.getUserId(),"_main"),/^[A-Za-z\d_-]*$/.test(r)||(r=RS(r)),n="".concat(this.client_.getSDKAppId(),"_").concat(r)),i={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),requestTime:Date.now(),sessionId:RS("".concat(this.client_.getRoomId(),"_").concat(this.client_.getUserId(),"_main")),streamId:n,streamType:0},this.isPublishingTencentCDN_=!0,this.publishTencentStreamRetryCount_=0,e.next=7,this.doStartPublishTencentCDN(i);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"doStartPublishTencentCDN",value:(o=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,pf.info("startPublishTencentCDN: "+JSON.stringify(t)),e.next=4,this.signalChannel_.sendWaitForResponse({command:lm,data:t,timeout:2e3,responseCommand:$f.START_PUBLISH_TENCENT_CDN_RES,commandDesc:"startPublishCDNStream",retry:2});case 4:if(n=e.sent,r=n.data.content,i=r.errCode,a=r.errMsg,0===i){e.next=11;break}throw this.isPublishingTencentCDN_=!1,-102083===i&&(a="Please enable relayed-push in https://console.cloud.tencent.com/trtc/ and try later, refer to https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-26-advanced-publish-cdn-stream.html"),pf.error("startPublishTencentCDN failed, errCode: ".concat(i,", errMsg: ").concat(a)),new Al({code:Cl.START_PUBLISH_CDN_FAILED,message:Zv({key:Z_,data:{message:a},link:{className:"Client",fnName:"startPublishCDNStream"}})});case 11:e.next=17;break;case 13:throw e.prev=13,e.t0=e.catch(0),this.isPublishingTencentCDN_=!1,e.t0;case 17:case"end":return e.stop()}}),e,this,[[0,13]])}))),function(e){return o.apply(this,arguments)})},{key:"stopPublishTencentCDN",value:(r=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),requestTime:Date.now(),sessionId:RS("".concat(this.client_.getRoomId(),"_").concat(this.client_.getUserId(),"_main"))},pf.info("stopPublishTencentCDN: "+JSON.stringify(t)),e.next=4,this.signalChannel_.sendWaitForResponse({command:hm,data:t,timeout:5e3,responseCommand:$f.STOP_PUBLISH_TENCENT_CDN_RES,commandDesc:"stopPublishCDNStream"});case 4:if(n=e.sent,r=n.data.content,i=r.errCode,a=r.errMsg,0!==i){e.next=10;break}this.isPublishingTencentCDN_=!1,e.next=17;break;case 10:if(-102069!==i){e.next=15;break}pf.warn("stopPublishTencentCDN failed, can not stopPublishTencentCDN in auto relayed-push mode"),this.isPublishingTencentCDN_=!1,e.next=17;break;case 15:throw pf.error("stopPublishTencentCDN failed, errCode: ".concat(i," errMsg: ").concat(a)),new Al({code:Cl.STOP_PUBLISH_CDN_FAILED,message:Zv({key:ev,data:{message:a},link:{className:"Client",fnName:"stopPublishCDNStream"}})});case 17:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"startPublishGivenCDN",value:(n=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),socketid:this.signalChannel_.getSocketId(),pushRequestTime:Date.now(),pushAppid:t.appId,pushBizId:t.bizId,pushCdnUrl:t.url,pushStreamType:"main"},pf.info("startPublishGivenCDN: "+JSON.stringify(n)),this.publishGivenCDNData_=n,this.isPublishingGivenCDN_=!0,e.prev=4,e.next=7,this.signalChannel_.sendWaitForResponse({command:pm,data:n,timeout:5e3,responseCommand:$f.START_PUBLISH_GIVEN_CDN_RES,commandDesc:"startPublishCDNStream"});case 7:if(r=e.sent,i=r.data.content,a=i.errCode,o=i.errMsg,0===a){e.next=13;break}throw pf.error("startPublishGivenCDN failed, errCode: ".concat(a,", errMsg: ").concat(o)),this.isPublishingGivenCDN_=!1,new Al({code:Cl.START_PUBLISH_CDN_FAILED,message:Zv({key:Z_,data:{message:o},link:{className:"Client",fnName:"startPublishCDNStream"}})});case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(4),this.isPublishingGivenCDN_=!1,e.t0;case 19:case"end":return e.stop()}}),e,this,[[4,15]])}))),function(e){return n.apply(this,arguments)})},{key:"stopPublishGivenCDN",value:(t=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.publishGivenCDNData_,n=t.pushAppid,r=t.pushBizId,i=t.pushCdnUrl,a=t.pushStreamType,o={roomid:String(this.client_.getRoomId()),sdkAppID:String(this.client_.getSDKAppId()),socketid:this.signalChannel_.getSocketId(),pushRequestTime:Date.now(),pushAppid:n,pushBizId:r,pushCdnUrl:i,pushStreamType:a},pf.info("stopPublishGivenCDN: "+JSON.stringify(o)),e.next=5,this.signalChannel_.sendWaitForResponse({command:fm,data:o,timeout:5e3,responseCommand:$f.STOP_PUBLISH_GIVEN_CDN_RES,commandDesc:"stopPublishCDNStream"});case 5:if(s=e.sent,c=s.data.content,u=c.errCode,d=c.errMsg,0!==u){e.next=11;break}this.isPublishingGivenCDN_=!1,e.next=13;break;case 11:throw pf.error("stopPublishGivenCDN failed, errCode: ".concat(u," errMsg: ").concat(d)),new Al({code:Cl.STOP_PUBLISH_CDN_FAILED,message:Zv({key:ev,data:{message:d},link:{className:"Client",fnName:"stopPublishCDNStream"}})});case 13:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})}]),e}(),wS=function(){function e(t){a(this,e),this.client_=t.client,this.durationMap_=new Map,this.installEvents()}return s(e,[{key:"installEvents",value:function(){ih.on(wh,this.handleSubscribed,this),ih.on(Rh,this.handleStreamTrackUpdated,this),ih.on(Ch,this.handleStreamStopped,this),ih.on(Ph,this.handleStreamStopped,this)}},{key:"uninstallEvents",value:function(){ih.off(wh,this.handleSubscribed,this),ih.off(Ah,this.handleStreamTrackUpdated,this),ih.off(Ch,this.handleStreamStopped,this),ih.off(Ph,this.handleStreamStopped,this)}},{key:"handleSubscribed",value:function(e){var t=e.client,n=e.stream;if(t===this.client_){var r=n.getUserId(),i=n.getType(),a="".concat(r,"_").concat(i);if(n.hasAudio())if(n.isMainAudioSubscribed){var o=new kS,s=this.durationMap_.get(a);s?this.isRecording(s.audio)||s.audio.push(o):this.durationMap_.set(a,{userId:r,type:i,audio:[o],video:[]})}else this.stopDurationItem(a,Gu);if(n.hasVideo())if("main"===i&&n.isMainVideoSubscribed||"auxiliary"===i&&n.isAuxVideoSubscribed){var c=new kS,u=this.durationMap_.get(a);u?this.isRecording(u.video)||u.video.push(c):this.durationMap_.set(a,{userId:r,type:i,audio:[],video:[c]})}else this.stopDurationItem(a,Ju)}}},{key:"handleStreamStopped",value:function(e){var t=e.client,n=e.stream;if(this.clientHitTest(t)){var r=n.getUserId(),i=n.getType(),a="".concat(r,"_").concat(i);this.stopDurationItem(a,Gu),this.stopDurationItem(a,Ju)}}},{key:"handleStreamTrackUpdated",value:function(e){var t=e.client,n=e.userId,r=e.tinyId,i=e.kind,a=e.action;if(this.clientHitTest(t)&&this.client_.getConnections().has(r)){var o=i===zu?i:"main",s="".concat(n,"_").concat(o);if(a===rd){var c=this.client_.getConnections().get(r).getSubscribeState();if(i===Gu&&!c.audio||i===Ju&&!c.video||i===zu&&!c.auxiliary)return;var u=new kS,d=this.durationMap_.get(s);d?(i!==Gu||this.isRecording(d.audio)||d.audio.push(u),i===Gu||this.isRecording(d.video)||d.video.push(u)):this.durationMap_.set(s,{userId:n,type:o,audio:i===Gu?[u]:[],video:i===Gu?[]:[u]})}else this.stopDurationItem(s,i===Gu?Gu:Ju)}}},{key:"isRecording",value:function(e){return e.findIndex((function(e){return 0===e.endTime}))>=0}},{key:"stopDurationItem",value:function(e,t){if(this.durationMap_.has(e)){var n=this.durationMap_.get(e)[t].find((function(e){return 0===e.endTime}));n&&n.stop()}}},{key:"clientHitTest",value:function(e){return this.client_===e}},{key:"getDuration",value:function(e,t){return this.durationMap_.has(e)?this.durationMap_.get(e)[t].reduce((function(e,t){return e+t.getDuration()}),0):0}},{key:"getDurationMap",value:function(){return this.durationMap_}},{key:"reset",value:function(){this.durationMap_.clear()}}]),e}(),CS=ti.f,AS=P((function(){return!Object.getOwnPropertyNames(1)}));Tt({target:"Object",stat:!0,forced:AS},{getOwnPropertyNames:CS});var PS,xS,DS,NS,LS,OS,MS,VS,US,FS,jS,BS,HS,GS,JS={unknown:0,wifi:1,"4g":2,"3g":3,"2g":4,wired:5},zS={msg_user_info:0,uint32_video_avg_fps:0,uint32_video_width:0,uint32_video_height:0,uint32_video_avg_bitrate:0,uint32_video_block_time:0,uint32_video_play_time:0,uint32_audio_block_time:0,uint32_audio_play_time:0,uint32_audio_play_db:0,uint32_avg_down_loss:0,uint32_stream_type:0,uint32_video_render_first:0,uint32_video_block_count:0,uint32_audio_block_count:0,uint32_audio_bitrate:0,uint32_video_black_screen_subjective:0,uint32_audio_recv_bitrate:0,uint32_video_external_block_time:0},WS=function e(t){a(this,e),this.str_identifier=String(t.userId),this.uint64_tinyid=Number(t.tinyId),this.uint32_role=t.role},qS=function(){function e(t){var n=this;a(this,e),this.frameWorkType_=t.frameWorkType||30,this.client_=t.client,this.keyPrefix_="key_point",this.storageKey_="".concat(this.keyPrefix_,"_").concat(this.client_.getUserId()),this.log_=pf.createLogger({id:"kpm|"+this.client_.getUserId(),userId:this.client_.getUserId(),sdkAppId:this.client_.getSDKAppId()}),this.upload=df({retryFunction:this.upload,settings:{timeout:500,retries:3},onError:function(e,t){return t()}}),Object.getOwnPropertyNames(this.__proto__).forEach((function(e){e.startsWith("handle")&&Vl(n[e])&&(n[e]=function(e){var t=e.fn,n=e.context;return i(regeneratorRuntime.mark((function e(){var r,i,a,o=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(e.prev=0,r=o.length,i=new Array(r),a=0;a<r;a++)i[a]=o[a];return e.next=4,t.apply(n||this,i);case 4:return e.abrupt("return",e.sent);case 7:e.prev=7,e.t0=e.catch(0),pf.error("".concat(t.name,"() error observed ")+e.t0);case 10:case"end":return e.stop()}}),e,this,[[0,7]])})))}({fn:n[e],context:n}))})),this.initData(),this.installEvents(),this.intervalId_=vg.setInterval(this.setStorage.bind(this),2e4)}var n,r,o;return s(e,[{key:"initData",value:function(){this.firstPublishedUserList_=[],this.networkQuality_={totalUplinkRTT:0,totalUplinkLoss:0,count:0,totalDownlinkRTTAndLossMap:new Map},this.basicInfo={string_sdk_version:"4.11.11",uint32_os_type:15,string_device_name:"",string_http_user_agent:navigator.userAgent,string_os_version:"",uint32_avg_rtt:0,uint32_avg_up_loss:0,uint32_scene:"live"===this.client_.getMode()?1:0,uint32_joining_duration:0,uint32_networkType:JS[Ll()],uint32_framework:this.frameWorkType_},this.pathJoinRoom_={uint64_start_time:0,uint64_init_audio_start_time:0,uint64_init_audio_end_time:0,uint64_init_camera_start_time:0,uint64_init_camera_end_time:0,uint64_send_request_acc_ip_cmd_start_time:0,uint64_send_request_acc_ip_cmd_end_time:0,uint64_send_request_enter_room_cmd_start_time:0,uint64_send_request_enter_room_cmd_end_time:0,uint64_send_first_video_frame_time:0,uint64_recv_userlist_time:0,uint64_end_time:0,int32_init_audio_ret:0,int32_init_camera_ret:0,int32_send_request_acc_ip_cmd_ret:0,int32_send_request_enter_room_cmd_ret:0,int32_end_ret:0},this.pathLeaveRoom_={uint64_start_time:0,uint64_send_request_exit_room_cmd_start_time:0,uint64_send_request_exit_room_cmd_end_time:0,uint64_end_time:0,int32_send_request_exit_room_cmd_ret:0,int32_end_ret:0},this.pathMainVideoMap_=new Map,this.pathMainAudioMap_=new Map,this.pathAuxiliaryMap_=new Map,this.localStreamStats_={totalVideoBitrate:0,totalVideoFPS:0,totalVideoHeight:0,totalVideoWidth:0,totalAudioLevel:0,videoCount:0,audioLevelCount:0,publishStartTime:0,statsToReport:{uint32_audio_capture_db:0,uint32_video_big_capture_fps:0,uint32_video_big_bitrate:0,uint32_video_big_resolution:0}},this.remoteStreamStatsMap_=new Map}},{key:"installEvents",value:function(){ih.on(ah,this.handleJoinStart,this),ih.on(_h,this.handleWSStart,this),ih.on(vh,this.handleWSEnd,this),ih.on(oh,this.handleJoinSendCMD,this),ih.on(sh,this.handleJoinReceivedCMDResponce,this),ih.on(ch,this.handleJoinSuccess,this),ih.on(uh,this.handleJoinFailed,this),ih.on(fh,this.handleReceivedPublishUserList,this),ih.on(Gh,this.handleConnectionStateChanged,this),ih.on(dh,this.handleLeaveStart,this),ih.on(hh,this.handleLeaveSuccess,this),ih.on(lh,this.handleLeaveSendCMD,this),ih.on(Jh,this.handleSendSubscribeCMD,this),ih.on(bh,this.handleVideoPlaying,this),ih.on(Ih,this.handleAudioPlaying,this),ih.on(zh,this.handleNetworkQuality,this),ih.on(ph,this.handleHeartbeatStats,this),ih.on(Th,this.handleRemoteStreamAdded,this),ih.on(Eh,this.handleRemoteStreamSubscribeStart,this),ih.on(wh,this.handleRemoteStreamSubscribed,this),ih.on(Uh,this.handleVideoLoadedData,this),ih.on(Vh,this.handlePlayStream,this),ih.on(mh,this.handlePublishStart,this),ih.on(Dh,this.handleLocalStreamInitStart,this),ih.on(Nh,this.handleLocalStreamInitEnd,this),ih.on(Lh,this.handleLocalStreamInitFailed,this)}},{key:"uninstallEvents",value:function(){ih.off(ah,this.handleJoinStart,this),ih.off(_h,this.handleWSStart,this),ih.off(vh,this.handleWSEnd,this),ih.off(oh,this.handleJoinSendCMD,this),ih.off(sh,this.handleJoinReceivedCMDResponce,this),ih.off(fh,this.handleReceivedPublishUserList,this),ih.off(Gh,this.handleConnectionStateChanged,this),ih.off(dh,this.handleLeaveStart,this),ih.off(hh,this.handleLeaveSuccess,this),ih.off(ch,this.handleJoinSuccess,this),ih.off(uh,this.handleJoinFailed,this),ih.off(lh,this.handleLeaveSendCMD,this),ih.off(Jh,this.handleSendSubscribeCMD,this),ih.off(bh,this.handleVideoPlaying,this),ih.off(Ih,this.handleAudioPlaying,this),ih.off(zh,this.handleNetworkQuality,this),ih.off(ph,this.handleHeartbeatStats,this),ih.off(Th,this.handleRemoteStreamAdded,this),ih.off(Eh,this.handleRemoteStreamSubscribeStart,this),ih.off(wh,this.handleRemoteStreamSubscribed,this),ih.off(Uh,this.handleVideoLoadedData,this),ih.off(Vh,this.handlePlayStream,this),ih.off(mh,this.handlePublishStart,this),ih.off(Dh,this.handleLocalStreamInitStart,this),ih.off(Nh,this.handleLocalStreamInitEnd,this),ih.off(Lh,this.handleLocalStreamInitFailed,this)}},{key:"handleJoinStart",value:function(e){this.hitTest(e.client)&&0===this.pathJoinRoom_.uint64_start_time&&(this.pathJoinRoom_.uint64_start_time=Date.now(),this.checkStorage())}},{key:"handleWSStart",value:function(e){var t=e.client;this.hitTest(t)&&0===this.pathJoinRoom_.uint64_send_request_acc_ip_cmd_start_time&&(this.pathJoinRoom_.uint64_send_request_acc_ip_cmd_start_time=Date.now())}},{key:"handleWSEnd",value:function(e){var t=e.client,n=e.error;this.hitTest(t)&&0===this.pathJoinRoom_.uint64_send_request_acc_ip_cmd_end_time&&(this.pathJoinRoom_.uint64_send_request_acc_ip_cmd_end_time=Date.now(),n&&(this.pathJoinRoom_.int32_send_request_acc_ip_cmd_ret=n instanceof Al?Number(n.getExtraCode()||n.getCode()):Cl.UNKNOWN,this.pathJoinRoom_.int32_end_ret=2))}},{key:"handleJoinSendCMD",value:function(e){this.hitTest(e.client)&&0===this.pathJoinRoom_.uint64_send_request_enter_room_cmd_start_time&&(this.pathJoinRoom_.uint64_send_request_enter_room_cmd_start_time=Date.now())}},{key:"handleJoinReceivedCMDResponce",value:function(e){this.hitTest(e.client)&&0===this.pathJoinRoom_.uint64_send_request_enter_room_cmd_end_time&&(this.pathJoinRoom_.uint64_send_request_enter_room_cmd_end_time=Date.now(),this.pathJoinRoom_.int32_send_request_enter_room_cmd_ret=e.code,0!==e.code&&(this.pathJoinRoom_.int32_end_ret=3))}},{key:"handleJoinSuccess",value:function(e){this.hitTest(e.client)&&0===this.pathJoinRoom_.uint64_end_time&&(this.pathJoinRoom_.uint64_end_time=Date.now(),this.pathJoinRoom_.int32_end_ret=0)}},{key:"handleJoinFailed",value:function(e){var t=e.client;this.hitTest(t)&&(this.pathJoinRoom_.uint64_end_time=Date.now(),0===this.pathJoinRoom_.int32_end_ret&&(this.pathJoinRoom_.int32_end_ret=3),this.prepareReport(),this.report())}},{key:"handleReceivedPublishUserList",value:function(e){this.hitTest(e.client)&&0===this.pathJoinRoom_.uint64_recv_userlist_time&&(this.pathJoinRoom_.uint64_recv_userlist_time=Date.now(),this.firstPublishedUserList_=e.data.content.userlist)}},{key:"handleConnectionStateChanged",value:function(e){var t=e.client,n=e.state,r=e.connection;if(this.hitTest(t)&&n===ou){this.client_.getUplinkConnection()===r&&0===this.pathJoinRoom_.uint64_send_first_video_frame_time&&this.localStreamStats_.publishStartTime>this.pathJoinRoom_.uint64_end_time&&this.localStreamStats_.publishStartTime-this.pathJoinRoom_.uint64_end_time<=100&&(this.pathJoinRoom_.uint64_send_first_video_frame_time=Date.now());var i=this.pathMainVideoMap_.get("".concat(r.getUserId(),"_").concat("main"));i&&0===i.statsToReport.uint64_pc_connected_time&&(i.statsToReport.uint64_pc_connected_time=Date.now())}}},{key:"handleLeaveStart",value:function(e){this.hitTest(e.client)&&(this.pathLeaveRoom_.uint64_start_time=Date.now())}},{key:"handleLeaveSuccess",value:function(e){this.hitTest(e.client)&&0===this.pathLeaveRoom_.uint64_end_time&&(this.pathLeaveRoom_.uint64_end_time=Date.now(),0!==this.pathJoinRoom_.uint64_end_time?this.basicInfo.uint32_joining_duration=this.pathLeaveRoom_.uint64_end_time-this.pathJoinRoom_.uint64_end_time:this.log_.warn("pathJoinRoom endTime is 0"),this.report())}},{key:"handleLeaveSendCMD",value:function(e){this.hitTest(e.client)&&(this.pathLeaveRoom_.uint64_send_request_exit_room_cmd_start_time=Date.now(),this.pathLeaveRoom_.uint64_send_request_exit_room_cmd_end_time=Date.now())}},{key:"handleRemoteStreamAdded",value:function(e){var n=e.client,r=e.stream;if(this.hitTest(n)){var i=r.getUserId(),a=r.getType(),o="".concat(i,"_").concat(a),s=this.remoteStreamStatsMap_.get(o);if(s)s.stream=r;else{var c={userId:i,totalVideoFPS:0,totalVideoBitrate:0,totalAudioLevel:0,totalAudioBitrate:0,totalLoss:0,audioCount:0,audioLevelCount:0,videoCount:0,networkQualityCount:0,streamAddedTime:Date.now(),subscribeStartTime:0,subscribedTime:0,playStreamTime:0,statsToReport:t({},zS),stream:r};c.statsToReport.msg_user_info=new WS({userId:i,tinyId:r.getTinyId(),role:Yc}),c.statsToReport.uint32_stream_type="main"===a?2:7,this.remoteStreamStatsMap_.set(o,c)}}}},{key:"handleRemoteStreamSubscribeStart",value:function(e){var t=e.client,n=e.stream;if(this.hitTest(t)){var r=n.getUserId(),i=n.getType(),a="".concat(r,"_").concat(i),o=this.remoteStreamStatsMap_.get(a);o&&0===o.subscribeStartTime&&(o.subscribeStartTime=Date.now())}}},{key:"handleSendSubscribeCMD",value:function(e){if(this.hitTest(e.client)){var t=new WS(e),n=Date.now(),r="".concat(e.userId,"_").concat("main");e.trackState.video&&e.subscribeState.video&&!this.pathMainVideoMap_.has(r)&&this.pathMainVideoMap_.set(r,{statsToReport:{msg_user_info:t,uint64_start_enter_time:this.pathJoinRoom_.uint64_start_time,uint64_render_first_frame_time:0,uint64_combine_first_frame_time:0,uint64_pc_connected_time:0},userId:e.userId,sendSubscribeCMDTime:n}),e.trackState.audio&&e.subscribeState.audio&&!this.pathMainAudioMap_.has(r)&&this.pathMainAudioMap_.set(r,{statsToReport:{msg_user_info:t,uint64_start_enter_time:this.pathJoinRoom_.uint64_start_time,uint64_play_first_frame_time:0},userId:e.userId,sendSubscribeCMDTime:n});var i="".concat(e.userId,"_").concat("auxiliary");e.trackState.auxiliary&&e.subscribeState.auxiliary&&!this.pathAuxiliaryMap_.has(i)&&this.pathAuxiliaryMap_.set(i,{sendSubscribeCMDTime:n})}}},{key:"handleRemoteStreamSubscribed",value:function(e){var t=e.client,n=e.stream;if(this.hitTest(t)){var r=n.getUserId(),i=n.getType(),a="".concat(r,"_").concat(i),o=this.remoteStreamStatsMap_.get(a);o&&0===o.subscribedTime&&(o.subscribedTime=Date.now(),o.stream=n)}}},{key:"handlePlayStream",value:function(e){var t=e.stream;if(t.isRemote()&&t.getConnection()&&this.hitTest(t.getConnection().getClient())){var n=t.getConnection().getUserId(),r="".concat(n,"_").concat(t.getType());if(this.remoteStreamStatsMap_.has(r)){var i=this.remoteStreamStatsMap_.get(r);0===i.playStreamTime&&(i.playStreamTime=Date.now())}}}},{key:"handleVideoLoadedData",value:function(e){var t=e.stream;if(t.isRemote()&&t.getConnection()&&this.hitTest(t.getConnection().getClient())){var n=t.getConnection().getUserId(),r="".concat(n,"_").concat(t.getType());if(this.pathMainVideoMap_.has(r)){var i=this.pathMainVideoMap_.get(r);0===i.statsToReport.uint64_combine_first_frame_time&&(i.statsToReport.uint64_combine_first_frame_time=Date.now())}}}},{key:"handleVideoPlaying",value:function(e){var t=e.stream;if(t.isRemote()&&t.getConnection()&&this.hitTest(t.getConnection().getClient())){var n=t.getConnection().getUserId(),r="".concat(n,"_").concat(t.getType()),i=Date.now();if(this.pathMainVideoMap_.has(r)){var a=this.pathMainVideoMap_.get(r);if(0===a.statsToReport.uint64_render_first_frame_time&&(a.statsToReport.uint64_render_first_frame_time=i),this.remoteStreamStatsMap_.has(r)){var o=this.remoteStreamStatsMap_.get(r),s=o.statsToReport,c=o.playStreamTime,u=o.subscribedTime;0===s.uint32_video_render_first&&c-u<=100&&(s.uint32_video_render_first=i-a.sendSubscribeCMDTime)}}if("auxiliary"===t.getType()&&this.pathAuxiliaryMap_.has(r)&&this.remoteStreamStatsMap_.has(r)){var d=this.remoteStreamStatsMap_.get(r),l=d.statsToReport,h=d.playStreamTime,p=d.subscribedTime;0===l.uint32_video_render_first&&h-p<=100&&(l.uint32_video_render_first=i-this.pathAuxiliaryMap_.get(r).sendSubscribeCMDTime)}}}},{key:"handleAudioPlaying",value:function(e){if(e.stream.isRemote()&&e.stream.getConnection()&&this.hitTest(e.stream.getConnection().getClient())){var t=e.stream.getConnection().getUserId(),n="".concat(t,"_").concat(e.stream.getType());if(this.pathMainAudioMap_.has(n)){var r=this.pathMainAudioMap_.get(n);0===r.statsToReport.uint64_play_first_frame_time&&(r.statsToReport.uint64_play_first_frame_time=Date.now())}}}},{key:"handleNetworkQuality",value:function(e){var t=this;this.hitTest(e.client)&&(this.networkQuality_.totalUplinkLoss+=e.uplinkLoss,this.networkQuality_.totalUplinkRTT+=e.uplinkRTT,this.networkQuality_.count++,e.downlinkLossAndRTTMap.forEach((function(e){var n=e.rtt,r=e.loss,i=e.userId,a=t.networkQuality_.totalDownlinkRTTAndLossMap.get(i);a?(a.totalRTT+=n,a.totalLoss+=r,a.count++):t.networkQuality_.totalDownlinkRTTAndLossMap.set(i,{totalRTT:n,totalLoss:r,count:1})})))}},{key:"handleHeartbeatStats",value:function(e){var t=this;if(this.hitTest(e.client)){var n=e.stats,r=n.VideoReportState,i=n.AudioReportState;r.VideoEncState[0]&&(this.localStreamStats_.totalVideoBitrate+=r.uint32_video_snd_br,this.localStreamStats_.totalVideoFPS+=r.VideoEncState[0].uint32_enc_fps,this.localStreamStats_.totalVideoWidth+=r.VideoEncState[0].uint32_enc_width,this.localStreamStats_.totalVideoHeight+=r.VideoEncState[0].uint32_enc_height,this.localStreamStats_.videoCount++),r.VideoDecState.forEach((function(e){var n=0===e.uint32_video_strtype,r=2===e.uint32_video_strtype,i="".concat(e.userId,"_").concat(n?"main":"auxiliary");if(t.remoteStreamStatsMap_.has(i)){var a=t.remoteStreamStatsMap_.get(i);(n&&a.stream.isMainVideoSubscribed||r&&a.stream.isAuxVideoSubscribed)&&(a.totalVideoFPS+=e.uint32_video_recv_fps,a.totalVideoBitrate+=e.uint32_video_recv_br,a.videoCount++,0===a.statsToReport.uint32_video_width&&(a.statsToReport.uint32_video_width=e.uint32_dec_width),0===a.statsToReport.uint32_video_height&&(a.statsToReport.uint32_video_height=e.uint32_dec_height))}})),i&&(i.AudioDecState.forEach((function(e){var n="".concat(e.userId,"_").concat("main");if(t.remoteStreamStatsMap_.has(n)){var r=t.remoteStreamStatsMap_.get(n);r.stream.isMainAudioSubscribed&&(r.totalAudioBitrate+=e.uint32_audio_real_recv_br,r.audioCount++,Math.floor(100*e.audioLevel)>0&&(r.totalAudioLevel+=e.audioLevel,r.audioLevelCount++))}})),Math.floor(100*i.sentAudioLevel)>0&&(this.localStreamStats_.totalAudioLevel+=i.sentAudioLevel,this.localStreamStats_.audioLevelCount++))}}},{key:"handlePublishStart",value:function(e){var t=e.client;this.hitTest(t)&&0===this.localStreamStats_.publishStartTime&&(this.localStreamStats_.publishStartTime=Date.now())}},{key:"handleLocalStreamInitStart",value:function(e){var t=e.audio,n=e.video;t&&0===this.pathJoinRoom_.uint64_init_audio_start_time&&(this.pathJoinRoom_.uint64_init_audio_start_time=Date.now()),n&&0===this.pathJoinRoom_.uint64_init_camera_start_time&&(this.pathJoinRoom_.uint64_init_camera_start_time=Date.now())}},{key:"handleLocalStreamInitEnd",value:function(e){var t=e.audio,n=e.video;t&&0===this.pathJoinRoom_.uint64_init_audio_end_time&&(this.pathJoinRoom_.uint64_init_audio_end_time=Date.now()),n&&0===this.pathJoinRoom_.uint64_init_camera_end_time&&(this.pathJoinRoom_.uint64_init_camera_end_time=Date.now())}},{key:"handleLocalStreamInitFailed",value:function(e){var t=e.audio,n=e.video,r=e.error,i=r instanceof Al?r.getExtraCode()||r.getCode():{NotFoundError:1,NotAllowedError:2,NotReadableError:3,OverConstrainedError:4,AbortError:5}[r.name]||Cl.UNKNOWN;t&&0===this.pathJoinRoom_.uint64_init_audio_end_time&&(this.pathJoinRoom_.int32_init_audio_ret=i,this.pathJoinRoom_.uint64_init_audio_end_time=Date.now()),n&&0===this.pathJoinRoom_.uint64_init_camera_end_time&&(this.pathJoinRoom_.int32_init_camera_ret=i,this.pathJoinRoom_.uint64_init_camera_end_time=Date.now())}},{key:"hasVideoFlag",value:function(e){return this.firstPublishedUserList_.findIndex((function(t){return t.userid===e&&1&t.flag}))>=0}},{key:"hasAudioFlag",value:function(e){return this.firstPublishedUserList_.findIndex((function(t){return t.userid===e&&8&t.flag}))>=0}},{key:"hasAuxFlag",value:function(e){return this.firstPublishedUserList_.findIndex((function(t){return t.userid===e&&4&t.flag}))>=0}},{key:"hitTest",value:function(e){return e===this.client_}},{key:"checkStorage",value:(o=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!(t=gg.getItem(this.storageKey_))){e.next=6;break}return e.next=5,this.upload(t);case 5:gg.deleteItem(this.storageKey_);case 6:e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),this.log_.warn(e.t0);case 11:case"end":return e.stop()}}),e,this,[[0,8]])}))),function(){return o.apply(this,arguments)})},{key:"setStorage",value:function(){this.prepareReport();var e=this.getReportData();0!==e.msg_path_enter_room.uint64_start_time&&gg.setItem(this.storageKey_,e)}},{key:"prepareReport",value:function(){var e=this;if(this.networkQuality_.count>0&&(this.basicInfo.uint32_avg_rtt=Math.floor(this.networkQuality_.totalUplinkRTT/this.networkQuality_.count),this.basicInfo.uint32_avg_up_loss=Math.floor(this.networkQuality_.totalUplinkLoss/this.networkQuality_.count)),this.localStreamStats_.videoCount>0){this.localStreamStats_.statsToReport.uint32_video_big_capture_fps=Math.floor(this.localStreamStats_.totalVideoFPS/this.localStreamStats_.videoCount),this.localStreamStats_.statsToReport.uint32_video_big_bitrate=Math.floor(this.localStreamStats_.totalVideoBitrate/this.localStreamStats_.videoCount);var t=Math.floor(this.localStreamStats_.totalVideoWidth/this.localStreamStats_.videoCount),n=Math.floor(this.localStreamStats_.totalVideoHeight/this.localStreamStats_.videoCount);this.localStreamStats_.statsToReport.uint32_video_big_resolution=t<<16|n}this.localStreamStats_.audioLevelCount>0&&(this.localStreamStats_.statsToReport.uint32_audio_capture_db=Math.floor(this.localStreamStats_.totalAudioLevel/this.localStreamStats_.audioLevelCount*100)),this.remoteStreamStatsMap_.forEach((function(t,n){var r=t.userId;if(e.networkQuality_.totalDownlinkRTTAndLossMap.has(r)){var i=e.networkQuality_.totalDownlinkRTTAndLossMap.get(r),a=i.totalLoss,o=i.count;t.statsToReport.uint32_avg_down_loss=Math.floor(a/o)}t.videoCount>0&&(t.statsToReport.uint32_video_avg_fps=Math.floor(t.totalVideoFPS/t.videoCount),t.statsToReport.uint32_video_avg_bitrate=Math.floor(t.totalVideoBitrate/t.videoCount)),t.audioCount>0&&(t.statsToReport.uint32_audio_recv_bitrate=t.statsToReport.uint32_audio_bitrate=Math.floor(t.totalAudioBitrate/t.audioCount)),t.audioLevelCount>0&&(t.statsToReport.uint32_audio_play_db=Math.floor(t.totalAudioLevel/t.audioLevelCount*100));var s=e.client_.getCallDurationCalculator();s&&(t.statsToReport.uint32_audio_play_time=s.getDuration(n,Gu),t.statsToReport.uint32_video_play_time=s.getDuration(n,Ju)),t.statsToReport.uint32_video_render_first=Math.min(t.statsToReport.uint32_video_render_first,5e3);var c=e.client_.getBadCaseDetector();if(c){var u=c.getDataFreezeDuration(n),d=u.dataFreeze,l=u.count,h=c.getRenderFreezeDuration(n).renderFreeze;t.statsToReport.uint32_video_block_count=l,t.statsToReport.uint32_video_block_time=Math.min(d,t.statsToReport.uint32_video_play_time),t.statsToReport.uint32_video_external_block_time=Math.min(h,t.statsToReport.uint32_video_play_time),c.isBlackStream(n)&&0===t.statsToReport.uint32_video_avg_fps?t.statsToReport.uint32_video_black_screen_subjective=1:t.statsToReport.uint32_video_black_screen_subjective=0}(0===t.subscribeStartTime||t.subscribeStartTime-t.streamAddedTime>100||0===t.playStreamTime)&&(e.pathMainAudioMap_.delete(n),e.pathMainVideoMap_.delete(n),t.statsToReport.uint32_video_render_first=0)})),this.pathMainAudioMap_.forEach((function(t,n){e.hasAudioFlag(t.userId)?t.statsToReport.uint64_play_first_frame_time-t.statsToReport.uint64_start_enter_time>5e3&&(t.statsToReport.uint64_play_first_frame_time=t.statsToReport.uint64_start_enter_time+5e3):e.pathMainAudioMap_.delete(n)})),this.pathMainVideoMap_.forEach((function(t,n){e.hasVideoFlag(t.userId)?t.statsToReport.uint64_render_first_frame_time-t.statsToReport.uint64_start_enter_time>5e3&&(t.statsToReport.uint64_render_first_frame_time=t.statsToReport.uint64_start_enter_time+5e3):e.pathMainVideoMap_.delete(n)})),this.pathJoinRoom_.uint64_end_time-this.pathJoinRoom_.uint64_start_time>5e3&&(this.pathJoinRoom_.uint64_end_time=this.pathJoinRoom_.uint64_start_time+5e3)}},{key:"getReportData",value:function(){var e=this.client_.getSignalInfo();return{uint32_sdk_app_id:Number(this.client_.getSDKAppId()),msg_user_info:new WS({userId:this.client_.getUserId(),tinyId:this.client_.getTinyId(),role:"anchor"===this.client_.getRole()?Yc:Zc}),msg_basic_info:this.basicInfo,uint32_acc_ip:Wl(e.relayIp),uint32_client_ip:Wl(e.localIp,"small"),uint32_acc_port:0,uint64_timestamp:Date.now(),uint32_seq:Math.floor(Math.random()*Math.pow(2,31)),msg_path_enter_room:this.pathJoinRoom_,msg_path_exit_room:this.pathLeaveRoom_,msg_path_recv_video:y(this.pathMainVideoMap_.values()).map((function(e){return e.statsToReport})),msg_quality_statistics:y(this.remoteStreamStatsMap_.values()).map((function(e){return e.statsToReport})),str_room_name:String(this.client_.getRoomId()),msg_path_recv_audio:y(this.pathMainAudioMap_.values()).map((function(e){return e.statsToReport})),uint32_info_client_ip:Wl(e.localIp,"small"),error_code:[],msg_local_statistics:this.localStreamStats_.statsToReport}}},{key:"report",value:(r=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=this.getReportData(),e.next=4,this.upload(t);case 4:gg.deleteItem(this.storageKey_),this.initData(),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),this.log_.warn(e.t0);case 11:case"end":return e.stop()}}),e,this,[[0,8]])}))),function(){return r.apply(this,arguments)})},{key:"upload",value:(n=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Mc&&0!==t.msg_path_enter_room.uint64_start_time){e.next=2;break}return e.abrupt("return");case 2:return n=Math.floor(Math.random()*Math.pow(2,31)),r=Number(this.client_.getSDKAppId()),i="".concat("https://yun.tim.qq.com","/v5/AVQualityReportSvc/C2S?random=").concat(n,"&sdkappid=").concat(r,"&cmdtype=jssdk_new_endreport"),e.next=7,wl.post(i,JSON.stringify(t));case 7:if("ok"===(a=e.sent).data){e.next=10;break}throw"key point upload failed: ".concat(a.data);case 10:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})}]),e}();function KS(){return function(e,t,n){var r=n.value,a=new Map;return n.value=i(regeneratorRuntime.mark((function e(){var n,i,o,s,c=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a.get(this)){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:s_,data:{name:t}})});case 2:for(e.prev=2,a.set(this,!0),n=c.length,i=new Array(n),o=0;o<n;o++)i[o]=c[o];return e.next=7,r.apply(this,i);case 7:return s=e.sent,a.set(this,!1),e.abrupt("return",s);case 12:throw e.prev=12,e.t0=e.catch(2),a.set(this,!1),e.t0;case 16:case"end":return e.stop()}}),e,this,[[2,12]])}))),n}}var QS=(PS=KS(),xS=rS(tS.CLIENT.join),DS=KS(),NS=KS(),LS=rS(tS.CLIENT.publish),OS=KS(),MS=rS(tS.CLIENT.unpublish),VS=rS.apply(void 0,y(tS.CLIENT.subscribe)),US=rS(tS.CLIENT.unsubscribe),FS=KS(),jS=rS(tS.CLIENT.switchRole),BS=rS(tS.CLIENT.startPublishCDNStream),HS=rS(tS.CLIENT.startMixTranscode),I((GS=function(){function Client(e){var t,n=this;if(a(this,Client),this.name_=ld,this.mode_=e.mode,this.sdpSemantics_="plan-b",Ul(e.sdpSemantics)?function(){if(!Ng())return!1;if(Ul(window.RTCRtpTransceiver))return!1;if(!("currentDirection"in RTCRtpTransceiver.prototype))return!1;var e=new RTCPeerConnection,t=!1;try{e.addTransceiver(Gu),t=!0}catch(mk){}return e.close(),t}()&&(this.sdpSemantics_="unified-plan"):this.sdpSemantics_=e.sdpSemantics,this.sdkAppId_=e.sdkAppId,this.userId_=e.userId,this.log_=pf.createLogger({id:"c".concat(e.seq,"|").concat(this.userId_),userId:this.userId_,sdkAppId:this.sdkAppId_}),this.userSig_=e.userSig,this.roomId_=0,this.useStringRoomId_=e.useStringRoomId||!1,this.recordId_=null,this.pureAudioPushMode_=null,this.version_=e.version,this.log_.info("using sdpSemantics: "+this.sdpSemantics_),!Ul(e.recordId)){if(!Number.isInteger(Number(e.recordId)))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:R_})});this.recordId_=e.recordId}this.signalChannel_=null,this.isScreenShareOnly_=0,Ul(e.isScreenShareOnly)||(this.isScreenShareOnly_=e.isScreenShareOnly?1:0),this.role_="anchor",this.privateMapKey_="",this.tinyId_=0,this.env_="",this.proxy_=null,this.connections_=new Map,this.mutedStates_=new Map,this.userMap_=new Map,this.syncUserListInterval_=-1,this.localStream_=null,this.uplinkConnection_=null,this.emitter_=new rh,this.signalInfo_={},this.isSignalReady_=!1,this.isJoined_=!1,this.heartbeat_=-1,this.lastHeartBeatTime_=-1,this.stats_=new _y(this),this.joinTimeout_=-1,this.networkQuality_=null,this.badCaseDetector_=null,this.autoSubscribe_=!!Ul(e.autoSubscribe)||e.autoSubscribe,this.startJoinTimestamp_=0,this.joinedTimestamp_=0,this.joinOptions_={},this.basis_={browser:Fc().name+"/"+Fc().version,os:Fg(),displayResolution:jg(),isScreenShareSupported:Ag(),isWebRTCSupported:kg(),isGetUserMediaSupported:Bg(),isWebAudioSupported:Hg(),isWebSocketsSupported:"WebSocket"in window&&2===window.WebSocket.CLOSING,isWebCodecSupported:(t={AudioDecoder:!1,AudioEncoder:!1,VideoDecoder:!1,VideoEncoder:!1,ImageDecoder:!1},Ul(window.AudioDecoder)||(t.AudioDecoder=!0),Ul(window.AudioEncoder)||(t.AudioEncoder=!0),Ul(window.VideoDecoder)||(t.VideoDecoder=!0),Ul(window.VideoEncoder)||(t.VideoEncoder=!0),Ul(window.ImageDecoder)||(t.ImageDecoder=!0),t),isMediaSessionSupported:"mediaSession"in navigator&&!Ul(navigator.mediaSession.setActionHandler),isWebTransportSupported:!Ul(window.WebTransport)},this.initBussinessInfo_(e),this.publishedCDN_=!1,this.publishCDNData_=null,this.mixedMCU_=!1,this.mixTranscodeData_=null,this.checkSystemResult_=null,this.enableAudioVolumeEvaluation_=!1,this.audioVolumeIntervalId_=null,this.mixTranscodeManager_=null,this.publishCDNManager_=null,this.keyPointManager_=new qS({client:this,frameWorkType:e.frameWorkType}),this.getUserList=df({retryFunction:this.getUserList,settings:{retries:3},onError:function(e,t){return t()},onRetrying:function(e){n.log_.info("retrying to get user list [".concat(e,"/3]"))}}),this.isPublishing_=!1,this.isEnableSmallStream_=!1,this.smallStreamConfig_={bitrate:100,frameRate:15,height:120,width:160},this.turnServers_=[],this.iceTransportPolicy_=e.iceTransportPolicy,this.schedule_={domains:null,iceServers:null,iceTransportPolicy:null},this.enableAutoPlayDialog_=!!Ul(e.enableAutoPlayDialog)||e.enableAutoPlayDialog}var e,n,r,o,c,u,d,l,h,p,f,m,_,v,S,k,I,R,T,E,w,C,A,P;return s(Client,[{key:"initBussinessInfo_",value:function(e){this.bussinessInfo_=e.bussinessInfo;var t={};if(Fl(e.bussinessInfo)&&(t=JSON.parse(e.bussinessInfo)),!Ul(e.pureAudioPushMode)){if(!Number.isInteger(Number(e.pureAudioPushMode)))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:T_})});this.pureAudioPushMode_=e.pureAudioPushMode,t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.pure_audio_push_mod=this.pureAudioPushMode_}if(!Ul(e.streamId)){if(!(Fl(e.streamId)&&String(e.streamId)&&String(e.streamId).length<=64))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:E_})});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_streamid_main=e.streamId}if(!Ul(e.userDefineRecordId)){if(null===e.userDefineRecordId.match(/^[A-Za-z0-9_-]{1,64}$/gi))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:w_})});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_record_id=e.userDefineRecordId}if(!Ul(e.userDefinePushArgs)){if(!(Fl(e.userDefinePushArgs)&&String(e.userDefinePushArgs)&&String(e.userDefinePushArgs).length<=256))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:C_})});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_push_args=e.userDefinePushArgs}r_(t)||(this.bussinessInfo_=JSON.stringify(t))}},{key:"setProxyServer",value:function(e){if(this.log_.info("set proxy server: ".concat(JSON.stringify(e))),Fl(e)){if(!e.startsWith("wss://"))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:A_})});this.proxy_=e}else if(Ol(e)){var t=e.websocketProxy,n=e.loggerProxy;t&&(this.proxy_=t),n&&Qc(n)}}},{key:"schedule",value:(P=i(regeneratorRuntime.mark((function e(n){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ql({userId:this.userId_,sdkAppId:this.sdkAppId_,roomId:n,useStringRoomId:this.useStringRoomId_,version:this.version_,userSig:this.userSig_});case 2:(r=e.sent)&&(this.log_.info("schedule: ".concat(JSON.stringify(r))),this.schedule_=t(t({},this.schedule_),r),ih.emit(yh,this.schedule_));case 4:case"end":return e.stop()}}),e,this)}))),function(e){return P.apply(this,arguments)})},{key:"getSignalChannelUrl",value:function(){var e={mainUrl:Bc,backupUrl:Hc},t=xl();return t?e.mainUrl=e.backupUrl="wss://".concat(t,".rtc.qq.com"):this.proxy_?e.mainUrl=e.backupUrl=this.proxy_:Array.isArray(this.schedule_.domains)&&this.schedule_.domains.length>0&&(e.mainUrl=e.backupUrl="wss://".concat(this.schedule_.domains[0]),this.schedule_.domains[1]&&(e.backupUrl="wss://".concat(this.schedule_.domains[1]))),e}},{key:"getUserId",value:function(){return this.userId_}},{key:"getUserSig",value:function(){return this.userSig_}},{key:"getRole",value:function(){return this.role_}},{key:"getSignalInfo",value:function(){return this.signalInfo_}},{key:"getRoomId",value:function(){return this.roomId_}},{key:"getSDKAppId",value:function(){return this.sdkAppId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"setTurnServer",value:function(e){this.log_.info("set turn server: "+JSON.stringify(e));var t=[];Array.isArray(e)?e.forEach((function(e){return t.push(Jl(e))})):Ol(e)&&t.push(Jl(e)),this.turnServers_=t}},{key:"getIceTransportPolicy",value:function(){return this.iceTransportPolicy_||this.schedule_.iceTransportPolicy||"all"}},{key:"initialize",value:function(){var e=this;return new Promise((function(t,n){e.log_.info("setup signal channel");var r=e.getSignalChannelUrl(),i=r.mainUrl,a=r.backupUrl;e.signalChannel_=new eg({sdkAppId:e.sdkAppId_,userId:e.userId_,userSig:e.userSig_,url:i,backupUrl:a,version:e.version_,client:e}),e.networkQuality_||(e.networkQuality_=new vy({connections:e.connections_,signalChannel:e.signalChannel_,userId:e.userId_,client:e}),e.networkQuality_.on(cy,(function(t){e.emitter_.emit(cy,t)}))),e.deviceDetector_||(e.deviceDetector_=new gy({client:e})),e.subscriptionManager_||(e.subscriptionManager_=new cS({client:e})),e.badCaseDetector_||(e.badCaseDetector_=new bS({client:e,stats:e.stats_})),e.callDurationCalculator_||(e.callDurationCalculator_=new wS({client:e})),e.mixTranscodeManager_||(e.mixTranscodeManager_=new TS({client:e,signalChannel:e.signalChannel_})),e.publishCDNManager_||(e.publishCDNManager_=new ES({client:e,signalChannel:e.signalChannel_})),e.signalChannel_.on(Hf,(function(t){switch(e.log_.info("SignalChannel state changed from ".concat(t.prevState," to ").concat(t.state)),t.state){case Kf:t.prevState===qf?(e.log_.warn("signal channel reconnect successfully"),e.syncUserList(),Pl.logSuccessEvent({userId:e.userId_,eventType:Eu})):t.prevState===Wf&&Pl.logSuccessEvent({userId:e.userId_,eventType:Tu})}e.emitter_.emit(ey,t)})),e.signalChannel_.on(Jf,(function(t){e.isSignalReady_?(e.closeUplink(),e.closeConnections(),e.emitter_.emit(dy,t)):(ih.emit(vh,{client:e,error:t}),n(t))})),e.signalChannel_.on($f.CHANNEL_SETUP_SUCCESS,(function(n){e.signalInfo_=n.signalInfo,e.tinyId_=e.signalInfo_.tinyId,e.isSignalReady_||(e.isSignalReady_=!0,ih.emit(vh,{client:e}),t())})),e.signalChannel_.on($f.PEER_JOIN,e.onPeerJoin,e),e.signalChannel_.on($f.PEER_LEAVE,e.onPeerLeave,e),e.signalChannel_.on($f.STREAM_ADDED,(function(t){e.onRemoteStreamAdded(t.data)})),e.signalChannel_.on($f.STREAM_REMOVED,(function(t){e.onRemoteStreamRemoved(t.data)})),e.signalChannel_.on($f.UPDATE_REMOTE_MUTE_STAT,(function(t){ih.emit(fh,{client:e,data:t.data}),e.onPublishedUserList(t.data),e.onUpdateRemoteMuteStat(t.data)})),e.signalChannel_.on($f.CLINET_BANNED,(function(t){var n=t.data.content,r=n.type;"banned"===n.type?r="you got banned by account admin":"kick"===n.type?r="duplicated userId joining the room":"user_time_out"===n.type&&e.log_.warn("last heart beat time: ".concat(e.lastHeartBeatTime_," interval: ").concat(Date.now()-e.lastHeartBeatTime_)),Pl.uploadEvent({log:"stat-banned:".concat(n.type),userId:e.userId_}),e.log_.error("user was banned because of [".concat(n.type,"]")),e.reset(),e.onClientBanned(r)})),e.signalChannel_.on($f.REQUEST_REBUILD_SESSION,(function(t){e.signalInfo_=t.signalInfo;var n=[];e.connections_&&n.push(0);var r,i=[],a=b(e.connections_);try{for(a.s();!(r=a.n()).done;){var o=g(r.value,2),s=o[0],c=o[1];n.push(s);var u=c.getPeerConnection();if(u){var d=u.remoteDescription;d&&i.push(d.sdp)}}}catch(h){a.e(h)}finally{a.f()}var l={socketid:e.signalInfo_.socketId,tinyid:e.tinyId_,appid:e.sdkAppId_,openid:e.userId_,sessionid:String(e.roomId_),sids:n,relayInfo:e.signalInfo_.relayInnerIp,remotesdp:i,useStrRoomid:!!e.useStringRoomId_&&1};e.log_.debug("reconnect - rebuild session with data: ".concat(JSON.stringify(l))),e.signalChannel_.send(nm,l)})),e.signalChannel_.on($f.CLIENT_REJOIN,(function(){e.reJoin()})),ih.emit(_h,{client:e}),e.signalChannel_.connect()}))}},{key:"join",value:(A=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isJoined_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:P_})});case 2:return e.next=4,wg();case 4:if(this.checkSystemResult_=e.sent,(n=xl())||(n=zc,this.proxy_&&(this.proxy_.startsWith(Gc)?n=Wc:this.proxy_.startsWith(Jc)&&(n=qc))),this.env_=n,Pl.setConfig({env:n,sdkAppId:this.sdkAppId_,userId:this.userId_}),this.uploadTrtcStats(),r=this.checkSystemResult_.detail,i=r.isH264EncodeSupported,a=r.isVp8EncodeSupported,kg()&&(i||a)){e.next=13;break}throw new Al({code:Cl.NOT_SUPPORTED,message:Zv({key:Hv})});case 13:if(this.joinOptions_=t,this.startJoinTimestamp_=zl(),ih.emit(ah,{client:this}),t_(this.userId_,{eventId:Fm,eventDesc:"joining room",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=17,this.proxy_||this.schedule_.domains){e.next=21;break}return e.next=21,this.schedule(t.roomId);case 21:return e.next=23,this.initialize(t);case 23:return e.next=25,this.doJoin(t);case 25:ih.emit(ch,{client:this}),this.joinedTimestamp_=zl(),o=this.joinedTimestamp_-this.startJoinTimestamp_,Pl.logSuccessEvent({userId:this.userId_,eventType:cu,delta:o}),Pl.logSuccessEvent({userId:this.userId_,eventType:su}),Pl.uploadEvent({log:"stat-autoplay-dialog:".concat(this.enableAutoPlayDialog_),userId:this.userId_}),e.next=39;break;case 33:throw e.prev=33,e.t0=e.catch(17),ih.emit(uh,{client:this,error:e.t0}),Pl.logFailedEvent({userId:this.userId_,eventType:su,error:e.t0}),this.reset(),e.t0;case 39:case"end":return e.stop()}}),e,this,[[17,33]])}))),function(e){return A.apply(this,arguments)})},{key:"uploadTrtcStats",value:(C=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c,u,d,l,h,p;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fk.getMicrophones();case 3:r=e.sent,t=r&&r.length,e.next=9;break;case 7:e.prev=7,e.t0=e.catch(0);case 9:return e.prev=9,e.next=12,fk.getCameras();case 12:i=e.sent,n=i&&i.length,e.next=18;break;case 16:e.prev=16,e.t1=e.catch(9);case 18:a={microphone:t,camera:n},o=this.checkSystemResult_.detail,s=o.isH264EncodeSupported,c=o.isVp8EncodeSupported,u=o.isH264DecodeSupported,d=o.isVp8DecodeSupported,l={webRTC:this.basis_.isWebRTCSupported,getUserMedia:this.basis_.isGetUserMediaSupported,webSocket:this.basis_.isWebSocketsSupported,screenShare:this.basis_.isScreenShareSupported,webAudio:this.basis_.isWebAudioSupported,h264Encode:s,h264Decode:u,vp8Encode:c,vp8Decode:d},h={browser:this.basis_.browser,os:this.basis_.os,trtc:l,devices:a},p={isWebCodecSupported:this.basis_.isWebCodecSupported,isMediaSessionSupported:this.basis_.isMediaSessionSupported,isWebTransportSupported:this.basis_.isWebTransportSupported},Pl.uploadEvent({log:"trtcstats-"+JSON.stringify(h),userId:this.userId_}),this.log_.info("TrtcStats-"+JSON.stringify(h)),Pl.uploadEvent({log:"trtcadvancedstats-"+JSON.stringify(p),userId:this.userId_});case 26:case"end":return e.stop()}}),e,this,[[0,7],[9,16]])}))),function(){return C.apply(this,arguments)})},{key:"getVersion",value:function(){var e=this.version_.split(".");return 1e3*parseInt(e[0])+100*parseInt(e[1])+parseInt(e[2])}},{key:"doJoin",value:function(e){var t=this;return new Promise((function(n,r){if(!t.isSignalReady_)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:N_})});t.roomId_=e.roomId,Ul(e.role)||(t.role_=e.role);var i="";Ul(e.privateMapKey)||(i=e.privateMapKey),t.privateMapKey_=i,t.log_.info("Join() => joining room: ".concat(e.roomId," useStringRoomId: ").concat(t.useStringRoomId_," mode: ").concat(t.mode_," role: ").concat(t.role_));var a=t.signalInfo_,o={openid:a.openId,tinyid:a.tinyId,peerconnectionport:"",useStrRoomid:!!t.useStringRoomId_&&1,roomid:String(e.roomId),sdkAppID:String(t.sdkAppId_),socketid:a.socketId,userSig:t.userSig_,privMapEncrypt:i,privMap:"",relayip:a.relayInnerIp,dataport:a.dataPort,stunport:a.stunPort,checkSigSeq:a.checkSigSeq,pstnBizType:0,pstnPhoneNumber:null,recordId:t.recordId_,role:"user",jsSdkVersion:String(t.getVersion()),sdpSemantics:t.sdpSemantics_,browserVersion:Oc,closeLocalMedia:!0,trtcscene:"live"===t.mode_?$c:Xc,trtcrole:"anchor"===t.role_?Yc:Zc,bussinessInfo:t.bussinessInfo_,isAuxUser:t.isScreenShareOnly_,autoSubscribe:t.autoSubscribe_};t.joinTimeout_=setTimeout((function(){t.log_.error("join room timeout observed"),r(new Al({code:Cl.JOIN_ROOM_FAILED,message:Zv({key:L_})}))}),5e3),ih.emit(oh,{client:t});var s={AbilityOption:{GeneralLimit:{CPULimit:{uint32_CPU_num:String(navigator.hardwareConcurrency||0),str_CPU_name:String(navigator.platform),uint32_CPU_maxfreq:String(0),model:"",uint32_total_memory:String(0)},uint32_terminal_type:String(Fs?4:Os?2:Ns?3:pc?12:hc?5:fc?13:1),uint32_device_type:String(0),str_os_verion:Fs?"Android":Os?"iPhone":Ns?"iPad":pc?"Mac":hc?"Windows":fc?"Linux":"unknown",uint32_link_type:String(1),str_client_version:"4.11.11",uint32_net_type:String(ad[Ll()]),ua:navigator.userAgent,version:""}}},c=t.getSystemResult().detail,u=c.isH264EncodeSupported,d=c.isVp8EncodeSupported,l="";u?l="H264":d&&(l="VP8");var h={EncVideoCodec:l,EncVideoWidth:0,EncVideoHeight:0,EncVideoBr:"0",EncVideoFps:0,EncAudioCodec:"opus",EncAudioFS:0,EncAudioCh:0,EncAudioBr:"0"};s.AbilityOption.AVLimit=h,t.signalChannel_.sendWithReport(Zf,o,s),t.signalChannel_.once($f.JOIN_ROOM_RESULT,(function(e){clearTimeout(t.joinTimeout_),t.joinTimeout_=-1;var i=e.data.content.ret;ih.emit(sh,{client:t,code:i}),i?(t.log_.error("Join room failed result: "+i+" error: "+e.data.content.error),r(new Al({code:Cl.JOIN_ROOM_FAILED,extraCode:i,message:Zv({key:O_,data:{error:e.data.content.error}})}))):(t.isJoined_=!0,t.log_.info("Join room success, start heartbeat"),t.startHeartbeat(),t.badCaseDetector_&&t.badCaseDetector_.start(),t.syncUserList(),t.startSyncUserListInterval(),n())}))}))}},{key:"connectSignalBeforeReJoin",value:function(){var e=this;return new Promise((function(t,n){e.log_.warn("connectSignalBeforeReJoin() try to connect signal before reJoin"),e.isSignalReady_=!1,e.signalChannel_.close(),e.signalChannel_.once($f.CHANNEL_SETUP_SUCCESS,(function(n){e.log_.warn("connectSignalBeforeReJoin() signal setup successfully"),t()})),e.signalChannel_.once(Jf,(function(t){e.log_.warn("connectSignalBeforeReJoin() signal setup failed"),n(t)})),e.signalChannel_.connect()}))}},{key:"reJoin",value:(w=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isJoined_){e.next=3;break}return this.log_.warn("reJoin() you haven't join room yet, abort reJoin"),e.abrupt("return");case 3:return this.isJoined_=!1,e.prev=4,this.log_.warn("reJoin() try to reJoin room: ".concat(this.joinOptions_.roomId)),this.subscriptionManager_&&this.subscriptionManager_.markAllStream(),e.next=9,this.connectSignalBeforeReJoin();case 9:return e.next=11,this.doJoin(this.joinOptions_);case 11:if(this.log_.warn("reJoin() reJoin successfully"),Pl.logSuccessEvent({userId:this.userId_,eventType:uu}),this.checkConnectionsToReconnect(),e.prev=14,!this.uplinkConnection_||!this.localStream_||this.uplinkConnection_.getIsReconnecting()){e.next=18;break}return e.next=18,this.republish();case 18:e.next=22;break;case 20:e.prev=20,e.t0=e.catch(14);case 22:e.next=29;break;case 24:e.prev=24,e.t1=e.catch(4),this.log_.warn("reJoin() reJoin failed"+e.t1),Pl.logFailedEvent({userId:this.userId_,eventType:uu,error:e.t1}),this.emitter_.emit(dy,new Al({code:Cl.JOIN_ROOM_FAILED,message:Zv({key:M_,data:{roomId:this.joinOptions_.roomId}})}));case 29:case"end":return e.stop()}}),e,this,[[4,24],[14,20]])}))),function(){return w.apply(this,arguments)})},{key:"republish",value:(E=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.log_.warn("republish() try to re-publish localStream"),t=this.localStream_,e.next=5,this.doUnpublish(t);case 5:return e.next=7,this.publish(t);case 7:this.log_.warn("republish() re-publish localStream successfully"),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(0),this.log_.warn("republish() re-publish localStream failed "+e.t0),e.t0;case 14:case"end":return e.stop()}}),e,this,[[0,10]])}))),function(){return E.apply(this,arguments)})},{key:"leave",value:(T=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ih.emit(dh,{client:this}),t_(this.userId_,{eventId:jm,eventDesc:"leaving room",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=2,e.next=5,this.doHeartbeat();case 5:e.next=9;break;case 7:e.prev=7,e.t0=e.catch(2);case 9:this.doLeave(),ih.emit(hh,{client:this}),Pl.logSuccessEvent({userId:this.userId_,eventType:du}),t=Math.floor((zl()-this.joinedTimestamp_)/1e3),Pl.logSuccessEvent({userId:this.userId_,eventType:lu,delta:t});case 14:case"end":return e.stop()}}),e,this,[[2,7]])}))),function(){return T.apply(this,arguments)})},{key:"doLeave",value:function(){this.isJoined_&&(ih.emit(lh,{client:this}),this.log_.info("leave() => leaving room"),this.signalChannel_.send(em),this.reset())}},{key:"clearNetworkQuality",value:function(){this.networkQuality_&&(this.networkQuality_.stop(),this.networkQuality_=null)}},{key:"closeConnections",value:function(){var e=this;this.connections_.forEach((function(t){e.closeDownLink(t.getTinyId())}))}},{key:"destroy",value:function(){if(this.isJoined_)throw this.log_.warn("please call leave() before destroy() client"),new Al({code:Cl.INVALID_OPERATION,message:Zv({key:V_})});this.log_.info("destroying SignalChannel"),this.signalChannel_&&(this.signalChannel_.close(),this.signalChannel_=null)}},{key:"reset",value:function(){this.keyPointManager_&&this.keyPointManager_.prepareReport(),this.mixTranscodeManager_&&(this.mixTranscodeManager_.stop(),this.mixTranscodeManager_=null),this.publishCDNManager_&&(this.publishCDNManager_=null),this.userMap_.clear(),this.stopSyncUserListInterval(),this.stopHeartbeat(),this.closeConnections(),this.mutedStates_.clear(),this.clearNetworkQuality(),this.badCaseDetector_&&this.callDurationCalculator_&&this.uploadAllCallStats(),this.closeUplink(),this.isJoined_=!1,this.isSignalReady_=!1,this.destroy()}},{key:"startSyncUserListInterval",value:function(){"live"===this.mode_&&"audience"===this.role_&&-1===this.syncUserListInterval_&&(this.syncUserListInterval_=vg.setInterval(this.syncUserList.bind(this),1e4))}},{key:"stopSyncUserListInterval",value:function(){vg.clearInterval(this.syncUserListInterval_),this.syncUserListInterval_=-1}},{key:"syncUserList",value:(R=i(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getUserList();case 3:t=e.sent,0!==this.userMap_.size&&this.userMap_.forEach((function(e){t.findIndex((function(t){return t.userId===e.userId}))<0&&(n.log_.info("peer leave detected: ".concat(e.userId)),n.cleanUser({userId:e.userId,tinyId:e.tinyId}))})),t.forEach((function(e){var t=e.userId;n.userMap_.has(t)||t===n.userId_||(n.userMap_.set(t,e),n.emitter_.emit(ty,{userId:t}))})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),this.log_.warn("sync user list failed: "+e.t0);case 11:case"end":return e.stop()}}),e,this,[[0,8]])}))),function(){return R.apply(this,arguments)})},{key:"getUserList",value:function(){var e=this;return new Promise((function(t,n){e.signalChannel_.send(vm),e.signalChannel_.once($f.USER_LIST_RES,(function(e){var n=e.data.content.userlist.map((function(e){var t=e.userid,n=e.srctinyid,r=e.role;return new IS({userId:t,tinyId:n,role:r})}));t(n)})),setTimeout(n,2e3)}))}},{key:"publish",value:(I=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.setPublishState(sd),this.isPublishing_=!0,n=zl(),ih.emit(mh,{client:this,stream:t}),this.log_.info("publish() => publishing local stream"),r=new _S({userId:this.userId_,tinyId:this.tinyId_,client:this,isUplink:!0,signalChannel:this.signalChannel_}),t.setConnection(r),r.initialize(),r.on(Kg,(function(e){var t=e.getCode();t!==Cl.ICE_TRANSPORT_ERROR&&(t===Cl.UPLINK_RECONNECTION_FAILED&&o.closeUplink(),o.emitter_.emit(dy,e))})),e.prev=9,e.next=12,r.publish(t);case 12:this.localStream_=e.sent,this.localStream_.getBeautyStatus()&&this.log_.info("beauty stream is published successfully"),this.log_.info("local stream is published successfully"),this.isPublishing_=!1,t.setPublishState(cd),this.uplinkConnection_=r,i=zl(),a=i-n,Pl.logSuccessEvent({userId:this.userId_,eventType:hu}),Pl.logSuccessEvent({userId:this.userId_,eventType:pu,delta:a}),t.hasAudio()&&t_(this.userId_,{eventId:km,eventDesc:"publish audio track",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),t.hasVideo()&&t_(this.userId_,{eventId:Sm,eventDesc:"publish video track",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),this.networkQuality_&&this.networkQuality_.setUplinkConnection(this.uplinkConnection_),this.deviceDetector_&&this.deviceDetector_.setLocalStream(this.localStream_),ih.emit(xh,{localStream:this.localStream_,client:this}),this.notPublishWithoutH264Supported_=!1,e.next=39;break;case 30:throw e.prev=30,e.t0=e.catch(9),e.t0 instanceof Al&&e.t0.getCode()===Cl.NOT_SUPPORTED_H264&&(this.notPublishWithoutH264Supported_=!0),t.setPublishState(od),r.close(),this.log_.error("failed to publish stream "+e.t0),this.isPublishing_=!1,Pl.logFailedEvent({userId:this.userId_,eventType:hu,error:e.t0}),e.t0;case 39:case"end":return e.stop()}}),e,this,[[9,30]])}))),function(e){return I.apply(this,arguments)})},{key:"unpublish",value:(k=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.log_.info("unpublish() => unpublishing local stream"),e.prev=1,e.next=4,this.doUnpublish();case 4:Pl.logSuccessEvent({userId:this.userId_,eventType:fu}),e.next=11;break;case 7:throw e.prev=7,e.t0=e.catch(1),Pl.logFailedEvent({userId:this.userId_,eventType:fu,error:e.t0}),e.t0;case 11:case"end":return e.stop()}}),e,this,[[1,7]])}))),function(){return k.apply(this,arguments)})},{key:"doUnpublish",value:function(){var e=this;return this.signalChannel_.sendWaitForResponse({command:sm,commandDesc:"unpublish",responseCommand:$f.UNPUBLISH_RESULT}).then((function(){e.closeUplink()})).catch((function(){e.closeUplink()}))}},{key:"closeUplink",value:function(){this.uplinkConnection_&&(this.uplinkConnection_.getIsReconnecting()&&this.uplinkConnection_.stopReconnection(),this.uplinkConnection_.close(),this.uplinkConnection_=null,this.networkQuality_&&this.networkQuality_.setUplinkConnection(null),this.localStream_.hasAudio()&&t_(this.userId_,{eventId:Im,eventDesc:"unpublish audio track",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.hasVideo()&&t_(this.userId_,{eventId:bm,eventDesc:"unpublish video track",timestamp:_o(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.setConnection(null),this.localStream_=null,this.deviceDetector_&&this.deviceDetector_.setLocalStream(null))}},{key:"closeDownLink",value:function(e){var t=this.connections_.get(e);t&&(t.getIsReconnecting()&&t.stopReconnection(),this.subscriptionManager_&&this.subscriptionManager_.delete(t.getUserId()),t.close(),this.connections_.delete(e),this.mutedStates_.delete(e))}},{key:"subscribe",value:(S=i(regeneratorRuntime.mark((function e(t,n){var r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.log_.info("subscribe() => subscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream with options: ").concat(JSON.stringify(n))),Ul(n)&&(n={audio:!0,video:!0}),Ul(n.video)&&(n.video=!0),Ul(n.audio)&&(n.audio=!0),e.prev=4,r=t.getConnection(),ih.emit(Eh,{client:this,stream:t}),e.next=9,r.subscribe(t,n);case 9:this.subscriptionManager_&&this.subscriptionManager_.addSubscriptionRecord(t.getUserId(),t,n),this.notSubscribeWithoutH264Supported_=!1,Pl.logSuccessEvent({userId:this.userId_,eventType:mu}),e.next=22;break;case 14:throw e.prev=14,e.t0=e.catch(4),(i=e.t0 instanceof Al?e.t0.getCode():Cl.UNKNOWN)===Cl.NOT_SUPPORTED_H264&&(this.notSubscribeWithoutH264Supported_=!0),a=new Al({code:i,message:Zv({key:J_,data:{message:e.t0.message}})}),Pl.logFailedEvent({userId:this.userId_,eventType:mu,error:a}),this.log_.error(a),a;case 22:case"end":return e.stop()}}),e,this,[[4,14]])}))),function(e,t){return S.apply(this,arguments)})},{key:"unsubscribe",value:(v=i(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.log_.info("unsubscribe() => unsubscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream")),e.prev=1,n=t.getConnection(),e.next=5,n.unsubscribe(t);case 5:this.subscriptionManager_&&this.subscriptionManager_.addUnsubscriptionRecord(t.getUserId(),t),ih.emit(Ch,{client:this,stream:t}),Pl.logSuccessEvent({userId:this.userId_,eventType:_u}),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(1),Pl.logFailedEvent({userId:this.userId_,eventType:_u,error:e.t0}),e.t0;case 14:case"end":return e.stop()}}),e,this,[[1,10]])}))),function(e){return v.apply(this,arguments)})},{key:"switchRole",value:(_=i(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.role_!==t){e.next=2;break}return e.abrupt("return");case 2:if("audience"!==t||!this.localStream_){e.next=5;break}return e.next=5,this.unpublish(this.localStream_);case 5:return this.log_.info("switchRole() => switch role to: "+t),e.next=8,this.doSwitchRole(t);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return _.apply(this,arguments)})},{key:"doSwitchRole",value:function(e){var t=this;return new Promise((function(n,r){var i={trtcscene:"live"===t.mode_?$c:Xc,trtcrole:"anchor"===e?Yc:Zc};t.log_.info("switchRole signal data: "+JSON.stringify(i));var a=setTimeout((function(){t.log_.error("switchRole timeout observed"),t.signalChannel_.off($f.SWITCH_ROLE_RES,o),r(new Al({code:Cl.SWITCH_ROLE_FAILED,message:Zv({key:q_})}))}),5e3),o=function(i){clearTimeout(a);var o=i.data.content,s=o.errCode,c=o.errMsg;0===s?(t.role_=e,n()):(t.log_.error("switchRole failed, errCode: ".concat(s,", errMsg: ").concat(c)),r(new Al({code:Cl.SWITCH_ROLE_FAILED,message:Zv({key:K_,data:{errMsg:c,errCode:s}})})))};t.signalChannel_.once($f.SWITCH_ROLE_RES,o),t.signalChannel_.send(gm,i)}))}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"off",value:function(e,t,n){"*"===e?this.emitter_.removeAllListeners():this.emitter_.off(e,t,n)}},{key:"getRemoteMutedState",value:function(){var e=this,n=[];return this.mutedStates_.forEach((function(r,i,a){var o=e.connections_.get(i);o&&n.push(t({userId:o.getUserId()},r))})),n}},{key:"getTransportStats",value:(m=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t={rtt:0,downlinksRTT:{}},!this.uplinkConnection_){e.next=6;break}return e.next=4,this.stats_.getSenderStats(this.uplinkConnection_);case 4:n=e.sent,t.rtt=n.rtt;case 6:r=b(this.connections_),e.prev=7,r.s();case 9:if((i=r.n()).done){e.next=17;break}return(a=g(i.value,2))[0],o=a[1],e.next=13,this.stats_.getReceiverStats(o);case 13:s=e.sent,t.downlinksRTT[s.userId]=s.rtt;case 15:e.next=9;break;case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(7),r.e(e.t0);case 22:return e.prev=22,r.f(),e.finish(22);case 25:return e.abrupt("return",t);case 26:case"end":return e.stop()}}),e,this,[[7,19,22,25]])}))),function(){return m.apply(this,arguments)})},{key:"getLocalAudioStats",value:(f=i(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.audio.bytesSent,packetsSent:n.audio.packetsSent};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"getLocalVideoStats",value:(p=i(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0,framesEncoded:0,framesSent:0,frameWidth:0,frameHeight:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.video.bytesSent,packetsSent:n.video.packetsSent,framesEncoded:n.video.framesEncoded,framesSent:n.video.framesSent,frameWidth:n.video.frameWidth,frameHeight:n.video.frameHeight};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"getRemoteAudioStats",value:(h=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t={},n=b(this.connections_),e.prev=2,n.s();case 4:if((r=n.n()).done){e.next=12;break}return(i=g(r.value,2))[0],a=i[1],e.next=8,this.stats_.getReceiverStats(a);case 8:(o=e.sent).hasAudio&&(t[o.userId]={bytesReceived:o.audio.bytesReceived,packetsReceived:o.audio.packetsReceived,packetsLost:o.audio.packetsLost});case 10:e.next=4;break;case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(2),n.e(e.t0);case 17:return e.prev=17,n.f(),e.finish(17);case 20:return e.abrupt("return",t);case 21:case"end":return e.stop()}}),e,this,[[2,14,17,20]])}))),function(){return h.apply(this,arguments)})},{key:"getRemoteVideoStats",value:(l=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s,c=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=c.length>0&&void 0!==c[0]?c[0]:"main",n={},r=b(this.connections_),e.prev=3,r.s();case 5:if((i=r.n()).done){e.next=14;break}return(a=g(i.value,2))[0],o=a[1],e.next=9,this.stats_.getReceiverStats(o);case 9:s=e.sent,"main"===t&&s.hasVideo&&(n[s.userId]={bytesReceived:s.video.bytesReceived,packetsReceived:s.video.packetsReceived,packetsLost:s.video.packetsLost,framesDecoded:s.video.framesDecoded,frameWidth:s.video.frameWidth,frameHeight:s.video.frameHeight}),"auxiliary"===t&&s.hasAuxiliary&&(n[s.userId]={bytesReceived:s.auxiliary.bytesReceived,packetsReceived:s.auxiliary.packetsReceived,packetsLost:s.auxiliary.packetsLost,framesDecoded:s.auxiliary.framesDecoded,frameWidth:s.auxiliary.frameWidth,frameHeight:s.auxiliary.frameHeight});case 12:e.next=5;break;case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(3),r.e(e.t0);case 19:return e.prev=19,r.f(),e.finish(19);case 22:return e.abrupt("return",n);case 23:case"end":return e.stop()}}),e,this,[[3,16,19,22]])}))),function(){return l.apply(this,arguments)})},{key:"getSdpSemantics",value:function(){return this.sdpSemantics_}},{key:"getIceServers",value:function(){return 0===this.turnServers_.length&&this.schedule_.iceServers?this.schedule_.iceServers:this.turnServers_}},{key:"getConnections",value:function(){return this.connections_}},{key:"getMutedStates",value:function(){return this.mutedStates_}},{key:"startHeartbeat",value:function(){if(-1===this.heartbeat_){this.log_.info("startHeartbeat..."),this.heartbeat_=vg.setInterval(this.doHeartbeat.bind(this),2e3)}}},{key:"stopHeartbeat",value:function(){-1!==this.heartbeat_&&(this.log_.info("stopHeartbeat"),vg.clearInterval(this.heartbeat_),this.heartbeat_=-1,this.lastHeartBeatTime_=-1)}},{key:"doHeartbeat",value:(d=i(regeneratorRuntime.mark((function e(){var t,n,r,i,a,o,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.badCaseDetector_.getMonitorFreeze(),e.next=3,this.stats_.getStatsReport({uplinkConnection:this.uplinkConnection_,downlinkConnections:this.connections_,freezeMap:t});case 3:if(n=e.sent,ih.emit(ph,{client:this,stats:n}),this.badCaseDetector_.resetMonitor(),this.signalChannel_){e.next=8;break}return e.abrupt("return");case 8:r=this.signalChannel_.isConnected()?(c=this.userId_,u=void 0,(u=e_.get(c))?e_.delete(c):u=[],u):[],i={WebRTCQualityReq:n,eventList:r,sdkAppId:this.sdkAppId_,tinyid:this.tinyId_,roomid:this.roomId_,socketid:this.signalInfo_.socketId,clientip:this.signalInfo_.localIp,serverip:this.signalInfo_.relayIp,cpunumber:navigator.hardwareConcurrency||0,cpudevice:navigator.platform,devicename:navigator.platform,ostype:navigator.platform,mode:this.localStream_?this.localStream_.getScreen()?"screen":Ju:""},a=0,this.localStream_&&this.localStream_.getMediaStream()&&(o=this.localStream_.getMediaStream().getAudioTracks(),a=o.length>0&&o[0].muted?3:1),i.WebRTCQualityReq.AudioReportState.uint32_microphone_status=a,this.signalChannel_.send(tm,i),s=Date.now(),this.lastHeartBeatTime_>0&&s-this.lastHeartBeatTime_>1e4&&this.log_.warn("heartbeat took ".concat(s-this.lastHeartBeatTime_)),this.lastHeartBeatTime_=s;case 17:case"end":return e.stop()}var c,u}),e,this)}))),function(){return d.apply(this,arguments)})},{key:"onRemoteStreamAdded",value:function(e){var t=e.content,n=t.srcopenid,r=t.srctinyid;if(null!==n){this.userMap_.has(n)||(this.userMap_.set(n,new IS({userId:n,tinyId:r,role:"anchor"})),this.emitter_.emit(ty,{userId:n}));var i=this.connections_.get(r);if(i){if(i.getIsReconnecting())return;this.log_.warn("duplicated stream-added observed, rebuild the connection"),i.close(),this.connections_.delete(r)}var a={audio:t.audio,video:t.bigVideo,auxiliary:t.auxVideo};this.log_.info("remote peer [".concat(n,"] published stream. trackState: ").concat(JSON.stringify(a))),this.createDownlinkConnection({userId:n,tinyId:r,trackState:a})}else this.log_.warn("received null userId on stream added")}},{key:"createDownlinkConnection",value:function(e){var t=this,n=e.userId,r=e.tinyId,i=e.trackState,a=new vS({userId:n,tinyId:r,client:this,isUplink:!1,signalChannel:this.signalChannel_,autoSubscribe:this.autoSubscribe_,trackState:i});this.connections_.set(r,a),this.installDownlinkEvents(a,n,r),this.autoSubscribe_?(a.initialize(),a.connect().catch((function(){a.getMainStream()||!i.audio&&!i.video||t.initRemoteStream({type:"main",userId:n,downlinkConnection:a}),!a.getAuxStream()&&i.auxiliary&&t.initRemoteStream({type:"auxiliary",userId:n,downlinkConnection:a})}))):((i.audio||i.video)&&this.initRemoteStream({type:"main",userId:n,downlinkConnection:a}),i.auxiliary&&this.initRemoteStream({type:"auxiliary",userId:n,downlinkConnection:a}))}},{key:"initRemoteStream",value:function(e){var t=e.type,n=e.userId,r=e.downlinkConnection,i=new sS({type:t,userId:n,client:this});i.setConnection(r),r.setRemoteStream("main"===t?eu:tu,i),i.setIsStreamAddedEventEmitted(!0),ih.emit(Th,{client:this,stream:i}),this.emitter_.emit(Xg,{stream:i})}},{key:"installDownlinkEvents",value:function(e,t,n){var r=this;e.on(Jg,(function(e){e.stream.setIsStreamAddedEventEmitted(!0),ih.emit(Th,{client:r,stream:e.stream}),r.emitter_.emit(Xg,{stream:e.stream})})),e.on(zg,(function(e){e.stream.stop(),e.stream.setIsStreamAddedEventEmitted(!1),r.subscriptionManager_&&r.subscriptionManager_.deleteAutoRecoveryFlag(e.stream.getUserId(),e.stream.getType()),ih.emit(Ph,{client:r,stream:e.stream}),r.emitter_.emit($g,{stream:e.stream})})),e.on(Wg,(function(e){ih.emit(Ah,{client:r,stream:e.stream}),r.emitter_.emit(Yg,{stream:e.stream})})),e.on(qg,(function(e){ih.emit(wh,{client:r,stream:e.stream}),r.emitter_.emit(Zg,{stream:e.stream})})),e.on(Kg,(function(e){var t=e.getCode();t!==Cl.ICE_TRANSPORT_ERROR&&(t===Cl.DOWNLINK_RECONNECTION_FAILED&&r.closeDownLink(n),r.emitter_.emit(dy,e))}))}},{key:"onRemoteStreamRemoved",value:function(e){var t=e.content,n=t.srctinyid,r=t.srcopenid,i=this.connections_.get(n);i&&(void 0===r&&(r=i.getUserId()),this.log_.info("remote peer [".concat(r,"] unpublished stream")),this.closeDownLink(n))}},{key:"onPeerJoin",value:function(e){var t=e.data.content,n=t.srctinyid,r=t.userid,i=t.role;this.userMap_.has(r)||(this.userMap_.set(r,new IS({userId:r,tinyId:n,role:i})),this.emitter_.emit(ty,{userId:r}))}},{key:"onPeerLeave",value:function(e){var t=e.data.content,n=t.srctinyid,r=t.userid;this.log_.info("peer leave [".concat(r,"]")),this.cleanUser({userId:r,tinyId:n})}},{key:"cleanUser",value:function(e){var t=e.userId,n=e.tinyId;this.userMap_.delete(t),this.closeDownLink(n),this.emitter_.emit(ny,{userId:t})}},{key:"onPublishedUserList",value:function(e){var t=this;try{var n=e.content.userlist.map((function(e){return e.userid}));this.connections_.forEach((function(e){var r=e.getUserId(),i=e.getTinyId();n.findIndex((function(e){return e===r}))<0&&(t.log_.info("peer unpublished detected [".concat(r,"]")),t.closeDownLink(i))})),e.content.userlist.forEach((function(e){var n=e.userid,r=e.srctinyid,i=e.flag;if(n!==t.userId_){var a=!!(1&i),o=!!(8&i),s=!!(4&i);if(t.connections_.has(r)){var c=t.connections_.get(r).getTrackState(),u=c.audio,d=c.video,l=c.auxiliary;!d&&a&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:rd,kind:Ju}),!u&&o&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:rd,kind:Gu}),!l&&s&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:rd,kind:zu}),d&&!a&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:id,kind:Ju}),u&&!o&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:id,kind:Gu}),l&&!s&&ih.emit(Rh,{client:t,tinyId:r,userId:n,action:id,kind:zu})}else t.log_.info("peer published detected [".concat(n,"]")),t.onRemoteStreamAdded({content:{audio:o,auxVideo:s,bigVideo:a,srcopenid:n,srctinyid:r}})}}))}catch(mk){}}},{key:"onUpdateRemoteMuteStat",value:function(e){var t=this;e.content.userlist.forEach((function(e){var n=e.srctinyid,r=e.userid;if(0!==n&&n!==t.tinyId_){var i=t.connections_.get(n);if(i){var a=i.getMainStream();if(a&&a.getIsStreamAddedEventEmitted()){var o=!!(1&e.flag),s=!!(8&e.flag),c=!!(2&e.flag),u=!!(64&e.flag),d=!!(16&e.flag),l=t.mutedStates_.get(n);if(void 0===l)return t.mutedStates_.set(n,{hasAudio:s,hasVideo:o,hasSmall:c,audioMuted:u,videoMuted:d}),o?d?t.emitter_.emit(iy,{userId:r}):t.emitter_.emit(oy,{userId:r}):t.emitter_.emit(iy,{userId:r}),void(s?u?t.emitter_.emit(ry,{userId:r}):t.emitter_.emit(ay,{userId:r}):t.emitter_.emit(ry,{userId:r}));var h=!u&&s;(!l.audioMuted&&l.hasAudio)!==h&&(h?t.emitter_.emit(ay,{userId:r}):t.emitter_.emit(ry,{userId:r}));var p=!d&&o;(!l.videoMuted&&l.hasVideo)!==p&&(p?t.emitter_.emit(oy,{userId:r}):t.emitter_.emit(iy,{userId:r})),t.mutedStates_.set(n,{hasAudio:s,hasVideo:o,hasSmall:c,audioMuted:u,videoMuted:d})}}else t.mutedStates_.delete(n)}}))}},{key:"onClientBanned",value:function(e){this.emitter_.emit(sy,new Al({code:Cl.CLIENT_BANNED,message:Zv({key:Q_,data:{reason:e}})}))}},{key:"getEnv",value:function(){return this.env_}},{key:"getSubscriptionManager",value:function(){return this.subscriptionManager_}},{key:"startPublishCDNStream",value:(u=i(regeneratorRuntime.mark((function e(){var t,n=arguments;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=n.length>0&&void 0!==n[0]?n[0]:{},this.localStream_){e.next=3;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:X_})});case 3:return this.log_.info("startPublishCDNStream params: ".concat(JSON.stringify(t))),e.next=6,this.publishCDNManager_.startPublishTencentCDN(t);case 6:if(!(t.appId&&t.bizId&&t.url)){e.next=9;break}return e.next=9,this.publishCDNManager_.startPublishGivenCDN(t);case 9:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})},{key:"stopPublishCDNStream",value:(c=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.publishCDNManager_.getIsPublishingTencentCDN()){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:$_})});case 2:return this.log_.info("stopPublishCDNStream"),e.next=5,this.publishCDNManager_.stopPublishTencentCDN();case 5:if(!this.publishCDNManager_.getIsPublishingGivenCDN()){e.next=8;break}return e.next=8,this.publishCDNManager_.stopPublishGivenCDN();case 8:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"startMixTranscode",value:(o=i(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isJoined_&&this.mixTranscodeManager_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:tv})});case 2:return Ul(t.mode)&&(t.mode=Lu.MANUAL),e.prev=3,this.log_.info("startMixTranscode with config ".concat(JSON.stringify(t))),Pl.uploadEvent({log:"mix-transcode-mode:".concat(t.mode),userId:this.userId_}),e.next=8,this.mixTranscodeManager_.startMixTranscode(t);case 8:Pl.logSuccessEvent({userId:this.userId_,eventType:Au}),e.next=15;break;case 11:throw e.prev=11,e.t0=e.catch(3),Pl.logFailedEvent({userId:this.userId_,eventType:Au,error:e.t0}),e.t0;case 15:case"end":return e.stop()}}),e,this,[[3,11]])}))),function(e){return o.apply(this,arguments)})},{key:"stopMixTranscode",value:(r=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isJoined_&&this.mixTranscodeManager_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:nv})});case 2:return e.prev=2,e.next=5,this.mixTranscodeManager_.stopMixTranscode();case 5:Pl.logSuccessEvent({userId:this.userId_,eventType:Pu}),e.next=12;break;case 8:throw e.prev=8,e.t0=e.catch(2),Pl.logFailedEvent({userId:this.userId_,eventType:Pu,error:e.t0}),e.t0;case 12:case"end":return e.stop()}}),e,this,[[2,8]])}))),function(){return r.apply(this,arguments)})},{key:"getSystemResult",value:function(){return this.checkSystemResult_}},{key:"enableAudioVolumeEvaluation",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!jl(t))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:rv})});if(this.log_.info("enableAudioVolumeEvaluation with interval: "+t),t<=0)return this.enableAudioVolumeEvaluation_=!1,vg.clearInterval(this.audioVolumeIntervalId_),void(this.audioVolumeIntervalId_=null);t=Math.floor(Math.max(t,16)),this.audioVolumeIntervalId_&&(vg.clearInterval(this.audioVolumeIntervalId_),this.audioVolumeIntervalId_=null),this.enableAudioVolumeEvaluation_=!0,this.audioVolumeIntervalId_=vg.setInterval((function(){var t=[];if(e.localStream_){var n=Math.floor(100*e.localStream_.getAudioLevel());t.push({userId:e.userId_,audioVolume:n,stream:e.localStream_})}e.connections_.forEach((function(e){var n=e.getSubscribedMainStream();if(n){var r=Math.floor(100*n.getAudioLevel());t.push({userId:e.getUserId(),audioVolume:r,stream:n})}})),e.emitter_.emit(uy,{result:t})}),t,n)}},{key:"uploadAllCallStats",value:function(){var e=this;this.callDurationCalculator_.getDurationMap().forEach((function(t,n){var r={userId:t.userId,type:t.type,duration:e.callDurationCalculator_.getDuration(n,Ju),dataFreeze:e.badCaseDetector_.getDataFreezeDuration(n).dataFreeze,renderFreeze:e.badCaseDetector_.getRenderFreezeDuration(n).renderFreeze};Pl.uploadEvent({log:"callStats-"+JSON.stringify(r),userId:e.userId_})})),this.badCaseDetector_.stop(),this.callDurationCalculator_.reset()}},{key:"enableSmallStream",value:(n=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isPublished()&&!this.isPublishing_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:iv})});case 2:if(!Gg()){e.next=7;break}this.setIsEnableSmallStream(!0),this.log_.info("SmallStream successfully enabled"),e.next=8;break;case 7:throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:ov})});case 8:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"disableSmallStream",value:(e=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isPublished()&&!this.isPublishing_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:av})});case 2:this.setIsEnableSmallStream(!1),this.log_.info("SmallStream successfully disabled");case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"setSmallStreamProfile",value:function(e){var t=this;e&&e.framerate&&(e.frameRate=e.framerate),Object.keys(this.smallStreamConfig_).forEach((function(n){e[n]&&(t.smallStreamConfig_[n]=e[n])})),this.log_.info("setSmallStreamProfile: bitrate=".concat(this.smallStreamConfig_.bitrate,", frameRate=").concat(this.smallStreamConfig_.frameRate,", height=").concat(this.smallStreamConfig_.height,", width=").concat(this.smallStreamConfig_.width));var n=this.smallStreamConfig_,r=n.width,i=n.height,a=n.bitrate,o=n.frameRate;if(r<0||i<0||a<0||o<0)throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:sv})})}},{key:"setRemoteVideoStreamType",value:function(e,t){switch(this.log_.info("setRemoteVideoStreamType: streamType=".concat(e.getType(),", set ").concat(t)),t){case"big":case"small":this.changeVideoType(e,t)}}},{key:"changeVideoType",value:function(e,t){if(!(e instanceof sS))throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:cv})});if(!e.getConnection())throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:G_})});var n=this.getRemoteMutedState().filter((function(t){return t.userId===e.getUserId()}))[0];if("small"===t&&n&&!n.hasSmall)throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:uv})});this.log_.info("change video type: streamType=".concat(e.getType(),", set ").concat(t));var r=e.getConnection().getTinyId(),i=1;"big"===t&&(i=0),this.signalChannel_.send(ym,{type:i,srctinyid:r})}},{key:"setIsEnableSmallStream",value:function(e){this.isEnableSmallStream_=e}},{key:"getIsEnableSmallStream",value:function(){return this.isEnableSmallStream_}},{key:"smallStreamConfig",get:function(){return this.smallStreamConfig_}},{key:"isPublished",value:function(){return!!this.localStream_}},{key:"getUplinkConnection",value:function(){return this.uplinkConnection_}},{key:"getLocalStream",value:function(){return this.localStream_}},{key:"getMode",value:function(){return this.mode_}},{key:"getBadCaseDetector",value:function(){return this.badCaseDetector_}},{key:"getCallDurationCalculator",value:function(){return this.callDurationCalculator_}},{key:"getIsJoined",value:function(){return this.isJoined_}},{key:"getAllConnections",value:function(){var e=y(this.connections_.values());return this.uplinkConnection_&&e.push(this.uplinkConnection_),e}},{key:"isRelayMaybeFailed",value:function(){var e=this.getAllConnections();if(0===e.length)return!1;for(var t=0;t<e.length;t++)if(e[t].getReconnectionCount()<6)return!1;return!0}},{key:"getUseStringRoomId",value:function(){return this.useStringRoomId_}},{key:"checkConnectionsToReconnect",value:function(){var e=this;this.getAllConnections().forEach((function(t){if(!t.getIsReconnecting()){var n=t.getPeerConnection();n&&"closed"===n.connectionState&&(e.log_.warn("[".concat(t.getUserId(),"] pc is closed but not reconnect")),t.startReconnection())}}))}},{key:"getEnableAutoPlayDialog",value:function(){return this.enableAutoPlayDialog_}}]),Client}()).prototype,"join",[PS,xS],Object.getOwnPropertyDescriptor(GS.prototype,"join"),GS.prototype),I(GS.prototype,"leave",[DS],Object.getOwnPropertyDescriptor(GS.prototype,"leave"),GS.prototype),I(GS.prototype,"publish",[NS,LS],Object.getOwnPropertyDescriptor(GS.prototype,"publish"),GS.prototype),I(GS.prototype,"unpublish",[OS,MS],Object.getOwnPropertyDescriptor(GS.prototype,"unpublish"),GS.prototype),I(GS.prototype,"subscribe",[VS],Object.getOwnPropertyDescriptor(GS.prototype,"subscribe"),GS.prototype),I(GS.prototype,"unsubscribe",[US],Object.getOwnPropertyDescriptor(GS.prototype,"unsubscribe"),GS.prototype),I(GS.prototype,"switchRole",[FS,jS],Object.getOwnPropertyDescriptor(GS.prototype,"switchRole"),GS.prototype),I(GS.prototype,"startPublishCDNStream",[BS],Object.getOwnPropertyDescriptor(GS.prototype,"startPublishCDNStream"),GS.prototype),I(GS.prototype,"startMixTranscode",[HS],Object.getOwnPropertyDescriptor(GS.prototype,"startMixTranscode"),GS.prototype),GS),XS=df({retryFunction:function(){var e=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Og()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,$S(t);case 4:if(n=e.sent,pf.info("getUserMedia with constraints: "+JSON.stringify(n)),!n.audio){e.next=13;break}return e.next=9,fk.getMicrophones();case 9:if(r=e.sent,pf.info("microphones: ".concat(JSON.stringify(r))),0!==r.length){e.next=13;break}throw new Al({code:Cl.DEVICE_NOT_FOUND,message:Zv({key:Kv})});case 13:if(!n.video){e.next=20;break}return e.next=16,fk.getCameras();case 16:if(i=e.sent,pf.info("cameras: ".concat(JSON.stringify(i))),0!==i.length){e.next=20;break}throw new Al({code:Cl.DEVICE_NOT_FOUND,message:Zv({key:Qv})});case 20:return e.next=22,navigator.mediaDevices.getUserMedia(n);case 22:return e.abrupt("return",e.sent);case 23:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),settings:{retries:3,timeout:500},onError:function(e,t,n){"NotReadableError"===e.name?t():n(e)},onRetrying:function(e){pf.warn("getUserMedia NotReadableError observed, retrying [".concat(e,"/3]"))}});function $S(e){return YS.apply(this,arguments)}function YS(){return(YS=i(regeneratorRuntime.mark((function e(n){var r,i,a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r={echoCancellation:n.echoCancellation,autoGainControl:n.autoGainControl,noiseSuppression:n.noiseSuppression},n.audio){e.next=5;break}r=!1,e.next=15;break;case 5:if(r_(n.microphoneId)){e.next=9;break}r=t({deviceId:{exact:n.microphoneId},sampleRate:n.sampleRate,channelCount:n.channelCount},r),e.next=15;break;case 9:return r=t({sampleRate:n.sampleRate,channelCount:n.channelCount},r),e.next=12,fk.getMicrophones();case 12:i=e.sent,(a=i.filter((function(e){return e.deviceId.length>0}))).length>0&&(r.deviceId={exact:a[0].deviceId});case 15:return o={},o=!Ul(n.facingMode)&&n.video?{facingMode:n.facingMode,width:n.width,height:n.height,frameRate:n.frameRate}:!r_(n.cameraId)&&n.video?{deviceId:{exact:n.cameraId},width:n.width,height:n.height,frameRate:n.frameRate}:!!n.video&&(!!Ul(n.width)||{width:n.width,height:n.height,frameRate:n.frameRate}),e.abrupt("return",{audio:r,video:o});case 18:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var ZS=function(){var e=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o,s,c,u,d;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Og()){e.next=2;break}return e.abrupt("return");case 2:if(n=null,!(Ac&&Pc<74||Dc)){e.next=27;break}return r=tk(t),pf.info("getDisplayMedia with constraints: "+JSON.stringify(r)),e.next=8,navigator.mediaDevices.getDisplayMedia(r);case 8:if(i=e.sent,!t.screenAudio){e.next=14;break}return pf.warn("Your browser not support capture system audio"),e.abrupt("return",i);case 14:if(!t.audio){e.next=24;break}return a=ek(t),pf.info("getUserMedia with constraints: "+JSON.stringify(a)),e.next=19,navigator.mediaDevices.getUserMedia(a);case 19:return n=e.sent,i.addTrack(n.getAudioTracks()[0]),e.abrupt("return",i);case 24:return e.abrupt("return",i);case 25:e.next=53;break;case 27:if(!t.screenAudio){e.next=37;break}return t.audioConstraints={echoCancellation:!0,noiseSuppression:!0,sampleRate:44100},o=tk(t),pf.info("getDisplayMedia with constraints: "+JSON.stringify(o)),e.next=33,navigator.mediaDevices.getDisplayMedia(o);case 33:return s=e.sent,e.abrupt("return",s);case 37:return c=tk(t),pf.info("getDisplayMedia with constraints: "+JSON.stringify(c)),e.next=41,navigator.mediaDevices.getDisplayMedia(c);case 41:if(u=e.sent,!t.audio){e.next=52;break}return d=ek(t),pf.info("getUserMedia with constraints: "+JSON.stringify(d)),e.next=47,navigator.mediaDevices.getUserMedia(d);case 47:return n=e.sent,u.addTrack(n.getAudioTracks()[0]),e.abrupt("return",u);case 52:return e.abrupt("return",u);case 53:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();function ek(e){var t={echoCancellation:e.echoCancellation,autoGainControl:e.autoGainControl,noiseSuppression:e.noiseSuppression,sampleRate:e.sampleRate,channelCount:e.channelCount};return Ul(e.microphoneId)||(t.deviceId={exact:e.microphoneId}),{audio:t,video:!1}}function tk(e){var t={},n={width:e.width,height:e.height,frameRate:e.frameRate};return Ul(e.screenSource)||(n.displaySurface=e.screenSource),t.video=n,Ul(e.audioConstraints)||(t.audio=e.audioConstraints),t}var nk=new Map;nk.set("120p",{width:160,height:120,frameRate:15,bitrate:200}),nk.set("180p",{width:320,height:180,frameRate:15,bitrate:350}),nk.set("240p",{width:320,height:240,frameRate:15,bitrate:400}),nk.set("360p",{width:640,height:360,frameRate:15,bitrate:800}),nk.set("480p",{width:640,height:480,frameRate:15,bitrate:900}),nk.set("720p",{width:1280,height:720,frameRate:15,bitrate:1500}),nk.set("1080p",{width:1920,height:1080,frameRate:15,bitrate:2e3}),nk.set("1440p",{width:2560,height:1440,frameRate:30,bitrate:4860}),nk.set("4K",{width:3840,height:2160,frameRate:30,bitrate:9e3});var rk=new Map;rk.set("480p",{width:640,height:480,frameRate:5,bitrate:900}),rk.set("480p_2",{width:640,height:480,frameRate:30,bitrate:1e3}),rk.set("720p",{width:1280,height:720,frameRate:5,bitrate:1200}),rk.set("720p_2",{width:1280,height:720,frameRate:30,bitrate:3e3}),rk.set("1080p",{width:1920,height:1080,frameRate:5,bitrate:1600}),rk.set("1080p_2",{width:1920,height:1080,frameRate:30,bitrate:4e3});var ik,ak,ok=new Map;ok.set("standard",{sampleRate:48e3,channelCount:1,bitrate:40}),ok.set("standard-stereo",{sampleRate:48e3,channelCount:2,bitrate:64}),ok.set("high",{sampleRate:48e3,channelCount:1,bitrate:192}),ok.set("high-stereo",{sampleRate:48e3,channelCount:2,bitrate:192});var sk,ck,uk,dk=(ik=rS.apply(void 0,y(tS.LOCAL_STREAM.switchDevice)),I((ak=function(e){u(LocalStream,e);var r,o,c,l,h,p,f,m,g=_(LocalStream);function LocalStream(e){var n;a(this,LocalStream);var r=t(t({},e),{isRemote:!1,type:"local"});return e.screen&&(r.mirror=!1),(n=g.call(this,r)).name_=hd,n.client_=null,n.video_=e.video,n.audio_=e.audio,n.cameraId_=e.cameraId,n.cameraGroupId_="",n.facingMode_=e.facingMode,n.microphoneId_=e.microphoneId,n.microphoneGroupId_="",n.videoSource_=e.videoSource,n.audioSource_=e.audioSource,n.screen_=e.screen,n.screenSource_=e.screenSource,n.screenAudio_=e.screenAudio,n.audioProfile_={echoCancellation:!!Ul(e.echoCancellation)||e.echoCancellation,autoGainControl:!!Ul(e.autoGainControl)||e.autoGainControl,noiseSuppression:!!Ul(e.noiseSuppression)||e.noiseSuppression,sampleRate:48e3,channelCount:1,bitrate:40},n.videoProfile_={width:640,height:480,frameRate:15,bitrate:900},n.screenProfile_={width:1920,height:1080,frameRate:5,bitrate:1600},n.videoBitrate_=n.screen_?1600:900,n.videoSetting_=null,n.mutedFlag_=0,n.beautyStatus_=!1,n.recoverCaptureCount_=0,n.initState(),n.canvas_=null,n.canvasInterval_=-1,n.log_.info("stream created: "+n.id_),n}return s(LocalStream,[{key:"initState",value:function(){this.isAddingTrack_=!1,this.isRemovingTrack_=!1,this.setIsReadyToPublish(!1),this.setPublishState(od)}},{key:"installEvents",value:function(){v(d(LocalStream.prototype),"installEvents",this).call(this),ih.on(xh,this.onStreamPublished,this),ih.on(jh,this.onVideoTrackStopped,this),ih.on(Oh,this.onVideoTrackStopped,this),ih.on(Hh,this.onAudioTrackStopped,this),ih.on(Bh,this.onAudioTrackStopped,this)}},{key:"uninstallEvents",value:function(){v(d(LocalStream.prototype),"uninstallEvents",this).call(this),ih.off(xh,this.onStreamPublished,this),ih.off(jh,this.onVideoTrackStopped,this),ih.off(Oh,this.onVideoTrackStopped,this),ih.off(Hh,this.onAudioTrackStopped,this),ih.off(Bh,this.onAudioTrackStopped,this)}},{key:"initialize",value:function(){var e=this;return new Promise((function(t,n){if(Mg())n(new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Bv})}));else{if(Ul(e.audio_)){var r=new MediaStream;return Ul(e.audioSource_)||(r.addTrack(e.audioSource_),e.updateAudioPlayingState(!0)),Ul(e.videoSource_)||(r.addTrack(e.videoSource_),e.updateVideoPlayingState(!0)),e.setMediaStream(r),Pl.logSuccessEvent({userId:e.client_?e.client_.getUserId():e.userId_,eventType:Ru,kind:"custom"}),e.setIsReadyToPublish(!0),t()}e.screen_?(e.log_.info("initialize stream audio: "+e.audio_+" screenAudio: "+e.screenAudio_+" screen: "+e.screen_),ZS({audio:e.audio_,screenAudio:e.screenAudio_,microphoneId:e.microphoneId_,screenSource:e.screenSource_,width:e.screenProfile_.width,height:e.screenProfile_.height,frameRate:e.screenProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount,autoGainControl:e.audioProfile_.autoGainControl,noiseSuppression:e.audioProfile_.noiseSuppression,echoCancellation:e.audioProfile_.echoCancellation}).then((function(n){return e.setMediaStream(n),e.updateAudioPlayingState(e.audio_||e.screenAudio_),e.updateVideoPlayingState(!0),e.listenForScreenSharingStopped(e.getVideoTrack()),e.setVideoContentHint("detail"),e.updateDeviceIdInUse(),e.setIsReadyToPublish(!0),Pl.logSuccessEvent({userId:e.client_?e.client_.getUserId():e.userId_,eventType:Ru,kind:"getDisplayMedia"}),t()})).catch((function(t){Pl.logFailedEvent({userId:e.client_?e.client_.getUserId():e.userId_,eventType:Ru,kind:"getDisplayMedia",error:t}),e.log_.error("getDisplayMedia error observed "+t),n(t)}))):(ih.emit(Dh,{stream:e,audio:e.audio_,video:e.video_}),e.log_.info("initialize stream audio: "+e.audio_+" video: "+e.video_),XS({audio:e.audio_,video:e.video_,facingMode:e.facingMode_,cameraId:e.cameraId_,microphoneId:e.microphoneId_,width:e.videoProfile_.width,height:e.videoProfile_.height,frameRate:e.videoProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount,autoGainControl:e.audioProfile_.autoGainControl,noiseSuppression:e.audioProfile_.noiseSuppression,echoCancellation:e.audioProfile_.echoCancellation}).then((function(n){return ih.emit(Nh,{stream:e,audio:e.audio_,video:e.video_}),"getSettings"in MediaStreamTrack.prototype&&(e.videoSetting_=n.getVideoTracks().length>0&&n.getVideoTracks()[0].getSettings()),e.setMediaStream(n),e.updateAudioPlayingState(e.audio_),e.updateVideoPlayingState(e.video_),e.updateDeviceIdInUse(),e.log_.info("gotStream hasAudio: "+e.hasAudio()+" hasVideo: "+e.hasVideo()),e.setIsReadyToPublish(!0),Pl.logSuccessEvent({userId:e.client_?e.client_.getUserId():e.userId_,eventType:Ru,kind:"getUserMedia"}),t()})).catch((function(t){ih.emit(Lh,{stream:e,audio:e.audio_,video:e.video_,error:t}),Pl.logFailedEvent({userId:e.client_?e.client_.getUserId():e.userId_,eventType:Ru,kind:"getUserMedia",error:t}),e.log_.error("getUserMedia error observed "+t),n(t)})))}}))}},{key:"listenForScreenSharingStopped",value:function(e){var t=this;e.addEventListener("ended",(function(e){t.log_.info("screen sharing was stopped because the video track is ended"),t.emitter_.emit(hy)}))}},{key:"muteAudio",value:function(){var e=v(d(LocalStream.prototype),"muteAudio",this).call(this);return e&&(this.log_.info("localStream mute audio"),this.sendMutedFlag(Gu,!0)),e}},{key:"muteVideo",value:function(){var e=v(d(LocalStream.prototype),"muteVideo",this).call(this);return e&&(this.log_.info("localStream mute video"),this.sendMutedFlag(Ju,!0)),e}},{key:"unmuteAudio",value:function(){var e=v(d(LocalStream.prototype),"unmuteAudio",this).call(this);return e&&(this.log_.info("localStream unmute audio"),this.sendMutedFlag(Gu,!1)),e}},{key:"unmuteVideo",value:function(){var e=v(d(LocalStream.prototype),"unmuteVideo",this).call(this);return e&&(this.log_.info("localStream unmute video"),this.sendMutedFlag(Ju,!1)),e}},{key:"sendMutedFlag",value:function(e,t){this.setMutedFlag(e,t);var n=this.getConnection();if(n){n.sendMutedFlag(this.mutedFlag_);var r=n.getUserId(),i=n.getTinyId(),a="".concat(t?Ku:Qu," local ").concat(e," track");t_(r,{eventId:e===Gu?t?Rm:Em:t?Tm:wm,eventDesc:a,timestamp:_o(),userId:r,tinyId:i})}}},{key:"setMutedFlag",value:function(e,t){e===Gu?t?this.mutedFlag_|=4:this.mutedFlag_&=-5:t?this.mutedFlag_|=1:this.mutedFlag_&=-2,this.log_.info("set ".concat(e," muted state: [").concat(t?Ku:Qu,"]"))}},{key:"setAudioProfile",value:function(e){var r;"object"===n(e)?r=e:void 0===(r=ok.get(e))&&(r=ok.get("standard")),this.log_.info("setAudioProfile: "+JSON.stringify(r)),this.audioProfile_=t(t({},this.audioProfile_),r)}},{key:"setVideoProfile",value:(m=i(regeneratorRuntime.mark((function e(n){var r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.connection_||Lg()){e.next=2;break}throw new Al({code:Cl.NOT_SUPPORTED,message:Zv({key:Gv})});case 2:if(Ol(n)?r=t(t({},this.videoProfile_),n):Fl(n)&&(r=nk.get(n),Ul(r)&&(r=nk.get("480p"))),this.log_.info("setVideoProfile "+JSON.stringify(r)),!(i=this.getVideoTrack())){e.next=8;break}return e.next=8,i.applyConstraints(r);case 8:if(this.videoBitrate_===r.bitrate){e.next=13;break}if(!this.connection_){e.next=12;break}return e.next=12,this.connection_.setBandwidth(r.bitrate,Ju);case 12:this.videoBitrate_=r.bitrate;case 13:this.videoProfile_=r;case 14:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"getVideoBitrate",value:function(){return this.videoBitrate_}},{key:"getAudioBitrate",value:function(){return this.audioProfile_.bitrate}},{key:"setScreenProfile",value:function(e){var t=e;"object"!==n(e)&&void 0===(t=rk.get(e))&&(t=rk.get("1080p")),this.log_.info("setScreenProfile "+JSON.stringify(e)),this.screenProfile_=t,this.videoBitrate_=t.bitrate}},{key:"getVideoProfile",value:function(){return this.screen_?this.screenProfile_:this.videoProfile_}},{key:"getAudioProfile",value:function(){return this.audioProfile_}},{key:"setVideoContentHint",value:function(e){var t=this.getVideoTrack();t&&"contentHint"in t&&(this.log_.info("set video track contentHint to: "+e),t.contentHint=e,t.contentHint!==e&&this.log_.warn("Invalid video track contentHint: "+e))}},{key:"switchDevice",value:(f=i(regeneratorRuntime.mark((function e(t,n){var r,i,a,o,s,c,u,d,l,h,p,f,m,_,v,g,y;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(r=t===Gu)){e.next=8;break}if(this.microphoneId_!==n){e.next=4;break}return e.abrupt("return");case 4:this.microphoneId_=n,this.audio_=!0,e.next=12;break;case 8:if(this.cameraId_!==n){e.next=10;break}return e.abrupt("return");case 10:n===Wu||n===qu?this.facingMode_=n:this.cameraId_=n,this.video_=!0;case 12:if(this.getMediaStream()){e.next=14;break}return e.abrupt("return");case 14:return this.setIsReadyToPublish(!1),this.log_.info("switchDevice "+t+" to: "+n),r||((i=this.getVideoTrack())&&i.stop(),yc&&(a=this.getAudioTrack())&&(this.log_.info("stop audio track first in huawei env"),a.stop())),r&&(o=this.getAudioTrack(),s=this.getMicrophoneTrackMixed(),o&&o.stop(),s&&s.stop()),e.next=20,XS({audio:this.audio_&&t===Gu||yc,video:this.video_&&t===Ju,facingMode:n===Wu||n===qu?n:void 0,cameraId:this.cameraId_,microphoneId:this.microphoneId_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 20:if(c=e.sent,u=null,!r){e.next=27;break}(d=c.getAudioTracks()[0])&&this.isAudioTrackMixed()?(l=this.getAudioTrack(),h=fk.AudioMixerPlugin.getAudioTrackMap(),p=fk.AudioMixerPlugin.mix({targetTrack:d,sourceList:h.get(l.id).sourceList,trackList:h.get(l.id).trackList}),u=p):u=d,e.next=36;break;case 27:if(!(u=c.getVideoTracks()[0])||!this.isVideoTrackBeautified()){e.next=32;break}return e.next=31,this.generateBeautyTrack(u);case 31:u=e.sent;case 32:if(!(f=c.getAudioTracks()[0])||!yc){e.next=36;break}return e.next=36,this.replaceTrack_(f);case 36:return e.next=38,this.replaceTrack_(u);case 38:this.updateDeviceIdInUse(),(m=this.getConnection())&&(_=m.getUserId(),v=m.getTinyId(),g=xm,y="switch camera",r&&(g=Dm,y="switch microphone"),t_(_,{eventId:g,eventDesc:y,timestamp:_o(),userId:_,tinyId:v})),this.log_.info("switch ".concat(r?"microphone":"camera"," success ")),this.setIsReadyToPublish(!0);case 43:case"end":return e.stop()}}),e,this)}))),function(e,t){return f.apply(this,arguments)})},{key:"addTrack",value:(p=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isAddingTrack_){e.next=2;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:fv})});case 2:if(!this.isRemovingTrack_){e.next=4;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:mv})});case 4:if(this.publishState_!==sd){e.next=6;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:_v})});case 6:if(n=this.getMediaStream()){e.next=9;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:vv})});case 9:if(!(t.kind===Gu&&n.getAudioTracks().length>0||t.kind===Ju&&n.getVideoTracks().length>0)){e.next=11;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:gv})});case 11:if(t.kind===Ju&&"getSettings"in MediaStreamTrack.prototype&&(r=t.getSettings(),!this.videoSetting_||r.width===this.videoSetting_.width&&r.height===this.videoSetting_.height||this.log_.warn("video resolution of the track (".concat(r.width," x ").concat(r.height,") shall be kept the same as the previous: ").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,". It may cause abnormal Cloud Recording."))),e.prev=12,this.isAddingTrack_=!0,this.keepMuteState(t),n.addTrack(t),!(i=this.getConnection())){e.next=20;break}return e.next=20,i.addTrack(t);case 20:t.kind===Gu?(this.audio_=!0,this.updateAudioPlayingState(!0)):(this.video_=!0,this.updateVideoPlayingState(!0)),this.isAddingTrack_=!1,e.next=29;break;case 24:throw e.prev=24,e.t0=e.catch(12),n.removeTrack(t),this.isAddingTrack_=!1,e.t0;case 29:case"end":return e.stop()}}),e,this,[[12,24]])}))),function(e){return p.apply(this,arguments)})},{key:"removeTrack",value:(h=i(regeneratorRuntime.mark((function e(t){var n,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.kind!==Gu){e.next=2;break}throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:yv})});case 2:if(!this.isAddingTrack_){e.next=4;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Sv})});case 4:if(!this.isRemovingTrack_){e.next=6;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:kv})});case 6:if(this.publishState_!==sd){e.next=8;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:bv})});case 8:if(n=this.getMediaStream()){e.next=11;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:vv})});case 11:if(-1!==n.getTracks().indexOf(t)){e.next=13;break}throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Iv})});case 13:if(1!==n.getTracks().length){e.next=15;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:Rv})});case 15:if(e.prev=15,this.isRemovingTrack_=!0,!(r=this.getConnection())){e.next=21;break}return e.next=21,r.removeTrack(t);case 21:n.removeTrack(t),t.kind===Gu?(this.audio_=!1,this.updateAudioPlayingState(!1)):(this.video_=!1,this.updateVideoPlayingState(!1)),this.isRemovingTrack_=!1,e.next=30;break;case 26:throw e.prev=26,e.t0=e.catch(15),this.isRemovingTrack_=!1,e.t0;case 30:case"end":return e.stop()}}),e,this,[[15,26]])}))),function(e){return h.apply(this,arguments)})},{key:"replaceTrack",value:(l=i(regeneratorRuntime.mark((function e(t){var n,r,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.getMediaStream()){e.next=3;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:pv})});case 3:if(this.publishState_!==sd){e.next=5;break}throw new Al({code:Cl.INVALID_OPERATION,message:Zv({key:hv})});case 5:if(!(t.kind===Gu&&n.getAudioTracks().length<=0||t.kind===Ju&&n.getVideoTracks().length<=0)){e.next=7;break}throw new Al({code:Cl.INVALID_PARAMETER,message:Zv({key:Tv,data:t})});case 7:if(t.kind===Ju&&"getSettings"in MediaStreamTrack.prototype&&(r=t.getSettings(),!this.videoSetting_||r.width===this.videoSetting_.width&&r.height===this.videoSetting_.height||this.log_.warn("video resolution of the track (".concat(r.width," x ").concat(r.height,") shall be kept the same as the previous: ").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,". It may cause abnormal Cloud Recording."))),this.keepMuteState(t),t.kind===Gu?(n.removeTrack(n.getAudioTracks()[0]),n.addTrack(t),v(d(LocalStream.prototype),"restartAudio",this).call(this)):(n.removeTrack(n.getVideoTracks()[0]),n.addTrack(t),v(d(LocalStream.prototype),"restartVideo",this).call(this)),!(i=this.getConnection())){e.next=14;break}return e.next=14,i.replaceTrack(t);case 14:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"updateStream",value:(c=i(regeneratorRuntime.mark((function e(t){var n,r,i,a,o,s,c,u,d,l,h,p,f,m,_;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaStream_){e.next=2;break}return e.abrupt("return");case 2:return this.log_.info("updateStream() try to recover local stream"),e.prev=3,e.next=6,fk.getCameras();case 6:return n=e.sent,e.next=9,fk.getMicrophones();case 9:if(r=e.sent,i=this.audio_&&t.audio,(a=this.video_&&t.video)&&0===n.length&&(a=!1,this.log_.info("updateStream() video flag is true, but no camera detected, set video to false")),i&&0===r.length&&(i=!1,this.log_.info("updateStream() audio flag is true, but no microphone detected, set audio to false")),!1!==i||!1!==a){e.next=17;break}return this.log_.info("updateStream() both audio and video are false, recover stream aborted"),e.abrupt("return");case 17:return o=t&&n.findIndex((function(e){return e.deviceId===t.cameraId}))>=0,s=t&&r.findIndex((function(e){return e.deviceId===t.microphoneId}))>=0,e.next=21,XS({audio:i,video:a,cameraId:o?t.cameraId:void 0,microphoneId:s?t.microphoneId:void 0,facingMode:this.facingMode_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 21:c=e.sent,u=c.getTracks(),d=0;case 24:if(!(d<u.length)){e.next=49;break}if((l=u[d]).kind!==Gu||!this.isAudioTrackMixed()){e.next=37;break}if(h=this.getAudioTrack(),p=fk.AudioMixerPlugin.getAudioTrackMap(),(f=p.get(h.id)).hasMicrophone){e.next=33;break}return l.stop(),e.abrupt("continue",46);case 33:return m=fk.AudioMixerPlugin.mix({targetTrack:l,sourceList:f.sourceList,trackList:f.trackList}),e.next=36,this.replaceTrack_(m);case 36:return e.abrupt("continue",46);case 37:if(l.kind!==Ju||!this.isVideoTrackBeautified()){e.next=44;break}return e.next=40,this.generateBeautyTrack(l);case 40:return _=e.sent,e.next=43,this.replaceTrack_(_);case 43:return e.abrupt("continue",46);case 44:return e.next=46,this.replaceTrack_(l);case 46:d++,e.next=24;break;case 49:this.updateDeviceIdInUse(),Pl.logSuccessEvent({userId:this.client_?this.client_.getUserId():this.userId_,eventType:wu}),this.log_.info("updateStream() recover local stream successfully"),e.next=59;break;case 54:e.prev=54,e.t0=e.catch(3),Pl.logFailedEvent({userId:this.client_?this.client_.getUserId():this.userId_,eventType:wu,error:e.t0}),this.log_.error("updateStream() failed to recover local stream, "+e.t0),this.emitter_.emit(fy,new Al({code:Cl.DEVICE_AUTO_RECOVER_FAILED,message:e.t0.message}));case 59:case"end":return e.stop()}}),e,this,[[3,54]])}))),function(e){return c.apply(this,arguments)})},{key:"replaceTrack_",value:(o=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.mediaStream_.getAudioTracks(),r=this.mediaStream_.getVideoTracks(),!(t.kind===Gu&&n.length<=0||t.kind===Ju&&r.length<=0)){e.next=5;break}return this.log_.info("there is no previous ".concat(t.kind," track, replacement ignored")),e.abrupt("return");case 5:if(this.keepMuteState(t),t.kind===Gu?(this.mediaStream_.removeTrack(n[0]),this.mediaStream_.addTrack(t),v(d(LocalStream.prototype),"restartAudio",this).call(this)):("getSettings"in MediaStreamTrack.prototype&&(i=t.getSettings(),!this.videoSetting_||i.width===this.videoSetting_.width&&i.height===this.videoSetting_.height||this.log_.warn("the resolution of video track to be replaced (".concat(i.width," x ").concat(i.height,") is different from the previous video settings (").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,"). It may cause a cloud recording exception"))),this.mediaStream_.removeTrack(r[0]),this.mediaStream_.addTrack(t),v(d(LocalStream.prototype),"restartVideo",this).call(this)),!(a=this.getConnection())){e.next=11;break}return e.next=11,a.replaceTrack(t);case 11:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"updateDeviceIdInUse",value:function(){var e=this;if(!this.mediaStream_)return this.cameraId_="",this.cameraGroupId_="",this.microphoneId_="",void(this.microphoneGroupId_="");"getSettings"in MediaStreamTrack.prototype&&this.mediaStream_.getTracks().forEach((function(t){if(t.kind===Gu&&e.isAudioTrackMixed()){var n=e.getMicrophoneTrackMixed();if(n){var r=n.getSettings(),i=r.deviceId,a=r.groupId;i&&(e.microphoneId_=i,e.microphoneGroupId_=a)}}else if(t.kind===Ju&&e.isVideoTrackBeautified()){var o=e.getBeautyOriginTrack();if(o){var s=o.getSettings(),c=s.deviceId,u=s.groupId;c&&(e.cameraId_=c,e.cameraGroupId_=u)}}else{var d=t.getSettings(),l=d.deviceId,h=d.groupId;l&&(t.kind===Gu?(e.microphoneId_=l,e.microphoneGroupId_=h):t.kind!==Ju||e.screen_||(e.cameraId_=l,e.cameraGroupId_=h))}}));var t=this.mediaStream_.getAudioTracks(),n=this.mediaStream_.getVideoTracks();t&&0===t.length&&(this.microphoneId_="",this.microphoneGroupId_=""),n&&0===n.length&&(this.cameraId_="",this.cameraGroupId_="")}},{key:"isAudioTrackMixed",value:function(){if(fk.AudioMixerPlugin){var e=fk.AudioMixerPlugin.getAudioTrackMap(),t=this.getAudioTrack();if(e&&t&&e.has(t.id))return!0}return!1}},{key:"getMicrophoneTrackMixed",value:function(){if(fk.AudioMixerPlugin){var e=fk.AudioMixerPlugin.getAudioTrackMap(),t=this.getAudioTrack();if(e&&t&&e.has(t.id)){var n=e.get(t.id);return n.hasMicrophone?n.microphoneTrack:null}}return null}},{key:"isVideoTrackBeautified",value:function(){if(fk.beautyTrackMap){var e=fk.beautyTrackMap,t=this.getVideoTrack();if(t&&e.has(t.id))return!0}return!1}},{key:"getBeautyOriginTrack",value:function(){if(fk.beautyTrackMap){var e=fk.beautyTrackMap,t=this.getVideoTrack();if(t&&e.has(t.id)){var n=e.get(t.id);if(n.originTrack)return n.originTrack}}return null}},{key:"generateBeautyTrack",value:(r=i(regeneratorRuntime.mark((function e(t){var n,r,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=null,r=this.getVideoTrack(),i=fk.beautyTrackMap.get(r.id),a=i.param,i.type){e.next=8;break}n=i.pluginInstance.generateBeautyTrack(t),e.next=22;break;case 8:e.t0=i.type,e.next="beauty"===e.t0?11:"virtual"===e.t0?13:"mixed"===e.t0?17:21;break;case 11:return n=i.pluginInstance.generateBeautyTrack(t),e.abrupt("break",22);case 13:return e.next=15,i.pluginInstance.generateVirtualTrack({videoTrack:t,type:a.type,img:a.img});case 15:return n=e.sent,e.abrupt("break",22);case 17:return e.next=19,i.pluginInstance.generateMixedTrack({videoTrack:t,type:a.type,img:a.img});case 19:return n=e.sent,e.abrupt("break",22);case 21:return e.abrupt("break",22);case 22:return i.pluginInstance.deleteSource(r.id),this.log_.info("regenerate beauty track, track id = ".concat(t.id)),e.abrupt("return",n);case 25:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"getScreen",value:function(){return this.screen_}},{key:"hasScreenTrack",value:function(){if(this.screen_)return!0;var e=this.getVideoTrack();return!!e&&("detail"===e.contentHint||"text"===e.contentHint)}},{key:"getVideo",value:function(){return this.video_}},{key:"getAudio",value:function(){return this.audio_}},{key:"getCameraId",value:function(){return this.cameraId_}},{key:"getMicrophoneId",value:function(){return this.microphoneId_}},{key:"getMicrophoneGroupId",value:function(){return this.microphoneGroupId_}},{key:"getIsReadyToPublish",value:function(){return this.isReadyToPublish_}},{key:"setIsReadyToPublish",value:function(e){this.isReadyToPublish_=e}},{key:"setPublishState",value:function(e){this.publishState_=e}},{key:"setBeautyStatus",value:function(e){this.beautyStatus_=!!e}},{key:"getBeautyStatus",value:function(){return this.beautyStatus_}},{key:"onStreamPublished",value:function(e){var t=e.localStream,n=e.client;if(t===this){this.client_=n,this.log_.setUserId(n.getUserId()),this.log_.setSdkAppId(n.getSDKAppId());var r=this.getAudioTrack(),i=this.getVideoTrack();if(r){var a=!r.enabled;this.setMutedFlag(Gu,a)}if(i){var o=!i.enabled;this.setMutedFlag(Ju,o)}this.connection_&&this.connection_.sendMutedFlag(this.mutedFlag_)}}},{key:"keepMuteState",value:function(e){e instanceof MediaStreamTrack&&this.mutedFlag_&(e.kind===Ju?1:4)&&(e.enabled=!1,this.log_.warn("prev ".concat(e.kind," track is muted, keep mute state")))}},{key:"onVideoTrackStopped",value:function(e){var t=e.stream,n=e.type;t!==this||!this.video_||!this.cameraId_||this.recoverCaptureCount_>10||(Vs||Fs)&&n===Ku||(this.recoverCaptureCount_+=1,Pl.uploadEvent({log:"stat-local-video-".concat(n),userId:this.userId_}),this.updateStream({audio:!1,video:!0,cameraId:this.cameraId_}))}},{key:"onAudioTrackStopped",value:function(e){var t=e.stream,n=e.type;t!==this||!this.audio_||!this.microphoneId_||this.recoverCaptureCount_>10||(Vs||Fs)&&n===Ku||(this.recoverCaptureCount_+=1,Pl.uploadEvent({log:"stat-local-audio-".concat(n),userId:this.userId_}),this.updateStream({audio:!0,video:!1,microphoneId:this.microphoneId_}))}},{key:"setAudioVolume",value:function(e){v(d(LocalStream.prototype),"setAudioVolume",this).call(this,e)}},{key:"clearCanvas",value:function(){this.canvasInterval_&&(vg.clearInterval(this.canvasInterval_),this.canvasInterval_=-1,this.canvas_=null)}},{key:"genCanvasTrack",value:function(e){var t=this;this.log_.info("gen canvas track");var n=e.getSettings(),r=n.width,i=n.height,a=n.frameRate;this.canvas_=document.createElement("canvas");var o=this.canvas_.getContext("2d");return this.canvas_.width=r,this.canvas_.height=i,this.canvasInterval_=vg.setInterval((function(){if(t.hasVideo()){var e=t.getVideoTrack().getSettings();e.width===t.canvas_.width&&e.height===t.canvas_.height||(t.canvas_.width=e.width,t.canvas_.height=e.height)}t.videoPlayer_&&t.videoPlayer_.element_&&o.drawImage(t.videoPlayer_.element_,0,0,t.canvas_.width,t.canvas_.height)}),Math.max(66,Math.floor(1e3/a))),this.canvas_.captureStream().getVideoTracks()[0]}}]),LocalStream}(oS)).prototype,"switchDevice",[ik],Object.getOwnPropertyDescriptor(ak.prototype,"switchDevice"),ak.prototype),ak),lk=0,hk=0,pk=(sk=nS(tS.TRTC.createClient),ck=nS(tS.TRTC.createStream),I((uk=function(){function e(){a(this,e),this.name_=dd,this.VERSION="4.11.11",this.Logger={loggerManager:pf,LogLevel:{TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,NONE:5},setLogLevel:function(e){pf.setLogLevel(e),th()&&e<=1&&Yv()},enableUploadLog:function(){pf.enableUploadLog()},disableUploadLog:function(){pf.disableUploadLog()}}}var n,r,o,c,u;return s(e,[{key:"checkSystemRequirements",value:(u=i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,wg();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))),function(){return u.apply(this,arguments)})},{key:"isScreenShareSupported",value:function(){return Ag()}},{key:"isSmallStreamSupported",value:function(){return Gg()}},{key:"getDevices",value:(c=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Mg()&&!Og()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return e.kind!==td||"communications"!=e.deviceId})).map((function(e,t){var n=e.label;e.label||(n=e.kind+"_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r})));case 6:case"end":return e.stop()}}),e)}))),function(){return c.apply(this,arguments)})},{key:"getCameras",value:(o=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Mg()&&!Og()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return e.kind===nd})).map((function(e,t){var n=e.label;e.label||(n="camera_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r})));case 6:case"end":return e.stop()}}),e)}))),function(){return o.apply(this,arguments)})},{key:"getMicrophones",value:(r=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Mg()&&!Og()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return e.kind===td&&"communications"!==e.deviceId})).map((function(e,t){var n=e.label;e.label||(n="microphone_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r})));case 6:case"end":return e.stop()}}),e)}))),function(){return r.apply(this,arguments)})},{key:"getSpeakers",value:(n=i(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Mg()&&!Og()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return"audiooutput"===e.kind})).map((function(e,t){var n=e.label;e.label||(n="speaker_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r})));case 6:case"end":return e.stop()}}),e)}))),function(){return n.apply(this,arguments)})},{key:"createClient",value:function(e){ff&&(ff=!1,pf.getLogLevel()!=fk.Logger.LogLevel.NONE&&(console.info("******************************************************************************"),console.info("*   欢迎使用 TRTC Web SDK - 腾讯云实时音视频通信"),console.info("*   API 文档：https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/index.html"),console.info("*   版本更新日志：https://cloud.tencent.com/document/product/647/38958"),console.info("*   反馈问题：https://github.com/tencentyun/TRTCSDK/issues"),console.info("******************************************************************************")),pf.info("TRTC Web SDK Version: 4.11.11"),pf.info("UserAgent: "+navigator.userAgent),pf.info("URL of current page: "+location.href));var n={version:this.VERSION},r=new QS(t(t(t({},n),e),{},{seq:++lk}));return ih.emit(gh,{client:r}),r}},{key:"createStream",value:function(e){return new dk(t(t({},e),{},{seq:++hk}))}}]),e}()).prototype,"createClient",[sk],Object.getOwnPropertyDescriptor(uk.prototype,"createClient"),uk.prototype),I(uk.prototype,"createStream",[ck],Object.getOwnPropertyDescriptor(uk.prototype,"createStream"),uk.prototype),uk);Mg();var fk=new pk;return fk}));
