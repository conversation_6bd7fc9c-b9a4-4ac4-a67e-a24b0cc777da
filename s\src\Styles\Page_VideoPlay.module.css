.container {
  background-color: #0a0a0a;
  color: #fff;
  min-height: 100vh;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.title {
  text-align: center;
  margin-bottom: 20px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.videoContainer {
  width: 100%;
  min-width: 400px;
  max-width: 800px;
}

.video {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 0 10px #00ffff;
}

.visualizationContainer {
  display: flex;
  justify-content: center;
  gap: 40px;
  width: 100%;
  max-width: 800px;
}

.canvasWrapper {
  position: relative;
  width: 300px;
  text-align: center;
}

.canvas {
  width: 100%;
  height: auto;
  border-radius: 6px;
  display: block;
  margin: 0 auto;
}

.waveformCanvas {
  border: 2px solid #00ffff;
  box-shadow: 0 0 8px #00ffff;
}

.spectrogramCanvas {
  border: 2px solid #ff00ff;
  box-shadow: 0 0 8px #ff00ff;
}

.label {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  color: #ccc;
  font-weight: bold;
}

.frequencyBarContainer {
  width: 100%;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-top: 10px;
}

.frequencyBarFill {
  height: 100%;
  width: 0%;
  background: linear-gradient(to right, #00f2ff, #ff00ff);
  transition: width 0.2s ease-out;
}