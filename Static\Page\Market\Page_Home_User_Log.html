<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
    <link rel="stylesheet" href="/static/CSS/bootstrap-fileinput/fileinput.min.css">
    <link rel="stylesheet" href="/static/CSS/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/CSS/animate/animate.min.css">
    <link rel="stylesheet" href="/static/CSS/jquery/fullcalendar/fullcalendar.min.css">
    <!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />

    <link href="/static/CSS/font-awesome/all.min.css" rel="stylesheet" />
    <!-- 加载 Select2 -->
    <link href="/static/CSS/select2/select2.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="/static/CSS/jquery/jquery.scrollbar.css">
    <!--     dialog   -->
    <link rel="stylesheet" href="/static/CSS/trumbowyg/trumbowyg.min.css">
    <link rel="stylesheet" href="/static/CSS/sweetalert/sweetalert2.min.css">
    <link rel="stylesheet" href="/static/CSS/server_style.css">

    <!-- data-table CSS ============================================ -->
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-table.css">
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-editable.css">
    <!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
    <!-- App styles -->
    <link rel="stylesheet" href="/static/CSS/app/app.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />


    <style type="text/css">
        .table {
            table-layout: fixed;
            word-break: break-all;
        }

        .nav-link {
            display: block;
            padding: 0rem 1.5rem;
        }
    </style>

</head>

<body class="bg-theme bg-theme1">

    <div class="wrapper">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>

        <div class="page-wrapper">
            <div class="page-content-wrapper">
                <div class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="mb-3">查询条件：</h6>

                            <div class="form-row">
                                <!-- <div class="col-lg-3 col-md-12 my-md-3">
                                    <select class="select2" data-minimum-results-for-search="Infinity">
                                        <option selected>服务类型</option>
                                        <option>服务媒体</option>
                                        <option>服务数量</option>
                                    </select>
                                </div> -->
                                <div class="col-lg-3 col-md-12 my-md-3">
                                    <select class="select2" id="User_History_Date" data-minimum-results-for-search="Infinity">
                                        <option value="week">一周</option>
                                        <option value="month">本月</option>
                                        <option value="year">本年</option>
                                        <option value="all" selected>所有</option>
                                    </select>
                                </div>
                                <div class="col-lg-3 col-md-12 my-md-3">
                                    <button class="btn btn-primary" id="query_table">查询</button>
                                    <button class="btn btn-success ml-3" id="export_table">导出</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <style>
                            .table-responsive select option {
                                background-color: rgba(52, 58, 64, 0.55) !important;
                                /* 设置下拉框的背景颜色为绿色 */
                                color: white;
                                /* 设置下拉框的文字颜色为白色 */
                            }
                        </style>
                        <table id="table" class="table table-hover">

                        </table>
                    </div>
                    <style>
                        .table-container {
                            height: 500px;
                            /* 表格容器的最大高度 */
                            overflow-y: auto;
                            /* 允许垂直滚动 */
                            overflow-x: hidden;
                        }
                    </style>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/daterangepicker/jquery.daterangepicker.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/fileinput.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/zh.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/theme.min.js"></script>
    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>
    <script src="/static/JavaScript/select2/select2.full.min.js"></script>
    <script src="/static/JavaScript/select2/zh-CN.js"></script>
    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>
    <!-- data-table  ============================================ -->
    <script src="/static/JavaScript/data-table/bootstrap-table.js"></script>
    <script src="/static/JavaScript/data-table/bootstrap-table-zh-CN.js"></script>

    <!-- App functions and dialog -->
    <script src="/static/JavaScript/App/clamp.js"></script>
    <script src="/static/JavaScript/App/trumbowyg.min.js"></script>

    <!-- App functions and notify -->
    <script src="/static/JavaScript/App/bootstrap-notify.min.js"></script>

    <!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>

    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <script src="/static/JavaScript/clipboard/cilpboard.min.js"></script>

    <script src="/static/JavaScript/QRcode/qrcode.min.js"></script>
    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/App/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/App/dataTables.buttons.min.js"></script>
    <script src="/static/JavaScript/App/buttons.print.min.js"></script>
    <script src="/static/JavaScript/App/jszip.min.js"></script>
    <script src="/static/JavaScript/App/buttons.html5.min.js"></script>
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <script src="/static/Page/Market/Page_Home_User_Log.js"></script>
    <script src="/static/JavaScript/xlxs/xlxs.min.js"></script>

    <script>
        var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>

    <script>
        function Page_Init() {
            Element_Sidebar_Header();
            Query_Orders()
        };

        window.onload = function () {
            Page_Init();
        }
    </script>

</body>
    
</html>