




class Server_Print {

    constructor(type,data){
        this.type = type
        this.data = data
    }


    
    run(){
        this.data['Content'] = ''
        try {
            for (let i in arguments) {
                console.log( 'Value',  i)
                this.data['Content']   +=  arguments[i].toString() 
                console.log('Content',  this.data['Content'])


                // this.data[key]  = arguments[0]
              
            }

            // this.data[]=
        } catch (error) {
            
        }



        console.log('key',  arguments[0])
        // try {
        //     for (let key in this.data) {
        //         // this.data[key]  = arguments[0]
              
        //     }

        //     // this.data[]=
        // } catch (error) {
            
        // }





        try {
           
            eval(`this.print_${this.type}()`);
        } catch (error) {
            return {'Error': error, "Type": this.type, "Data": this.data, "Status": "Failed"}
            
        }
    }
    // "top","right","fa fa-comments","success","animated fadeInRight","animated fadeOutRight",'','试用客户未开放该功能'
    print_Notify (from='top', align='right', icon ='fa fa-comments', Message_Type = 'danger', animIn='animated fadeInRight', animOut='animated fadeOutRight',Message_Title=this.data['Type'],message=this.data['Content']){
        console.log("run",this.type,from);
        
        let Notify_Hint={
            'success':'成功',
            'info':'提示',
            'warning':'警告',
            'danger':'错误'
        }


        try {
    

            Message_Type = this.data['Type']
            Message_Title = Notify_Hint[this.data['Type']]



        } catch (error) {
            Message_Type = ''
        }


            
            console.log("Message_Type",Message_Type);
            $.notify({
                icon: icon,
                title: Message_Title,
                message: message,
                url: ''
            },{
                element: 'body',
                type: Message_Type,
                allow_dismiss: true,
                placement: {
                    from: from,
                    align: align
                },
                offset: {
                    x: 20,
                    y: 20
                },
                spacing: 10,
                z_index: 1031,
                delay: 2500,
                timer: 5000,
                url_target: '_blank',
                mouse_over: false,
                animate: {
                    enter: animIn,
                    exit: animOut
                },
                template:   '<div data-notify="container" class="alert alert-dismissible alert-{0} alert--notify" role="alert">' +
                                '<span data-notify="icon"></span> ' +
                                '<span data-notify="title">{1}</span> ' +
                                '<span data-notify="message">{2}</span>' +
                                '<div class="progress" data-notify="progressbar">' +
                                    '<div class="progress-bar progress-bar-{0}" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;"></div>' +
                                '</div>' +
                                '<a href="{3}" target="{4}" data-notify="url"></a>' +
                                '<button type="button" aria-hidden="true" data-notify="dismiss" class="close"><span>×</span></button>' +
                            '</div>'
            });
        }
    }
