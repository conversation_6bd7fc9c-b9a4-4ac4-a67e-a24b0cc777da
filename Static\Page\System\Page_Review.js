/****************************************初始化请求数据***************************************************/ 
var Link_Alt_Info = '';
function Requests_Sentiment_Info() {
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_review_info',
        "data_argument": `{}`,
        "data_kwargs": {'Review_UUID':Review_UUID}
    };
    __Service_Requests = new Service_Requests("Sync",Requests_Data);
    Result = __Service_Requests.callMethod()
    console.log('Result:',Result);
    if (Result.Status == 'Success') {
        document.getElementById('title').innerText = Result.Intelligence.TITLE;
        document.getElementById('author').innerText = Result.Intelligence.AUTHOR;
        document.getElementById('date').innerText = Result.Intelligence.TIME;
        document.getElementById('content').innerText = Result.Intelligence.CONTENT;
        // document.getElementById('Orange_Title').innerText = '相关报道链接';
        document.getElementById('Orange_Title').innerHTML = `相关报道链接<i class="fadeIn animated bx bx-link-alt" data-toggle="tooltip" data-placement="top" title="原网站复核跳转" id="Editor_Review_Link_Element"></i>`;
        document.getElementById('Link_Url').src = Result.Intelligence.URL;
        Link_Alt_Info = Result.Intelligence.URL;
    } else {
        console.log('请求失败')
    }
};
/****************************************主页跳转***************************************************/ 
$('#Editor_Review_Link_Element').on('click',function() {
    window.open(Link_Alt_Info, "_blank");
})