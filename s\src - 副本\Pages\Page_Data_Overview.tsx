import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Typography, Table, Spin } from 'antd';
import { useECharts } from '../Function/Function_useECharts';
import SimpleMapChart from '../Component/SimpleMapChart';
import '../Styles/Page_Data_Overview.css';

const { Title, Text } = Typography;

interface SentimentData {
  totalCount: number;
  newsCount: number;
  negativeCount: number;
  positiveCount: number;
  neutralCount: number;
  todayCount: number;
  platformData: {
    web: number;
    wechat: number;
    weibo: number;
    app: number;
    forum: number;
    newspaper: number;
    video: number;
    toutiao: number;
    sohu: number;
    qa: number;
    comment: number;
    other: number;
  };
}

interface HotArticle {
  id: string;
  title: string;
  source: string;
  publishTime: string;
}

const Page_Data_Overview: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [sentimentData, setSentimentData] = useState<SentimentData>({
    totalCount: 5111929,
    newsCount: 551607,
    negativeCount: 653225,
    positiveCount: 1200000,
    neutralCount: 800000,
    todayCount: 25918,
    platformData: {
      web: 6737,
      wechat: 5275,
      weibo: 7687,
      app: 4219,
      forum: 827,
      newspaper: 574,
      video: 963,
      toutiao: 790,
      sohu: 435,
      qa: 193,
      comment: 2,
      other: 859
    }
  });

  const [hotArticles, setHotArticles] = useState<HotArticle[]>([]);
  const [negativeArticles, setNegativeArticles] = useState<HotArticle[]>([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // 地图数据 - 用于百度地图标点
  const [mapData, setMapData] = useState([
    { name: '北京', value: 1200, lng: 116.405285, lat: 39.904989 },
    { name: '上海', value: 980, lng: 121.472644, lat: 31.231706 },
    { name: '广州', value: 1500, lng: 113.280637, lat: 23.125178 },
    { name: '杭州', value: 800, lng: 120.153576, lat: 30.287459 },
    { name: '南京', value: 750, lng: 118.767413, lat: 32.041544 },
    { name: '济南', value: 650, lng: 117.000923, lat: 36.675807 },
    { name: '郑州', value: 600, lng: 113.665412, lat: 34.757975 },
    { name: '成都', value: 550, lng: 104.065735, lat: 30.659462 },
    { name: '武汉', value: 500, lng: 114.298572, lat: 30.584355 },
    { name: '长沙', value: 450, lng: 112.982279, lat: 28.19409 }
  ]);

  // 图表配置
  const [platformOptions, setPlatformOptions] = useState({});
  const [sentimentOptions, setSentimentOptions] = useState({});
  const [trendOptions, setTrendOptions] = useState({});

  // 图表容器引用
  const platformChartRef = useECharts(platformOptions, false);
  const sentimentChartRef = useECharts(sentimentOptions, false);
  const trendChartRef = useECharts(trendOptions, false);

  // 获取数据总览信息
  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟热门文章数据
      const mockHotArticles: HotArticle[] = [
        {
          id: '1',
          title: '重要政策发布，影响深远',
          source: '人民日报',
          publishTime: '2025-01-23 10:30'
        },
        {
          id: '2',
          title: '科技创新取得重大突破',
          source: '科技日报',
          publishTime: '2025-01-23 09:15'
        },
        {
          id: '3',
          title: '经济发展呈现良好态势',
          source: '经济日报',
          publishTime: '2025-01-23 08:45'
        },
        {
          id: '4',
          title: '教育改革新政策引发热议',
          source: '教育报',
          publishTime: '2025-01-23 08:20'
        },
        {
          id: '5',
          title: '环保议题再次成为社会焦点',
          source: '环保网',
          publishTime: '2025-01-23 07:50'
        }
      ];

      // 模拟负面文章数据
      const mockNegativeArticles: HotArticle[] = [
        {
          id: '6',
          title: '某地区环境污染问题引关注',
          source: '环保网',
          publishTime: '2025-01-23 11:20'
        },
        {
          id: '7',
          title: '食品安全事件调查进展',
          source: '食品安全报',
          publishTime: '2025-01-23 10:50'
        },
        {
          id: '8',
          title: '交通事故频发需要重视',
          source: '交通报',
          publishTime: '2025-01-23 10:30'
        }
      ];

      setHotArticles(mockHotArticles);
      setNegativeArticles(mockNegativeArticles);
    } catch (error) {
      console.error('获取数据总览失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 滚动动画
  const startScroll = () => {
    const scrollContainer = document.getElementById('Today-Sentiment-Hot-Article-container');
    if (scrollContainer) {
      setInterval(() => {
        setScrollPosition(prev => prev - 1);
      }, 50);
    }
  };



  // 初始化平台分布图表
  const initPlatformChart = () => {
    const option = {
      title: {
        text: '近30天平台分布',
        left: 'center',
        textStyle: {
          color: '#ffffff'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: '#ffffff'
        }
      },
      series: [
        {
          name: '平台分布',
          type: 'pie',
          radius: '50%',
          data: [
            { value: sentimentData.platformData.web, name: '网页' },
            { value: sentimentData.platformData.wechat, name: '微信' },
            { value: sentimentData.platformData.weibo, name: '微博' },
            { value: sentimentData.platformData.app, name: 'App' },
            { value: sentimentData.platformData.forum, name: '论坛' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    setPlatformOptions(option);
  };

  // 初始化情感分类图表
  const initSentimentChart = () => {
    const option = {
      title: {
        text: '近30天情感属性',
        left: 'center',
        textStyle: {
          color: '#ffffff'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: '#ffffff'
        }
      },
      series: [
        {
          name: '情感分类',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: sentimentData.positiveCount, name: '正面', itemStyle: { color: '#28a745' } },
            { value: sentimentData.neutralCount, name: '中性', itemStyle: { color: '#ffc107' } },
            { value: sentimentData.negativeCount, name: '负面', itemStyle: { color: '#dc3545' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    setSentimentOptions(option);
  };

  // 初始化趋势图表
  const initTrendChart = () => {
    const option = {
      title: {
        text: '近30天舆情趋势',
        left: 'center',
        textStyle: {
          color: '#ffffff'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['舆情数量'],
        textStyle: {
          color: '#ffffff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1日', '3日', '5日', '7日', '10日', '12日', '15日', '18日', '20日', '22日', '25日', '27日', '30日'],
        axisLabel: {
          color: '#ffffff',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#444'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#444'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#ffffff',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#444'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#444'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#333',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '舆情数量',
          type: 'line',
          smooth: true, // 启用平滑曲线
          data: [120, 135, 132, 128, 101, 115, 134, 142, 90, 105, 230, 245, 210],
          itemStyle: {
            color: '#03A9F4'
          },
          lineStyle: {
            width: 3, // 线条宽度
            color: '#03A9F4'
          },
          areaStyle: {
            // 添加渐变填充区域
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(3, 169, 244, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(3, 169, 244, 0.05)'
                }
              ]
            }
          },
          symbol: 'circle', // 数据点样式
          symbolSize: 6, // 数据点大小
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: '#ffffff',
              borderColor: '#03A9F4',
              borderWidth: 2
            }
          }
        }
      ]
    };

    setTrendOptions(option);
  };

  useEffect(() => {
    fetchOverviewData();
    startScroll();
  }, []);

  // 单独的useEffect用于初始化图表，只在组件挂载时执行一次
  useEffect(() => {
    const timer = setTimeout(() => {
      initPlatformChart();
      initSentimentChart();
      initTrendChart();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // 当数据变化时，只更新图表选项，不重新初始化
  useEffect(() => {
    if (sentimentData) {
      initPlatformChart();
      initSentimentChart();
      initTrendChart();
    }
  }, [sentimentData]);

  // 计算环比变化（与原HTML完全一致）
  const getChangePercent = (current: number, type: string) => {
    const changes: { [key: string]: number } = {
      web: -15,
      wechat: 6,
      weibo: -54,
      app: -12,
      forum: -30,
      newspaper: 25,
      video: -63,
      toutiao: -40,
      sohu: -18,
      qa: -39,
      comment: -50,
      other: -32
    };
    return changes[type] || 0;
  };

  // 表格数据
  const tableData = [
    {
      key: '1',
      type: '信息累计总量',
      count: sentimentData.totalCount,
      icon: 'bx-data',
      color: 'info'
    },
    {
      key: '2',
      type: '互联网新闻信息稿源量',
      count: sentimentData.newsCount,
      icon: 'bx-paste',
      color: 'warning'
    },
    {
      key: '3',
      type: '负面舆情总量',
      count: sentimentData.negativeCount,
      icon: 'bx-notification',
      color: 'danger'
    }
  ];

  const tableColumns = [
    {
      title: '汇总类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <button type="button" className={`btn btn-${record.color} btn-sm`}>
            <i className={`fadeIn animated bx ${record.icon}`}></i>
          </button>
          <div style={{ marginLeft: '5px' }}>{text}</div>
        </div>
      ),
    },
    {
      title: '采集总量',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => (
        <span className="text-semibold">{count.toLocaleString()}</span>
      ),
    },
  ];

  // 平台图标组件
  const PlatformIcon: React.FC<{ type: string }> = ({ type }) => {
    const iconSvgs: { [key: string]: JSX.Element } = {
      web: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M870.4 1024H153.6C69.12 1024 0 954.88 0 870.4V153.6C0 69.12 69.12 0 153.6 0h716.8c84.48 0 153.6 69.12 153.6 153.6v716.8c0 84.48-69.12 153.6-153.6 153.6z" fill="#5B8BFF"></path>
          <path d="M708.096 571.392h186.88c1.536-12.8 2.048-26.112 2.048-39.936 0-63.488-16.896-122.88-46.592-174.08 30.72-81.92 29.696-151.04-11.264-192.512-38.912-38.912-143.872-32.768-262.656 19.968a348.16 348.16 0 0 0-364.032 262.144c51.2-65.536 104.96-112.64 176.64-147.456-6.656 6.144-44.544 44.032-51.2 50.176-189.44 189.44-249.344 436.736-184.832 500.736 48.64 48.64 137.216 40.448 239.104-9.216a347.209143 347.209143 0 0 0 486.4-197.12H690.688a153.526857 153.526857 0 0 1-134.656 80.384 152.502857 152.502857 0 0 1-134.656-80.384 155.867429 155.867429 0 0 1-17.92-72.704v-0.512h304.64v0.512zM402.944 479.744a146.432 146.432 0 0 1 145.92-138.24c77.824 0 141.824 61.44 145.92 138.24h-291.84z m433.664-275.968c26.624 26.624 26.112 76.288 3.072 137.728a348.306286 348.306286 0 0 0-161.792-132.608c71.168-30.72 129.024-34.816 158.72-5.12z m-635.392 635.392c-33.792-33.792-23.552-104.96 19.968-190.464a349.622857 349.622857 0 0 0 147.968 181.248c-75.264 33.792-136.704 39.936-167.936 9.216z" fill="#FFFFFF"></path>
        </svg>
      ),
      wechat: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M0 0m184.32 0l655.36 0q184.32 0 184.32 184.32l0 655.36q0 184.32-184.32 184.32l-655.36 0q-184.32 0-184.32-184.32l0-655.36q0-184.32 184.32-184.32Z" fill="#65DB79"></path>
          <path d="M663.21408 407.02976c-128.75776 0-233.13408 87.296-233.13408 194.97984s104.37632 194.97984 233.13408 194.97984a273.85856 273.85856 0 0 0 88.79104-14.76608l66.74432 35.55328-8.98048-64.12288a183.78752 183.78752 0 0 0 86.5792-151.64416c-0.01024-107.68384-104.38656-194.97984-233.13408-194.97984z" fill="#FFFFFF"></path>
          <path d="M404.48 194.56c137.0112 0 250.28608 83.968 276.16256 195.2768-48.82432-4.01408-302.08 23.27552-261.85728 271.36-36.4032 0.1024-86.016-1.49504-121.5488-13.4656l-80.62976 42.97728 10.8544-77.45536C164.7104 571.7504 122.88 505.00608 122.88 430.08c0-130.048 126.07488-235.52 281.6-235.52z" fill="#FFFFFF"></path>
          <path d="M313.344 352.256m-36.864 0a36.864 36.864 0 1 0 73.728 0 36.864 36.864 0 1 0-73.728 0Z" fill="#65DB79"></path>
          <path d="M497.664 352.256m-36.864 0a36.864 36.864 0 1 0 73.728 0 36.864 36.864 0 1 0-73.728 0Z" fill="#65DB79"></path>
          <path d="M585.728 544.768m-32.768 0a32.768 32.768 0 1 0 65.536 0 32.768 32.768 0 1 0-65.536 0Z" fill="#65DB79"></path>
          <path d="M741.376 544.768m-32.768 0a32.768 32.768 0 1 0 65.536 0 32.768 32.768 0 1 0-65.536 0Z" fill="#65DB79"></path>
        </svg>
      ),
      weibo: (
        <svg className="icon" viewBox="0 0 1027 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M896 1024h-768c-70.4 0-128-57.6-128-128V128c0-70.4 57.6-128 128-128h768c70.4 0 128 57.6 128 128v768c0 70.4-57.6 128-128 128" fill="#FF6E5E"></path>
          <path d="M460.8 748.8c-115.2 12.8-211.2-44.8-217.6-115.2-6.4-76.8 76.8-147.2 192-160 108.8-12.8 204.8 44.8 211.2 115.2s-76.8 147.2-185.6 160m198.4-249.6s89.6-153.6-166.4-70.4c32-25.6 25.6-76.8 0-96-89.6-64-320 147.2-320 275.2 0 76.8 83.2 179.2 268.8 179.2 236.8 0 313.6-128 313.6-192 6.4-96-96-96-96-96" fill="#FFFFFF"></path>
        </svg>
      ),
      app: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M140.728 32h744.889c56.107 4.054 103.822 52.409 106.382 108.8v742.187c-2.418 56.035-49.422 104.035-104.96 109.013h-748.8c-56.107-4.124-103.822-52.622-106.24-108.942v-744.676c4.124-56.107 52.409-103.751 108.728-106.382M281.671 394.88c-31.004 83.058-63.146 165.689-94.792 248.533 11.662 0 23.395 0 35.058 0.142 9.529-26.098 18.916-52.195 28.516-78.222 33.352 0 66.702-0.142 100.054 0.143 9.743 26.027 19.343 52.054 29.084 78.008 12.444 0 24.889 0 37.333 0.071-33.067-83.2-66.702-166.187-100.836-248.96-11.449 0.071-22.969 0.214-34.417 0.284M434.062 394.737c-0.071 82.915-0.071 165.902 0 248.818 11.306 0 22.613 0 33.991-0.071 0.071-33.138 0-66.204 0-99.342 38.898-2.275 80.071 6.258 116.978-9.671 52.124-25.387 49.208-118.187-8.604-134.969-46.72-9.814-95.004-2.702-142.364-4.764M647.396 394.737c-0.071 82.915-0.071 165.902 0 248.818 11.306 0 22.613 0 33.991-0.071 0.071-33.138 0-66.204 0-99.342 38.898-2.275 80.142 6.258 117.049-9.671 52.054-25.529 49.138-118.116-8.604-134.898-46.72-9.955-95.004-2.773-142.435-4.836z" fill="#5EAAF7"></path>
          <path d="M468.054 423.182c29.44 0.712 59.022-1.92 88.391 1.067 40.534 8.676 43.093 75.876 3.342 87.396-29.796 7.964-61.226 2.418-91.733 3.769v-92.231zM681.387 423.253c29.155 0.569 58.453-1.849 87.608 0.782 41.102 7.964 44.231 75.804 4.267 87.538-29.867 8.107-61.369 2.489-91.876 3.84v-92.16zM260.48 536.818c13.44-37.12 28.8-73.6 38.684-111.858 11.165 38.045 26.453 74.666 39.965 111.929-26.168 0.071-52.409 0-78.649-0.071z" fill="#5EAAF7"></path>
          <path d="M281.671 394.88c11.449-0.071 22.969-0.214 34.417-0.284 34.133 82.774 67.769 165.76 100.836 248.96-12.444-0.071-24.889-0.071-37.333-0.071-9.742-25.956-19.343-51.983-29.084-78.008-33.352-0.284-66.702-0.142-100.054-0.142-9.6 26.027-18.986 52.124-28.516 78.222-11.662-0.142-23.395-0.142-35.058-0.142 31.644-82.844 63.787-165.475 94.792-248.533M260.48 536.818c26.24 0.071 52.48 0.142 78.649 0.071-13.511-37.262-28.8-73.884-39.965-111.929-9.884 38.257-25.244 74.737-38.684 111.858zM434.062 394.737c47.36 2.063 95.645-5.048 142.364 4.764 57.814 16.782 60.728 109.582 8.604 134.969-36.907 15.929-78.080 7.396-116.978 9.671 0 33.138 0.071 66.204 0 99.342-11.377 0.071-22.685 0.071-33.991 0.071-0.071-82.915-0.071-165.902 0-248.818M468.054 423.182v92.231c30.506-1.351 61.938 4.195 91.733-3.769 39.751-11.52 37.192-78.72-3.342-87.396-29.369-2.987-58.951-0.355-88.391-1.067zM647.396 394.737c47.431 2.063 95.716-5.12 142.435 4.836 57.743 16.782 60.658 109.369 8.604 134.898-36.907 15.929-78.151 7.396-117.049 9.671 0 33.138 0.071 66.204 0 99.342-11.377 0.071-22.685 0.071-33.991 0.071-0.071-82.915-0.071-165.902 0-248.818M681.387 423.253v92.16c30.506-1.351 62.009 4.267 91.876-3.84 39.965-11.734 36.835-79.573-4.267-87.538-29.155-2.632-58.453-0.214-87.608-0.782z" fill="#FFFFFF"></path>
        </svg>
      ),
      forum: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M0 0m256 0l512 0q256 0 256 256l0 512q0 256-256 256l-512 0q-256 0-256-256l0-512q0-256 256-256Z" fill="#6A30F5"></path>
          <path d="M128 310.6688m51.2 0l537.6 0q51.2 0 51.2 51.2l0 320q0 51.2-51.2 51.2l-537.6 0q-51.2 0-51.2-51.2l0-320q0-51.2 51.2-51.2Z" fill="#FFFFFF"></path>
          <path d="M256 528.2688m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" fill="#6A30F5"></path>
          <path d="M448 528.2688m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" fill="#6A30F5"></path>
          <path d="M640 528.2688m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" fill="#6A30F5"></path>
          <path d="M425.3056 803.1616a25.6 25.6 0 0 1-44.0832 0v-69.8752A25.6 25.6 0 0 1 403.2 694.6688h76.8a25.6 25.6 0 0 1 21.9776 38.6176z" fill="#FFFFFF"></path>
          <path d="M307.2 208.2688m32 0l499.2 0q32 0 32 32l0 0q0 32-32 32l-499.2 0q-32 0-32-32l0 0q0-32 32-32Z" fill="#FFFFFF"></path>
          <path d="M870.4 214.6688m0 32l0 384q0 32-32 32l0 0q-32 0-32-32l0-384q0-32 32-32l0 0q32 0 32 32Z" fill="#FFFFFF"></path>
        </svg>
      ),
      newspaper: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M1024 938.665984C1024 985.812992 985.812992 1024 938.665984 1024H85.334016C38.187008 1024 0 985.812992 0 938.665984V85.334016C0 38.187008 38.187008 0 85.334016 0h853.331968C985.812992 0 1024 38.187008 1024 85.334016v853.331968z" fill="#2192FD"></path>
          <path d="M527.142912 652.253184h-131.678208a18.784256 18.784256 0 0 1-13.30176-5.525504 18.8928 18.8928 0 0 1-5.50912-13.338624v-9.433088c0-5.005312 1.980416-9.803776 5.50912-13.340672a18.784256 18.784256 0 0 1 13.30176-5.525504h131.678208c10.389504 0 18.81088 8.445952 18.81088 18.866176v9.433088c0 10.418176-8.421376 18.864128-18.81088 18.864128z m75.24352-103.76192H395.466752a18.784256 18.784256 0 0 1-13.30176-5.525504 18.8928 18.8928 0 0 1-5.50912-13.340672V520.192c0-5.003264 1.980416-9.801728 5.50912-13.338624a18.784256 18.784256 0 0 1 13.30176-5.525504h206.923776c4.988928 0 9.773056 1.98656 13.30176 5.525504a18.8928 18.8928 0 0 1 5.50912 13.338624v9.433088a18.8928 18.8928 0 0 1-5.50912 13.340672 18.784256 18.784256 0 0 1-13.30176 5.525504z m0-103.76192H395.466752a18.784256 18.784256 0 0 1-13.30176-5.525504 18.8928 18.8928 0 0 1-5.50912-13.340672v-9.433088c0-5.003264 1.980416-9.801728 5.50912-13.338624a18.784256 18.784256 0 0 1 13.30176-5.525504h206.923776c10.389504 0 18.81088 8.445952 18.81088 18.864128v9.433088c0 10.420224-8.421376 18.866176-18.81088 18.866176z m107.13088 257.613824V421.146624a37.7856 37.7856 0 0 0-11.19232-26.78784l-96.784384-96.12288a37.566464 37.566464 0 0 0-26.42944-10.942464h-223.00672c-20.779008 0-37.62176 16.893952-37.62176 37.732352v377.317376c0 20.8384 16.842752 37.730304 37.62176 37.730304h319.791104c20.779008 0 37.62176-16.891904 37.62176-37.730304zM577.181696 229.376a93.919232 93.919232 0 0 1 66.49856 27.63776l94.713856 95.649792A94.468096 94.468096 0 0 1 765.952 419.354624v282.988544C765.952 754.440192 723.841024 796.672 671.895552 796.672H352.104448C300.158976 796.672 258.048 754.440192 258.048 702.34112V323.70688c0-52.099072 42.110976-94.33088 94.056448-94.33088h225.0752z" fill="#FFFFFF"></path>
        </svg>
      ),
      video: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M0 0m93.090909 0l837.818182 0q93.090909 0 93.090909 93.090909l0 837.818182q0 93.090909-93.090909 93.090909l-837.818182 0q-93.090909 0-93.090909-93.090909l0-837.818182q0-93.090909 93.090909-93.090909Z" fill="#7C8EEE"></path>
          <path d="M727.773091 553.821091l-348.974546 209.093818C355.490909 777.041455 325.818182 760.087273 325.818182 732.544V314.333091c0-26.845091 29.672727-43.799273 52.980363-30.370909l348.974546 209.093818c22.609455 14.126545 22.609455 47.325091 0 60.741818z" fill="#FFFFFF"></path>
        </svg>
      ),
      toutiao: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M896 0H128a128 128 0 0 0-128 128v768a128 128 0 0 0 128 128h768a128 128 0 0 0 128-128V128a128 128 0 0 0-128-128z" fill="#D64541"></path>
          <path d="M912 256v484c-8.64 0.4-17.28 0.736-25.888 1.184-14.64 0.768-29.28 1.568-43.888 2.4-13.424 0.752-26.832 1.6-40.24 2.352-22.656 1.232-45.312 2.368-67.968 3.584-13.808 0.752-27.632 1.6-41.44 2.368l-64.32 3.568-42.672 2.384-63.088 3.568c-13.824 0.784-27.632 1.6-41.456 2.368-22.24 1.216-44.496 2.368-66.752 3.584-13.808 0.752-27.632 1.6-41.44 2.352-22.24 1.216-44.496 2.368-66.752 3.584-13.824 0.768-27.632 1.6-41.44 2.368-22.256 1.216-44.512 2.368-66.752 3.584-13.824 0.752-27.632 1.6-41.456 2.368L112 784V297.616c4.992-0.384 9.968-0.896 14.96-1.152 23.664-1.232 47.328-2.4 71.008-3.6 14.832-0.768 29.664-1.6 44.48-2.368 22.88-1.2 45.728-2.4 68.592-3.568 15.648-0.8 31.296-1.568 46.928-2.384 22.864-1.184 45.728-2.368 68.576-3.584 14.432-0.752 28.864-1.6 43.296-2.352 15.84-0.832 31.68-1.6 47.52-2.4 15.856-0.8 31.712-1.552 47.552-2.368 22.656-1.168 45.312-2.384 67.968-3.568 15.04-0.8 30.08-1.6 45.12-2.368l46.928-2.4 48.16-2.368c15.632-0.784 31.28-1.568 46.912-2.384 15.04-0.768 30.08-1.6 45.12-2.368L912 256z" fill="#FFFFFF"></path>
        </svg>
      ),
      sohu: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M666.988089 0H357.011911c-102.8096 0-154.191644 0-209.5104 17.499022A217.406578 217.406578 0 0 0 17.521778 147.478756C0 202.820267 0 254.225067 0 356.989156V666.965333c0 102.855111 0 154.2144 17.521778 209.555911a217.383822 217.383822 0 0 0 129.979733 129.956978c55.318756 17.521778 106.7008 17.521778 209.5104 17.521778h309.976178c102.832356 0 154.191644 0 209.533155-17.521778a217.383822 217.383822 0 0 0 129.979734-129.956978C1024 821.179733 1024 769.797689 1024 666.965333V356.989156c0-102.786844 0-154.168889-17.521778-209.5104A217.4976 217.4976 0 0 0 876.498489 17.476267C821.179733 0 769.797689 0 666.988089 0z" fill="#F2C81C"></path>
          <path d="M570.686578 342.721422c3.959467-3.4816 14.1312-17.089422 16.702578-21.754311 0.477867-0.955733 3.367822-4.960711 3.367822-4.960711 1.342578-6.621867-2.616889-13.835378-3.367822-18.363733-0.2048-0.113778 2.366578-3.322311 0-6.712889-2.321067-3.367822-9.966933 1.820444-14.973156 5.051733-1.934222 1.092267-1.501867 3.6864-3.322311 4.937956-12.606578 8.783644-15.086933 9.671111-28.444445 3.345066-1.865956-2.753422-3.663644-4.414578 0-9.989689 3.663644-5.643378 21.890844-15.041422 33.427912-20.047644-0.273067-4.164267 1.206044-11.172978 0-15.041422-0.273067-0.682667-4.482844-2.821689-4.983467-3.390578-20.844089-24.189156-11.810133-30.014578-11.810133-30.014578s12.470044-6.917689 20.138666-3.345066c7.509333 3.549867 15.473778 42.825956 25.008356 18.363733 0.796444-2.025244 3.845689-8.783644 3.345066-11.696356-0.386844-2.139022-6.280533-5.279289-6.644622-6.667377-0.546133-1.388089 0.091022-6.781156 0.091022-6.781156l1.524623-3.185778 6.667377-3.367822s6.781156-0.341333 10.126223 1.706667c2.161778 1.297067 9.693867 17.999644 9.989689 21.731555 0.4096 5.097244-1.4336 14.267733-3.322312 18.386489-1.069511 1.979733-5.779911 3.868444-6.690133 6.667378-1.752178 4.232533 0.113778 6.371556-1.752178 9.966933-1.137778 2.116267-6.121244 3.8912-6.644622 6.712889-3.254044 15.132444 9.966933 35.157333 11.719111 48.446578 5.9392 46.921956-2.161778 102.149689-30.082844 120.308622-0.4096 0.2048-4.278044 0.728178-4.778667 1.024l-10.262755-2.730667c-1.615644-2.685156-4.892444-4.460089-6.599112-6.621866-2.343822-2.958222-2.821689-6.940444-5.097244-10.0352-1.456356-2.025244-7.418311-4.573867-8.328533-6.712889-8.100978-17.840356 8.988444-17.112178 18.409244-11.719111 2.002489 1.160533 5.666133 6.485333 8.374045 6.712889 3.527111 0.318578 4.778667-2.366578 6.667377-3.345067 14.176711-7.600356 5.575111-14.7456 6.644623-28.398933 0.182044-2.616889 4.596622-9.489067 4.983466-11.6736 2.002489-12.196978-0.796444-23.256178-3.254044-31.812267-0.318578 0.4096-4.596622 1.183289-5.097245 1.706667-2.207289 2.161778-1.774933 7.190756-3.299555 10.0352-2.6624 4.391822-7.964444 8.192-11.696356 11.741866-5.302044 4.619378-10.740622 13.494044-18.409244 15.018667-2.821689 0.477867-10.217244-1.729422-9.989689-1.729422-0.227556-0.2048-5.5296 1.934222-8.3968-6.644622-2.912711-8.578844 10.695111-10.490311 23.369955-20.047645l6.690134-5.074489z" fill=""></path>
        </svg>
      ),
      qa: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M0 0m102.4 0l819.2 0q102.4 0 102.4 102.4l0 819.2q0 102.4-102.4 102.4l-819.2 0q-102.4 0-102.4-102.4l0-819.2q0-102.4 102.4-102.4Z" fill="#14CEAA"></path>
          <path d="M441.856 769.2288a314.368 314.368 0 0 1-92.7744-13.6192 23.552 23.552 0 0 0-15.0528 0.3584c-28.5696 10.6496-56.832 22.0672-85.2992 33.0752-16.9984 6.6048-24.576 0.6656-21.8624-17.5104a1960.96 1960.96 0 0 1 12.1856-72.7552 14.08 14.08 0 0 0-4.4544-14.336 275.456 275.456 0 0 1-75.6736-143.7696 280.9344 280.9344 0 0 1 128-293.4272 283.5968 283.5968 0 0 1 329.1648 22.016c115.6096 95.6416 135.2192 265.5232 47.8208 383.6928-55.552 75.1104-131.3792 112.3328-222.1056 116.2752z m-0.512-206.9504h0.6144a23.0912 23.0912 0 0 0 23.2448-22.8864v-4.7616c0.2048-23.7568 8.704-28.3136 28.0064-37.9904 17.2032-8.704 33.0752-21.7088 39.3728-40.4992 12.288-36.9152 5.7344-72.3456-21.5552-99.8912a101.888 101.888 0 0 0-109.9776-22.528c-25.2928 9.8304-42.7008 30.1056-51.9168 54.9376a26.4704 26.4704 0 0 0 20.8896 34.816l0.7168 0.1536a26.8288 26.8288 0 0 0 28.3648-14.9504c7.8848-16.128 23.552-35.8912 53.248-28.928 17.3056 4.096 32.3584 19.456 35.1232 36.6592 3.2768 20.0704-1.792 29.952-21.1968 41.3696-2.4576 1.4848-5.0688 2.816-7.4752 4.3008-23.9104 14.7456-37.9392 35.9424-40.192 63.9488a537.6 537.6 0 0 0-0.1536 13.056 23.04 23.04 0 0 0 22.8864 23.1936z m4.3008 26.1632a31.232 31.232 0 0 0-31.232 30.8224c0.2048 16.4352 14.1824 30.464 30.5664 30.5664a31.744 31.744 0 0 0 30.8224-30.3616c0.0512-16.1792-14.4384-31.0272-30.208-31.0272z" fill="#FFFFFF"></path>
        </svg>
      ),
      comment: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M0 0m136.533333 0l750.933334 0q136.533333 0 136.533333 136.533333l0 750.933334q0 136.533333-136.533333 136.533333l-750.933334 0q-136.533333 0-136.533333-136.533333l0-750.933334q0-136.533333 136.533333-136.533333Z" fill="#1DC9B7"></path>
          <path d="M819.2 477.866667c0-150.869333-136.533333-273.066667-307.2-273.066667s-307.2 122.197333-307.2 273.066667a273.066667 273.066667 0 0 0 166.570667 242.688L356.693333 819.2l173.738667-68.266667c161.109333-9.557333 288.768-128 288.768-273.066666z m-307.2-54.613334a54.613333 54.613333 0 1 1-54.613333 54.613334 54.613333 54.613333 0 0 1 54.613333-54.613334zM286.72 477.866667a54.613333 54.613333 0 1 1 54.613333 54.613333A54.613333 54.613333 0 0 1 286.72 477.866667zM682.666667 532.48a54.613333 54.613333 0 1 1 54.613333-54.613333 54.613333 54.613333 0 0 1-54.613333 54.613333z" fill="#FFFFFF"></path>
        </svg>
      ),
      other: (
        <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="42" height="42">
          <path d="M938.666667 0a85.333333 85.333333 0 0 1 85.333333 85.333333v853.333334a85.333333 85.333333 0 0 1-85.333333 85.333333H85.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h853.333334z" fill="#8573DD"></path>
          <path d="M256 426.666667a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666z m256 0a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666z m256 0a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666zM256 469.333333a42.666667 42.666667 0 1 1 0 85.333334 42.666667 42.666667 0 0 1 0-85.333334z m256 0a42.666667 42.666667 0 1 1 0 85.333334 42.666667 42.666667 0 0 1 0-85.333334z m256 0a42.666667 42.666667 0 1 1 0 85.333334 42.666667 42.666667 0 0 1 0-85.333334z" fill="#8573DD"></path>
        </svg>
      )
    };

    return (
      <div className="ml-auto text-white">
        {iconSvgs[type] || iconSvgs.web}
      </div>
    );
  };

  return (
    <div style={{ padding: '1rem' }}>
      {/* 第一行：地图和今日舆情总量 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '1rem' }}>
        <Col xs={24} lg={16}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    近30天舆情发布地区
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <SimpleMapChart
                data={mapData}
                height="400px"
                showLabel={true}
              />
            </div>
            <div style={{ marginTop: '1rem' }}>
              <Table
                dataSource={tableData}
                columns={tableColumns}
                pagination={false}
                size="small"
                showHeader={true}
              />
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card className="radius-15">
            <div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Title level={5} style={{ margin: 0 }}>
                  <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                  今日舆情总量
                </Title>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '1rem' }}>
                <div id="Sentiment_Today_Craw_Count" style={{ fontSize: '40px' }} className="odometer">
                  {sentimentData.todayCount.toLocaleString()}
                </div>
              </div>

              {/* 平台数据展示 */}
              <Row>
                {Object.entries(sentimentData.platformData).map(([key, value], index) => {
                  const platformNames: { [key: string]: string } = {
                    web: '网页',
                    wechat: '微信',
                    weibo: '微博',
                    app: 'App',
                    forum: '论坛',
                    newspaper: '报刊',
                    video: '视频',
                    toutiao: '头条',
                    sohu: '搜狐号',
                    qa: '问答',
                    comment: '评论',
                    other: '其他类型'
                  };

                  const changePercent = getChangePercent(value, key);

                  return (
                    <Col key={key} xs={12} lg={12} style={{ marginTop: '1rem' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <PlatformIcon type={key} />
                        <div style={{ marginLeft: '0.75rem' }}>
                          <Text style={{ margin: 0, color: '#ffffff' }}>{platformNames[key]} {value}</Text>
                          <br />
                          <Text style={{ margin: 0, color: changePercent >= 0 ? '#28a745' : '#dc3545' }}>
                            环比昨日 {changePercent >= 0 ? '+' : ''}{changePercent}%
                          </Text>
                        </div>
                      </div>
                    </Col>
                  );
                })}
              </Row>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 第二行：平台分布和情感分类 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '1rem' }}>
        <Col xs={24} lg={12}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    近30天平台分布
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <div ref={platformChartRef.chartRef} style={{ height: '400px' }} id="platform-chart"></div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    近30天情感属性
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <div ref={sentimentChartRef.chartRef} style={{ height: '400px' }} id="sentiment-chart"></div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 第三行：舆情趋势图 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '1rem' }}>
        <Col xs={24}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    近30天舆情趋势
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <div ref={trendChartRef.chartRef} style={{ height: '400px' }} id="trend-chart"></div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 第四行：热门文章和最新负面 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    热门文章
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <div
                id="Today-Sentiment-Hot-Article-container"
                style={{
                  height: '400px',
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                <div
                  id="Today-Sentiment-Hot-Article-content"
                  style={{
                    position: 'absolute',
                    top: scrollPosition,
                    width: '100%'
                  }}
                >
                  {loading ? (
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                      <Spin size="large" />
                    </div>
                  ) : (
                    <div>
                      {hotArticles.map((article, index) => (
                        <div
                          key={article.id}
                          className="item"
                          style={{
                            width: '100%',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            boxSizing: 'border-box',
                            paddingRight: '10px',
                            marginBottom: '20px'
                          }}
                        >
                          <Text style={{ color: '#ffffff' }}>{article.title}</Text>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0 }}>
                    <i className="fadeIn animated bx bx-chevrons-right" style={{ color: '#03A9F4' }}></i>
                    最新负面
                  </Title>
                </div>
              </div>
            </div>
            <div>
              <div
                id="Today-Sentiment-Negative-Article-container"
                style={{
                  height: '400px',
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                <div
                  id="Today-Sentiment-Negative-Article-content"
                  style={{
                    position: 'absolute',
                    top: 0,
                    width: '100%'
                  }}
                >
                  {loading ? (
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                      <Spin size="large" />
                    </div>
                  ) : (
                    <div>
                      {negativeArticles.map((article, index) => (
                        <div
                          key={article.id}
                          className="item"
                          style={{
                            width: '100%',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            boxSizing: 'border-box',
                            paddingRight: '10px',
                            marginBottom: '20px'
                          }}
                        >
                          <Text style={{ color: '#ffffff' }}>{article.title}</Text>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Page_Data_Overview;
