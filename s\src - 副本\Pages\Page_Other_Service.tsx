import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  message,
  Tag,
  Typography,
  Modal,
  Descriptions
} from 'antd';
import { SearchOutlined, EyeOutlined, CreditCardOutlined } from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Other_Service.css';

const { Text } = Typography;

// 数据类型定义
interface WorkOrderData {
  id: string;
  WORK_TIME: string;         // 工单时间
  WORK_NAME: string;         // 工单类型
  WORK_CONTENT: string;      // 工单内容
  WORK_PRICE: number | null; // 信用点
  WORK_STATUS: string;       // 状态
  WORK_DESCRIPTION?: string; // 详细描述
}

interface UserInfo {
  user_name: string;
  user_type: string;
  user_phone: string;
  user_email: string;
  user_charger: number;
}

const Page_Other_Service: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [workOrderData, setWorkOrderData] = useState<WorkOrderData[]>([]);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<WorkOrderData | null>(null);

  // 状态映射
  const statusMap: { [key: string]: string } = {
    'Waitting': '已创建',
    'Inline': '待支付',
    'Active': '执行中',
    'Finish': '已完成',
    'Recharge': '已关闭',
    'Failed': '异常中'
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Waitting': return 'default';
      case 'Inline': return 'warning';
      case 'Active': return 'processing';
      case 'Finish': return 'success';
      case 'Recharge': return 'default';
      case 'Failed': return 'error';
      default: return 'default';
    }
  };

  // 初始化数据
  useEffect(() => {
    loadUserInfo();
    loadWorkOrderData();
  }, []);

  // 加载用户信息
  const loadUserInfo = async () => {
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'Sentinel',
      //   data_type: 'Service_User',
      //   data_methods: 'return_user_credit_points',
      //   data_argument: '{}',
      //   data_kwargs: '{}'
      // };
      // const response = await serviceRequests.Async(requestData);
      // setUserInfo(response);

      // 模拟数据
      const mockUserInfo: UserInfo = {
        user_name: '测试用户',
        user_type: '标准用户',
        user_phone: '138****8888',
        user_email: '<EMAIL>',
        user_charger: 1500
      };
      setUserInfo(mockUserInfo);
    } catch (error) {
      message.error('加载用户信息失败');
    }
  };

  // 加载工单数据
  const loadWorkOrderData = async () => {
    setLoading(true);
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'Network',
      //   data_type: 'Service',
      //   data_methods: 'return_work_list',
      //   data_argument: '{}',
      //   data_kwargs: '{}'
      // };
      // const response = await serviceRequests.Async(requestData);
      // setWorkOrderData(response || []);

      // 模拟数据
      const mockData: WorkOrderData[] = [
        {
          id: '1',
          WORK_TIME: '2024-01-15 10:30:00',
          WORK_NAME: '账号验证服务',
          WORK_CONTENT: 'Facebook账号真实性验证',
          WORK_PRICE: 50,
          WORK_STATUS: 'Finish',
          WORK_DESCRIPTION: '对指定Facebook账号进行真实性验证，包括账号活跃度、注册时间、好友数量等多维度检测。'
        },
        {
          id: '2',
          WORK_TIME: '2024-01-14 14:20:00',
          WORK_NAME: '内容审核服务',
          WORK_CONTENT: '社交媒体内容合规性审核',
          WORK_PRICE: null,
          WORK_STATUS: 'Inline',
          WORK_DESCRIPTION: '对社交媒体发布内容进行合规性审核，确保内容符合平台规范。'
        },
        {
          id: '3',
          WORK_TIME: '2024-01-13 09:15:00',
          WORK_NAME: '数据分析服务',
          WORK_CONTENT: '用户行为数据分析报告',
          WORK_PRICE: 200,
          WORK_STATUS: 'Active',
          WORK_DESCRIPTION: '基于用户行为数据生成详细的分析报告，包括用户画像、行为模式等。'
        },
        {
          id: '4',
          WORK_TIME: '2024-01-12 16:45:00',
          WORK_NAME: '安全检测服务',
          WORK_CONTENT: '网站安全漏洞扫描',
          WORK_PRICE: 100,
          WORK_STATUS: 'Failed',
          WORK_DESCRIPTION: '对指定网站进行全面的安全漏洞扫描，识别潜在的安全风险。'
        },
        {
          id: '5',
          WORK_TIME: '2024-01-11 11:30:00',
          WORK_NAME: 'API接口测试',
          WORK_CONTENT: '第三方API接口稳定性测试',
          WORK_PRICE: 80,
          WORK_STATUS: 'Waitting',
          WORK_DESCRIPTION: '对第三方API接口进行稳定性和性能测试，确保接口可靠性。'
        }
      ];
      setWorkOrderData(mockData);
    } catch (error) {
      message.error('加载工单数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const handleViewDetail = (record: WorkOrderData) => {
    setSelectedOrder(record);
    setDetailModalVisible(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadUserInfo();
    loadWorkOrderData();
  };

  // 显示信用点信息
  const handleShowCredit = () => {
    setCreditModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '工单时间',
      dataIndex: 'WORK_TIME',
      key: 'WORK_TIME',
      width: 160,
    },
    {
      title: '工单类型',
      dataIndex: 'WORK_NAME',
      key: 'WORK_NAME',
      width: 150,
    },
    {
      title: '工单内容',
      dataIndex: 'WORK_CONTENT',
      key: 'WORK_CONTENT',
      ellipsis: true,
    },
    {
      title: '信用点',
      dataIndex: 'WORK_PRICE',
      key: 'WORK_PRICE',
      width: 120,
      render: (price: number | null) => (
        price !== null ? (
          <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
            {price}
          </Text>
        ) : (
          <Text style={{ color: '#999' }}>等待人工审核</Text>
        )
      ),
    },
    {
      title: '状态',
      dataIndex: 'WORK_STATUS',
      key: 'WORK_STATUS',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {statusMap[status] || status}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: WorkOrderData) => (
        <Button 
          type="primary" 
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div className="other-service-page">
      {/* 顶部信用点显示 */}
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
        <Button 
          type="primary" 
          size="large"
          icon={<CreditCardOutlined />}
          onClick={handleShowCredit}
          style={{ 
            background: '#52c41a',
            borderColor: '#52c41a'
          }}
        >
          信用点 {userInfo?.user_charger || 0}
        </Button>
      </div>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="primary" 
            icon={<SearchOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新工单
          </Button>
        </Space>
      </Card>

      {/* 工单列表 */}
      <div className="table-responsive">
        <Table
          columns={columns}
          dataSource={workOrderData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1000 }}
        />
      </div>

      {/* 工单详情模态框 */}
      <Modal
        title="工单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedOrder && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="工单时间">
              {selectedOrder.WORK_TIME}
            </Descriptions.Item>
            <Descriptions.Item label="工单类型">
              {selectedOrder.WORK_NAME}
            </Descriptions.Item>
            <Descriptions.Item label="工单内容">
              {selectedOrder.WORK_CONTENT}
            </Descriptions.Item>
            <Descriptions.Item label="信用点">
              {selectedOrder.WORK_PRICE !== null ? (
                <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  {selectedOrder.WORK_PRICE}
                </Text>
              ) : (
                <Text style={{ color: '#999' }}>等待人工审核</Text>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={getStatusColor(selectedOrder.WORK_STATUS)}>
                {statusMap[selectedOrder.WORK_STATUS] || selectedOrder.WORK_STATUS}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="详细描述">
              {selectedOrder.WORK_DESCRIPTION || '暂无详细描述'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 信用点信息模态框 */}
      <Modal
        title="哨兵信用点信息"
        open={creditModalVisible}
        onCancel={() => setCreditModalVisible(false)}
        footer={[
          <Button key="refresh" type="primary" onClick={loadUserInfo}>
            刷新
          </Button>,
          <Button key="close" onClick={() => setCreditModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={500}
      >
        {userInfo && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="用户名称">
              {userInfo.user_name}
            </Descriptions.Item>
            <Descriptions.Item label="用户类型">
              {userInfo.user_type}
            </Descriptions.Item>
            <Descriptions.Item label="联系电话">
              {userInfo.user_phone}
            </Descriptions.Item>
            <Descriptions.Item label="联系邮箱">
              {userInfo.user_email || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="信用点数">
              <Text
                style={{
                  fontSize: '24px',
                  color: '#E67E22',
                  fontWeight: 'bold'
                }}
              >
                {userInfo.user_charger}
              </Text>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default Page_Other_Service;
