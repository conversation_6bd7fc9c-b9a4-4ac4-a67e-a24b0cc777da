import React, { useState, useEffect } from 'react';
import { Card, Tabs, Input, Button, Checkbox, Table, Select, Timeline, Space, Row, Col, Avatar, Tag, Spin, message } from 'antd';
import { SearchOutlined, UserOutlined, BookOutlined, HistoryOutlined } from '@ant-design/icons';
import '../Styles/Page_Information_Insight.css';
import { useServiceRequests } from '@/Core/Core_Control';

const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

// 定义数据接口
interface AccountInfo {
  id: string;
  nickname: string;
  biography: string;
  avatar: string;
  platform: string;
  location: string;
  fans: string;
  following: string;
  type: string;
  direction: string;
  link: string;
  tags: string[];
}

interface TopicPush {
  id: string;
  time: string;
  platform: string;
  title: string;
  content: string;
}

interface StatisticsData {
  total: number;
  positive: number;
  neutral: number;
  negative: number;
  weibo: number;
  wechat: number;
  xiaohongshu: number;
}

const Page_Information_Insight: React.FC = () => {
  const { AsyncTokenRequests } = useServiceRequests();
  const [activeTab, setActiveTab] = useState<string>('1');
  const [accounts, setAccounts] = useState<AccountInfo[]>([]);
  const [topicPushes, setTopicPushes] = useState<TopicPush[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['All']);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statisticsData, setStatisticsData] = useState<StatisticsData>({
    total: 1256,
    positive: 456,
    neutral: 500,
    negative: 300,
    weibo: 400,
    wechat: 350,
    xiaohongshu: 506
  });

  // 平台选项
  const platformOptions = [
    { value: 'All', label: '全部', count: 0 },
    { value: '抖音', label: '抖音', count: 0 },
    { value: '快手', label: '快手', count: 0 },
    { value: '哔哩哔哩', label: '哔哩哔哩', count: 0 },
    { value: '知乎', label: '知乎', count: 0 },
    { value: '微博', label: '微博', count: 0 },
    { value: '小红书', label: '小红书', count: 0 }
  ];

  // 获取账号分析数据
  const fetchAccountAnalysis = async () => {
    setLoading(true);
    try {
      // 构造请求参数，参考 Page_Rumor.js 的 Requests_Sentiment_Rumor_Info
      const Requests_Data = {
        user_id: '', // useServiceRequests 会自动填充
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_rumor_account_info',
        data_argument: '{}',
        data_kwargs: '{}',
      };
      const result = await AsyncTokenRequests(Requests_Data);
      if (result && result.Status === 'Success' && Array.isArray(result.Account_List)) {
        // 适配 AccountInfo 结构
        setAccounts(result.Account_List.map((item: any, idx: number) => ({
          id: item.id || String(idx + 1),
          nickname: item.nickname || '',
          biography: item.biography || '',
          avatar: item.avatar || '',
          platform: item.platform || '',
          location: item.location || '',
          fans: item.fans || '',
          following: item.following || '',
          type: item.type || '',
          direction: item.direction || '',
          link: item.link || '',
          tags: item.tags || [],
        })));
      } else {
        setAccounts([]);
        message.warning('未获取到账号分析数据');
      }
    } catch (error) {
      console.error('获取账号分析失败:', error);
      message.error('获取账号分析失败');
    } finally {
      setLoading(false);
    }
  };


  // 获取专题推送数据
  const fetchTopicPushes = async () => {
    setLoading(true);
    try {
      // 构造请求参数，仿照账号分析，只需 data_methods 不同
      const Requests_Data = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_rumor_topic_push', // 假设API方法名为此，如有不同请调整
        data_argument: '{}',
        data_kwargs: '{}',
      };
      const result = await AsyncTokenRequests(Requests_Data);
      if (result && result.Status === 'Success' && Array.isArray(result.Topic_List)) {
        setTopicPushes(result.Topic_List.map((item: any, idx: number) => ({
          id: item.id || String(idx + 1),
          time: item.time || '',
          platform: item.platform || '',
          title: item.title || '',
          content: item.content || '',
        })));
      } else {
        setTopicPushes([]);
        message.warning('未获取到专题推送数据');
      }
    } catch (error) {
      console.error('获取专题推送失败:', error);
      message.error('获取专题推送失败');
    } finally {
      setLoading(false);
    }
  };


  // 处理平台筛选
  const handlePlatformChange = (checkedValues: string[]) => {
    setSelectedPlatforms(checkedValues);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    console.log('搜索关键词:', value);
    switch (activeTab) {
      case '1':
        fetchAccountAnalysis();
        break;
      case '2':
        fetchTopicPushes();
        break;
      case '3':
        // 历史事件搜索逻辑
        break;
    }
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSearchKeyword('');
    setSelectedPlatforms(['All']);
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAccountAnalysis();
  }, []);

  // 专题推送表格列定义
  const topicColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      align: 'center' as const,
    },
    {
      title: '发帖时间',
      dataIndex: 'time',
      key: 'time',
      width: 140,
      align: 'center' as const,
    },
    {
      title: '发贴平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '文章标题',
      dataIndex: 'title',
      key: 'title',
      width: '25%',
    },
    {
      title: '文章内容',
      dataIndex: 'content',
      key: 'content',
      width: '40%',
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: () => (
        <Button type="primary" size="small" ghost>
          复核
        </Button>
      ),
    },
  ];

  return (
    <div className="information-insight-page">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0 }}>信息洞察</h2>
          <Search
            placeholder="搜索账号、专题或事件..."
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            style={{ width: 300 }}
            onSearch={handleSearch}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab={<span><UserOutlined />账号分析</span>} key="1">
            <div style={{ marginBottom: 16 }}>
              <Space wrap>
                <span>账号平台:</span>
                <Checkbox.Group
                  value={selectedPlatforms}
                  onChange={handlePlatformChange}
                >
                  <Space wrap>
                    {platformOptions.map(option => (
                      <Checkbox key={option.value} value={option.value}>
                        {option.label}({option.count})
                      </Checkbox>
                    ))}
                  </Space>
                </Checkbox.Group>
              </Space>
            </div>

            <Spin spinning={loading}>
              <Row gutter={[16, 16]}>
                {accounts.map((account) => (
                  <Col span={24} key={account.id}>
                    <Card size="small" hoverable>
                      <Row gutter={16}>
                        <Col span={4}>
                          <div style={{ textAlign: 'center' }}>
                            <Avatar
                              size={80}
                              src={account.avatar}
                              icon={<UserOutlined />}
                            />
                            <div style={{ marginTop: 8 }}>
                              <strong>{account.nickname}</strong>
                            </div>
                            <div style={{ color: '#666', fontSize: '12px' }}>
                              {account.platform}
                            </div>
                          </div>
                        </Col>
                        <Col span={20}>
                          <Row gutter={[16, 8]}>
                            <Col span={24}>
                              <div style={{ marginBottom: 8 }}>
                                <strong>简介：</strong>
                                <span>{account.biography}</span>
                              </div>
                            </Col>
                            <Col span={8}>
                              <div><strong>地区：</strong>{account.location}</div>
                            </Col>
                            <Col span={8}>
                              <div><strong>粉丝：</strong>{account.fans}</div>
                            </Col>
                            <Col span={8}>
                              <div><strong>关注：</strong>{account.following}</div>
                            </Col>
                            <Col span={8}>
                              <div><strong>类型：</strong>{account.type}</div>
                            </Col>
                            <Col span={8}>
                              <div><strong>倾向：</strong>{account.direction}</div>
                            </Col>
                            <Col span={8}>
                              <Button type="link" href={account.link} target="_blank">
                                查看详情
                              </Button>
                            </Col>
                            <Col span={24}>
                              <div style={{ marginTop: 8 }}>
                                <strong>标签：</strong>
                                <Space wrap>
                                  {account.tags.map((tag, index) => (
                                    <Tag key={index} color="blue">
                                      {tag}
                                    </Tag>
                                  ))}
                                </Space>
                              </div>
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Spin>
          </TabPane>

          <TabPane tab={<span><BookOutlined />专题推送</span>} key="2">
            <Spin spinning={loading}>
              <Table
                columns={topicColumns}
                dataSource={topicPushes}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                }}
                scroll={{ x: 800 }}
              />
            </Spin>
          </TabPane>

          <TabPane tab={<span><HistoryOutlined />历史舆情大事纪</span>} key="3">
            <Spin spinning={loading}>
              <Timeline mode="left">
                <Timeline.Item label="2024-01-15" color="blue">
                  <Card size="small">
                    <h4>重要舆情事件1</h4>
                    <p>这是一个重要的舆情事件描述，包含了事件的基本信息和影响范围。</p>
                    <Space>
                      <Tag color="red">热点</Tag>
                      <Tag color="orange">政治</Tag>
                      <Tag color="blue">社会</Tag>
                    </Space>
                  </Card>
                </Timeline.Item>
                <Timeline.Item label="2024-01-10" color="green">
                  <Card size="small">
                    <h4>重要舆情事件2</h4>
                    <p>另一个重要的舆情事件，展示了不同类型的舆情信息。</p>
                    <Space>
                      <Tag color="green">正面</Tag>
                      <Tag color="purple">经济</Tag>
                    </Space>
                  </Card>
                </Timeline.Item>
                <Timeline.Item label="2024-01-05" color="red">
                  <Card size="small">
                    <h4>重要舆情事件3</h4>
                    <p>第三个舆情事件的详细描述和相关信息。</p>
                    <Space>
                      <Tag color="red">负面</Tag>
                      <Tag color="cyan">环境</Tag>
                    </Space>
                  </Card>
                </Timeline.Item>
              </Timeline>
            </Spin>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Page_Information_Insight;
