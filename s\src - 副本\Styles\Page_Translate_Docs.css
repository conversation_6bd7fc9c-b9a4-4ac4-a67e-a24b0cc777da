/* 动画定义 */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 容器头部布局 - 确保批量提交按钮在右上角 */
.docs-container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.docs-header-title-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 4px solid #1890ff;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.docs-header-title-wrapper:hover {
  background: rgba(255, 255, 255, 0.08);
}

.docs-header-title-wrapper h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f0f0f0;
  letter-spacing: 0.3px;
}

/* 分界线样式 */
.docs-header-divider {
  border: 0;
  border-top: 1px solid #e8e8e8;
  margin: 20px 0;
}

/* 批量提交按钮样式 */
.docs-batch-submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  margin-left: auto;
}

.docs-batch-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.docs-batch-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.docs-batch-submit-btn:hover::before {
  left: 100%;
}

.docs-batch-submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* 上传组件样式 */
.docs-ff_fileupload_wrap {
  padding: 0 20px;
}

/* 拖拽区域 */
.docs-drop-zone {
  height: 200px;
  border: 2px dashed #c9c9c9;
  border-radius: 8px;
  padding: 32px 20px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: transparent !important;
  position: relative;
  display: flex;
  align-items: center;
}

.docs-drop-zone:hover {
  border-color: #007bff;
  background-color: #f1f9ff;
}

.docs-drop-zone p {
  margin: 0;
  font-size: 16px;
  color: #fff;
}

.docs-drop-zone input[type='file'] {
  display: none;
}

/* 错误信息 */
.docs-error-message {
  color: #d32f2f;
  font-size: 14px;
  margin-top: 12px;
  display: block;
}

/* 文件列表 */
.docs-file-list {
  list-style: none;
  margin-top: 24px;
  padding: 0;
}

/* 文件项样式 */
.docs-file-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: transparent;
  transition: all 0.3s ease;
}

.docs-file-item:hover {
  border-color: #1890ff;
}

.docs-file-icon,
.docs-file-preview {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
  font-size: 24px;
  color: #fff;
}

.docs-file-info {
  flex: 1;
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.docs-file-name {
  margin: 0;
  font-weight: 500;
  color: #fff;
  word-break: break-all;
}

.docs-file-size {
  font-size: 12px;
  color: #fff;
}

.docs-file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 单个提交图标样式 */
.docs-submit-icon {
  cursor: pointer;
  font-size: 18px;
  color: #52c41a;
  padding: 8px;
  border-radius: 50%;
  background: rgba(82, 196, 26, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  position: relative;
  overflow: hidden;
}

.docs-submit-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(82, 196, 26, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.docs-submit-icon:hover {
  color: #389e0d;
  background: rgba(82, 196, 26, 0.15);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.docs-submit-icon:hover::before {
  width: 100%;
  height: 100%;
}

.docs-submit-icon:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
}

/* 删除按钮样式 */
.docs-remove-btn {
  cursor: pointer;
  font-size: 18px;
  color: #ff4d4f;
  padding: 8px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  position: relative;
  overflow: hidden;
}
.docs-remove-btn:hover {
  transform: scale(1.1);
  color: #ff1f1f;
}

.docs-remove-btn:active {
  transform: scale(0.95);
}

/* 上传按钮 */
.upload-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-btn:hover {
  background-color: #0056b3;
}

/* 表格相关样式 */
.docs-container-translate-table {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  margin-top: 40px;
  height: 39px;
}

.docs-table-title-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 4px solid #1890ff;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.docs-table-title-wrapper:hover {
  background: rgba(255, 255, 255, 0.08);
}

.docs-table-title-wrapper h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f0f0f0;
  letter-spacing: 0.3px;
}


.docs-process-table-container {
  padding: 20px;
  border-radius: 8px;
  background-color: transparent;
}

.docs-process-table-container h3 {
  margin: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.docs-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.reset-btn,
.process-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}

.reset-btn:hover {
  background-color: #e6e6e6;
  color: #333;
  border-color: #bfbfbf;
}

.process-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.process-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.process-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.process-table thead {
  background-color: #f5f5f5;
}

.process-table th,
.process-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.process-table th {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.process-table td {
  color: #666;
  font-size: 13px;
}

.process-table tbody tr:hover {
  background-color: #f9f9f9;
}

.process-table tbody tr:last-child td {
  border-bottom: none;
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-等待上传 {
  background-color: black;
  color: #666;
}

.status-上传中 {
  background-color: black;
  color: #52c41a;
}

.status-服务器处理中 {
  background-color: black;
  color: #52c41a;
}

.status-已上传 {
  background-color: black;
  color: #52c41a;
}

.status-上传失败 {
  background-color: black;
  color: #ff4d4f;
}

.delete-icon {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.delete-icon:hover {
  background-color: #fff2f0;
  color: #ff1f1f;
}

/* 进度条样式 */
.docs-progress-container {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.docs-progress-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.docs-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.docs-progress-fill.docs-with-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  min-width: 35px;
}

/* 加载动画 */
.processing-spinner {
  animation: spin 1s linear infinite;
}

/* 自定义表格样式 */
.docs-custom-table .ant-table {
  background-color: #1e263d; /* 表格整体背景 */
  color: #ffffff; /* 表格文字颜色 */
}

/* 表头样式 */
.docs-custom-table .ant-table thead > tr > th {
  background-color: #161f35; /* 更深一点的颜色，突出表头 */
  color: #ffffff; /* 表头文字颜色 */
  border-bottom: 2px solid #2a3452; /* 可选边框 */
}

/* 表格行样式 */
.docs-custom-table .ant-table tbody > tr > td {
  background-color: #1e263d; /* 行背景色 */
  color: #ffffff; /* 行文字颜色 */
}

/* 鼠标悬停行样式 */
.docs-custom-table .ant-table tbody > tr.ant-table-row:hover > td {
  background-color: #2a3452; /* 悬停时的背景色 */
}

/* 分页器样式（如果需要） */
.docs-custom-table .ant-table-pagination {
  background-color: #1e263d;
}

.docs-custom-table .ant-table-placeholder .ant-table-cell {
  background-color: #1e263d !important;
  padding: 0 !important;
}