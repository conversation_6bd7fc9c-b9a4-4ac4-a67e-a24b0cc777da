// UserContext.tsx （注意：必须是 .tsx 扩展名！）
import React, { createContext, useContext, useState, ReactNode } from 'react';

// 定义用户信息的类型
interface User {
  name: string;
  age: number;
}

// 定义上下文的类型
interface UserContextType {
  user: User;
  updateUser: (newUser: User) => void;
}

// 定义上下文的初始值
const initialUser: User = {
  name: '<PERSON>',
  age: 30,
};

// 创建上下文
const Core_Context = createContext<UserContextType | undefined>(undefined);

// 创建上下文提供者组件
interface UserProviderProps {
  children: ReactNode;
}

const CoreProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User>(initialUser);

  const updateUser = (newUser: User) => {
    setUser(newUser);
  };

  const contextValue: UserContextType = {
    user,
    updateUser,
  };

  // 确保 return 语句在组件内部
  return <Core_Context.Provider value={contextValue}>{children}</Core_Context.Provider>;
};

// 创建一个自定义钩子来使用上下文
const SetContext = () => {
  const context = useContext(Core_Context);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export { CoreProvider, SetContext };