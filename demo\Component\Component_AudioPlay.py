
import sys
import numpy as np
import librosa
import pyaudio
from PySide6.QtWidgets import <PERSON>App<PERSON>, QWidget, QVBoxLayout, QSlider, QLabel, QPushButton
from PySide6.QtCore import Qt, QThread, Signal
from PySide6 import QtWidgets,QtCore,QtGui

class AudioPlayer(QThread):
    update_signal = Signal(int)
    finished_signal = Signal()

    def __init__(self, file_path, sample_rate=44100, chunk_size=1024):
        super().__init__()
        self.file_path = file_path
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.running = False
        self.paused = False
        self.current_position = 0
        self.y = None
        self.p = None
        self.stream = None

    def run(self):
        self.running = True
        try:
            # 使用librosa加载音频文件
            self.y, sr = librosa.load(self.file_path, sr=self.sample_rate, mono=True)

            # 初始化 pyaudio
            self.p = pyaudio.PyAudio()
            self.stream = self.p.open(format=pyaudio.paFloat32,
                                      channels=1,
                                      rate=self.sample_rate,
                                      output=True)

            # 分块处理音频数据
            total_chunks = len(self.y) // self.chunk_size
            for i in range(0, len(self.y), self.chunk_size):
                if not self.running:
                    break

                chunk = self.y[i:i + self.chunk_size]
                if len(chunk) < self.chunk_size:
                    chunk = np.pad(chunk, (0, self.chunk_size - len(chunk)))

                # 播放音频数据
                self.stream.write(chunk.astype(np.float32).tobytes())

                # 更新进度
                self.current_position = i
                self.update_signal.emit(int((i / len(self.y)) * 100))

            self.finished_signal.emit()

        except Exception as e:
            print(f"音频播放错误: {e}")

        finally:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            if self.p:
                self.p.terminate()

    def stop(self):
        self.running = False
        self.wait()

    def pause(self):
        self.paused = not self.paused
        if self.paused:
            self.stream.stop_stream()
        else:
            self.stream.start_stream()

    def seek(self, position):
        # 将位置转换为样本索引
        self.current_position = int(position / 100 * len(self.y))
        self.update_signal.emit(position)

class MusicProgressBar(QWidget):
    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path
        self.initUI()

    def initUI(self):
        # 创建 QVBoxLayout
        layout = QVBoxLayout(self)

        # 创建 QSlider
        self.slider = QSlider(Qt.Horizontal)  # 设置为水平方向
        self.slider.setMinimum(0)  # 最小值
        self.slider.setMaximum(100)  # 最大值
        self.slider.setValue(0)  # 初始值
        self.slider.setTickPosition(QSlider.TicksBelow)  # 显示刻度
        self.slider.setTickInterval(10)  # 刻度间隔
        self.slider.valueChanged.connect(self.on_slider_value_changed)  # 连接信号槽

        # 创建 QLabel 用于显示当前值
        self.label = QLabel("",)
        self.label.setAlignment(Qt.AlignCenter)

        # 创建 QPushButton 用于控制播放
        self.play_button = QPushButton("Play")
        self.play_button.clicked.connect(self.on_play_button_clicked)

        # 创建 AudioPlayer 线程
        self.audio_player = AudioPlayer(self.file_path)
        self.audio_player.update_signal.connect(self.on_audio_progress)
        self.audio_player.finished_signal.connect(self.on_audio_finished)

        # 将 QSlider、QLabel 和 QPushButton 添加到布局

        StyleSheet_QLabel = """
                                         QPushButton {
                                           background-color: rgba(40, 52, 80, 0.3);;
                                             border: 1px solid rgba(0, 180, 255, 60);
                                             border-radius: 4px;

                                             color:rgba(0, 255, 136, 255);

                                         }
                                         QPushButton:hover {
                                             background-color: rgba(0, 100, 150, 150);
                                         }
                                     """

        QLabel_Function = QtWidgets.QLabel()
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(QLabel_Function)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 8, 8, 8)



        QPushButton_Back = QtWidgets.QPushButton()
        QPushButton_Back.setText("停止")
        QPushButton_Back.setMinimumHeight(30)
        QPushButton_Back.setMaximumHeight(30)
        QPushButton_Back.setStyleSheet(StyleSheet_QLabel)

        QPushButton_Paly = QtWidgets.QPushButton()
        QPushButton_Paly.setMinimumHeight(30)
        QPushButton_Paly.setMaximumHeight(30)
        QPushButton_Paly.setText("播放")
        QPushButton_Paly.setStyleSheet(StyleSheet_QLabel)
        QPushButton_Paly.clicked.connect(self.on_play_button_clicked)


        QPushButton_Pause = QtWidgets.QPushButton()
        QPushButton_Pause.setMinimumHeight(30)
        QPushButton_Pause.setMaximumHeight(30)
        QPushButton_Pause .setText("暂停")
        QPushButton_Pause.setStyleSheet(StyleSheet_QLabel)

        QPushButton_Forward  = QtWidgets.QPushButton()
        QPushButton_Forward.setMinimumHeight(30)
        QPushButton_Forward.setMaximumHeight(30)
        QPushButton_Forward .setText("快进")
        QPushButton_Forward.setStyleSheet(StyleSheet_QLabel)

        QPushButton_Stop = QtWidgets.QPushButton()
        QPushButton_Stop.setMinimumHeight(30)
        QPushButton_Stop.setMaximumHeight(30)
        QPushButton_Stop.setText("停止")
        QPushButton_Stop.setStyleSheet(StyleSheet_QLabel)



        QHBoxLayout_Content.addWidget(QPushButton_Back)
        QHBoxLayout_Content.addWidget(QPushButton_Paly)
        QHBoxLayout_Content.addWidget(QPushButton_Pause)
        QHBoxLayout_Content.addWidget(QPushButton_Forward)
        QHBoxLayout_Content.addWidget(QPushButton_Stop)


        layout.addWidget(self.slider)
        # layout.addWidget(self.label)
        layout.addWidget(QLabel_Function)

        # 设置窗口属性
        self.setWindowTitle("Music Progress Bar")
        self.resize(400, 100)

    def on_slider_value_changed(self, value):
        # 根据 QSlider 的值更新 QLabel 的文本
        self.label.setText(f"{value}%")
        if self.audio_player.isRunning():
            self.audio_player.seek(value)

    def on_play_button_clicked(self):
        if self.audio_player.isRunning():
            self.audio_player.pause()
            self.play_button.setText("Play")
        else:
            self.audio_player.start()
            self.play_button.setText("Pause")

    def on_audio_progress(self, progress):
        # 更新进度条
        self.slider.setValue(progress)

    def on_audio_finished(self):
        # 播放结束
        self.play_button.setText("Play")
        self.slider.setValue(0)
        self.label.setText("0%")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MusicProgressBar(r"D:\M800001ziKgJ3o5Ipp.mp3")
    window.show()
    sys.exit(app.exec())