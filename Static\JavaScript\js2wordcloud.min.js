!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Js2WordCloud=e():t.Js2WordCloud=e()}(this,function(){return function(t){function e(a){if(i[a])return i[a].exports;var n=i[a]={exports:{},id:a,loaded:!1};return t[a].call(n.exports,n,n.exports,e),n.loaded=!0,n.exports}var i={};return e.m=t,e.c=i,e.p="",e(0)}([function(t,e,i){"use strict";function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t){t.list&&t.list.sort(function(t,e){return e[1]-t[1]})}function o(t){if(this._maskCanvas){t.clearCanvas=!1;var e=window.document.createElement("canvas").getContext("2d");e.fillStyle=t.backgroundColor||"#fff",e.fillRect(0,0,1,1);var i=e.getImageData(0,0,1,1).data,a=window.document.createElement("canvas");a.width=this._canvas.width,a.height=this._canvas.height;var n=a.getContext("2d");n.drawImage(this._maskCanvas,0,0,this._maskCanvas.width,this._maskCanvas.height,0,0,a.width,a.height);for(var o=n.getImageData(0,0,a.width,a.height),r=n.createImageData(o),s=0;s<o.data.length;s+=4)o.data[s+3]>128?(r.data[s]=i[0],r.data[s+1]=i[1],r.data[s+2]=i[2],r.data[s+3]=i[3]):(r.data[s]=i[0],r.data[s+1]=i[1],r.data[s+2]=i[2],r.data[s+3]=i[3]?i[3]-1:1);n.putImageData(r,0,0),n=this._canvas.getContext("2d"),n.clearRect(0,0,this._canvas.width,this._canvas.height),n.drawImage(a,0,0)}if(this._dataEmpty()&&t&&t.noDataLoadingOption){var l="";t.noDataLoadingOption.textStyle&&("string"==typeof t.noDataLoadingOption.textStyle.color&&(l+="color: "+t.noDataLoadingOption.textStyle.color+";"),"number"==typeof t.noDataLoadingOption.textStyle.fontSize&&(l+="font-size: "+t.noDataLoadingOption.textStyle.fontSize+"px;")),"string"==typeof t.noDataLoadingOption.backgroundColor&&(this._dataMask.style.backgroundColor=t.noDataLoadingOption.backgroundColor);var c=t.noDataLoadingOption.text||"";this._showMask(h+'<span class="__wc_no_data_text__" style="'+l+'">'+c+"</span>"+f)}else this._showMask(""),this._wordcloud2=d(this._canvas,t)}function r(t){this._maskCanvas=window.document.createElement("canvas"),this._maskCanvas.width=500,this._maskCanvas.height=500;var e=this._maskCanvas.getContext("2d");e.fillStyle="#000000",e.beginPath(),e.arc(250,250,240,0,2*Math.PI,!0),e.closePath(),e.fill();for(var i=e.getImageData(0,0,this._maskCanvas.width,this._maskCanvas.height),a=e.createImageData(i),n=0;n<i.data.length;n+=4){var r=i.data[n]+i.data[n+1]+i.data[n+2],s=i.data[n+3];s<128||r>384?(a.data[n]=a.data[n+1]=a.data[n+2]=255,a.data[n+3]=0):(a.data[n]=a.data[n+1]=a.data[n+2]=0,a.data[n+3]=255)}e.putImageData(a,0,0),o.call(this,t)}function s(t){var e=this,i=window.document.createElement("img");i.crossOrigin="Anonymous",i.onload=function(){e._maskCanvas=window.document.createElement("canvas"),e._maskCanvas.width=i.width,e._maskCanvas.height=i.height;var a=e._maskCanvas.getContext("2d");a.drawImage(i,0,0,i.width,i.height);for(var n=a.getImageData(0,0,e._maskCanvas.width,e._maskCanvas.height),r=a.createImageData(n),s=0;s<n.data.length;s+=4){var l=n.data[s]+n.data[s+1]+n.data[s+2],d=n.data[s+3];d<128||l>384?(r.data[s]=r.data[s+1]=r.data[s+2]=255,r.data[s+3]=0):(r.data[s]=r.data[s+1]=r.data[s+2]=0,r.data[s+3]=255)}a.putImageData(r,0,0),o.call(e,t)},i.onerror=function(){o.call(this,t)},i.src=t.imageShape}Object.defineProperty(e,"__esModule",{value:!0});var l=function(){function t(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}return function(e,i,a){return i&&t(e.prototype,i),a&&t(e,a),e}}(),d=i(1),c=(i(2),i(6).default),h='<div class="__wc_loading_wrapper__"><div style="padding-left: 60px;" class="__wc_loading_wrapper_item__"><div class="__wc_loading_wrapper_item_inner__">',f="</div></div></div>",u=e.Js2WordCloud=function(){function t(e){a(this,t),this._container=e,this._wrapper=null,this._canvas=null,this._maskCanvas=null,this._dataMask=null,this._tooltip=null,this._wordcloud2=null,this._option=null,this._init()}return l(t,[{key:"setOption",value:function(t){var e=this;this._option=c.copy(t,!0);var i=this._option.hover;this._option.fontFamily=this._option.fontFamily||"Microsoft YaHei,Helvetica,Times,serif",this._fixWeightFactor(this._option),this._maskCanvas=null;var a=function(t,a,n){if(t){var o=t[0]+": "+t[1];"function"==typeof e._option.tooltip.formatter&&(o=e._option.tooltip.formatter(t)),e._tooltip.innerHTML=o,e._tooltip.style.top=n.offsetY+10+"px",e._tooltip.style.left=n.offsetX+15+"px",e._tooltip.style.display="block",e._wrapper.style.cursor="pointer"}else e._tooltip.style.display="none",e._wrapper.style.cursor="default";i&&i(t,a,n)};t.tooltip&&t.tooltip.show===!0&&(this._tooltip||(this._tooltip=window.document.createElement("div"),this._tooltip.className="__wc_tooltip__",this._tooltip.style.backgroundColor=t.tooltip.backgroundColor||"rgba(0, 0, 0, 0.701961)",this._tooltip.style.color="#fff",this._tooltip.style.padding="5px",this._tooltip.style.borderRadius="5px",this._tooltip.style.fontSize="12px",this._tooltip.style.fontFamily=t.fontFamily||this._option.fontFamily,this._tooltip.style.lineHeight=1.4,this._tooltip.style.webkitTransition="left 0.2s, top 0.2s",this._tooltip.style.mozTransition="left 0.2s, top 0.2s",this._tooltip.style.transition="left 0.2s, top 0.2s",this._tooltip.style.position="absolute",this._tooltip.style.whiteSpace="nowrap",this._tooltip.style.zIndex=999,this._tooltip.style.display="none",this._wrapper.appendChild(this._tooltip),this._container.onmouseout=function(){e._tooltip.style.display="none"}),this._option.hover=a),n(this._option),this._option&&/\.(jpg|png)$/.test(this._option.imageShape)?s.call(this,this._option):"circle"===this._option.shape?r.call(this,this._option):o.call(this,this._option)}},{key:"on",value:function(){}},{key:"showLoading",value:function(t){var e,i="正在加载...",a='<div class="__wc_loading__"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>';if(t)if(t.backgroundColor&&(this._dataMask.style.backgroundColor=t.backgroundColor),e=void 0===t.text?i:t.text,"spin"===t.effect){this._showMask(h+a+e+f);var n=this._dataMask.childNodes[0].childNodes[0],o=n.style.paddingLeft;n.style.paddingLeft=parseInt(o)+45+"px"}else this._showMask(h+e+f);else this._showMask(h+a+i+f)}},{key:"hideLoading",value:function(){this._dataMask&&(this._dataMask.style.display="none")}},{key:"resize",value:function(){this._canvas.width=this._container.clientWidth,this._canvas.height=this._container.clientHeight,o.call(this,this._option)}},{key:"_init",value:function(){var t=this._container.clientWidth,e=this._container.clientHeight;this._container.innerHTML="",this._wrapper=window.document.createElement("div"),this._wrapper.style.position="relative",this._wrapper.style.width="100%",this._wrapper.style.height="inherit",this._dataMask=window.document.createElement("div"),this._dataMask.height="inherit",this._dataMask.style.textAlign="center",this._dataMask.style.color="#888",this._dataMask.style.fontSize="14px",this._dataMask.style.position="absolute",this._dataMask.style.left="0",this._dataMask.style.right="0",this._dataMask.style.top="0",this._dataMask.style.bottom="0",this._dataMask.style.display="none",this._wrapper.appendChild(this._dataMask),this._container.appendChild(this._wrapper),this._canvas=window.document.createElement("canvas"),this._canvas.width=t,this._canvas.height=e,this._wrapper.appendChild(this._canvas)}},{key:"_fixWeightFactor",value:function(t){if(t.maxFontSize="number"==typeof t.maxFontSize?t.maxFontSize:60,t.minFontSize="number"==typeof t.minFontSize?t.minFontSize:12,t.list&&t.list.length>0){for(var e=t.list[0][1],i=0,a=0,n=t.list.length;a<n;a++)e>t.list[a][1]&&(e=t.list[a][1]),i<t.list[a][1]&&(i=t.list[a][1]);if(i>e){var o="number"==typeof t.fontSizeFactor?t.fontSizeFactor:.1,r=(t.maxFontSize-t.minFontSize)/(Math.pow(i,o)-Math.pow(e,o)),s=t.maxFontSize-r*Math.pow(i,o);t.weightFactor=function(t){return Math.ceil(r*Math.pow(t,o)+s)}}else t.weightFactor=function(e){return t.minFontSize}}}},{key:"_showMask",value:function(t){this._dataMask&&(this._dataMask.innerHTML=t,""===t?this._dataMask.style.display="none":this._dataMask.style.display="block")}},{key:"_dataEmpty",value:function(){return!this._option||!this._option.list||this._option.list.length<=0}}]),t}();t.exports=u},function(t,e,i){var a,n;(function(){/*!
	   * wordcloud2.js
	   * http://timdream.org/wordcloud2.js/
	   *
	   * Copyright 2011 - 2013 Tim Chien
	   * Released under the MIT license
	   */
"use strict";window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var t=[void 0],e="zero-timeout-message",i=function(i){var a=t.length;return t.push(i),window.postMessage(e+a.toString(36),"*"),a};return window.addEventListener("message",function(i){if("string"==typeof i.data&&i.data.substr(0,e.length)===e){i.stopImmediatePropagation();var a=parseInt(i.data.substr(e.length),36);t[a]&&(t[a](),t[a]=void 0)}},!0),window.clearImmediate=function(e){t[e]&&(t[e]=void 0)},i}()||function(t){window.setTimeout(t,0)}}()),window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(t){window.clearTimeout(t)}}()),function(i){var o=function(){var t=document.createElement("canvas");if(!t||!t.getContext)return!1;var e=t.getContext("2d");return!!e.getImageData&&(!!e.fillText&&(!!Array.prototype.some&&!!Array.prototype.push))}(),r=function(){if(o){for(var t,e,i=document.createElement("canvas").getContext("2d"),a=20;a;){if(i.font=a.toString(10)+"px sans-serif",i.measureText("Ｗ").width===t&&i.measureText("m").width===e)return a+1;t=i.measureText("Ｗ").width,e=i.measureText("m").width,a--}return 0}}(),s=function(t){for(var e,i,a=t.length;a;e=Math.floor(Math.random()*a),i=t[--a],t[a]=t[e],t[e]=i);return t},l=function(t,e){function i(t,e){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(e-t)+t).toFixed()+"%)"}if(o){Array.isArray(t)||(t=[t]),t.forEach(function(e,i){if("string"==typeof e){if(t[i]=document.getElementById(e),!t[i])throw"The element id specified is not found."}else if(!e.tagName&&!e.appendChild)throw"You must pass valid HTML elements, or ID of the element."});var a={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(e)for(var n in e)n in a&&(a[n]=e[n]);if("function"!=typeof a.weightFactor){var l=a.weightFactor;a.weightFactor=function(t){return t*l}}if("function"!=typeof a.shape)switch(a.shape){case"circle":default:a.shape="circle";break;case"cardioid":a.shape=function(t){return 1-Math.sin(t)};break;case"diamond":case"square":a.shape=function(t){var e=t%(2*Math.PI/4);return 1/(Math.cos(e)+Math.sin(e))};break;case"triangle-forward":a.shape=function(t){var e=t%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"triangle":case"triangle-upright":a.shape=function(t){var e=(t+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"pentagon":a.shape=function(t){var e=(t+.955)%(2*Math.PI/5);return 1/(Math.cos(e)+.726543*Math.sin(e))};break;case"star":a.shape=function(t){var e=(t+.955)%(2*Math.PI/10);return(t+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-e)+3.07768*Math.sin(2*Math.PI/10-e)):1/(Math.cos(e)+3.07768*Math.sin(e))}}a.gridSize=Math.max(Math.floor(a.gridSize),4);var d,c,h,f,u,p,_,m=a.gridSize,v=m-a.maskGapWidth,g=Math.abs(a.maxRotation-a.minRotation),w=Math.min(a.maxRotation,a.minRotation);switch(a.color){case"random-dark":_=function(){return i(10,50)};break;case"random-light":_=function(){return i(50,90)};break;default:"function"==typeof a.color&&(_=a.color)}var y=null;"function"==typeof a.classes&&(y=a.classes);var b,x=!1,k=[],M=function(t){var e,i,a=t.currentTarget,n=a.getBoundingClientRect();t.touches?(e=t.touches[0].clientX,i=t.touches[0].clientY):(e=t.clientX,i=t.clientY);var o=e-n.left,r=i-n.top,s=Math.floor(o*(a.width/n.width||1)/m),l=Math.floor(r*(a.height/n.height||1)/m);return k[s][l]},C=function(t){var e=M(t);if(b!==e)return b=e,e?void a.hover(e.item,e.dimension,t):void a.hover(void 0,void 0,t)},S=function(t){var e=M(t);e&&(a.click(e.item,e.dimension,t),t.preventDefault())},I=[],T=function(t){if(I[t])return I[t];var e=8*t,i=e,n=[];for(0===t&&n.push([f[0],f[1],0]);i--;){var o=1;"circle"!==a.shape&&(o=a.shape(i/e*2*Math.PI)),n.push([f[0]+t*o*Math.cos(-i/e*2*Math.PI),f[1]+t*o*Math.sin(-i/e*2*Math.PI)*a.ellipticity,i/e*2*Math.PI])}return I[t]=n,n},E=function(){return a.abortThreshold>0&&(new Date).getTime()-p>a.abortThreshold},L=function(){return 0===a.rotateRatio?0:Math.random()>a.rotateRatio?0:0===g?w:w+Math.random()*g},O=function(t,e,i){var n=!1,o=a.weightFactor(e);if(o<=a.minSize)return!1;var s=1;o<r&&(s=function(){for(var t=2;t*o<r;)t+=2;return t}());var l=document.createElement("canvas"),d=l.getContext("2d",{willReadFrequently:!0});d.font=a.fontWeight+" "+(o*s).toString(10)+"px "+a.fontFamily;var c=d.measureText(t).width/s,h=Math.max(o*s,d.measureText("m").width,d.measureText("Ｗ").width)/s,f=c+2*h,u=3*h,p=Math.ceil(f/m),_=Math.ceil(u/m);f=p*m,u=_*m;var v=-c/2,g=.4*-h,w=Math.ceil((f*Math.abs(Math.sin(i))+u*Math.abs(Math.cos(i)))/m),y=Math.ceil((f*Math.abs(Math.cos(i))+u*Math.abs(Math.sin(i)))/m),b=y*m,x=w*m;l.setAttribute("width",b),l.setAttribute("height",x),n&&(document.body.appendChild(l),d.save()),d.scale(1/s,1/s),d.translate(b*s/2,x*s/2),d.rotate(-i),d.font=a.fontWeight+" "+(o*s).toString(10)+"px "+a.fontFamily,d.fillStyle="#000",d.textBaseline="middle",d.fillText(t,v*s,(g+.5*o)*s);var k=d.getImageData(0,0,b,x).data;if(E())return!1;n&&(d.strokeRect(v*s,g,c*s,h*s),d.restore());for(var M,C,S,I=[],T=y,L=[w/2,y/2,w/2,y/2];T--;)for(M=w;M--;){S=m;t:{for(;S--;)for(C=m;C--;)if(k[4*((M*m+S)*b+(T*m+C))+3]){I.push([T,M]),T<L[3]&&(L[3]=T),T>L[1]&&(L[1]=T),M<L[0]&&(L[0]=M),M>L[2]&&(L[2]=M),n&&(d.fillStyle="rgba(255, 0, 0, 0.5)",d.fillRect(T*m,M*m,m-.5,m-.5));break t}n&&(d.fillStyle="rgba(0, 0, 255, 0.5)",d.fillRect(T*m,M*m,m-.5,m-.5))}}return n&&(d.fillStyle="rgba(0, 255, 0, 0.5)",d.fillRect(L[3]*m,L[0]*m,(L[1]-L[3]+1)*m,(L[2]-L[0]+1)*m)),{mu:s,occupied:I,bounds:L,gw:y,gh:w,fillTextOffsetX:v,fillTextOffsetY:g,fillTextWidth:c,fillTextHeight:h,fontSize:o}},F=function(t,e,i,n,o){for(var r=o.length;r--;){var s=t+o[r][0],l=e+o[r][1];if(s>=c||l>=h||s<0||l<0){if(!a.drawOutOfBound)return!1}else if(!d[s][l])return!1}return!0},R=function(e,i,n,o,r,s,l,d,c){var h,f=n.fontSize;h=_?_(o,r,f,s,l):a.color;var u;u=y?y(o,r,f,s,l):a.classes;var p,v=n.bounds;p={x:(e+v[3])*m,y:(i+v[0])*m,w:(v[1]-v[3]+1)*m,h:(v[2]-v[0]+1)*m},t.forEach(function(t){if(t.getContext){var r=t.getContext("2d"),s=n.mu;r.save(),r.scale(1/s,1/s),r.font=a.fontWeight+" "+(f*s).toString(10)+"px "+a.fontFamily,r.fillStyle=h,r.translate((e+n.gw/2)*m*s,(i+n.gh/2)*m*s),0!==d&&r.rotate(-d),r.textBaseline="middle",r.fillText(o,n.fillTextOffsetX*s,(n.fillTextOffsetY+.5*f)*s),r.restore()}else{var l=document.createElement("span"),p="";p="rotate("+-d/Math.PI*180+"deg) ",1!==n.mu&&(p+="translateX(-"+n.fillTextWidth/4+"px) scale("+1/n.mu+")");var _={position:"absolute",display:"block",font:a.fontWeight+" "+f*n.mu+"px "+a.fontFamily,left:(e+n.gw/2)*m+n.fillTextOffsetX+"px",top:(i+n.gh/2)*m+n.fillTextOffsetY+"px",width:n.fillTextWidth+"px",height:n.fillTextHeight+"px",lineHeight:f+"px",whiteSpace:"nowrap",transform:p,webkitTransform:p,msTransform:p,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};h&&(_.color=h),l.textContent=o;for(var v in _)l.style[v]=_[v];if(c)for(var g in c)l.setAttribute(g,c[g]);u&&(l.className+=u),t.appendChild(l)}})},z=function(e,i,a,n,o){if(!(e>=c||i>=h||e<0||i<0)){if(d[e][i]=!1,a){var r=t[0].getContext("2d");r.fillRect(e*m,i*m,v,v)}x&&(k[e][i]={item:o,dimension:n})}},D=function(e,i,n,o,r,s){var l,d=r.occupied,f=a.drawMask;f&&(l=t[0].getContext("2d"),l.save(),l.fillStyle=a.maskColor);var u;if(x){var p=r.bounds;u={x:(e+p[3])*m,y:(i+p[0])*m,w:(p[1]-p[3]+1)*m,h:(p[2]-p[0]+1)*m}}for(var _=d.length;_--;){var v=e+d[_][0],g=i+d[_][1];v>=c||g>=h||v<0||g<0||z(v,g,f,u,s)}f&&l.restore()},P=function(t){var e,i,n;Array.isArray(t)?(e=t[0],i=t[1]):(e=t.word,i=t.weight,n=t.attributes);var o=L(),r=O(e,i,o);if(!r)return!1;if(E())return!1;if(!a.drawOutOfBound){var l=r.bounds;if(l[1]-l[3]+1>c||l[2]-l[0]+1>h)return!1}for(var d=u+1,f=function(a){var s=Math.floor(a[0]-r.gw/2),l=Math.floor(a[1]-r.gh/2),c=r.gw,h=r.gh;return!!F(s,l,c,h,r.occupied)&&(R(s,l,r,e,i,u-d,a[2],o,n),D(s,l,c,h,r,t),!0)};d--;){var p=T(u-d);a.shuffle&&(p=[].concat(p),s(p));var _=p.some(f);if(_)return!0}return!1},j=function(e,i,a){return i?!t.some(function(t){var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,!0,i,a||{}),!t.dispatchEvent(n)},this):void t.forEach(function(t){var n=document.createEvent("CustomEvent");n.initCustomEvent(e,!0,i,a||{}),t.dispatchEvent(n)},this)},A=function(){var e=t[0];if(e.getContext)c=Math.ceil(e.width/m),h=Math.ceil(e.height/m);else{var i=e.getBoundingClientRect();c=Math.ceil(i.width/m),h=Math.ceil(i.height/m)}if(j("wordcloudstart",!0)){f=a.origin?[a.origin[0]/m,a.origin[1]/m]:[c/2,h/2],u=Math.floor(Math.sqrt(c*c+h*h)),d=[];var n,o,r;if(!e.getContext||a.clearCanvas)for(t.forEach(function(t){if(t.getContext){var e=t.getContext("2d");e.fillStyle=a.backgroundColor,e.clearRect(0,0,c*(m+1),h*(m+1)),e.fillRect(0,0,c*(m+1),h*(m+1))}else t.textContent="",t.style.backgroundColor=a.backgroundColor,t.style.position="relative"}),n=c;n--;)for(d[n]=[],o=h;o--;)d[n][o]=!0;else{var s=document.createElement("canvas").getContext("2d");s.fillStyle=a.backgroundColor,s.fillRect(0,0,1,1);var l=s.getImageData(0,0,1,1).data,_=e.getContext("2d").getImageData(0,0,c*m,h*m).data;n=c;for(var v,g;n--;)for(d[n]=[],o=h;o--;){g=m;t:for(;g--;)for(v=m;v--;)for(r=4;r--;)if(_[4*((o*m+g)*c*m+(n*m+v))+r]!==l[r]){d[n][o]=!1;break t}d[n][o]!==!1&&(d[n][o]=!0)}_=s=l=void 0}if(a.hover||a.click){for(x=!0,n=c+1;n--;)k[n]=[];a.hover&&e.addEventListener("mousemove",C),a.click&&(e.addEventListener("click",S),e.addEventListener("touchstart",S),e.addEventListener("touchend",function(t){t.preventDefault()}),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",function t(){e.removeEventListener("wordcloudstart",t),e.removeEventListener("mousemove",C),e.removeEventListener("click",S),b=void 0})}r=0;var w,y;0!==a.wait?(w=window.setTimeout,y=window.clearTimeout):(w=window.setImmediate,y=window.clearImmediate);var M=function(e,i){t.forEach(function(t){t.addEventListener(e,i)},this)},I=function(e,i){t.forEach(function(t){t.removeEventListener(e,i)},this)},T=function t(){I("wordcloudstart",t),y(L)};M("wordcloudstart",T);var L=w(function t(){if(r>=a.list.length)return y(L),j("wordcloudstop",!1),void I("wordcloudstart",T);p=(new Date).getTime();var e=P(a.list[r]),i=!j("wordclouddrawn",!0,{item:a.list[r],drawn:e});return E()||i?(y(L),a.abort(),j("wordcloudabort",!1),j("wordcloudstop",!1),void I("wordcloudstart",T)):(r++,void(L=w(t,a.wait)))},a.wait)}};A()}};l.isSupported=o,l.minFontSize=r,i.WordCloud=l,a=[],n=function(){return l}.apply(e,a),!(void 0!==n&&(t.exports=n))}(this)}).call(window)},function(t,e,i){var a=i(3);"string"==typeof a&&(a=[[t.id,a,""]]);i(5)(a,{});a.locals&&(t.exports=a.locals)},function(t,e,i){e=t.exports=i(4)(),e.push([t.id,"@-webkit-keyframes __wc_loading_animation__{50%{opacity:.3;-webkit-transform:scale(.4);transform:scale(.4)}to{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@keyframes __wc_loading_animation__{50%{opacity:.3;-webkit-transform:scale(.4);transform:scale(.4)}to{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}.__wc_loading_wrapper__{display:table;height:100%;width:100%}.__wc_loading_wrapper_item__{display:table-cell;vertical-align:middle;text-align:center;position:relative;padding-right:60px;line-height:1.4}.__wc_loading_wrapper_item_inner__{position:relative;text-align:left;display:inline-block}.__wc_loading__{position:absolute;top:50%;left:-40px;margin-top:-6px}.__wc_loading__>div:first-child{top:15px;left:0;-webkit-animation:__wc_loading_animation__ 1s 0s infinite linear;animation:__wc_loading_animation__ 1s 0s infinite linear}.__wc_loading__>div:nth-child(2){top:10.22726px;left:10.22726px;-webkit-animation:__wc_loading_animation__ 1s .12s infinite linear;animation:__wc_loading_animation__ 1s .12s infinite linear}.__wc_loading__>div:nth-child(3){top:0;left:15px;-webkit-animation:__wc_loading_animation__ 1s .24s infinite linear;animation:__wc_loading_animation__ 1s .24s infinite linear}.__wc_loading__>div:nth-child(4){top:-10.22726px;left:10.22726px;-webkit-animation:__wc_loading_animation__ 1s .36s infinite linear;animation:__wc_loading_animation__ 1s .36s infinite linear}.__wc_loading__>div:nth-child(5){top:-15px;left:0;-webkit-animation:__wc_loading_animation__ 1s .48s infinite linear;animation:__wc_loading_animation__ 1s .48s infinite linear}.__wc_loading__>div:nth-child(6){top:-10.22726px;left:-10.22726px;-webkit-animation:__wc_loading_animation__ 1s .6s infinite linear;animation:__wc_loading_animation__ 1s .6s infinite linear}.__wc_loading__>div:nth-child(7){top:0;left:-15px;-webkit-animation:__wc_loading_animation__ 1s .72s infinite linear;animation:__wc_loading_animation__ 1s .72s infinite linear}.__wc_loading__>div:nth-child(8){top:10.22726px;left:-10.22726px;-webkit-animation:__wc_loading_animation__ 1s .84s infinite linear;animation:__wc_loading_animation__ 1s .84s infinite linear}.__wc_loading__>div{background-color:#d3d3d3;width:10px;height:10px;border-radius:100%;margin:2px;-webkit-animation-fill-mode:both;animation-fill-mode:both;position:absolute}",""])},function(t,e){t.exports=function(){var t=[];return t.toString=function(){for(var t=[],e=0;e<this.length;e++){var i=this[e];i[2]?t.push("@media "+i[2]+"{"+i[1]+"}"):t.push(i[1])}return t.join("")},t.i=function(e,i){"string"==typeof e&&(e=[[null,e,""]]);for(var a={},n=0;n<this.length;n++){var o=this[n][0];"number"==typeof o&&(a[o]=!0)}for(n=0;n<e.length;n++){var r=e[n];"number"==typeof r[0]&&a[r[0]]||(i&&!r[2]?r[2]=i:i&&(r[2]="("+r[2]+") and ("+i+")"),t.push(r))}},t}},function(t,e,i){function a(t,e){for(var i=0;i<t.length;i++){var a=t[i],n=u[a.id];if(n){n.refs++;for(var o=0;o<n.parts.length;o++)n.parts[o](a.parts[o]);for(;o<a.parts.length;o++)n.parts.push(d(a.parts[o],e))}else{for(var r=[],o=0;o<a.parts.length;o++)r.push(d(a.parts[o],e));u[a.id]={id:a.id,refs:1,parts:r}}}}function n(t){for(var e=[],i={},a=0;a<t.length;a++){var n=t[a],o=n[0],r=n[1],s=n[2],l=n[3],d={css:r,media:s,sourceMap:l};i[o]?i[o].parts.push(d):e.push(i[o]={id:o,parts:[d]})}return e}function o(t,e){var i=m(),a=w[w.length-1];if("top"===t.insertAt)a?a.nextSibling?i.insertBefore(e,a.nextSibling):i.appendChild(e):i.insertBefore(e,i.firstChild),w.push(e);else{if("bottom"!==t.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");i.appendChild(e)}}function r(t){t.parentNode.removeChild(t);var e=w.indexOf(t);e>=0&&w.splice(e,1)}function s(t){var e=document.createElement("style");return e.type="text/css",o(t,e),e}function l(t){var e=document.createElement("link");return e.rel="stylesheet",o(t,e),e}function d(t,e){var i,a,n;if(e.singleton){var o=g++;i=v||(v=s(e)),a=c.bind(null,i,o,!1),n=c.bind(null,i,o,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(i=l(e),a=f.bind(null,i),n=function(){r(i),i.href&&URL.revokeObjectURL(i.href)}):(i=s(e),a=h.bind(null,i),n=function(){r(i)});return a(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;a(t=e)}else n()}}function c(t,e,i,a){var n=i?"":a.css;if(t.styleSheet)t.styleSheet.cssText=y(e,n);else{var o=document.createTextNode(n),r=t.childNodes;r[e]&&t.removeChild(r[e]),r.length?t.insertBefore(o,r[e]):t.appendChild(o)}}function h(t,e){var i=e.css,a=e.media;if(a&&t.setAttribute("media",a),t.styleSheet)t.styleSheet.cssText=i;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(i))}}function f(t,e){var i=e.css,a=e.sourceMap;a&&(i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */");var n=new Blob([i],{type:"text/css"}),o=t.href;t.href=URL.createObjectURL(n),o&&URL.revokeObjectURL(o)}var u={},p=function(t){var e;return function(){return"undefined"==typeof e&&(e=t.apply(this,arguments)),e}},_=p(function(){return/msie [6-9]\b/.test(self.navigator.userAgent.toLowerCase())}),m=p(function(){return document.head||document.getElementsByTagName("head")[0]}),v=null,g=0,w=[];t.exports=function(t,e){e=e||{},"undefined"==typeof e.singleton&&(e.singleton=_()),"undefined"==typeof e.insertAt&&(e.insertAt="bottom");var i=n(t);return a(i,e),function(t){for(var o=[],r=0;r<i.length;r++){var s=i[r],l=u[s.id];l.refs--,o.push(l)}if(t){var d=n(t);a(d,e)}for(var r=0;r<o.length;r++){var l=o[r];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete u[l.id]}}}};var y=function(){var t=[];return function(e,i){return t[e]=i,t.filter(Boolean).join("\n")}}()},function(t,e){"use strict";function i(t){return o[Object.prototype.toString.call(t)]||"Object"}function a(t,e){return i(t)===e}Object.defineProperty(e,"__esModule",{value:!0});for(var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o={},r=["Null","Undefined","Number","Boolean","String","Object","Function","Array","RegExp","Date"],s=0;s<r.length;s++)o["[object "+r[s]+"]"]=r[s].toLowerCase();e.default={copy:function(t,e){if(null===t||"object"!=("undefined"==typeof t?"undefined":n(t)))return t;var o,r,s,l=a(t,"array")?[]:{};for(o in t)r=t[o],s=i(r),!e||"array"!==s&&"object"!==s?l[o]=r:l[o]=this.copy(r);return l}}}])});