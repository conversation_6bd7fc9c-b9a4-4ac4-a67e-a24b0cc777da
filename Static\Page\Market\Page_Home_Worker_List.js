// 获取用户信息
function Get_User_CreditPoints() {
    loaderShow()
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'User_Info',
    //     'User_Token': User_Token,
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        "data_class": "Sentinel", 
        "data_type": 'Service_User',
        "data_methods": "return_user_credit_points",
        "data_argument": `{}`,
        "data_kwargs":`{}`,
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ('Status' in res) {
                return notify({
                    message: '获取用户数据失败！',
                    type: 'danger',
                })
            }
            user_info = res
            Set_User_Info(res)
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        }).finally(() => loaderHide());
}
$('#reflush_points').on('click', function () {
    Get_User_CreditPoints()
})
// 设置用户信息
function Set_User_Info(info) {
    $('#user_charger').text(info.user_charger)
    $('#username').val(info.user_name)
    $('#usertype').val(info.user_type)
    $('#phone').val(info.user_phone)
    $('#credit_points').val(info.user_charger)
    $('#email').val(info.user_email || "未设置")
}
// function market_typeFormatter(row) {
//   if (row['MARKET_ADDR'] === 'account') {
//     return '账号采购'
//   } else if (row['MARKET_ADDR'] === 'counter') {
//     return '网络反制'
//   } else if (row['MARKET_ADDR'] === 'analysis') {
//     return '网络分析引擎'
//   } else {
//     return '其他'
//   }
// }
// var status_Emus = {
//     'Finish': '已完成',
//     'Active': '执行中',
//     'Recharge': '已退款',
//     'Failed': '异常中'
// }
// 获取用户订单
function Query_Work_Orders() {
    loaderShow()
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'User_Query_Work_Order',
    //     'User_Token': User_Token,
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'Network', 
        'data_type': 'Service',
        'data_methods': 'return_work_list',
        "data_argument": `{}`,
        "data_kwargs":`{}`,
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
    .then(res => {
        $table.clear().draw();
        if (res.length > 0) {

            $table.rows.add(res).draw();

        }
    }).finally(() => {
        loaderHide()
    })
}


// 日期选择
// $('#selectedOptions_time').on('change', function () {
//     console.log($(this).val())
//     if ($(this).val() == 'other') {
//         $('#diy_time_list').parent().show()
//         $('#clear_date').show()
//     } else {
//         $('#diy_time_list').parent().hide()
//         $('#clear_date').hide()
//         clear_date()
//     }
// })
// $('#diy_time_list').dateRangePicker({
//     language: 'cn',
//     separator: ' 至 ',
//
// }).bind('datepicker-apply', function (event, obj) {
//
//     console.log(obj);
// })

// 订单table
var $table = $('#table').DataTable({
    autoWidth: false,
    "columns": [
        { "title": "序号", width: '80px', "data": null, render: function (data, type, row, meta) { return meta.row + 1; } },
        { "title": "工单时间", "data": "WORK_TIME" },
        { "title": "工单类型", "data": "WORK_NAME" },
        { "title": "工单内容", "data": "WORK_CONTENT" },
        { "title": "信用点", "data": "WORK_PRICE", render: function (data, type, row, meta) { return data || '等待人工审核' } },
        {
            "title": "状态",
            "data": 'WORK_STATUS',
            render: status_format
        },
        {
            "title": "操作",
            "data": null,
            render: table_Action
        }
    ],
    "columnDefs": [
        { className: 'text-center', targets: '_all' },
    ],
    "oLanguage": { //国际化配置
        "sProcessing": "正在获取数据，请稍后...",
        "sLengthMenu": "显示 _MENU_ 条",
        "sZeroRecords": "没有您要搜索的内容",
        "sInfo": "从 _START_ 到  _END_ 条记录 总记录数为 _TOTAL_ 条",
        "sInfoEmpty": "记录数为0",
        "sInfoFiltered": "(全部记录数 _MAX_ 条)",
        "sInfoPostFix": "",
        "sSearch": "搜索",
        "sUrl": "",
        "oPaginate": {
            "sFirst": "第一页",
            "sPrevious": "上一页",
            "sNext": "下一页",
            "sLast": "最后一页"
        }
    },
});
var status_Emus = {
    'Waitting': '已创建',
    'Inline': '待支付',
    'Active': '执行中',
    'Finish': '已完成',
    'Recharge': '已关闭',
    'Failed': '异常中'
}
function status_format(data, type, row, meta) {
    let btn_html = ''
    switch (row.WORK_STATUS) {
        case 'Waitting':
            btn_html += `
            <button class='btn btn-light btn-sm btn-check'>已创建</button>
            `
            break;
        case 'Finish':
            btn_html += `
            <button class='btn btn-success btn-sm btn-check'>已完成</button>
            `
            break;
        case 'Active':
            btn_html += `
            <button class='btn btn-secondary btn-sm btn-check'>执行中</button>
            `
            break;
        case 'Recharge':
            btn_html += `
            <button class='btn btn-dark btn-sm btn-check'>已关闭</button>
            `
            break;
        case 'Failed':
            btn_html += `
            <button class='btn btn-danger btn-sm btn-check'>异常中</button>
            `
            break;
        case 'Inline':
            btn_html += `
            <button class='btn btn-warning btn-sm btn-check'>待支付</button>
            `
            break;
        default:
            return ''
    }
    return btn_html
}
// 操作栏
function table_Action(data, type, row, meta) {
    /*
    *   'Waitting' 已创建
        'Inline'   待支付
        'Active'   执行中
        'Finish'   已完成
        'Recharge'  已关闭
        'Failed'    异常中
    * */
    let btn_html = `
            <button class='btn btn-primary btn-check mr-3' data-toggle="modal" data-target="#modal-detail">详情</button>
        `
    // switch (row.WORK_STATUS) {
    //     case 'Finish':
    //         btn_html += `<a href="http://home.csc-tech.cn:9001/FileShare?ids=${row.WORK_ID}" class="btn btn-light">下载结果</a>`
    //         break;

    //     default:
    //         break;
    // }
    return btn_html
}


// function clear_date() {
//     $('#diy_time_list').val('')
// }
// 查询
// $('#query_table').on('click', function () {
//     let diy_time_list = $('#diy_time_list').val().split(' 至 ')
//     let selectedOptions_time = $('#selectedOptions_time').val()
//     Query_Orders(selectedOptions_time, diy_time_list)
// })

//智能转义
function zy_tmpl(row) {
    return `
            <p id="WORK_ID">工单号：${row.WORK_ID}</p>
            <p id="WORK_NAME">工单类型：${row.WORK_NAME}</p>
            <p id="WORK_FORM_TO">语种转换：${row.WORK_FORM_TO}</p>
            <p id="WORK_STATUS">工单状态：${status_Emus[row.WORK_STATUS]}</p>
            <p id="WORK_CONTENT">工单内容：${row.WORK_CONTENT}</p>
            <p id="WORK_TIME">创建时间：${row.WORK_TIME}</p>
            <p id="WORK_DESCRIBE">备注：${row.WORK_DESCRIBE || "——"}</p>
    `
}
function sg_tmpl(row) {
    return `
            <p id="WORK_ID">工单号：${row.WORK_ID}</p>
            <p id="WORK_NAME">工单类型：${row.WORK_NAME}</p>
            <p id="WORK_STATUS">工单状态：${status_Emus[row.WORK_STATUS]}</p>
            <p id="WORK_CONTENT">工单内容：${row.WORK_CONTENT}</p>
            <p id="WORK_TIME">创建时间：${row.WORK_TIME}</p>
            <p id="WORK_DESCRIBE">备注：${row.WORK_DESCRIBE || "——"}</p>
    `
}
function aw_tmpl(row) {
    return `
            <p id="WORK_ID">工单号：${row.WORK_ID}</p>
            <p id="WORK_NAME">工单类型：${row.WORK_NAME}</p>
            <p id="WORK_STATUS">工单状态：${status_Emus[row.WORK_STATUS]}</p>
            <p id="WORK_CONTENT">工单内容：${row.WORK_CONTENT}</p>
            <p id="WORK_COUNT">工单数量：${row.WORK_COUNT}</p>
            <p id="WORK_DATE">工单时长：${row.WORK_DATE}个月</p>
            <p id="WORK_TIME">创建时间：${row.WORK_TIME}</p>
            <p id="WORK_DESCRIBE">备注：${row.WORK_DESCRIBE || "——"}</p>
    `
}
// 查看详情
$('#table tbody').on('click', '.btn-check', function () {
    let data = $table.row($(this).parents('tr')).data();
    // console.log(data); // 假设第一列包含了需要的数据
    if (data.WORK_STATUS == 'Inline') {
        $('#modal-detail .modal-footer').show()
    } else {
        $('#modal-detail .modal-footer').hide()
    }


    $('#modal-detail').modal('show')
    let tmpl = ''
    /*
    *   'Waitting' 已创建
        'Inline'   待支付
        'Active'   执行中
        'Finish'   已完成
        'Recharge'  已关闭
        'Failed'    异常中
    * */
    switch (data.WORK_NAME) {
        case '社工库查询':
            tmpl = sg_tmpl(data)
            break;
        case '智能音视频转译':
            tmpl = zy_tmpl(data)
        case "暗网查控通道":
            tmpl = aw_tmpl(data)
        case "反制工作云主机":
            tmpl = aw_tmpl(data)
        default:
            break;
    }
    let load_html = `
        <div class="text-center">
            <a href="http://home.csc-tech.cn:9001/FileShare?ids=${data.WORK_ID}" class="btn btn-light">下载结果</a>
        </div>
    `
    tmpl += data.WORK_STATUS == 'Finish' ? load_html : ''
    let body = $('#modal-detail .modal-body')
    body.empty()
    body.append(tmpl)
    $('#submit_editor').off('click').on('click', function () {
        submit_editor(data)
    })
});
//支付
function submit_editor(data) {
    // console.log(data);
    if (data.WORK_PRICE > user_info.user_charger) {
        $('#modal-point').modal('show');
        return
    }
    let order_dict = [{ ...data['ORDER_LIST_INFO'] }]
    order_dict[0]['ANALYSIS_LIST'] = {}
    for (let key in data) {
        if (key !== 'ORDER_LIST_INFO') {
            order_dict[0]['ANALYSIS_LIST'][key] = data[key]
        }
    }
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Order_Submit',
    //     'User_Token': User_Token,
    //     order_dict
    // });
    loaderShow()
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'Market', 
        'data_type': 'Network', 
        'data_methods': 'submit_order',
        "data_argument": `{}`,
        "data_kwargs":{'order_dict':order_dict},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ('Status' in res && res.Status === 'success') {
                $('#modal-detail').modal('hide');
                Query_Work_Orders()
                return notify({
                    message: '提交订单成功！',
                    type: 'success',
                })
            }
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        }).finally(() => loaderHide());
}
function __debounce(func, wait, immediate) {
    let timeout;

    return function () {
        const context = this;
        const args = arguments;

        const later = function () {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };

        const callNow = immediate && !timeout;

        clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
    };
}
/************************Recharge_Points*********************************/
const debouncedRechargePoints = __debounce(recharge_points, 1000);
$('#Recharge_Points').on('submit', function (event) {
    event.preventDefault()
    debouncedRechargePoints()
})
function recharge_points() {
    let price = $('#rechange_price').val()
    if (price && price <= 0) {
        return notify({
            message: '充值金额不能为空',
            type: 'warning'
        })
    }
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Create_Pay',
    //     'User_Token': User_Token,
    //     "Points": price
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        "data_class": "CreditPoints", 
        "data_type": 'Check', 
        "data_methods": "recharge",
        "data_argument": `{}`,
        "data_kwargs":{"Points": price},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ("status" in res && res.status === 'success') {
                let pay_url = res.pay_url
                $('#qrcode').empty()
                let qrcode = new QRCode('qrcode', {
                    width: 220,
                    height: 220,
                    text: pay_url,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                })
                $('#qrcode').show()
                $('#qr_submit').hide()
                $('#cancel_pay').hide()
                $('#reflush_points').show()
                // 开启轮询支付状态
                query_pay(res['pay_order_id'])

            }
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        });
}
var query_pay_timer = null
// 定时查询支付状态
function query_pay(pay_order_id, delay = 5000) {
    if (!pay_order_id) return
    // const __Query_Pay_Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Order_Handle',
    //     'User_Token': User_Token,
    //     pay_order_id: pay_order_id
    // });
    query_pay_timer = setInterval(() => {
        // __Query_Pay_Server_Data.async_run()
        let Requests_Data = {
            "user_id": 'Market',
            "user_token":User_Token,
            "data_class": "CreditPoints", 
            "data_type": 'Check', 
            "data_methods": "order_handle",
            "data_argument": `{}`,
            "data_kwargs":{'pay_order_id': pay_order_id},
        };
        __Service_Requests = new Service_Requests("Async",Requests_Data);
        __Service_Requests.callMethod()
        .then(res => {
            if ("Status" in res && res["Status"] === 'Success') {
                let Trade_Status = res['Trade_Status']
                // console.log(Trade_Status)
                switch (Trade_Status) {
                    case 'TRADE_CLOSED': //交易关闭
                        clearInterval(query_pay_timer)
                    case "TRADE_SUCCESS": //交易成功
                        clearInterval(query_pay_timer)
                        Get_User_CreditPoints()
                        $('#qr_submit').show()
                        $('#cancel_pay').show()
                        $('#reflush_points').hide()
                        $('#qrcode').hide()
                        $('#pay_order_status').text('')
                        break;
                    case "WAIT_BUYER_PAY": //等待支付
                        break;
                    default:
                        clearInterval(query_pay_timer)
                }
                $('#pay_order_status').text(res["Messag"])
            } else {
                $('#pay_order_status').text(res["Messag"])
            }
        })
    }, delay)

}
