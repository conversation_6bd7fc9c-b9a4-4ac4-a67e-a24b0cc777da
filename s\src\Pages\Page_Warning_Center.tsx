import React, { useState, useEffect } from 'react';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Warning_Center.css';

interface WarningItem {
  id: string;
  title: string;
  content: string;
  warningType: string;
  warningLevel: 'high' | 'medium' | 'low';
  source: string;
  platform: string;
  publishTime: string;
  warningTime: string;
  status: 'pending' | 'processing' | 'completed' | 'ignored';
  handler: string;
  handleTime: string;
  handleNote: string;
}

interface FilterOptions {
  dateRange: string;
  warningType: string;
  status: string;
  keyword: string;
}

const Page_Warning_Center: React.FC = () => {
  const [warnings, setWarnings] = useState<WarningItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedWarning, setSelectedWarning] = useState<WarningItem | null>(null);
  const [showHandleModal, setShowHandleModal] = useState(false);
  const [handleNote, setHandleNote] = useState('');
  const [handleAction, setHandleAction] = useState<'process' | 'complete' | 'ignore'>('process');
  
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: 'today',
    warningType: 'all',
    status: 'all',
    keyword: ''
  });

  const serviceRequests = new Service_Requests();

  // 日期范围选项
  const dateRangeOptions = [
    { value: 'today', label: '今天' },
    { value: 'yesterday', label: '昨天' },
    { value: 'week', label: '近7天' },
    { value: 'month', label: '近30天' },
    { value: 'custom', label: '自定义' }
  ];

  // 预警方式选项
  const warningTypeOptions = [
    { value: 'all', label: '全部' },
    { value: 'keyword', label: '关键词预警' },
    { value: 'sentiment', label: '情感预警' },
    { value: 'volume', label: '舆情量预警' },
    { value: 'hotspot', label: '热点预警' }
  ];

  // 处置状态选项
  const statusOptions = [
    { value: 'all', label: '全部' },
    { value: 'pending', label: '待处理' },
    { value: 'processing', label: '处理中' },
    { value: 'completed', label: '已完成' },
    { value: 'ignored', label: '已忽略' }
  ];

  // 获取预警数据
  const fetchWarnings = async () => {
    setLoading(true);
    try {
      const response = await serviceRequests.Async({
        user_id: localStorage.getItem('user_id') || '',
        user_token: localStorage.getItem('User_Token') || '',
        data_class: 'Warning',
        data_type: 'Center',
        data_methods: 'GetList',
        data_argument: JSON.stringify({
          page: currentPage,
          filters: filters
        }),
        data_kwargs: ''
      });

      if (response && response.Status === 'Success') {
        setWarnings(response.Data.list || []);
        setTotalCount(response.Data.total || 0);
      }
    } catch (error) {
      console.error('获取预警数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1);
  };

  // 处理预警
  const handleWarning = async () => {
    if (!selectedWarning) return;

    try {
      const response = await serviceRequests.Async({
        user_id: localStorage.getItem('user_id') || '',
        user_token: localStorage.getItem('User_Token') || '',
        data_class: 'Warning',
        data_type: 'Center',
        data_methods: 'Handle',
        data_argument: JSON.stringify({
          warningId: selectedWarning.id,
          action: handleAction,
          note: handleNote
        }),
        data_kwargs: ''
      });

      if (response && response.Status === 'Success') {
        setShowHandleModal(false);
        setSelectedWarning(null);
        setHandleNote('');
        fetchWarnings(); // 刷新列表
      }
    } catch (error) {
      console.error('处理预警失败:', error);
    }
  };

  // 复核预警
  const reviewWarning = async (warningId: string) => {
    try {
      const response = await serviceRequests.Async({
        user_id: localStorage.getItem('user_id') || '',
        user_token: localStorage.getItem('User_Token') || '',
        data_class: 'Warning',
        data_type: 'Center',
        data_methods: 'Review',
        data_argument: JSON.stringify({
          warningId: warningId
        }),
        data_kwargs: ''
      });

      if (response && response.Status === 'Success') {
        fetchWarnings(); // 刷新列表
      }
    } catch (error) {
      console.error('复核预警失败:', error);
    }
  };

  // 获取预警级别颜色
  const getWarningLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'secondary';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'danger';
      case 'processing': return 'warning';
      case 'completed': return 'success';
      case 'ignored': return 'secondary';
      default: return 'secondary';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待处理';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'ignored': return '已忽略';
      default: return '未知';
    }
  };

  useEffect(() => {
    fetchWarnings();
  }, [currentPage, filters]);

  return (
    <div className="warning-center">
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="mb-0">预警中心</h5>
            <div className="d-flex align-items-center">
              <span className="text-muted mr-3">共 {totalCount} 条预警</span>
            </div>
          </div>
        </div>
        
        <div className="card-body">
          {/* 筛选条件 */}
          <div className="filters-section mb-4">
            <div className="row">
              <div className="col-md-3">
                <label className="form-label">日期范围:</label>
                <select 
                  className="form-control"
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                >
                  {dateRangeOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div className="col-md-3">
                <label className="form-label">预警方式:</label>
                <select 
                  className="form-control"
                  value={filters.warningType}
                  onChange={(e) => handleFilterChange('warningType', e.target.value)}
                >
                  {warningTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div className="col-md-3">
                <label className="form-label">处置状态:</label>
                <select 
                  className="form-control"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div className="col-md-3">
                <label className="form-label">预警内容:</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="搜索预警内容..."
                  value={filters.keyword}
                  onChange={(e) => handleFilterChange('keyword', e.target.value)}
                />
              </div>
            </div>
          </div>
          
          {/* 预警列表 */}
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border text-info" role="status">
                <span className="sr-only">Loading...</span>
              </div>
            </div>
          ) : (
            <div className="warnings-list">
              {warnings.map((warning) => (
                <div key={warning.id} className="card mb-3 warning-item">
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-8">
                        <div className="d-flex align-items-center mb-2">
                          <h6 className="card-title mb-0 mr-2">{warning.title}</h6>
                          <span className={`badge badge-${getWarningLevelColor(warning.warningLevel)} mr-2`}>
                            {warning.warningLevel === 'high' ? '高' : warning.warningLevel === 'medium' ? '中' : '低'}
                          </span>
                          <span className={`badge badge-${getStatusColor(warning.status)}`}>
                            {getStatusText(warning.status)}
                          </span>
                        </div>
                        <p className="card-text text-truncate">{warning.content}</p>
                        <div className="text-muted small">
                          <span className="mr-3">
                            <i className="bx bx-globe mr-1"></i>{warning.platform}
                          </span>
                          <span className="mr-3">
                            <i className="bx bx-link mr-1"></i>{warning.source}
                          </span>
                          <span className="mr-3">
                            <i className="bx bx-time mr-1"></i>发布: {warning.publishTime}
                          </span>
                          <span>
                            <i className="bx bx-alarm mr-1"></i>预警: {warning.warningTime}
                          </span>
                        </div>
                        {warning.handler && (
                          <div className="text-muted small mt-1">
                            <i className="bx bx-user mr-1"></i>处理人: {warning.handler} | 
                            <i className="bx bx-time ml-2 mr-1"></i>处理时间: {warning.handleTime}
                            {warning.handleNote && (
                              <span className="ml-2">| 备注: {warning.handleNote}</span>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="col-md-4 text-right">
                        <div className="btn-group-vertical">
                          {warning.status === 'pending' && (
                            <button
                              className="btn btn-primary btn-sm mb-1"
                              onClick={() => {
                                setSelectedWarning(warning);
                                setShowHandleModal(true);
                              }}
                            >
                              <i className="bx bx-edit mr-1"></i>处置
                            </button>
                          )}
                          <button
                            className="btn btn-outline-secondary btn-sm"
                            onClick={() => reviewWarning(warning.id)}
                          >
                            <i className="bx bx-check mr-1"></i>复核
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {warnings.length === 0 && (
                <div className="text-center py-4">
                  <p className="text-muted">暂无预警数据</p>
                </div>
              )}
            </div>
          )}
          
          {/* 分页 */}
          {totalCount > 20 && (
            <nav aria-label="Page navigation" className="mt-4">
              <ul className="pagination justify-content-center">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button 
                    className="page-link" 
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    上一页
                  </button>
                </li>
                <li className="page-item active">
                  <span className="page-link">{currentPage}</span>
                </li>
                <li className={`page-item ${currentPage * 20 >= totalCount ? 'disabled' : ''}`}>
                  <button 
                    className="page-link" 
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage * 20 >= totalCount}
                  >
                    下一页
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </div>
      </div>
      
      {/* 处置模态框 */}
      {showHandleModal && selectedWarning && (
        <div className="modal fade show" style={{ display: 'block' }} tabIndex={-1}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">处置预警</h5>
                <button 
                  type="button" 
                  className="close" 
                  onClick={() => setShowHandleModal(false)}
                >
                  <span>&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <strong>预警标题:</strong> {selectedWarning.title}
                </div>
                <div className="mb-3">
                  <strong>预警内容:</strong> {selectedWarning.content}
                </div>
                <div className="form-group">
                  <label>处置方式:</label>
                  <select 
                    className="form-control"
                    value={handleAction}
                    onChange={(e) => setHandleAction(e.target.value as 'process' | 'complete' | 'ignore')}
                  >
                    <option value="process">标记为处理中</option>
                    <option value="complete">标记为已完成</option>
                    <option value="ignore">标记为已忽略</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>处置备注:</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="请输入处置备注..."
                    value={handleNote}
                    onChange={(e) => setHandleNote(e.target.value)}
                  />
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowHandleModal(false)}
                >
                  取消
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={handleWarning}
                >
                  确认处置
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {showHandleModal && <div className="modal-backdrop fade show"></div>}
    </div>
  );
};

export default Page_Warning_Center;
