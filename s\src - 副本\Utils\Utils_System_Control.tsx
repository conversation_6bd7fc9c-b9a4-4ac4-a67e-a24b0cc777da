import React, { useState } from 'react';
import { Button, Select, Dropdown, Menu } from 'antd';
import { DownOutlined,CaretRightOutlined  } from '@ant-design/icons';




interface Props_Status {
  isOpen: boolean; // 接收 isOpen 状态
  toggleControl: () => void; // 接收 toggleDialog 函数
}





const { Option } = Select;

interface OptionType {
  label: string;
  value: string;
}

const options: OptionType[] = [
  { label: '执行(一次)', value: '1' },
  { label: '执行(实时)', value: '2' },
  { label: '执行(循环)', value: '3' },
];

const Utils_System_Control: React.FC<Props_Status> = ({ isOpen, toggleControl }) => {
  const [selectedValue, setSelectedValue] = useState<OptionType | null>(null);

  // 处理选择框的值变化
  const handleSelectChange = (value: string) => {
    const selectedOption = options.find((opt) => opt.value === value);
    setSelectedValue(selectedOption || null);
  };

  // 按钮的显示文字
  const buttonText = selectedValue ? selectedValue.label : '执行';

  // 下拉菜单内容
  const menu = (
    <Menu onClick={({ key }) => handleSelectChange(key)}>
      {options.map((opt) => (
        <Menu.Item key={opt.value}>{opt.label}</Menu.Item>
      ))}
    </Menu>
  );

  return (

   
      <Dropdown overlay={menu} trigger={['click']} placement="topCenter">
        
        <Button type="primary" style={{ width: 150 }}>
        <CaretRightOutlined   style={{ color: 'white' }} />{buttonText} <DownOutlined />  
        </Button>
      </Dropdown>
  
    
  );
};

export default Utils_System_Control;