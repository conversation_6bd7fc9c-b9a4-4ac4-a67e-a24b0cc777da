import React, { useState, useEffect, useRef } from 'react';
import { Modal, Table, Button, Space } from 'antd';
import { useAuth } from '@/Core/Core_AuthContent';
import MediaPlayerModal from '@/Component/MediaPlayer/MediaPlayerModal';
import styles from './DocumentDetailModal.module.css'

interface Mission {
    XID: string;
    MISSION_TYPE: '降噪' | '萃取' | '文本转译' | '音视频转译' | '文档转译';
    MISSION_CONTENT: string;
    MISSION_RESULT: string;
    MISSION_STATUS: 'Active' | 'Execute' | 'Finish';
    Index: string;
    key?: string;
    MISSION_RESULT_FULL?: TableDataItem[];
}

type TextDetailModalProps = {
    visible: boolean;
    onCancel: () => void;
    detailData: Mission | null;
};

interface TableDataItem {
    hash_info: string;
    file_name: string;
    file_size: string;
    file_status: string;
    file_time: string;
    file_type: string;
    save_name: string;
}

interface MediaPlayerState {
    isVisible: boolean;
    currentFile: TableDataItem | null;
    blobUrl: string | null;
    isLoading: boolean;
    error: string | null;
    isVideo: boolean;
}

const DocumentDetailModal: React.FC<TextDetailModalProps> = ({ visible, onCancel, detailData }) => {
    const [tableData, setTableData] = useState<TableDataItem[]>([]);
    const [mediaPlayer, setMediaPlayer] = useState<MediaPlayerState>({
        isVisible: false,
        currentFile: null,
        blobUrl: null,
        isLoading: false,
        error: null,
        isVideo: false
    });
    const [ModalTitle, setModalTitle] = useState<string>('文档转译详情');
    const { user } = useAuth();

    const [showMediaPlayer, setShowMediaPlayer] = useState(false);
    const [currentMediaUrl, setCurrentMediaUrl] = useState('');
    const [currentMediaType, setCurrentMediaType] = useState<'video' | 'audio'>('video');

    const [isLoading, setIsLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState<string | null>(null);

    // 服务器在线播放功能
    const handleServerPlay = async (fileItem: TableDataItem) => {
        try {
            setIsLoading(true); // 开始加载
            setErrorMsg(null);

            const playUrl = process.env.REACT_APP_VIDEO_PLAY_URL;
            if (!playUrl) throw new Error('PLAY URL is not defined');
        
            const formData = new FormData();
            formData.append('save_name', 'Result_' + fileItem.save_name.replace('Result_', ''));
            formData.append('file_time', fileItem.file_time);
            formData.append('user_token', user?.usertoken || '');
        
            const response = await fetch(playUrl, { method: 'POST', body: formData });
            if (!response.ok) throw new Error('播放请求失败');
        
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
        
            setCurrentMediaUrl(blobUrl);
            setCurrentMediaType(fileItem.file_type.startsWith('video/') || fileItem.save_name?.endsWith('.mp4') ? 'video' : 'audio');
            setShowMediaPlayer(true);
        } catch (error) {
            console.error('在线播放出错:', error);
            setErrorMsg(error instanceof Error ? error.message : '未知错误');
        } finally {
            setIsLoading(false); // 结束加载
        }
    };

    // 萃取和降噪任务和音视频转译任务结果的下载功能
    const handleExtractDenoiseDownload = async (fileItem: TableDataItem) => {
        if (!fileItem.hash_info || !fileItem.file_time) {
            console.error('文件信息不完整，无法下载');
            return;
        }

        // 检查文件处理状态
        if (fileItem.file_status !== 'Success') {
            console.error('文件尚未处理完成，无法下载');
            return;
        }

        try {
            console.log('开始下载文件:', fileItem.file_name);
            console.log('任务类型:', detailData?.MISSION_TYPE);
            console.log('Hash Info:', fileItem.hash_info);
            console.log('File Time:', fileItem.file_time);

            const downloadUrl = process.env.REACT_APP_DOWNLOAD_URL;
            if (!downloadUrl) {
                throw new Error('Download URL is not defined in environment variables');
            }

            let saveName;
            if (detailData?.MISSION_TYPE === '降噪' || detailData?.MISSION_TYPE === '萃取') {
                // 降噪/萃取处理的文件名
                saveName = 'Result_' + fileItem.save_name.replace('Result_', '');
            } else if ( detailData?.MISSION_TYPE === '音视频转译') {
                saveName = 'Result_' + fileItem.hash_info + '.txt';
            } else {
                console.error('不支持的处理类型');
                return;
            }

            const formData = new FormData();
            formData.append('save_name', saveName);
            formData.append('file_time', fileItem.file_time);
            formData.append('user_token', 'c4135ce7186f6890a');

            console.log('下载参数:', {
                save_name: saveName,
                file_time: fileItem.file_time,
                user_token: 'c4135ce7186f6890a'
            });

            const response = await fetch(downloadUrl, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                // 获取文件blob
                const blob = await response.blob();

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                // 设置下载文件名
                let downloadFileName;
                if (detailData?.MISSION_TYPE === '降噪') {
                    downloadFileName = `${saveName}`;
                } else if (detailData?.MISSION_TYPE === '萃取') {
                    downloadFileName = `${saveName}`;
                }

                link.download = downloadFileName || saveName;

                // 触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                console.log('文件下载成功:', downloadFileName);
            } else {
                const errorText = await response.text();
                console.error('下载失败:', response.status, errorText);
            }
        } catch (error) {
            console.error('下载过程中出现错误:', error);
        }
    };

    // 原有的文本转译、音视频转译、文档转译下载功能
    const Download_Upload_Info = async (file: { file_status?: string; file_type?: any; file_time?: any; save_name?: any; }) => {
        console.log('下载译文:', file);

        const { file_type, file_time, save_name } = file;

        const UPLOAD_CONFIG = {
            user_id: user?.usertoken  || '',
            user_token: user?.usertoken  || '',
            forward: 'False'
        };

        const formData = new URLSearchParams();
        formData.append('file_type', file_type);
        formData.append('file_time', file_time);
        formData.append('save_name', 'Result_' + save_name);
        formData.append('forward', UPLOAD_CONFIG.forward);
        formData.append('user_id', UPLOAD_CONFIG.user_id);
        formData.append('user_token', UPLOAD_CONFIG.user_token);

        try {
            const DownloadURL = process.env.REACT_APP_DOWNLOAD_URL;
            if (!DownloadURL) {
                throw new Error('Download URL is not defined in environment variables');
            }
            
            const response = await fetch(DownloadURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData.toString(),
            });

            if (!response.ok) {
                throw new Error('网络响应失败');
            }

            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = 'Result_' + save_name;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error('下载失败:', error);
        }
    };


    // 定义列
    const columns = [
        {
            title: '文件标识',
            dataIndex: 'hash_info',
            key: 'hash_info',
        },
        {
            title: '文件名称',
            dataIndex: 'file_name',
            key: 'file_name',
        },
        {
            title: '文件大小',
            dataIndex: 'file_size',
            key: 'file_size',
        },
        {
            title: '文件状态',
            dataIndex: 'file_status',
            key: 'file_status',
            render: (status: string) =>
                status === 'Success' ? (
                    <Button type="primary" size="small">
                        已完成
                    </Button>
                ) : (
                    <Button type="default" size="small">
                        未完成
                    </Button>
                ),
        },
        {
            title: '操作',
            dataIndex: 'file_status',
            key: 'action',
            render: (_: any, record: TableDataItem) => {
                if (record.file_status === 'Success') {
                    // 判断是否为降噪任务或萃取任务
                    // const isMediaTask = detailData?.MISSION_TYPE === '降噪' || detailData?.MISSION_TYPE === '音视频转译';
                    const isMediaTask = detailData?.MISSION_TYPE === '降噪' || detailData?.MISSION_TYPE === '萃取';
                    // 判断是否为萃取或降噪任务
                    const isExtractDenoiseTask = detailData?.MISSION_TYPE === '萃取' || detailData?.MISSION_TYPE === '降噪' || detailData?.MISSION_TYPE === '音视频转译';
                    
                    return (
                        <Space>
                            {isExtractDenoiseTask ? (
                                <Button 
                                    type="link" 
                                    onClick={() => handleExtractDenoiseDownload(record)}
                                >
                                    结果下载
                                </Button>
                            ) : (
                                <Button 
                                    type="link" 
                                    onClick={() => Download_Upload_Info(record)}
                                >
                                    结果下载
                                </Button>
                            )}
                            {isMediaTask && (
                                <Button 
                                    type="link" 
                                    onClick={() => handleServerPlay(record)}
                                >
                                    在线播放
                                </Button>
                            )}
                        </Space>
                    );
                }
                return null;
            },
        },
    ];

    useEffect(() => {
        // 判断任务类型 修改modal的title
        setModalTitle(detailData?.MISSION_TYPE + '任务详情' || '任务详情')
    }, []);

    // 数据更新
    useEffect(() => {
        if (visible && detailData?.MISSION_RESULT_FULL) {
            const transformedData = detailData.MISSION_RESULT_FULL.map((item): TableDataItem => ({
                hash_info: item?.hash_info ?? '未知Hash',
                file_name: item?.file_name ?? '未知文件名',
                file_size: item?.file_size ?? '0KB',
                file_status: item?.file_status ?? 'Error',
                file_time: item?.file_time ?? '未知时间',
                file_type: item?.file_type ?? '未知类型',
                save_name: item?.save_name ?? '未命名文件'
            }));
            setTableData(transformedData);
        } else {
            setTableData([]);
        }
    }, [visible, detailData]);

    return (
        <>
            <Modal
                title={ModalTitle}
                visible={visible}
                onCancel={onCancel}
                footer={[
                    <Button key="close" onClick={onCancel}>
                        关闭
                    </Button>,
                ]}
                width={1000}
                centered
                destroyOnClose
            >
                <Table
                    dataSource={tableData}
                    columns={columns}
                    pagination={false}
                    rowKey="hash_info"
                />
            </Modal>

            {/* 媒体播放器模态框 */}
            {showMediaPlayer && (
                <MediaPlayerModal
                    visible={showMediaPlayer}
                    url={currentMediaUrl}
                    mediaType={currentMediaType}
                    // mediaType='video'
                    onClose={() => {
                        setShowMediaPlayer(false);
                        setCurrentMediaUrl('');
                    }}
                />
            )}

            {/* 播放器加载状态 */}
            {isLoading && (
                <div className={styles.loadingOverlay}>
                    <div className={styles.loadingContainer}>
                        <div className={styles.spinner}></div>
                        <p>正在加载媒体，请稍候...</p>
                    </div>
                </div>
            )}

            {/* 错误提示 */}
            {/* {errorMsg && (
                <div style={{
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: '#ff4d4f',
                    color: '#fff',
                    padding: '12px 24px',
                    borderRadius: '4px',
                    zIndex: 9999,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                }}>
                    {errorMsg}
                </div>
            )} */}
            {errorMsg && (
                <div style={{
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: '#ff4d4f',
                    color: '#fff',
                    padding: '12px 24px',
                    borderRadius: '4px',
                    zIndex: 9999,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    maxWidth: '400px'
                    }}
                >
                    <span>{errorMsg}</span>
                    <button
                        onClick={() => setErrorMsg(null)}
                        style={{
                        marginLeft: '16px',
                        background: 'none',
                        border: 'none',
                        color: 'white',
                        fontSize: '18px',
                        cursor: 'pointer'
                        }}
                        aria-label="Close error message"
                    >
                        &times;
                    </button>
                </div>
            )}
        </>
    );
};

export default DocumentDetailModal;