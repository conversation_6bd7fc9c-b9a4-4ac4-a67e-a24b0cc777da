/* 本地监控页面样式 */
.local-monitor-page {
  padding: 24px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 标签页样式 */
.monitor-tabs {
  margin-bottom: 16px;
}

.monitor-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.monitor-tabs .ant-tabs-tab-active {
  color: #1890ff;
}

/* 聊天界面布局 */
.chat-wrapper {
  display: flex;
  height: calc(100vh - 120px);
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

/* 左侧群组列表 */
.chat-sidebar {
  width: 300px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.chat-sidebar.collapsed {
  width: 0;
  overflow: hidden;
}

.chat-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.chat-sidebar-content {
  flex: 1;
  overflow: hidden;
}

.chat-list {
  height: 100%;
  overflow-y: auto;
}

.chat-list .ant-list {
  height: 100%;
}

.chat-list .ant-list-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f5f5f5;
}

.chat-list .ant-list-item:hover {
  background: #f5f5f5;
}

.chat-list .ant-list-item.active {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.group-item .ant-list-item-meta-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.group-info {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.last-message {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

/* 右侧消息内容 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.chat-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  min-height: 64px;
}

.chat-toggle-btn {
  margin-right: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.chat-toggle-btn:hover {
  background: #f0f0f0;
}

.chat-title {
  flex: 1;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

/* 消息表格 */
.chat-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.chat-content .ant-table {
  height: 100%;
}

.chat-content .ant-table-tbody > tr > td {
  padding: 8px 16px;
}

.chat-content .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 表格行悬停效果 */
.chat-content .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 消息内容列样式 */
.chat-content .ant-table-tbody > tr > td:last-child {
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 加载状态 */
.ant-spin-container {
  height: 100%;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 搜索框样式 */
.chat-sidebar-header .ant-input-search {
  border-radius: 6px;
}

.chat-sidebar-header .ant-input-search .ant-input {
  border-radius: 6px 0 0 6px;
}

.chat-sidebar-header .ant-input-search .ant-input-search-button {
  border-radius: 0 6px 6px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .local-monitor-page {
    padding: 16px;
  }
  
  .chat-wrapper {
    height: calc(100vh - 100px);
  }
  
  .chat-sidebar {
    width: 250px;
  }
  
  .chat-header {
    padding: 12px 16px;
  }
  
  .chat-content {
    padding: 12px;
  }
  
  .last-message {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .local-monitor-page {
    padding: 12px;
  }
  
  .chat-wrapper {
    height: calc(100vh - 80px);
  }
  
  .chat-sidebar {
    width: 200px;
  }
  
  .chat-sidebar.collapsed {
    width: 0;
  }
  
  .chat-header {
    padding: 8px 12px;
  }
  
  .chat-header .chat-title h4 {
    font-size: 16px;
  }
  
  .chat-content {
    padding: 8px;
  }
  
  .group-info,
  .last-message {
    font-size: 11px;
  }
  
  .last-message {
    max-width: 120px;
  }
}

/* 滚动条样式 */
.chat-list::-webkit-scrollbar {
  width: 6px;
}

.chat-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chat-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

/* 表格空状态 */
.chat-content .ant-table-placeholder {
  padding: 40px 0;
}

/* 按钮样式 */
.chat-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.chat-actions .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.chat-actions .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 标签页内容区域 */
.ant-tabs-content-holder {
  height: calc(100vh - 120px);
}

/* 列表项图标 */
.group-item .ant-list-item-meta-avatar {
  color: #1890ff;
  font-size: 18px;
}

/* 表格固定高度 */
.chat-content .ant-table-container {
  height: calc(100vh - 300px);
}

.chat-content .ant-table-body {
  height: calc(100vh - 380px);
  overflow-y: auto;
}
