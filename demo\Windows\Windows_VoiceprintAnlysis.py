import sys,random
import numpy as np
import librosa
from PySide6 import QtWidgets,QtCore,QtGui
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QToolButton, QLabel, QFrame, QSizePolicy,
                               QDockWidget, QStatusBar, QMenuBar, QMenu, )
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont, QPalette, QColor,QAction

from Bin.Utils.UtilsCenter import *
# sys.path.append(r"D:\Sentinel Foundation\Media\Server\OS\Page")
from Bin.System.OS.Page.Media import Page_Widget_Voiceprint_Analysis


Page_Info={}
class Windows_VoiceprintAnlysis(QtWidgets.QMainWindow):
    def __init__(self):
        global Page_Info
        super().__init__()
        self.setWindowTitle("哨兵音频分析工作站")
        self.resize(1920, 1080)


        self.setWindowIcon(QtGui.QIcon(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.ico"))

        # 设置主窗口背景
        Palette_Main = self.palette()
        Palette_Main.setColor(QtGui.QPalette.Window, QColor(15, 20, 25))
        self.setPalette(Palette_Main)

        self.initUI()
     # 音频分析器
        self.audio_analyzer = None
        self.audio_file = None
       # 模拟数据更新
       #  self.timer = QtCore.QTimer(self)
       #  self.timer.timeout.connect(self.update_visualizations)
       #  self.timer.start(30)

        self.audio_buffer = np.zeros(1024, dtype=np.float32)



    def initUI(self):


        # 创建菜单栏
        self.Set_Menubar()

        # 创建中央部件
        QWidget_Central = QtWidgets.QWidget()
        self.setCentralWidget(QWidget_Central)

        # 主布局
        QVBoxLayout_Central = QtWidgets.QVBoxLayout(QWidget_Central)
        QVBoxLayout_Central.setContentsMargins(0, 0, 0, 0)
        QVBoxLayout_Central.setSpacing(0)

        # 顶部工具栏
        self.Set_Toolbar()
        QVBoxLayout_Central.addWidget(self.QFrame_Toolbar)

        # 面包屑导航
        self.Breadcrumb = BreadcrumbBar()
        QVBoxLayout_Central.addWidget(self.Breadcrumb)

        # 中央工作区
        self.QWidget_Workspace = QtWidgets.QWidget()
        self.QWidget_Workspace.setStyleSheet("background-color: rgba(25, 35, 45, 200);")
        QVBoxLayout_Central.addWidget(self.QWidget_Workspace, 1)

        # 状态栏
        self.Set_Statusbar()

        # 创建可折叠面板
        self.Set_Dock_Panels()

        # 示例内容
        self.Set_Content_Workspace()

    def Set_Menubar(self):
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: rgba(30, 40, 50, 220);
                color: rgb(180, 200, 220);
                border-bottom: 1px solid rgba(0, 100, 150, 80);
                padding: 3px;
            }
            QMenuBar::item {
                padding: 5px 10px;
                background: transparent;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: rgba(60, 90, 120, 150);
            }
            QMenu {
                background-color: rgba(30, 40, 50, 220);
                border: 1px solid rgba(0, 100, 150, 80);
                padding: 5px;
            }
            QMenu::item {
                padding: 5px 25px 5px 20px;
                color: rgb(180, 200, 220);
            }
            QMenu::item:selected {
                background-color: rgba(60, 90, 120, 150);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(0, 100, 150, 80);
                margin: 5px 0;
            }
        """)

        # File 菜单
        file_menu = menubar.addMenu("文件(&F)")

        new_action = QAction(QIcon.fromTheme("document-new"), "新建项目(&N)", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)

        open_action = QAction(QIcon.fromTheme("document-open"), "打开项目(&O)", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        save_action = QAction(QIcon.fromTheme("document-save"), "保存(&S)", self)
        save_action.setShortcut("Ctrl+S")
        file_menu.addAction(save_action)

        save_as_action = QAction("另存为(&A)...", self)
        save_as_action.setShortcut("Ctrl+Shift+S")
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        exit_action = QAction(QIcon.fromTheme("application-exit"), "退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Edit 菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        undo_action = QAction(QIcon.fromTheme("edit-undo"), "撤销(&U)", self)
        undo_action.setShortcut("Ctrl+Z")
        edit_menu.addAction(undo_action)

        redo_action = QAction(QIcon.fromTheme("edit-redo"), "重做(&R)", self)
        redo_action.setShortcut("Ctrl+Y")
        edit_menu.addAction(redo_action)

        edit_menu.addSeparator()

        cut_action = QAction(QIcon.fromTheme("edit-cut"), "剪切(&T)", self)
        cut_action.setShortcut("Ctrl+X")
        edit_menu.addAction(cut_action)

        copy_action = QAction(QIcon.fromTheme("edit-copy"), "复制(&C)", self)
        copy_action.setShortcut("Ctrl+C")
        edit_menu.addAction(copy_action)

        paste_action = QAction(QIcon.fromTheme("edit-paste"), "粘贴(&P)", self)
        paste_action.setShortcut("Ctrl+V")
        edit_menu.addAction(paste_action)

        # View 菜单
        view_menu = menubar.addMenu("视图(&V)")

        toolbar_action = QAction("工具栏", self)
        toolbar_action.setCheckable(True)
        toolbar_action.setChecked(True)
        view_menu.addAction(toolbar_action)

        statusbar_action = QAction("状态栏", self)
        statusbar_action.setCheckable(True)
        statusbar_action.setChecked(True)
        view_menu.addAction(statusbar_action)

        view_menu.addSeparator()

        left_panel_action = QAction("左侧面板", self)
        left_panel_action.setCheckable(True)
        left_panel_action.setChecked(True)
        left_panel_action.toggled.connect(lambda checked: self.left_panel.setVisible(checked))
        view_menu.addAction(left_panel_action)

        right_panel_action = QAction("右侧面板", self)
        right_panel_action.setCheckable(True)
        right_panel_action.setChecked(True)
        right_panel_action.toggled.connect(lambda checked: self.right_panel.setVisible(checked))
        view_menu.addAction(right_panel_action)

        bottom_panel_action = QAction("底部面板", self)
        bottom_panel_action.setCheckable(True)
        bottom_panel_action.setChecked(True)
        bottom_panel_action.toggled.connect(lambda checked: self.bottom_panel.setVisible(checked))
        view_menu.addAction(bottom_panel_action)

        # Tools 菜单
        tools_menu = menubar.addMenu("工具(&T)")

        analyze_action = QAction("开始分析", self)
        analyze_action.setShortcut("F5")
        tools_menu.addAction(analyze_action)

        stop_action = QAction("停止分析", self)
        stop_action.setShortcut("F6")
        tools_menu.addAction(stop_action)

        tools_menu.addSeparator()

        preferences_action = QAction(QIcon.fromTheme("preferences-system"), "首选项...", self)
        tools_menu.addAction(preferences_action)

        # Help 菜单
        help_menu = menubar.addMenu("帮助(&H)")

        about_action = QAction(QIcon.fromTheme("help-about"), "关于", self)
        help_menu.addAction(about_action)

        docs_action = QAction(QIcon.fromTheme("help-contents"), "文档", self)
        docs_action.setShortcut("F1")
        help_menu.addAction(docs_action)

    def Set_Toolbar(self):
        self.QFrame_Toolbar = QtWidgets.QFrame()
        # self.QFrame_Toolbar.setFixedHeight(0)
        self.QFrame_Toolbar.setMinimumWidth(0)
        self.QFrame_Toolbar.setMaximumWidth(0)
        # self.QFrame_Toolbar.setFixedWidth(0)

        self.QFrame_Toolbar.setStyleSheet("""
            QFrame {
                background-color: rgba(30, 40, 50, 220);
                border-bottom: 1px solid rgba(0, 100, 150, 80);
            }
        """)

        QHBoxLayout_Toolbar = QtWidgets.QHBoxLayout(self.QFrame_Toolbar)
        QHBoxLayout_Toolbar.setContentsMargins(10, 0, 10, 0)


        # for btn_text in ["文件", "设备", "设置", "分析", "导出"]:
        #     label = QLabel(btn_text)
        #     label.setAlignment(Qt.AlignCenter)
        #     label.setStyleSheet("""
        #         QLabel {
        #             background-color: rgba(0, 50, 80, 120);
        #             border: 1px solid rgba(0, 180, 255, 60);
        #             border-radius: 4px;
        #             padding: 8px;
        #             min-width: 80px;
        #         }
        #         QLabel:hover {
        #             background-color: rgba(0, 100, 150, 150);
        #         }
        #     """)
        #     control_layout.addWidget(label)


        StyleSheet_QPushButton ="""
                QPushButton {
                    background-color: rgba(0, 50, 80, 120);
                    border: 1px solid rgba(0, 180, 255, 60);
                    border-radius: 4px;
                    padding: 8px;
                    color:white;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: rgba(0, 100, 150, 150);
                }
            """

        self.QPushButton_OpenFile = QtWidgets.QPushButton("打开音频文件")
        self.QPushButton_OpenFile.setStyleSheet(StyleSheet_QPushButton)
        # self.QPushButton_OpenFile.clicked.connect(self.open_file)

        self.QPushButton_Start = QtWidgets.QPushButton("执行分析")
        self.QPushButton_Start.setStyleSheet(StyleSheet_QPushButton)
        # self.QPushButton_Start.clicked.connect(self.start_playback)



        self.QPushButton_Stop = QtWidgets.QPushButton("停止")
        self.QPushButton_Stop.setStyleSheet(StyleSheet_QPushButton)
        # self.QPushButton_Stop.clicked.connect(self.stop_playback)


        self.QPushButton_Stop1 = QtWidgets.QPushButton("编辑")
        self.QPushButton_Stop1.setStyleSheet(StyleSheet_QPushButton)

        self.QPushButton_Stop2 = QtWidgets.QPushButton("AI")
        self.QPushButton_Stop2.setStyleSheet(StyleSheet_QPushButton)

        self.QPushButton_Stop3 = QtWidgets.QPushButton("输出")
        self.QPushButton_Stop3.setStyleSheet(StyleSheet_QPushButton)



        QHBoxLayout_Toolbar.addWidget( self.QPushButton_OpenFile)
        QHBoxLayout_Toolbar.addWidget( self.QPushButton_Start)
        QHBoxLayout_Toolbar.addWidget( self.QPushButton_Stop)
        QHBoxLayout_Toolbar.addWidget( self.QPushButton_Stop1)
        QHBoxLayout_Toolbar.addWidget( self.QPushButton_Stop2)
        QHBoxLayout_Toolbar.addWidget( self.QPushButton_Stop3)


        # # 左侧工具按钮
        # QWidget_Buttons = QtWidgets.QWidget()
        # QHBoxLayout_Left = QtWidgets.QHBoxLayout(QWidget_Buttons)
        # QHBoxLayout_Left.setContentsMargins(0, 0, 0, 0)
        #
        # for icon_name, tooltip in [("document-open", "打开项目"),
        #                            ("media-playback-start", "开始分析"),
        #                            ("media-playback-stop", "停止")]:
        #     btn = QtWidgets.QToolButton()
        #     btn.setIcon(QIcon.fromTheme(icon_name))
        #     btn.setToolTip(tooltip)
        #     btn.setStyleSheet("""
        #         QToolButton {
        #             background: transparent;
        #             padding: 5px;
        #             border-radius: 3px;
        #         }
        #         QToolButton:hover {
        #             background-color: rgba(60, 90, 120, 150);
        #         }
        #     """)
        #     QHBoxLayout_Left.addWidget(btn)
        # # self.open_btn = QtWidgets.QPushButton("打开音频文件")
        # # self.open_btn.clicked.connect(self.open_file)
        # # QHBoxLayout_Left.addWidget( self.open_btn)
        # # 右侧工具按钮
        # right_buttons = QWidget()
        # right_layout = QHBoxLayout(right_buttons)
        # right_layout.setContentsMargins(0, 0, 0, 0)
        #
        # for icon_name, tooltip in [("preferences-system", "设置"),
        #                            ("help-about", "关于")]:
        #     btn = QToolButton()
        #     btn.setIcon(QIcon.fromTheme(icon_name))
        #     btn.setToolTip(tooltip)
        #     btn.setStyleSheet("""
        #         QToolButton {
        #             background: transparent;
        #             padding: 5px;
        #             border-radius: 3px;
        #         }
        #         QToolButton:hover {
        #             background-color: rgba(60, 90, 120, 150);
        #         }
        #     """)
        #     right_layout.addWidget(btn)
        #
        # QHBoxLayout_Left.addWidget(QWidget_Buttons)
        # QHBoxLayout_Left.addStretch()
        # QHBoxLayout_Left.addWidget(right_buttons)

    def Set_Statusbar(self):
        self.QStatusBar_Main = QtWidgets.QStatusBar()
        self.QStatusBar_Main.setStyleSheet("""
            QStatusBar {
                background-color: rgba(30, 40, 50, 220);
                color: rgb(180, 200, 220);
                border-top: 1px solid rgba(0, 100, 150, 80);
            }
        """)

        # 状态标签
        Status_Labels = [
            ("就绪", "normal"),
            ("44.1kHz", "info"),
            ("16位", "info"),
            ("立体声", "info")
        ]

        # for Text, Style in Status_Labels:
        #     QLabel_Status = QLabel(Text)
        #     if Style == "info":
        #         QLabel_Status.setStyleSheet("""
        #             QLabel {
        #                 padding: 0 8px;
        #                 border-left: 1px solid rgba(100, 130, 150, 80);
        #                 color: rgb(150, 180, 210);
        #             }
        #         """)
        #     else:
        #         QLabel_Status.setStyleSheet("padding: 0 8px;")
        #     # ScrollingBanner_Status =
        #     self.QStatusBar_Main.addWidget(QLabel_Status)



        self.setStatusBar(self.QStatusBar_Main)

    def Set_Dock_Panels(self):
        # 左侧面板 - 项目资源 (绿色系)
        self.CollapsiblePanel_Left = CollapsiblePanel("项目资源", self)
        self.CollapsiblePanel_Left.setMinimumWidth(30)
        self.CollapsiblePanel_Left.setMaximumWidth(300)
        self.CollapsiblePanel_Left.setStyleSheet("""
            QFrame {
                background-color: rgba(20, 40, 30, 220);
                border: 1px solid rgba(50, 150, 100, 80);
            }
            QToolButton {
                background-color: rgba(30, 60, 40, 200);
                color: rgb(150, 220, 180);
            }
            QToolButton:hover {
                background-color: rgba(50, 90, 60, 200);
            }
        """)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.CollapsiblePanel_Left)

        # 右侧面板 - 分析工具 (蓝色系)
        self.CollapsiblePanel_Right = CollapsiblePanel("分析工具", self)
        self.CollapsiblePanel_Right.setMinimumWidth(10)
        self.CollapsiblePanel_Right.setMaximumWidth(300)
        self.CollapsiblePanel_Right.setStyleSheet("""
            QFrame {
                background-color: rgba(20, 30, 50, 220);
                border: 1px solid rgba(50, 100, 150, 80);
            }
            QToolButton {
                background-color: rgba(30, 40, 70, 200);
                color: rgb(150, 180, 220);
            }
            QToolButton:hover {
                background-color: rgba(50, 60, 90, 200);
            }
        """)
        self.addDockWidget(Qt.RightDockWidgetArea, self.CollapsiblePanel_Right)

        # 底部面板 - 输出控制台 (紫色系)
        self.CollapsiblePanel_Bottom = CollapsiblePanel("输出控制台", self)
        self.CollapsiblePanel_Bottom.setMinimumHeight(150)
        self.CollapsiblePanel_Bottom.setStyleSheet("""
            QFrame {
                background-color: rgba(40, 30, 50, 220);
                border: 1px solid rgba(100, 50, 150, 80);
            }
            QToolButton {
                background-color: rgba(60, 40, 70, 200);
                color: rgb(200, 150, 220);
            }
            QToolButton:hover {
                background-color: rgba(80, 60, 90, 200);
            }
        """)
        self.addDockWidget(Qt.BottomDockWidgetArea, self.CollapsiblePanel_Bottom)

    def Set_Content_Workspace(self):


        # 在中央工作区创建示例内容
        QHBoxLayout_Workspace = QtWidgets.QHBoxLayout(self.QWidget_Workspace)
        QHBoxLayout_Workspace.setContentsMargins(20, 20, 20, 20)
        QHBoxLayout_Workspace.addWidget(self.Add_Page(""))
        # # 示例波形显示区
        # wave_display = QLabel("主音频可视化区域")
        # wave_display.setAlignment(Qt.AlignCenter)
        # wave_display.setStyleSheet("""
        #     QLabel {
        #         background-color: rgba(20, 30, 40, 180);
        #         border: 1px dashed rgba(0, 180, 255, 60);
        #         border-radius: 8px;
        #         color: rgb(0, 200, 255);
        #         font: bold 16px;
        #     }
        # """)
        # wave_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # # 可视化部件
        # self.waveform_widget = AudioVisualizationWidget(800, 150, "waveform")
        # self.waveform = WaveformVisualizer()
        # QHBoxLayout_Workspace.addWidget(self.waveform_widget)


        # self.QWidget_Content.setStyleSheet('''background:red ''')
        # self.Stacked_Center = QtWidgets.QStackedLayout( self.QWidget_Workspace)
        # # 添加页面
        # Page_List = ["Voiceprint_Analysis"
        #
        #              ]
        #
        # self.Page_Change_Index = {}
        # # self.QLabel_Title
        # # Page_Widget_Home.Page_Widget_Home(args[0])
        # # PP(Page_Info,9)
        # Index = 0
        # for Page in Page_List:
        #     exec("self.__Page_%s = Page_Widget_%s.Page_Widget_%s(Page_Info)" % (Page, Page, Page))
        #     exec("self.__Page_%s.setMinimumSize(self.QWidget_Workspace.width(),self.QWidget_Workspace.height())" % (Page))
        #     exec("self.__Page_%s.Signal_Result.connect(self.PAGE_HANDLE_RESULT)" % (Page))
        #     exec("self.Stacked_Center.addWidget(self.__Page_%s)" % (Page))
        #     self.Page_Change_Index[Page] = Index
        #     Index += 1
        #     PP(("Index", Index))
        # # Page_Info["Page_Change_Index"] = self.Page_Change_Index
        # self.Stacked_Center.setCurrentIndex(0)

        # self.Stacked_Center.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def Add_Page(self,Page_Name):
        __Page_Widget_Voiceprint_Analysis = Page_Widget_Voiceprint_Analysis.Page_Widget_Voiceprint_Analysis(Page_Info)
        __Page_Widget_Voiceprint_Analysis.setMinimumSize(self.QWidget_Workspace.width(),self.QWidget_Workspace.height())
        __Page_Widget_Voiceprint_Analysis.Signal_Result.connect(self.PAGE_HANDLE_RESULT)

        return __Page_Widget_Voiceprint_Analysis




    def PAGE_HANDLE_RESULT(self,Result):
        PP(Result)




    def update_visualizations(self):
        # 生成随机数据用于演示
        # waveform_data = [random.random() * 2 - 1 for _ in range(100)]
        # spectrum_data = [random.random() for _ in range(32)]
        self.waveform_widget.update_data(self.audio_buffer)
        # self.waveform.update_values(waveform_data)
        # self.spectrum.update_bands(spectrum_data)




        # QHBoxLayout_Workspace.addWidget(wave_display)


class BreadcrumbBar(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(30)
        QHBoxLayout_Main = QHBoxLayout(self)
        QHBoxLayout_Main.setContentsMargins(10, 0, 10, 0)
        QHBoxLayout_Main.setSpacing(5)

        # 示例面包屑
        for i, text in enumerate(["项目", "音频分析", "实时监测"]):
            label = QLabel(text)
            label.setStyleSheet("""
                QLabel {
                    color: rgb(180, 200, 220);
                    padding: 2px 8px;
                }
            """)
            QHBoxLayout_Main.addWidget(label)

            if i < 2:
                sep = QLabel(">")
                sep.setStyleSheet("color: rgb(100, 130, 150);")
                QHBoxLayout_Main.addWidget(sep)

        QHBoxLayout_Main.addStretch()


class CollapsiblePanel(QtWidgets.QDockWidget):
    def __init__(self, title="Panel", parent=None):
        super().__init__(title, parent)
        self.setFeatures(QDockWidget.DockWidgetClosable |
                         QDockWidget.DockWidgetMovable |
                         QDockWidget.DockWidgetFloatable)
        self.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea | Qt.BottomDockWidgetArea)

        # 内容容器
        self.content = QWidget()
        self.setWidget(self.content)
        self._layout = QVBoxLayout(self.content)
        self._layout.setContentsMargins(5, 15, 5, 5)

        # 添加示例内容
        example_label = QLabel(f"{title}内容区域")
        example_label.setAlignment(Qt.AlignCenter)
        example_label.setStyleSheet("color: rgb(200, 220, 255);")
        self._layout.addWidget(example_label)

        # 自定义标题栏
        self.title_bar = QToolButton()
        self.title_bar.setText(title)
        self.title_bar.setStyleSheet("""
            QToolButton {
                text-align: left;
                padding: 5px;
                border: none;
                color: rgb(200, 220, 255);
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 30);
            }
        """)
        self.title_bar.setIcon(QIcon.fromTheme("window-close"))
        self.title_bar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.title_bar.setIconSize(QSize(12, 12))
        self.title_bar.clicked.connect(self.toggle_collapse)

        self.setTitleBarWidget(self.title_bar)





        ScrollingBanner_Status = ScrollingBanner("欢迎使用云加速安全防护网络，安全、极速、稳定、简洁！")
        # self.QStatusBar_Main.addWidget(ScrollingBanner_Status)
        self._layout.addWidget(ScrollingBanner_Status)

        # 初始状态
        self._expanded = True
        self._original_size = None

    def toggle_collapse(self):
        if self._expanded:
            self._original_size = self.size()
            self.setFixedHeight(self.title_bar.height())
            self.title_bar.setIcon(QIcon.fromTheme("window-new"))
            self._expanded = False
        else:
            self.setMinimumSize(200, 200)
            self.setMaximumSize(16777215, 16777215)
            if self._original_size:
                self.resize(self._original_size)
            self.title_bar.setIcon(QIcon.fromTheme("window-close"))
            self._expanded = True




class ScrollingBanner(QtWidgets.QLabel):
    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.original_text = text
        self.scroll_position = 0
        self.scroll_speed = 1  # 每次滚动的像素数
        self.scroll_delay = 30  # 滚动间隔(ms)
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_scroll)

        # 设置紧凑样式
        self.setStyleSheet("""
            background-color: rgba(255, 255, 255, 0.15);  /* 图片中的半透明白色 */
                color: white;
                border: none;             /* 无边框 */
                padding: 0;               /* 无内边距 */
                margin: 0;                /* 无外边距 */
                font-weight: bold;
                font-size: 11px;
                min-height: 0;            /* 禁用最小高度 */
                max-height: 20px;         /* 完全匹配文字高度 */
        """)
        # 确保高度精确匹配文字
        self.setMinimumHeight(self.fontMetrics().height() + 2)  # 文字高度 +1px 微小空间
        self.setMaximumHeight(self.fontMetrics().height() + 2)

        # 确保左右完全贴边
        self.setContentsMargins(0, 0, 0, 0)
        # 启动滚动
        self.timer.start(self.scroll_delay)

    def update_scroll(self):
        # 计算新的滚动位置
        self.scroll_position -= self.scroll_speed
        if self.scroll_position < -self.fontMetrics().horizontalAdvance(self.original_text):
            self.scroll_position = self.width()

        # 触发重绘
        self.update()

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.TextAntialiasing)

        # 计算文本位置
        text_width = self.fontMetrics().horizontalAdvance(self.original_text)
        y_pos = self.height() / 2 + self.fontMetrics().height() / 4

        # 绘制滚动文本
        painter.drawText(self.scroll_position, y_pos, self.original_text)

        # 如果文本宽度大于控件宽度，绘制第二份文本实现循环
        if text_width > self.width():
            painter.drawText(self.scroll_position + text_width + 20, y_pos, self.original_text)







class SciFiPanel(QtWidgets.QFrame):
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.title = title
        self.setMinimumSize(200, 200)

        # 面板样式
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(20, 30, 40, 180);
                border: 1px solid rgba(0, 180, 255, 80);
                border-radius: 8px;
            }
        """)

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 绘制标题
        if self.title:
            font = QFont("Arial", 10, QFont.Bold)
            painter.setFont(font)
            painter.setPen(QColor(0, 200, 255))
            painter.drawText(10, 20, self.title)

        # 绘制装饰性边框
        border_pen = QtGui.QPen(QColor(0, 180, 255, 60), 1)
        painter.setPen(border_pen)
        painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 6, 6)

        # 绘制装饰性线条
        highlight_pen = QtGui.QPen(QColor(0, 255, 255, 30), 1)
        painter.setPen(highlight_pen)
        for i in range(5, self.width(), 15):
            painter.drawLine(i, 5, i, 10)



class WaveformVisualizer(SciFiPanel):
    def __init__(self, parent=None):
        super().__init__("波形分析", parent)
        self.values = [0] * 100

    def update_values(self, new_values):
        self.values = new_values[-100:]
        self.update()

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        if not self.values:
            return

        # 绘制波形
        path = QtGui.QPainterPath()
        width = self.width()
        height = self.height()
        step = width / len(self.values)

        path.moveTo(0, height / 2)
        for i, value in enumerate(self.values):
            x = i * step
            y = height / 2 - value * (height / 2) * 0.8
            path.lineTo(x, y)

        # 波形渐变
        gradient = QtGui.QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor(0, 255, 255, 200))
        gradient.setColorAt(1, QColor(0, 180, 255, 150))

        pen = QtGui.QPen(gradient, 2)
        painter.setPen(pen)
        painter.drawPath(path)

        # 绘制峰值指示器
        peak = max(self.values) if self.values else 0
        if peak > 0.1:
            peak_y = height / 2 - peak * (height / 2) * 0.8
            peak_pen = QtGui.QPen(QColor(255, 50, 50, 200), 1)
            painter.setPen(peak_pen)
            painter.drawLine(0, peak_y, width, peak_y)






if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 设置应用程序字体
    font = QFont("Noto Sans", 10)
    app.setFont(font)

    Window = Windows_VoiceprintAnlysis()
    Window.show()
    sys.exit(app.exec())