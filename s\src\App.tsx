import React, { useMemo } from 'react';
import { RouterProvider, createBrowserRouter } from 'react-router-dom'
// import router from '@/Core/Core_Router'
import { useFilteredRoutes } from '@/Core/Core_Router'
import { AuthProvider } from '@/Core/Core_AuthContent';
import { FullscreenProvider  } from '@/Core/Core_FullscreenContext';
import ErrorBoundary from '@/Component/ErrorBoundary'; 
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN'; // 引入中文语言包
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'; // 设置 dayjs 的中文支持
import '@/Assets/CSS/App.css';

// 设置 dayjs 使用中文
dayjs.locale('zh-cn');
function App() {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm, // 暗黑主题算法
        token: {
          colorPrimary: '#5b8ff9', // 主题色
          colorBgBase: '#1e263d',   // 基础背景色
          colorTextBase: '#ffffff',  // 基础文字颜色
          colorBorder: '#3a3f53', // 深灰色，与背景色协调
          borderRadius: 8, // 圆角
        },
        components: {
          Table: {
            colorBgContainer: '#1e263d', // 表格整体背景色
            headerBg: '#161f35', // 表头背景色
            headerColor: '#ffffff', // 表头文字颜色
            borderColor: '#2a3452', // 表头边框颜色
            colorText: '#ffffff', // 表格文字颜色
            rowHoverBg: '#2a3452', // 悬停行背景色
          },
          Card: {
            colorBgContainer: 'transparent', // Card 背景透明
          }
        },
      }}
      locale={zhCN}
    >
      <AuthProvider>
        <ErrorBoundary> {/* 添加 ErrorBoundary 包裹 404全局错误边界组件 */}
          <FullscreenProvider> {/* 添加 FullscreenProvider 包裹 全局全屏边界组件 */}
            <AppContent />
          </FullscreenProvider>
        </ErrorBoundary>
      </AuthProvider>
    </ConfigProvider>
  )
}
/**
 * 
 * @returns 权限验证路由组件
 */
const AppContent = () => {
  const filteredRoutes = useFilteredRoutes();
  const router = useMemo(() => createBrowserRouter(filteredRoutes), [filteredRoutes]);
  return (
    <div className="app">
      <RouterProvider router={router} />
    </div>
  );
};

export default App