import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Checkbox, 
  Row, 
  Col, 
  Space, 
  Typography, 
  List,
  Pagination,
  message,
  Spin,
  Empty,
  Tooltip
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined,
  ReloadOutlined,
  FireOutlined
} from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Search_Display.css';

const { Title, Text } = Typography;
const { Search } = Input;

// 数据类型定义
interface SearchResult {
  id: string;
  title: string;
  content: string;
  source: string;
  author: string;
  publishTime: string;
  emotion: string;
  url: string;
}

interface HotNotice {
  id: string;
  content: string;
  type: number;
  url: string;
  level: string;
}

interface EmotionCount {
  all: number;
  positive: number;
  neutral: number;
  negative: number;
}

const Page_Search_Display: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [hotNotices, setHotNotices] = useState<HotNotice[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [emotionFilter, setEmotionFilter] = useState<string[]>(['All']);
  const [emotionCounts, setEmotionCounts] = useState<EmotionCount>({
    all: 0,
    positive: 0,
    neutral: 0,
    negative: 0
  });
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  // 初始化数据
  useEffect(() => {
    loadHotNotices();
  }, []);

  // 加载热点动态
  const loadHotNotices = async () => {
    try {
      // 模拟数据
      const mockHotNotices: HotNotice[] = [
        {
          id: '1',
          content: '科技创新推动经济高质量发展，新兴产业蓬勃发展',
          type: 1,
          url: 'https://example.com/news1',
          level: 'level1'
        },
        {
          id: '2',
          content: '教育改革深入推进，素质教育成效显著',
          type: 2,
          url: 'https://example.com/news2',
          level: 'level2'
        },
        {
          id: '3',
          content: '环保政策效果明显，生态环境持续改善',
          type: 3,
          url: 'https://example.com/news3',
          level: 'level3'
        },
        {
          id: '4',
          content: '数字化转型加速，传统产业焕发新活力',
          type: 1,
          url: 'https://example.com/news4',
          level: 'level1'
        },
        {
          id: '5',
          content: '文化产业蓬勃发展，文化自信不断增强',
          type: 2,
          url: 'https://example.com/news5',
          level: 'level2'
        },
        {
          id: '6',
          content: '医疗健康服务水平不断提升，民生福祉持续改善',
          type: 1,
          url: 'https://example.com/news6',
          level: 'level1'
        },
        {
          id: '7',
          content: '交通基础设施建设加快，出行更加便民',
          type: 3,
          url: 'https://example.com/news7',
          level: 'level3'
        },
        {
          id: '8',
          content: '金融服务实体经济能力增强，支持高质量发展',
          type: 2,
          url: 'https://example.com/news8',
          level: 'level2'
        }
      ];
      
      setHotNotices(mockHotNotices);
    } catch (error) {
      message.error('加载热点动态失败');
    }
  };

  // 执行搜索
  const handleSearch = async (keyword?: string) => {
    const searchTerm = keyword || searchKeyword;
    if (!searchTerm.trim()) {
      message.warning('请输入搜索关键词');
      return;
    }

    setLoading(true);
    try {
      // 模拟数据
      const mockResults: SearchResult[] = [
        {
          id: '1',
          title: `关于"${searchTerm}"的最新报道：科技创新推动经济高质量发展`,
          content: `随着${searchTerm}相关政策的不断完善，科技创新正在成为推动经济高质量发展的重要引擎。各地积极响应，加大投入力度，推动产业转型升级。`,
          source: '人民日报',
          author: '张记者',
          publishTime: '2024-01-15 14:30:25',
          emotion: '正面',
          url: 'https://example.com/article1'
        },
        {
          id: '2',
          title: `${searchTerm}领域教育改革深入推进，素质教育成效显著`,
          content: `在${searchTerm}相关教育改革的推动下，素质教育理念深入人心，学生综合素质得到全面提升，教育质量不断改善。`,
          source: '光明日报',
          author: '李记者',
          publishTime: '2024-01-15 13:45:12',
          emotion: '正面',
          url: 'https://example.com/article2'
        },
        {
          id: '3',
          title: `${searchTerm}相关环保政策效果明显，生态环境持续改善`,
          content: `在严格的${searchTerm}环保政策推动下，各地生态环境质量显著改善，绿色发展理念深入人心，可持续发展取得新进展。`,
          source: '环球时报',
          author: '王记者',
          publishTime: '2024-01-15 12:20:08',
          emotion: '正面',
          url: 'https://example.com/article3'
        },
        {
          id: '4',
          title: `${searchTerm}数字化转型加速，传统产业焕发新活力`,
          content: `数字化转型浪潮下，${searchTerm}相关传统产业积极拥抱新技术，实现转型升级，焕发出新的发展活力。`,
          source: '经济日报',
          author: '赵记者',
          publishTime: '2024-01-15 11:15:33',
          emotion: '中性',
          url: 'https://example.com/article4'
        },
        {
          id: '5',
          title: `${searchTerm}文化产业蓬勃发展，文化自信不断增强`,
          content: `${searchTerm}文化产业快速发展，优秀传统文化与现代科技深度融合，文化自信不断增强，文化软实力显著提升。`,
          source: '中国文化报',
          author: '钱记者',
          publishTime: '2024-01-15 10:30:45',
          emotion: '正面',
          url: 'https://example.com/article5'
        }
      ];
      
      setSearchResults(mockResults);
      setTotalCount(25); // 模拟总数
      setEmotionCounts({
        all: 25,
        positive: 18,
        neutral: 5,
        negative: 2
      });
      setShowResults(true);
    } catch (error) {
      message.error('搜索失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理情感筛选
  const handleEmotionFilter = (checkedValues: any) => {
    setEmotionFilter(checkedValues);
  };

  // 处理分页
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
    handleSearch();
  };

  // 下载全部结果
  const handleDownloadAll = () => {
    message.success('开始下载全部搜索结果');
  };

  // 刷新热点动态
  const handleRefreshHotNotices = () => {
    loadHotNotices();
    message.success('热点动态已刷新');
  };

  // 点击热点新闻
  const handleHotNoticeClick = (notice: HotNotice) => {
    window.open(notice.url, '_blank');
  };

  return (
    <div className="search-display-page">
      <Row justify="center">
        <Col xs={24} lg={20}>
          {/* 搜索卡片 */}
          <Card className="search-card">
            <div className="search-header">
              <Title level={1} className="search-title">
                元引擎搜索
              </Title>
            </div>
            
            <div className="search-input-container">
              <Search
                placeholder="请输入搜索关键词,多个关键词之间空格隔开(如:杭州 2022 亚运会)"
                size="large"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onSearch={handleSearch}
                enterButton="搜索一下"
                loading={loading}
                className="search-input"
              />
            </div>

            {/* 搜索结果区域 */}
            {showResults && (
              <div className="search-results-section">
                {/* 筛选和统计 */}
                <Row className="results-toolbar">
                  <Col span={16}>
                    <Space align="center">
                      <Text strong>情感属性:</Text>
                      <Checkbox.Group
                        value={emotionFilter}
                        onChange={handleEmotionFilter}
                      >
                        <Space wrap>
                          <Checkbox value="All">
                            全部({emotionCounts.all})
                          </Checkbox>
                          <Checkbox value="正面">
                            正面({emotionCounts.positive})
                          </Checkbox>
                          <Checkbox value="中性">
                            中性({emotionCounts.neutral})
                          </Checkbox>
                          <Checkbox value="负面">
                            负面({emotionCounts.negative})
                          </Checkbox>
                        </Space>
                      </Checkbox.Group>
                    </Space>
                  </Col>
                  <Col span={8}>
                    <div className="results-actions">
                      <Space>
                        <Text>共{totalCount}条消息</Text>
                        <Button 
                          size="small" 
                          icon={<DownloadOutlined />}
                          onClick={handleDownloadAll}
                        >
                          全部下载
                        </Button>
                      </Space>
                    </div>
                  </Col>
                </Row>

                {/* 搜索结果列表 */}
                <div className="search-results-list">
                  {loading ? (
                    <div className="loading-container">
                      <Spin size="large" />
                    </div>
                  ) : searchResults.length > 0 ? (
                    <List
                      dataSource={searchResults}
                      renderItem={(item) => (
                        <List.Item className="search-result-item">
                          <div className="result-content">
                            <div className="result-header">
                              <a 
                                href={item.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="result-title"
                              >
                                {item.title}
                              </a>
                              <Space className="result-meta">
                                <Text type="secondary">{item.source}</Text>
                                <Text type="secondary">{item.author}</Text>
                                <Text type="secondary">{item.publishTime}</Text>
                                <span 
                                  className={`emotion-tag ${
                                    item.emotion === '正面' ? 'positive' : 
                                    item.emotion === '负面' ? 'negative' : 'neutral'
                                  }`}
                                >
                                  {item.emotion}
                                </span>
                              </Space>
                            </div>
                            <div className="result-body">
                              <Text>{item.content}</Text>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  ) : (
                    <Empty description="暂无搜索结果" />
                  )}
                </div>

                {/* 分页 */}
                {totalCount > 0 && (
                  <div className="search-pagination">
                    <Pagination
                      current={currentPage}
                      pageSize={pageSize}
                      total={totalCount}
                      showSizeChanger
                      showQuickJumper
                      showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                      onChange={handlePageChange}
                    />
                  </div>
                )}
              </div>
            )}

            {/* 热点动态 */}
            <div className="hot-notices-section">
              <div className="hot-notices-header">
                <Title level={5}>热点动态</Title>
                <Button 
                  size="small" 
                  icon={<ReloadOutlined />}
                  onClick={handleRefreshHotNotices}
                >
                  换一换
                </Button>
              </div>
              
              <div className="hot-notices-list">
                {hotNotices.map((notice) => (
                  <div 
                    key={notice.id}
                    className={`hot-notice-item notices-${notice.level}`}
                    onClick={() => handleHotNoticeClick(notice)}
                  >
                    <div className="notice-content">
                      <FireOutlined className="notice-icon" />
                      <Text ellipsis={{ tooltip: notice.content }}>
                        {notice.content}
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Page_Search_Display;
