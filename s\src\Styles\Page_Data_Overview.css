/* 数据总览页面样式 - 使用默认背景 */

/* 圆角样式 */
.radius-15 {
  border-radius: 15px !important;
}

/* 数字显示样式 */
.odometer {
  font-weight: bold;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 滚动容器样式 */
#Today-Sentiment-Hot-Article-container,
#Today-Sentiment-Negative-Article-container {
  height: 400px;
  overflow: hidden;
  position: relative;
}

#Today-Sentiment-Hot-Article-content,
#Today-Sentiment-Negative-Article-content {
  position: absolute;
  top: 0;
}

.item {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  padding-right: 10px;
  margin-bottom: 20px;
}

/* 按钮样式 */
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.text-semibold {
  font-weight: 600;
}