import React from 'react';
import styles from '../Styles/Utils_Common.module.css';
import TeslaDashboardPlus from '../Pages/TeslaDashboardPlus';
import { AlignLeftOutlined ,BarsOutlined ,PlusOutlined,MinusOutlined,FullscreenOutlined,EyeOutlined ,ExpandOutlined  ,AimOutlined,CloseOutlined} from '@ant-design/icons';
interface DialogProps {
  isOpen: boolean; // 接收 isOpen 状态
  toggleDialog: () => void; // 接收 toggleDialog 函数
}

const Utils_Dialog_Setting: React.FC<DialogProps> = ({ isOpen, toggleDialog }) => {
  return (
    <div className={`${styles.Dialog_Common} ${isOpen ? styles.Open : ''}`}>
      <div className={styles.Dialog_Content}>
        <p>这是一个对话框</p>
        {/* <button onClick={toggleDialog}>关闭</button> */}


        <div style={{ height: "33px", width: "33px",top: 8,right: 10,position: 'absolute',zIndex:99999,backgroundColor:"transparent",}} >
            <CloseOutlined  onClick={toggleDialog}  style={{margin: "4px", color: 'white', fontSize:18 }} />

        </div>






         <TeslaDashboardPlus ></TeslaDashboardPlus>

      </div>
    </div>
  );
};

export default Utils_Dialog_Setting;