/* 图标外层容器：用于固定图标区域 */
.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

/* 图标样式保持不变 */
.icon {
  width: 16px;
  height: 16px;
  color: white;
  z-index: 2;
}

/* 文字样式：默认隐藏，hover 显示 */
.textSpan {
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease;
  color: #fff !important;
  z-index: 1;
  display: none;
}


/* 全屏按钮整体定位 */
:global(.fullscreen-exit-button) {
  position: fixed;
  top: 3px;
  right: 2px;
  z-index: 9999;
  display: flex;
  justify-content: flex-end;
}

/* 按钮内容容器：初始状态只显示图标 */
:global(.fullscreen-button-content) {
  position: relative;
  width: 32px;
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0.8;
  flex-shrink: 0;
}

/* Hover 状态下扩展按钮宽度并显示文字 */
:global(.fullscreen-button-content:hover) {
  width: 120px;
  padding: 8px 12px;
  gap: 8px;
  justify-content: flex-start;
}

/* Hover 时显示文字 */
:global(.fullscreen-button-content:hover .FullScreenExitButton_textSpan__2POqa) {
  display: inline-block;
  opacity: 1;
}
