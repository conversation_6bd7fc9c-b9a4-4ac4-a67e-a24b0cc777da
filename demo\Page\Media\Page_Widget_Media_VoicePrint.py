# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
# from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.Utils.UtilsCenter import *
import numpy as np
import librosa

Page_Info={
    "Title":"哨兵核心服务配置",
    "Param": {},

}

from Bin.System.OS.Component import Component_Common, Component_AudioPlay,Component_AudioEQ,Component_AudioNoise,Component_AudioChat,Component_AudioTranslate,Component_Processing_EQ





PP(Page_Info)
# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_Media_VoicePrint(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass

        Page_Info["Page_Element_List"]={}
        Page_Info["StyleSheet"]={}
        Page_Info["StyleSheet"]["Value_Color"] ="rgba(0, 255, 136, 255)"

        self.initUI()

        # 音频分析器
        self.audio_analyzer = None
        self.audio_file = None



    def initUI(self):
        # self.Set_Title()
        self.Set_Content()

        self.setStyleSheet("background-color: rgba(40, 52, 80, 0.3)")


    def Set_Title(self):
        Icon = qta.icon('ph.caret-left-light', scale_factor=1, color=('black'), color_active='black')
        Button_Back = QtWidgets.QPushButton(self)
        Button_Back.setIconSize(QtCore.QSize(25, 25))
        Button_Back.setStyleSheet("background:transparent")
        Button_Back.setIcon(Icon)
        Button_Back.setGeometry(QtCore.QRect(16, 38, 30, 30))
        Button_Back.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE({"Command": "Page_Change", "Page_Name": "Home"}))

        Label_Title = QtWidgets.QLabel(self)
        Label_Title.setStyleSheet("background:transparent")
        Label_Title.setFont(QtGui.QFont("Microsoft YaHei", 13))
        Label_Title.setGeometry(QtCore.QRect(36, 12, 280, 80))
        Label_Title.setText(Page_Info['Title'])


    def Set_Content(self):
        QVBoxLayout_Workspace = QtWidgets.QVBoxLayout(self)
        QVBoxLayout_Workspace.setContentsMargins(3, 3, 3, 3)
        #
        # StyleSheet_QLabel = """
        #                QLabel {
        #                    background-color: rgba(0, 50, 80, 120);
        #                    border: 1px solid rgba(0, 180, 255, 60);
        #                    border-radius: 4px;
        #                    padding: 8px;
        #                    color:white;
        #                    min-width: 80px;
        #                }
        #                QLabel:hover {
        #                    background-color: rgba(0, 100, 150, 150);
        #                }
        #            """
        self.StyleSheet_QLabel = """
                       QLabel {
                           background-color: rgba(40, 52, 80, 0.3);
                           border: 1px solid rgba(0, 180, 255, 60);
                           border-radius: 4px;
                           padding: 0px;
                           color:white;
        
                       }
                       QLabel:hover {
                           background-color: rgba(0, 100, 150, 150);
                       }
                   """

        self.QLabel_Row1 = QtWidgets.QLabel()
        self.QLabel_Row1.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")


        self.QLabel_Row2 = QtWidgets.QLabel()
        self.QLabel_Row2.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")

        self.QLabel_Row3 = QtWidgets.QLabel()
        self.QLabel_Row3.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")

        self.QLabel_Row4 = QtWidgets.QLabel()
        self.QLabel_Row4.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")


        self.QLabel_Row5 = QtWidgets.QLabel()
        self.QLabel_Row5.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")



        # Label_1.setMinimumSize(QtCore.QSize(30, 120))  # 设置最小尺寸为 (0, 0)
        # Label_1.setMaximumSize(QtCore.QSize(30, 120))  # 设置最大尺寸为 (0, 0)


        QVBoxLayout_Workspace.addWidget(self.QLabel_Row1,1)
        QVBoxLayout_Workspace.addWidget(self.QLabel_Row2,1)
        QVBoxLayout_Workspace.addWidget(self.QLabel_Row3,1)
        QVBoxLayout_Workspace.addWidget(self.QLabel_Row4,1)
        QVBoxLayout_Workspace.addWidget(self.QLabel_Row5,1)

        Page_Info["Page_Element_List"]["QLabel_Row1"] = self.QLabel_Row1

        self.Set_Row1()
        self.Set_Row2()
        self.Set_Row3()
        self.Set_Row4()
        self.Set_Row5()

    def Set_Row1(self):
        StyleSheet_QLabel = """
                                           QLabel {
                                               background-color: rgba(0, 255, 255, 0.8);
                                               border: 1px solid rgba(0, 180, 255, 60);
                                               border-radius: 4px;
                                               padding: 8px;
                                               color:white;
                                               min-width: 80px;
                                           }
                                           QLabel:hover {
                                               background-color: rgba(0, 100, 150, 150);
                                           }
                                       """
        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Row1)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Graphic_Equalizer = QtWidgets.QLabel()
        QLabel_Graphic_Equalizer.setStyleSheet(StyleSheet_QLabel)

        QLabel_Analysis_Filter = QtWidgets.QLabel()
        QLabel_Analysis_Filter.setStyleSheet(StyleSheet_QLabel)

        QLabel_Analysis_Envelope = QtWidgets.QLabel()
        QLabel_Analysis_Envelope.setStyleSheet(StyleSheet_QLabel)

        # __QHBoxLayout.addWidget(self.Set_Graphic_Equalizer())
        __QHBoxLayout.addWidget(self.Set_Analysis_ChatVocie())
        __QHBoxLayout.addWidget(self.Set_Analysis_Amplitude())
        __QHBoxLayout.addWidget(self.Set_Analysis_Waveform())


    def Set_Row2(self):
        StyleSheet_QLabel = """
                                     QLabel {
                                         background-color: rgba(0, 255, 255, 0.8);
                                         border: 1px solid rgba(0, 180, 255, 60);
                                         border-radius: 4px;
                                         padding: 8px;
                                         color:white;
                                         min-width: 80px;
                                     }
                                     QLabel:hover {
                                         background-color: rgba(0, 100, 150, 150);
                                     }
                                 """
        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Row2)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Frequency_Response = QtWidgets.QLabel()
        QLabel_Frequency_Response.setStyleSheet(StyleSheet_QLabel)

        QLabel_Analysis_Positional = QtWidgets.QLabel()
        QLabel_Analysis_Positional.setStyleSheet(StyleSheet_QLabel)


        QLabel_Analysis_Distortion = QtWidgets.QLabel()
        QLabel_Analysis_Distortion.setStyleSheet(StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Control_Audio())
        __QHBoxLayout.addWidget(self.Set_Control_EQ())
        __QHBoxLayout.addWidget(self.Set_Control_1())



    def Set_Row3(self):
        StyleSheet_QLabel = """
                                  QLabel {
                                      background-color: rgba(0, 255, 255, 0.8);
                                      border: 1px solid rgba(0, 180, 255, 60);
                                      border-radius: 4px;
                                      padding: 8px;
                                      color:white;
                                      min-width: 80px;
                                  }
                                  QLabel:hover {
                                      background-color: rgba(0, 100, 150, 150);
                                  }
                              """
        QHBoxLayout_Row1 = QtWidgets.QHBoxLayout(self.QLabel_Row3)
        QHBoxLayout_Row1.setContentsMargins(0, 0, 0, 0)

        QLabel_Analysis_Waveform = QtWidgets.QLabel()
        QLabel_Analysis_Waveform.setStyleSheet(StyleSheet_QLabel)

        QLabel_Analysis_Spectrogram = QtWidgets.QLabel()
        QLabel_Analysis_Spectrogram.setStyleSheet(StyleSheet_QLabel)

        # QHBoxLayout_Row1.addWidget(self.Set_Analysis_Waveform(), 1)
        QHBoxLayout_Row1.addWidget(self.Set_Control_AudioPlay(), 1)
        # QHBoxLayout_Row1.addWidget(self.Set_Analysis_Spectrogram(), 1)
        QHBoxLayout_Row1.addWidget(self.Set_Control_ChatTranslate(), 1)


    def Set_Row4(self):
        StyleSheet_QLabel = """
                                           QLabel {
                                               background-color: rgba(0, 255, 255, 0.8);
                                               border: 1px solid rgba(0, 180, 255, 60);
                                               border-radius: 0px;
                                               padding: 0px;
                                               color:white;
                                           }
                                           QLabel:hover {
                                               background-color: rgba(0, 100, 150, 150);
                                           }
                                       """
        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Row4)
        # QHBoxLayout_Row4.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Co1 = QtWidgets.QLabel()
        QLabel_Co1.setStyleSheet(StyleSheet_QLabel)

        QLabel_Co2 = QtWidgets.QLabel()
        QLabel_Co2.setStyleSheet(StyleSheet_QLabel)

        QLabel_Co3 = QtWidgets.QLabel()
        QLabel_Co3.setStyleSheet(StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Row4_Col1())
        __QHBoxLayout.addWidget(self.Set_Row4_Col2())
        __QHBoxLayout.addWidget(self.Set_Row4_Col3())
        # QHBoxLayout_Row4.addWidget(QLabel_Analysis_Envelope)

    def Set_Row4_Col1(self):

        QLabel_Row4_Col1 = QtWidgets.QLabel()
        QLabel_Row4_Col1.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row4_Col1)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)





        QLabel_Analysis_Correlation = QtWidgets.QLabel()
        QLabel_Analysis_Correlation.setStyleSheet(self.StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Analysis_Noise())
        __QHBoxLayout.addWidget(self.Set_Analysis_Association())
        # __QHBoxLayout.addWidget(QLabel_Analysis_Correlation)

        return QLabel_Row4_Col1

    def Set_Row4_Col2(self):

        QLabel_Row4_Col2 = QtWidgets.QLabel()
        QLabel_Row4_Col2.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row4_Col2)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)





        QLabel_Analysis_Correlation = QtWidgets.QLabel()
        QLabel_Analysis_Correlation.setStyleSheet(self.StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Analysis_HumanVoice())
        __QHBoxLayout.addWidget(self.Set_Analysis_Language())
        # __QHBoxLayout.addWidget(QLabel_Analysis_Correlation)

        return QLabel_Row4_Col2

    def Set_Row4_Col3(self):

        QLabel_Row4_Col3 = QtWidgets.QLabel()
        QLabel_Row4_Col3.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row4_Col3)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)





        QLabel_Analysis_Correlation = QtWidgets.QLabel()
        QLabel_Analysis_Correlation.setStyleSheet(self.StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Analysis_Scene())
        __QHBoxLayout.addWidget(self.Set_Analysis_Sex())
        # __QHBoxLayout.addWidget(QLabel_Analysis_Correlation)

        return QLabel_Row4_Col3


    def Set_Row5(self):

        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Row5)
        # QHBoxLayout_Row4.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Co1 = QtWidgets.QLabel()
        QLabel_Co1.setStyleSheet(self.StyleSheet_QLabel)

        QLabel_Co2 = QtWidgets.QLabel()
        QLabel_Co2.setStyleSheet(self.StyleSheet_QLabel)

        QLabel_Co3 = QtWidgets.QLabel()
        QLabel_Co3.setStyleSheet(self.StyleSheet_QLabel)

        __QHBoxLayout.addWidget(self.Set_Row5_Col1())
        __QHBoxLayout.addWidget(self.Set_Row5_Col2())
        __QHBoxLayout.addWidget(self.Set_Row5_Col3())
        # QHBoxLayout_Row4.addWidget(QLabel_Analysis_Envelope)

    def Set_Row5_Col1(self):

        QLabel_Row5_Col1 = QtWidgets.QLabel()
        QLabel_Row5_Col1.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row5_Col1)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)



        __QHBoxLayout.addWidget(self.Set_File_Management())
        __QHBoxLayout.addWidget(self.Set_Audio_Processing())


        return QLabel_Row5_Col1

    def Set_Row5_Col2(self):

        QLabel_Row5_Col2 = QtWidgets.QLabel()
        QLabel_Row5_Col2.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row5_Col2)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)



        __QHBoxLayout.addWidget(self.Set_Data_Export())
        __QHBoxLayout.addWidget(self.Set_Analysis_AI())


        return QLabel_Row5_Col2

    def Set_Row5_Col3(self):

        QLabel_Row5_Col3 = QtWidgets.QLabel()
        QLabel_Row5_Col3.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")

        __QHBoxLayout = QtWidgets.QHBoxLayout(QLabel_Row5_Col3)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)



        __QHBoxLayout.addWidget(self.Set_Material_Evaluation())
        __QHBoxLayout.addWidget(self.Set_Computing_Monitoring())


        return QLabel_Row5_Col3



    def Set_Analysis_Waveform(self):



        QLabel_Info = {
            "Title_Name": "实时波形分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        self.QWidget_Waveform = AudioVisualizationWidget(800, 150, "waveform")

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("采样率: 48kHz|24bit")
        QVBoxLayout_Content.addWidget(self.QWidget_Waveform)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)


        return __QLabel




    def Set_Analysis_Spectrogram(self):






        QLabel_Info = {
            "Title_Name": "语谱图分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        self.QWidget_Spectrogram = AudioVisualizationWidget(800, 150, "spectrogram")

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("®FFT:2048|窗函数: Hanning")
        QVBoxLayout_Content.addWidget(self.QWidget_Spectrogram)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)


        return __QLabel


    def Set_Analysis_ChatVocie(self):

        QLabel_Info = {
            "Title_Name": "音频振幅分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        audio_file = r"D:\M800001ziKgJ3o5Ipp.mp3"
        # self.__Component_AudioChat = Component_AudioChat.Component_AudioChat(audio_file)

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("®FFT:2048|窗函数: Hanning")
        # QVBoxLayout_Content.addWidget(self.__Component_AudioChat)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel

    #



    def Set_Analysis_Amplitude(self):

        QLabel_Info = {
            "Title_Name": "音频振幅分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        # 创建波形图组件，替换原来的AudioEqualizerBar
        self.__AudioWaveformWidget = AudioWaveformWidget()

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("®FFT:2048|窗函数: Hanning")
        QVBoxLayout_Content.addWidget(self.__AudioWaveformWidget)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel





    def Set_Control_Audio(self):
        QLabel_Info = {
            "Title_Name": "素材增益"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)









        #
        StyleSheet_QLabel = """
                                  QLabel {
                                    background-color: rgba(40, 52, 80, 0.3);;
                                      border: 1px solid rgba(0, 180, 255, 60);
                                      border-radius: 4px;

                                      color:rgba(0, 255, 136, 255);

                                  }
                                  QLabel:hover {
                                      background-color: rgba(0, 100, 150, 150);
                                  }
                              """


        Function_List=[
            {"Function_Name":"只留场景","Function_Command":{"Command":""},},
            {"Function_Name":"只留人声","Function_Command":{"Command":""},},
            {"Function_Name":"消音","Function_Command":{"Command":""},},
            {"Function_Name":"降噪","Function_Command":{"Command":""},},
            {"Function_Name":"增益","Function_Command":{"Command":""},},
            {"Function_Name":"人声增强","Function_Command":{"Command":""},},
            {"Function_Name":"背景增强","Function_Command":{"Command":""},},
            {"Function_Name":"EQ处理","Function_Command":{"Type":"Self_Open_Processing","Processing":"Processing_EQ"},},
            {"Function_Name":"关键音","Function_Command":{"Command":""},},
            {"Function_Name":"降低衰减","Function_Command":{"Command":""},},
        ]

        def Set_Fucntion(Row,Col):
            _QLabel = Component_Common.Component_Common_QLabel_Click()
            _QLabel.setText(Function_List[Row * 3 + Col]["Function_Name"])
            _QLabel.setAlignment(Qt.AlignCenter)  # 设置文本居中
            _QLabel.setFixedHeight(30)
            _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
            _QLabel.clicked.connect(lambda :self.PAGE_HANDLER_EXECUTE(Function_List[Row * 3 + Col]["Function_Command"]))
            return _QLabel

        for Row in range(3):
            for Col in range(3):

                QGridLayout_Content.addWidget(Set_Fucntion(Row,Col), Row, Col)

        return __QLabel






    def Set_Control_AudioPlay(self):
        QLabel_Info = {
            "Title_Name": "音频播放控制"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(1)
        QHBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        StyleSheet_QLabel = """
                                 QLabel {
                                   background-color: rgba(40, 52, 80, 0.3);;
                                     border: 1px solid rgba(0, 180, 255, 60);
                                     border-radius: 4px;

                                     color:rgba(0, 255, 136, 255);

                                 }
                                 QLabel:hover {
                                     background-color: rgba(0, 100, 150, 150);
                                 }
                             """

        file_path=r"D:\M800001ziKgJ3o5Ipp.mp3"
        __MusicProgressBar = Component_AudioPlay.MusicProgressBar(file_path)

        QHBoxLayout_Content.addWidget(__MusicProgressBar)
        # QHBoxLayout_Content.addWidget(QLabel_Right)

        return __QLabel


    def Set_Control_ChatTranslate(self):
        QLabel_Info = {
            "Title_Name": "语音转译"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(1)
        QHBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        StyleSheet_QLabel = """
                                 QLabel {
                                   background-color: rgba(40, 52, 80, 0.3);;
                                     border: 1px solid rgba(0, 180, 255, 60);
                                     border-radius: 4px;

                                     color:rgba(0, 255, 136, 255);

                                 }
                                 QLabel:hover {
                                     background-color: rgba(0, 100, 150, 150);
                                 }
                             """

        file_path=r"D:\M800001ziKgJ3o5Ipp.mp3"
        __Component_AudioTranslate = Component_AudioTranslate.Component_AudioTranslate()

        QHBoxLayout_Content.addWidget(__Component_AudioTranslate)
        # QHBoxLayout_Content.addWidget(QLabel_Right)

        return __QLabel


    def Set_Control_EQ(self):

        QLabel_Info = {
            "Title_Name": "音频EQ调试"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        __Component_AudioEQ =         Component_AudioEQ.Component_AudioEQ()







        # QLabel_Content_Status = QtWidgets.QLabel()
        # QLabel_Content_Status.setMinimumHeight(18)
        # QLabel_Content_Status.setMaximumHeight(18)
        # QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        # QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        # QLabel_Content_Status.setText("*得分：92/100")

        QVBoxLayout_Content.addWidget(__Component_AudioEQ)
        # QVBoxLayout_Content.addWidget(QLabel_Content_Status,0,alignment=QtCore.Qt.AlignBottom)

        return __QLabel





    def Set_Control_1(self):

        QLabel_Info = {
            "Title_Name": "音频噪音调试"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(8)
        QVBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        __Component_AudioNoise = Component_AudioNoise.Component_AudioNoise()

        # QLabel_Content_Status = QtWidgets.QLabel()
        # QLabel_Content_Status.setMinimumHeight(18)
        # QLabel_Content_Status.setMaximumHeight(18)
        # QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        # QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        # QLabel_Content_Status.setText("*得分：92/100")

        QVBoxLayout_Content.addWidget(__Component_AudioNoise)
        # QVBoxLayout_Content.addWidget(QLabel_Content_Status,0,alignment=QtCore.Qt.AlignBottom)

        return __QLabel

        # QLabel_Analysis_Control = QtWidgets.QLabel()
        # __QVBoxLayout = QtWidgets.QVBoxLayout(QLabel_Analysis_Control)
        # __QVBoxLayout.setSpacing(0)
        # __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        #
        # QLabel_Title = QtWidgets.QLabel()
        # QLabel_Title.setStyleSheet("background-color: rgba(0, 0, 0, 0.3);")
        # QLabel_Title.setMinimumHeight(30)
        # QLabel_Title.setMaximumHeight(30)
        #
        # QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        # QHBoxLayout_Title.setSpacing(0)
        # QHBoxLayout_Title.setContentsMargins(0, 0, 0, 0)
        #
        # QLabel_Icon = QtWidgets.QLabel()
        # QLabel_Icon.setFixedSize(28, 30)
        # # QLabel_Icon.setGeometry(0,0,18, 18)
        # QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        # QLabel_Icon.setStyleSheet(
        #     "QLabel {background-color: rgba(0, 0, 255, 0.3); padding: 0px; margin: 3px; border: 0px; }")
        #
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        # Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # QLabel_Icon.setPixmap(Image_Logo)
        #
        # QLabel_Title_Name = QtWidgets.QLabel()
        # QLabel_Title_Name.setText("分析控制")
        #
        # QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        # QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        # QLabel_Content = QtWidgets.QLabel()
        #
        # QVBoxLayout_Content = QtWidgets.QVBoxLayout(QLabel_Content)
        # QVBoxLayout_Content.setSpacing(1)
        # QVBoxLayout_Content.setContentsMargins(3, 0, 3, 0)
        # StyleSheet_QLabel = """
        #                                     QLabel {
        #                                         background-color: rgba(0, 50, 80, 120);
        #                                         border: 1px solid rgba(0, 180, 255, 60);
        #                                         border-radius: 4px;
        #                                         padding: 8px;
        #                                         color:cyan;
        #                                         min-width: 80px;
        #                                     }
        #                                     QLabel:hover {
        #                                         background-color: rgba(0, 100, 150, 150);
        #                                     }
        #                                 """
        #
        #
        #
        # QLabel_Content_Control= QtWidgets.QLabel()
        # QLabel_Content_Control.setStyleSheet(StyleSheet_QLabel)
        # QHBoxLayout_Content_Control = QtWidgets.QHBoxLayout(QLabel_Content_Control)
        # QHBoxLayout_Content_Control.setSpacing(1)
        # QHBoxLayout_Content_Control.setContentsMargins(0, 0, 0, 0)
        #
        #
        # __Switch_Rotary_1 = Switch_Rotary()
        # __Switch_Rotary_2 = Switch_Rotary()
        # __Switch_Rotary_3 = Switch_Rotary()
        # __Switch_Rotary_4 = Switch_Rotary()
        # # __Switch_Rotary_1 = Switch_Rotary()
        # # __Switch_Rotary_1 = Switch_Rotary()
        # # __Switch_Rotary.show()
        # QHBoxLayout_Content_Control.addWidget(__Switch_Rotary_1,alignment=QtCore.Qt.AlignCenter)
        # QHBoxLayout_Content_Control.addWidget(__Switch_Rotary_2,alignment=QtCore.Qt.AlignCenter)
        # QHBoxLayout_Content_Control.addWidget(__Switch_Rotary_3,alignment=QtCore.Qt.AlignCenter)
        # QHBoxLayout_Content_Control.addWidget(__Switch_Rotary_4,alignment=QtCore.Qt.AlignCenter)
        #
        #
        #
        # # QHBoxLayout_Content.addWidget(self.waveform_widget)
        #
        # QLabel_Content_Status = QtWidgets.QLabel()
        # QLabel_Content_Status.setMinimumHeight(18)
        # QLabel_Content_Status.setMaximumHeight(18)
        # QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        # QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        # QLabel_Content_Status.setText("®FFT:2048|窗函数: Hanning")
        #
        # QVBoxLayout_Content.addWidget(QLabel_Content_Control)
        # QVBoxLayout_Content.addWidget(QLabel_Content_Status)
        #
        # __QVBoxLayout.addWidget(QLabel_Title, )
        # __QVBoxLayout.addWidget(QLabel_Content, )
        #
        # return QLabel_Analysis_Control





    def Set_Analysis_Noise(self):

        QLabel_Info = {
            "Title_Name": "噪音分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        QLabel_Content_Evaluation_Class = QtWidgets.QLabel()
        QLabel_Content_Evaluation_Class.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Content_Evaluation_Class.setAlignment(QtCore.Qt.AlignCenter)



        QLabel_Content_Evaluation_Class.setText(
            f"""<div style="text-align:center;">
                <span style="font-size:18pt;color:{Page_Info["StyleSheet"]["Value_Color"]};font-weight: bold">-88dB</span><br>
                <span style="font-size:8pt;color:#1E90FF;">本底噪声</span>
                </div>"""
        )

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("*A-weighted")

        QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel



    def Set_Analysis_Association(self):

        QLabel_Info = {
            "Title_Name": "关联性分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                          QLabel {
                            background-color: rgba(40, 52, 80, 0.3);;
                              border: 1px solid rgba(0, 180, 255, 60);
                              border-radius: 4px;

                              color:rgba(0, 255, 136, 255);

                          }
                          QLabel:hover {
                              background-color: rgba(0, 100, 150, 150);
                          }
                      """
        QLabel_Left= QtWidgets.QLabel()
        QLabel_Left.setMinimumHeight(60)
        QLabel_Left.setMaximumHeight(60)
        QLabel_Left.setStyleSheet(StyleSheet_QLabel)
        QLabel_Left.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Left.setText('<div style="text-align:center;">'
                '<span style="font-size:13pt;font-weight: bold">0.18</span><br>'
                '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">L-R相关</span>'
                '</div>')

        QLabel_Right = QtWidgets.QLabel()
        QLabel_Right.setMinimumHeight(60)
        QLabel_Right.setMaximumHeight(60)
        QLabel_Right.setStyleSheet(StyleSheet_QLabel)
        QLabel_Right.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Right.setText('<div style="text-align:center;">'
            '<span style="font-size:13pt;font-weight: bold">0.15</span><br>'
            '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">互相关</span>'
            '</div>')

        QHBoxLayout_Content.addWidget(QLabel_Left)
        QHBoxLayout_Content.addWidget(QLabel_Right)




        return __QLabel



    def Set_Analysis_HumanVoice(self):

        QLabel_Info = {
            "Title_Name": "说话人检测"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)




        QLabel_Content_Evaluation_Class = QtWidgets.QLabel()
        QLabel_Content_Evaluation_Class.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Content_Evaluation_Class.setAlignment(QtCore.Qt.AlignCenter)



        QLabel_Content_Evaluation_Class.setText(
            f"""<div style="text-align:center;">
                <span style="font-size:18pt;color:{Page_Info["StyleSheet"]["Value_Color"]};font-weight: bold">3人</span><br>
                <span style="font-size:8pt;color:#1E90FF;">说话人数</span>
                </div>"""
        )

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("*精度: ±0.1cent")

        QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel

    def Set_Analysis_Language(self):

        QLabel_Info = {
            "Title_Name": "语种检测"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        QLabel_Content_Evaluation_Class = QtWidgets.QLabel()
        QLabel_Content_Evaluation_Class.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Content_Evaluation_Class.setAlignment(QtCore.Qt.AlignCenter)

        QLabel_Content_Evaluation_Class.setText(
            f"""<div style="text-align:center;">
                  <span style="font-size:18pt;color:{Page_Info["StyleSheet"]["Value_Color"]};font-weight: bold">英语</span><br>
                  <span style="font-size:8pt;color:#1E90FF;">所属语言</span>
                  </div>"""
        )

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("*匹配率 88%")

        QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel


    def Set_Analysis_Scene(self):

        QLabel_Info = {
            "Title_Name": "素材场景分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                          QLabel {
                            background-color: rgba(40, 52, 80, 0.3);;
                              border: 1px solid rgba(0, 180, 255, 60);
                              border-radius: 4px;

                              color:rgba(0, 255, 136, 255);

                          }
                          QLabel:hover {
                              background-color: rgba(0, 100, 150, 150);
                          }
                      """

        QLabel_Left = QtWidgets.QLabel()
        QLabel_Left.setMinimumHeight(60)
        QLabel_Left.setMaximumHeight(60)
        QLabel_Left.setStyleSheet(StyleSheet_QLabel)
        QLabel_Left.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Left.setText('<div style="text-align:center;">'
                            '<span style="font-size:13pt;font-weight: bold">茶楼</span><br>'
                            '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">主场景</span>'
                            '</div>')

        QLabel_Right = QtWidgets.QLabel()
        QLabel_Right.setMinimumHeight(60)
        QLabel_Right.setMaximumHeight(60)
        QLabel_Right.setStyleSheet(StyleSheet_QLabel)
        QLabel_Right.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Right.setText('<div style="text-align:center;">'
                             '<span style="font-size:13pt;font-weight: bold">商场</span><br>'
                             '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">广义场景</span>'
                             '</div>')

        QHBoxLayout_Content.addWidget(QLabel_Left)
        QHBoxLayout_Content.addWidget(QLabel_Right)




        return __QLabel


    def Set_Analysis_Sex(self):

        QLabel_Info = {
            "Title_Name": "说话人性别"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                          QLabel {
                            background-color: rgba(40, 52, 80, 0.3);;
                              border: 1px solid rgba(0, 180, 255, 60);
                              border-radius: 4px;

                              color:rgba(0, 255, 136, 255);

                          }
                          QLabel:hover {
                              background-color: rgba(0, 100, 150, 150);
                          }
                      """



        QLabel_Left = QtWidgets.QLabel()
        QLabel_Left.setMinimumHeight(60)
        QLabel_Left.setMaximumHeight(60)
        QLabel_Left.setStyleSheet(StyleSheet_QLabel)
        QLabel_Left.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Left.setText('<div style="text-align:center;">'
                            '<span style="font-size:13pt;font-weight: bold">男</span><br>'
                            '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">主体说话人</span>'
                            '</div>')

        QLabel_Right = QtWidgets.QLabel()
        QLabel_Right.setMinimumHeight(60)
        QLabel_Right.setMaximumHeight(60)
        QLabel_Right.setStyleSheet(StyleSheet_QLabel)
        QLabel_Right.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Right.setText('<div style="text-align:center;">'
                             '<span style="font-size:13pt;font-weight: bold">男</span><br>'
                             '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">其他说话人</span>'
                             '</div>')

        QHBoxLayout_Content.addWidget(QLabel_Left)
        QHBoxLayout_Content.addWidget(QLabel_Right)



        return __QLabel



    def Set_File_Management(self):
        QLabel_Info = {
            "Title_Name": "文件管理"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 0, 8, 0)

        StyleSheet_QPushButton = """
                               QPushButton {
                                   background-color: rgba(0, 50, 80, 120);
                                   border: 1px solid rgba(0, 180, 255, 60);
                                   border-radius: 4px;
                                   padding: 3px;
                                   color:#1E90FF;

                               }
                               QPushButton:hover {
                                   background-color: rgba(0, 100, 150, 150);
                               }
                           """
        QPushButton_Load = QtWidgets.QPushButton("加载素材")
        QPushButton_Load.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Load.clicked.connect(self.open_file)

        QPushButton_Save = QtWidgets.QPushButton("保持分析")
        QPushButton_Save.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Save.clicked.connect(self.start_playback)

        QPushButton_Clear = QtWidgets.QPushButton("清除数据")
        QPushButton_Clear.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Clear.clicked.connect(self.stop_playback)

        QVBoxLayout_Content.addWidget(QPushButton_Load)
        QVBoxLayout_Content.addWidget(QPushButton_Save)
        QVBoxLayout_Content.addWidget(QPushButton_Clear)

        return __QLabel

    def Set_Audio_Processing(self):
        QLabel_Info = {
            "Title_Name": "素材处理"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 0, 8, 0)

        StyleSheet_QPushButton = """
                               QPushButton {
                                   background-color: rgba(0, 50, 80, 120);
                                   border: 1px solid rgba(0, 180, 255, 60);
                                   border-radius: 4px;
                                   padding: 3px;
                                   color:#1E90FF;

                               }
                               QPushButton:hover {
                                   background-color: rgba(0, 100, 150, 150);
                               }
                           """
        QPushButton_Load = QtWidgets.QPushButton("初始化")
        QPushButton_Load.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Load.clicked.connect(self.start_playback)

        QPushButton_Save = QtWidgets.QPushButton("压缩")
        QPushButton_Save.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Save.clicked.connect(self.start_playback)

        QPushButton_Clear = QtWidgets.QPushButton("增强")
        QPushButton_Clear.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Clear.clicked.connect(self.stop_playback)

        QVBoxLayout_Content.addWidget(QPushButton_Load)
        QVBoxLayout_Content.addWidget(QPushButton_Save)
        QVBoxLayout_Content.addWidget(QPushButton_Clear)

        return __QLabel

    def Set_Data_Export(self):
        QLabel_Info = {
            "Title_Name": "导出数据"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 0, 8, 0)

        StyleSheet_QPushButton = """
                       QPushButton {
                           background-color: rgba(0, 50, 80, 120);
                           border: 1px solid rgba(0, 180, 255, 60);
                           border-radius: 4px;
                           padding: 3px;
                           color:#1E90FF;

                       }
                       QPushButton:hover {
                           background-color: rgba(0, 100, 150, 150);
                       }
                   """
        QPushButton_Load = QtWidgets.QPushButton("导出CSV")
        QPushButton_Load.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Load.clicked.connect(self.open_file)

        QPushButton_Save = QtWidgets.QPushButton("导出Json")
        QPushButton_Save.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Save.clicked.connect(self.start_playback)

        QPushButton_Clear = QtWidgets.QPushButton("生成报告")
        QPushButton_Clear.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Clear.clicked.connect(self.stop_playback)

        QVBoxLayout_Content.addWidget(QPushButton_Load)
        QVBoxLayout_Content.addWidget(QPushButton_Save)
        QVBoxLayout_Content.addWidget(QPushButton_Clear)


        return __QLabel


    def Set_Analysis_AI(self):

        QLabel_Info = {
            "Title_Name": "AI分析"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8,0,8, 0)


        StyleSheet_QPushButton="""
                QPushButton {
                    background-color: rgba(0, 50, 80, 120);
                    border: 1px solid rgba(0, 180, 255, 60);
                    border-radius: 4px;
                    padding: 3px;
                    color:#1E90FF;

                }
                QPushButton:hover {
                    background-color: rgba(0, 100, 150, 150);
                }
            """

        QPushButton_Load = QtWidgets.QPushButton("语音识别")
        QPushButton_Load.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Load.clicked.connect(self.open_file)

        QPushButton_Save = QtWidgets.QPushButton("语义识别")
        QPushButton_Save.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Save.clicked.connect(self.start_playback)

        QPushButton_Clear = QtWidgets.QPushButton("语音增强")
        QPushButton_Clear.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Clear.clicked.connect(self.stop_playback)

        QVBoxLayout_Content.addWidget(QPushButton_Load)
        QVBoxLayout_Content.addWidget(QPushButton_Save)
        QVBoxLayout_Content.addWidget(QPushButton_Clear)
        # QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        # QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel


        #
        # QVBoxLayout_Content = QtWidgets.QVBoxLayout(QLabel_Content)
        # QVBoxLayout_Content.setSpacing(1)
        # QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        #
        #
        # QPushButton_Load = QtWidgets.QPushButton("语音识别")
        # QPushButton_Load.setStyleSheet(StyleSheet_QPushButton)
        # QPushButton_Load.clicked.connect(self.open_file)
        #
        # QPushButton_Save = QtWidgets.QPushButton("语义识别")
        # QPushButton_Save.setStyleSheet(StyleSheet_QPushButton)
        # QPushButton_Save.clicked.connect(self.start_playback)
        #
        #
        # QPushButton_Clear= QtWidgets.QPushButton("语音增强")
        # QPushButton_Clear.setStyleSheet(StyleSheet_QPushButton)
        # QPushButton_Clear.clicked.connect(self.stop_playback)
        #
        #
        #
        # QVBoxLayout_Content.addWidget(QPushButton_Load )
        # QVBoxLayout_Content.addWidget(QPushButton_Save )
        # QVBoxLayout_Content.addWidget(QPushButton_Clear )


        # self.waveform_widget = AudioVisualizationWidget(800, 150, "waveform")
        # # self.waveform = WaveformVisualizer()
        # QHBoxLayout_Content.addWidget(self.waveform_widget)
        #
        # __QVBoxLayout.addWidget(QLabel_Title, )
        # __QVBoxLayout.addWidget(QLabel_Content, )
        #
        # return QLabel_Analysis_AI

    def Set_Material_Evaluation(self):
        QLabel_Info={
            "Title_Name":"素材评分"
        }
        __QLabel  = QLabel_Function(QLabel_Info)


        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(1)
        QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        QLabel_Content_Evaluation_Class = QtWidgets.QLabel()
        QLabel_Content_Evaluation_Class.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Content_Evaluation_Class.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Content_Evaluation_Class.setText(
                '<div style="text-align:center;">'
                '<span style="font-size:18pt;color:cyan;font-weight: bold">A+</span><br>'
                '<span style="font-size:8pt;color:#1E90FF;">素材评分</span>'
                '</div>'
            )

        QLabel_Content_Status = QtWidgets.QLabel()
        QLabel_Content_Status.setMinimumHeight(18)
        QLabel_Content_Status.setMaximumHeight(18)
        QLabel_Content_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content_Status.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Content_Status.setStyleSheet("color:#1E90FF;")
        QLabel_Content_Status.setText("*得分：92/100")

        QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        QVBoxLayout_Content.addWidget(QLabel_Content_Status)

        return __QLabel

    def Set_Computing_Monitoring(self):

        QLabel_Info = {
            "Title_Name": "算力监测"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                                 QLabel {
                                   background-color: rgba(40, 52, 80, 0.3);;
                                     border: 1px solid rgba(0, 180, 255, 60);
                                     border-radius: 4px;

                                     color:rgba(0, 255, 136, 255);

                                 }
                                 QLabel:hover {
                                     background-color: rgba(0, 100, 150, 150);
                                 }
                             """

        QLabel_Left = QtWidgets.QLabel()
        QLabel_Left.setMinimumHeight(60)
        QLabel_Left.setMaximumHeight(60)
        QLabel_Left.setStyleSheet(StyleSheet_QLabel)
        QLabel_Left.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Left.setText('<div style="text-align:center;">'
                            '<span style="font-size:13pt;font-weight: bold">78%</span><br>'
                            '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">算例占比</span>'
                            '</div>')

        QLabel_Right = QtWidgets.QLabel()
        QLabel_Right.setMinimumHeight(60)
        QLabel_Right.setMaximumHeight(60)
        QLabel_Right.setStyleSheet(StyleSheet_QLabel)
        QLabel_Right.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Right.setText('<div style="text-align:center;">'
                             '<span style="font-size:13pt;font-weight: bold">运行</span><br>'
                             '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">模型状态</span>'
                             '</div>')

        QHBoxLayout_Content.addWidget(QLabel_Left)
        QHBoxLayout_Content.addWidget(QLabel_Right)

        return __QLabel



    def Set_Bottom(self):
        pass



    def open_file(self):
        file_dialog = QtWidgets.QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.wav *.mp3 *.ogg *.flac *.aac *.m4a)"
        )

        if file_path:
            self.audio_file = file_path
            self.stop_playback()

    def start_playback(self):

        # 模拟数据更新
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_visualizations)
        self.timer.start(30)

        if self.audio_file:
            self.stop_playback()
            self.audio_analyzer = AudioAnalyzer(self.audio_file)
            self.audio_analyzer.update_signal.connect(self.update_audio_buffer)
            self.audio_analyzer.update_signal_33.connect(self.update_audio_buffer_33)
            self.audio_analyzer.finished_signal.connect(self.stop_playback)
            self.audio_analyzer.start()

    def stop_playback(self):
        if self.audio_analyzer:
            self.audio_analyzer.stop()
            self.audio_analyzer = None
        self.audio_buffer = np.zeros(1024, dtype=np.float32)
        self.update_visualizations()
    def update_audio_buffer(self, data):
        self.audio_buffer = data


    def update_audio_buffer_33(self,data):
        # 将数据传递给波形图组件
        if hasattr(self, '__AudioWaveformWidget'):
            self.__AudioWaveformWidget.setValues(data)

    def update_visualizations(self):
        # 生成随机数据用于演示
        # waveform_data = [random.random() * 2 - 1 for _ in range(100)]
        # spectrum_data = [random.random() for _ in range(32)]
        self.QWidget_Waveform.update_data(self.audio_buffer)
        # self.QWidget_Spectrogram.update_data(self.audio_buffer)


    def PAGE_HANDLER_EXECUTE(self, CommandParameter):



        if "Self_" in CommandParameter["Type"]:
            PP(CommandParameter)
            self.Open_Processing(CommandParameter)

        else:

            self.Signal_Result.emit(CommandParameter)  # 发送信号并传递数据


    def Open_Processing(self,ProcessingParameter):
        PP(ProcessingParameter)
        # {"Command": "Media_PlayerConfig", "Channel_ID": self.Channel_Info["Channel_ID"]})

        # PopupDialog = PopupParameter["Command"]


        # __Component_Processing_EQ  =Component_Processing_EQ.Component_Processing_EQ()
        # __Component_Processing_EQ.show()
        # Dialog_Type, Dialog_Name = (f"_{Parts[0]}", Parts[1]) if (Parts := PopupDialog.split("_")) and len(Parts) > 1 else ("", PopupDialog)
        exec(f"QLabel_Component= Component_{ProcessingParameter['Processing']}.Component_{ProcessingParameter['Processing']}(self,ProcessingParameter)")
        exec("QLabel_Component.Signal_Result.connect(self.PAGE_HANDLER_EXECUTE)")
        exec("QLabel_Component.move(self.geometry().center() - QLabel_Component.rect().center())")
        exec("QLabel_Component.show()")







class AudioVisualizationWidget(QtWidgets.QWidget):
    def __init__(self, width=600, height=150, type="waveform"):
        super().__init__()
        # self.setFixedSize(width, height)
        self.type = type
        self.data = np.zeros(1024, dtype=np.float32)

        # self.label = QtWidgets.QLabel("波形图" if type == "waveform" else "语谱图", self)
        # self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 0.5);")
        # self.label.setGeometry(0, 0, width, 20)

    def update_data(self, new_data):
        if isinstance(new_data, np.ndarray):
            self.data = new_data.astype(np.float32)
        else:
            self.data = np.array(new_data, dtype=np.float32)
        self.update()

    def paintEvent(self, event):
        try:
            painter = QtGui.QPainter(self)
            painter.fillRect(self.rect(), Qt.black)

            width = self.width()
            height = self.height()

            if self.type == "waveform":
                if len(self.data) == 0:
                    return

                pen = QtGui.QPen(QtGui.QColor(0, 255, 255, 200))
                pen.setWidth(2)
                painter.setPen(pen)

                norm_data = self.data / (np.max(np.abs(self.data)) + 1e-6)

                slice_width = width / len(norm_data)
                x = 0

                prev_x, prev_y = 0, 0
                for i, value in enumerate(norm_data):
                    curr_x = int(x)
                    curr_y = int(height / 2 - value * height / 2)

                    if i > 0:
                        painter.drawLine(prev_x, prev_y, curr_x, curr_y)

                    prev_x, prev_y = curr_x, curr_y
                    x += slice_width

            elif self.type == "spectrogram":
                if len(self.data) == 0:
                    return

                window = np.hanning(len(self.data))
                windowed_data = self.data * window
                fft_data = np.abs(np.fft.rfft(windowed_data))
                fft_data = np.log10(fft_data + 1e-6)

                if np.max(fft_data) > 0:
                    fft_data = (fft_data - np.min(fft_data)) / (np.max(fft_data) - np.min(fft_data))

                max_bars = 100
                bar_count = min(len(fft_data), max_bars)
                bar_width = max(1, int(width / bar_count * 2.5))

                x = 0
                for i in range(bar_count):
                    value = fft_data[i]
                    bar_height = int(min(max(value, 0), 0.8) * height * 0.5)
                    color_value = min(int(value * 155) + 100, 255)
                    # color = QtGui.QColor(color_value, 50, 50, 150)
                    color = QtGui.QColor(0, 0, 150, 155)
                    # QColor(20, 20, 30)
                    painter.fillRect(
                        int(x),
                        int(height - bar_height),
                        int(bar_width),
                        int(bar_height),
                        color
                    )
                    x += bar_width + 1

        except Exception as e:
            print(f"绘图错误: {e}")




class AudioEqualizerBar(QtWidgets.QWidget):
    def __init__(self, bars, steps):
        super().__init__()

        self.setSizePolicy(QtWidgets.QSizePolicy.Policy.MinimumExpanding, QtWidgets.QSizePolicy.Policy.MinimumExpanding)

        if isinstance(steps, list):
            # list of colours.
            self.n_steps = len(steps)
            self.steps = steps

        elif isinstance(steps, int):
            # int number of bars, defaults to red.
            self.n_steps = steps
            self.steps = ["red"] * steps

        else:
            raise TypeError("steps must be a list or int")

        # Bar appearance.
        self.n_bars = bars
        self._x_solid_percent = 0.8
        self._y_solid_percent = 0.8
        self._background_color = QtGui.QColor("black")
        self._padding = 25  # n-pixel gap around edge.

        # Bar behaviour
        self._timer = None
        self.setDecayFrequencyMs(100)
        self._decay = 10

        # Ranges
        self._vmin = 0
        self._vmax = 100

        # Current values are stored in a list.
        self._values = [0.0] * bars

    def paintEvent(self, e):
        painter = QtGui.QPainter(self)

        brush = QtGui.QBrush()
        brush.setColor(self._background_color)
        brush.setStyle(QtCore.Qt.BrushStyle.SolidPattern)
        rect = QtCore.QRect(0, 0, painter.device().width(), painter.device().height())
        painter.fillRect(rect, brush)

        # Define our canvas.
        d_height = painter.device().height() - (self._padding * 2)
        d_width = painter.device().width() - (self._padding * 2)

        # Draw the bars.
        step_y = d_height / self.n_steps
        bar_height = step_y * self._y_solid_percent
        bar_height_space = step_y * (1 - self._x_solid_percent) / 2

        step_x = d_width / self.n_bars
        bar_width = step_x * self._x_solid_percent
        bar_width_space = step_x * (1 - self._y_solid_percent) / 2

        for b in range(self.n_bars):
            # Calculate the y-stop position for this bar, from the value in range.
            pc = (self._values[b] - self._vmin) / (self._vmax - self._vmin)
            n_steps_to_draw = int(pc * self.n_steps)

            for n in range(n_steps_to_draw):
                brush.setColor(QtGui.QColor(self.steps[n]))
                rect = QtCore.QRectF(
                    self._padding + (step_x * b) + bar_width_space,
                    self._padding + d_height - ((1 + n) * step_y) + bar_height_space,
                    bar_width,
                    bar_height,
                )
                painter.fillRect(rect, brush)

        painter.end()

    def sizeHint(self):
        return QtCore.QSize(20, 120)

    def _trigger_refresh(self):
        self.update()

    def setDecay(self, f):
        self._decay = float(f)

    def setDecayFrequencyMs(self, ms):
        if self._timer:
            self._timer.stop()

        if ms:
            self._timer = QtCore.QTimer()
            self._timer.setInterval(ms)
            self._timer.timeout.connect(self._decay_beat)
            self._timer.start()

    def _decay_beat(self):
        self._values = [max(0, v - self._decay) for v in self._values]
        self.update()  # Redraw new position.

    def setValues(self, v):
        # PJ(v)
        self._values = v
        self.update()

    def values(self):
        return self._values

    def setRange(self, vmin, vmax):
        assert float(vmin) < float(vmax)
        self._vmin, self._vmax = float(vmin), float(vmax)

    def setColor(self, color):
        self.steps = [color] * self._bar.n_steps
        self.update()

    def setColors(self, colors):
        self.n_steps = len(colors)
        self.steps = colors
        self.update()

    def setBarPadding(self, i):
        self._padding = int(i)
        self.update()

    def setBarSolidXPercent(self, f):
        self._x_solid_percent = float(f)
        self.update()

    def setBarSolidYPercent(self, f):
        self._y_solid_percent = float(f)
        self.update()

    def setBackgroundColor(self, color):
        self._background_color = QtGui.QColor(color)
        self.update()



class QLabel_Function(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:self.QLabel_Info = args[0]
        except:pass

        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        # QLabel_Icon.setGeometry(0,0,18, 18)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        # Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # QLabel_Icon.setPixmap(Image_Logo)
        Icon_List = {
            "音频振幅分析": "amplitude.png", "实时波形分析": "waveform.png", "素材增益": "material.png",
            "音频EQ调试": "EQ.png", "音频噪音调试": "noise_control.png", "音频播放控制": "playpause.png",
            "语音转译": "translate.png", "噪音分析": "noise_analysis.png", "关联性分析": "relevance.png",
            "说话人检测": "speaker.png", "语种检测": "language.png", "素材场景分析": "scenario.png",
            "说话人性别": "gender.png", "文件管理": "file.png", "素材处理": "material_processing.png",
            "导出数据": "export.png", "AI分析": "AI.png", "素材评分": "score.png", "算力监测": "hashrate.png"
        }

        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.QLabel_Info.get("Title_Name", "")
        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)

        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")



        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        QLabel_Title_Name.setText(self.QLabel_Info["Title_Name"])

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")


        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)








class AudioAnalyzer(QtCore.QThread):
    update_signal = QtCore.Signal(np.ndarray)
    update_signal_33 = QtCore.Signal(np.ndarray)
    finished_signal = QtCore.Signal()

    def __init__(self, file_path, sample_rate=44100, chunk_size=1024):
        super().__init__()
        self.file_path = file_path
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.running = False

    def run(self):
        self.running = True
        try:
            # 使用librosa加载音频文件
            y, sr = librosa.load(self.file_path, sr=self.sample_rate, mono=True)

            # 分块处理音频数据
            for i in range(0, len(y), self.chunk_size):
                if not self.running:
                    break

                chunk = y[i:i + self.chunk_size]
                if len(chunk) < self.chunk_size:
                    chunk = np.pad(chunk, (0, self.chunk_size - len(chunk)))




                # 计算音频块的能量（或其他特征）
                energy = np.abs(np.fft.fft(chunk))[:self.chunk_size // 2]
                energy = np.log1p(energy)  # 对数变换，增强动态范围
                energy = (energy / np.max(energy)) * 100  # 归一化到0-100

                self.update_signal_33.emit(energy)

                self.update_signal.emit(chunk)
                QtCore.QThread.msleep(int(self.chunk_size / self.sample_rate * 1000))

            self.finished_signal.emit()

        except Exception as e:
            print(f"音频分析错误: {e}")

    def stop(self):
        self.running = False
        self.wait()



class AudioAnalyzer_2(QtCore.QThread):
    update_signal = QtCore.Signal(np.ndarray)
    finished_signal = QtCore.Signal()

    def __init__(self, file_path, sample_rate=44100, chunk_size=1024):
        super().__init__()
        self.file_path = file_path
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.running = False

    def run(self):
        self.running = True
        try:
            # 使用librosa加载音频文件
            y, sr = librosa.load(self.file_path, sr=self.sample_rate, mono=True)

            # 分块处理音频数据
            for i in range(0, len(y), self.chunk_size):
                if not self.running:
                    break

                chunk = y[i:i + self.chunk_size]
                if len(chunk) < self.chunk_size:
                    chunk = np.pad(chunk, (0, self.chunk_size - len(chunk)))

                # 计算音频块的能量（或其他特征）
                energy = np.abs(np.fft.fft(chunk))[:self.chunk_size // 2]
                energy = np.log1p(energy)  # 对数变换，增强动态范围
                energy = (energy / np.max(energy)) * 100  # 归一化到0-100

                self.update_signal.emit(energy)
                QtCore.QThread.msleep(int(self.chunk_size / self.sample_rate * 1000))

            self.finished_signal.emit()

        except Exception as e:
            print(f"音频分析错误: {e}")

    def stop(self):
        self.running = False
        self.wait()















# class WaveformVisualizer(SciFiPanel):
#     def __init__(self, parent=None):
#         super().__init__("波形分析", parent)
#         self.values = [0] * 100
#
#     def update_values(self, new_values):
#         self.values = new_values[-100:]
#         self.update()
#
#     def paintEvent(self, event):
#         super().paintEvent(event)
#         painter = QtGui.QPainter(self)
#         painter.setRenderHint(QtGui.QPainter.Antialiasing)
#
#         if not self.values:
#             return
#
#         # 绘制波形
#         path = QtGui.QPainterPath()
#         width = self.width()
#         height = self.height()
#         step = width / len(self.values)
#
#         path.moveTo(0, height / 2)
#         for i, value in enumerate(self.values):
#             x = i * step
#             y = height / 2 - value * (height / 2) * 0.8
#             path.lineTo(x, y)
#
#         # 波形渐变
#         gradient = QtGui.QLinearGradient(0, 0, 0, height)
#         gradient.setColorAt(0, QColor(0, 255, 255, 200))
#         gradient.setColorAt(1, QColor(0, 180, 255, 150))
#
#         pen = QtGui.QPen(gradient, 2)
#         painter.setPen(pen)
#         painter.drawPath(path)
#
#         # 绘制峰值指示器
#         peak = max(self.values) if self.values else 0
#         if peak > 0.1:
#             peak_y = height / 2 - peak * (height / 2) * 0.8
#             peak_pen = QtGui.QPen(QColor(255, 50, 50, 200), 1)
#             painter.setPen(peak_pen)
#             painter.drawLine(0, peak_y, width, peak_y)



class Slider_Custom(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        # 创建 QVBoxLayout
        layout = QtWidgets.QVBoxLayout(self)

        # 创建 QSlider
        self.slider = QtWidgets.QSlider(Qt.Vertical)  # 设置为垂直方向
        self.slider.setMinimum(0)  # 最小值
        self.slider.setMaximum(10)  # 最大值
        self.slider.setValue(5)  # 初始值
        self.slider.setTickPosition(QtWidgets.QSlider.TicksBothSides)  # 显示刻度
        self.slider.setTickInterval(1)  # 刻度间隔
        self.slider.valueChanged.connect(self.on_value_changed)  # 连接信号槽

        # 创建 QLabel 用于显示当前值
        # self.label = QtWidgets.QLabel("50", self)
        # self.label.setAlignment(Qt.AlignCenter)

        # 将 QSlider 和 QLabel 添加到布局
        layout.addWidget(self.slider)
        # layout.addWidget(self.label)

        # 设置窗口属性
        # self.setWindowTitle("Frequency Control")
        # self.resize(100, 400)

    def on_value_changed(self, value):
        # 根据 QSlider 的值更新 QLabel 的文本
        # self.label.setText(str(value))
        self.update()  # 触发重绘

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # # 绘制刻度和数字
        # painter.setPen(QtGui.QPen(Qt.white, 1))
        # font = QtGui.QFont()
        # font.setPointSize(8)
        # painter.setFont(font)
        #
        # slider_rect = self.slider.geometry()
        # slider_height = slider_rect.height()
        # slider_width = slider_rect.width()
        # tick_interval = self.slider.tickInterval()
        # tick_count = (self.slider.maximum() - self.slider.minimum()) // tick_interval + 1
        #
        # for i in range(tick_count):
        #     value = self.slider.minimum() + i * tick_interval
        #     y = slider_height - (value - self.slider.minimum()) * (slider_height / (self.slider.maximum() - self.slider.minimum()))
        #     painter.drawLine(slider_width + 0.5, y, slider_width + 1, y)  # 绘制刻度线
        #     painter.drawText(slider_width + 1.5, y + 0.5, str(value))  # 绘制数字




class Page_Mission_Execute(QtCore.QThread):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.CONFIG()
        self.Type = args[0]

    def run(self):
        # PP("Page_Update_Status",9)
        try:
            Method = getattr(self, f'{self.Type}', None)
            if Method and callable(Method):
                return Method()
            else:
                return f"{self.__class__.__name__} No Such Method: {self.Type}"
        except Exception as e:
            self.Signal_Result.emit({"Error": e})

    def Worker_Info(self):
        try:
            self.CacheStack_Data = {}
            self.CacheStack_Data['Method'] = "Key_Get"
            self.CacheStack_Data['Data'] = ["Worker_Info"]
            Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
            Worker_Info = json.loads(Response.text)
            # PJ(Worker_Info)
            self.Signal_Result.emit({"Worker_Info": Worker_Info})
        except:
            PT()
            self.Signal_Result.emit({"Worker_Info": "Failed"})




        pass

    def Mission_Update_Status(self):
        PP("Mission_Update_Status", 3)
        # PJ(CServer())

        # Server_Status_Info  =
        self.Result["Server_Status_Info"]    = CServer()
        self.Result["Status"]                = "Success"
        self.Result["Command"]               = "Page_Update"
        self.Signal_Result.emit(self.Result)
        # return self.Result




    def CONFIG(self):
        self.Result = {'Status': 'Failed'}








            # Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            # # PI(eval(Response.text))
            # Sentinel_Service = eval(Response.text)
            # if Sentinel_Service["Status"] == "Active":
            #     Page_Info["Sentinel_Service"] = "Success"

        #
        # try:
        #     self.CacheStack_Data = {}
        #     self.CacheStack_Data['Method'] = "Key_Get"
        #     self.CacheStack_Data['Data'] = ["System_Status"]
        #     Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
        #     System_Status = json.loads(Response.text)
        #     PJ(System_Status)
        #     self.Signal_Result.emit({"System_Status": System_Status})
        # except:
        #     PT()
        #     self.Signal_Result.emit({"System_Status": "Failed"})



            # Page_Info["Sentinel_Service"] = "Failed"






        # global Page_Info
        # while True:
        #     time.sleep(10)
        #     PP("Core Update")

            # self.Proxies =
            # Response
            # try:
            #     Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            #     # PI(eval(Response.text))
            #     Sentinel_Service = eval(Response.text)
            #     if Sentinel_Service["Status"] == "Active":
            #         Page_Info["Sentinel_Service"] = "Success"
            # except:
            #     Page_Info["Sentinel_Service"] = "Failed"

            # System_Status =  SCKeYGet({"Key":"System_Status"})
            # PJ(System_Status)
            # try:
            #     pass
            #     # Page_Info["System_Status"] = SCKey_Get("System_Status")
            # except:
            #     PT()

            # System_Status









class AudioWaveformWidget(QtWidgets.QWidget):
    """
    音频波形图组件，类似TypeScript版本的波形图效果
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(120)
        self.setMaximumHeight(120)

        # 音频数据
        self._values = np.zeros(512, dtype=np.float32)  # 存储音频数据
        self._display_values = np.zeros(300, dtype=np.float32)  # 用于显示的数据点

        # 颜色设置
        self._line_color = QtGui.QColor(0, 255, 255, 200)  # 青色波形线
        self._background_color = QtGui.QColor(20, 30, 40, 255)  # 深色背景
        self._grid_color = QtGui.QColor(60, 80, 100, 100)  # 网格线颜色

        # 动画相关
        self._animation_offset = 0
        self._timer = QtCore.QTimer()
        self._timer.timeout.connect(self._update_animation)
        self._timer.start(50)  # 20fps更新

    def setValues(self, values):
        """设置音频数据值"""
        if isinstance(values, np.ndarray):
            # 将频谱数据转换为时域波形数据进行显示
            # 取前300个点用于显示
            display_count = min(300, len(values))
            self._values = values[:display_count].astype(np.float32)

            # 归一化到-1到1范围
            if np.max(np.abs(self._values)) > 0:
                self._values = self._values / np.max(np.abs(self._values))

            # 更新显示数据
            self._update_display_values()
            self.update()

    def _update_display_values(self):
        """更新用于显示的数据点"""
        # 将频谱数据转换为类似波形的显示效果
        # 使用正弦波调制来创建更自然的波形外观
        if len(self._values) > 0:
            # 插值到300个显示点
            x_old = np.linspace(0, 1, len(self._values))
            x_new = np.linspace(0, 1, 300)
            self._display_values = np.interp(x_new, x_old, self._values)

            # 添加一些波形特征
            for i in range(len(self._display_values)):
                # 添加轻微的正弦波调制
                wave_factor = 0.1 * np.sin(i * 0.1 + self._animation_offset)
                self._display_values[i] = self._display_values[i] * (1 + wave_factor)

    def _update_animation(self):
        """更新动画偏移"""
        self._animation_offset += 0.1
        if self._animation_offset > 2 * np.pi:
            self._animation_offset = 0

        # 重新计算显示值以产生动画效果
        if len(self._values) > 0:
            self._update_display_values()
            self.update()

    def paintEvent(self, event):
        """绘制波形图"""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        width = self.width()
        height = self.height()

        # 绘制背景
        painter.fillRect(0, 0, width, height, self._background_color)

        # 绘制网格线
        self._draw_grid(painter, width, height)

        # 绘制波形
        self._draw_waveform(painter, width, height)

        painter.end()

    def _draw_grid(self, painter, width, height):
        """绘制网格线"""
        painter.setPen(QtGui.QPen(self._grid_color, 1))

        # 水平网格线
        for i in range(1, 4):
            y = height * i / 4
            painter.drawLine(0, y, width, y)

        # 垂直网格线
        for i in range(1, 8):
            x = width * i / 8
            painter.drawLine(x, 0, x, height)

    def _draw_waveform(self, painter, width, height):
        """绘制波形线"""
        if len(self._display_values) == 0:
            return

        # 设置画笔
        pen = QtGui.QPen(self._line_color, 2)
        painter.setPen(pen)

        # 创建路径
        path = QtGui.QPainterPath()

        center_y = height / 2
        x_step = width / len(self._display_values)

        # 移动到起始点
        start_x = 0
        start_y = center_y - self._display_values[0] * (height * 0.4)
        path.moveTo(start_x, start_y)

        # 绘制平滑曲线
        for i in range(1, len(self._display_values)):
            x = i * x_step
            y = center_y - self._display_values[i] * (height * 0.4)  # 0.4是振幅缩放因子
            path.lineTo(x, y)

        # 绘制路径
        painter.drawPath(path)

        # 添加发光效果
        glow_pen = QtGui.QPen(QtGui.QColor(0, 255, 255, 80), 4)
        painter.setPen(glow_pen)
        painter.drawPath(path)


if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Analysis()
    Page_Widget.show()
    sys.exit(app.exec())
