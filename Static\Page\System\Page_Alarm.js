/****************************************初始化插件***************************************************/ 
$('.single-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: Boolean($(this).data('allow-clear')),
});

var Choice_Date_Content = 'Today'
var Choice_Date_List = []
var Alarm_Info_List = []

/**********************************初始化table***************************************************/
var $table = $('#alarm_table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 6,
        render: function (data, type, row) {
            if (row.ALERT_STATUS === 'Active') {
                return `
                    <button class="btn btn-outline-success btn-edit btn-sm m-1 px-2">处置</button>
                    <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2">复核</button>
                `;
            } else {
                return `
                    <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2">复核</button>
                `;
            }
        },
      },
    ],
    columns: [
      { title: '序号', data: 'ID', width: '50px' },
      { title: '创建时间', data: 'ALERT_TIME', width: '150px' },
      { title: '预警方式', data: 'ALERT_CLASS', width: '150px'},
      { title: '处置状态', data: 'ALERT_STATUS', width: '150px', render: function(data, type, row) {
        if (row.ALERT_STATUS === 'Active') {
            return `<button class="btn btn-warning btn-edit btn-sm m-1 px-2" >未读</button>`
        } else {
            return `<button class="btn btn-success btn-edit btn-sm m-1 px-2" >已读</button>`
        };
      }},
      { title: '预警标题', data: 'ALERT_TITLE' },
      { title: '预警内容', data: 'ALERT_CONTENT'},
    ],
    scrollY: 'calc(100vh - 320px)', // 设置表格固定高度
    searching:false,
});

/****************************************初始化请求数据***************************************************/ 
function Requests_Sentiment_Alarm_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_alarm_info',
        "data_argument": `{}`,
        "data_kwargs": {
            'Choice_Date_Content':Choice_Date_Content,
            'Choice_Date_List':Choice_Date_List,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                $table.clear();
                $table.rows.add(Result.Alert_List);
                $table.draw();
                Alarm_Info_List = Result.Alert_List;
                Loading_Hide();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            }
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
};


$('#Sentiment_Alarm_Time_Element').on('change',function() {
    Choice_Date_Content = this.value;
    console.log('当前筛选的报警请求时间:', Choice_Date_Content);
    if (this.value === 'Other') {
        console.log('打开日期选择框')
    } else {
        console.log('隐藏日期选择框')
    };
});

/****************************************重置***************************************************/ 
function Init_Show_Table_Params() {
    document.getElementById('Sentiment_Alert_Title_Content_Element').innerText = '';
    $('#Sentiment_Alert_Class_Element').val('All').trigger('change');
    $('#Sentiment_Alert_Status_Element').val('All').trigger('change');
};
/****************************************条件查询***************************************************/ 
function Chioce_Cheak_Table() {
    let Alert_Class_Info = $('#Sentiment_Alert_Class_Element').val();
    let Alert_Status_Info = $('#Sentiment_Alert_Status_Element').val();
    let Alert_Title_Content_Info = $('#Sentiment_Alert_Title_Content_Element').val();
    if (Alert_Class_Info === 'All' && Alert_Status_Info === 'All' && Alert_Title_Content_Info.length === 0) {
        //
    } else {
        Alarm_Info_List = Alarm_Info_List.filter(Item => {
            if (Alert_Class_Info === 'All') {
                //
            } else {
                if (Item.ALERT_CLASS.includes(Alert_Class_Info)) {
                    //
                } else {
                    return false;
                }
            };
            if (Alert_Status_Info === 'All') {
                //
            } else {
                if (Item.ALERT_STATUS === Alert_Status_Info) {
                    //
                } else {
                    return false;
                }
            };
            if (Alert_Title_Content_Info === '') {
                //
            } else {
                if ((Item.ALERT_TITLE+Item.ALERT_CONTENT).includes(Alert_Class_Info)) {
                    //
                } else {
                    return false;
                }
            };
        });
        $table.clear();
        $table.rows.add(Alarm_Info_List);
        $table.draw();
    };
};

