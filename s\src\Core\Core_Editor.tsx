import { createRoot } from "react-dom/client";
import { NodeE<PERSON>or, GetSchemes, ClassicPreset } from "rete";
import { AreaPlugin, AreaExtensions,Zoom } from "rete-area-plugin";
import React, { useEffect,useState,useRef } from 'react';
// import { SetContext } from '@/Core/Core_Context';
import { eventBus } from '@/Function/Function_nodeEventBus'; // 全局事件总线

import {
  BidirectFlow, //多联
  ConnectionPlugin,
  Presets as ConnectionPresets
} from "rete-connection-plugin";
import { ReactPlugin, Presets, ReactArea2D } from "rete-react-plugin";
import {
  AutoArrangePlugin,
  Presets as ArrangePresets
} from "rete-auto-arrange-plugin";
import { DataflowEngine } from "rete-engine";
import {

  ContextMenuExtra,
  ContextMenuPlugin,
  Presets as ContextMenuPresets
} from "rete-context-menu-plugin";
// import { ContextMenuPlugin, Presets as ContextMenuPresets } from 'rete-context-menu-plugin';
// import { ContextMenuContext } from 'rete-context-menu-plugin';

// ---------------------------------------------------------------------------------------- 自定义组件
 // @ts-ignore - 忽略下一行的类型检查
import { CustomSwitch,CustomSelect1,CustomNumberInput,CustomTextArea,CustomImage,CustomTextArea_Rumor,Preview_Score,} from "@/Component/Component_NodeFlow/MyButtonComponent";
import { NumberInputControl,SelectControl,TextareaControl,Textarea_Rumor_Control,Control_Preview_Score} from "@/Component/Component_NodeFlow/MyButtonComponent";
// import { TextAreaControl }  from "./TextareaControl";
// import { NumberInputControl} from "./MyButtonComponent";
 // @ts-ignore - 忽略下一行的类型检查
import { CustomNodeComponent } from "./CustomNode";


import { Node_Styles_System,Node_Styles_Object,Node_Styles_Model }from "@/Nodes/Node_Styles"
import { Node_System_Component_Start }from "@/Nodes/Node_System_Component_Start"
import { Node_System_Component_Object }from "@/Nodes/Node_System_Component_Object"
import { Node_System_Component_Output }from "@/Nodes/Node_System_Component_Output"
import { Node_AI_Component_DeepSeek }from "@/Nodes/Node_AI_DeepSeek/Node_AI_Component_DeepSeek"
import { Node_AI_DeepSeek, AIControl_DeepSeek}from "@/Nodes/Node_AI_DeepSeek/Node_AI_Control_DeepSeek"

import {Node_System_Component_Start_WeChat}from "@/Nodes/Node_System_Start_WeChat/Node_System_Component_Start_WeChat"
import {Node_System_Start_WeChat,SystemControl_Start_WeChat}from "@/Nodes/Node_System_Start_WeChat/Node_System_Control_Start_WeChat"




import { Node_DataSource_Component_Rumor }from "@/Nodes/Node_DataSource_Component_Rumor"
import { Node_DataSource_Component_Statistic }from "@/Nodes/Node_DataSource_Component_Statistic"
import { Node_Model_Component_Rumor }from "@/Nodes/Node_Model_Component_Rumor"
import { Node_Preview_Component_Rumor }from "@/Nodes/Node_Preview_Component_Rumor"
import { Node_System_Component_Status }from "@/Nodes/Node_System_Component_Status"
// import { Node_System_Component_Status }from "../Nodes/Node_System_Component_Status"


// import { Node_System_Component } from  "./Node_System";
//  // @ts-ignore - 忽略下一行的类型检查
// import { Node_Process_Component} from  "./Node_Process";
// import { Node_DataSource_Component } from "./Node_DataSource";
// import { Node_Analysis_Component } from "./Node_Analysis";
// import { Node_Model_Component } from "./Node_Model";
// import { Node_Template_Component } from "./Node_Template";

import {Node_System,Node_Process,Node_DataSource,
  Node_DataSource_Score,
  Node_Model,Node_Analysis,Node_Template,Node_Echart,Node_End,Node_Start,Node_Object,Node_Preview} from "@/Component/Component_NodeFlow/Nodes"
import{SelectOption} from "@/Component/Component_NodeFlow/Nodes"


// import{ButtonControl} from "@/Component/Component_NodeFlow/Node_System_Component_Start"
import{CustomButton,ProgressControl_Object,ButtonControl_Start, ButtonControl,ButtonControl_More,ButtonControl_Release,ImageControl_Face, DataSourceControl_Rumor,SystemControl_Object ,DataSourceControl_Statistic,
  ModelControl_Rumor,PreviewControl_Rumor,SystemControl_Output,SystemControl_Status,
  
} from "@/Nodes/Node_Controls"
import{Progress_Object,Button_Release,Button_More,Image_Face,Node_DataSource_Rumor,Node_System_Object,Node_DataSource_Statistic,Node_Preview_Rumor,Node_System_Start,
  Node_Model_Rumor,Node_System_Output,Node_System_Status,
} from "@/Nodes/Node_Controls"



import { CustomSocket } from "@/Nodes/CustomSocket";
import { CustomConnection } from "@/Nodes/CustomConnection";






import { start } from "repl";
import { Service_Requests } from "@/Core/Core_Control"
import CoreConfig from "@/Component/Component_NodeFlow/CoreConfig.json"




// ---------------------------------------------------------------------------------------- 配置数据类型
export interface Type_Json {[key: string]: string | any; }
// ---------------------------- 节点列表库
var CoreNodeTree: Type_Json = {}
CoreNodeTree = {"Nodes":{},"Connections":{}}
// ---------------------------- 节点类型列表
var Node_Type_List = CoreConfig.Node_Type_List


// ---------------------------- 课题方案
var WorkFolw_Schems: Type_Json = {}
WorkFolw_Schems={
  "Start":{},
  "Object":{},
  "DataSource":[],
  "Analysis":[],
  "Model":{},
  "AI":[],
  "Template":[],
  "End":[],
  "OnceSource":{},
  "OncePreview":{},
  "OnceOutput":{},
  "OnceModel":{},
  "Statistic":{},

}



// CoreNodeTree["Nodes"]["q"]={}
                           
// CoreNodeTree["Nodes"][String(a.id)]=  {"Class":"System","Type":"系统","Method":"Start",
//   "Explain":"工作流的启动控制",
//   "Status":"未启用",
//   "Schemes":"情报+AI测试",
// }


// Node_Type_List={
//   "Object":    {"Name":"课题", "Title":"【未定义】","Method":{},"Explain":"本工作流的目标功能",},
//   "System":    {"Name":"系统", "Title":"【未定义】","Method":{"Start":"启动分析"},"Explain":"工作流的启动控制",},
//   "Method":    {"Name":"业务", "Title":"【未定义】","Method":{},"Explain":"工作流具体实现业务",},
//   "DataSource":{"Name":"数源", "Title":"【未定义】","Method":{"Start":"国内数据"},"Explain":"开源数据",}

// }
// Node_Type_List =





const socket = new ClassicPreset.Socket("socket");

class NumberNode extends ClassicPreset.Node<
  {},
  { value: ClassicPreset.Socket },
  { value: ClassicPreset.InputControl<"number"> }
> {
  height = 120;
  width = 180;

  constructor(initial: number, change?: () => void) {
    super("Number");
    this.addControl(
      "value",
      new ClassicPreset.InputControl("number", { initial, change })
    );
    this.addOutput("value", new ClassicPreset.Output(socket, "Number"));
  }

  data(): { value: number } {
    return {
      value: this.controls.value.value || 0
    };
  }
}

class AddNode extends ClassicPreset.Node<
  { left: ClassicPreset.Socket; right: ClassicPreset.Socket },
  { value: ClassicPreset.Socket },
  { value: ClassicPreset.InputControl<"number"> }
> {
  height = 190;
  width = 180;

  constructor(
    change?: () => void,
    private update?: (control: ClassicPreset.InputControl<"number">) => void
  ) {
    super("Add");
    const left = new ClassicPreset.Input(socket, "Left");
    const right = new ClassicPreset.Input(socket, "Right");

    left.addControl(
      new ClassicPreset.InputControl("number", { initial: 0, change })
    );
    right.addControl(
      new ClassicPreset.InputControl("number", { initial: 0, change })
    );

    this.addInput("left", left);
    this.addInput("right", right);
    this.addControl(
      "value",
      new ClassicPreset.InputControl("number", {
        readonly: true
      })
    );
    this.addOutput("value", new ClassicPreset.Output(socket, "Number"));
  }

  data(inputs: { left?: number[]; right?: number[] }): { value: number } {
    const leftControl = this.inputs.left?.control as ClassicPreset.InputControl<
      "number"
    >;
    const rightControl = this.inputs.right
      ?.control as ClassicPreset.InputControl<"number">;

    const { left, right } = inputs;
    const value =
      (left ? left[0] : leftControl.value || 0) +
      (right ? right[0] : rightControl.value || 0);

    this.controls.value.setValue(value);

    if (this.update) this.update(this.controls.value);

    return { value };
  }
}




// ---------------------------------------------------------------------------------------- 配置变量

// class ButtonControl extends ClassicPreset.Control {
//   constructor(public label: string, public onClick: () => void) {
//     super();
//   }
// }

// class ProgressControl extends ClassicPreset.Control {
//   constructor(public percent: number) {
//     super();
//   }
// }








// export type SelectOption<T extends string | number> = {
//   value: T;
//   label: string;
// };

// export class SelectControl<T extends string | number = string | number> 
//   extends ClassicPreset.Control {
//   constructor(
//     public options: SelectOption<T>[],
//     public selected: T,
//     public onChange: (value: T) => void
//   ) {
//     super();
//   }
// }









class Connection<
  A extends Node,
  B extends Node
> extends ClassicPreset.Connection<A, B> {}

type Node = NumberNode | AddNode  ;
type ConnProps = Connection<NumberNode, AddNode> | Connection<AddNode, AddNode> ;







type Schemes = GetSchemes<
  Node | Node_Echart  | Node_Template | Node_Model ,
  ConnProps
>;
// type Schemes = GetSchemes<Node, ConnProps>;

type AreaExtra = ReactArea2D<any> | ContextMenuExtra;




export interface EditorApi {
  destroy: () => void;
  FunctionService: (config: Type_Json) => void;
}


export async function createEditor(container: HTMLElement) {
  const editor = new NodeEditor<Schemes>();
  const area = new AreaPlugin<Schemes, AreaExtra>(container);
  const connection = new ConnectionPlugin<Schemes, AreaExtra>();
  const render = new ReactPlugin<Schemes, AreaExtra>({ createRoot });
    // @ts-ignore - 忽略下一行的类型检查
  const arrange = new AutoArrangePlugin<Schemes>();
    // @ts-ignore - 忽略下一行的类型检查
  const engine = new DataflowEngine<Schemes>();
  // @ts-ignore - 忽略下一行的类型检查
  const zoom = new Zoom<Schemes>();

  connection.addPreset(() => new BidirectFlow());
  // 定义菜单项的正确方式
type MenuItem = {
  label: string;
  // handler: (context: ContextMenuContext<Schemes>) => void; // 使用 ContextMenuContext
};

// 示例配置
const items: MenuItem[] = [
  {
    label: "删除节点",
    // handler: ({ node, editor }) => {
    //   if (node && editor) {
    //     editor.removeNode(node.id);
    //   }
    // }
  }
];
 


  // // // const [age, setAge] = useState(user.age.toString());





  function process() {
    engine.reset();

    editor
      .getNodes()
      .filter((n) => n instanceof AddNode)
      .forEach((n) => engine.fetch(n.id));
  }


// ---------------------------------------------------------------------------------------- 设置自定义样式

render.addPreset(
  Presets.classic.setup({
    customize: {
       // @ts-ignore - 忽略下一行的类型检查
      connection(data) {
        return CustomConnection;
      },
      // @ts-ignore - 忽略下一行的类型检查
      control(data) {
        if (data.payload instanceof ButtonControl_Start) {
          return Node_System_Start;
        }
        if (data.payload instanceof ButtonControl_Release) {
          return Button_Release;
        }
        if (data.payload instanceof ButtonControl_More) {
          return Button_More;
        }
        if (data.payload instanceof ProgressControl_Object) {
          return Progress_Object;
        }
        if (data.payload instanceof SystemControl_Object) {
          return Node_System_Object;
        }
        if (data.payload instanceof DataSourceControl_Statistic) {
          return Node_DataSource_Statistic;
        }
        if (data.payload instanceof AIControl_DeepSeek) {
          return Node_AI_DeepSeek;
        }
        if (data.payload instanceof ImageControl_Face) {
          return Image_Face;
        }
        if (data.payload instanceof DataSourceControl_Rumor) {
          return Node_DataSource_Rumor;
        }
        if (data.payload instanceof PreviewControl_Rumor) {
          return Node_Preview_Rumor;
        }


        if (data.payload instanceof ModelControl_Rumor) {
          return Node_Model_Rumor;
        }

        if (data.payload instanceof SystemControl_Output) {
          return Node_System_Output;
        }
        

        if (data.payload instanceof SystemControl_Status) {
          return Node_System_Status;
        }
        if (data.payload instanceof SystemControl_Start_WeChat) {
          return Node_System_Start_WeChat;
        }

        
        
        // if (data.payload instanceof SwitchControl) {

        //   console.log('检测到SwitchControl实例');
        //   // return <CustomSwitch data={data.payload as SwitchControl} />;
        //   return CustomSwitch;
        // }
        if (data.payload instanceof SelectControl) {
          return CustomSelect1;
        }
        if (data.payload instanceof NumberInputControl) {
          return CustomNumberInput;
        }
        if (data.payload instanceof ClassicPreset.InputControl) {
          return Presets.classic.Control;
        }

        
        if (data.payload instanceof TextareaControl) {
          return CustomImage;
        }

        if (data.payload instanceof Textarea_Rumor_Control) {
          return CustomTextArea_Rumor;
        }
        if (data.payload instanceof Control_Preview_Score) {
          return Preview_Score;
        }
        // if (data.payload instanceof TextareaControl) {
        //   return CustomTextArea;

        //   // return data.payload.render();
        // }
        return null;
      },


      // @ts-ignore - 忽略下一行的类型检查
      node(data) {

        if (data.payload instanceof Node_System_Component_Start) {
          return Node_Styles_System;
        }

        if (data.payload instanceof Node_System_Component_Object) {
          return Node_Styles_Object;
        }

        if (data.payload instanceof Node_DataSource_Component_Rumor) {
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_DataSource_Component_Statistic) {
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Model_Component_Rumor) {
          return Node_Styles_Model;
        }
        if (data.payload instanceof Node_Preview_Component_Rumor) {
          return Node_Styles_Model;
        }

        if (data.payload instanceof Node_AI_Component_DeepSeek) {
          return Node_Styles_System;
        }

        if (data.payload instanceof Node_System_Component_Output) {
          return Node_Styles_System;
        }



        if (data.payload instanceof Node_System) {
          // return Node_Process_Component;
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Process) {
          // return Node_Process_Component;
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_DataSource) {
          // return Node_DataSource_Component;
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Analysis) {
          // return Node_Analysis_Component;
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Model) {
          // return Node_Model_Component;
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Template) {
          // return Node_Template_Component;
          return Node_Styles_System;
        }

        return null;

        
      },
      socket(data) {
        return CustomSocket;
      },
      // connection(data) {
      //   return CustomConnection;
      // }
      
    },
 
  })
);
connection.addPreset(ConnectionPresets.classic.setup());

// ---------------------------------------------------------------------------------------- 右键创建节点

const contextMenu = new ContextMenuPlugin<Schemes>({
    items: ContextMenuPresets.classic.setup([


  

      ["系统",[

        // @ts-ignore - 忽略下一行的类型检查
        ["拷贝", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["复制", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["删除", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["排列整理", () =>  {RightMenu_ReSet_Arrange();}],
        // @ts-ignore - 忽略下一行的类型检查
        ["清理缓存", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["设置课题", () =>  {RightMenu_System("");}],
      ]],

      ["模板", [
        // @ts-ignore - 忽略下一行的类型检查
        // ["突出谣言", () =>  {
        //   try {
        //     RightMenu_Model("Model_Rumor");
        //     return null;
        //   } catch (error) {
            
        //   }
         
        
        
        // }],

        ["突出谣言", () =>  {
          let NodeNew = new Node_Object("突谣")
          CoreConfig.Node_System_Function["ID"]    = String(NodeNew.id);
          RightMenu_Nade_Add(CoreConfig.Node_System_Function);
          RightMenu_Model("Model_Rumor");
          return NodeNew
        }],

        ["情报汇总", () =>  new Node_Template("情报汇总")],
        ["行动预判", () =>  new Node_Template("行动预判")],
        ["课题研判", () =>  new Node_Template("目标动向")],
        ["线索发现", () =>  new Node_Template("线索发现")],
        ["目标动向", () =>  new Node_Template("目标动向")],
      
      
      ]],
                  // Node_Preview_Score
      // -------------------------------------- 右键创建节点
      ["流程",[
        // @ts-ignore - 忽略下一行的类型检查
        ["开始", () => {

          let nodeNew = new Node_System_Component_Start_WeChat("【系统-开始】")
          // let nodeNew = new Node_System_Component_Start("【系统-开始】")
          // nodeNew.setButtonAction(() => {
          //   // alert("新的按钮逻辑被触发！");
          //   Schemes_Start();
          //   // 这里可以访问节点数据或其他逻辑
          //   // console.log("当前节点数据:", nodeNew.data());
          // });
          return nodeNew
        } ],
        // ["测试", () => {
        //   let nodeNew = new Node_System_Component_Start_Test("【系统-开始】")
        //   return nodeNew
        // } ],
        // @ts-ignore - 忽略下一行的类型检查
        ["课题", () =>  {let NodeNew = new Node_System_Component_Object("【课题-未知】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Object);return NodeNew}],
       
        
    

        
      

        
       

       
       ]],

      ["数源", [
        // @ts-ignore - 忽略下一行的类型检查
        ["开源数据",  () =>  {let NodeNew = new Node_DataSource_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);return NodeNew}],
        ["数据方向", [
            ["政治", () => new Node_Model("模型【政治】")],
            ["特侦", () => new Node_Model("模型【特侦】")],
            ["暴恐", () => new Node_Model("模型【暴恐】")],
            ["反邪", () => new Node_Model("模型【反邪】")],
            ["食药环", () => new Node_Model("模型【食药环】")],
          ]],
        ["自定义", []

        ],

  
  
      
      ]],

        ["模型", [
          ["突谣", () => new Node_Model("模型【突谣】")],
          ["情报方向", [
            ["政治", () => new Node_Model("模型【政治】")],
            ["特侦", () => new Node_Model("模型【特侦】")],
            ["暴恐", () => new Node_Model("模型【暴恐】")],
            ["反邪", () => new Node_Model("模型【反邪】")],
            ["食药环", () => new Node_Model("模型【食药环】")],
            ["过滤器", () => new Node_Model("模型【过滤器】")],
            ["筛选器", () => new Node_Model("模型【筛选器】")],
            ]
          ]
     
          // ["汇总", [
          //   ["LDA(潜在狄利克雷分配)", () =>  new Node_Model("系统")],
          //   ["NMF(非负矩阵分解)", () =>  new Node_Model("系统")],
          
          // ]],
          // ["类聚", [
          //   ["DeepSeep", () =>  new Node_Model("系统")],
          //   ["iERM", () =>  new Node_Model("系统")],
          
          // ]],
          // ["预测", [
          //   ["ARIMA【时间序列分析】",  () =>  new Node_Model("系统")],
          //   ["LSTM（长短期记忆网络）", () =>  new Node_Model("系统")],
          
          // ]],
          // ["异常检测", [
          //   ["I孤立森林（Isolation Forest）", () =>  new Node_Template("系统")],
          //   ["One-Class SVM", () =>  new Node_Template("系统")],
          
          // ]],
          // ["关系", [
          //   ["社交网络图谱构建", () =>  new Node_Template("系统")],
          //   ["社区发现", () =>  new Node_Template("系统")],
          
          // ]],
          // ["权重", [
          //   ["分词与停用词过滤", () =>  new Node_Template("系统")],
          //   ["TF-IDF", () =>  new Node_Template("系统")],
          
          // ]],
      
      ]],
      ["算法", [
        ["基础算法", [
          ["LDA(潜在狄利克雷分配)", () =>  new Node_Model("系统")],
          ["NMF(非负矩阵分解)", () =>  new Node_Model("系统")],
        
        ]],
          // @ts-ignore - 忽略下一行的类型检查
          ["AI-DeepSeek", () =>  {let NodeNew = new Node_AI_Component_DeepSeek("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek);return NodeNew}],
          // @ts-ignore - 忽略下一行的类型检查
          ["AI-ChatGLM",  () =>  {let NodeNew = new Node_Model("【AI-ChatGLM】");  CoreConfig.Node_AI_ChatGLM["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_ChatGLM);return NodeNew}],

        // ["DeepSeek", () =>  new Node_Template("模型【DeepSeek】")],
        // ["ChatGLM", () =>  new Node_Template("ChatGLM")],
        // ["iERM", () =>  new Node_Template("iERM")],
      
      ]],

      ["预览", [
        
        // @ts-ignore - 忽略下一行的类型检查
        ["突出谣言", () =>  {let NodeNew = new Node_Preview_Score("【预览-突出谣言】"); CoreConfig.Node_System_Preview["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Preview);return NodeNew}],
        // ["评分", () =>  {
        //   let nodeNew = new Node_Preview_Score("预览【等级】")
        //   // RightMenu_Nade_Add(String(nodeNew.id),"Method","1","评分","OncePreview");
        //   return nodeNew
        // }],
        
      
        
      


    
      ["论坛", () =>  new Node_Template("论坛")],


      ]],
      ["输出", [
        
        // @ts-ignore - 忽略下一行的类型检查
        ["突出谣言", () =>  {let NodeNew = new Node_System_Component_Output("【输出-突出谣言】"); CoreConfig.Node_System_Output["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Output);return NodeNew}],
    
      ["汇总", () =>  new Node_Template("汇总")],


      ]],

    ])
  });
  area.use(contextMenu);



// ---------------------------------------------------------------------------------------- 界面系统功能配置

  AreaExtensions.selectableNodes(area, AreaExtensions.selector(), {
    accumulating: AreaExtensions.accumulateOnCtrl()
  });

  render.addPreset(Presets.contextMenu.setup());
  render.addPreset(Presets.classic.setup());

  connection.addPreset(ConnectionPresets.classic.setup());

  arrange.addPreset(ArrangePresets.classic.setup());

  editor.use(engine);
  editor.use(area);
  area.use(connection);
  area.use(render);
  area.use(arrange);
  // area.use(zoom);
  

  AreaExtensions.simpleNodesOrder(area);
  AreaExtensions.showInputControl(area);

  editor.addPipe((context) => {
    if (["connectioncreated", "connectionremoved"].includes(context.type)) {
      process();
    }
    return context;
  });





// 安全节点创建函数

// async function createAndAddNode(editor: NodeEditor<Schemes>, type: string) {
//   const node = new ClassicPreset.Node(type);
//   node.id = `${type}-${Date.now()}`; // 时间戳保证唯一

//   if (!editor.getNode(node.id)) {
//     await editor.addNode(node);
//     return node;
//   }
//   return null;
// }




  // const a = new Node_Echart("系统【开始】");
  // RightMenu_Nade_Add(String(a.id),"System","Start","开始","");
  // // a.setLabel("新标签");
  // // CoreNodeTree["Nodes"][String(a.id)]=  {"Class":"System","Type":"系统","Method":"Start",
  // //   "Explain":"工作流的启动控制",
  // //   "Status":"未启用",
  // //   "Schemes":"情报+AI测试",
  // // }
  

  // // a.addOutput("a", new ClassicPreset.Output(socket));

  // const progressControl = new ProgressControl(0);
  // // const switchControl = new SwitchControl(true,  (checked) => {
  // //     // console.log('回调收到新值:', checked);
  // //     console.log('Switch 状态变更:', checked);

  // //     area.update("control", switchControl.id);
  // //     // this.update(); // 触发节点更新
      
  // // } );



  // const options: SelectOption<string>[] = [
  //   { value: 'once', label: '即时执行' },
  //   { value: 'Loop', label: '循环执行' },
  //   // { value: 'Timed', label: '定时运行' },
  // ];
  // const selectControl = new SelectControl<string>(
  //   options,
  //   'Loop', // 默认选中的值
    
  //   (value) => {
  //     console.log('Selected value:', value);
  //     WorkFolw_Schems["Start"]["Method"] = value;
  //     area.update("control", selectControl.id);
  //   }
  // );




  // const inputControl = new ClassicPreset.InputControl("number", {
  //   initial: 0,
  //   change(value) {
  //     progressControl.percent = value;
  //     console.log("OK",  a.controls);

  //     // handleSubmit({ name: 'John Doe', email: '<EMAIL>' })
  //     area.update("control", progressControl.id);
  //   }
  // });








  
  // a.addControl("input1",inputControl); // 添加下拉菜单的具体选项
  // a.addControl("input", inputControl);
  // a.addControl("control", switchControl);
  // a.addControl("progress", progressControl);
  // a.addControl("control1", selectControl);


  // const numberControl = new NumberInputControl(1, 0, 10, 1,(value) => {
  //   console.log('[回调] 新值:', value); // 确保此处能触发
  //   area.update("control", numberControl.id);
  // });
  // a.addControl("quantity", numberControl)




  // a.addControl(
  //   "button",
  //   new ButtonControl("启动", () => {
  //     const percent = Math.round(Math.random() * 100);

  //     // inputControl.setValue(percent);
  //     // area.update("control", inputControl.id);

  //     // progressControl.percent = percent;
  //     // area.update("control", progressControl.id);

  //     // area.update("control", numberControl.id);

  //     Schemes_Start();


  //   })



    
  // );
  // await editor.addNode(a);


  //   "ID":Node_ID,
  //   "Class":Type_Info.Name,
  //   "Type":Node_Type,
  //   "Method":Node_Method,
  //   "Explain":Type_Info.Explain,
  //   "Status":"未启用",
  //   "Schemes":"情报+AI测试",

// ----------------------------------------------------------------------------------------

  let System_Start = new Node_System_Component_Start("【系统-开始】")

  let Info ={}
  // RightMenu_Nade_Add(String(Node_System_Start.id),"DataSource","Score","评分","OnceSource");
  
  CoreConfig.Node_System_Start["ID"]    = String(System_Start.id)
  RightMenu_Nade_Add(CoreConfig.Node_System_Start);

  // System_Start.setButtonAction(() => {Schemes_Start();});
  // 设置回调并接收参数
  (System_Start.controls.Content as ButtonControl_Start).setButtonAction((nodeId, action) => {
    console.log("节点 ID:", nodeId);
    console.log("动作类型:", action);

    Schemes_Start()
    // const buttonElement = document.getElementById('Button_Left_Node');
    //   if (buttonElement) {
    //     buttonElement.click();
    //   }
  });
  await editor.addNode(System_Start);
    // const element = document.getElementById('System_Status');
    // if (element) {
    //   element.style.backgroundColor = 'yellow'; // 修改样式
    // }

    // // 这里可以访问节点数据或其他逻辑
    // console.log("当前节点数据:", Node_System_Start.data());
  // });



  // const b = new Node_Preview("系统【开始】");
  // RightMenu_Nade_Add(String(b.id),"System","Start","开始");

  // await editor.addNode(b);





  // const a1 = new NumberNode(1, process);
  // const b = new NumberNode(1, process);
  // const c = new AddNode(process, (c) => area.update("control", c.id));


  // const d = new Node_Template("情报");



  // const con1 = new Connection(a1, "value", c, "left");
  // const con2 = new Connection(b, "value", c, "right");

  // await editor.addNode(a1);
  // await editor.addNode(b);
  // await editor.addNode(c);
  // await editor.addNode(d);

  // await editor.addConnection(con1);
  // await editor.addConnection(con2);







//  function  RightMenu_Nade_Add(Node_ID:string,Node_Class:string,Node_Type:string,Node_Method:string,Tag:string){



// 


function SettingContext  (newName: string) {
    const Element_TextArea_Props = document.getElementById('TextArea_Props');
    if (Element_TextArea_Props) {
      Element_TextArea_Props.textContent=newName
    }
    const Element_Button_Context_Prop = document.getElementById('Button_Context_Prop');
    if (Element_Button_Context_Prop) {
      Element_Button_Context_Prop.click();
    }

};

function RemoveNode(Node_ID:string) {
  // await editor.removeNode(Node_ID)
  const node = editor.getNode(Node_ID);
  if (node) {
     editor.removeNode(Node_ID);
  } else {
    console.warn(`节点 ${Node_ID} 不存在，跳过删除`);
  }
  // await 
}





// ---------------------------------------------------------------------------------------- 功能函数
// 右键添加函数
 function  RightMenu_Nade_Add(Node_Add_Info:Type_Json){

    console.log(Node_Add_Info)
    CoreNodeTree["Nodes"][Node_Add_Info["ID"]]= Node_Add_Info 

    console.log("CoreNodeTree",CoreNodeTree)

    // WorkFolw_Schems={
    //   "Start":{},
    //   "Object":{},
    //   "DataSource":[],
    //   "Analysis":[],
    //   "Model":[],
    //   "AI":[],
    //   "Template":[],
    //   "End":[],
    //   "OnceSource":{},
    //   "OncePreview":{},
    let nodes = editor.getNodes();
    let Node_Start       = nodes.find((node) => node.id === Node_Add_Info["ID"]);

    if  (Node_Start) {
      console.log(`节点 ${Node_Add_Info["ID"]}`);
    }else{  console.warn(`节点 ${Node_Add_Info["ID"]} 不存在，跳过删除`);}

    if ( ["Functon"].includes(Node_Add_Info["Type"])) {
      console.log("Node_Add_Info",Node_Add_Info)
      // let nodes = editor.getNodes();
      // let Node      = nodes.find((node) => node.id === Node_Add_Info["ID"]);
      // await editor.removeNode(Node_Add_Info["ID"])
      // RemoveNode(Node_Add_Info["ID"])

      setTimeout(() => {
        RemoveNode(Node_Add_Info["ID"]);
      }, 300);

    }




    const NowTime = new Date();
    Node_Add_Info["Update"] = `${NowTime.toLocaleDateString()} ${NowTime.toLocaleTimeString()}`

    if ( ["System"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]] =  Node_Add_Info} catch (error) {}
    }

    if ( ["AI","DataSource"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]].push(Node_Add_Info)} catch (error) {}
    }

    if ( ["OncePreview","OnceSource","OnceOutput","Statistic","OnceModel"].includes(Node_Add_Info["Type"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]]=  Node_Add_Info} catch (error) {}
    }





    // try {
    //   WorkFolw_Schems[Node_Add_Info["Module"]].push(Node_Add_Info)
    // } catch (error) {
    //   console.log(error)
      
    // }
    
    console.log("WorkFolw_Schems",WorkFolw_Schems)
    

//   // let NewNode  = editor.getNode(Node_ID)
//   // area.translate(a.id, { x: 120, y:700 });
//   let Type_Info =Node_Type_List[Node_Class];
  // CoreNodeTree["Nodes"][Node_ID]={
  //   "ID":Node_ID,
  //   "Class":Type_Info.Name,
  //   "Type":Node_Type,
  //   "Method":Node_Method,
  //   "Explain":Type_Info.Explain,
  //   "Status":"未启用",
  //   "Schemes":"情报+AI测试",
  // }


// // Node_Class =

//     try {
//       WorkFolw_Schems[Tag] =  CoreNodeTree["Nodes"][Node_ID]
//     } catch (error) {
      
//     }


//     switch (Node_Type) {
//       case "Start":
//         WorkFolw_Schems["Start"] = CoreNodeTree["Nodes"][Node_ID]

//         break;
//       case "Object":
//           WorkFolw_Schems["Object"] = CoreNodeTree["Nodes"][Node_ID]
//           break;
//       default:
//         break;
//     }
    

  // NewNode.addControl(
  //   "button",
  //   new ButtonControl("启动", () => {
      


  //   })
  // )

  
};

async function  RightMenu_Model(Model:string){


  let NodeNew = null
  for (let Node_Name of ["Object","DataSource","AI","Preview","Output","Statistic","Model","Status"]) {
    console.log("Node",Node_Name);
    switch (Node_Name) {
      
      case "Object":
         // @ts-ignore - 忽略下一行的类型检查
         NodeNew= new Node_System_Component_Object("【课题-突出谣言】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Object)
        break
      case "DataSource":
         // @ts-ignore - 忽略下一行的类型检查
         NodeNew = new Node_DataSource_Component_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);
        break;
      case "AI":
         // @ts-ignore - 忽略下一行的类型检查
         try {NodeNew = new Node_AI_Component_DeepSeek("【AI-DeepSeek】");
            let Config_Info ={"AI_Computing":'0',"Title":"11111","AI_Version":"1"}
            NodeNew?.updateContent(Config_Info);
                
          
          
          CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek)} catch (error) {console.error("节点添加失败:", error);throw error;}

        break;
      case "Preview":
         // @ts-ignore - 忽略下一行的类型检查
         try {NodeNew = new Node_Preview_Component_Rumor("【预览-突出谣言】");CoreConfig.Node_System_Preview["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_System_Preview);} catch (error) {console.error("节点添加失败:", error);throw error;}
        
  
         
         break;
      case "Output":
          // @ts-ignore - 忽略下一行的类型检查
          NodeNew = new Node_System_Component_Output("【 输出-突出谣言】");CoreConfig.Node_System_Output["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_System_Output);
      
        break;
      case "Statistic":
          // @ts-ignore - 忽略下一行的类型检查
          NodeNew = new Node_DataSource_Component_Statistic("【 输出-突出谣言】");CoreConfig.Node_DataSource_Statistic["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_DataSource_Statistic);
        break;
      case "Status":
          // @ts-ignore - 忽略下一行的类型检查
          NodeNew = new Node_System_Component_Status("【 输出-突出谣言】");CoreConfig.Node_System_Status["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_System_Status);
        break;
      case "Model":
          // @ts-ignore - 忽略下一行的类型检查
          NodeNew = new Node_Model_Component_Rumor("【 模型-突出谣言】");CoreConfig.Node_Model_Rumor["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_Model_Rumor);

         // 设置回调并接收参数
         (NodeNew.controls.More as ButtonControl_More).setButtonAction((nodeId, action) => {
          console.log("节点 ID:", nodeId);
          console.log("动作类型:", action);
          const buttonElement = document.getElementById('Button_Left_Node');
            if (buttonElement) {
              buttonElement.click();
            }
        });
        break;
    
      default:
        break;
    }
  

    
    try {
      
        // @ts-ignore - 忽略下一行的类型检查
        await editor.addNode(NodeNew);
      
  //     // area.translate(0, 0); 
  //     // area.zoom(0.8, area.area.mouse);
    } catch (error) {
      
    }
    setTimeout((RightMenu_ReSet_Arrange), 1000);
    
  }
  
  
};


function  RightMenu_ReSet_Arrange(){

  console.log("WorkFolw_Schems",WorkFolw_Schems)
  WorkFolw_Schems["Location"]={
    "Start":{x:-120,y:0},
    "Object":{x:320,y:0},
    "OnceSource":{x:320,y:300},
    "OncePreview":{x:620,y:300},
    "OnceOutput":{x:620,y:300},
    "Statistic":{x:620,y:300}, 
    "Model":{x:620,y:300}, 
    // "OncePreview":{x:320,y:0},
  }
  let Location = WorkFolw_Schems["Location"]



  let nodes = editor.getNodes();

  let Start_ID = WorkFolw_Schems["Start"]["ID"]

  area.translate(Start_ID,{ x: Location["Start"]["x"], y:Location["Start"]["y"]})




  let Object_ID = WorkFolw_Schems["Object"]["ID"]

  area.translate(Object_ID,{ x: Location["Object"]["x"], y:Location["Object"]["y"]})










  let OnceSource_Id = WorkFolw_Schems["OnceSource"]["ID"]
  area.translate(OnceSource_Id,{ x: Location["Object"]["x"]+430, y:Location["Object"]["y"]})




  let OncePreview_Id = WorkFolw_Schems["OncePreview"]["ID"]
  area.translate(OncePreview_Id,{ x: Location["Object"]["x"]+1000, y:Location["Object"]["y"]})



  

  let OnceOutput_Id = WorkFolw_Schems["OnceOutput"]["ID"]
  area.translate(OnceOutput_Id,{ x: Location["Object"]["x"]+1680, y:Location["Object"]["y"]})

  


  
  let Statistic_Id = WorkFolw_Schems["Statistic"]["ID"]
  area.translate(Statistic_Id,{ x:200, y:-500})
  
  let OnceModel_Id = WorkFolw_Schems["OnceModel"]["ID"]
  area.translate(OnceModel_Id,{ x: Location["Object"]["x"], y:Location["Object"]["y"]+200})
  

  let AI_Id = WorkFolw_Schems["AI"][0]["ID"]
  area.translate(AI_Id,{x:Location["Object"]["x"]+430, y:450})
  



  let Node_Start       = nodes.find((node) => node.id === Start_ID);
  let Node_Object      = nodes.find((node) => node.id === Object_ID);
  let Node_OnceSource  = nodes.find((node) => node.id === OnceSource_Id);


  let Node_OnceModel   = nodes.find((node) => node.id === OnceModel_Id);
  let Node_AI          = nodes.find((node) => node.id === AI_Id);
  let Node_OncePreview = nodes.find((node) => node.id === OncePreview_Id);
  let Node_OnceOutput  = nodes.find((node) => node.id === OnceOutput_Id);






















   // @ts-ignore - 忽略下一行的类型检查
  const con1 = new Connection(Node_Start, "Start", Node_Object, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const con2 = new Connection(Node_Object, "Output", Node_OnceSource, "Input");


   // @ts-ignore - 忽略下一行的类型检查
   const con3 = new Connection(Node_OnceModel, "Output", Node_OnceSource, "Input");

   // @ts-ignore - 忽略下一行的类型检查
   const con4 = new Connection(Node_OnceSource, "Output", Node_OncePreview, "Input");




      // @ts-ignore - 忽略下一行的类型检查
   const con5 = new Connection(Node_AI, "Output", Node_OncePreview, "Input");


   // @ts-ignore - 忽略下一行的类型检查
   const con6 = new Connection(Node_OncePreview, "Output", Node_OnceOutput, "Input");




  editor.addConnection(con1);
  editor.addConnection(con2);
  editor.addConnection(con3);
  editor.addConnection(con4);
  editor.addConnection(con5);
  editor.addConnection(con6);


  area.area.zoom(0.3, 0, 0)

  //  arrange.layout();

};




 

 function  RightMenu_Schemes_Info(){



  // console.log("toggleFeature",editor.getNodes())
  // console.log("Node_Type_List",Node_Type_List)
  // console.log("CoreNodeTree",CoreNodeTree)


  

};



 /**
  * 执行启动功能
  */
 function Schemes_Start(){
  const intervalId = setInterval(() => {











    try {
      // 每秒执行的任务
      // setCounter(prev => prev + 1); // 示例：每秒计数器+1
      // console.log('定时任务执行', new Date().toLocaleTimeString());
      let Object_ID = WorkFolw_Schems["Object"]["ID"]
      // console.log("Object_ID",Object_ID)
      //  let nodes = editor.getNodes();
      // let Node_Object = nodes.find((node) => node.id === Object_ID);
      let nodes = editor.getNodes();


      let AI_Id = WorkFolw_Schems["AI"][0]["ID"]
      let Node_AI          = nodes.find((node) => node.id === AI_Id);

      console.log("Node_AI",Node_AI)
      
      let Node_Object = nodes.find((node) => node.id === Object_ID);
      if (Node_Object){
        try {
                    
    
                    if (Node_Object){
                      
                      if (Count<=100){
                        //     Count+=10;
                        // }else{
                        //   Count=0;
                        // }
                        
                        Count+=20
                    
                      // } catch (error) {
                            
                      // }

    
                      // // @ts-ignore - 忽略下一行的类型检查
                      // let Progress = Node_Object.controls.Progress as ProgressControl_Object;
                      // // @ts-ignore - 忽略下一行的类型检查
                      // if (Progress) {
                      //   // @ts-ignore - 忽略下一行的类型检查
                      //   Progress.setPercent(Count); // 需要自定义更新方法
                        
                      //   // 4. 触发控件更新（核心）
                      //   area.update('control', Progress.id);
                      // }
                      // if (Count<=100){
                      //     Count+=10;
                      // }else{
                      //   Count=0;
                      // }
                      // console.log("Progress",Progress);
                  //   // 3. 更新进度值（假设 ProgressControl 有 update 方法）
    
                      }else{

                        Count=0
                        // 当 Count 超过 100 时，清理定时器
                        clearInterval(intervalId);
                        console.log("定时器已停止，Count 超过 100");
                      
                      }


                      let Object_Info={
                        "Title":"",
                        "Percent":Count,
                      }

                    // try {
                      // @ts-ignore - 忽略下一行的类型检查
                      // Cnode?.updateContentValue('新的内容值11111');
                      // // @ts-ignore - 忽略下一行的类型检查
                      Node_Object?.updateContent(Object_Info);
                  
                      // console.log('Label',Node_OnceSource?.label);
                      area.update("node", Object_ID);


    
                    };
            } catch (error) {
              
            }
    
      }
    } catch (error) {
      console.log('--------定时请求失败 直接取消请求-------------')
      clearInterval(intervalId); 
    }
  
  }, 2000);
  console.log("CoreNodeTree",CoreNodeTree)
  let Object_ID = WorkFolw_Schems["Object"]["ID"]
  console.log("Object_ID",Object_ID)





  let nodes = editor.getNodes();
  let Node_Object = nodes.find((node) => node.id === Object_ID);

  // if (Node_Object){
    // const progressControl = Node_Object.controls['Content'] as ProgressControl_Object | undefined;
  // console.log("Node_Object",Node_Object);


  // Node_Object.label = "New Label";

  // area.update('node', Object_ID);


  // // @ts-ignore - 忽略下一行的类型检查
  // let Progress = Node_Object.controls.Progress as ProgressControl_Object;
  //  // @ts-ignore - 忽略下一行的类型检查
  // if (Progress) {
  //   // @ts-ignore - 忽略下一行的类型检查
  //   Progress.setPercent(30); // 需要自定义更新方法
    
  //   // 4. 触发控件更新（核心）
  //   area.update('control', Progress.id);
  // }
  // console.log("Progress",Progress);
  //   // 3. 更新进度值（假设 ProgressControl 有 update 方法）

    

  // };




  // progressControl.percent = value;
  // console.log("OK");

  console.log("Object",CoreNodeTree["Nodes"][Object_ID])
  console.log("WorkFolw_Schems",WorkFolw_Schems)

  let Connections = editor.getConnections();
  // console.log('执行启动功能获取到的当前连接：', Connections)
  WorkFolw_Schems['Connections'] = Connections



  let CoreNodeTree_Str = JSON.stringify(CoreNodeTree, null, 2);
  let WorkFolw_Schems_Str = JSON.stringify(WorkFolw_Schems, null, 2);
  const __Service_Requests = new Service_Requests()
  let RequestsData={
    'user_id': '',
    'user_token': '',
    'data_class': 'Analysis',
    'data_type': 'Service_WorFlow',
    'data_methods': 'start_scheme_mission',
    'data_argument': "{}",
    'data_kwargs': WorkFolw_Schems_Str,
  };
  
  // let Response_Data = __CoreControl.Update_Tree(RequestsData);
  (async () => {
    let Response_Data =  await __Service_Requests.Async(RequestsData);
    console.log("Response Data:", Response_Data);
    // 拿到结果 打开一个抽屉展示
    if (Response_Data.Status == "Success") {
      eventBus.emit('open-modal', { type: 'update_node_think', think_info: Response_Data.Content_Info });
    } else {
      console.log("思考出错 不打开弹窗")
    }
    
    let Score_Info_Status = document.getElementById('Score_Info_Status');
    if (Score_Info_Status) {
      Score_Info_Status.textContent=JSON.stringify(Response_Data);
    }

    let OnceSource_Id = WorkFolw_Schems["OnceSource"]["ID"]
    console.log('OnceSource_Id',OnceSource_Id);
   

    let Node_OnceSource = nodes.find((node) => node.id === OnceSource_Id);
  
    console.log('clicked:Node_OnceSource',Node_OnceSource);
    try {
      // @ts-ignore - 忽略下一行的类型检查
      // Cnode?.updateContentValue('新的内容值11111');
      // // @ts-ignore - 忽略下一行的类型检查
      Node_OnceSource?.updateContent(Response_Data["Intelligence"]);
  
      console.log('Label',Node_OnceSource?.label);
      area.update("node", OnceSource_Id);
  
    } catch (error) {
          
    }




    // -----结果展示
    let OncePreview_Id = WorkFolw_Schems["OncePreview"]["ID"]
    let Node_OncePreview = nodes.find((node) => node.id === OncePreview_Id);
    console.log('clicked:Node_OncePreview',Node_OncePreview);
    try {
 
      Response_Data["Intelligence"]["Analysis_Status"]["Title"]   = Response_Data["Intelligence"]["INTELLIGENCE_TITLE_CN"];
      Response_Data["Intelligence"]["Analysis_Status"]["Content"] = Response_Data["Intelligence"]["INTELLIGENCE_CONTENT_CN"];
        // @ts-ignore - 忽略下一行的类型检查
      Node_OncePreview?.updateContent(Response_Data["Intelligence"]["Analysis_Status"]);
  
      console.log('Label',Node_OncePreview?.label);
      area.update("node", OncePreview_Id);
  
    } catch (error) {
          
    }

  

    // if (scoreInfoRef.current) {
    //   scoreInfoRef.current.textContent = 'Hello, world!';
    // }

  })();


  

  // console.log("Response_Data",Response_Data)


 };




//  function findNodeById() {
//   // 获取所有节点
//   const nodes = editor.getNodes();
//   // 查找匹配的节点



//   // return nodes.find(node => node.id === nodeId);
// }



//  通过节点ID 获取节点的value值
function findNodeById(nodeId: string) {
  const nodes = editor.getNodes();
  const Cnode = nodes.find((node) => node.id === nodeId);
  console.log('clicked:updateContentValue',Cnode);
  try {
    // @ts-ignore - 忽略下一行的类型检查
    // Cnode?.updateContentValue('新的内容值11111');
    // // @ts-ignore - 忽略下一行的类型检查
    // Cnode?.updateLabel('新的内容值11111');
    console.log('Label',Cnode?.label);
  } catch (error) {
    console.log('获取节点label出错');
  };
};







// 监听节点点击事件
area.addPipe((context) => {

  
  // console.log('[editor.ts]监听节点点击事件:',context);
  if (context.type === 'nodepicked') {
    const node = context.data;
    let Node_Info = CoreNodeTree["Nodes"][node.id];
    try {
      findNodeById(node.id)
      // @ts-ignore - 忽略下一行的类型检查
      node.updateContentValue('新的内容值');
    } catch (error) {
      
    };
      
    // let CoreNodeTree_Str = JSON.stringify(CoreNodeTree, null, 2);
    // let CoreC = new CoreControl()
    // let RequestsData={
    //   'user_id': '',
    //   'user_token': '',
    //   'data_class': 'Config',
    //   'data_type': 'Service_Check',
    //   'data_methods': 'return_sentinel_config_custom',
    //   'data_argument': "{}",
    //   'data_kwargs': CoreNodeTree_Str,
    // }
    
    // CoreC.Update_Tree(RequestsData);


    try {
      console.log('Node clicked:', node.id,Node_Info);
      console.log('Node clicked:', node);

      let Node_Submit={
        "Command_Type":"Node_Update",
        "Node_ID":node.id,
        "Node_Schemes":Node_Info.Schemes,
        "Node_Class":Node_Info.Class,
        "Node_Type":Node_Info.Type,
        "Node_Method":Node_Info.Method,
        "Node_Explain":Node_Info.Explain,
        "Node_Status":Node_Info.Status,
        // "Node_Schemes":Node_Info.Schemes,

      }

      // SettingContext(`{"Command_Type":"Node_Update","Node_ID":"${node.id}"}`)
      SettingContext(JSON.stringify(Node_Submit))









      // node.
      let Node_Info_ID = document.getElementById('Node_Info_ID');
      if (Node_Info_ID) {
        Node_Info_ID.textContent=`ID: ${node.id}`;
      }
  
      let Node_Info_Schemes = document.getElementById('Node_Info_Schemes');
      if (Node_Info_Schemes) {
        Node_Info_Schemes.textContent=`课题: ${Node_Info.Schemes}`;
      }
  
  
  
      let Node_Info_Class = document.getElementById('Node_Info_Class');
      if (Node_Info_Class) {
        Node_Info_Class.textContent=`模块: ${Node_Info.Class}`;
      }
  
  
      let Node_Info_Type = document.getElementById('Node_Info_Type');
      if (Node_Info_Type) {
  
  
        if (Node_Info.Class ==="数源"){
          Node_Info_Type.textContent=`方向: ${Node_Info.Type}`;
        }else{
          Node_Info_Type.textContent=`类型: ${Node_Info.Type}`;
        }
          
        
      }
  
      let Node_Info_Method = document.getElementById('Node_Info_Method');
      if (Node_Info_Method) {
        
      if (Node_Info.Class ==="数源"){
        Node_Info_Method.textContent=`类型: ${Node_Info.Method}`;
      }else{
        Node_Info_Method.textContent=`方法: ${Node_Info.Method}`;
      }
        
      }
      let Node_Info_Explain = document.getElementById('Node_Info_Explain');
      if (Node_Info_Explain) {
        Node_Info_Explain.textContent=`说明: ${Node_Info.Explain}`;
      }
      let Node_Info_Status = document.getElementById('Node_Info_Status');
      if (Node_Info_Status) {
        Node_Info_Status.textContent=` ${Node_Info.Status}`;
      }
  
      
    } catch (error) {
      
    };

   




  }



  

  // }else{
  //   if (context.type === 'connectioncreate'){
  //     const Connection = context.data;
  //     console.log('Connection clicked:', context.data);
  //   }
  // }


  // if (["connectioncreated", "connectionremoved"].includes(context.type)) {
  //   process();
  // }

  //   // } if (canCreateConnection(context.data)) return false

  // }



  return context;
});



// ---------------------------------------------------------------------------------------- 添加按钮事件监听
const Button_Node_Update = document.getElementById('Button_Node_Update');
if (Button_Node_Update) {
  Button_Node_Update.addEventListener('click', async () => {
    
    let Object_ID = WorkFolw_Schems["Object"]["ID"]
    console.log("Object_ID",Object_ID)
    let Input_Node_Label  = document.getElementById('Input_Node_Label') as HTMLInputElement;
  
    // let InText = ;
    // console.log("Click",InText)
  
    let nodes = editor.getNodes();
    let Node_Object = nodes.find((node) => node.id === Object_ID);
  
    if (Node_Object){
        //   const progressControl = Node_Object.controls['progress'] as ProgressControl_Object | undefined;
        console.log("Node_Object",Node_Object);
      
      
        Node_Object.label = `课题【${Input_Node_Label?.value}】`;
      
        area.update('node', Object_ID);
      
  
     }

  });
}

var Count =1



// WorkFolw_Schems={
//   "Start":{},
//   "Object":{},
//   "DataSource":[],
//   "Analysis":[],
//   "Model":[],
//   "AI":[],
//   "Template":[],
//   "End":[],
//   "OnceSource":{},
//   "OncePreview":{},

// }


// ["AI-DeepSeek", () =>  {let NodeNew = new Node_Model("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek);return NodeNew}],
// ["实时突谣评分",  () =>  {let NodeNew = new Node_DataSource_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);return NodeNew}],

function Initial_Schemes() {
  // const Node_List =["Object","OncePreview"]
  let NodeNew = null
  for (let Node_Name of ["Object","DataSource","AI","Preview"]) {
    console.log("Node",Node_Name);
    switch (Node_Name) {
      case "Object":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew= new Node_System_Component_Object("【课题-未知】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Object)

        break;
      case "DataSource":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_DataSource_Component_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);

        break;
      case "AI":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek)

        break;
      case "Preview":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Preview_Score("【预览-突出谣言】");CoreConfig[`Node_System_Preview`]["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_System_Preview);
      


        break;
    
      default:
        break;
    }



    
    try {
      
      // @ts-ignore - 忽略下一行的类型检查
      editor.addNode(NodeNew);
      
      // area.translate(0, 0); 
      // area.zoom(0.8, area.area.mouse);
    } catch (error) {
      
    }
    setTimeout((RightMenu_ReSet_Arrange), 1000);
    
  }
};




// Initial_Schemes()

// setTimeout(Initial_Schemes, 1000);
// function arrangeNodes(nodes: Node[], spacing = 100) {
//   let yPos = 0;
//   nodes.forEach(node => {
//     node.position.set(0, yPos);
//     yPos += node.height + spacing;
//   });
// }

// // 使用示例
// const allNodes = editor.getNodes();
// arrangeNodes(allNodes);


  // 2. 定义服务方法并绑定必要上下文
  // const Function_Service = (config: string) => {
  //   console.log("执行服务函数",config);

  //   console.log("服务配置:", { config });
  //   // 可以访问编辑器内部状态
  //   console.log("当前节点数量:", editor.getNodes().length);
  // };

  const FunctionService = (maxi:Type_Json) => {
    console.log("执行服务函数",maxi);
    // 可以访问编辑器内部状态
    console.log("当前节点数量:", editor.getNodes().length);
    Initial_Schemes();
  };


  await arrange.layout();
  AreaExtensions.zoomAt(area, editor.getNodes());

  return {
    // destroy: () => area.destroy(),
    destroy: () => {
      area.destroy();
      editor.clear();
    },
    FunctionService
  };
}
