<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>哨兵导控</title>
    <!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
    <link rel="stylesheet" href="/static/CSS/bootstrap-fileinput/fileinput.min.css">
    <link rel="stylesheet" href="/static/CSS/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/CSS/animate/animate.min.css">
    <link rel="stylesheet" href="/static/CSS/jquery/fullcalendar/fullcalendar.min.css">
    
    <link rel="stylesheet" href="/static/CSS/daterangepicker/daterangepicker.min.css">

    <!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />

    <link href="/static/CSS/font-awesome/all.min.css" rel="stylesheet" />
    <!-- 加载 Select2 -->
    <link href="/static/CSS/select2/select2.min.css" rel="stylesheet" />
    
    <link rel="stylesheet" href="/static/CSS/jquery/jquery.scrollbar.css">
    <!--     dialog   -->
    <link rel="stylesheet" href="/static/CSS/trumbowyg/trumbowyg.min.css">
    <link rel="stylesheet" href="/static/CSS/sweetalert/sweetalert2.min.css">

    <link rel="stylesheet" href="/static/CSS/server_style.css">

    <!-- data-table CSS ============================================ -->
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-table.css">
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-editable.css">

    <!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
    <!-- App styles -->
    <link rel="stylesheet" href="/static/CSS/app/app.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />

    <style type="text/css">
        .table {
            table-layout: fixed;
            word-break: break-all;
        }

        
        .nav-link {
            display: block;
            padding: 0rem 1.5rem;
        }

    </style>
</head>

<body class="bg-theme bg-theme1">

    <div class="wrapper">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>

        <div class="page-wrapper">
            <div class="page-content-wrapper">
                <div class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="mb-3">查询条件：</h6>

                            <div class="form-row">
                                <!-- <div class="col-lg-3 col-md-12 my-md-3">
                                    <select class="select2" data-minimum-results-for-search="Infinity">
                                        <option selected>服务类型</option>
                                        <option>服务媒体</option>
                                        <option>服务数量</option>
                                    </select>
                                </div> -->
                                <div class="col-lg-3 col-md-12 my-md-3 d-flex align-items-center">
                                    <label class="btn btn-light" style="flex-shrink: 0;"
                                        for="selectedOptions_time">订单创建时间</label>：
                                    <select class="select2" id="selectedOptions_time"
                                        data-minimum-results-for-search="Infinity">
                                        <option value="day">今日</option>
                                        <option value="yesterday">昨日</option>
                                        <option value="week" selected>一周</option>
                                        <option value="other">自定义</option>
                                        <option value="mouth">本月</option>
                                        <option value="all" >所有</option>
                                    </select>
                                </div>
                                <div class="col-lg-3 col-md-12 my-md-3" style="display: none;">
                                    <input type="text" id="diy_time_list" class="form-control" placeholder="点击选择日期范围">
                                </div>
                                <div class="col-lg-3 col-md-12 my-md-3">
                                    <button class="btn btn-primary" id="query_table">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <style>
                            .table-responsive select option {
                                background-color: rgba(52, 58, 64, 0.55) !important;
                                /* 设置下拉框的背景颜色为绿色 */
                                color: white;
                                /* 设置下拉框的文字颜色为白色 */
                            }

                            /*修改默认的展开图标*/
                            .bootstrap-table .detail-icon {
                                font-family: Material-Design-Iconic-Font;
                                /* 指定字体 */
                                src: url(/static/app/fonts/Material-Design-Iconic-Font.woff2?v=2.2.0) format('woff2');
                                font-weight: 900;
                                /* FontAwesome 的 Solid 风格需要粗体 */
                                font-size: large;
                                font-size: 30px;
                            }

                            .bootstrap-table .detail-icon::before {
                                content: '\f2fb';
                                /* FontAwesome 的加号图标 */
                            }

                            /* 当行被展开时，更改图标 */
                            .bootstrap-table .detail-view .detail-icon::before {
                                content: '\f2f9';
                                /* FontAwesome 的减号图标 */
                            }
                        </style>
                        <table id="table" class="table table-hover"></table>
                    </div>
                    <style>
                        .table-container {
                            height: 500px;
                            /* 表格容器的最大高度 */
                            overflow-y: auto;
                            /* 允许垂直滚动 */
                            overflow-x: hidden;
                        }
                    </style>
                    <!--已购服务 详情 弹窗-->
                    <div class="modal fade" id="modal-detail" tabindex="-1" data-backdrop="false">
                        <div class="modal-dialog modal-xl">
                            <div class="modal-content" style="background: url('/static/images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/images/App/Sball.gif">
                                        已购服务详情
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body table-responsive table-container">
                                    <table style="width: 100%" id="orderDetailTable" class="table table-hover table-no-bordered"
                                        data-toggle="table" data-detail-view="true" data-detail-formatter="detailFormatter">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal fade" id="modal-account" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/images/App/Sball.gif">
                                        已购服务详情
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/daterangepicker/jquery.daterangepicker.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/fileinput.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/zh.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/theme.min.js"></script>
    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>
    <script src="/static/JavaScript/select2/select2.full.min.js"></script>
    <script src="/static/JavaScript/select2/zh-CN.js"></script>
    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>
    <!-- data-table  ============================================ -->
    <script src="/static/JavaScript/data-table/bootstrap-table.js"></script>
    <script src="/static/JavaScript/data-table/bootstrap-table-zh-CN.js"></script>

    <!-- App functions and dialog -->
    <script src="/static/JavaScript/App/clamp.js"></script>
    <script src="/static/JavaScript/App/trumbowyg.min.js"></script>

    <!-- App functions and notify -->
    <script src="/static/JavaScript/App/bootstrap-notify.min.js"></script>

    <!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>

    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <!-- <script src="/static/JavaScript/Config/Server_Tools/Server_Print.js"></script>
    <script src="/static/JavaScript/Config/Server_Tools/Server_Href.js"></script>
    <script src="/static/JavaScript/Config/Server_Function/Server_Data.js"></script>
    <script src="/static/JavaScript/Config/System_BaseConfig.js"></script> -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <script src="/static/JavaScript/clipboard/cilpboard.min.js"></script>
    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/App/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/App/dataTables.buttons.min.js"></script>
    <script src="/static/JavaScript/App/buttons.print.min.js"></script>
    <script src="/static/JavaScript/App/jszip.min.js"></script>
    <script src="/static/JavaScript/App/buttons.html5.min.js"></script>
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <script src="/static/Page/Market/Page_Home_Orders_List.js"></script>

    <script>
		var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>
    <script>
        function Page_Init() {
            Query_Orders();
        };

        window.onload = function () {
            Element_Sidebar_Header();
            Page_Init();
        };
    </script>
</body>

</html>