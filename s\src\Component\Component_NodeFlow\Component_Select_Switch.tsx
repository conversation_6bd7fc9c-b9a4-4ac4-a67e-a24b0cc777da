import React, { useState } from 'react';
import { Select, But<PERSON>,ConfigProvider,Space } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import type { ThemeConfig } from 'antd';
// import '../Styles/Utils_Common.module.css'
import SelectStyles from '@/Styles/Utils_Common.module.css'




interface Props_Select{
    Options_Select: any;
//     Toggle_Dialog_KeyWord: () => void; // 接收 toggleDialog 函数
  }






 const Component_Select_Switch: React.FC<Props_Select> = ({ 
    Options_Select, 

  }) => {



    const options  = Options_Select
  // 选项数据
//   const options = [
//     { value: 'option1', label: '选项1' },
//     { value: 'option2', label: '选项2' },
//     { value: 'option3', label: '选项3' },
//     { value: 'option4', label: '选项4' },
//   ];

  const [selectedValue, setSelectedValue] = useState<string>(options[0].value);

  // 获取当前选中的索引
//   @ts-ignore - 忽略下一行的类型检查
  const currentIndex = options.findIndex(opt => opt.value === selectedValue);

  // 左箭头点击 - 切换到上一个选项
  const handleLeftClick = () => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
    setSelectedValue(options[newIndex].value);
  };

  // 右箭头点击 - 切换到下一个选项
  const handleRightClick = () => {
    const newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
    setSelectedValue(options[newIndex].value);
  };

  // Select 选择变化
  const handleSelectChange = (value: string) => {
    setSelectedValue(value);
  };
    const theme: ThemeConfig = {
        components: {
            Select: {
                // 核心配置
                multipleItemBg: 'transparent',
                selectorBg: 'transparent',
                multipleSelectorBgDisabled: 'transparent',
                hoverBorderColor: 'transparent',
                multipleItemBorderColor: 'transparent',
                activeBorderColor: 'transparent',
                activeOutlineColor: 'transparent',
                multipleItemColorDisabled:"red",
                colorText:"white",
                colorBorder:"transparent",
                // optionPadding: '55px 12px',
                // selectorBg: '#ffffff',

                
            },


            },
        };

    // <Space wrap>
  return (
    // <Space style={{ 
    //   display: 'flex', 
    //   alignItems: 'center', 
    //   justifyContent: 'center',
    //   height:20,
    //   width:440,
    //   background:"transparent",
    //   gap: '8px'
    // }}>
   
      <ConfigProvider theme={theme}>
        <Select
            // className="custom-double-select"
            className={SelectStyles['custom-double-select']}
            size={"small"}
            value={selectedValue}
            onChange={handleSelectChange}
            // style={{ flex: 1, width: '120px' , backgroundColor: '#f0f8ff', alignItems:"center",justifyItems:"center"}}
            style={{ flex: 1, width: '70px' , alignItems:"center",justifyItems:"center",color:"white"}}
            options={options}
 
       
        />


 

      </ConfigProvider>

    // </Space>






  );
};

export default Component_Select_Switch;