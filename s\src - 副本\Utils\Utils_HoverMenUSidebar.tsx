import React, { useState, useEffect,forwardRef, useRef, useImperativeHandle,useMemo } from 'react';
import { Button, message ,Timeline,Tooltip,Layout} from "antd";

import { DownloadOutlined,HomeOutlined,CloudServerOutlined,LoginOutlined,ArrowLeftOutlined,LeftOutlined,BookOutlined,HistoryOutlined,FunctionOutlined,NodeIndexOutlined,SettingFilled,UserOutlined} from '@ant-design/icons';


import type { TooltipProps } from 'antd';


interface MenuProps {
  toggleDrawer: () => void; // 接收 toggleDrawer 函数
  Toggle_Dialog_KeyWord: () => void; // 接收 toggleDrawer 函数
}

export interface HoverMenuHandle {
  toggleSidebar: () => void;
  getSidebarState: () => boolean;
}
const Utils_HoverMenUSidebar = forwardRef<HoverMenuHandle, MenuProps>(({toggleDrawer,Toggle_Dialog_KeyWord },  ref)=> {
  const [isSidebarVisible, setSidebarVisible] = useState(false); // 控制主侧边栏的显示
  const [activeSubSidebar, setActiveSubSidebar] = useState<number | null>(null); // 当前打开的子侧边栏编号
  const [buttonState, setButtonState] = useState<number | null>(null); // 记录被点击的按钮编号
  const threshold = 20; // 鼠标靠近屏幕左边的距离阈值（像素）
  const sidebarWidth = 60; // 主侧边栏宽度
  const subSidebarWidth = 300; // 每个子侧边栏的宽度
  // 时间线
  const items = [
    { children: 'sample1', label: '11:00' ,color:"red"},
    { children: 'sample2', label: '11:00' ,color:"red"},
    { children: 'sample3', label: '11:00' ,color:"red"},
    { children: 'sample4', label: '11:00' ,color:"red"},
    { children: 'sample5', label: '11:00' ,color:"red"},
    { children: 'sample6', label: '11:00' ,color:"red"},
    { children: 'sample7', label: '11:00' ,color:"red"},
    { children: 'sample8', label: '11:00' ,color:"red"},
  
  ];
  const Array_Data_Setting = [
    {"ID":"1","NAME":"热词","CONTENT":"当前热词【100】"},
    {"ID":"2","NAME":"条件","CONTENT":"当前条件【25】"},
    {"ID":"3","NAME":"方向","CONTENT":"当前方向【5】"},
    // 更多对象...
  ];
    // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    toggleSidebar: () => {
      setSidebarVisible(prev => !prev);
    },
    getSidebarState: () => isSidebarVisible
  }));

  const [arrow, setArrow] = useState<'Show' | 'Hide' | 'Center'>('Show');
  const mergedArrow = useMemo<TooltipProps['arrow']>(() => {
    if (arrow === 'Hide') {
      return false;
    }

    if (arrow === 'Show') {
      return true;
    }

    return {
      pointAtCenter: true,
    };
  }, [arrow]);

  // 监听鼠标移动事件
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      // 判断鼠标是否靠近屏幕左边
      if (event.clientX <= threshold) {
        setSidebarVisible(true); // 鼠标靠近屏幕左边时显示主侧边栏
      }
    };

    // 添加全局事件监听
    window.addEventListener('mousemove', handleMouseMove);

    // 清理事件监听
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  // 切换子侧边栏的显示状态
  const toggleSubSidebar = (sidebarNumber: number) => {
    setActiveSubSidebar((prev) => (prev === sidebarNumber ? null : sidebarNumber));
  };

  // 按钮点击事件
  const handleButtonClick = (buttonNumber: number) => {
    setButtonState(buttonNumber);
  };  

  return (
    <div
      style={{
        position: 'fixed',
        top: 40,
        left: 0,
        width: sidebarWidth, // 主侧边栏的宽度
        height: '100%',
        backgroundColor: 'transparent', // 使整个区域透明
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      {/* 主侧边栏 */}


      <Layout  style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}>

        <Layout  style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}>
        <div
        style={{
          width: sidebarWidth,
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 1)',
          // backgroundColor: '#007bff',
          boxShadow: '0px 0 0px rgba(131, 157, 241, 0.0)',
          transform: isSidebarVisible ? 'translateX(0)' : `translateX(-${sidebarWidth}px)`,
          transition: 'transform 0.3s ease-in-out',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'start',
          alignItems: 'center',
          zIndex: 1002, // 确保主侧边栏的层级高于子侧边栏
        }}
      >
          
        <Tooltip placement="rightTop" title={"情报数源"} arrow={mergedArrow}>
          <button
            onClick={() => toggleSubSidebar(1)} // 切换子侧边栏1的显示状态
            style={{
              padding: '8px 16px',
              backgroundColor: buttonState === 1 ? 'yellow' : 'rgba(0, 0, 0, 1)',
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              width: '100%',
              textAlign: 'center',
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 2 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
          >
          <CloudServerOutlined   style={{fontSize:20}}/>
          </button>
          </Tooltip>
          <Tooltip placement="rightTop" title={"情报课题"} arrow={mergedArrow}>
              <button
                onClick={() => toggleSubSidebar(2)} // 切换子侧边栏1的显示状态
                style={{
                  padding: '8px 16px',
                  backgroundColor: buttonState === 2 ? 'yellow' : 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 2 ? 'yellow' : 'rgba(0, 0, 0, 1)'}
              >
                <BookOutlined  style={{fontSize:18}} />
              </button>
          </Tooltip>
          <Tooltip placement="rightTop" title={"历史记录"} arrow={mergedArrow}>
              <button
                onClick={() => toggleSubSidebar(3)} // 切换子侧边栏2的显示状态
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor =  '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 3 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
              >
              <HistoryOutlined style={{fontSize:18}} />
              </button>
          </Tooltip>
          <Tooltip placement="rightTop" title={"功能组件"} arrow={mergedArrow}>
              <button
                onClick={() => toggleSubSidebar(4)} // 切换子侧边栏3的显示状态
                style={{
                  padding: '8px 16px',
                  backgroundColor: buttonState === 4 ? 'yellow' : 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 4 ? 'yellow' : 'rgba(0, 0, 0, 1)'}
              >
                <FunctionOutlined style={{fontSize:22}}  />
              </button>
           </Tooltip>
           <Tooltip placement="rightTop" title={"节点设置"} arrow={mergedArrow}>
              <button
                onClick={toggleDrawer} // 控制Drawer的显示和隐藏
                style={{
                  padding: '8px 16px',
                  backgroundColor: buttonState === 5 ? 'yellow' : 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor =  '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 5 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
              >
                <NodeIndexOutlined style={{fontSize:22}} />
              </button>
          </Tooltip>
   
      </div> 

        </Layout>

        <Layout 

    
          style={{
            width: sidebarWidth,
            height: '60%',
            backgroundColor: 'rgba(0, 0, 0, 1)',
            // backgroundColor: '#007bff',
            boxShadow: '0px 0 0px rgba(131, 157, 241, 0.0)',
            transform: isSidebarVisible ? 'translateX(0)' : `translateX(-${sidebarWidth}px)`,
            transition: 'transform 0.3s ease-in-out',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'end',
            alignItems: 'center',
            zIndex: 1002, // 确保主侧边栏的层级高于子侧边栏
          }}
        >


      <Tooltip placement="rightTop" title={"隐蔽菜单"} arrow={mergedArrow}>
              <button
                onClick={() =>setSidebarVisible(false)} // 切换子侧边栏4的显示状态
                style={{
                  padding: '8px 16px',
                  backgroundColor: buttonState === 6 ? 'yellow' : 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 6 ? 'yellow' : 'rgba(0, 0, 0, 1)'}
              >
               <DownloadOutlined style={{fontSize:18,transform: 'rotate(90deg)'}} />
              </button>
          </Tooltip>


          
                 <Tooltip placement="rightTop" title={"系统配置"} arrow={mergedArrow}>
              <button
                onClick={() => toggleSubSidebar(5)} // 切换子侧边栏4的显示状态
                style={{
                  padding: '8px 16px',
                  backgroundColor: buttonState === 6 ? 'yellow' : 'rgba(0, 0, 0, 1)',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  width: '100%',
                  textAlign: 'center',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 6 ? 'yellow' : 'rgba(0, 0, 0, 1)'}
              >
                <SettingFilled style={{fontSize:18}} />
              </button>
          </Tooltip>
        <Tooltip placement="rightTop" title={"用户配置"} arrow={mergedArrow}>
          <button
            onClick={() => setSidebarVisible(false)} // 点击按钮隐藏主侧边栏
            style={{
              padding: '8px 16px',
              backgroundColor: buttonState === 7 ? 'yellow' : 'rgba(0, 0, 0, 1)',
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              width: '100%',
              textAlign: 'center',
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 1 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
          >
            {/* <HomeOutlined  style={{fontSize:20}}/><ArrowLeftOutlined />transform: 'rotate(180deg)'<LeftOutlined /> <UserOutlined />*/}
            <UserOutlined  style={{fontSize:18,}}/>
          </button>
        </Tooltip>
        <Tooltip placement="rightTop" title={"隐蔽菜单"} arrow={mergedArrow}>
          <button
            onClick={() => setSidebarVisible(false)} // 点击按钮隐藏主侧边栏
            style={{
              padding: '8px 16px',
              backgroundColor: buttonState === 7 ? 'yellow' : 'rgba(0, 0, 0, 1)',
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              width: '100%',
              textAlign: 'center',
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 1 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
          >
            {/* <HomeOutlined  style={{fontSize:20}}/><ArrowLeftOutlined />transform: 'rotate(180deg)'<LeftOutlined /> */}
            <LeftOutlined  style={{fontSize:18,color:'rgba(0, 0, 0, 0)'}}/>
          </button>
        </Tooltip>

          <button
           
           style={{
             padding: '8px 16px',
             backgroundColor: buttonState === 7 ? 'yellow' : 'rgba(0, 0, 0, 0)',
             color: 'white',
             border: 'none',
             cursor: 'pointer',
             width: '100%',
             textAlign: 'center',
     
           }}
           onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
           onMouseLeave={(e) => e.currentTarget.style.backgroundColor = buttonState === 1 ? 'yellow' :  'rgba(0, 0, 0, 1)'}
         >
           {/* <HomeOutlined  style={{fontSize:20}}/><ArrowLeftOutlined />transform: 'rotate(180deg)'<LeftOutlined /> */}
           <LeftOutlined  style={{fontSize:18,color:'rgba(0, 0, 0, 0)'}}/>
         </button>
         
          
      
        </Layout>




      </Layout>







 

      {/* 子侧边栏1  数源的设置 */}
      <div
        style={{
          width: subSidebarWidth,
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 1)',
          boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
          position: 'absolute',
          left: sidebarWidth, // 子侧边栏始终位于主侧边栏的右侧
          transform: activeSubSidebar === 1 ? 'translateX(0)' : `translateX(-${subSidebarWidth}px)`,
          transition: 'transform 0.3s ease-in-out',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          opacity: isSidebarVisible ? 1 : 0, // 当主侧边栏隐藏时，子侧边栏完全不可见
          pointerEvents: isSidebarVisible ? 'auto' : 'none', // 当主侧边栏隐藏时，子侧边栏不可交互
        }}
      >
        <div style={{
                padding: 20,
                height: 'calc(100% - 40px)', // 确保内容区域高度合理
                overflowY: 'auto', // 启用垂直滚动
                width: '100%',
              }}>
          <h3  style={{ textAlign: 'center',color:"white" }}>数源数据控制</h3>
            {/* <Button  type="primary"  size="small"  style={{ marginTop:3,width: 80 ,backgroundColor: 'rgba(0,0,0,0.8)',borderColor: 'rgba(0,0,0,0.8)', }} >热词排名</Button>
            <Button  type="primary"  size="small"  style={{ marginLeft:3,width: 80 ,backgroundColor: 'rgba(0,0,0,0.8)',borderColor: 'rgba(0,0,0,0.8)', }} >公式</Button> */} 

            {/* {Array.from({ length: 30 }).map((_, index) => ( */}
            {Array_Data_Setting.map((item, index) => (
              <div
                key={item.ID}
                style={{
                  height: 30,
                  width: '96%',
                  // backgroundColor: `hsl(${index * 12}, 70%, 60%)`, // 使用 HSL 颜色模式生成不同颜色
                  backgroundColor: `rgba(255,255,255,0.3)`, // 使用 HSL 颜色模式生成不同颜色
                  margin: '5px auto',
                  borderRadius: '4px',
                  // border: '1px solid #ddd',
                }}
              >
                  <Button  onClick={Toggle_Dialog_KeyWord}   type="primary"  size="small"  style={{ marginLeft:5,marginTop:2,width: 50 ,height: 25 ,backgroundColor: 'rgba(123, 142, 220, 0.8)',borderColor: 'rgba(0,0,0,0.0)', }} >{item.NAME}</Button>
                  <Button                           type="primary"  size="small"  style={{ marginLeft:5,marginTop:2,width: "75%" ,height: 25 ,backgroundColor: 'rgba(123, 142, 220, 0.8)',borderColor: 'rgba(0,0,0,0.0)', }} >{item.CONTENT}</Button>
              </div>
            ))}




       
        </div>
      </div>

      {/* 子侧边栏2 */}
      <div
        style={{
          width: subSidebarWidth,
          height: '100%',
          backgroundColor: 'rgba(255, 255, 255, 0.0)',
          boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
          position: 'absolute',
          left: sidebarWidth, // 子侧边栏始终位于主侧边栏的右侧
          transform: activeSubSidebar === 2 ? 'translateX(0)' : `translateX(-${subSidebarWidth}px)`,
          transition: 'transform 0.3s ease-in-out',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          opacity: isSidebarVisible ? 1 : 0, // 当主侧边栏隐藏时，子侧边栏完全不可见
          pointerEvents: isSidebarVisible ? 'auto' : 'none', // 当主侧边栏隐藏时，子侧边栏不可交互
        }}
      >
        <div style={{ padding: 20 }}>
          <h3>子侧边栏2内容</h3>
          <Timeline
          items={items}
          mode="left"
          >
            
          {/* <Timeline.Item>Sample</Timeline.Item> */}
        </Timeline>
        </div>
      </div>

      {/* 子侧边栏3 */}
      <div
        style={{
          width: subSidebarWidth,
          height: '100%',
          backgroundColor: 'rgba(255, 255, 255, 0.0)',
          boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
          position: 'absolute',
          left: sidebarWidth, // 子侧边栏始终位于主侧边栏的右侧
          transform: activeSubSidebar === 3 ? 'translateX(0)' : `translateX(-${subSidebarWidth}px)`,
          transition: 'transform 0.3s ease-in-out',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          opacity: isSidebarVisible ? 1 : 0, // 当主侧边栏隐藏时，子侧边栏完全不可见
          pointerEvents: isSidebarVisible ? 'auto' : 'none', // 当主侧边栏隐藏时，子侧边栏不可交互
        }}
      >
        <div style={{ padding: 20 }}>
          <h3>子侧边栏3内容</h3>
          <p>这里是子侧边栏3的内容。</p>
        </div>
      </div>

      {/* 子侧边栏4 */}
      <div
        style={{
          width: subSidebarWidth,
          height: '100%',
          backgroundColor: 'rgba(255, 255, 255, 0.0)',
          boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
          position: 'absolute',
          left: sidebarWidth, // 子侧边栏始终位于主侧边栏的右侧
          transform: activeSubSidebar === 4 ? 'translateX(0)' : `translateX(-${subSidebarWidth}px)`,
          transition: 'transform 0.3s ease-in-out',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          opacity: isSidebarVisible ? 1 : 0, // 当主侧边栏隐藏时，子侧边栏完全不可见
          pointerEvents: isSidebarVisible ? 'auto' : 'none', // 当主侧边栏隐藏时，子侧边栏不可交互
        }}
      >
        <div style={{ padding: 20 }}>
          <h3>子侧边栏4内容</h3>
          <p>这里是子侧边栏4的内容。</p>
        </div>
      </div>
    </div>
  );
});

export default Utils_HoverMenUSidebar;