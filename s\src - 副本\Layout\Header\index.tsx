import { FC, useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom';
import {
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MessageOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Layout, Dropdown, Flex, Avatar, Badge, theme, Space, notification  } from 'antd';
import { useAuth } from '@/Core/Core_AuthContent';
import { useServiceRequests } from '@/Core/Core_Control';
import './index.css'

const { Header } = Layout

interface MyHeaderProps {
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
  extra?: React.ReactNode
};

interface AlertMessage { 
  uuid: string,
  title: string,
  time: string,
  read: boolean
}

const ShowAvatarURL = process.env.REACT_APP_SHOW_AVATAR_URL; // 获取上传的URL
if (!ShowAvatarURL) {
  throw new Error('show avatar URL is not defined in environment variables');
};

const MyHeader: FC<MyHeaderProps> = ({ collapsed, setCollapsed, extra }) => {

  const { user, logout } = useAuth();

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const navigate = useNavigate();
  const { AsyncTokenRequests } = useServiceRequests();
  const [alertMessages, setAlertMessages] = useState<AlertMessage[]>([]);
  const [messageList, setMessageList] = useState<AlertMessage[]>([]);
  //登出
  const Handle_Logout = () => {
    logout()
    navigate('/Page_Login'); // 登录成功后跳转到首页
  };

  const Menu_User = [
    {
      key: '1',
      label: (
        <Space size={4} onClick={() => navigate('/Page_Manage/User')}>
          <SettingOutlined rev={undefined} />
          <span>用户设置</span>
        </Space>
      ),
    },
    {
      key: '2',
      label: (
        <Space size={4} onClick={() => Handle_Logout()}>
          <LogoutOutlined rev={undefined} />
          <span>退出登录</span>
        </Space>
      ),
    },
  ];

  const handlegetMessage = () => { 
    let Requests_Data={
      "user_id": "",
      "user_token":"" ,
      "data_class": "Sentinel",
      "data_type": 'System',
      "data_methods": "return_alarm_message_info",
      "data_argument": `{}`,
      "data_kwargs":`{}`,
    };
    (async () => {
      AsyncTokenRequests(Requests_Data)
        .then((Response_Data) => {
          console.log("handlegetMessage 请求成功 Response Data:", Response_Data);
          if (Response_Data.Status === 'Success') {
            setMessageList(Response_Data.Message_Info_list)
            setAlertMessages(Response_Data.Alarm_Info_list)
          } 
        }).catch ((err) => {
          console.log('请求失败',err)
        })
    })();
  };

  const handlesetredMessage = (Message_Type: string) => { 
    let Requests_Data={
      "user_id": "",
      "user_token":"" ,
      "data_class": "Sentinel",
      "data_type": 'System',
      "data_methods": "editor_alarm_message_info",
      "data_argument": `{}`,
      "data_kwargs":JSON.stringify({
        'Message_Type':Message_Type
      }),
    };
    (async () => {
      AsyncTokenRequests(Requests_Data)
        .then((Response_Data) => {
          console.log("handlegetMessage 请求成功 Response Data:", Response_Data);
          if (Response_Data.Status === 'Success') {
            notification.success({
              message: '消息通知',
              description: '全部已读操作执行成功',
              placement: 'topRight',
              duration: 4,
            });
            handlegetMessage();
          } 
        }).catch ((err) => {
          console.log('请求失败',err)
        })
    })();
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    const fetchDataAndSetInterval = () => {
      handlegetMessage(); // 首次立即请求数据
  
      if (intervalId) clearInterval(intervalId); // 清除旧的定时器
  
      intervalId = setInterval(() => {
        handlegetMessage();
      }, 5 * 60 * 1000); // 每 5 分钟执行一次
    };
  
    fetchDataAndSetInterval();
  
    return () => {
      if (intervalId) {
        clearInterval(intervalId); // 组件卸载时清除定时器
        intervalId = null;
        console.log('Header消息定时器已清除');
      }
    };
  }, []);

  // const alertMessages = [
  //   { title: '系统通知：版本更新上线', time: '10分钟前', read: false },
  //   { title: '您有新的审批请求', time: '30分钟前', read: false },
  //   { title: '服务器资源即将耗尽', time: '1小时前', read: true },
  //   { title: '用户反馈提交成功', time: '2小时前', read: true },
  // ];
  
  // const messageList = [
  //   { title: '张三发来新消息', time: '刚刚', read: false },
  //   { title: '李四提到了你', time: '5分钟前', read: false },
  //   { title: '会议将在10分钟后开始', time: '10分钟前', read: false },
  //   { title: '您的任务已分配', time: '1小时前', read: true },
  // ];
  
  const renderMessages = (
    title: string,
    messages: { title: string; time: string; read: boolean }[],
    onMarkAllAsRead: () => void
  ) => {
    return (
      <div style={{
        backgroundColor: '#131a2b', // 主背景色
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)', // 更强的阴影
        border: '1px solid #2d354d',
        color: '#ffffff',
        transition: 'background-color 0.3s ease'
      }}>
        {/* 标题 */}
        <div style={{
          padding: '12px 16px',
          fontSize: 16,
          fontWeight: 'bold',
          borderBottom: '1px solid #2d354d',
          textAlign: 'left',
          color: '#00f0ff' // 强调色标题
        }}>
          {title}
        </div>
  
        {/* 列表 */}
        <div 
          style={{
            maxHeight: 240,
            overflowY: 'auto'
          }}
          className='custom-scrollbar'
        >
          {/* 判断是否有消息 */}
          {messages.length === 0 ? (
            <div style={{
              padding: '16px',
              textAlign: 'center',
              color: '#ffffff',
              fontStyle: 'italic',
              alignItems: 'center',
              backgroundColor: 'transparent',
            }}>
              暂无通知
            </div>
          ) : (
            messages.map((msg, index) => (
              <div
                key={index}
                style={{
                  padding: '8px 12px',
                  borderBottom: '1px solid #2d354d',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: msg.read ? 'transparent' : '#1e263d',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease'
                }}
              >
                <div>
                  <div style={{
                    fontWeight: msg.read ? 'normal' : 'bold',
                    color: '#ffffff'
                  }}>
                    {msg.title}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: '#999999'
                  }}>
                    {msg.time}
                  </div>
                </div>
                {!msg.read && (
                  <span style={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: '#00f0ff' // 使用强调色作为未读提示
                  }} />
                )}
              </div>
            ))
          )}
        </div>
  
        {/* 底部按钮 */}
        <div
          style={{
            padding: '8px 12px',
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#00f0ff',
            cursor: 'pointer',
            transition: 'color 0.2s ease',
            borderTop: '1px solid #2d354d'
          }}
          onClick={onMarkAllAsRead}
        >
          已读全部
        </div>
      </div>
    );
  };

  return (
    <Header style={{ padding: 0 }} className="header-container">
      <Flex style={{width: '100%',marginTop:"0px",padding:"0px"}} justify={"space-between"} align={"center"}>
        <Flex style={{width: '20%',marginTop:"0px",padding:"0px"}} justify={"flex-start"} align={"center"}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
        </Flex>
        <Flex style={{width: '80%',marginTop:"0px",padding:"0px"}} justify={"flex-end"} align={"center"}>
          <div  style={{marginLeft:"30px",padding:"0px"}}  >
            <Dropdown
              overlayStyle={{ width: 300 }}
              dropdownRender={() => (
                <div style={{ 
                  padding: 8, 
                  maxHeight: 400, 
                  overflowY: 'auto',
                }}>
                  {renderMessages('消息通知',messageList,() => handlesetredMessage('Message'))}
                </div>
              )}
              trigger={['hover']}
            >
              <Badge count={messageList.filter(m => !m.read).length} size="small" className="custom-badge">
                <Button
                  type="text"
                  shape="circle"
                  icon= {<MessageOutlined  />}
                  style={{
                    fontSize: 25,
                    color: '#ffffff',
                    backgroundColor: 'transparent',
                    width: 32,
                    height: 32,
                    boxSizing: 'border-box',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s ease',
                    backfaceVisibility: 'hidden',
                    transform: 'translateZ(0)'
                  }}
                />
              </Badge>
            </Dropdown>
          </div>
          <div  style={{marginLeft:"30px",padding:"0px"}}  >
            <Dropdown
              overlayStyle={{ width: 300 }}
              dropdownRender={() => (
                <div style={{ padding: 8, maxHeight: 400, overflowY: 'auto' }}>
                  {renderMessages('预警通知',alertMessages,() => handlesetredMessage('Alarm'))}
                </div>
              )}
              trigger={['hover']}
            >

                <Badge count={alertMessages.filter(m => !m.read).length} size="small" className="custom-badge">
                  <Button
                    type="text"
                    shape="circle"
                    icon= {<BellOutlined  />}
                    style={{
                      fontSize: 25,
                      color: '#ffffff',
                      backgroundColor: 'transparent',
                      width: 32,
                      height: 32,
                      boxSizing: 'border-box',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.2s ease',
                      backfaceVisibility: 'hidden',
                      transform: 'translateZ(0)'
                    }}
                  />
                </Badge>

            </Dropdown>
          </div>
          <div style={{ marginLeft:"30px",padding:"0px"}}>
            {extra}
          </div>
          
          <div  style={{marginLeft:"30px",marginRight:"20px",padding:"0px"}} className="dark-dropdown" >
            <Dropdown 
              menu={{ items: Menu_User }} 
              // trigger={['click','hover']} 
              trigger={['click']} 
              overlayClassName='header-dropdown-menu'
              // className="header-dropdown"
              >
              <Avatar 
                size="large" 
                src={
                  user?.userface ? (
                    <img 
                      src={`${ShowAvatarURL}/${encodeURIComponent(user.userface)}`} 
                      alt="用户头像" 
                    />
                  ) : (
                    <UserOutlined />
                  )
                } 
              />
            </Dropdown>
          </div>
        </Flex>
      </Flex>
    </Header>
  )
}
export default MyHeader
