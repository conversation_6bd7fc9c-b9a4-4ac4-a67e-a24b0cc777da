# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
# from PyQt5.QtGui import QScreen
from PySide6.QtCore import QTimer, QTime, Qt, Slot
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEnginePage
import qtawesome as qta
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.Toolkit import Service_Print
PP    = Service_Print.Service_Print()
PW    = Service_Print.Service_Print('Warning')
PJ    = Service_Print.Service_Print('Json')
PPP   = Service_Print.Service_Print('Json_Time')
# from Bin.System.OS.Resource.CSS import UI_Icons
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog_Video

class Page_Widget_Media_Center(QtWidgets.QWidget):
    Signal_Command = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()

        try:
            self.data = args[0]
            print('Windows_Media_Center init:',self.data)
        except:
            pass
        self.initUI()


    def initUI(self):
        # self.setWindowFlags(self.windowFlags() & ~QtCore.Qt.WindowMaximizeButtonHint)
        # self.setWindowIcon(QtGui.QIcon(u":/rs/System/Logo.ico"))
        # self.setWindowTitle("哨兵系统")
        self.QVBoxLayout = QtWidgets.QVBoxLayout(self)
        self.QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.QVBoxLayout.setSpacing(0)

        self.QLabel_Menu = QtWidgets.QLabel()
        self.QLabel_Menu.setStyleSheet('background:#112040;border:1px solid #002040;')

        self.QLabel_Content  = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet('background:#112040;border:1px solid #002040;')

        self.QVBoxLayout.addWidget(self.QLabel_Menu, 1)
        self.QVBoxLayout.addWidget(self.QLabel_Content, 11)

        self.Set_Menu()
        self.Set_Content()



        # self.Set_PopupDialog_Video()


    def Set_Menu(self):

        self.Hlayout_Menu = QtWidgets.QHBoxLayout(self.QLabel_Menu)  # 水品布局
        self.Hlayout_Menu.setSpacing(0)  # 内边界
        self.Hlayout_Menu.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Menu_Title   = QtWidgets.QLabel()

        self.QLabel_Menu_Function = QtWidgets.QLabel()
        self.QLabel_Menu_Function.setStyleSheet('background-color:transparent')
        #

        self.QLabel_Menu_System = QtWidgets.QLabel()
        self.QLabel_Menu_System.setStyleSheet('background-color:transparent')
        self.QLabel_Menu_System.setFixedSize(200, 200)

        self.Pixmap_Title = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\System\OS\Resource\Image\System\Media_Title_Min.png")
        self.Pixmap_Title.scaled(80, 180)
        # Pixmap.scaledToWidth(40)
        # Pixmap.scaled(picSize, Qt::KeepAspectRatio)

        # self.Label_Menu_Title.setGeometry(0, 0, 300, 200)
        self.QLabel_Menu_Title.setPixmap(self.Pixmap_Title)
        # self.Label_Menu_Title.setScaledContents(True)

        self.Hlayout_Menu.addWidget(self.QLabel_Menu_Title, 1)
        self.Hlayout_Menu.addWidget(self.QLabel_Menu_Function, 5)
        self.Hlayout_Menu.addWidget(self.QLabel_Menu_System, 1)






        QVBoxLayout_System = QtWidgets.QVBoxLayout(self.QLabel_Menu_System)  # 水品布局
        QVBoxLayout_System.setSpacing(0)  # 内边界
        QVBoxLayout_System.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Show_Time_1 = QtWidgets.QLabel()
        self.QLabel_Show_Time_1.setStyleSheet("""QLabel{color:rgb(139,134,78);font-size:12px; background-color: transparent;}""")
        self.QLabel_Show_Time_1.setFixedSize(200, 200)

        self.QPushButton_Test   = QtWidgets.QPushButton(self.QLabel_Show_Time_1)
        self.QPushButton_Test.setGeometry(0, 0, 88, 38)
        self.QPushButton_Test.setStyleSheet("""QPushButton{color:rgb(255,255,255);font-size:12px; background-color: transparent;}""")
        self.QPushButton_Test.setText("弹窗")
        # self.QPush.clicked.connect(self.Set_PopupDialog_Video)
        self.QPushButton_Test.clicked.connect(self.toggle_sidebar)


        self.QLabel_Show_Time= QtWidgets.QLabel()
        # self.QLabel_Show_Time.resize(168, 28)
        self.QLabel_Show_Time.setStyleSheet("""QLabel{color:rgb(255,255,255);font-size:15px; background-color: transparent;}""")
        self.QLabel_Show_Time.setText(str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))

        QVBoxLayout_System.addWidget(self.QLabel_Show_Time,1)
        QVBoxLayout_System.addWidget(self.QLabel_Show_Time_1,1)

        # 设置计时器



        self.QTimer_Count = QtCore.QTimer(self)
        self.QTimer_Count.timeout.connect(self.Update_Timer_Date)
        self.QTimer_Count.start(1000)  # 每秒更新一次








    def Set_Content(self):
        HLayout = QtWidgets.QHBoxLayout(self)
        HLayout.setContentsMargins(0, 0, 0, 0)
        HLayout.setSpacing(0)
        # Page_Service_Intelligence_Article
        self.QLabel_Content.setLayout(HLayout)


        # Url = "https://home.csc-tech.cn:8746"
        Url = "http://***************:8080/Page_Login"
        # Url = "http://localhost:8080"
        # Url = "http://127.0.0.1:3000/Page_Media_Center"
        # Url = "http://***************:3000/Page_Media_Gis"
        # Url = "http://***************:3000/Page_Index"




        self.Browser_Location = QWebEngine_Browser(Url)
        # self.Browser_Location.loadFinished.connect(self.on_load_finished)
        # 注入额外的 JavaScript 代码 alert('JavaScript code injected!'); document.getElementById("account").value = "********";
        # self.web_page = CommunicationPage()
        # self.Browser_Location.setPage(self.web_page)





        HLayout.addWidget(self.Browser_Location)

        Width = 380
        Height = 980
        PP(("Height",Width,Height))
        self.Sidebar_Left   = Sidebar_Left(self.QLabel_Content,{"Width":Width,"Height":Height})
        self.Sidebar_Right  = Sidebar_Right(self.QLabel_Content,{"Width":Width,"Height":Height})

        # screen = QDesktopWidget().screenGeometry()
        # PW((screen.width(), screen.height()))
        # self.Browser_Location.resize(screen.width(), screen.height())
        # HLayout.addWidget(self.Browser_Location,1)
        # self.Browser_Location.show()
        # Url= "http://*************:9311/Service_Page?Page=Page_Chat_ChatGPT4_APP"
        # self.Web_Server = QWebEngine_Browser(self,Url)
        # self.Web_Server.show()


    # def on_load_finished(self):
    #
    #     print("on_load_finished")
    #     self.browser.page().runJavaScript(
    #
    #         """
    #
    #     document.getElementById('account').value = '页11111'
    #
    #
    #
    #
    #             """)
#
#
#
    def toggle_sidebar(self):

        payload = {
            "timestamp": "2023-08-04T12:34:56",
            "status": "success",
            "values": [42, 3.14, "hello"],
            "nested": {
                "key": "value"
            }
        }

        # 发送数据到前端
        self.Browser_Location.Send()

        # if self.Sidebar_Left.isVisible():
        #     self.Sidebar_Left.hide_sidebar()
        # else:
        #     self.Sidebar_Left.show_sidebar()
        #
        # if self.Sidebar_Right.isVisible():
        #     self.Sidebar_Right.hide_sidebar()
        # else:
        #     self.Sidebar_Right.show_sidebar()

    def Set_PopupDialog_Video(self):
        screen_pos = self.mapToGlobal(self.rect().center())

        # if Dialog in ["About"]:
        #     popup = PopupDialog_About(self)
        # else:
        # from Page_Utils import Utils_PopupDialog
        popup = Utils_PopupDialog_Video.PopupDialog_Video(self)
        popup.resize(400, 300)
        # 设置弹出窗口的位置
        popup.move(screen_pos - QtCore.QPoint(popup.width() // 2, popup.height() // 2 + 20))
        popup.show()





    @Slot()
    def Update_Timer_Date(self):

        self.QLabel_Show_Time.setText( str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))
class CommunicationPage(QWebEnginePage):
    def send_to_frontend(self, data):
        # 直接执行前端预定义的 JavaScript 函数
        self.runJavaScript(f"window.receivePythonData({data!r})")

class CustomWebPage(QWebEnginePage):
    def acceptNavigationRequest(self, url, type, isMainFrame):
        if url.scheme() == "pyapp":
            print(f"收到按钮事件: {url.host()}/{url.path()}")
            return False  # 阻止实际导航
        return super().acceptNavigationRequest(url, type, isMainFrame)

    def send_to_frontend(self, data):
        # 直接执行前端预定义的 JavaScript 函数
        self.runJavaScript(f"window.receivePythonData({data!r})")

class QWebEngine_Browser(QWebEngineView):
    def __init__(self, *args):
        super().__init__()
        pass
        self.Url =args[0]
        self.Set_UI()
    def Set_UI(self):
        # self.resize(900,700)
        # self.load(QUrl("https://mapv.baidu.com/gl/examples/editor.html#effects-bloom.html"))
        # self.load(QUrl(r"D:/ProjectsCode/PyCharm/textSelect/MapWeb/1.html"))
        # screen = QtCore.QDesktopWidget().screenGeometry()
        # PW((screen.width(), screen.height()))
        # self.resize(screen.width(), screen.height())
        # self.load(QUrl(r"http://127.0.0.1:9333/Page_Location_Track"))
        self.custom_page = CustomWebPage(self)
        self.setPage(self.custom_page)
        self.load(QtCore.QUrl(self.Url))
        # self.load(QUrl(r"http://**************:9333/Page_Track_Map"))
        self.show()
        # pass
    # def setPage(self,web_page):
    #
    #     self.web_page = web_page
    #     pass


    def Send(self):
        payload = {
            "timestamp": "2023-08-04T12:34:56",
            "status": "success",
            "values": [42, 3.14, "hello"],
            "nested": {
                "key": "value"
            }
        }

        # 发送数据到前端
        self.custom_page.send_to_frontend(payload)


# from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QPushButton, QWidget, QHBoxLayout
# from PySide6.QtCore import Qt, QPropertyAnimation, QRect
# from PySide6.QtGui import QColor
# from PySide6.QtGui import QPainter, QPen
# from PySide6.QtWidgets import QMainWindow, QApplication
from PySide6.QtCharts import QChart, QChartView, QPieSeries
from PySide6.QtCharts import (QBarCategoryAxis, QBarSeries, QBarSet, QChart,
                              QChartView, QLineSeries, QValueAxis)
from PySide6.QtCharts import (QBarCategoryAxis, QBarSet, QChart, QChartView,
                              QStackedBarSeries, QValueAxis)

from PySide6.QtCore import Qt
from PySide6.QtGui import QPainter
from PySide6.QtWidgets import QMainWindow, QApplication
from PySide6.QtCharts import (QBarCategoryAxis, QBarSet, QChart, QChartView,
                              QStackedBarSeries, QValueAxis)
class Sidebar_Left(QtWidgets.QWidget):
    def __init__(self, parent=None,*args):
        super().__init__(parent)
        self.setWindowFlags(QtCore.Qt.FramelessWindowHint)
        # self.setAttribute(QtCore.Qt.WA_TranslucentBackground, False)  # 如果需要背景透明则设置为True，但这里我们要设置颜色
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground, True)  # 如果需要背景透明则设置为True，但这里我们要设置颜色
        self.setStyleSheet("background:transparent")
        # self.setStyleSheet("background-color: purple;")
        self.setFixedSize(int(args[0]["Width"]), args[0]["Height"])  # 侧边栏宽度和高度
        self.hide()  # 初始隐藏
        # {"Width": Width, "Height": Height}
        # 侧边栏内容，这里是一个QLabel











        # self.label.setAlignment(QtCore.Qt.AlignCenter)

        # 动画属性
        self.animation = QtCore.QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)  # 动画持续时间


        self.Set_Content()

    def Set_Content(self):

        QVBoxLayout_Left = QtWidgets.QVBoxLayout(self)  # 水品布局
        QVBoxLayout_Left.setSpacing(0)  # 内边界
        QVBoxLayout_Left.setContentsMargins(0, 0, 0, 0)  # 外边






        self.QLabel_Left_1 =QtWidgets.QLabel()
        self.QLabel_Left_1.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")


        self.QLabel_Left_2 = QtWidgets.QLabel( )
        self.QLabel_Left_2.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")


        self.QLabel_Left_3 = QtWidgets.QLabel()
        self.QLabel_Left_3.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")

        QVBoxLayout_Left.addWidget(self.QLabel_Left_1,1)
        QVBoxLayout_Left.addWidget(self.QLabel_Left_2,1)
        QVBoxLayout_Left.addWidget(self.QLabel_Left_3,1)

        self.Set_1()
        self.Set_2()
        self.Set_3()





    def Set_1(self):
        # 创建饼图序列QPieSeries

        QHBoxLayout_Left_1 = QtWidgets.QHBoxLayout(self.QLabel_Left_1)  # 水品布局
        QHBoxLayout_Left_1.setSpacing(10)  # 内边界
        QHBoxLayout_Left_1.setContentsMargins(0, 0, 0, 0)  # 外边

        #
        # self.series_1 = QPieSeries()
        # self.series_2 = QPieSeries()
        #
        # # 向QPieSeries中添加切片 (3种append方法之一：直接传入标签和数值)
        # self.series_1.append('Jane', 1)
        # self.series_1.append('Joe', 2)
        # self.series_1.append('Andy', 3)
        # self.series_1.append('Barbara', 4)
        # self.series_1.append('Axel', 5)
        #
        #
        #
        # self.series_2.append('Jane', 1)
        # self.series_2.append('Joe', 2)
        # self.series_2.append('Andy', 3)
        #
        #
        #
        # # 设置第二个切片QPieSlice的格式
        # self.slice = self.series_1.slices()[1]
        # # 使其分离饼图中心 (该方法的形参exploded默认为True)
        # self.slice.setExploded()
        # # 使标签可见 (该方法的形参visible默认为True)
        # self.slice.setLabelVisible()
        # # 设置该切片的轮廓颜色
        # self.slice.setPen(QtGui.QPen(QtCore.Qt.darkGreen, 2))
        # # 设置该切片的内部填充颜色
        # self.slice.setBrush(QtCore.Qt.green)
        #
        # self.chart = QChart()
        # self.chart.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        # # self.chart.setStyleSheet("background:transparent")
        # self.chart.addSeries(self.series_1)
        # self.chart.addSeries(self.series_2)
        # self.chart.setTitle('Simple piechart example')
        # self.chart.legend().hide()

        # 创建第一个饼图系列和图表
        self.series_1 = QPieSeries()
        self.series_1.append('Jane', 1)
        self.series_1.append('Joe', 2)
        self.series_1.append('Andy', 3)
        self.series_1.append('Barbara', 4)
        self.series_1.append('Axel', 5)

        slice_1 = self.series_1.slices()[0]
        # slice_1.setExploded()
        # slice_1.setLabelVisible()
        slice_1.setPen(QtGui.QPen(QtCore.Qt.darkGreen, 2))
        slice_1.setBrush(QtCore.Qt.green)



        # colors = [QtGui.QColor(255, 0, 0), QtGui.QColor(0, 255, 0), QtGui.QColor(0, 0, 255), QtGui.QColor(255, 255, 0), QtGui.QColor(255, 0, 255)]
        # self.series_1.setSliceColors(colors)


        self.chart_1 = QChart()
        self.chart_1.setMargins(QtCore.QMargins(-68,0,0,0))
        self.chart_1.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        self.chart_1.addSeries(self.series_1)
        self.chart_1.setTitle('Pie Chart 1')
        self.chart_1.legend().hide()

        # 创建图表视图
        self.chart_view_1 = QChartView(self.chart_1)
        self.chart_view_1.setFixedSize(180, 180)
        self.chart_view_1.setStyleSheet("""QChartView {color:rgba(255,255,255,0);font-size:12px;border:0px; background-color: transparent;}""")
        self.chart_view_1.setRenderHint(QtGui.QPainter.Antialiasing)



        # 创建第二个饼图系列和图表
        self.series_2 = QPieSeries()
        self.series_2.append('Jane', 1)
        self.series_2.append('Joe', 2)
        self.series_2.append('Andy', 3)

        self.chart_2 = QChart()
        self.chart_2.setMargins(QtCore.QMargins(-68, 0, 0, 0))
        self.chart_2.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        self.chart_2.addSeries(self.series_2)
        self.chart_2.setTitle('Pie Chart 2')
        self.chart_2.legend().hide()




        self.chart_view_2 = QChartView(self.chart_2)
        self.chart_view_2.setFixedSize(180, 180)
        self.chart_view_2.raise_()
        self.chart_view_2.setStyleSheet("""QChartView {color:rgba(255,255,255,0);font-size:12px;border:0px; background-color: transparent;}""")
        self.chart_view_2.setRenderHint(QtGui.QPainter.Antialiasing)

        # 创建第二个饼图系列和图表
        self.series_3 = QPieSeries()
        self.series_3.append('Jane', 1)
        self.series_3.append('Joe', 2)
        self.series_3.append('Andy', 3)

        self.chart_3 = QChart()
        self.chart_3.setMargins(QtCore.QMargins(-68, 0, 0, 0))
        self.chart_3.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        self.chart_3.addSeries(self.series_3)
        self.chart_3.setTitle('Pie Chart 2')
        self.chart_3.legend().hide()

        self.chart_view_3 = QChartView(self.chart_3)
        self.chart_view_3.setFixedSize(180, 180)
        self.chart_view_3.setStyleSheet("""QChartView {color:rgba(255,255,255,0);font-size:12px;border:0px; background-color: transparent;}""")
        self.chart_view_3.setRenderHint(QtGui.QPainter.Antialiasing)




        # self._chart_view = QChartView(self.chart)
        # self._chart_view.setStyleSheet("background:transparent")
        # self._chart_view.setRenderHint(QtGui.QPainter.Antialiasing)

        # self.QLabel_Left_1.setCentralWidget(self._chart_view)

        QHBoxLayout_Left_1.addWidget(self.chart_view_1,1)
        QHBoxLayout_Left_1.addWidget(self.chart_view_2,1)
        QHBoxLayout_Left_1.addWidget(self.chart_view_3,1)

    def Set_2(self):
        QHBoxLayout_Left_2 = QtWidgets.QHBoxLayout(self.QLabel_Left_2)  # 水品布局
        QHBoxLayout_Left_2.setSpacing(0)  # 内边界
        QHBoxLayout_Left_2.setContentsMargins(0, 0, 0, 0)  # 外边
        self.set0 = QBarSet("Jane")
        self.set1 = QBarSet("John")
        self.set2 = QBarSet("Axel")
        self.set3 = QBarSet("Mary")
        self.set4 = QBarSet("Sam")

        self.set0.append([1, 2, 3, 4, 5, 6])
        self.set1.append([5, 0, 0, 4, 0, 7])
        self.set2.append([3, 5, 8, 13, 8, 5])
        self.set3.append([5, 6, 7, 3, 4, 5])
        self.set4.append([9, 7, 5, 3, 1, 2])

        self._bar_series = QBarSeries()
        self._bar_series.append(self.set0)
        self._bar_series.append(self.set1)
        self._bar_series.append(self.set2)
        self._bar_series.append(self.set3)
        self._bar_series.append(self.set4)

        self._line_series = QLineSeries()
        self._line_series.setName("trend")
        self._line_series.append(QtCore.QPoint(0, 4))
        self._line_series.append(QtCore.QPoint(1, 15))
        self._line_series.append(QtCore.QPoint(2, 20))
        self._line_series.append(QtCore.QPoint(3, 4))
        self._line_series.append(QtCore.QPoint(4, 12))
        self._line_series.append(QtCore.QPoint(5, 17))

        self.chart = QChart()
        self.chart.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        self.chart.addSeries(self._bar_series)
        self.chart.addSeries(self._line_series)
        self.chart.setTitle("Line and barchart example")

        self.categories = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
        self._axis_x = QBarCategoryAxis()
        self._axis_x.append(self.categories)
        self.chart.addAxis(self._axis_x, QtCore.Qt.AlignBottom)
        self._line_series.attachAxis(self._axis_x)
        self._bar_series.attachAxis(self._axis_x)
        self._axis_x.setRange("Jan", "Jun")

        self._axis_y = QValueAxis()
        self.chart.addAxis(self._axis_x, Qt.AlignLeft)
        self._line_series.attachAxis(self._axis_y)
        self._bar_series.attachAxis(self._axis_y)
        self._axis_y.setRange(0, 20)

        self.chart.legend().setVisible(True)
        self.chart.legend().setAlignment(Qt.AlignBottom)

        self._chart_view = QChartView(self.chart)
        self._chart_view.setStyleSheet("""QChartView {color:rgba(255,255,255,1);font-size:8px;border:0px; background-color: transparent;}""")
        self._chart_view.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        QHBoxLayout_Left_2.addWidget(self._chart_view)


    def Set_3(self):
        QVBoxLayout_Left_3 = QtWidgets.QVBoxLayout(self.QLabel_Left_3)  # 水品布局
        QVBoxLayout_Left_3.setSpacing(0)  # 内边界
        QVBoxLayout_Left_3.setContentsMargins(0, 0, 0, 0)  # 外边
        w_1= PlotWidget()
        w_1.setStyleSheet("""QWidget {border:0px; background-color:rgba(0,0,0,0.0);}""")
        w_2= PlotWidget()
        # w_3= PlotWidget()
        # w_4= PlotWidget()
        # w.show()
        QVBoxLayout_Left_3.addWidget(w_1)
        QVBoxLayout_Left_3.addWidget(w_2)
        # QVBoxLayout_Left_3.addWidget(w_3)
        # QVBoxLayout_Left_3.addWidget(w_4)


    def show_sidebar(self):
        # 计算动画的起始和结束值

        PP(("Start",-self.width(), 0, self.width(), self.height()))
        PP(("End",0, 0, self.width(), self.height()))
        # ('Start', -380, 0, 380, 1080)

        # ('End', 0, 0, 380, 1080)

        start_rect = QtCore.QRect(-self.width(), 0, self.width(), self.height())
        end_rect =  QtCore.QRect(0, 0, self.width(), self.height())
        self.animation_Show =  QtCore.QPropertyAnimation(self, b"geometry")
        self.animation_Show.setDuration(300)  # 动画持续时间
        self.animation_Show.setStartValue(start_rect)
        self.animation_Show.setEndValue(end_rect)
        self.animation_Show.start()
        # self.animation.finished.connect(self.show)
        self.show()

    def hide_sidebar(self):
        # 计算动画的起始和结束值
        start_rect =  QtCore.QRect(0, 0, self.width(), self.height())
        end_rect =  QtCore.QRect(-self.width(), 0, self.width(), self.height())

        self.animation.setStartValue(start_rect)
        self.animation.setEndValue(end_rect)
        self.animation.start()
        self.animation.finished.connect(self.hide)

from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget
from PySide6.QtGui import QImage, QPixmap
from PySide6.QtCore import QTimer, Qt
from PySide6.QtCore import QThread, Signal, Slot
import cv2
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QScrollArea, QScrollBar
from PySide6.QtCore import Qt, QTimer, QRect, QPoint

class Sidebar_Right(QtWidgets.QWidget):
    def __init__(self, parent=None,*args):
        super().__init__(parent)
        self.setWindowFlags(QtCore.Qt.FramelessWindowHint)
        # self.setAttribute(QtCore.Qt.WA_TranslucentBackground, False)  # 如果需要背景透明则设置为True，但这里我们要设置颜色
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground, True)  # 如果需要背景透明则设置为True，但这里我们要设置颜色
        self.setStyleSheet("background:transparent")
        # self.setStyleSheet("background-color: purple;")
        self.setFixedSize(int(args[0]["Width"]), args[0]["Height"])  # 侧边栏宽度和高度
        self.hide()  # 初始隐藏
        # {"Width": Width, "Height": Height}
        # 侧边栏内容，这里是一个QLabel











        # self.label.setAlignment(QtCore.Qt.AlignCenter)

        # 动画属性
        self.animation = QtCore.QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)  # 动画持续时间


        self.Set_Content()

    def Set_Content(self):

        QVBoxLayout_Right = QtWidgets.QVBoxLayout(self)  # 水品布局
        QVBoxLayout_Right.setSpacing(0)  # 内边界
        QVBoxLayout_Right.setContentsMargins(0, 0, 0, 0)  # 外边


        self.QLabel_Right_1 =QtWidgets.QLabel()
        self.QLabel_Right_1.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")


        self.QLabel_Right_2 = QtWidgets.QLabel( )
        self.QLabel_Right_2.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")


        self.QLabel_Right_3 = QtWidgets.QLabel()
        self.QLabel_Right_3.setStyleSheet("""QLabel {border:0px; background-color:rgba(0,0,0,0.3);}""")

        QVBoxLayout_Right.addWidget(self.QLabel_Right_1,1)
        QVBoxLayout_Right.addWidget(self.QLabel_Right_2,1)
        QVBoxLayout_Right.addWidget(self.QLabel_Right_3,1)

        self.Set_1()
        self.Set_2()
        self.Set_3()
    #
    # def show_sidebar(self):
    #     # 动画从屏幕右侧开始，到完全显示结束
    #     start_rect = QtCore.QRect(self.width(), 0, self.width(), self.height())  # 起始位置在屏幕右侧外
    #     end_rect = QtCore.QRect(0, 0, self.width(), self.height())  # 结束位置完全覆盖屏幕左侧
    #
    #     self.animation_show = QtCore.QPropertyAnimation(self, b"geometry")
    #     self.animation_show.setDuration(300)  # 动画持续时间
    #     self.animation_show.setStartValue(start_rect)
    #     self.animation_show.setEndValue(end_rect)
    #     self.animation_show.start()
    #
    #     # 注意：通常不需要在这里调用 self.show()，因为侧边栏应该已经是可见的（或者至少是可访问的）
    #     # 如果你在动画开始前隐藏了侧边栏，可以在动画开始前调用 self.show()
    #
    # def hide_sidebar(self):
    #     # 动画从完全显示开始，到屏幕右侧结束
    #     start_rect = QtCore.QRect(0, 0, self.width(), self.height())  # 起始位置完全覆盖屏幕左侧
    #     end_rect = QtCore.QRect(self.width(), 0, self.width(), self.height())  # 结束位置在屏幕右侧外
    #
    #     # 注意：你需要确保这里使用的是正确的动画对象名称（之前你使用了 self.animation，但现在应该是 self.animation_hide）
    #     self.animation_hide = QtCore.QPropertyAnimation(self, b"geometry")  # 确保创建一个新的动画对象或重用之前的对象（如果已正确初始化）
    #     self.animation_hide.setDuration(300)  # 动画持续时间
    #     self.animation_hide.setStartValue(start_rect)
    #     self.animation_hide.setEndValue(end_rect)
    #     self.animation_hide.start()
    #     self.animation_hide.finished.connect(self.hide)

    def Set_1(self):
        QHBoxLayout_Right_3 = QtWidgets.QHBoxLayout(self.QLabel_Right_1)  # 水品布局
        QHBoxLayout_Right_3.setSpacing(0)  # 内边界
        QHBoxLayout_Right_3.setContentsMargins(0, 0, 0, 0)  # 外边
        low = QBarSet("Min")
        high = QBarSet("Max")
        low.append([-52, -50, -45.3, -37.0, -25.6, -8.0,
                    -6.0, -11.8, -19.7, -32.8, -43.0, -48.0])
        high.append([11.9, 12.8, 18.5, 26.5, 32.0, 34.8,
                     38.2, 34.8, 29.8, 20.4, 15.1, 11.8])

        series = QStackedBarSeries()
        series.append(low)
        series.append(high)

        chart = QChart()
        chart.setBackgroundBrush(QtGui.QBrush(QtCore.Qt.transparent))
        chart.addSeries(series)
        chart.setTitle("Temperature records in celcius")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        categories = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul",
                      "Aug", "Sep", "Oct", "Nov", "Dec"]
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        axis_x.setTitleText("Month")
        chart.addAxis(axis_x, Qt.AlignBottom)
        axis_y = QValueAxis()
        axis_y.setRange(-52, 52)
        axis_y.setTitleText("Temperature [&deg;C]")
        chart.addAxis(axis_y, Qt.AlignLeft)
        series.attachAxis(axis_x)
        series.attachAxis(axis_y)

        chart.legend().setVisible(True)
        chart.legend().setAlignment(Qt.AlignBottom)

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)

        QHBoxLayout_Right_3.addWidget(chart_view)


    def Set_2(self):
        QHBoxLayout_Right_2 = QtWidgets.QHBoxLayout(self.QLabel_Right_2)  # 水品布局
        QHBoxLayout_Right_2.setSpacing(0)  # 内边界
        QHBoxLayout_Right_2.setContentsMargins(0, 0, 0, 0)  # 外边
        self.video_source = ""
        self.video_label = QLabel()
        self.video_label.setGeometry(10, 10, 640, 380)
        self.video_label.setFixedSize(640, 380)

        self.stop_button = QtWidgets.QPushButton('Stop', self)
        self.stop_button.setGeometry(0, 0, 100, 30)
        self.stop_button.clicked.connect(self.stop_video)

        # self.video_thread = VideoCaptureThread(self.video_source)
        # self.video_thread.frame_updated.connect(self.update_frame)
        # self.video_thread.start()

        QHBoxLayout_Right_2.addWidget(self.video_label)

    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()

    @Slot(QImage)
    def update_frame(self, frame):
        self.video_label.setPixmap(QPixmap.fromImage(frame))


    def Set_3(self):
        content = QWidget()
        content_layout = QVBoxLayout(content)
        for i in range(20):
            # Add 20 labels as content
            NowTime=str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
            label = QLabel(f'{NowTime}   新消息 {i + 1}')
            label.setStyleSheet('background-color: rgba(255,255,255,0.3); padding: 10px; margin: 3px 0;color:rgb(255,255,255)')
            content_layout.addWidget(label)

        # Create a scroll area and set the content widget
        scroll_area = QScrollArea(self.QLabel_Right_3)
        scroll_area.setWidget(content)
        scroll_area.verticalScrollBar().setFixedWidth(0)
        scroll_area.horizontalScrollBar().setFixedHeight(0)
        scroll_area.setWidgetResizable(True)

        # Set the scroll area as the central widget of the main window
        layout = QVBoxLayout(self.QLabel_Right_3)
        layout.addWidget(scroll_area)

        # Get the vertical scroll bar of the scroll area
        self.vertical_bar = scroll_area.verticalScrollBar()
        self.vertical_bar.setValue(self.vertical_bar.maximum())
        # Connect a timer to a slot to create the rolling effect
        self.timer = QTimer(self.QLabel_Right_3)
        self.timer.timeout.connect(self.roll_up)
        self.timer.start(500)  # Start the timer with 100ms interval

        self.timer_update = QTimer(self.QLabel_Right_3)
        self.timer_update.timeout.connect(self.roll_restart)
        self.timer_update.start(20000)  # Start the

    def roll_up(self):
        # Get the current value of the scroll bar
        current_value = self.vertical_bar.value()

        # Define the step size for each scroll (you can adjust this value)
        step = 10

        # Calculate the new value, ensuring it doesn't go below 0
        new_value = max(0, current_value - step)

        # Set the new value of the scroll bar (this will trigger the scrolling)
        self.vertical_bar.setValue(new_value)

        # Optionally, you can add an animation using QPropertyAnimation
        # Here's an example of how you might do that:
        # (Note: This part is optional and may require further refinement)
        # animation = QPropertyAnimation(self.vertical_bar, b'value')
        # animation.setDuration(100)  # Animation duration in milliseconds
        # animation.setStartValue(current_value)
        # animation.setEndValue(new_value)
        # animation.setEasingCurve(QEasingCurve.OutQuad)
        # animation.start()

    def roll_restart(self):
        self.vertical_bar.setValue(self.vertical_bar.maximum())



    def show_sidebar(self):
        # 计算动画的起始和结束值
        # ('Start', -380, 0, 380, 1080)

        # ('End', 0, 0, 380, 1080)
        start_rect = QtCore.QRect(1920, 0, self.width(), self.height())
        end_rect =  QtCore.QRect(1920-self.width(), 0, self.width(), self.height())
        self.animation_Show =  QtCore.QPropertyAnimation(self, b"geometry")
        self.animation_Show.setDuration(300)  # 动画持续时间
        self.animation_Show.setStartValue(start_rect)
        self.animation_Show.setEndValue(end_rect)
        self.animation_Show.start()
        # self.animation.finished.connect(self.show)
        self.show()

    def hide_sidebar(self):
        # 计算动画的起始和结束值
        # start_rect =  QtCore.QRect(0, 0, self.width(), self.height())
        # end_rect =  QtCore.QRect(-self.width(), 0, self.width(), self.height())
        start_rect =  QtCore.QRect(1920-self.width(), 0, self.width(), self.height())
        end_rect =  QtCore.QRect(1920, 0, self.width(), self.height())
        self.animation.setStartValue(start_rect)
        self.animation.setEndValue(end_rect)
        self.animation.start()
        self.animation.finished.connect(self.hide)




import math
import sys

from PySide6.QtWidgets import QWidget, QApplication
from PySide6.QtCore import QPoint, QRect, QTimer, Qt
from PySide6.QtGui import QPainter, QPointList,QPen,QColor





class PlotWidget(QWidget):
    """Illustrates the use of opaque containers. QPointList
       wraps a C++ QList<QPoint> directly, removing the need to convert
       a Python list in each call to QPainter.drawPolyline()."""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.WIDTH = 680
        self.HEIGHT = 90
        self.setStyleSheet("""border:0px; background-color:rgba(0,0,0,0.0)""")

        self._timer = QTimer(self)
        self._timer.setInterval(100)
        self._timer.timeout.connect(self.shift)

        self._points = QPointList()
        self._points.reserve(self.WIDTH)
        self._x = 0
        self._delta_x = 0.05
        self._half_height = self.HEIGHT / 2
        self._factor = 0.8 * self._half_height

        for i in range(self.WIDTH):
            self._points.append(QPoint(i, self.next_point()))

        self.setFixedSize(self.WIDTH, self.HEIGHT)

        self._timer.start()

    def next_point(self):
        result = self._half_height - self._factor * math.sin(self._x)
        self._x += self._delta_x
        return result

    def shift(self):
        last_x = self._points[self.WIDTH - 1].x()
        self._points.pop_front()
        self._points.append(QPoint(last_x + 1, self.next_point()))
        self.update()

    def paintEvent(self, event):
        with QPainter(self) as painter:
            rect = QRect(QPoint(0, 0), self.size())
            pen = QPen(QColor(255, 0, 0))  # 红色
            pen.setWidth(2)
            painter.setPen(pen)
            painter.fillRect(rect, Qt.transparent)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.translate(-self._points[0].x(), 0)
            painter.drawPolyline(self._points)





class VideoCaptureThread(QtCore.QThread):
    frame_updated = QtCore.Signal(QImage)

    def __init__(self, video_source):
        super().__init__()
        # self.video_source = "rtsp://admin:csc888888!@192.168.123.119:554/Streaming/Channels/101"
        # self.video_source = "http://132.232.201.7/live/livestream.flv"
        self.video_source = r"D:\Sentinel Foundation\Bin\System\OS\Page\Page_Utils\1.mp4"
        self.video_source = r"http://43.136.176.127:9311/rtp/34020000001320000003_34020000001310000103.live.flv?originTypeStr=rtp_push"
        self.video_source = r"rtsp://admin:csc2024!@192.168.123.199:554/h264/ch1/main/av_stream"
        # self.video_source = "http://43.136.176.127:9311/rtp/34020000002000000001_34020000001320000001/hls.m3u8"
        # self.video_source = "http://132.232.201.7/live/livestream.flv"
        # self.video_source = 0
        self.cap = cv2.VideoCapture( self.video_source)
        self.running = True

    def run(self):
        while self.running and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break
            # Convert the frame from BGR to RGB format
            frame_resized = cv2.resize(frame, (640, 380))
            rgb_frame = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            # Convert the frame to QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            # convert_to_Qt_format = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            convert_to_Qt_format = QImage(rgb_frame.data, 640, 380, bytes_per_line, QImage.Format_RGB888)
            # Emit the frame to the main thread
            qimg_resized = convert_to_Qt_format.scaled(640, 380, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.frame_updated.emit(qimg_resized)

    def stop(self):
        self.running = False
        self.cap.release()


class VideoPlayer(QWidget):
    def __init__(self, video_source):
        super().__init__()
        self.video_source = video_source
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Video Player')
        self.setGeometry(0, 0, 320, 240)

        self.video_label = QLabel(self)
        self.video_label.setGeometry(0, 0, 320, 240)

        self.stop_button = QtWidgets.QPushButton('Stop', self)
        self.stop_button.setGeometry(0, 0, 100, 30)
        self.stop_button.clicked.connect(self.stop_video)

        self.video_thread = VideoCaptureThread(self.video_source)
        self.video_thread.frame_updated.connect(self.update_frame)
        self.video_thread.start()

    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()

    @Slot(QImage)
    def update_frame(self, frame):
        # self.video_label.setPixmap(QPixmap.fromImage(frame))
        pix = QPixmap.fromImage(frame)
        self.video_label.setPixmap(pix.scaled(320,240))





if __name__ == '__main__':
    App = QApplication(sys.argv)
    __Windows_Media_Center = Windows_Media_Center()
    __Windows_Media_Center.resize(1920,1080)
    __Windows_Media_Center.show()
    sys.exit(App.exec())
