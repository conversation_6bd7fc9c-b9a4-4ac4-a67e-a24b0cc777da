.translate-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls {
  display: flex;
  gap: 10px;
}

.SwitchBotton {
  background-color: transparent;
  border: none;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CheckboxInfo {
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  height: 32px
}

.TranslateButton {
  background-color: #4d5566 !important;
  color: white !important;
  border-color: #4d5566 !important;
  border-radius: 6px !important;
}

.TranslateButton:hover {
  background-color: #6e7687 !important;
  border-color: #6e7687 !important;
  color: white !important;
}

.InputText {
  height: 500px !important;
  width: 50% !important;
  background-color: #2d3748 !important;
  color: white !important; 
}

.InputText.ant-input::placeholder {
  color: #a0aec0 !important;
}

.OutputText {
  height: 500px !important;
  width: 100% !important;
  background-color: #2d3748 !important;
  color: white !important; 
}

.OutputDiv {
  height: 500px !important;
  width: 100% !important;
  line-height: 1.5714285714285714;
  overflow: auto;
  background-color: #4a5568 !important;
  color: white !important;
  border-color: #718096 !important;
  padding: 11px 11px;
  border-radius: 6px;
  white-space: pre-line;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.actions {
  display: flex;
  gap: 10px;
  position: absolute;
  bottom: 0;
  right: 13px;
  z-index: 10;
  padding: 6px;
  border-radius: 4px
}

.custom-select .ant-select-selector {
  background-color: transparent !important;
  color: white !important;
  box-shadow: none !important;
  border: 1px solid #4d5566;
}

.custom-select .ant-select-arrow {
  color: white !important;
}

.custom-select .ant-select-selection-item {
  color: white !important;
}

.custom-select .ant-select-selection-search-input {
  color: white !important;
}


/* 复制/导出/切换状态按钮样式 */
.green-bordered-button {
  background-color: transparent !important;
  border-color: #4ade80 !important;
  color: white !important;
}

.green-bordered-button:hover {
  background-color: transparent !important;
  border-color: #4ade80 !important;
  color: white !important;
}

.bright-loading {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}

.dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4fa9f1, #5ad4f0);
  box-shadow: 0 0 8px rgba(79, 169, 241, 0.6), 0 0 16px rgba(79, 169, 241, 0.4);
  animation: pulse 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}