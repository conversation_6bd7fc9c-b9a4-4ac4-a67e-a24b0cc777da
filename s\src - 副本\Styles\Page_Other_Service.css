/* 其他服务查询页面样式 */
.other-service-page {
  padding: 24px;
  min-height: 100vh;
}

/* 信用点显示区域 */
.credit-display {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.credit-button {
  background: #52c41a !important;
  border-color: #52c41a !important;
  font-size: 16px;
  font-weight: bold;
  height: 48px;
  padding: 0 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.credit-button:hover {
  background: #73d13d !important;
  border-color: #73d13d !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
}

/* 操作卡片 */
.other-service-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 表格容器 */
.table-responsive {
  overflow-x: auto;
}

.table-responsive .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.table-responsive .ant-table-thead > tr > th {

  font-weight: 600;
  text-align: center;
}

.table-responsive .ant-table-tbody > tr > td {
  text-align: center;
  padding: 12px 16px;
}

.table-responsive .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.ant-btn-small {
  padding: 4px 12px;
  height: auto;
  font-size: 12px;
}

/* 信用点数字样式 */
.credit-amount {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #52c41a;
}

/* 工单内容样式 */
.work-content {
  max-width: 200px;
  word-break: break-all;
  white-space: normal;
}

/* 状态标签特殊样式 */
.status-waitting {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #595959;
}

.status-inline {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.status-active {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.status-finish {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.status-recharge {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #595959;
}

.status-failed {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 8px 8px;
}

.ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

/* 描述列表样式 */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background: #fafafa;
  font-weight: 600;
}

.ant-descriptions-bordered .ant-descriptions-item-content {
  background: #fff;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 空间组件样式 */
.ant-space {
  width: 100%;
}

.ant-space-item {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .other-service-page {
    padding: 16px;
  }
  
  .credit-display {
    justify-content: center;
    margin-bottom: 12px;
  }
  
  .credit-button {
    font-size: 14px;
    height: 40px;
    padding: 0 16px;
  }
  
  .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .table-responsive .ant-table {
    font-size: 12px;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .work-content {
    max-width: 150px;
  }
  
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .ant-modal-content {
    max-height: calc(100vh - 32px);
    overflow-y: auto;
  }
}

@media (max-width: 480px) {
  .other-service-page {
    padding: 12px;
  }
  
  .credit-button {
    font-size: 12px;
    height: 36px;
    padding: 0 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
  
  .work-content {
    max-width: 100px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 成功状态样式 */
.ant-message-success .anticon {
  color: #52c41a;
}

/* 错误状态样式 */
.ant-message-error .anticon {
  color: #ff4d4f;
}

/* 警告状态样式 */
.ant-message-warning .anticon {
  color: #faad14;
}

/* 信息状态样式 */
.ant-message-info .anticon {
  color: #1890ff;
}

/* 表格滚动条样式 */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 信用点高亮样式 */
.credit-highlight {
  font-size: 24px;
  color: #E67E22;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 工单类型样式 */
.work-type {
  font-weight: 500;
  color: #262626;
}

/* 等待审核样式 */
.pending-review {
  color: #999;
  font-style: italic;
}

/* 详情按钮样式 */
.detail-button {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.detail-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}
