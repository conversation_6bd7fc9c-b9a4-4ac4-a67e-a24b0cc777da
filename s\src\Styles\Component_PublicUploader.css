
    /* model title样式*/
    .publicupload-header-title-wrapper {
        display: flex;
        align-items: center;
        padding: 0px 10px;
        /* background: rgba(255, 255, 255, 0.05); */
        border-left: 4px solid #1890ff;
        border-radius: 6px;
        transition: background 0.3s ease;
    }
    
    .publicupload-header-title-wrapper:hover {
        /* background: rgba(255, 255, 255, 0.08); */
    }
    
    .publicupload-header-title-wrapper h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #f0f0f0;
        letter-spacing: 0.3px;
    }

    /* 上传总div 样式 */
    .publicupload-file-wrap {
        padding: 10px 20px;
    }

    /* 拖拽区域 */
    .publicupload-drop-zone {
        height: 200px;
        border: 2px dashed #c9c9c9;
        border-radius: 8px;
        /* background-color: #2A3147; */
        background-color: transparent !important;
        display: flex;
        flex-direction: column;
        align-items: center;     /* 垂直居中 */
        justify-content: center; /* 水平居中 */
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .publicupload-drop-zone:hover {
        border-color: #007bff;
        background-color: #2A3147;
    }
    
    .publicupload-drop-zone p {
        margin: 4px 0;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
        width: 100%;
        
    }
    
    .publicupload-drop-zone input[type='file'] {
        display: none;
    }

    .publicupload-format-tag {
        display: inline-block;
        margin: 2px 4px;
        padding: 2px 6px;
        background-color: #13a8a8;
        border-radius: 4px;
        font-size: 12px;
    }

    /* 错误信息 */
    .publicupload-error-message {
        color: #ff6b6b;
        font-size: 14px;
        margin-top: 12px;
        display: block;
    }

    /* 文件列表和文件项 */
    .publicupload-file-list {
        margin-top: 24px;
        list-style: none;
        padding: 0;
        border-radius: 8px;
        overflow: hidden;
    }

    .publicupload-file-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 12px;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        /* background-color: #2A3147; */
        background-color: transparent;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }
      
    .publicupload-file-item:hover {
        border-color: #1890ff;
        /* background-color: #2A3147; */
    }

    .publicupload-file-icon,
    .publicupload-file-preview {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border-radius: 4px;
        margin-right: 12px;
        font-size: 24px;
        color: #ffffff;
    }

    .publicupload-file-info {
        flex: 1;
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        gap: 4px;
        width: 40%;
    }

    .publicupload-file-name {
        font-weight: 500;
        color: #ffffff;
        word-break: break-all;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    
    .publicupload-file-size {
        color: #ffffff;
        font-size: 12px;
    }

    /* 进度条 */
    .publicupload-progress-container {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .publicupload-progress-bar {
        flex: 1;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        position: relative;
    }
    
    .publicupload-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
        position: relative;
    }
    
    .publicupload-progress-fill.with-shimmer::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%
        );
        animation: shimmer 2s infinite;
    }

    /* 文件操作按钮容器 */
    .publicupload-file-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 提交图标 */
    .publicupload-submit-icon {
        cursor: pointer;
        font-size: 16px;
        color: #52c41a;
        padding: 8px;
        border-radius: 50%;
        background: rgba(82, 196, 26, 0.1);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 34px;
        height: 34px;
        position: relative;
        overflow: hidden;
    }
    
    .publicupload-submit-icon::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(82, 196, 26, 0.2);
        border-radius: 50%;
        transition: all 0.3s ease;
        transform: translate(-50%, -50%);
    }
    
    .publicupload-submit-icon:hover {
        color: #389e0d;
        background: rgba(82, 196, 26, 0.15);
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
    }
    
    .publicupload-submit-icon:hover::before {
        width: 100%;
        height: 100%;
    }
    
    .publicupload-submit-icon:active {
        transform: scale(0.95);
        box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
    }

    
    /* 删除按钮 */
    .publicupload-remove-btn {
        cursor: pointer;
        font-size: 18px;
        color: #ff4d4f;
        padding: 8px;
        border-radius: 50%;
        background: transparent;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 34px;
        height: 34px;
        position: relative;
        overflow: hidden;
    }
    .publicupload-remove-btn:hover {
        transform: scale(1.1);
        color: #ff1f1f;
    }
    
    .publicupload-remove-btn:active {
        transform: scale(0.95);
    }

      /* 自定义表格样式 */
    .publicupload-custom-table .ant-table {
        /* background-color: #1e263d; 表格整体背景 */
        color: #ffffff; /* 表格文字颜色 */
    }
    
    /* 表头样式 */
    .publicupload-custom-table .ant-table thead > tr > th {
        background-color: #161f35; /* 更深一点的颜色，突出表头 */
        color: #ffffff; /* 表头文字颜色 */
        border-bottom: 2px solid #2a3452; /* 可选边框 */
    }
    
    /* 表格行样式 */
    .publicupload-custom-table .ant-table tbody > tr > td {
        background-color: #1e263d; /* 行背景色 */
        color: #ffffff; /* 行文字颜色 */
    }
    
    /* 鼠标悬停行样式 */
    .publicupload-custom-table .ant-table tbody > tr.ant-table-row:hover > td {
        background-color: #2a3452; /* 悬停时的背景色 */
    }
    
    /* 分页器样式（如果需要） */
    .publicupload-custom-table .ant-table-pagination {
        /* background-color: #1e263d; */
    }
    
    .publicupload-custom-table .ant-table-placeholder .ant-table-cell {
        /* background-color: #1e263d !important; */
        padding: 0 !important;
    }
    /* 表格删除按钮*/
    .publicupload-action-icon {
        font-size: 16px;
        cursor: pointer;
        transition: color 0.2s;
        color: #ffffff;
    }

    .publicupload-delete-icon {
        color: #ff4d4f;
        cursor: pointer;
        font-size: 16px;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    
    .publicupload-delete-icon:hover {
        background-color: #3a3f53;
        color: #ff1f1f;
    }