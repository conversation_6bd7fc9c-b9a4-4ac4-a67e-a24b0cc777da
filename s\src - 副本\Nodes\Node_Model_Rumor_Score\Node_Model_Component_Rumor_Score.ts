

import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,ModelControl_Rumor_Score} from "./Node_Model_Control_Rumor_Score";


export class Node_Model_Component_Rumor_Score extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    Conent:ModelControl_Rumor_Score,
    

  }> 
  {
    width =480;
    height = 428;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new ModelControl_Rumor_Score(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:群聊名称', // Initial value
        '【类型】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '\n', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }

      updateContent(Intelligence:Record<string, any>){
        const contentControl = this.controls.Conent;

        contentControl.setContent(Intelligence)
        console.log('Intelligence:', Intelligence);
        


      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    