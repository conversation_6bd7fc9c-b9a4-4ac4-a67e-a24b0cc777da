/// <reference types="react-scripts" />
declare namespace BMap {
    // 根据实际需要声明用到的类型
    class Map {
      constructor(container: string | HTMLElement, options?: MapOptions)
      public centerAndZoom(point: Point, zoom: number): void;
      public enableScrollWheelZoom(enable?: boolean): void;
      public addEventListener(event: string, handler: (e: any) => void): void;
      public removeEventListener(event: string, handler: (e: any) => void): void;
    }
  
    interface MapOptions {
      minZoom?: number
      maxZoom?: number
      enableHighResolution?: boolean
    }
  
    class Point {
      constructor(lng: number, lat: number)
    }
}