import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,SystemControl_Preview_Dashboard} from "./Node_System_Control_Preview_Dashboard";

export class Node_System_Component_Preview_Dashboard extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| SystemControl_Preview_Dashboard}> 
  {
    width  = 480;
    height = 328;
    constructor(Label: string,) {
      super(Label);

      const ConentControl = new SystemControl_Preview_Dashboard(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("<PERSON><PERSON>",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
       