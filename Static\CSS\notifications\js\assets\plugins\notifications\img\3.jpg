<!DOCTYPE html>
<html class="no-js" lang="en" prefix="og: http://ogp.me/ns#">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width">
      <link rel="shortcut icon" type="image/x-icon" href="https://codervent.com/wp-content/uploads/2020/04/mini-logo.png">
            <!-- Jetpack Site Verification Tags -->
<meta name="google-site-verification" content="DiuQh2EJRHwcqEtn77A-u4-qlZ0pTMzobceY1AJbONY" />

<!-- This site is optimized with the Yoast SEO Premium plugin v13.4 - https://yoast.com/wordpress/plugins/seo/ -->
<meta name="robots" content="noindex,follow"/>
<meta property="og:locale" content="en_US" />
<meta property="og:type" content="object" />
<meta property="og:title" content="Page not found - codervent" />
<meta property="og:site_name" content="codervent" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="Page not found - codervent" />
<meta name="twitter:site" content="@codervent" />
<script type='application/ld+json' class='yoast-schema-graph yoast-schema-graph--main'>{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://codervent.com/#organization","name":"codervent","url":"https://codervent.com/","sameAs":["https://www.facebook.com/Codervent/","https://www.youtube.com/channel/UCOIBypwG4Fl3ZyA-eJwshoQ?view_as=subscriber","https://in.pinterest.com/codervent/","https://twitter.com/codervent"],"logo":{"@type":"ImageObject","@id":"https://codervent.com/#logo","inLanguage":"en","url":"https://i1.wp.com/codervent.com/wp-content/uploads/2020/04/logo-e1586169656723.png?fit=120%2C37&ssl=1","width":120,"height":37,"caption":"codervent"},"image":{"@id":"https://codervent.com/#logo"}},{"@type":"WebSite","@id":"https://codervent.com/#website","url":"https://codervent.com/","name":"codervent","inLanguage":"en","description":"Themes &amp; Website Templates Starting From $7","publisher":{"@id":"https://codervent.com/#organization"},"potentialAction":[{"@type":"SearchAction","target":"https://codervent.com/?s={search_term_string}","query-input":"required name=search_term_string"}]}]}</script>
<!-- / Yoast SEO Premium plugin. -->


<!-- Search Engine Optimization by Rank Math - https://s.rankmath.com/home -->
<title>Page Not Found - codervent</title>
<meta name="robots" content="follow, noindex"/>
<meta property="og:locale" content="en_US">
<meta property="og:type" content="object">
<meta property="og:title" content="Page Not Found - codervent">
<meta property="og:site_name" content="codervent">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Page Not Found - codervent">
<script type="application/ld+json" class="rank-math-schema">{
    "@context": "https://schema.org",
    "@graph": [
        {
            "@type": "Organization",
            "@id": "https://codervent.com/#organization",
            "name": "codervent",
            "url": "https://codervent.com",
            "logo": {
                "@type": "ImageObject",
                "url": "https://codervent.com/wp-content/uploads/2020/04/mini-logo-1.png"
            }
        },
        {
            "@type": "WebSite",
            "@id": "https://codervent.com/#website",
            "url": "https://codervent.com",
            "name": "codervent",
            "publisher": {
                "@id": "https://codervent.com/#organization"
            },
            "inLanguage": "en",
            "potentialAction": {
                "@type": "SearchAction",
                "target": "https://codervent.com/?s={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        },
        {
            "@type": "WebPage",
            "@id": "#webpage",
            "url": false,
            "name": "Page Not Found - codervent",
            "isPartOf": {
                "@id": "https://codervent.com/#website"
            },
            "inLanguage": "en"
        }
    ]
}</script>
<!-- /Rank Math WordPress SEO plugin -->

<link rel='dns-prefetch' href='//www.googletagmanager.com' />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<link rel='dns-prefetch' href='//s.w.org' />
<link rel='dns-prefetch' href='//c0.wp.com' />
<link rel='dns-prefetch' href='//i0.wp.com' />
<link rel='dns-prefetch' href='//i1.wp.com' />
<link rel='dns-prefetch' href='//i2.wp.com' />
<link rel="alternate" type="application/rss+xml" title="codervent &raquo; Feed" href="https://codervent.com/feed/" />
<link rel="alternate" type="application/rss+xml" title="codervent &raquo; Comments Feed" href="https://codervent.com/comments/feed/" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.0\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.0\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/codervent.com\/wp-includes\/js\/wp-emoji-release.min.js"}};
			!function(e,a,t){var r,n,o,i,p=a.createElement("canvas"),s=p.getContext&&p.getContext("2d");function c(e,t){var a=String.fromCharCode;s.clearRect(0,0,p.width,p.height),s.fillText(a.apply(this,e),0,0);var r=p.toDataURL();return s.clearRect(0,0,p.width,p.height),s.fillText(a.apply(this,t),0,0),r===p.toDataURL()}function l(e){if(!s||!s.fillText)return!1;switch(s.textBaseline="top",s.font="600 32px Arial",e){case"flag":return!c([127987,65039,8205,9895,65039],[127987,65039,8203,9895,65039])&&(!c([55356,56826,55356,56819],[55356,56826,8203,55356,56819])&&!c([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]));case"emoji":return!c([55357,56424,8205,55356,57212],[55357,56424,8203,55356,57212])}return!1}function d(e){var t=a.createElement("script");t.src=e,t.defer=t.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(t)}for(i=Array("flag","emoji"),t.supports={everything:!0,everythingExceptFlag:!0},o=0;o<i.length;o++)t.supports[i[o]]=l(i[o]),t.supports.everything=t.supports.everything&&t.supports[i[o]],"flag"!==i[o]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[i[o]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",n,!1),e.addEventListener("load",n,!1)):(e.attachEvent("onload",n),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&t.readyCallback()})),(r=t.source||{}).concatemoji?d(r.concatemoji):r.wpemoji&&r.twemoji&&(d(r.twemoji),d(r.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='https://c0.wp.com/c/5.5.3/wp-includes/css/dist/block-library/style.min.css' type='text/css' media='all' />
<style id='wp-block-library-inline-css' type='text/css'>
.has-text-align-justify{text-align:justify;}
</style>
<link rel='stylesheet' id='edd-styles-css'  href='https://codervent.com/wp-content/themes/olam/edd_templates/edd.css' type='text/css' media='all' />
<link rel='stylesheet' id='ewd-uwpm-css-css'  href='https://codervent.com/wp-content/plugins/ultimate-wp-mail/css/ewd-uwpm-css.css' type='text/css' media='all' />
<link rel='stylesheet' id='dashicons-css'  href='https://c0.wp.com/c/5.5.3/wp-includes/css/dashicons.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='edd-reviews-css'  href='https://codervent.com/wp-content/plugins/edd-reviews/assets/css/edd-reviews.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='normalize-css'  href='https://codervent.com/wp-content/themes/olam/css/normalize.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='olam-bootstrap-css'  href='https://codervent.com/wp-content/themes/olam/css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='olam-style-css'  href='https://codervent.com/wp-content/themes/olam/css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='owl-carousel-css'  href='https://codervent.com/wp-content/themes/olam/css/owl.carousel.css' type='text/css' media='all' />
<link rel='stylesheet' id='olam-color-css'  href='https://codervent.com/wp-content/themes/olam/css/color.css.php' type='text/css' media='all' />
<style id='olam-color-inline-css' type='text/css'>

                body {
                  font-family : Roboto, Arial, Helvetica;
                  color : #6b6b6b;
                  font-size : 14px;
                  }
        
</style>
<link rel='stylesheet' id='olam-google-fonts-css'  href='https://fonts.googleapis.com/css?family=Roboto%3A100%2C300%2C400%2C500%2C700%7CMontserrat%3A400%2C700' type='text/css' media='all' />
<link rel='stylesheet' id='fw-ext-builder-frontend-grid-css'  href='https://codervent.com/wp-content/plugins/unyson/framework/extensions/builder/static/css/frontend-grid.css' type='text/css' media='all' />
<link rel='stylesheet' id='fw-ext-forms-default-styles-css'  href='https://codervent.com/wp-content/plugins/unyson/framework/extensions/forms/static/css/frontend.css' type='text/css' media='all' />
<link rel='stylesheet' id='font-awesome-css'  href='https://codervent.com/wp-content/plugins/unyson/framework/static/libs/font-awesome/css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='jetpack_css-css'  href='https://c0.wp.com/p/jetpack/9.0.2/css/jetpack.css' type='text/css' media='all' />
<script type='text/javascript' src='https://c0.wp.com/c/5.5.3/wp-includes/js/jquery/jquery.js' id='jquery-core-js'></script>
<script type='text/javascript' id='olam-register-login-js-extra'>
/* <![CDATA[ */
var ajax_auth_object = {"ajaxurl":"https:\/\/codervent.com\/wp-admin\/admin-ajax.php","redirecturl":"","loadingmessage":"Sending user info, please wait..."};
/* ]]> */
</script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/olam-register-login.js' id='olam-register-login-js'></script>
<script type='text/javascript' id='ewd-uwpm-js-js-extra'>
/* <![CDATA[ */
var ewd_uwpm_data = {"ajaxurl":"https:\/\/codervent.com\/wp-admin\/admin-ajax.php"};
/* ]]> */
</script>
<script type='text/javascript' src='https://codervent.com/wp-content/plugins/ultimate-wp-mail/js/ewd-uwpm-js.js' id='ewd-uwpm-js-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/vendor/modernizr-2.8.3-respond-1.4.2.min.js' id='modernizr-js'></script>
<script type='text/javascript' src='https://www.googletagmanager.com/gtag/js?id=UA-151891557-1' id='google_gtagjs-js'></script>
<script type='text/javascript' id='google_gtagjs-js-after'>
window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('set', 'developer_id.dZTNiMT', true);
gtag('config', 'UA-151891557-1');
</script>
<link rel="https://api.w.org/" href="https://codervent.com/wp-json/" /><link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://codervent.com/xmlrpc.php?rsd" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="https://codervent.com/wp-includes/wlwmanifest.xml" /> 
<meta name="generator" content="WordPress 5.5.3" />
<meta name="generator" content="Easy Digital Downloads v2.9.26" />
<!-- This code is added by WP Analytify (3.1.5) https://analytify.io/downloads/analytify-wordpress-plugin/ !-->				 <script>
				 (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
					 (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
					 m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
				 })

				 (window,document,'script','//www.google-analytics.com/analytics.js','ga');
				 	ga('create', 'UA-151891557-1', 'auto');ga('send', 'pageview');				 </script>

				 <!-- This code is added by WP Analytify (3.1.5) !--><style type='text/css'>img#wpstats{display:none}</style>    <script type="text/javascript">
      var ajaxurl = 'https://codervent.com/wp-admin/admin-ajax.php';
    </script>
    <script>
//<![CDATA[
jQuery(window).load(function(){
var retina = window.devicePixelRatio > 1 ? true : false;if( retina ){var retinaEl = jQuery(".logo img.site-logo");var retinaLogoW = retinaEl.width();var retinaLogoH = retinaEl.height();retinaEl.attr( "src", "https://codervent.com/wp-content/uploads/2020/04/logo-e1586169656723.png" ).width( retinaLogoW ).height( retinaLogoH );}});
//]]>
</script>
<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style>			<style type="text/css">
				/* If html does not have either class, do not show lazy loaded images. */
				html:not( .jetpack-lazy-images-js-enabled ):not( .js ) .jetpack-lazy-image {
					display: none;
				}
			</style>
			<script>
				document.documentElement.classList.add(
					'jetpack-lazy-images-js-enabled'
				);
			</script>
		  </head>
  
 <body data-rsssl=1 class="error404 header-sticky">
        <!--[if lt IE 8]>
            <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please upgrade your browser to improve your experience.</p>            <![endif]-->
            <!-- Preloader -->
<style type="text/css" scoped>
    .preloader-wrapper  {position: fixed; top: 0; left: 0; background: #fff; height: 100%; width: 100%; z-index: 999999999}
    .preloader-contents {position: absolute; left: 0; top: 0; bottom: 0; right: 0; height: 64px; margin: auto; text-align: center; width: 100%;}
</style>


<div class="preloader-wrapper">
    <div class="preloader-contents">
        <div class="preloader-loader">
            <img src="https://codervent.com/wp-content/themes/olam/img/grid.svg" alt="Loading">
        </div>
    </div>
</div>
<!-- Preloader -->    
            <div class="wrapper">
              <div class="middle-area edd-review-middle">
                            <div class="header-wrapper header-bg ">
                  <!-- Header -->
                  
                  <header id="header" class="header navbar-fixed-top">
                    <div class="container">
                      <div>
                      <div class="header-section">
                        <div class="header-wrap">
                          <div class="header-col col-logo">
                            <div class="logo">
                              <a href="https://codervent.com"> 
                                
                               <img class="site-logo" src="https://codervent.com/wp-content/uploads/2020/04/logo-e1586169656723.png"  alt="codervent"> 
                             </a>
                           </div>
                         </div>
                         <div class="header-col col-nav">
                          <nav id="nav">
                            <div class="menu-top-menu-container"><ul id="menu-top-menu" class="menu"><li id="menu-item-4053" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-4053"><a href="https://codervent.com/" >Home</a></li>
<li id="menu-item-4056" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-4056"><a href="https://codervent.com/about-us/" >About Us</a></li>
<li id="menu-item-4328" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-4328"><a href="https://codervent.com/downloads/category/free/" >Free</a></li>
<li id="menu-item-4284" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-4284"><a >Shop</a>
<ul class="sub-menu sub-menu-has-icons">
	<li id="menu-item-4213" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-icon menu-item-4213"><a href="https://codervent.com/downloads/category/bootstrap/" class="fa fa-bold" >Bootstrap</a></li>
	<li id="menu-item-4214" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-icon menu-item-4214"><a href="https://codervent.com/downloads/category/angular/" class="fa fa-font" >Angular</a></li>
</ul>
</li>
<li id="menu-item-4024" class="menu-item menu-item-type-post_type menu-item-object-page current_page_parent menu-item-4024"><a href="https://codervent.com/blog/" >Blog</a></li>
</ul></div> 
                            <ul class="shop-nav">

                                                              <li> <a href="#" class="login-button login-trigger">Login</a></li>
                              
                                                              <li>    <div class="cart-widget">
      <span class="cart-btn">
        <i class="demo-icon icon-cart"></i>
        <span> 0 Items</span>
      </span>
      <!-- Cart widget -->
      <div class="dd-cart">
        <div class="inner-scroll">
          <ul class="cart_list product_list_widget ">
                          <li>
                <div class="empty-cart text-center">
                  <div class="cart-icon"><i class="demo-icon icon-cart"></i></div>
                  <span class="edd_empty_cart">Your cart is empty!</span>
                </div>
              </li>
            </ul>
                      </div>
        </div>                        
      </div>
      </li>
                              
                            </ul>
                          </nav>
                        </div>

                        <div class="header-col col-shop">
                        </div>
                      </div>
                      <div class="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <!-- mobile navigation -->
                      <div class="mob-nav">
                      </div>
                    </div>
                    </div>
                  </div>
                </header>
                <!-- Header End -->
                                <!-- Search Section-->
                                              <!-- Search -->
            </div>
<div class='page-404'>
	<div class='section'>
		<div class="container text-center">
			<div class="title-404">oo<span>4o4</span>ps</div>
			<h4>Page not Found...</h4>
		</div>
	</div>
</div>


 
</div>
<!-- wrapper --></div>

<footer id="footer" class=" edd-review-footer" >   
    <div class="container">
    <ul class="footer-social social-icons">
     <li class="social-facebook"><a href="https://www.facebook.com/Codervent/" target="_blank"><span class="icon"><i class="demo-icon icon-facebook"></i></span></a></li>      <li class="social-youtube"><a href="https://www.youtube.com/channel/UCOIBypwG4Fl3ZyA-eJwshoQ?view_as=subscriber" target="_blank"><span class="icon"><i class="demo-icon icon-youtube"></i></span></a></li>      <li class="social-twitter"><a href="https://twitter.com/codervent" target="_blank"><span class="icon"><i class="demo-icon icon-twitter"></i></span></a></li>         <li class="social-pinterest"><a href="https://in.pinterest.com/codervent/pins/" target="_blank"><span class="icon"><i class="fa fa-pinterest"></i></span></a></li></ul>
  <div class="footer-text">&copy;  All Rights reserved codervent </div></div>
</footer>
<div class="scroll-top">
  <span class="scrollto-icon"><i class="demo-icon icon-rocket"></i></span>
  <span class="flame"></span>
  <span class="flame"></span>
  <span class="flame"></span>
</div>

<!-- Popup Login -->
<div id="loginBox" class="lightbox-wrapper">
    <div class="lightbox-content">
        <div class="lightbox-area">
            <div class="lightbox">
                <div class="boxed">
                    <div class="lightbox-close">
                        <div class="close-btn">
                            <span class="close-icon">
                                <i class="demo-icon icon-cancel"></i>
                            </span>
                        </div>
                    </div>


                    <div class="boxed-body signin-area">
                            <div class="lightbox-title">Login</div>
                                    <form id="olam-login">
                                        <!-- additional fields start -  -->
                                        <p class="olam-msg-status"></p>
                                        <input type="hidden" id="security" name="security" value="61c3fa3b6a" /><input type="hidden" name="_wp_http_referer" value="/syntrans/demo/vertical/assets/plugins/notifications/js/assets/plugins/notifications/img/3.jpg" />  
                                        <!-- additional fields end -  -->
                                        <div class="field-holder">
                                            <label><i class="demo-icon icon-user"></i> Name</label>
                                            <input id="username" name="name" type="text">
                                        </div>
                                        <div class="field-holder">
                                            <label><i class="demo-icon icon-lock-filled"></i> Password</label>
                                            <input id="password" type="password">
                                        </div>
                                        <div class="btn-pro-frame">
                                        <input id="olam-pop-login-submit" type="submit" value="Submit " class="btn btn-md btn-white">
                                        <span class="btn-pro"><img src="https://codervent.com/wp-content/themes/olam/img/reload.gif" alt="reload"></span>
                                        </div>
                                    </form>
                                    <p class="edd-lost-password"><a href="https://codervent.com/wp-login.php?action=lostpassword" title="Lost Password">Lost Password?</a></p>

                        <div class="social"></div>

                    </div>

                    
                    <div class="boxed-head toggle-signup">
                        <div class="lightbox-subtitle">Don&#039;t Have an Account? </div>
                        <div class="lightbox-title">Sign Up Now</div>
                        <div class="signup-icon"><span><i class="demo-icon icon-rocket"></i></span></div>
                    </div>
                    <div class="boxed-body signup-area">
                        <form id="olam-register">
                            <p class="status"></p>
                            <!-- additional fields start -  -->
                            <p class="olam-msg-status"></p>
                            <input type="hidden" id="signonsecurity" name="signonsecurity" value="90d45a0879" /><input type="hidden" name="_wp_http_referer" value="/syntrans/demo/vertical/assets/plugins/notifications/js/assets/plugins/notifications/img/3.jpg" />   
                            <!-- additional fields end -  -->  
                            <div class="field-holder">
                                <label><i class="demo-icon icon-user"></i> Name</label>
                                <input id="reg-username" name="username" type="text">
                            </div>
                            <div class="field-holder">
                                <label><i class="demo-icon icon-mail-alt"></i> Email</label>
                                <input name="email" id="reg-email" type="text">
                            </div>
                            <div class="field-holder">
                                <label><i class="demo-icon icon-lock-filled"></i> Password</label>
                                <input name="password" id="reg-password" type="password">
                            </div>
                            <div class="field-holder">
                                <label><i class="demo-icon icon-lock-filled"></i> Confirm Password</label>
                                <input id="reg-password2" name="password2" type="password">
                            </div>
                            <div class="btn-pro-frame">
                                <input type="submit" value="Register" class="btn btn-md btn-white">
                                <span class="btn-pro"><img src="https://codervent.com/wp-content/themes/olam/img/reload.gif" alt="reload"></span>
                            </div>
                        </form>
                        <div class="social"></div>
                    </div>
                    

                </div>
            </div>
        </div>
    </div>
    <div class="lightbox-overlay"></div>
</div><!-- Quick contact -->
<div id="quickContact" class="quick-contact-window">
    <div class="quick-contact">
        <div class="qw-title">
           Support            <span><i class="icon-sample icon-minus"></i></span>
        </div>
        <div class="quick-window">
            <div class="quickcontact-success">
            </div>
            <form method="POST" id="olam-quick-contact">
                <div class="input-wrap name-field"><div class="olam_name form-alert"></div><input name="qc-name" id="qc-name" type="text" placeholder="Name"></div>
                <div class="input-wrap email-field"><div class="olam_email form-alert"></div><input name="qc-email" id="qc-email" type="email" placeholder="Email"></div>
                <div class="input-wrap message-field"> <div class="olam_message form-alert"></div><textarea name="message" id="qc-message" rows="6" placeholder="Message"></textarea> </div>
                <input type="submit" value="Support">
            </form>
        </div>
    </div>
</div>
<script type='text/javascript' src='https://c0.wp.com/p/jetpack/9.0.2/_inc/build/photon/photon.min.js' id='jetpack-photon-js'></script>
<script type='text/javascript' id='edd-ajax-js-extra'>
/* <![CDATA[ */
var edd_scripts = {"ajaxurl":"https:\/\/codervent.com\/wp-admin\/admin-ajax.php","position_in_cart":"-1","has_purchase_links":"","already_in_cart_message":"You have already added this item to your cart","empty_cart_message":"Your cart is empty","loading":"Loading","select_option":"Please select an option","is_checkout":"0","default_gateway":"paypal","redirect_to_checkout":"0","checkout_page":"https:\/\/codervent.com\/checkout\/","permalinks":"1","quantities_enabled":"","taxes_enabled":"0"};
/* ]]> */
</script>
<script type='text/javascript' src='https://codervent.com/wp-content/plugins/easy-digital-downloads/assets/js/edd-ajax.min.js' id='edd-ajax-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/jquery.easypiechart.min.js' id='jquery-easypiechart-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/bootstrap.min.js' id='olam-bootstrap-js-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/sly.min.js' id='sly-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/owl.carousel.min.js' id='owl-carousel-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/appear.js' id='appear-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/easing.js' id='easing-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/jquery.parallax-1.1.3.js' id='jquery-parallax-js'></script>
<script type='text/javascript' id='olam-main-js-extra'>
/* <![CDATA[ */
var olam_main_ajax = {"ajaxurl":"https:\/\/codervent.com\/wp-admin\/admin-ajax.php","nonce":"4f6d11eeb4","piecolor":"#0ba5dd"};
/* ]]> */
</script>
<script type='text/javascript' src='https://codervent.com/wp-content/themes/olam/js/olam-main.js' id='olam-main-js'></script>
<script type='text/javascript' src='https://codervent.com/wp-content/plugins/jetpack/vendor/automattic/jetpack-lazy-images/src/js/lazy-images.min.js' id='jetpack-lazy-images-js'></script>
<script type='text/javascript' src='https://c0.wp.com/c/5.5.3/wp-includes/js/wp-embed.min.js' id='wp-embed-js'></script>
<script type='text/javascript' src='https://stats.wp.com/e-202050.js' async='async' defer='defer'></script>
<script type='text/javascript'>
	_stq = window._stq || [];
	_stq.push([ 'view', {v:'ext',j:'1:9.0.2',blog:'173746057',post:'0',tz:'0',srv:'codervent.com'} ]);
	_stq.push([ 'clickTrackerInit', '173746057', '0' ]);
</script>
</body>
</html>
