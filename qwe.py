# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,datetime
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
from PySide6.QtWebEngineWidgets import QWebEngineView
import qtawesome as qta
import cv2
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.Toolkit import Service_Print,Service_Table
PP    = Service_Print.Service_Print()
PW    = Service_Print.Service_Print('Warning')
PJ    = Service_Print.Service_Print('Json')
PPP   = Service_Print.Service_Print('Json_Time')
# from Bin.System.OS.Resource.CSS import UI_Icons
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog_Video

sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Component")

import Component_Common,Component_VideoPlay,Component_Setting,Component_Rolling_Message,Component_Track,Component_Devicetree,Component_Processing_Portrait,Component_Processing_Radar,Component_Processing_Dooralarm
sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Utils")
import Utils_PopupDialog,Utils_PopupDialog_Media

# D:\Sentinel Foundation\Bin\System\OS\Component\.py
from PySide6.QtWidgets import (QApplication, QWidget, QPushButton, QLabel,QGridLayout, QVBoxLayout, QHBoxLayout, QSizePolicy)
from PySide6.QtCore import QPropertyAnimation, QRect, Qt
import qtawesome
Page_Info={}
class Page_Widget_Media_VideoMonitor(QtWidgets.QWidget):
    Signal_Command = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass


        Page_Info["Page_Element_List"]={}
        Page_Info["Page_Def_List"]  ={}
        Page_Info["StyleSheet"]={}

        Page_Info["StyleSheet"]["Value_Color"] ="rgba(0, 255, 136, 255)"

        # Page_Info["Page_Def_List"]["ReSet"]            = self.ReSet
        Page_Info["Page_Def_List"]["Set_PopupDialog"]        = self.Set_PopupDialog
        Page_Info["Page_Def_List"]["Re_Set_Channel"]         = self.Re_Set_Channel
        Page_Info["Page_Def_List"]["Set_Strict_Control"]     = self.Set_Strict_Control
        Page_Info["Page_Def_List"]["Set_DrawerSetting"]      = self.Set_DrawerSetting
        # Page_Info["Page_Def_List"]["Toggle_Drawer_Right"]    = self.Toggle_Drawer_Right

        Page_Info["Page_Channel_List"]={}
        Page_Info["Page_Channel_List"]["Channel_1"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_2"]    =   {"Video_Source":"rtsp://admin:csc2024!@192.168.123.57:554/h264/ch1/main/av_stream"}
        Page_Info["Page_Channel_List"]["Channel_3"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_4"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_5"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_6"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_7"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_8"]    =   {"Video_Source":"未知"}
        Page_Info["Page_Channel_List"]["Channel_9"]    =   {"Video_Source":"未知"}





        self.initUI()
        self.is_expanded_Drawer_Right = False

    def initUI(self):

        __QHBoxLayout = QtWidgets.QHBoxLayout(self)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        QLabel_Left = QtWidgets.QLabel()



        self.QWidget_Right_Drawer = QtWidgets.QWidget()
        self.QWidget_Right_Drawer.setStyleSheet('background:transparent; color:white;')
        self.QWidget_Right_Drawer.setFixedWidth(0)
        # self.QLabel_Title.setAlignment(Qt.AlignCenter)  # 设置文本居中
        self.QWidget_Right_Drawer.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self.QVBoxLayout_Right_Drawer = QtWidgets.QVBoxLayout(self.QWidget_Right_Drawer)
        self.QVBoxLayout_Right_Drawer.setContentsMargins(0, 0, 0, 0)












        __QHBoxLayout.addWidget(QLabel_Left)
        __QHBoxLayout.addWidget(self.QWidget_Right_Drawer)

        __QVBoxLayout = QtWidgets.QVBoxLayout(QLabel_Left)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(0)


        self.QLabel_Title = QtWidgets.QLabel()
        self.QLabel_Title.setStyleSheet('background:transparent;')
        self.QLabel_Title.setFixedHeight(58)

        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet('background:transparent; color:red;')


        __QVBoxLayout.addWidget(self.QLabel_Title,0)
        __QVBoxLayout.addWidget(self.QLabel_Content)


        self.Set_Title()
        self.Set_Content()



        # 设置计时器
        self.QTimer_Count = QtCore.QTimer(self)
        self.QTimer_Count.timeout.connect(self.Page_Update_Timer_Date)
        self.QTimer_Count.start(1000)  # 每秒更新一次

        # 定时器，用于模拟新数据的添加
        self.QTimer_Message = QtCore.QTimer(self)
        self.QTimer_Message.timeout.connect(self.Check_Meassage)
        self.QTimer_Message.start(10000)  # 每5秒添加一个新的 Line



    def Set_Title(self):
        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Title)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)


        __QHBoxLayout.addWidget(self.Set_Title_Menu(), 1)
        __QHBoxLayout.contentsRect()
        __QHBoxLayout.addWidget(self.Set_System_Status(),1)


    def Set_PopupDialog(self,PopupParameter):

        # PP(PopupParameter,4)
        PopupDialog   =  PopupParameter["Command"]
        PP(PopupDialog)


        Dialog_Type, Dialog_Name = (f"_{Parts[0]}", Parts[1]) if (Parts := PopupDialog.split("_")) and len(Parts) > 1 else ("", PopupDialog)
        exec(f"QLabel_Dialog = Utils_PopupDialog{Dialog_Type}.Utils_Dialog{Dialog_Type}_{Dialog_Name}(self,PopupParameter)")
        exec("QLabel_Dialog.Signal_Result.connect(self.PAGE_HANDLE_RESULT)")
        exec("QLabel_Dialog.move(self.geometry().center() - QLabel_Dialog.rect().center())")
        exec("QLabel_Dialog.show()")


    def Set_DrawerSetting(self,DrawerParameter):
        # __QVBoxLayout = QtWidgets.QVBoxLayout(self.QWidget_Right_Drawer)
        # __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        # self.QVBoxLayout_Right_Drawer
        while self.QVBoxLayout_Right_Drawer.count():
            item = self.QVBoxLayout_Right_Drawer.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Drawer_Type
        exec(f"self.Set_Drawer_{DrawerParameter['Drawer_Type']}(DrawerParameter)")

        self.Toggle_Drawer_Right()

    def Set_Drawer_Config_Control(self,DrawerParameter):
        print('点击了云台控制按钮')
        QLabel_Title = QtWidgets.QLabel(f'{DrawerParameter["Drawer_Title"]}【{DrawerParameter["Channel_ID"]}】')
        QLabel_Title.setStyleSheet('background:transparent; color:white;font-size:16px')

        QLabel_Title.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Title.setFixedHeight(62)
        QLabel_Title.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        QLabel_Content = Component_Setting.Component_Setting_Control()
        QLabel_Content.setStyleSheet('background:rgba(40, 52, 80, 0.6);')

        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Title, alignment=QtCore.Qt.AlignTop)
        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Content)



    def Set_Drawer_Config_Channel(self,DrawerParameter):
        QLabel_Title = QtWidgets.QLabel(f'{DrawerParameter["Drawer_Title"]}【{DrawerParameter["Channel_ID"]}】')
        QLabel_Title.setStyleSheet('background:transparent; color:white;font-size:16px')

        QLabel_Title.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Title.setFixedHeight(62)
        QLabel_Title.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        QLabel_Content = Component_Setting.Component_Setting_Channel()
        QLabel_Content.setStyleSheet('background:rgba(40, 52, 80, 0.6);')

        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Title, alignment=QtCore.Qt.AlignTop)
        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Content)


    def Set_Drawer_Config_Function(self,DrawerParameter):
        QLabel_Title = QtWidgets.QLabel(f'{DrawerParameter["Drawer_Title"]}')
        QLabel_Title.setStyleSheet('background:transparent; color:white;font-size:16px')

        QLabel_Title.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Title.setFixedHeight(62)
        QLabel_Title.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        QLabel_Content = Component_Setting.Component_Setting_Function(self)
        QLabel_Content.setStyleSheet('background:rgba(40, 52, 80, 0.6);')

        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Title, alignment=QtCore.Qt.AlignTop)
        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Content)


    def Set_Drawer_Device_Tree(self,DrawerParameter):
        QLabel_Title = QtWidgets.QLabel(f'{DrawerParameter["Drawer_Title"]}')
        QLabel_Title.setStyleSheet('background:transparent; color:white;font-size:16px')

        QLabel_Title.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Title.setFixedHeight(62)
        QLabel_Title.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        QLabel_Content = Component_Devicetree.Component_Devicetree_List(self)
        QLabel_Content.setStyleSheet('background:rgba(40, 52, 80, 0.6);')
        # 连接设备树的信号到视频播放页面的处理方法
        QLabel_Content.Signal_Result.connect(self.PAGE_HANDLE_RESULT)

        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Title, alignment=QtCore.Qt.AlignTop)
        self.QVBoxLayout_Right_Drawer.addWidget(QLabel_Content)

    def Function_QLabel_Setcel_Change(self,Index):
        # print("parent_method"*888)
        Page_Info["Page_Element_List"]["Function_QLabel_List"][Index].setStyleSheet("QLabel {border-radius: 4px;border: 3px solid  cyan}")
        # Page_Info["Page_Element_List"]["Set_Analysis_Face"].setStyleSheet("QLabel {border-radius: 4px;border: 1px solid  blue}")
        # try:
        #     while Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"]["1"].count():
        #         Item = Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"]["1"].takeAt(0)
        #         if Item.widget():
        #             Item.widget().deleteLater()
        # except:
        #     pass

        # Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"]["1"].addWidget(self.Set_Analysis_Face_Result())

    def Function_QLabel_Setcel_Reback(self,Index):
        print("parent_method"*888)
        StyleSheet_QLabel = """
                                                         QLabel {
                                                           background-color: rgba(40, 52, 80, 0.3);;
                                                             border: 1px solid rgba(0, 180, 255, 60);
                                                             border-radius: 4px;

                                                             color:rgba(0, 255, 136, 255);

                                                         }
                                                         QLabel:hover {
                                                             background-color: rgba(0, 100, 150, 150);
                                                         }
                                                     """
        Page_Info["Page_Element_List"]["Function_QLabel_List"][Index].setStyleSheet(StyleSheet_QLabel)


    def Set_Title_Menu(self):
        __QLabel = QtWidgets.QLabel()
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setAlignment( QtCore.Qt.AlignLeft)
        __QHBoxLayout.setContentsMargins(10, 3, 3, 3)

        StyleSheet_QLabel = """
                                                             QLabel {
                                                                 background-color:rgba(40, 52, 80, 0.3);
                                                                 border: 0px solid rgba(0, 180, 255, 60);
                                                                 border-radius: 3px;
                                                                 padding: 0px;
                                                                 font-size:13px;
                                                                 font-weight: bold;
                                                                 color:white;
                                                             }
                                                             QLabel:hover {
                                                                 background-color: rgba(0, 100, 150, 150);
                                                             }
                                                         """

        def Set_Buttun(Menu_Info):
            _QLabel = Component_Common.Component_Common_QLabel_Click()
            _QLabel.setFixedSize(60, 30)
            # _QLabel.setStyleSheet("QLabel{background-color: rgba(255, 255, 255, 1);color:white,font-size:13px;border-radius: 3px;}")
            _QLabel.setStyleSheet(StyleSheet_QLabel)
            _QLabel.setAlignment(QtCore.Qt.AlignCenter)
            _QLabel.setText(Menu_Info["Menu_Name"])
            _QLabel.clicked.connect(lambda: Page_Info["Page_Def_List"][Menu_Info["Menu_Command"]["Command"]](Menu_Info["Menu_Command"]))

            # if ["Menu_Name"]  in ["1","2","4","9"]:
            #     _QLabel.clicked.connect(lambda : self.Re_Set_Channel(Menu_Info["Menu_Name"]))
            # if ["Menu_Name"] in ["设备", "功能"]:
            #
            # if ["Menu_Name"] in ["严控", "功能"]:
            #         _QLabel.clicked.connect(lambda: Page_Info["Page_Def_List"]["Set_DrawerSetting"](Menu_Info["Menu_Command"]))
            return _QLabel

        # ["严控", "1", "2", "4", "9", "设备", "功能"]:
        Menu_List=[
            {"Menu_Name":"严控" , "Menu_Command":{"Command":"Set_Strict_Control", 'Drawer_Type': 'Device_Tree', 'Drawer_Title': '设备树',}},
            {"Menu_Name":"1" ,    "Menu_Command":{"Command":"Re_Set_Channel", 'Channel_Number': '1',}},
            {"Menu_Name":"2" ,    "Menu_Command":{"Command":"Re_Set_Channel", 'Channel_Number': '2',}},
            {"Menu_Name":"4" ,    "Menu_Command":{"Command":"Re_Set_Channel", 'Channel_Number': '4' }},
            {"Menu_Name":"9" ,    "Menu_Command":{"Command":"Re_Set_Channel", 'Channel_Number': '9',}},
            {"Menu_Name":"设备"  ,"Menu_Command":{"Command":"Set_DrawerSetting",'Drawer_Type': 'Device_Tree', 'Drawer_Title': '设备树',}},
            {"Menu_Name":"功能"  ,"Menu_Command":{"Command":"Set_DrawerSetting",'Drawer_Type': 'Config_Function', 'Drawer_Title': '哨兵功能',}},
        ]

        for Menu_Info in Menu_List:
            __QHBoxLayout.addWidget(Set_Buttun(Menu_Info))

        # QLabel_Device = Component_Common.Component_Common_QLabel_Click()
        # QLabel_Device.setFixedSize(60, 30)
        # QLabel_Device.setStyleSheet(StyleSheet_QLabel)
        # QLabel_Device.setAlignment(QtCore.Qt.AlignCenter)
        # QLabel_Device.setText('设备')
        # Device_Info = {
        #     'Drawer_Type': 'Device_Tree',
        #     'Drawer_Title': '设备树',
        # }
        # QLabel_Device.clicked.connect(lambda: Page_Info["Page_Def_List"]["Set_DrawerSetting"](Device_Info))
        # __QHBoxLayout.addWidget(QLabel_Device)
        #
        #
        # QLabel_Function = Component_Common.Component_Common_QLabel_Click()
        # QLabel_Function.setFixedSize(60, 30)
        # # _QLabel.setStyleSheet("QLabel{background-color: rgba(255, 255, 255, 1);color:white,font-size:13px;border-radius: 3px;}")
        # QLabel_Function.setStyleSheet(StyleSheet_QLabel)
        # QLabel_Function.setAlignment(QtCore.Qt.AlignCenter)
        #
        # QLabel_Function.setText('功能')
        # Function_Info={
        #     'Drawer_Type':'Config_Function',
        #     'Drawer_Title':'哨兵功能',
        # }
        #
        #
        # QLabel_Function.clicked.connect(lambda :Page_Info["Page_Def_List"]["Set_DrawerSetting"](Function_Info))
        # __QHBoxLayout.addWidget(QLabel_Function)


        return __QLabel

    def Set_System_Status(self):
        __QLabel = QtWidgets.QLabel()
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setAlignment( QtCore.Qt.AlignRight)
        __QHBoxLayout.setContentsMargins(3, 3, 18, 3)
        self.QLabel_Show_Time = QtWidgets.QLabel()
        self.QLabel_Show_Time.setStyleSheet("""QLabel{color:rgba(255, 255, 255, 255);font-size:15px; background-color: transparent;}""")
        self.QLabel_Show_Time.setFont(QtGui.QFont("Microsoft YaHei", 15))
        self.QLabel_Show_Time.setText(str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))

        Menu_Info = {"Menu_Icon": "fa6.circle-play", "Menu_Size": 18, "Menu_Command": {"Command": "Player"}}
        Icon = qtawesome.icon(Menu_Info["Menu_Icon"], color='white', size=Menu_Info["Menu_Size"])

        QLabel_CPU = QtWidgets.QLabel()
        QLabel_CPU.setFixedSize(120,60)
        QLabel_CPU.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_CPU.setText('<div style="text-align:center;">'
                             '<span style="font-size:13pt;color:rgba(0, 255, 136, 255);font-weight: bold">15%</span><br>'
                             '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">CPU使用率</span>'
                              '</div>')

        QLabel_GPU = QtWidgets.QLabel()
        QLabel_GPU.setFixedSize(120,60)
        QLabel_GPU.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_GPU.setText('<div style="text-align:center;">'
                             '<span style="font-size:13pt;color:rgba(0, 255, 136, 255);font-weight: bold">11%</span><br>'
                             '<span style="font-size:8pt;color:#1E90FF;font-weight: bold">GPU使用率</span>'
                              '</div>')

        __QHBoxLayout.addWidget(QLabel_CPU)
        __QHBoxLayout.addWidget(QLabel_GPU)
        __QHBoxLayout.addWidget(self.QLabel_Show_Time)




        return __QLabel





    def Set_Content(self):










        __QVBoxLayout = QtWidgets.QVBoxLayout( self.QLabel_Content)
        __QVBoxLayout.setContentsMargins(3, 3, 3, 3)

        self.QLabel_Video_Show = QtWidgets.QLabel()
        self.QLabel_Video_Show.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")

        self.QLabel_Video_Control = QtWidgets.QLabel()
        self.QLabel_Video_Control.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 3px; border: 0px; }")

        __QVBoxLayout.addWidget(self.QLabel_Video_Show, 3)
        __QVBoxLayout.addWidget(self.QLabel_Video_Control, 1)


        self.Set_Video_Show()
        self.Set_Video_Function()







    def Set_Video_Show(self):
        self.QGridLayout_Channels = QtWidgets.QGridLayout(self.QLabel_Video_Show)
        self.QGridLayout_Channels.setSpacing(0)
        self.QGridLayout_Channels.setContentsMargins(0, 0, 0, 0)
        while self.QGridLayout_Channels.count():
            item = self.QGridLayout_Channels.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        Channel_ID = 1
        for row in range(1):
            for col in range(2):
                self.QGridLayout_Channels.addWidget(self.Set_Channel(Channel_ID), row, col)
                Channel_ID += 1

    def Set_Video_Function(self):
        StyleSheet_QLabel = """
                                                   QLabel {
                                                       background-color: rgba(0, 255, 255, 0.8);
                                                       border: 1px solid rgba(0, 180, 255, 60);
                                                       border-radius: 0px;
                                                       padding: 0px;
                                                       color:white;
                                                   }
                                                   QLabel:hover {
                                                       background-color: rgba(0, 100, 150, 150);
                                                   }
                                               """
        # __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Video_Control)
        # # QHBoxLayout_Row4.setSpacing(0)
        # __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        QGridLayout_Content = QtWidgets.QGridLayout(self.QLabel_Video_Control)
        QGridLayout_Content.setSpacing(0)
        QGridLayout_Content.setContentsMargins(0,0, 0, 0)

        StyleSheet_QLabel = """
                                                 QLabel {
                                                   background-color: rgba(40, 52, 80, 0.3);;
                                                     border: 1px solid rgba(0, 180, 255, 60);
                                                     border-radius: 4px;

                                                     color:rgba(0, 255, 136, 255);

                                                 }
                                                 QLabel:hover {
                                                     background-color: rgba(0, 100, 150, 150);
                                                 }
                                             """
        # self.Function_List=[]
        Function_List = [
            {"Function_Text": "今日中标", "Function_Value": "0人"},
            {"Function_Text": "中标总数", "Function_Value": "8人"},
            {"Function_Text": "部署人数", "Function_Value": "8人"},
            {"Function_Text": "目标群体", "Function_Value": "18人"},
            {"Function_Text": "目标群体", "Function_Value": "18人"},
            {"Function_Text": "目标群体", "Function_Value": "18人"},

        ]



        # for in
        Page_Info["Page_Element_List"]["Function_QLabel_List"]      = {}
        Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"] = {}


        Count = 0
        for Row in range(1):
            for Col in range(6):
                _QLabel = QtWidgets.QLabel()
                _QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
                # _QLabel.setFixedHeight(60)
                _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
                _QLabel.setText(f'''<div style="text-align:center;">
                                    <span style="font-size:13pt;font-weight: bold">{Function_List[Count]["Function_Value"]}</span><br>
                                    <span style="font-size:8pt;color:#1E90FF;font-weight: bold">{Function_List[Count]["Function_Text"]}</span>
                                    </div>''')
                _QHBoxLayout = QtWidgets.QHBoxLayout(_QLabel)
                _QHBoxLayout.setSpacing(0)
                _QHBoxLayout.setContentsMargins(0, 0, 0, 0)
                Page_Info["Page_Element_List"]["Function_QLabel_List"][f"{Col+1}"]      =  _QLabel
                Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"][f"{Col+1}"] =  _QHBoxLayout
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1


        PJ(Page_Info["Page_Element_List"])
        Function_QHBoxLayout_List={}
        Function_QHBoxLayout_List["1"] = self.Set_Analysis_Face()
        Function_QHBoxLayout_List["2"] = self.Set_Analysis_Face_Result()
        Function_QHBoxLayout_List["3"] = self.Set_Analysis_Radar()
        Function_QHBoxLayout_List["4"] = self.Set_Analysis_Radar_Result()
        Function_QHBoxLayout_List["5"] = self.Set_Analysis_Vibration()
        Function_QHBoxLayout_List["6"] = self.Set_Analysis_Track()

        # )
        # __QHBoxLayout.addWidget(self.Set_Analysis_Radar_Result())


        # __QHBoxLayout.addWidget(self.Set_Analysis_Vibration())
        # __QHBoxLayout.addWidget(self.Set_Analysis_Vibration())






        for Name, _QHBoxLayout in Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"].items():
            _QHBoxLayout.addWidget( Function_QHBoxLayout_List[Name])






        # QLabel_Co1 = QtWidgets.QLabel()
        # QLabel_Co1.setStyleSheet(StyleSheet_QLabel)
        #
        # QLabel_Co2 = QtWidgets.QLabel()
        # QLabel_Co2.setStyleSheet(StyleSheet_QLabel)
        #
        # QLabel_Co3 = QtWidgets.QLabel()
        # QLabel_Co3.setStyleSheet(StyleSheet_QLabel)
        #
        # __QHBoxLayout.addWidget(self.Set_Co11())
        # __QHBoxLayout.addWidget(self.Set_Co12())
        # __QHBoxLayout.addWidget(self.Set_Co13())

    def Set_Co11(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        # Page_Info["Page_Element_List"]["Set_Analysis_Face"] = self.Set_Analysis_Face()


        # __QHBoxLayout.addWidget(Page_Info["Page_Element_List"]["Set_Analysis_Face"])
        # __QHBoxLayout.addWidget(self.Set_Analysis_Face_Result())
        # __QHBoxLayout.addWidget(QLabel_Analysis_Correlation)

        return __QLabel

    def Set_Co12(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        __QHBoxLayout.addWidget(self.Set_Analysis_Radar())
        __QHBoxLayout.addWidget(self.Set_Analysis_Radar_Result())
        return __QLabel

    def Set_Co13(self):

        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("""QLabel {background-color: transparent;border: 0px solid rgba(0, 180, 255, 60);""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        # __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        __QHBoxLayout.addWidget(self.Set_Analysis_Vibration())
        __QHBoxLayout.addWidget(self.Set_Analysis_Vibration())
        return __QLabel

    def Set_Analysis_Face(self):
        QLabel_Info = {
            "Title_Name": "人脸预警"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        #
        StyleSheet_QLabel = """
                                         QLabel {
                                           background-color: rgba(40, 52, 80, 0.3);;
                                             border: 1px solid rgba(0, 180, 255, 60);
                                             border-radius: 4px;

                                             color:rgba(0, 255, 136, 255);

                                         }
                                         QLabel:hover {
                                             background-color: rgba(0, 100, 150, 150);
                                         }
                                     """

        Function_List = [
            {"Function_Text": "今日告警","Function_Value": "0人", "Function_Command": {"Type": "Self_Open_Processing","Processing": "Processing_Portrait"}},
            # {"Function_Text":"今日告警", "Function_Value": "0人", "Function_Command": {"Type": "Self_Open_Processing","Processing": "Processing_Portrait"}},
            {"Function_Text": "中标人数","Function_Value": "8人"},
            {"Function_Text": "部署人数","Function_Value": "3人", "Function_Command": {"Type": "Self_Open_Processing","Processing": "Processing_Portrait"}},
            {"Function_Text": "本月中标","Function_Value": "18人"},

        ]
        Count=0
        for Row in range(2):
            for Col in range(2):
                # _QLabel = QtWidgets.QLabel()
                _QLabel = Component_Common.Component_Common_QLabel_Click()  # 使用可点击的QLabel
                _QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
                _QLabel.setFixedHeight(60)
                _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
                _QLabel.setText(f'''<div style="text-align:center;">
                            <span style="font-size:13pt;font-weight: bold">{Function_List[Count]["Function_Value"]}</span><br>
                            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">{Function_List[Count]["Function_Text"]}</span>
                            </div>''')

                if Function_List[Count]["Function_Text"] == "今日告警":
                    _QLabel.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Function_List[0]["Function_Command"]))


                if Function_List[Count]["Function_Text"] == "部署人数":
                    _QLabel.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Function_List[2]["Function_Command"]))

                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count+=1

        return __QLabel





    def load_face_alert_data(self):
        """加载今日人脸报警数据"""
        try:
            Data = {
                "Table_Name": "Portrait_Today_Alarm",
                "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Portrait\Portrait_Today_Alarm.db",
            }
            __Service_Table = Service_Table.Service_Table("sqlite_backall")
            raw_data = __Service_Table(Data)

            # 转换为需要的格式
            Face_List = []
            for item in raw_data:
                Face_List.append({
                    "Face_Name": f"嫌疑人：{item['PORTRAIT_NAME']}",
                    "Face_Image": item['PORTRAIT_PHOTO'],  # 人像特征图片
                    "Video_Image": item['PORTRAIT_VIDEO'], # 视频截取图片
                    "Case_Info": item['PORTRAIT_CASE'],
                    "Update_Time": item['PORTRAIT_UPDATE']
                })
            PP(f"成功加载 {len(Face_List)} 条人脸报警数据")
            return Face_List
        except Exception as e:
            PP(f"加载人脸报警数据失败: {e}")


    def Set_Analysis_Face_Result(self):
        QLabel_Info = {
            "Title_Name": "人脸布控详情"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        # 创建滚动区域
        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        # scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        # scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)  # 隐藏垂直滚动条
        scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)  # 隐藏水平滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(40, 52, 80, 0.3);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(0, 180, 255, 100);
                border-radius: 6px;
                min-height: 20px;
            }
        """)

        scroll_widget = QtWidgets.QWidget()
        QGridLayout_Content = QtWidgets.QGridLayout(scroll_widget)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        scroll_area.setWidget(scroll_widget)

        # 将滚动区域添加到主布局
        main_layout = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # 加载数据
        Face_List = self.load_face_alert_data()

        total_items = len(Face_List)

        for Row in range(total_items):
            face_widget = self.Set_Face_Group(Face_List[Row])
            QGridLayout_Content.addWidget(face_widget, Row, 0)

        return __QLabel

    def Set_Face_Group(self, Face_Info):
        # 创建容器
        container = QtWidgets.QWidget()
        container.setFixedSize(240, 148)
        container.setStyleSheet("""
            QWidget {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                color: rgba(0, 255, 136, 255);
            }
            QWidget:hover {
                background-color: rgba(0, 100, 150, 150);
            }
        """)

        # 主布局（垂直）
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 图片布局（水平）
        images_layout = QtWidgets.QHBoxLayout()
        images_layout.setSpacing(5)

        # 左侧：人像特征图片
        photo_label = QtWidgets.QLabel()
        photo_label.setFixedSize(108, 108)
        photo_label.setScaledContents(True)
        photo_label.setStyleSheet("background-color: red;")

        # 加载人像特征图片
        if os.path.exists(Face_Info["Face_Image"]):
            pixmap = QtGui.QPixmap(Face_Info["Face_Image"])
            if not pixmap.isNull():
                photo_label.setPixmap(pixmap)
            else:
                photo_label.setText("人像\n未找到")
                photo_label.setAlignment(QtCore.Qt.AlignCenter)
        else:
            photo_label.setText("人像\n未找到")
            photo_label.setAlignment(QtCore.Qt.AlignCenter)

        # 右侧：视频截取图片
        video_label = QtWidgets.QLabel()
        video_label.setFixedSize(108, 108)
        video_label.setScaledContents(True)
        video_label.setStyleSheet("background-color: red;")

        # 加载视频截取图片
        if "Video_Image" in Face_Info and os.path.exists(Face_Info["Video_Image"]):
            video_pixmap = QtGui.QPixmap(Face_Info["Video_Image"])
            if not video_pixmap.isNull():
                video_label.setPixmap(video_pixmap)
            else:
                video_label.setText("视频\n未找到")
                video_label.setAlignment(QtCore.Qt.AlignCenter)
        else:
            video_label.setText("视频\n未找到")
            video_label.setAlignment(QtCore.Qt.AlignCenter)

        images_layout.addWidget(photo_label)
        images_layout.addWidget(video_label)

        # 底部文字信息
        info_layout = QtWidgets.QHBoxLayout()
        info_layout.setSpacing(5)

        # 左侧标签
        left_label = QtWidgets.QLabel(Face_Info["Face_Name"])
        left_label.setStyleSheet("""
            QLabel {
                background: transparent;
                border: none;
                font-size: 8pt;
                color: rgba(0, 255, 136, 255);
                font-weight: bold;
            }
        """)
        left_label.setAlignment(QtCore.Qt.AlignCenter)

        # 右侧标签
        right_label = QtWidgets.QLabel("中标人像")
        right_label.setStyleSheet("""
            QLabel {
                background: transparent;
                border: none;
                font-size: 8pt;
                color: rgba(0, 255, 136, 255);
                font-weight: bold;
            }
        """)
        right_label.setAlignment(QtCore.Qt.AlignCenter)

        info_layout.addWidget(left_label)
        info_layout.addWidget(right_label)

        # 添加到主布局
        main_layout.addLayout(images_layout, 9)  # 图片占大部分空间
        main_layout.addLayout(info_layout, 1)    # 文字占小部分空间

        return container

    def Set_Face(self,Face_Info):
        StyleSheet_QLabel = """
                                                           QLabel {
                                                             background-color: rgba(40, 52, 80, 0.3);;
                                                               border: 1px solid rgba(0, 180, 255, 60);
                                                               border-radius: 4px;

                                                               color:rgba(0, 255, 136, 255);

                                                           }
                                                           QLabel:hover {
                                                               background-color: rgba(0, 100, 150, 150);
                                                           }
                                                       """
        __QLabel = QtWidgets.QLabel()
        __QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
        __QLabel.setFixedSize(108,148)
        # __QLabel.setFixedHeight(148)
        # '"D:\Data\Image\Face\001.png"'

        __QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框

        __QVBoxLayout = QtWidgets.QVBoxLayout(__QLabel)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(0)

        QLabel_Face = QtWidgets.QLabel()
        QLabel_Face.setStyleSheet("background-color: red;")
        QLabel_Face.setScaledContents(True)
        QLabel_Face.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        image_path =Face_Info["Face_Image"]
        pixmap = QtGui.QPixmap(image_path)
        target_size = QLabel_Face.size()

        # 2. 使用缓存的QPixmap避免重复创建
        # if not hasattr(self, '_cached_pixmap'):
        #     self._cached_pixmap = QtGui.QPixmap()

        # 3. 直接缩放并设置（保持纵横比）
        if not pixmap.isNull():
            # 设置图片到 QLabel
            QLabel_Face.setPixmap(pixmap)
        else:
            print("图片加载失败，请检查路径是否正确")



        QLabel_Info = QtWidgets.QLabel()
        QLabel_Info.setStyleSheet("""background: transparent;border: none;""")
        QLabel_Info.setText(f'''<div style="text-align:center;">
                               <span style="font-size:8pt;color:rgba(0, 255, 136, 255);font-weight: bold">{Face_Info["Face_Name"]}</span>
                               </div>''')





        __QVBoxLayout.addWidget(QLabel_Face,9)
        __QVBoxLayout.addWidget(QLabel_Info,1)
        return __QLabel


    def Set_Analysis_Track(self):

        QLabel_Info = {
            "Title_Name": "人体雷达"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        #
        StyleSheet_QLabel = """
                                                  QLabel {
                                                    background-color: rgba(40, 52, 80, 0.3);;
                                                      border: 1px solid rgba(0, 180, 255, 60);
                                                      border-radius: 4px;

                                                      color:rgba(0, 255, 136, 255);

                                                  }
                                                  QLabel:hover {
                                                      background-color: rgba(0, 100, 150, 150);
                                                  }
                                              """

        __Component_Track=Component_Track.Component_Track()
        QGridLayout_Content.addWidget(__Component_Track,0,1)
        return __QLabel




    def Set_Analysis_Radar(self):
        QLabel_Info = {
            "Title_Name": "人体雷达"
        }
        __QLabel = QLabel_Function(QLabel_Info)

        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        #
        StyleSheet_QLabel = """
                                            QLabel {
                                              background-color: rgba(40, 52, 80, 0.3);;
                                                border: 1px solid rgba(0, 180, 255, 60);
                                                border-radius: 4px;

                                                color:rgba(0, 255, 136, 255);

                                            }
                                            QLabel:hover {
                                                background-color: rgba(0, 100, 150, 150);
                                            }
                                        """

        Function_List = [

            {"Function_Text": "今日中标", "Function_Value": "0次","Function_Command": {"Type": "Self_Open_Processing", "Processing": "Processing_Radar"}},
            {"Function_Text": "中标总数", "Function_Value": "2次"},
            {"Function_Text": "部署地点", "Function_Value": "8个"},
            {"Function_Text": "目标群体", "Function_Value": "8人"},

        ]
        Count = 0
        for Row in range(2):
            for Col in range(2):
                # _QLabel = QtWidgets.QLabel()
                _QLabel = Component_Common.Component_Common_QLabel_Click()  # 使用可点击的QLabel

                _QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
                _QLabel.setFixedHeight(60)
                _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
                _QLabel.setText(f'''<div style="text-align:center;">
                               <span style="font-size:13pt;font-weight: bold">{Function_List[Count]["Function_Value"]}</span><br>
                               <span style="font-size:8pt;color:#1E90FF;font-weight: bold">{Function_List[Count]["Function_Text"]}</span>
                               </div>''')


                if Function_List[Count]["Function_Text"] == "今日中标":
                    _QLabel.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Function_List[0]["Function_Command"]))
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1

        return __QLabel

    def Set_Analysis_Radar_Result(self):
        QLabel_Info = {
            "Title_Name": "人体雷达监控结果"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        #
        StyleSheet_QLabel = """
                                                   QLabel {
                                                     background-color: rgba(40, 52, 80, 0.3);;
                                                       border: 1px solid rgba(0, 180, 255, 60);
                                                       border-radius: 4px;

                                                       color:rgba(0, 255, 136, 255);

                                                   }
                                                   QLabel:hover {
                                                       background-color: rgba(0, 100, 150, 150);
                                                   }
                                               """
        self.__Component_Rolling_Message = Component_Rolling_Message.Component_Rolling_Message()

        self.QProgressBar_Radar = QtWidgets.QProgressBar()
        self.QProgressBar_Radar.setRange(0, 0)  # 隐藏默认文本

        self.QProgressBar_Radar.setStyleSheet("""
                            QProgressBar {
                                border: 1px solid #4cc9f0;
                                border-radius: 3px;
                                background-color: rgba(0, 0, 0, 0.1);
                                height: 6px;
                                margin-left:8px;
                                margin-top:18px;
                            }
                            QProgressBar::chunk {
                                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4cc9f0, stop:1 #0077cc);
                                border-radius: 0px;
                            }
                        """)
        # self.__QVBoxLayout.addWidget(self.QProgressBar_Loading)

        QGridLayout_Content.addWidget(self.__Component_Rolling_Message, 0, 1)
        QGridLayout_Content.addWidget(self.QProgressBar_Radar, 1, 1)
        self.QProgressBar_Radar.setVisible(False)


        return __QLabel

    def Check_Meassage(self):

        self.QProgressBar_Radar.setVisible(True)

        __QTimer_Player = QtCore.QTimer(self)
        __QTimer_Player.setSingleShot(True)  # 设置为单次触发
        __QTimer_Player.timeout.connect(self.Update_Message)  # 连接到要执行的函数
        __QTimer_Player.start(5000)

    def Update_Message(self):
        self.QProgressBar_Radar.setVisible(False)
        self.__Component_Rolling_Message.Update_Message()

    def Set_Analysis_Vibration(self):
        QLabel_Info = {
            "Title_Name": "开门震动预警"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(8, 20, 8, 20)

        #
        StyleSheet_QLabel = """
                                            QLabel {
                                              background-color: rgba(40, 52, 80, 0.3);;
                                                border: 1px solid rgba(0, 180, 255, 60);
                                                border-radius: 4px;

                                                color:rgba(0, 255, 136, 255);

                                            }
                                            QLabel:hover {
                                                background-color: rgba(0, 100, 150, 150);
                                            }
                                        """

        Function_List = [
            {"Function_Text": "今日中标", "Function_Value": "0人", "Function_Command": {"Type": "Self_Open_Processing", "Processing": "Processing_Dooralarm"}},
            {"Function_Text": "中标总数", "Function_Value": "12次"},
            {"Function_Text": "部署地点", "Function_Value": "22个"},
            {"Function_Text": "目标群体", "Function_Value": "11人"},

        ]
        Count = 0
        for Row in range(2):
            for Col in range(2):
                # _QLabel = QtWidgets.QLabel()
                _QLabel = Component_Common.Component_Common_QLabel_Click()  # 使用可点击的QLabel
                _QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
                _QLabel.setFixedHeight(60)
                _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
                _QLabel.setText(f'''<div style="text-align:center;">
                               <span style="font-size:13pt;font-weight: bold">{Function_List[Count]["Function_Value"]}</span><br>
                               <span style="font-size:8pt;color:#1E90FF;font-weight: bold">{Function_List[Count]["Function_Text"]}</span>
                               </div>''')
                if Function_List[Count]["Function_Text"] == "今日中标":
                    _QLabel.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Function_List[0]["Function_Command"]))
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1

        return __QLabel

    def Set_Strict_Control(self,*args):
        PP("Set_Strict_Control")
        while self.QGridLayout_Channels.count():
            item = self.QGridLayout_Channels.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.QGridLayout_Channels.addWidget(self.Set_Channel("1"), 1, 1)
        self.QGridLayout_Channels.addWidget(self.Set_Analysis_Track(), 1, 2)



    def Re_Set_Channel(self,ChannelParameter):
        PP("Re_Set_Channel")
        Channel_Number  = ChannelParameter["Channel_Number"]
        if Channel_Number == "9":
            self.Set_PopupDialog({"Command": "Normal","Dialog_Content":"抱歉，该功能为服务器版本才能使用！"})
        else:
            Channel_List={"1":[1,1],"2":[1,2],"4":[2,2],"9":[3,3],}
            while self.QGridLayout_Channels.count():
                item = self.QGridLayout_Channels.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            Channel_ID = 1
            for Row in range(Channel_List[Channel_Number][0]):
                for Col in range(Channel_List[Channel_Number][1]):
                    self.QGridLayout_Channels.addWidget(self.Set_Channel(Channel_ID), Row, Col)
                Channel_ID += 1

    def Set_Channel(self,Channel_ID):
        QLabel_Info = {
            "Title_Name": "视频播放",
            "Channel_ID": Channel_ID,


        }
        __QLabel = QLabel_Channel(QLabel_Info)
        Page_Info["Page_Element_List"][f"QLabel_Channel_{Channel_ID}"]=__QLabel
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                                       QLabel {
                                         background-color: rgba(40, 52, 80, 0.3);;
                                           border: 1px solid rgba(0, 180, 255, 60);
                                           border-radius: 4px;

                                           color:rgba(0, 255, 136, 255);

                                       }
                                       QLabel:hover {
                                           background-color: rgba(0, 100, 150, 150);
                                       }
                                   """

        # Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{Channel_ID}"]

        __QLabel_Video_Channel = QLabel_Video_Play({"Channel_ID":Channel_ID})
        Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{Channel_ID}"] = __QLabel_Video_Channel
        QHBoxLayout_Content.addWidget(__QLabel_Video_Channel)

        return __QLabel

    def Set_Channel_2(self):
        QLabel_Info = {
            "Title_Name": "视频播放"
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(__QLabel.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)
        #
        StyleSheet_QLabel = """
                                          QLabel {
                                            background-color: rgba(40, 52, 80, 0.3);;
                                              border: 1px solid rgba(0, 180, 255, 60);
                                              border-radius: 4px;

                                              color:rgba(0, 255, 136, 255);

                                          }
                                          QLabel:hover {
                                              background-color: rgba(0, 100, 150, 150);
                                          }
                                      """

        __QLabel_Video_Channel = QLabel_Video_Channel(
            {"ID": f"{1}", "Icon": 'fa5.play-circle', 'Name': 'No Signal', "Size": 38})
        QHBoxLayout_Content.addWidget(__QLabel_Video_Channel)

        return __QLabel



    def Toggle_Drawer_Right(self):
        end_width = 0 if self.is_expanded_Drawer_Right else 300
        self.animation = QtCore.QPropertyAnimation(self.QWidget_Right_Drawer, b"minimumWidth")
        self.animation.setDuration(200)  # 动画持续时间500ms
        self.animation.setStartValue(self.QWidget_Right_Drawer.width())
        self.animation.setEndValue(end_width)
        self.animation.start()

        self.is_expanded_Drawer_Right = not self.is_expanded_Drawer_Right





    def Page_Update_Timer_Date(self):
        self.QLabel_Show_Time.setText(str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))

    def PAGE_HANDLE_RESULT(self,Result):
        pass
        PP(Result)
        print('Result:',Result)
        # 处理设备树发送的视频源设置命令
        if Result.get("Command") == "SetVideoSource":
            self.Handle_Device_Video_Source(Result)
            return



        if "Dialog_Info" in Result and "Play_Channel" in Result["Dialog_Info"]:
            # Play_Video
            # Page_Info["Page_Channel_List"][f"Channel_{Result['Dialog_Info']['Play_Channel']}"] = {"Video_Source":Result["Dialog_Info"]["Play_Source"]}
            # Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{Result['Dialog_Info']['Play_Channel']}"].Video_Play()
            QLabel_Menu_Paly = Page_Info["Page_Element_List"][f"Channel_Menu_{Result['Dialog_Info']['Play_Channel']}"]["Play"]
            QLabel_Channel = Page_Info["Page_Element_List"][f"QLabel_Channel_{Result['Dialog_Info']['Play_Channel']}"]


        Icon = qtawesome.icon("fa5.stop-circle", color='red', size=18)
        QLabel_Menu_Paly.setPixmap(Icon.pixmap(18, 18))
        try:QLabel_Menu_Paly.clicked.disconnect()
        except :pass
        QLabel_Menu_Paly.clicked.connect(lambda: QLabel_Channel.Menu_Command("Stop"))

        # # self.Set_Title()
        # self.Set_Content()
        #
        # self.setStyleSheet("background-color: rgba(40, 52, 80, 0.3)")

    def Handle_Device_Video_Source(self, result):
        """处理设备树发送的视频源信息"""
        try:
            device_name = result.get("Device_Name", "")
            channel_id = result.get("Channel_ID", "")
            video_source = result.get("Video_Source", "")

            if not all([device_name, channel_id, video_source]):
                PE("设备视频源信息不完整")
                return

            # 更新指定通道的视频源
            Page_Info["Page_Channel_List"][f"Channel_{channel_id}"] = {"Video_Source": video_source}

            # 更新通道状态显示
            if f"QLabel_Status_Source_{channel_id}" in Page_Info["Page_Element_List"]:
                status_label = Page_Info["Page_Element_List"][f"QLabel_Status_Source_{channel_id}"]
                status_label.setText(
                    f'''<div style="text-align:center;">
                    <span style="font-size:8pt;color:#1E90FF;font-weight: bold">视频源：{video_source}</span>
                    </div>'''
                )

            # 如果通道的视频播放组件存在，重新加载视频
            if f"QLabel_Video_Channel_{channel_id}" in Page_Info["Page_Element_List"]:
                video_widget = Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{channel_id}"]
                if hasattr(video_widget, 'Video_Play'):
                    video_widget.Video_Play()

            PP(f"已成功设置通道 {channel_id} 的视频源为设备 '{device_name}' 的视频流")

        except Exception as e:
            PP(f"处理设备视频源时发生错误: {e}")

    def PAGE_HANDLER_EXECUTE(self, CommandParameter):
        """处理页面执行命令"""
        PP(f"PAGE_HANDLER_EXECUTE: {CommandParameter}")
        if "Self_" in CommandParameter["Type"]:
            PP(CommandParameter)
            self.Open_Processing(CommandParameter)
        else:
            # 如果有Signal_Result信号，发送信号
            if hasattr(self, 'Signal_Result'):
                self.Signal_Result.emit(CommandParameter)

    def Open_Processing(self, ProcessingParameter):
        """打开处理弹窗"""
        PP(f"Open_Processing: {ProcessingParameter}")
        try:
            # 动态创建组件
            exec(
                f"QLabel_Component= Component_{ProcessingParameter['Processing']}.Component_{ProcessingParameter['Processing']}(self,ProcessingParameter)")
            # 连接信号（如果组件有Signal_Result信号）
            exec(
                "if hasattr(QLabel_Component, 'Signal_Result'): QLabel_Component.Signal_Result.connect(self.PAGE_HANDLER_EXECUTE)")
            # 居中显示
            exec("QLabel_Component.move(self.geometry().center() - QLabel_Component.rect().center())")
            # 显示弹窗
            exec("QLabel_Component.show()")
            PP("弹窗已打开")
        except Exception as e:
            PP(f"打开弹窗时发生错误: {e}")


class QLabel_Channel(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:
            self.Channel_Info = args[0]
            self.Control_Info = {
                "Channel_ID": self.Channel_Info.get("Channel_ID", ""),
                # "Drawer_Type": "",
                # "Drawer_Title": ""
            }
        except:
            pass
        self.CHANNEL_PLAY_SOURCE = Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"]["Video_Source"]
        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 8, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        # QLabel_Icon.setGeometry(0,0,18, 18)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        # Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # QLabel_Icon.setPixmap(Image_Logo)

        Icon_List = {
            "视频播放": "video_playback.png",
            "人脸预警": "face.png",
            "人脸布控": "face_control.png",
            "人体雷达监控结果": "radar_control.png",
            "人体雷达": "radar.png",
            "开门震动预警": "door.png"
        }

        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.Channel_Info.get("Title_Name", "")
        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)
        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        QLabel_Title_Name.setText(self.Channel_Info["Title_Name"])

        self.QLabel_Title_Menu = QtWidgets.QLabel()
        # self.QLabel_Title_Menu.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.QLabel_Title_Menu.setVisible(False)
        QHBoxLayout_Menu = QtWidgets.QHBoxLayout(self.QLabel_Title_Menu)
        QHBoxLayout_Menu.setAlignment( QtCore.Qt.AlignRight)
        QHBoxLayout_Menu.setSpacing(8)
        QHBoxLayout_Menu.setContentsMargins(13,3, 14, 3)

        # Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)

        self.menu_button = QtWidgets.QPushButton()
        self.menu_button.setIcon(qta.icon('fa6s.display', color='white', ))
        self.menu_button.setFixedSize(38, 15)
        self.menu_button.setStyleSheet("""QPushButton { background-color: transparent;border: none; }QPushButton::menu-indicator {image: none;width: 0;height: 0;}QPushButton:hover {
                                                                   background-color: rgba(0, 100, 150, 150);border-radius: 3px;
                                                               }""")
        # 创建一个菜单
        Menu_Box = QtWidgets.QMenu(self)  # 第一个参数是菜单的标题，第二个参数是父对象
        Menu_Box.setStyleSheet("""
                    QMenu {
                        background-color: rgba(0, 0, 0, 0.3);
                        border: 0px solid rgba(255, 255, 255, 0.2);
                        border-radius: 8px;
                        color: white; /* 设置菜单项文字颜色为白色 */
                    }
                    QMenu::item {
                        padding: 8px 16px;
                        background-color: transparent;
                    }
                    QMenu::item:selected {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                    QMenu::separator {
                        height: 1px;
                        background: rgba(255, 255, 255, 0.2);
                    }
                    QMenu::indicator {
                        width: 0px;
                        height: 0px;
                        image: none;
                        subcontrol-position: right;
                    }
                """)
        # Menu_Box.resize(68, 68)
        Menu_Box.setIcon(qta.icon('ri.menu-line',color='white',options=[{'scale_factor': 1.2}]))

        Menu_List = [
            {"Name": "超高清(1920*1080)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 1},
            {"Name": "高清(720*576)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},
            {"Name": "标清(640*480)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},
            {"Name": "省流(480*320)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},

        ]

        for Menu in Menu_List:

                exec("Action_%s = QtGui.QAction(Menu['Name'], self)" % Menu['Command'])
                # exec("Action_%s.setIcon(qta.icon('%s'))" % (Menu['Command'], Menu['Icon']))
                # exec("Action_%s.triggered.connect(functools.partial(self.PAGE_HANDLE_RESULT, %s))" % (
                # Menu['Command'], str(Menu)))
                exec("Menu_Box.addAction(Action_%s)" % Menu['Command'])

        # 将菜单添加到菜单栏中
        # QMenuBar_Menu.addMenu(Menu_Box)
        self.menu_button.setMenu(Menu_Box)
        QHBoxLayout_Menu.addWidget( self.menu_button)








        Menu_List=[
            {"Menu_Name":"Play",         "Menu_Icon":"fa6.circle-play","Menu_Size":18,          "Menu_Command":{"Command":"Play"}},
            {"Menu_Name":"Save",         "Menu_Icon":"mdi.database-arrow-down","Menu_Size":18,  "Menu_Command":{"Command":"Save"}},
            {"Menu_Name":"ScreenShot",   "Menu_Icon":"fa5s.camera","Menu_Size":18,              "Menu_Command":{"Command":"ScreenShot"}},
            {"Menu_Name":"Chat",         "Menu_Icon":"fa6s.walkie-talkie","Menu_Size":18,       "Menu_Command":{"Command":"Chat"}},
            {"Menu_Name":"Voice",        "Menu_Icon":"ri.volume-mute-fill","Menu_Size":18,      "Menu_Command":{"Command":"Voice"}},
            {"Menu_Name": "Control",      "Menu_Icon": "mdi.arrow-all", "Menu_Size": 18,         "Menu_Command": {"Command": "Control"}},
            {"Menu_Name":"More",         "Menu_Icon":"ri.more-fill","Menu_Size":18,             "Menu_Command":{"Command":"More"}},
            # {"Menu_Icon":"mdi.stop-circle-outline","Menu_Size":18,"Menu_Command":{"Command":"Player"}},
        ]

        StyleSheet_QLabel = """
                                                               QLabel {
                                                                   background-color:rgba(40, 52, 80, 0.1);
                                                                   border: 0px solid rgba(0, 180, 255, 60);
                                                                   border-radius: 3px;
                                                                   padding: 0px;
                                                                   font-size:13px;
                                                                   font-weight: bold;
                                                                   color:white;
                                                               }
                                                               QLabel:hover {
                                                                   background-color: rgba(0, 100, 150, 150);
                                                               }
                                                           """
        Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]={}





        def Set_Menu(Menu_Info):
            Icon = qtawesome.icon(Menu_Info["Menu_Icon"], color='white', size=Menu_Info["Menu_Size"])
            QLabel_Menu = Component_Common.Component_Common_QLabel_Click()
            QLabel_Menu.setFixedSize(38, 20)
            QLabel_Menu.setStyleSheet(StyleSheet_QLabel)
            QLabel_Menu.setPixmap(Icon.pixmap(18, 18))
            QLabel_Menu.clicked.connect(lambda :self.Menu_Command(Menu_Info["Menu_Command"]))
            Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"][Menu_Info["Menu_Name"]] = QLabel_Menu
            # Page_Info["Page_Element_List"][f"Channel_Menu_{self.Control_Info['Channel_ID']}"][Menu_Info["Menu_Name"]] = QLabel_Menu

            return QLabel_Menu


        for Menu_Info in Menu_List:
            QHBoxLayout_Menu.addWidget(Set_Menu(Menu_Info))







        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 2)


        # btn_layout = QtWidgets.QHBoxLayout()
        QHBoxLayout_Title.addStretch()
        QHBoxLayout_Title.addWidget(self.QLabel_Title_Menu,1)





        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")




        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)
        __QVBoxLayout.addWidget(self.Set_Bottom())



    def Set_Bottom(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        __QLabel.setMinimumHeight(30)
        __QLabel.setMaximumHeight(30)
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QLabel_Status = QtWidgets.QLabel()
        self.QLabel_Status.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status.hide()

        self.QHBoxLayout_Status = QtWidgets.QHBoxLayout(self.QLabel_Status)
        self.QHBoxLayout_Status.setAlignment(QtCore.Qt.AlignLeft)
        self.QHBoxLayout_Status.setSpacing(0)
        self.QHBoxLayout_Status.setContentsMargins(8, 0, 0, 0)

        # Page_Info["Page_Channel_List"]["Channel_1"]["Video_Source"]
        PP(self.Channel_Info)



        self.QLabel_Status_Source = QtWidgets.QLabel()
        self.QLabel_Status_Source.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status_Source.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Status_Source.setText(
            f'''<div style="text-align:center;">
            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">视频源：{self.CHANNEL_PLAY_SOURCE}</span>
            </div>'''
        )
        # 标记各通道播放源
        Page_Info["Page_Element_List"][f"QLabel_Status_Source_{self.Channel_Info['Channel_ID']}"] = self.QLabel_Status_Source
        self.QHBoxLayout_Status.addWidget(self.QLabel_Status_Source)



        __QHBoxLayout.addWidget(self.QLabel_Status)

        return __QLabel






    def Menu_Command(self,Command):
        PP(("ReSet",Command))

        match Command["Command"]:
            # case "Play":
            #     Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
            #     Icon = qtawesome.icon("fa5.stop-circle", color='red', size=18)
            #     Menu_Play.setPixmap(Icon.pixmap(18, 18))
            #     try:Menu_Play.clicked.disconnect()
            #     except:pass
            #     Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Stop"}))
            #
            #     # Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source":r"D:\45M.mp4"}
            #     Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source":"https://42.193.47.16:443/rtp/34020000001320000001_34020000001320000001.live.flv?originTypeStr=rtp_push"}
            #     Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Play()
            case "Play":
                Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
                Icon = qtawesome.icon("fa5.stop-circle", color='red', size=18)
                Menu_Play.setPixmap(Icon.pixmap(18, 18))
                try:Menu_Play.clicked.disconnect()
                except:pass
                Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Stop"}))

                # 获取当前通道的视频源，如果没有设置则使用默认值
                current_video_source = Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"].get("Video_Source", "未知")

                # 如果视频源是"未知"，使用默认的测试视频源
                if current_video_source == "未知":
                    current_video_source = "https://42.193.47.16:443/rtp/34020000001320000001_34020000001320000001.live.flv?originTypeStr=rtp_push"
                    Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source": current_video_source}

                Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Play()



                # PP(Command*8)
            case "Stop":
                Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
                Icon = qtawesome.icon("fa6.circle-play", color='white', size=18)
                Menu_Play.setPixmap(Icon.pixmap(18, 18))
                try:Menu_Play.clicked.disconnect()
                except:pass
                Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Play"}))
                # PP(Command*8)


                Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Stop()


            case "More":
                PP("More")
                self.Channel_Info["Drawer_Type"]  = "Config_Channel"
                self.Channel_Info["Drawer_Title"] = "视频通道设置"
                Page_Info["Page_Def_List"]["Set_DrawerSetting"](self.Channel_Info)

            case "Control":
                PP("Control")
                self.Control_Info["Drawer_Type"]  = "Config_Control"
                self.Control_Info["Drawer_Title"] = "云台控制视频"
                # self.Control_Info["Channel_ID"] = self.Channel_Info["Channel_ID"]
                Page_Info["Page_Def_List"]["Set_DrawerSetting"](self.Control_Info)


            case _:print("unknown")











    # 鼠标事件
    def enterEvent(self, event):
        self.QLabel_Title_Menu.setVisible(True)
        self.QLabel_Status.setVisible(True)


        super().enterEvent(event)

    def leaveEvent(self, event):
        self.QLabel_Title_Menu.setVisible(False)
        self.QLabel_Status.setVisible(False)

        super().leaveEvent(event)
        #



class QLabel_Video_Play(QtWidgets.QLabel):
    Clicked_More = QtCore.Signal(dict)

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        self.Channel_Info = args[0]
        self.setMouseTracking(True)
        self._is_hovered = False
        self._is_Recode = False
        self.setAlignment(QtCore.Qt.AlignCenter)

        self.initUI()

    def initUI(self):
        self.__QVBoxLayout = QtWidgets.QVBoxLayout(self)
        self.__QVBoxLayout.setAlignment(QtCore.Qt.AlignCenter)
        self.__QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.__QVBoxLayout.setSpacing(0)



        # self.QLabel_Play_Status.clicked.connect(lambda :Page_Info["Page_Def_List"]["Set_PopupDialog"]("Normal"))
        # self.QLabel_Play_Status.clicked.connect(self.Play_Video)

        # Page_Info["Page_Def_List"]["Set_PopupDialog"]


        self.__QVBoxLayout.addWidget(self.Status_Default())

    def Status_Default(self):

        StyleSheet_QLabel = """
                    QLabel {
                        background-color: rgba(40, 52, 80, 0.13);
                        border: 0px solid rgba(0, 180, 255, 60);
                        border-radius: 4px;
                        color:rgba(0, 255, 136, 255);
                    }
                    QLabel:hover {
                        background-color: rgba(0, 100, 150, 150);
                    }
                """
        self.QLabel_Play_Status = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Play_Status.setMinimumSize(120, 60)
        self.QLabel_Play_Status.setMaximumSize(120, 60)
        self.QLabel_Play_Status.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Play_Status.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Play_Status.setText(
            f'''<div style="text-align:center;">
            <span style="font-size:13pt;font-weight: bold">视频{self.Channel_Info["Channel_ID"]}</span><br>
            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">No Signal</span>
            </div>'''
        )
        # self.QLabel_Play_Status.clicked.connect(lambda :Page_Info["Page_Def_List"]["Set_PopupDialog"]("Play"))
        self.QLabel_Play_Status.clicked.connect(lambda: Page_Info["Page_Def_List"]["Set_PopupDialog"]({"Command": "Media_PlayerConfig", "Channel_ID": self.Channel_Info["Channel_ID"]}))

        return self.QLabel_Play_Status


    def Video_Play(self):
        pass

        # Video_Info = Page_Info["Page_Channel_List"][f'Channel_{self.Channel_Info["Channel_ID"]}']
        # self.__Component_VideoPlay = Component_VideoPlay.Component_VideoPlay(Video_Info)
        # self.__Component_VideoPlay.show()

        self.Count = 9
        self.Set_Loading()


    def Set_Loading(self):
        # if self.Count  > 0:
        #     print(f"执行第 {10 - self.Count } 次")
        #     QtCore.QTimer.singleShot(1000, lambda: self.Set_Loading())
        #
        #     self.QLabel_Play_Status.setText(
        #         f'''<div style="text-align:center;">
        #         <span style="font-size:13pt;font-weight: bold">视频{self.Channel_Info["Channel_ID"]}</span><br>
        #         <span style="font-size:8pt;color:#1E90FF;font-weight: bold">Loading{'.'*(10-self.Count)}</span>
        #         </div>'''
        #     )
        #     self.Count-=1
        # else:
        #     print("执行完毕")
        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        self.QProgressBar_Loading = QtWidgets.QProgressBar()
        self.QProgressBar_Loading.setFixedSize(188,12)
        # self.progress_bar.setRange(0, 100)
        # self.progress_bar.setValue(0)
        # self.QProgressBar_Loading.setTextVisible(False)  # 隐藏默认文本
        self.QProgressBar_Loading.setRange(0, 0)  # 隐藏默认文本
        self.QProgressBar_Loading.setStyleSheet("""
                    QProgressBar {
                        border: 1px solid #4cc9f0;
                        border-radius: 3px;
                        background-color: rgba(0, 0, 0, 0.1);
                        height: 10px;
                    }
                    QProgressBar::chunk {
                        background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4cc9f0, stop:1 #0077cc);
                        border-radius: 0px;
                    }
                """)
        self.__QVBoxLayout.addWidget(self.QProgressBar_Loading)



        self.QTimer_Player = QtCore.QTimer(self)
        self.QTimer_Player.setSingleShot(True)  # 设置为单次触发
        self.QTimer_Player.timeout.connect(self.Execute_Play)  # 连接到要执行的函数
        self.QTimer_Player.start(5000)



        # self.Eexecute_Play()

        # QtCore.QTimer.singleShot(5000, lambda: self.Execute_Play())

    def Execute_Play(self):

        Video_Info = Page_Info["Page_Channel_List"][f'Channel_{self.Channel_Info["Channel_ID"]}']
        self.__Component_VideoPlay = Component_VideoPlay.Component_VideoPlay(Video_Info)

        Page_Info["Page_Element_List"][f"QLabel_Status_Source_{self.Channel_Info['Channel_ID']}"].setText(
                f'''<div style="text-align:center;">
                <span style="font-size:8pt;color:#1E90FF;font-weight: bold">视频源：{Video_Info["Video_Source"]}</span>
                </div>'''
        )

        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # __Component_VideoPlay = Component_VideoPlay.Component_VideoPlay(Video_Info)
        # __Component_VideoPlay.show()
        self.__QVBoxLayout.addWidget( self.__Component_VideoPlay)

    def Video_Stop(self):
        try: self.QTimer_Player.stop()
        except:pass
        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.__QVBoxLayout.addWidget(self.Status_Default())


    #
    #
    # def TODO(self):
    #     # Page_Info["Page_Channel_List"]["Channel_1"] = {"Video_Source": r"D:\Data\1.mp4"}
    #
    #     Video_Info = Page_Info["Page_Channel_List"][f'Channel_{self.Channel_Info["Channel_ID"]}']
    #


        # self.__QVBoxLayout.
        # QLabel_Video_Play


    # 鼠标事件
    def enterEvent(self, event):

        super().enterEvent(event)

    def leaveEvent(self, event):

        super().leaveEvent(event)



class QLabel_Function(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:self.QLabel_Info = args[0]
        except:pass

        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        # QLabel_Icon.setGeometry(0,0,18, 18)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        # Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # QLabel_Icon.setPixmap(Image_Logo)

        Icon_List = {
            "视频播放": "video_playback.png",
            "人脸预警": "face.png",
            "人脸布控": "face_control.png",
            "人体雷达监控结果": "radar_control.png",
            "人体雷达": "radar.png",
            "开门震动预警": "door.png"
        }

        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.QLabel_Info.get("Title_Name", "")
        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)
        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")


        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        QLabel_Title_Name.setText(self.QLabel_Info["Title_Name"])

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")
        # self.QVBoxLayout_Content = QtWidgets.QVBoxLayout(QLabel_Content)
        # self.QVBoxLayout_Content.setSpacing(1)
        # self.QVBoxLayout_Content.setContentsMargins(8, 8, 8, 8)
        # StyleSheet_QLabel = """
        #                         QLabel {
        #                             background-color: rgba(0, 50, 80, 120);
        #                             border: 1px solid rgba(0, 180, 255, 60);
        #                             border-radius: 4px;
        #                             padding: 8px;
        #                             color:cyan;
        #                             min-width: 80px;
        #                         }
        #                         QLabel:hover {
        #                             background-color: rgba(0, 100, 150, 150);
        #                         }
        #                     """
        # QLabel_Content_Evaluation_Class = QtWidgets.QLabel()
        #
        # QLabel_Content_Evaluation_Class.setFont(QtGui.QFont("Microsoft YaHei", 15))
        # QLabel_Content_Evaluation_Class.setStyleSheet(StyleSheet_QLabel)
        # QLabel_Content_Evaluation_Class.setAlignment(QtCore.Qt.AlignHCenter | QtCore.Qt.AlignBottom)
        # QLabel_Content_Evaluation_Class.setText("A+")
        #
        # QLabel_Content_Evaluation_Text = QtWidgets.QLabel()
        # QLabel_Content_Evaluation_Text.setMinimumHeight(30)
        # QLabel_Content_Evaluation_Text.setMaximumHeight(30)
        # QLabel_Content_Evaluation_Class.setFont(QtGui.QFont("Microsoft YaHei", 11))
        # QLabel_Content_Evaluation_Text.setAlignment(QtCore.Qt.AlignHCenter | QtCore.Qt.AlignTop)
        # QLabel_Content_Evaluation_Text.setText("素材评级")
        #
        # QLabel_Content_Evaluation_Score = QtWidgets.QLabel()
        # QLabel_Content_Evaluation_Score.setMinimumHeight(18)
        # QLabel_Content_Evaluation_Score.setMaximumHeight(18)
        # QLabel_Content_Evaluation_Score.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Content_Evaluation_Score.setAlignment(QtCore.Qt.AlignLeft)
        # QLabel_Content_Evaluation_Score.setStyleSheet("color:#1E90FF;")
        # QLabel_Content_Evaluation_Score.setText("*得分：92/100")
        #
        # self.QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Class)
        # self.QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Text, )
        # self.QVBoxLayout_Content.addWidget(QLabel_Content_Evaluation_Score)

        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)










if __name__ == "__main__":
    # app = QApplication(sys.argv)
    # window = DynamicLabelWindow()
    # window.show()
    # sys.exit(app.exec())

    App = QApplication(sys.argv)
    __Windows_Media_View = Windows_Media_View()
    __Windows_Media_View.resize(1920,1080)
    __Windows_Media_View.show()
    sys.exit(App.exec())

