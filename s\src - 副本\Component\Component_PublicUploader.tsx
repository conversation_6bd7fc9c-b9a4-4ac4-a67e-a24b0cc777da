import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Modal, Table, TableProps, Tag, Button } from 'antd';
import { useAuth } from '@/Core/Core_AuthContent';
import {SwapOutlined,AudioOutlined,VideoCameraOutlined,FileTextOutlined,FileOutlined,CheckOutlined,DeleteOutlined,DownloadOutlined,PlayCircleOutlined} from '@ant-design/icons';
import '@/Styles/Component_PublicUploader.css'

interface PublicUploaderProps {
    accept?: string;
    maxCount?: number;
    maxSize?: number;
    onUpload: (fileList: File[], tableData: DataType[]) => void;
    visible: boolean;
    setVisible: (visible: boolean) => void;
}

interface DataType {
    id: string;
    file_status: string;
    file_type: string,
    file_size: string,
    file_name: string,
    save_name: string,
    file_time: string,
    hash_info: string,
}

interface PublicFileItem {
    file: File;  // 文件对象
    preview?: string; // 文件预览
    id: string; // 文件ID
    status: '等待上传' | '上传中' | '服务器处理中' | '已上传' | '上传失败'; // 文件状态
    progress?: number; // 网络传输进度
    uploadProgress?: number; // 总体进度（包含服务器处理）
    // 新增服务器响应返回字段
    File_UUID?: string; // 服务器返回的文件UUID
    File_Msg?: string;  // 服务器返回的状态消息(出错原因)
    File_Date?: string; // 服务器返回的文件日期
    File_Type?: string; // 服务器返回的文件类型
    File_Response?: DataType; // 返回本地信息
}

const PublicUploader: React.FC<PublicUploaderProps> = ({
  accept = '',
  maxCount = 1,
  maxSize = 10,
  onUpload,
  visible,
  setVisible,
}) => {
    const [fileList, setFileList] = useState<any[]>([]); // 上传文件列表
    const [error, setError] = useState<string | null>(null); // 上传报错
    const [uploading, setUploading] = useState<boolean>(false); // 上传提交
    // const [processing, setProcessing] = useState<boolean>(false); //上传提交过程
    const uploadTasks = useRef<Record<string, { xhr: XMLHttpRequest; cleanup?: () => void }>>({}); // 存储每个文件上传任务的 xhr 实例
    // 文件类型对应
    const MIME_TYPE_MAP: Record<string, string> = {
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.txt': 'text/plain',
        '.csv': 'text/csv',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.pdf': 'application/pdf',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.wav': 'audio/wav',
        '.mp3':'audio/mp3',
        '.mpeg':'video/mpeg',
        '.mp4': 'video/mp4',
        '.zip': 'application/zip',
        '.rar': 'application/x-rar-compressed',
    };

    const UploadURL = process.env.REACT_APP_UPLOAD_URL; // 获取上传的URL
    if (!UploadURL) {
        throw new Error('Upload URL is not defined in environment variables');
    };

    const { user } = useAuth();
    const UPLOAD_CONFIG = {
        url: UploadURL, // 上传的URL
        user_id: user?.usertoken || '', // 用户权限
        user_token: user?.usertoken  || '', // 用户权限
        forward: 'False' // 是否流式转发 True/False
    };

    const [tabledata, setTableData] = useState<DataType[]>([]);
    const Tablecolumns: TableProps<DataType>['columns'] = [
        {
          title: '文件标识',
          dataIndex: 'hash_info',
          key: 'hash_info',
          width: 200,
          render: (hash_info: string) => (
            <span 
                style={{ 
                    display: 'inline-block', 
                    maxWidth: '200px', 
                    overflow: 'hidden', 
                    whiteSpace: 'nowrap' 
                }} 
                title={hash_info} // 鼠标悬停显示完整内容
            >
                {hash_info}
            </span>
        ),
        },
        {
          title: '文件名称',
          dataIndex: 'file_name',
          key: 'file_name',
          width: 150,
          render: (file_name: string) => (
            <span 
                style={{ 
                    display: 'inline-block', 
                    maxWidth: '150px', 
                    overflow: 'hidden', 
                    whiteSpace: 'nowrap' 
                }} 
                title={file_name} // 鼠标悬停显示完整内容
            >
                {file_name}
            </span>
        ),
        },
        {
          title: '文件大小',
          dataIndex: 'file_size',
          key: 'file_size',
        },
        {
          title: '文件状态',
          key: 'file_status',
          dataIndex: 'file_status',
          render: (status) => {
            let color = '';
            let text = '';
            switch (status) {
              case 'Active':
                color = 'blue';
                text = '排队中'
                break;
              case 'Execute':
                color = 'orange';
                text = '正在执行'
                break;
              case 'Finish':
                color = 'green';
                text = '执行完成'
                break;
              case 'Error':
                color = 'red';
                text = '执行出错'
                break;
              case 'Cancel':
                color = 'default';
                text = '任务取消'
                break;
              case '已上传':
                color = 'purple';
                text = '已上传'
                break;
              default:
                color = 'default';
                text = '状态未知'
            }
            return <Tag color={color} key={text}>{text}</Tag>;
          },
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          render: (_, record) => {
            return (
              <div>
                <DeleteOutlined 
                    className="publicupload-action-icon publicupload-delete-icon" 
                    onClick={() => Upload_Remove_Info(record.hash_info)}
                    title="删除"
                />
              </div>
            );
          },
        },
    ]; 
    // 通过文件名 重新计算文件格式
    const getFileMimeType = (file: File): string => {
        const fileName = file.name.toLowerCase();
        if (fileName.endsWith('.doc')) return 'application/msword';
        if (fileName.endsWith('.docx')) return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        if (fileName.endsWith('.txt')) return 'text/plain';
        if (fileName.endsWith('.csv')) return 'text/csv';
        if (fileName.endsWith('.xlsx')) return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        if (fileName.endsWith('.xls')) return 'application/vnd.ms-excel';
        if (fileName.endsWith('.ppt')) return 'application/vnd.ms-powerpoint';
        if (fileName.endsWith('.pptx')) return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
        if (fileName.endsWith('.pdf')) return 'application/pdf';
        if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) return 'image/jpeg';
        if (fileName.endsWith('.png')) return 'image/png';
        if (fileName.endsWith('.gif')) return 'image/gif';
        if (fileName.endsWith('.wav')) return 'audio/wav';
        if (fileName.endsWith('.mp3')) return 'audio/mp3';
        if (fileName.endsWith('.mpeg')) return 'audio/mpeg';
        if (fileName.endsWith('.mp4')) return 'video/mp4';
        if (fileName.endsWith('.zip')) return 'application/zip';
        if (fileName.endsWith('.rar')) return 'application/x-rar-compressed';
        return file.type || 'application/octet-stream'; // 默认取浏览器识别的 type
    };
    
    // 计算文件大小
    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 B';
      
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
      
        const value = bytes / Math.pow(k, i);
        return new Intl.NumberFormat('zh-CN', { maximumFractionDigits: 2 }).format(value) + ' ' + sizes[i];
    };

    // 文件推到到区域后触发 ok
    const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        const droppedFiles = e.dataTransfer.files;
        const fakeEvent = { target: { files: droppedFiles } } as React.ChangeEvent<HTMLInputElement>;
        handleFileChange(fakeEvent);
    }, []);

    // 允许拖放操作(必须存在 否则浏览器会默认阻止拖放) ok
    const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    }, []);

    // 拖拽框点击触发文件选择窗口 ok
    const handleDropZoneClick = () => {
        const fileInput = document.getElementById('publicupload-file-input') as HTMLInputElement;
        fileInput?.click();
    };

    // 解析服务器响应数据 刷新成本界面所需的变量
    const parseServerResponse = (responseText: string) => {
        try {
            console.log('原始响应:', responseText);
            
            const response = JSON.parse(responseText);
            console.log('解析后的response:', response);
            
            if (response.Status === "Success" && response.File_Response) {
                const fileResponse = response.File_Response;
                return {
                    success: response.Status === "Success",
                    File_UUID: fileResponse.hash_info,
                    File_Msg: response.Msg,
                    File_Date: fileResponse.file_time,
                    File_Type: fileResponse.file_type,
                    File_Response: response.File_Response,
                };
            }
            
            // 如果外层Status不是Success，返回错误信息
            return { 
                success: false, 
                File_Msg: response.Msg || '服务器响应格式错误',
                File_UUID: undefined,
                File_Date: undefined,
                File_Type: undefined,
                File_Response: undefined
            };
        } catch (error) {
            console.error('解析服务器响应失败:', error);
            console.error('响应内容:', responseText);
            return { 
                success: false, 
                File_Msg: '解析响应失败',
                File_UUID: undefined,
                File_Date: undefined,
                File_Type: undefined,
                File_Response: undefined,
            };
        }
    };

    // 模拟服务器处理进度 ok
    const simulateServerProgress = (fileId: string, startProgress: number = 70) => {
        let currentProgress = startProgress;
        const targetProgress = 95; // 最高到95%，留5%给真正完成
        const interval = 200; // 每200ms更新一次
        const incrementBase = 0.5; // 基础增长速度
        
        const timer = setInterval(() => {
        if (currentProgress >= targetProgress) {
            clearInterval(timer);
            return;
        }
        
        // 随着进度增加，速度递减（模拟服务器处理的不确定性）
        const progressRatio = (currentProgress - startProgress) / (targetProgress - startProgress);
        const decayFactor = Math.pow(0.7, progressRatio * 3); // 指数衰减
        const increment = incrementBase * decayFactor * (0.5 + Math.random() * 0.5); // 添加随机性
        
        currentProgress = Math.min(currentProgress + increment, targetProgress);
        
        updateFileStatus(fileId, {
            uploadProgress: Math.round(currentProgress)
        });
        }, interval);
        
        // 返回清理函数
        return () => clearInterval(timer);
    };

    // 单个文件上传函数（真实进度 + 模拟服务器进度）
    const uploadSingleFile = async (fileItem: PublicFileItem): Promise<{ success: boolean; serverData?: any }> => {
        return new Promise((resolve) => {
            const formData = new FormData();
            let serverProgressCleanup: (() => void) | null = null;
            
            // 添加参数
            // const mimeType = getFileMimeType(fileItem.file);
            formData.append('file', fileItem.file, fileItem.file.name);
            formData.append('user_id', UPLOAD_CONFIG.user_id);
            formData.append('file_time', new Date().toISOString().slice(0, 19));
            formData.append('user_token', UPLOAD_CONFIG.user_token);
            formData.append('forward', UPLOAD_CONFIG.forward);

            const xhr = new XMLHttpRequest();

            // 保存 xhr 到 uploadTasks
            uploadTasks.current[fileItem.id] = { xhr };

            // 监听上传进度（网络传输进度）
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                const networkProgress = Math.round((event.loaded / event.total) * 100);
                // 网络传输占总进度的70%
                const totalProgress = Math.round(networkProgress * 0.7);
                // 修改上传进度
                updateFileStatus(fileItem.id, {
                    progress: networkProgress,
                    uploadProgress: totalProgress,
                    status: '上传中'
                });
                }
            });

            // 监听上传完成（网络传输完成，开始服务器处理）
            xhr.upload.addEventListener('load', () => {
                // 网络传输完成，开始模拟服务器处理进度
                updateFileStatus(fileItem.id, {
                    progress: 100,
                    uploadProgress: 70,
                    status: '服务器处理中'
                });
                
                // 开始模拟服务器处理进度
                serverProgressCleanup = simulateServerProgress(fileItem.id, 70);

                // 保存 cleanup 函数
                if (serverProgressCleanup) {
                    uploadTasks.current[fileItem.id].cleanup = serverProgressCleanup;
                }
            });

            // 监听请求完成（服务器处理完成）
            xhr.addEventListener('load', async () => {
                // 清理服务器进度模拟
                if (serverProgressCleanup) {
                    serverProgressCleanup();
                }
                
                if (xhr.status >= 200 && xhr.status < 300) {
                    console.log(`文件上传成功: ${fileItem.file.name}`);
                    console.log('响应状态:', xhr.status);
                    console.log('响应内容:', xhr.responseText);
                    
                    // 解析服务器响应
                    const serverResponse = parseServerResponse(xhr.responseText);
                    console.log('服务器响应解析结果:', serverResponse);
                    
                    if (serverResponse.success) {
                        // 服务器处理完成，更新文件信息
                        const updateData = {
                            progress: 100,
                            uploadProgress: 100,
                            status: '已上传' as const,
                            File_UUID: serverResponse.File_UUID,
                            File_Msg: serverResponse.File_Msg,
                            File_Date: serverResponse.File_Date,
                            File_Type: serverResponse.File_Type,
                            File_Response: serverResponse.File_Response,
                        };
                        
                        console.log('准备更新文件状态:', updateData);
                        
                        updateFileStatus(fileItem.id, updateData);
                        resolve({ success: true, serverData: serverResponse });
                    } else {
                        console.error(`服务器处理失败: ${fileItem.file.name}`, serverResponse.File_Msg);
                        updateFileStatus(fileItem.id, {
                            progress: 0,
                            uploadProgress: 0,
                            status: '上传失败',
                            File_Msg: serverResponse.File_Msg || '服务器处理失败'
                        });
                        resolve({ success: false });
                    }
                } else {
                console.error(`文件上传失败: ${fileItem.file.name}`, xhr.status, xhr.responseText);
                updateFileStatus(fileItem.id, {
                    progress: 0,
                    uploadProgress: 0,
                    status: '上传失败',
                    File_Msg: `HTTP错误: ${xhr.status}`
                });
                resolve({ success: false });
                }
            });

            // 监听错误
            xhr.addEventListener('error', () => {
                console.error(`文件上传出错: ${fileItem.file.name}`);
                if (serverProgressCleanup) {
                serverProgressCleanup();
                }
                updateFileStatus(fileItem.id, {
                    progress: 0,
                    uploadProgress: 0,
                    status: '上传失败',
                    File_Msg: '网络错误'
                });
                resolve({ success: false });
            });

            // 监听超时
            xhr.addEventListener('timeout', () => {
                console.error(`文件上传超时: ${fileItem.file.name}`);
                if (serverProgressCleanup) {
                    serverProgressCleanup();
                }
                updateFileStatus(fileItem.id, {
                    progress: 0,
                    uploadProgress: 0,
                    status: '上传失败',
                    File_Msg: '上传超时'
                });
                resolve({ success: false });
            });

            // 设置超时时间（10分钟）
            xhr.timeout = 10 * 60 * 1000;

            // 发送请求
            xhr.open('POST', UPLOAD_CONFIG.url);
            xhr.send(formData);

            // 将 xhr 放入 uploadTasks，供删除时 abort
            uploadTasks.current[fileItem.id] = {
                xhr,
                cleanup: serverProgressCleanup || undefined
            };
        });
    };

    // 动态生成文件ID ok
    const generateFileId = () => {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    };

    // 更新单个文件进度和状态 ok
    const updateFileStatus = (fileId: string, updates: Partial<PublicFileItem>) => {
        setFileList(prev => prev.map(file => 
            file.id === fileId 
                ? { ...file, ...updates }
                : file
        ));
    };
    // 修改表格数据某一项的状态 ok
    const updateTableDataStatus = (fileId: string, updates: Partial<DataType>) => {
        setTableData(prev => prev.map(file => 
          file.id === fileId 
            ? { ...file, ...updates }
            : file
        ));
    };

    // 获取 accept 支持的文件类型列表 ok
    const getAcceptFileTypes = (accept: string): string[] => {
        if (!accept) return [];
        return accept
          .split(',')
          .map(type => type.trim().toLowerCase())
          .filter(Boolean);
    };

    // 判断文件是否在 accept 类型中 ok
    const isFileTypeAccepted = (file: File, acceptTypes: string[]): boolean => {
        console.log('acceptTypes:',acceptTypes)
        // 如果没有设置 accept 类型，直接返回 true（允许所有文件）
        if (acceptTypes.length === 0) {
            return true;
        }
        const fileName = file.name.toLowerCase();
        const fileExt = fileName.slice(fileName.lastIndexOf('.'));
        return acceptTypes.includes(fileExt);
    };

    // 上传文件变动触发 ok
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = e.target.files;
    
        if (!selectedFiles) return;
    
        const newFiles: PublicFileItem[] = [];
        const allowedTypes = getAcceptFileTypes(accept);
    
        Array.from(selectedFiles).forEach(file => {
          // 验证文件类型
          if (!isFileTypeAccepted(file, allowedTypes)) {
            console.log(`不支持的文件类型: ${file.name}。支持的格式: ${allowedTypes.join(', ')}`)
            setError(`不支持的文件类型: ${file.name}。支持的格式: ${allowedTypes.join(', ')}`);
            return;
          }

          // 验证文件大小
          if (file.size > maxSize * 1024 * 1024) {
            console.log(`文件太大，最大支持 ${maxSize}MB: ${formatFileSize(file.size)}`)
            setError(`${file.name}文件太大，最大支持 ${maxSize}MB: ${formatFileSize(file.size)}`);
            return;
          };

          // 验证是否满足最大的文件数量
          if (newFiles.length >= maxCount) {
            console.log(`最多只能上传 ${maxCount} 个文件`)
            setError(`最多只能上传 ${maxCount} 个文件`);
            return;
          };
          // 构造File文件项
          newFiles.push({
            file,
            preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
            id: generateFileId(),
            status: '等待上传',
            progress: 0,
            uploadProgress: 0
          });
        });
        // 添加文件到FileList
        setFileList(prev => [...prev, ...newFiles]);
        // 设置错误为空
        // setError(null);
        // 只有在所有文件都通过验证后才清空 error
        if (newFiles.length !== 0) {
            setError(null);
        }
    };

    // 单个文件上传提交 ok
    const handleSingleSubmit = async (index: number) => {
        const file = fileList[index];
        
        // 如果文件已经提交过，直接返回
        if (file.status === '已上传') {
          return;
        }
        // 更新状态为正在执行 其他按钮disable  文字显示内容控制
        setUploading(true);
        
        try {
          // 重置进度并开始上传
          updateFileStatus(file.id, {
            progress: 0,
            uploadProgress: 0,
            status: '上传中'
          });
          
          const uploadResult = await uploadSingleFile(file);
          
          // 上传完成后只记录结果，不更新表格
          if (uploadResult.success) {
            // 等待一下确保状态更新完成，然后获取最新的文件状态
            // setTimeout(() => {
            await new Promise(resolve => setTimeout(resolve, 100));
            const currentFile = fileList.find(f => f.id === file.id);
            if (currentFile) {
                const fileForProcessTable = {
                    id: uploadResult.serverData?.id || currentFile.id, 
                    file_status: uploadResult.serverData?.File_Response.file_status || currentFile.File_Response?.file_status,
                    file_type: uploadResult.serverData?.File_Response.file_type || currentFile.File_Response?.file_type,
                    file_size: uploadResult.serverData?.File_Response.file_size || currentFile.File_Response?.file_size,
                    file_name: uploadResult.serverData?.File_Response.file_name || currentFile.File_Response?.file_name,
                    save_name: uploadResult.serverData?.File_Response.save_name || currentFile.File_Response?.save_name,
                    file_time: uploadResult.serverData?.File_Response.file_time || currentFile.File_Response?.file_time,
                    hash_info: uploadResult.serverData?.File_Response.hash_info || currentFile.File_Response?.hash_info,
                };
                console.log('添加到处理表格的文件:', fileForProcessTable);
                // 检查文件是否已经在处理表格中
                const existsInProcessTable = tabledata.some(pf => pf.id === file.id);
                if (!existsInProcessTable) {
                    setTableData(prev => [...prev, fileForProcessTable]);
                } else {
                    updateTableDataStatus(file.id, fileForProcessTable);
                }
            }
            // }, 100);
          }
        } catch (error) {
          console.error('上传出错:', error);
          setError('上传失败，请重试');
          // 重置进度并设置状态为上传失败
          updateFileStatus(file.id, {
            progress: 0,
            uploadProgress: 0,
            status: '上传失败'
          });
        } finally {
          setUploading(false);
        }
    };

    // 批量提交上传功能
    const handleBatchSubmit = async () => {
        // 过滤出未提交的文件
        const unsubmittedFiles = fileList.filter(file => file.status !== '已上传');
        
        if (unsubmittedFiles.length === 0) {
            setError('所有文件都已上传');
            return;
        }
        // 更新状态为正在执行 其他按钮disable  文字显示内容控制
        setUploading(true);
        
        try {
            // 重置未提交文件的进度
            unsubmittedFiles.forEach(file => {
                updateFileStatus(file.id, {
                    progress: 0,
                    uploadProgress: 0,
                    status: '上传中'
                });
            });

            // 串行上传文件（避免服务器压力过大），每上传完一个就更新处理表格
            for (const file of unsubmittedFiles) {
                try {
                    const uploadResult = await uploadSingleFile(file);
                    if (uploadResult.success) {
                        // 等待状态更新完成，然后获取最新的文件状态
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        const currentFile = fileList.find(f => f.id === file.id);
                        if (currentFile) {
                            const fileForProcessTable = {
                                id: uploadResult.serverData?.id || currentFile.id, 
                                file_status: uploadResult.serverData?.File_Response.file_status || currentFile.File_Response?.file_status,
                                file_type: uploadResult.serverData?.File_Response.file_type || currentFile.File_Response?.file_type,
                                file_size: uploadResult.serverData?.File_Response.file_size || currentFile.File_Response?.file_size,
                                file_name: uploadResult.serverData?.File_Response.file_name || currentFile.File_Response?.file_name,
                                save_name: uploadResult.serverData?.File_Response.save_name || currentFile.File_Response?.save_name,
                                file_time: uploadResult.serverData?.File_Response.file_time || currentFile.File_Response?.file_time,
                                hash_info: uploadResult.serverData?.File_Response.hash_info || currentFile.File_Response?.hash_info,
                            };
                            
                            console.log('批量上传-添加到处理表格的文件:', fileForProcessTable);
                            
                            // 检查文件是否已经在处理表格中
                            const existsInProcessTable = tabledata.some(pf => pf.id === file.id);
                            if (!existsInProcessTable) {
                                // 逐个添加到处理表格中
                                setTableData(prev => [...prev, fileForProcessTable]);
                            } else {
                                // 如果表格中此数据已存在，则更新状态
                                updateTableDataStatus(file.id, fileForProcessTable);
                            }
                        }
                    }
                } catch (error) {
                    console.error(`上传文件 ${file.file.name} 时出错:`, error);
                }
            }
        } catch (error) {
            console.error('批量上传出错:', error);
            setError('批量上传失败，请重试');
        } finally {
            setUploading(false);
        }
    };

    // 上传文件列表删除(集成了普通删除[创建/完成]+取消上传)
    const handleRemove = (index: number) => {
        // console.log('handleRemove', index,fileList);
        const fileToRemove = fileList[index];
        // console.log('fileToRemove:',fileToRemove)
        // 如果是上传中或服务器处理中，尝试中断上传
        if ( fileToRemove.status === '上传中' || fileToRemove.status === '服务器处理中') {
            const task = uploadTasks.current[fileToRemove.id];
            // console.log('task：',task)
            if (task) {
                task.xhr.abort(); // 中断 XHR 请求
                if (task.cleanup) task.cleanup(); // 清理模拟进度条
            }

            // 更新状态为“上传取消”
            updateFileStatus(fileToRemove.id, {
                status: '上传失败',
                File_Msg: '用户取消上传',
                progress: 0,
                uploadProgress: 0
            });
            // 允许继续上传
            setUploading(false);
        }

        const updatedFiles = fileList.filter((_, i) => i !== index);
        // console.log('updatedFiles;',updatedFiles)
        setFileList(updatedFiles);
    };

    // 表格文件删除
    const Upload_Remove_Info = (hash_info: string) => {
        // 实现删除逻辑
        console.log('删除文件:', hash_info);
        setTableData(prev => prev.filter(item => item.hash_info !== hash_info));
    };
    // 弹窗界面重置
    const handleReset = () => {
        setTableData([]); // 表格数据
        setFileList([]);  // 文件数据
        setUploading(false); // 允许上传
        setError(null); 
    };

    // 组件卸载时 清除所有未完成上传
    useEffect(() => {
        return () => {
            Object.values(uploadTasks.current).forEach(task => {
                task.xhr.abort();
                if (task.cleanup) task.cleanup();
            });
        };
    }, []);
    

    const handleOk = () => {
        // onUpload(fileList, tabledata);
        // 无任何成功上传的表格数据 则直接关闭  不回调触发主文件的onUpload函数
        if (tabledata.length === 0) {
            setVisible(false); // 关闭弹窗
            return;
          }
        
          // 否则继续执行 onUpload 回调
          onUpload(fileList, tabledata);
    };

    return (
        <>
        <Modal
            title={
                <div className="publicupload-header-title-wrapper">
                    <h3>文件上传</h3>
                    <Button 
                        type="primary" 
                        size="small" 
                        style={{ marginLeft: 'auto', marginRight: 20 }}
                        onClick={handleBatchSubmit}
                        disabled={uploading}
                    >
                        {uploading ? '上传中...' : `批量提交 (${fileList.filter(file => file.status !== '已上传').length})`}
                    </Button>
                </div>
            }
            visible={visible}
            maskClosable={false}
            onOk={handleOk}
            onCancel={() => setVisible(false)}
            footer={() => (
                <div style={{ textAlign: 'right' }}>
                  <Button
                    key="clear"
                    onClick={() => {
                      handleReset();   // 清除缓存
                    }}
                    style={{ marginRight: 8 }}
                  >
                    条件重置
                  </Button>
                  <Button
                    key="submit"
                    type="primary"
                    onClick={handleOk}
                  >
                    {/* 上传完成 */}
                    {tabledata.length === 0 ? '界面关闭' : '上传完成'}
                  </Button>
                </div>
              )}
            width={750} // 设置 Modal 的最大宽度为 600px
            style={{ maxWidth: '750px' }} // 确保内容区域不超过 600px
            wrapClassName="custom-scrollbar" 
        >
            <div className='publicupload-file-wrap'>
                <div
                    className="publicupload-drop-zone"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onClick={handleDropZoneClick}
                    style={{ cursor: 'pointer' }}
                >
                    <p>拖放文件到此处 或 点击此区域选择文件</p>
                    {/* <p>支持格式: wav, mp3, mpeg, mp4</p> */}
                    {accept ? (
                        <p style={{ fontSize: 12 }}>
                            支持格式:
                            {accept.split(',').map((type, index) => type.trim()).filter(Boolean).map((type, index) => (
                                <span key={index} className="publicupload-format-tag">
                                    {type}
                                </span>
                            ))}
                        </p>
                    ) : (
                        <p style={{ fontSize: 12 }}>支持所有格式</p>
                    )}
                    <p style={{ fontSize: 12 }}>最多可上传 <span className="publicupload-format-tag">{maxCount}</span> 个文件, 单个文件最大支持 <span className="publicupload-format-tag">{maxSize}MB</span></p>
                    <input 
                        id="publicupload-file-input"
                        type="file" 
                        multiple 
                        onChange={handleFileChange} 
                        accept={accept}
                        style={{ display: 'none' }}
                    />
                </div>

                {error && <div className="publicupload-error-message">{error}</div>}

                <ul className="publicupload-file-list">
                    {fileList.map((item, index) => (
                    <li key={index} className="publicupload-file-item">
                        {item.preview ? (
                            <img src={item.preview} alt="预览" className="publicupload-file-preview" />
                        ) : item.file.type.startsWith('audio/') ? (
                            <AudioOutlined className="publicupload-file-icon" />
                        ) : item.file.type.startsWith('video/') ? (
                            <VideoCameraOutlined className="publicupload-file-icon" />
                        ) : item.file.type.startsWith('text/') || item.file.type === 'application/pdf' ? (
                            <FileTextOutlined className="publicupload-file-icon" />
                        ) : (
                            <FileOutlined className="publicupload-file-icon" />
                        )}
                        <div className="publicupload-file-info">
                            <span className="publicupload-file-name">{item.file.name}</span>
                            <span className="publicupload-file-size">{formatFileSize(item.file.size)}</span>
                            
                            {(item.status === '上传中' || item.status === '服务器处理中') && typeof item.uploadProgress === 'number' && (
                                <div className="publicupload-progress-container">
                                    <div className="publicupload-progress-bar">
                                        <div 
                                            className="publicupload-progress-fill with-shimmer"
                                            style={{ width: `${item.uploadProgress}%` }}
                                        ></div>
                                    </div>
                                </div>
                            )}
                            
                        </div>
                        
                        <div className="publicupload-file-actions">
                            {item.status === '已上传' ? (
                            <button 
                                className="publicupload-submitted-btn"
                                disabled
                                style={{ 
                                    backgroundColor: '#52c41a',
                                    color: 'white',
                                    border: 'none',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    cursor: 'not-allowed',
                                    opacity: 0.8
                                }}
                            >
                                已上传
                            </button>
                            ) : (
                            <CheckOutlined 
                                className="publicupload-submit-icon" 
                                onClick={() => handleSingleSubmit(index)}
                                style={{ 
                                    opacity: (uploading || item.status === '上传中' || item.status === '服务器处理中') ? 0.5 : 1, 
                                    cursor: (uploading || item.status === '上传中' || item.status === '服务器处理中') ? 'not-allowed' : 'pointer' 
                                }}
                            />
                            )}
                            <button 
                                className="publicupload-remove-btn" 
                                onClick={() => handleRemove(index)}
                                // disabled={item.status === '上传中' || item.status === '服务器处理中'}
                            >
                                <DeleteOutlined />
                            </button>
                        </div>
                    </li>
                    ))}
                </ul>
            </div>
            <Table<DataType> 
                columns={Tablecolumns} 
                dataSource={tabledata}
                bordered
                rowKey="id"
                className="publicupload-custom-table"
            />
        </Modal>
        </>
    );
};

export default PublicUploader;