import React, { useState } from 'react';
import { aesEncrypt, aesDecrypt } from '../Core/Core_Crypto';
import { Service_Requests } from '@/Core/Core_Control';
import { Input, <PERSON><PERSON>, Card } from 'antd';

const CryptoDemo: React.FC = () => {
  const [text, setText] = useState('');
  const [encrypted, setEncrypted] = useState('');
  const [decrypted, setDecrypted] = useState('');

  const handleEncrypt = () => {
    const result = aesEncrypt(text);
    setEncrypted(result);

    Requests(result)


  };

  const handleDecrypt = () => {
    const result = aesDecrypt(encrypted);
    setDecrypted(result);
  };



  const Requests = (Result :string) => {


  let __Service_Requests= new Service_Requests()
  let Requests_Data={
    'user_id': '',
    'user_token': '',
    'data_class': 'Gis',
    'data_type': 'Service_Client',
    'data_methods': 'return_gis_info',
    'data_argument': "{}",
    'data_kwargs': JSON.stringify({
      'Encry_Data':Result,

    }),
  };
  

  (async () => {
    __Service_Requests.Async(Requests_Data)
      .then((Response_Data) => {
        console.log("fetchData_Init 请求成功 Response Data:", Response_Data);
//         // 表格更新
//         setData(Response_Data.results);
//         // 关闭Loading动画
//         setLoading(false);
//         // 设置表格属性
//         setTableParams({
//           ...tableParams,
//           pagination: {
//             ...tableParams.pagination,
//             total: 200,
//             // 200 is mock data, you should read it from server
//             // total: data.totalCount,
//           },
//         });
      }).catch ((err) => {
        console.log('请求失败',err)
      })
  
  })();
};


















  return (
    // <div>
    //   <input 
    //     type="text" 
    //     value={text} 
    //     onChange={(e) => setText(e.target.value)} 
    //     placeholder="输入要加密的内容"
    //   />
    //   <button onClick={handleEncrypt}>加密</button>
    //   <button onClick={handleDecrypt}>解密</button>
      
    //   <div>加密结果: {encrypted}</div>
    //   <div>解密结果: {decrypted}</div>
    // </div>
    <Card title="加密解密演示" style={{ maxWidth: 600, margin: 'auto', marginTop: 50 }}>
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder="输入要加密的内容"
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
      </div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleEncrypt} style={{ marginRight: 8 }}>
          加密
        </Button>
        <Button type="default" onClick={handleDecrypt}>
          解密
        </Button>
      </div>
      <div>
        <strong>加密结果:</strong>
        <p className="break-all">{encrypted}</p>
      </div>
      <div>
        <strong>解密结果:</strong>
        <p>{decrypted}</p>
      </div>
    </Card>
  );
};

export default CryptoDemo;