function Query_Orders(User_History_Date = 'all') {
    loaderShow()
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'User_Query_Point_Log',
    //     'User_Token': User_Token,
    //     'User_History_Date': User_History_Date,
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'User', 
        'data_type': 'Service',
        'data_methods': 'personal_history_service',
        "data_argument": `{}`,
        "data_kwargs":{'User_History_Date': User_History_Date,},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
    .then(res => {
        $table.clear().draw();
        if (res.length > 0) {
            $table.rows.add(res).draw();
        }
    }).finally(() => {
        loaderHide()
    })
}

// 订单table
var $table = $('#table').DataTable({
    autoWidth: false,
    "columns": [
        { "title": "序号", width: 80, "data": null, render: function (data, type, row, meta) { return meta.row + 1; } },
        { "title": "日志时间", "data": "XID" },
        { "title": "日志类型", "data": "MARKET_TYPE" },
        {
            "title": "服务内容", "data": "MARKET_SERVER", render: function (data, type, row, meta) {

                let value = eval(data)

                return value.map(e => `【${e.NAME}】`).join('')
            }
        },
        {
            "title": "服务前", "data": "MARKET_DEAL_INFO", render: function (data, type, row, meta) {
                let value = eval(data)
                return value['last']
            }
        },
        {
            "title": "当次消费", "data": "MARKET_DEAL_INFO", render: function (data, type, row, meta) {
                let value = eval(data)
                return value['consume']
            }
        },
        {
            "title": "结余", "data": "MARKET_DEAL_INFO", render: function (data, type, row, meta) {
                let value = eval(data)
                return value['update']
            }
        },
    ],
    "oLanguage": { //国际化配置
        "sProcessing": "正在获取数据，请稍后...",
        "sLengthMenu": "显示 _MENU_ 条",
        "sZeroRecords": "没有您要搜索的内容",
        "sInfo": "从 _START_ 到  _END_ 条记录 总记录数为 _TOTAL_ 条",
        "sInfoEmpty": "记录数为0",
        "sInfoFiltered": "(全部记录数 _MAX_ 条)",
        "sInfoPostFix": "",
        "sSearch": "搜索",
        "sUrl": "",
        "oPaginate": {
            "sFirst": "第一页",
            "sPrevious": "上一页",
            "sNext": "下一页",
            "sLast": "最后一页"
        }
    },
});


$('#query_table').on('click', function () {
    let User_History_Date = $('#User_History_Date').val()
    Query_Orders(User_History_Date)
})
// 导出
$('#export_table').on('click', function () {
    //获取当前时间
    let date = new Date();
    // 转年月日
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    // 拼接一下
    let filename = year + '-' + month + '-' + day;
    exportTableToExcel('table', filename)
})
function exportTableToExcel(tableID, filename = '') {
    let downloadLink;
    let dataType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    let tableSelect = document.getElementById(tableID);
    let tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');

    let wb = XLSX.utils.table_to_book(tableSelect, { sheet: "Sheet JS" });
    let wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }

    let blob = new Blob([s2ab(wbout)], { type: dataType });
    downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = filename ? filename + '.xlsx' : 'excel_data.xlsx';

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}