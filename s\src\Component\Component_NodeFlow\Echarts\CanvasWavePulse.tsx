import React, { useEffect, useRef } from 'react';

interface CanvasWavePulseProps {
  color?: string;
  waveCount?: number;
  duration?: number;
  size?: number;
}

const CanvasWavePulse: React.FC<CanvasWavePulseProps> = ({
  color = '#4fd1c5',
  waveCount = 3,
  duration = 2000,
  size = 100,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef =useRef<number | null>(null); 
  const startTimeRef = useRef<number>(Date.now());

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = size / 2;
    const centerY = size / 2;
    const maxRadius = size / 2;
    const waveInterval = duration / waveCount;

    const animate = () => {
      const currentTime = Date.now() - startTimeRef.current;
      ctx.clearRect(0, 0, size, size);

      // Draw center dot
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(centerX, centerY, 6, 0, Math.PI * 2);
      ctx.fill();

      // Draw waves
      for (let i = 0; i < waveCount; i++) {
        const waveTime = (currentTime - i * waveInterval) % duration;
        if (waveTime < 0) continue;

        const progress = waveTime / duration;
        const radius = maxRadius * progress;
        const opacity = 1 - progress;

        ctx.strokeStyle = color;
        ctx.globalAlpha = opacity * 0.6;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();
      }

      ctx.globalAlpha = 1;
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [color, duration, size, waveCount]);

  return <canvas ref={canvasRef} width={size} height={size} />;
};

export default CanvasWavePulse;