import { NodeEditor, GetSchemes, ClassicPreset} from "rete";
import { Node_Socket,SystemControl_Algorithm_Load} from "./Node_System_Control_Algorithm_Load";

export class Node_System_Component_Algorithm_Load extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| SystemControl_Algorithm_Load}> 
  {
    width  = 450;
    height = 660
    constructor(Label: string,) {
      super(Label);

      this.addInput("optional_lora_stack", new ClassicPreset.Input(Node_Socket, "optional_lora_stack",),);
      this.addInput("optional_controlnet_stack", new ClassicPreset.Input(Node_Socket, "optional_controlnet_stack"),);
      this.addInput("empty_latent_width", new ClassicPreset.Input(Node_Socket, "empty_latent_width"),);

      this.addOutput("pipe", new ClassicPreset.Output(Node_Socket, "pipe"));
      this.addOutput("model", new ClassicPreset.Output(Node_Socket, "model"));
      this.addOutput("vae", new ClassicPreset.Output(Node_Socket, "vae"));
        
      const ConentControl = new SystemControl_Algorithm_Load(
        '【标题】:未知', // Label for the text area
        '0',
        '0',
        '1',

        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    