import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button } from 'antd';

interface Mission {
  XID: string;
  MISSION_TYPE: '降噪' | '萃取' | '文本转译' | '音视频转译' | '文档转译';
  MISSION_CONTENT: string;
  // MISSION_SOURCE: string;
  MISSION_RESULT: string;
  MISSION_STATUS: 'Active' | 'Execute' | 'Finish';
  Index: string;
  key?: string;
  MISSION_RESULT_FULL?:TableDataItem[];
};
interface TableDataItem {
  hash_info: string;
  file_name: string;
  file_size: string;
  file_status: string;
  file_time: string;
  file_type: string;
  save_name: string;
};

type TextDetailModalProps = {
  visible: boolean;
  onCancel: () => void;
  detailData: Mission | null; // 根据你的数据结构调整
};
const TextDetailModal: React.FC<TextDetailModalProps> = ({ visible, onCancel, detailData }) => {
  const [form] = Form.useForm();

  // 如果有传入 detailData，则填充内容；否则清空
  useEffect(() => {
    if (visible && detailData) {
      form.setFieldsValue({
        content: detailData.MISSION_CONTENT,
        result: detailData.MISSION_RESULT,
      });
    } else {
      form.resetFields();
    }
  }, [visible, detailData, form]);

  return (
    <Modal
      title="文本转译日志详情"
      visible={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
      ]}
      width={1000} // 设置为 modal-xl 的等效宽度
      centered // 居中显示
    >
      <Form form={form} layout="vertical">
        <Form.Item label="原文内容" name="content">
          <Input.TextArea rows={6} style={{ borderRadius: '2px' }} />
        </Form.Item>

        <Form.Item label="转译结果" name="result">
          <Input.TextArea rows={6} style={{ borderRadius: '2px' }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TextDetailModal;