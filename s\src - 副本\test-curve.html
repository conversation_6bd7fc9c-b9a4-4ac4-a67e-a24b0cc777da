<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>曲线图测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            background-color: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            background-color: #16213e;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="title">近30天舆情趋势对比</div>
    
    <div class="title">原来的折线图：</div>
    <div id="oldChart" class="chart-container"></div>
    
    <div class="title">新的平滑曲线图：</div>
    <div id="newChart" class="chart-container"></div>

    <script>
        // 原来的折线图
        const oldChart = echarts.init(document.getElementById('oldChart'));
        const oldOption = {
            title: {
                text: '折线图（原版）',
                left: 'center',
                textStyle: {
                    color: '#ffffff'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['1日', '3日', '5日', '7日', '10日', '12日', '15日', '18日', '20日', '22日', '25日', '27日', '30日'],
                axisLabel: {
                    color: '#ffffff'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#ffffff'
                }
            },
            series: [{
                name: '舆情数量',
                type: 'line',
                data: [120, 135, 132, 128, 101, 115, 134, 142, 90, 105, 230, 245, 210],
                itemStyle: {
                    color: '#03A9F4'
                }
            }]
        };
        oldChart.setOption(oldOption);

        // 新的平滑曲线图
        const newChart = echarts.init(document.getElementById('newChart'));
        const newOption = {
            title: {
                text: '平滑曲线图（新版）',
                left: 'center',
                textStyle: {
                    color: '#ffffff'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['1日', '3日', '5日', '7日', '10日', '12日', '15日', '18日', '20日', '22日', '25日', '27日', '30日'],
                axisLabel: {
                    color: '#ffffff',
                    fontSize: 12
                },
                axisLine: {
                    lineStyle: {
                        color: '#444'
                    }
                },
                axisTick: {
                    lineStyle: {
                        color: '#444'
                    }
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#ffffff',
                    fontSize: 12
                },
                axisLine: {
                    lineStyle: {
                        color: '#444'
                    }
                },
                axisTick: {
                    lineStyle: {
                        color: '#444'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#333',
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: '舆情数量',
                type: 'line',
                smooth: true, // 启用平滑曲线
                data: [120, 135, 132, 128, 101, 115, 134, 142, 90, 105, 230, 245, 210],
                itemStyle: {
                    color: '#03A9F4'
                },
                lineStyle: {
                    width: 3, // 线条宽度
                    color: '#03A9F4'
                },
                areaStyle: {
                    // 添加渐变填充区域
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(3, 169, 244, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(3, 169, 244, 0.05)'
                            }
                        ]
                    }
                },
                symbol: 'circle', // 数据点样式
                symbolSize: 6, // 数据点大小
                emphasis: {
                    focus: 'series',
                    itemStyle: {
                        color: '#ffffff',
                        borderColor: '#03A9F4',
                        borderWidth: 2
                    }
                }
            }]
        };
        newChart.setOption(newOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            oldChart.resize();
            newChart.resize();
        });
    </script>
</body>
</html>
