import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Select, 
  DatePicker, 
  Modal, 
  Space, 
  message,
  Tooltip,
  Tag,
  Typography
} from 'antd';
import { EyeOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import dayjs from 'dayjs';
import '../Styles/Page_Purchased_Service.css';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

// 数据类型定义
interface OrderInfo {
  id: string;
  Create_time: string;
  MARKET_UPDATE: string;
  MARKET_ADDR: string;
  MARKET_CONTENT: string;
  MARKET_STATUS: string;
  MARKET_DEAL_INFO: OrderDetail[];
}

interface OrderDetail {
  ORDER_SID: string;
  NAME: string;
  ORDER_URL: string;
  ORDER_NUMBER: number;
  ORDER_CREATE_TIME: string;
  ORDER_LAST_NUMBER: number;
  ORDER_NOW_NUMBER: number;
  ORDER_UPDATE: string;
  ORDER_STATUS: string;
  ORDER_TYPE?: string;
  ORDER_CONTENT?: any[];
  ACCOUNT_LIST?: string[];
}

const Page_Purchased_Service: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [orderData, setOrderData] = useState<OrderInfo[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('week');
  const [customDateRange, setCustomDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [showCustomDate, setShowCustomDate] = useState(false);
  
  // 模态框状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderInfo | null>(null);
  const [selectedOrderDetail, setSelectedOrderDetail] = useState<OrderDetail | null>(null);

  // 状态映射
  const statusMap: { [key: string]: string } = {
    'Finish': '已完成',
    'Active': '执行中',
    'Recharge': '已退款',
    'Failed': '异常中',
    'Execute': '执行中',
    'API': '执行中'
  };

  // 状态标签颜色
  const getStatusColor = (status: string): string => {
    switch (status) {
      case '已完成': return 'success';
      case '执行中': return 'processing';
      case '已退款': return 'warning';
      case '异常中': return 'error';
      default: return 'default';
    }
  };

  // 初始化数据
  useEffect(() => {
    loadOrderData();
  }, []);

  // 加载订单数据
  const loadOrderData = async (timeRange: string = 'week', customDates: string[] = []) => {
    setLoading(true);
    try {
      // 模拟数据
      const mockData: OrderInfo[] = [
        {
          id: '1',
          Create_time: '2024-01-15 10:30:00',
          MARKET_UPDATE: '2024-01-15 11:00:00',
          MARKET_ADDR: '账号采购',
          MARKET_CONTENT: 'Facebook账号购买 x5',
          MARKET_STATUS: '已完成',
          MARKET_DEAL_INFO: [
            {
              ORDER_SID: 'ORD001',
              NAME: 'Facebook账号',
              ORDER_URL: 'https://facebook.com/target',
              ORDER_NUMBER: 5,
              ORDER_CREATE_TIME: '2024-01-15 10:30:00',
              ORDER_LAST_NUMBER: 0,
              ORDER_NOW_NUMBER: 5,
              ORDER_UPDATE: '2024-01-15 11:00:00',
              ORDER_STATUS: 'Finish',
              ACCOUNT_LIST: ['account1:password1', 'account2:password2']
            }
          ]
        },
        {
          id: '2',
          Create_time: '2024-01-14 14:20:00',
          MARKET_UPDATE: '2024-01-14 15:30:00',
          MARKET_ADDR: '网络反制',
          MARKET_CONTENT: 'Twitter点赞服务 x100',
          MARKET_STATUS: '执行中',
          MARKET_DEAL_INFO: [
            {
              ORDER_SID: 'ORD002',
              NAME: 'Twitter点赞',
              ORDER_URL: 'https://twitter.com/post/123',
              ORDER_NUMBER: 100,
              ORDER_CREATE_TIME: '2024-01-14 14:20:00',
              ORDER_LAST_NUMBER: 0,
              ORDER_NOW_NUMBER: 65,
              ORDER_UPDATE: '2024-01-14 15:30:00',
              ORDER_STATUS: 'Active'
            }
          ]
        }
      ];
      setOrderData(mockData);
    } catch (error) {
      message.error('加载订单数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setSelectedTimeRange(value);
    if (value === 'other') {
      setShowCustomDate(true);
    } else {
      setShowCustomDate(false);
      setCustomDateRange(null);
      loadOrderData(value);
    }
  };

  // 处理自定义日期范围变化
  const handleCustomDateChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setCustomDateRange([dates[0], dates[1]]);
    } else {
      setCustomDateRange(null);
    }
  };

  // 查询按钮点击
  const handleQuery = () => {
    if (selectedTimeRange === 'other' && customDateRange) {
      const dateStrings = [
        customDateRange[0].format('YYYY-MM-DD'),
        customDateRange[1].format('YYYY-MM-DD')
      ];
      loadOrderData(selectedTimeRange, dateStrings);
    } else {
      loadOrderData(selectedTimeRange);
    }
  };

  // 查看详情
  const handleViewDetail = (record: OrderInfo) => {
    setSelectedOrder(record);
    setDetailModalVisible(true);
  };

  // 查看账号详情
  const handleViewAccount = (orderDetail: OrderDetail) => {
    setSelectedOrderDetail(orderDetail);
    setAccountModalVisible(true);
  };

  // 复制文本
  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success('复制成功');
    } catch (error) {
      message.error('复制失败');
    }
  };

  // 下载文本
  const handleDownload = (text: string) => {
    const element = document.createElement('a');
    const file = new Blob([text], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `account_${Date.now()}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    message.success('下载成功');
  };

  // 主表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '创建时间',
      dataIndex: 'Create_time',
      key: 'Create_time',
      width: 160,
    },
    {
      title: '状态更新时间',
      dataIndex: 'MARKET_UPDATE',
      key: 'MARKET_UPDATE',
      width: 160,
    },
    {
      title: '订单类型',
      dataIndex: 'MARKET_ADDR',
      key: 'MARKET_ADDR',
      width: 120,
    },
    {
      title: '订单内容',
      dataIndex: 'MARKET_CONTENT',
      key: 'MARKET_CONTENT',
      ellipsis: true,
    },
    {
      title: '订单状态',
      dataIndex: 'MARKET_STATUS',
      key: 'MARKET_STATUS',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: OrderInfo) => (
        <Button 
          type="primary" 
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div className="purchased-service-page">
      {/* 查询条件 */}
      <Card style={{ marginBottom: 16 }}>
        <h6 style={{ marginBottom: 16 }}>查询条件：</h6>
        <Space wrap>
          <Space>
            <span>订单创建时间：</span>
            <Select
              value={selectedTimeRange}
              onChange={handleTimeRangeChange}
              style={{ width: 120 }}
            >
              <Option value="day">今日</Option>
              <Option value="yesterday">昨日</Option>
              <Option value="week">一周</Option>
              <Option value="other">自定义</Option>
              <Option value="mouth">本月</Option>
              <Option value="all">所有</Option>
            </Select>
          </Space>
          
          {showCustomDate && (
            <RangePicker
              value={customDateRange}
              onChange={handleCustomDateChange}
              placeholder={['开始日期', '结束日期']}
            />
          )}
          
          <Button type="primary" onClick={handleQuery}>
            查询
          </Button>
        </Space>
      </Card>

      {/* 订单列表 */}
      <div className="table-responsive">
        <Table
          columns={columns}
          dataSource={orderData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1000 }}
        />
      </div>

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <h6>订单基本信息</h6>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text><strong>创建时间：</strong>{selectedOrder.Create_time}</Text>
                <Text><strong>更新时间：</strong>{selectedOrder.MARKET_UPDATE}</Text>
                <Text><strong>订单类型：</strong>{selectedOrder.MARKET_ADDR}</Text>
                <Text><strong>订单内容：</strong>{selectedOrder.MARKET_CONTENT}</Text>
                <Text><strong>订单状态：</strong>
                  <Tag color={getStatusColor(selectedOrder.MARKET_STATUS)} style={{ marginLeft: 8 }}>
                    {selectedOrder.MARKET_STATUS}
                  </Tag>
                </Text>
              </Space>
            </Card>

            <Card size="small">
              <h6>订单详细信息</h6>
              <Table
                size="small"
                columns={[
                  { title: '订单编号', dataIndex: 'ORDER_SID', key: 'ORDER_SID', width: 120 },
                  { title: '服务名称', dataIndex: 'NAME', key: 'NAME', width: 120 },
                  { title: '目标链接', dataIndex: 'ORDER_URL', key: 'ORDER_URL', ellipsis: true },
                  { title: '订购数量', dataIndex: 'ORDER_NUMBER', key: 'ORDER_NUMBER', width: 80 },
                  { title: '已完成', dataIndex: 'ORDER_NOW_NUMBER', key: 'ORDER_NOW_NUMBER', width: 80 },
                  {
                    title: '状态',
                    dataIndex: 'ORDER_STATUS',
                    key: 'ORDER_STATUS',
                    width: 100,
                    render: (status: string) => (
                      <Tag color={getStatusColor(statusMap[status] || status)}>
                        {statusMap[status] || status}
                      </Tag>
                    )
                  },
                  {
                    title: '操作',
                    key: 'action',
                    width: 100,
                    render: (_: any, record: OrderDetail) => (
                      record.ACCOUNT_LIST && record.ACCOUNT_LIST.length > 0 ? (
                        <Button
                          type="link"
                          size="small"
                          onClick={() => handleViewAccount(record)}
                        >
                          查看账号
                        </Button>
                      ) : null
                    ),
                  },
                ]}
                dataSource={selectedOrder.MARKET_DEAL_INFO}
                rowKey="ORDER_SID"
                pagination={false}
              />
            </Card>
          </div>
        )}
      </Modal>

      {/* 账号详情模态框 */}
      <Modal
        title="账号详情"
        open={accountModalVisible}
        onCancel={() => setAccountModalVisible(false)}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={() => {
            if (selectedOrderDetail?.ACCOUNT_LIST) {
              handleCopy(selectedOrderDetail.ACCOUNT_LIST.join('\n'));
            }
          }}>
            复制全部
          </Button>,
          <Button key="download" icon={<DownloadOutlined />} onClick={() => {
            if (selectedOrderDetail?.ACCOUNT_LIST) {
              handleDownload(selectedOrderDetail.ACCOUNT_LIST.join('\n'));
            }
          }}>
            下载
          </Button>,
          <Button key="close" onClick={() => setAccountModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedOrderDetail && selectedOrderDetail.ACCOUNT_LIST && (
          <div>
            <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
              <Text><strong>订单编号：</strong>{selectedOrderDetail.ORDER_SID}</Text>
              <Text><strong>服务名称：</strong>{selectedOrderDetail.NAME}</Text>
              <Text><strong>账号数量：</strong>{selectedOrderDetail.ACCOUNT_LIST.length}</Text>
            </Space>

            <div style={{
              maxHeight: 300,
              overflow: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: 6,
              padding: 12,
              backgroundColor: '#fafafa'
            }}>
              {selectedOrderDetail.ACCOUNT_LIST.map((account, index) => (
                <div key={index} style={{
                  marginBottom: 8,
                  padding: 8,
                  backgroundColor: '#fff',
                  borderRadius: 4,
                  border: '1px solid #f0f0f0',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Text code style={{ flex: 1 }}>{account}</Text>
                  <Tooltip title="复制">
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopy(account)}
                    />
                  </Tooltip>
                </div>
              ))}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Page_Purchased_Service;
