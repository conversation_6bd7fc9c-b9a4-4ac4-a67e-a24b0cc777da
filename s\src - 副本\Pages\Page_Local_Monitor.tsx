import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Input, 
  Button, 
  List, 
  Table, 
  Space, 
  Typography, 
  Spin,
  message,
  Row,
  Col
} from 'antd';
import { 
  SearchOutlined, 
  MessageOutlined, 
  MenuOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Local_Monitor.css';

const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Search } = Input;

// 数据类型定义
interface GroupInfo {
  id: string;
  name: string;
  type: string;
  memberCount?: number;
  lastMessage?: string;
  lastTime?: string;
}

interface MessageInfo {
  ID: string;
  SOURCE_WEB_DATE: string;
  SOURCE_AUTHOR: string;
  SOURCE_TITLE: string;
  GROUP_ID?: string;
  GROUP_NAME?: string;
}

const Page_Local_Monitor: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('wechat');
  const [loading, setLoading] = useState<boolean>(false);
  const [groupList, setGroupList] = useState<GroupInfo[]>([]);
  const [messageList, setMessageList] = useState<MessageInfo[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<GroupInfo | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);

  // 初始化数据
  useEffect(() => {
    loadGroupList();
  }, [activeTab]);

  // 加载群组列表
  const loadGroupList = async () => {
    setLoading(true);
    try {
      // 模拟数据
      const mockGroups: GroupInfo[] = [
        {
          id: '1',
          name: '技术交流群',
          type: activeTab,
          memberCount: 128,
          lastMessage: '大家好，今天讨论一下新技术',
          lastTime: '2024-01-15 14:30:25'
        },
        {
          id: '2',
          name: '项目讨论组',
          type: activeTab,
          memberCount: 56,
          lastMessage: '项目进度如何？',
          lastTime: '2024-01-15 13:45:12'
        },
        {
          id: '3',
          name: '产品反馈群',
          type: activeTab,
          memberCount: 89,
          lastMessage: '用户反馈很不错',
          lastTime: '2024-01-15 12:20:08'
        },
        {
          id: '4',
          name: '运营推广组',
          type: activeTab,
          memberCount: 234,
          lastMessage: '本周活动效果分析',
          lastTime: '2024-01-15 11:15:33'
        },
        {
          id: '5',
          name: '客服支持群',
          type: activeTab,
          memberCount: 67,
          lastMessage: '客户问题已解决',
          lastTime: '2024-01-15 10:30:45'
        }
      ];
      
      setGroupList(mockGroups);
      
      // 默认选中第一个群组
      if (mockGroups.length > 0) {
        setSelectedGroup(mockGroups[0]);
        loadMessages(mockGroups[0].id);
      }
    } catch (error) {
      message.error('加载群组列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载消息列表
  const loadMessages = async (groupId: string) => {
    setLoading(true);
    try {
      // 模拟数据
      const mockMessages: MessageInfo[] = [
        {
          ID: '1',
          SOURCE_WEB_DATE: '2024-01-15 14:30:25',
          SOURCE_AUTHOR: '张三',
          SOURCE_TITLE: '大家好，今天我们来讨论一下新技术的应用场景，希望大家积极参与讨论。',
          GROUP_ID: groupId
        },
        {
          ID: '2',
          SOURCE_WEB_DATE: '2024-01-15 14:28:15',
          SOURCE_AUTHOR: '李四',
          SOURCE_TITLE: '我觉得这个技术很有前景，可以在我们的项目中尝试使用。',
          GROUP_ID: groupId
        },
        {
          ID: '3',
          SOURCE_WEB_DATE: '2024-01-15 14:25:08',
          SOURCE_AUTHOR: '王五',
          SOURCE_TITLE: '同意楼上的观点，不过需要考虑实施的成本和风险。',
          GROUP_ID: groupId
        },
        {
          ID: '4',
          SOURCE_WEB_DATE: '2024-01-15 14:20:33',
          SOURCE_AUTHOR: '赵六',
          SOURCE_TITLE: '我们可以先做一个小规模的试点，看看效果如何。',
          GROUP_ID: groupId
        },
        {
          ID: '5',
          SOURCE_WEB_DATE: '2024-01-15 14:15:45',
          SOURCE_AUTHOR: '钱七',
          SOURCE_TITLE: '这个想法不错，我支持先试点的方案。',
          GROUP_ID: groupId
        }
      ];
      
      setMessageList(mockMessages);
    } catch (error) {
      message.error('加载消息列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理群组选择
  const handleGroupSelect = (group: GroupInfo) => {
    setSelectedGroup(group);
    loadMessages(group.id);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSelectedGroup(null);
    setMessageList([]);
  };

  // 切换侧边栏
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // 刷新数据
  const refreshData = () => {
    loadGroupList();
  };

  // 过滤群组列表
  const filteredGroups = groupList.filter(group => 
    group.name.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'ID',
      key: 'ID',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '创建时间',
      dataIndex: 'SOURCE_WEB_DATE',
      key: 'SOURCE_WEB_DATE',
      width: 180,
      align: 'center' as const,
    },
    {
      title: '消息作者',
      dataIndex: 'SOURCE_AUTHOR',
      key: 'SOURCE_AUTHOR',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '消息内容',
      dataIndex: 'SOURCE_TITLE',
      key: 'SOURCE_TITLE',
      align: 'left' as const,
      ellipsis: true,
    },
  ];

  return (
    <div className="local-monitor-page">
      {/* 标签页 */}
      <Tabs 
        activeKey={activeTab} 
        onChange={handleTabChange}
        className="monitor-tabs"
      >
        <TabPane tab="微信" key="wechat" />
        <TabPane tab="QQ" key="qq" />
      </Tabs>

      {/* 聊天界面布局 */}
      <div className="chat-wrapper">
        {/* 左侧群组列表 */}
        <div className={`chat-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
          <div className="chat-sidebar-header">
            <Search
              placeholder="群组名"
              allowClear
              onSearch={handleSearch}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ width: '100%' }}
            />
          </div>
          <div className="chat-sidebar-content">
            <div className="chat-list">
              <List
                loading={loading}
                dataSource={filteredGroups}
                renderItem={(group) => (
                  <List.Item
                    className={`group-item ${selectedGroup?.id === group.id ? 'active' : ''}`}
                    onClick={() => handleGroupSelect(group)}
                  >
                    <List.Item.Meta
                      avatar={<MessageOutlined />}
                      title={group.name}
                      description={
                        <div>
                          <div className="group-info">
                            成员: {group.memberCount} | {group.lastTime}
                          </div>
                          <div className="last-message">{group.lastMessage}</div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          </div>
        </div>

        {/* 右侧消息内容 */}
        <div className="chat-main">
          {/* 头部 */}
          <div className="chat-header">
            <div className="chat-toggle-btn" onClick={toggleSidebar}>
              <MenuOutlined />
            </div>
            <div className="chat-title">
              <Title level={4} style={{ margin: 0 }}>
                {selectedGroup?.name || '请选择群组'}
              </Title>
            </div>
            <div className="chat-actions">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refreshData}
                loading={loading}
              >
                刷新
              </Button>
            </div>
          </div>

          {/* 消息表格 */}
          <div className="chat-content">
            <Table
              columns={columns}
              dataSource={messageList}
              rowKey="ID"
              loading={loading}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条消息`,
              }}
              scroll={{ y: 'calc(100vh - 300px)' }}
              size="small"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page_Local_Monitor;
