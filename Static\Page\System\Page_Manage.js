// 设置选择框样式
$('.single-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: <PERSON><PERSON><PERSON>($(this).data('allow-clear')),
});

$('.multiple-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: Bo<PERSON>an($(this).data('allow-clear')),
});
/****************************************方案设置***************************************************/
var Manage_Keyword_Info_list = [];
var Mamage_Keyword_Info_UUID = '';
$('#keyword-tab').click(function() {
    console.log('方案设置')
    if (Manage_Keyword_Info_list.length === 0) {
        Reload_Table_Keyword_Style();
        Requests_Manage_Keyword_Info();
    };
});

// 实例化表格
var $Manage_Keyword_Table = $('#Manage_Keyword_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '160px',
        targets: 5,
        render: function (data, type, row) {
            var element_editor_Info = `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" data-toggle="modal" data-target="#Detail_Keyword_Modal">编辑</button>
                <button class="btn btn-outline-info btn-delect btn-sm m-1 px-2" >删除</button>
            `;
            if (row.USER_STATUS !== 'Active') {
                element_editor_Info += `<button class="btn btn-outline-info btn-section btn-sm m-1 px-2" >关闭</button>`
            } else {
                element_editor_Info += `<button class="btn btn-outline-info btn-section btn-sm m-1 px-2" >激活</button>`
            };
            return element_editor_Info;
        },
      },
    ],
    columns: [
      { title: '序号', data: 'Index', width: '30px'},
      { title: '方案名称', data: 'USER_NAME', width: '120px'},
      { title: '所属分类', data: 'USER_TYPE', width: '80px'},
      { title: '预设条件', data: 'USER_USE', render: function (data, type, row) {
            var element_Info = ``;
            element_Info += `<button class="btn btn-light btn-sm m-1 px-1" >关键词:${row['USER_USE']['KEYWORD_INFO']}</button>`;
            element_Info += `<button class="btn btn-light btn-sm m-1 px-1" >特征词:${row['USER_USE']['FEATUREWORD_INFO']}</button>`;
            element_Info += `<button class="btn btn-light btn-sm m-1 px-1" >停用词:${row['USER_USE']['STOPWORD_INFO']}</button>`;
            return element_Info;
        },
      },
      { title: '方案状态', data: 'USER_STATUS', width: '80px', render: function (data, type, row) {
            if (row.USER_STATUS !== 'Active') {
                var element_status_Info = `<button class="btn btn-success btn-sm m-1 px-1" >当前激活</button>`;
            } else {
                var element_status_Info = `<button class="btn btn-light btn-sm m-1 px-1" >等待激活</button>`;
            };
            return element_status_Info;
        },
      },
    ],
});

// 重置表格属性
function Reload_Table_Keyword_Style() {
    // 弹窗表格表头和行都被添加了width:0px,统一清除
    $('#Manage_Keyword_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Manage_Keyword_Table td, #Manage_Keyword_Table th');
    cells.forEach(function(cell) {
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '方案名称') {
            cell.style.width = '150px';
        } else if (cell.textContent === '操作') {
            cell.style.width = '150px';
        } else {
            cell.style.width = '';
        };
    });
};

// 初始化请求
function Requests_Manage_Keyword_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_keyword_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Keyword_Info_list = Result.Keyword_Info_list;
                $Manage_Keyword_Table.clear();
                $Manage_Keyword_Table.rows.add(Manage_Keyword_Info_list);
                $Manage_Keyword_Table.draw();
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

// 监听方案编辑触发
$('#Manage_Keyword_Table tbody').on('click', '.btn-edit', function () {
    // 获取整行数据
    let tr = $(this).closest('tr');
    let row = $Manage_Keyword_Table.row(tr).data();
    console.log('方案编辑:',row);
    Mamage_Keyword_Info_UUID = row.USER_UUID;
    $('#Keyword_USER_NAME').val(row.USER_NAME);
    $('#Keyword_USER_TYPE').val(row.USER_TYPE).trigger('change');
    $('#Keyword_KEYWORD_INFO').val(row.USER_USE.KEYWORD_INFO);
    $('#Keyword_FEATUREWORD_INFO').val(row.USER_USE.FEATUREWORD_INFO);
    $('#Keyword_STOPWORD_INFO').val(row.USER_USE.STOPWORD_INFO);
});

// 监听方案激活触发
$('#Manage_Keyword_Table tbody').on('click', '.btn-section', function () {
    let tr = $(this).closest('tr');
    let row = $Manage_Keyword_Table.row(tr).data();
    console.log('方案激活触发:',row);
    Manage_Keyword_Info_list.forEach(item => {
        if (item.USER_UUID === row.USER_UUID) {
            item.USER_STATUS = "Section";
        } else {
            item.USER_STATUS = "Active"; 
        };
    });
    Change_Keyword_Table_Info();
});

// 监听方案删除触发
$('#Manage_Keyword_Table tbody').on('click', '.btn-delect', function () {
    let tr = $(this).closest('tr');
    let row = $Manage_Keyword_Table.row(tr).data();
    console.log('方案删除触发:',row);
    let indexToRemove = Manage_Keyword_Info_list.findIndex(item => item.USER_UUID === row.USER_UUID);
    if (indexToRemove !== -1) {
        Manage_Keyword_Info_list.splice(indexToRemove, 1);
    };
    Change_Keyword_Table_Info();
});

// 监听方案新增触发
$('.btn-addkeyword').on('click', function () {
    console.log('监听方案新增触发:');
    Mamage_Keyword_Info_UUID = '';
    $('#Keyword_USER_NAME').val('');
    $('#Keyword_USER_TYPE').val('时事新闻').trigger('change');
    $('#Keyword_KEYWORD_INFO').val('');
    $('#Keyword_FEATUREWORD_INFO').val('');
    $('#Keyword_STOPWORD_INFO').val('');
});

// 方案新增/编辑modal 保存按钮触发
$('#Editor_Add_Keyword_Save_Button').on('click',function () {
    console.log('方案新增/编辑modal  保存按钮触发');
    let Editor_USER_NAME = $('#Keyword_USER_NAME').val();
    let Editor_USER_TYPE = $('#Keyword_USER_TYPE').val();
    let Editor_KEYWORD_INFO = $('#Keyword_KEYWORD_INFO').val();
    let Editor_FEATUREWORD_INFO = $('#Keyword_FEATUREWORD_INFO').val();
    let Editor_STOPWORD_INFO = $('#Keyword_STOPWORD_INFO').val();
    console.log('Editor_USER_NAME:',Editor_USER_NAME);
    console.log('Editor_USER_TYPE:',Editor_USER_TYPE);
    console.log('Editor_KEYWORD_INFO:',Editor_KEYWORD_INFO);
    console.log('Editor_FEATUREWORD_INFO:',Editor_FEATUREWORD_INFO);
    console.log('Editor_STOPWORD_INFO:',Editor_STOPWORD_INFO);
    console.log('Mamage_Keyword_Info_UUID:',Mamage_Keyword_Info_UUID);
    if (Editor_USER_NAME.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[方案名称]为空!'
        });
        return ;
    };
    if (Editor_KEYWORD_INFO.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[关键词]为空!'
        });
        return ;
    };
    if (Mamage_Keyword_Info_UUID !== '') {
        // 编辑保存
        let indexToRemove = Manage_Keyword_Info_list.findIndex(item => item.USER_UUID === Mamage_Keyword_Info_UUID);
        // console.log('indexToRemove:',indexToRemove)
        Manage_Keyword_Info_list[indexToRemove]['USER_NAME'] = Editor_USER_NAME;
        Manage_Keyword_Info_list[indexToRemove]['USER_TYPE'] = Editor_USER_TYPE;
        Manage_Keyword_Info_list[indexToRemove]['USER_USE']['KEYWORD_INFO'] = Editor_KEYWORD_INFO;
        Manage_Keyword_Info_list[indexToRemove]['USER_USE']['FEATUREWORD_INFO'] = Editor_FEATUREWORD_INFO;
        Manage_Keyword_Info_list[indexToRemove]['USER_USE']['STOPWORD_INFO'] = Editor_STOPWORD_INFO;
    
    }else {
        // 新增保存
        // 判断用户名是否被使用
        let indexToRepect = Manage_Keyword_Info_list.findIndex(item => item.USER_NAME === Editor_USER_NAME);
        // console.log('indexToRepect:',indexToRepect)
        if (indexToRepect !== -1) {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '此[方案名称]已被使用，请更换!'
            });
            return ;
        };
        let Keyword_Add_Dict = {
            'Index':Manage_Keyword_Info_list.length + 1,
            'ID':Manage_Keyword_Info_list.length + 1,
            'USER_UUID':Manage_Keyword_Info_list.length + 1000,
            'USER_NAME':Editor_USER_NAME,
            'USER_TYPE':Editor_USER_TYPE,
            'USER_USE':{
                'KEYWORD_INFO':Editor_KEYWORD_INFO,
                'FEATUREWORD_INFO':Editor_FEATUREWORD_INFO,
                'STOPWORD_INFO':Editor_STOPWORD_INFO,
            },
            'USER_STATUS':'Active',
        };
        Manage_Keyword_Info_list.push(Keyword_Add_Dict);
    };
    // 关闭弹窗
    $('#Detail_Keyword_Modal').modal('hide');
    // console.log("修改后的表格信息:",User_Info_Dist.User_Info_list);
    Change_Keyword_Table_Info();
});

// 表格变动 刷新到Table_UI
function Change_Keyword_Table_Info() {
    $Manage_Keyword_Table.clear();
    $Manage_Keyword_Table.rows.add(Manage_Keyword_Info_list);
    $Manage_Keyword_Table.draw();
};

// 方案提交
$('#Submit_Translate_Manage_Keyword').on('click',function () {
    console.log('执行方案整体提交:',Manage_Keyword_Info_list);
    // console.log('提交用户信息')
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_keyword_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_Keyword_Info_list':Manage_Keyword_Info_list,
            'Manage_Type':'Manage'
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '提交更新网络出错！'
            });
        });
});
/****************************************预警设置***************************************************/ 
var Manage_Alarm_Info_list = [];
var Mamage_Alarm_Info_UUID = '';
$('#alarm-tab').click(function() {
    console.log('预警设置')
    if (Manage_Alarm_Info_list.length === 0) {
        Reload_Table_Alarm_Style();
        Requests_Manage_Alarm_Info();
    };
});
// 实例化表格
var $Manage_Alarm_Table = $('#Manage_Alarm_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 4,
        render: function (data, type, row) {
            return `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" data-toggle="modal" data-target="#Detail_Alarm_Modal">编辑</button>
                <button class="btn btn-outline-info btn-delect btn-sm m-1 px-2" >删除</button>
            `;
        },
      },
    ],
    columns: [
      { title: '序号', data: 'Index', width: '50px'},
      { title: '预警名称', data: 'ALARM_NAME', width: '150px'},
      { title: '预警方式', data: 'ALARM_TYPE', width: '150px'},
      { title: '预警内容', data: 'ALARM_INFO'},
    ],
});
// 重置表格属性
function Reload_Table_Alarm_Style() {
    // 弹窗表格表头和行都被添加了width:0px,统一清除
    $('#Manage_Alarm_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Manage_Alarm_Table td, #Manage_Alarm_Table th');
    cells.forEach(function(cell) {
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '预警名称') {
            cell.style.width = '150px';
        } else if (cell.textContent === '预警方式') {
            cell.style.width = '150px';
        } else if (cell.textContent === '操作') {
            cell.style.width = '120px';
        } else {
            cell.style.width = '';
        };
    });
};
// 初始化请求
function Requests_Manage_Alarm_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_alarm_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Alarm_Info_list = Result.Alarm_Info_list;
                $Manage_Alarm_Table.clear();
                $Manage_Alarm_Table.rows.add(Result.Alarm_Info_list);
                $Manage_Alarm_Table.draw();
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};
// 监听预警编辑触发
$('#Manage_Alarm_Table tbody').on('click', '.btn-edit', function () {
    // 获取整行数据
    let tr = $(this).closest('tr');
    let row = $Manage_Alarm_Table.row(tr).data();
    console.log('方案编辑:',row);
    Mamage_Alarm_Info_UUID = row.ALARM_UUID;
    $('#Editor_ALARM_NAME').val(row.ALARM_NAME);
    $('#Editor_ALARM_TYPE').val(row.ALARM_TYPE).trigger('change');
    $('#Editor_ALARM_INFO').val(row.ALARM_INFO);
    $('#Editor_ALARM_SAMPLE').val("K'关键词',S'特征词',U'账号',WB'网站',FR'论坛',SA'社交帐号',F'次数',T'时间(天数)',||'或',&&'与',!'非',()'括号'>> 'K:(西藏||新疆)&&S:(游行||示威)&&SA&&T:3',表示'三天内，关键词包含西藏或新疆，特征词包含游行或示威，来源为社交媒体的情报'");

});
// 监听预警新增触发
$('.btn-addalarm').on('click', function () {
    console.log('监听预警新增触发:');
    Mamage_Alarm_Info_UUID = '';
    $('#Editor_ALARM_TYPE').val('平台预警').trigger('change');
    $('#Editor_ALARM_NAME').val('');
    $('#Editor_ALARM_INFO').val('');
    $('#Editor_ALARM_SAMPLE').val("K'关键词',S'特征词',U'账号',WB'网站',FR'论坛',SA'社交帐号',F'次数',T'时间(天数)',||'或',&&'与',!'非',()'括号'>> 'K:(西藏||新疆)&&S:(游行||示威)&&SA&&T:3',表示'三天内，关键词包含西藏或新疆，特征词包含游行或示威，来源为社交媒体的情报'");
});

// 预警新增/编辑modal 保存按钮触发
$('#Editor_Add_Alarm_Save_Button').on('click',function () {
    console.log('预警新增/编辑modal  保存按钮触发');
    let Editor_ALARM_NAME = $('#Editor_ALARM_NAME').val();
    let Editor_ALARM_TYPE = $('#Editor_ALARM_TYPE').val();
    let Editor_ALARM_INFO = $('#Editor_ALARM_INFO').val();
    console.log('Editor_ALARM_NAME:',Editor_ALARM_NAME);
    console.log('Editor_ALARM_TYPE:',Editor_ALARM_TYPE);
    console.log('Editor_ALARM_INFO:',Editor_ALARM_INFO);
    if (Editor_ALARM_NAME.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[预警名称]为空!'
        });
        return ;
    };
    if (Editor_ALARM_INFO.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[预警内容]为空!'
        });
        return ;
    };
    if (Mamage_Alarm_Info_UUID !== '') {
        // 编辑保存
        let indexToRemove = Manage_Alarm_Info_list.findIndex(item => item.ALARM_UUID === Mamage_Alarm_Info_UUID);
        // console.log('indexToRemove:',indexToRemove)
        Manage_Alarm_Info_list[indexToRemove]['ALARM_NAME'] = Editor_ALARM_NAME;
        Manage_Alarm_Info_list[indexToRemove]['ALARM_TYPE'] = Editor_ALARM_TYPE;
        Manage_Alarm_Info_list[indexToRemove]['ALARM_INFO'] = Editor_ALARM_INFO;

    }else {
        // 新增保存
        // 判断用户名是否被使用
        let indexToRepect = Manage_Alarm_Info_list.findIndex(item => item.ALARM_NAME === Editor_ALARM_NAME);
        // console.log('indexToRepect:',indexToRepect)
        if (indexToRepect !== -1) {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '此[预警名称]已注册，请使用其他名称!'
            });
            return ;
        };
        let Alarm_Add_Dict = {
            'Index':Manage_Alarm_Info_list.length + 1,
            'ID':Manage_Alarm_Info_list.length + 1,
            'ALARM_UUID':Manage_Alarm_Info_list.length + 1000,
            'ALARM_NAME':Editor_ALARM_NAME,
            'ALARM_TYPE':Editor_ALARM_TYPE,
            'ALARM_INFO':Editor_ALARM_INFO,
        };
        Manage_Alarm_Info_list.push(Alarm_Add_Dict);
    };
    // 关闭弹窗
    $('#Detail_Alarm_Modal').modal('hide');
    // console.log("修改后的表格信息:",User_Info_Dist.User_Info_list);
    Change_Alarm_Table_Info();
});

// 监听预警删除触发
$('#Manage_Alarm_Table tbody').on('click', '.btn-delect', function () {
    let tr = $(this).closest('tr');
    let row = $Manage_Alarm_Table.row(tr).data();
    console.log('预警删除触发:',row);
    let indexToRemove = Manage_Alarm_Info_list.findIndex(item => item.ALARM_UUID === row.ALARM_UUID);
    if (indexToRemove !== -1) {
        Manage_Alarm_Info_list.splice(indexToRemove, 1);
    };
    Change_Alarm_Table_Info();
});

// 表格变动 刷新到Table_UI
function Change_Alarm_Table_Info() {
    $Manage_Alarm_Table.clear();
    $Manage_Alarm_Table.rows.add(Manage_Alarm_Info_list);
    $Manage_Alarm_Table.draw();
};

// 预警提交
$('#Submit_Translate_Manage_Alarm').on('click',function () {
    console.log('执行预警提交整体提交:',Manage_Alarm_Info_list);
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_alarm_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_Alarm_Info_list':Manage_Alarm_Info_list,
            'Manage_Type':'Manage'
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '提交更新网络出错！'
            });
        });
});
/****************************************账号管理***************************************************/ 
var Manage_Account_Info_list = [];
var Mamage_Account_Info_UUID = '';
$('#account-tab').click(function() {
    console.log('账号管理');
    if (Manage_Account_Info_list.length === 0) {
        Reload_Table_Account_Style();
        Requests_Manage_Account_Info();
    };
});
// 实例化表格
var $Manage_Account_Table = $('#Manage_Account_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 5,
        render: function (data, type, row) {
            return `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" data-toggle="modal" data-target="#Detail_Account_Modal">编辑</button>
                <button class="btn btn-outline-info btn-delect btn-sm m-1 px-2" >删除</button>
            `;
        },
      },
    ],
    columns: [
      { title: '序号', data: 'Index', width: '50px'},
      { title: '账号平台', data: 'ACCOUNT_PLATFORM', width: '150px'},
      { title: '账号类型', data: 'ACCOUNT_TYPE', width: '150px'},
      { title: '账号名称', data: 'ACCOUNT_NAME'},
      { title: '账号主页', data: 'ACCOUNT_URL'},
    ],
});

// 重置表格属性
function Reload_Table_Account_Style() {
    // 弹窗表格表头和行都被添加了width:0px,统一清除
    $('#Manage_Account_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Manage_Account_Table td, #Manage_Account_Table th');
    cells.forEach(function(cell) {
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '账号平台') {
            cell.style.width = '150px';
        } else if (cell.textContent === '账号类型') {
            cell.style.width = '150px';
        } else if (cell.textContent === '操作') {
            cell.style.width = '120px';
        } else {
            cell.style.width = '';
        };
    });
};
// 监听账号编辑触发
$('#Manage_Account_Table tbody').on('click', '.btn-edit', function () {
    // 获取整行数据
    let tr = $(this).closest('tr');
    let row = $Manage_Account_Table.row(tr).data();
    console.log('方案编辑:',row);
    Mamage_Account_Info_UUID = row.ACCOUNT_UUID;
    $('#Editor_ACCOUNT_TYPE').val(row.ACCOUNT_TYPE).trigger('change');
    $('#Editor_ACCOUNT_PLATFORM').val(row.ACCOUNT_PLATFORM).trigger('change');
    $('#Editor_ACCOUNT_NAME').val(row.ACCOUNT_NAME);
    $('#Editor_ACCOUNT_URL').val(row.ACCOUNT_URL);
});
// 监听账号新增触发
$('.btn-addaccount').on('click', function () {
    console.log('监听账号新增触发:');
    Mamage_Account_Info_UUID = '';
    $('#Editor_ACCOUNT_TYPE').val('敌对账号').trigger('change');
    $('#Editor_ACCOUNT_PLATFORM').val('抖音').trigger('change');
    $('#Editor_ACCOUNT_NAME').val('');
    $('#Editor_ACCOUNT_URL').val('');
});

// 账号新增/编辑modal 保存按钮触发
$('#Editor_Add_Account_Save_Button').on('click',function () {
    console.log('账号新增/编辑modal  保存按钮触发');
    let Editor_ACCOUNT_TYPE = $('#Editor_ACCOUNT_TYPE').val();
    let Editor_ACCOUNT_PLATFORM = $('#Editor_ACCOUNT_PLATFORM').val();
    let Editor_ACCOUNT_NAME = $('#Editor_ACCOUNT_NAME').val();
    let Editor_ACCOUNT_URL = $('#Editor_ACCOUNT_URL').val();
    console.log('Editor_ACCOUNT_TYPE:',Editor_ACCOUNT_TYPE);
    console.log('Editor_ACCOUNT_PLATFORM:',Editor_ACCOUNT_PLATFORM);
    console.log('Editor_ACCOUNT_NAME:',Editor_ACCOUNT_NAME);
    console.log('Editor_ACCOUNT_URL:',Editor_ACCOUNT_URL);
    console.log('Mamage_Account_Info_UUID:',Mamage_Account_Info_UUID);
    if (Editor_ACCOUNT_NAME.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[账号名称]为空!'
        });
        return ;
    };
    if (Editor_ACCOUNT_URL.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[账号主页]为空!'
        });
        return ;
    };
    if (Mamage_Account_Info_UUID !== '') {
        // 编辑保存
        let indexToRemove = Manage_Account_Info_list.findIndex(item => item.ACCOUNT_UUID === Mamage_Account_Info_UUID);
        // console.log('indexToRemove:',indexToRemove)
        Manage_Account_Info_list[indexToRemove]['ACCOUNT_TYPE'] = Editor_ACCOUNT_TYPE;
        Manage_Account_Info_list[indexToRemove]['ACCOUNT_PLATFORM'] = Editor_ACCOUNT_PLATFORM;
        Manage_Account_Info_list[indexToRemove]['ACCOUNT_NAME'] = Editor_ACCOUNT_NAME;
        Manage_Account_Info_list[indexToRemove]['ACCOUNT_URL'] = Editor_ACCOUNT_URL;

    }else {
        // 新增保存
        // 判断用户名是否被使用
        let indexToRepect = Manage_Account_Info_list.findIndex(item => item.ACCOUNT_NAME === Editor_ACCOUNT_NAME);
        // console.log('indexToRepect:',indexToRepect)
        if (indexToRepect !== -1) {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '此[账号名称]已注册，请添加其他账号!'
            });
            return ;
        };
        let Account_Add_Dict = {
            'Index':Manage_Account_Info_list.length + 1,
            'ID':Manage_Account_Info_list.length + 1,
            'ACCOUNT_UUID':Manage_Account_Info_list.length + 1000,
            'ACCOUNT_URL':Editor_ACCOUNT_URL,
            'ACCOUNT_NAME':Editor_ACCOUNT_NAME,
            'ACCOUNT_TYPE':Editor_ACCOUNT_TYPE,
            'ACCOUNT_PLATFORM':Editor_ACCOUNT_PLATFORM,
        };
        Manage_Account_Info_list.push(Account_Add_Dict);
    };
    // 关闭弹窗
    $('#Detail_Account_Modal').modal('hide');
    // console.log("修改后的表格信息:",User_Info_Dist.User_Info_list);
    Change_Account_Table_Info();
});

// 账号提交
$('#Submit_Translate_Manage_Account').on('click',function () {
    console.log('执行账号提交整体提交:',Manage_Account_Info_list);
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_account_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_Account_Info_list':Manage_Account_Info_list,
            'Manage_Type':'Manage'
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '提交更新网络出错！'
            });
        });
});

// 监听账号删除触发
$('#Manage_Account_Table tbody').on('click', '.btn-delect', function () {
    let tr = $(this).closest('tr');
    let row = $Manage_Account_Table.row(tr).data();
    console.log('账号删除触发:',row);
    let indexToRemove = Manage_Account_Info_list.findIndex(item => item.ACCOUNT_UUID === row.ACCOUNT_UUID);
    if (indexToRemove !== -1) {
        Manage_Account_Info_list.splice(indexToRemove, 1);
    };
    Change_Account_Table_Info();
});

// 表格变动 刷新到Table_UI
function Change_Account_Table_Info() {
    $Manage_Account_Table.clear();
    $Manage_Account_Table.rows.add(Manage_Account_Info_list);
    $Manage_Account_Table.draw();
};

// 初始化请求
function Requests_Manage_Account_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_account_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Account_Info_list = Result.Account_Info_list;
                $Manage_Account_Table.clear();
                $Manage_Account_Table.rows.add(Result.Account_Info_list);
                $Manage_Account_Table.draw();
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};
/****************************************数源管理***************************************************/ 
var Manage_Source_Info_list = [];
var Mamage_Source_Info_UUID = '';
$('#source-tab').click(function() {
    console.log('数源管理');
    if (Manage_Source_Info_list.length === 0) {
        Reload_Table_Source_Style();
        Requests_Manage_Source_Info();
    };
});
// 实例化表格
var $Manage_Source_Table = $('#Manage_Source_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 4,
        render: function (data, type, row) {
            return `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" data-toggle="modal" data-target="#Detail_Source_Modal">编辑</button>
                <button class="btn btn-outline-info btn-delect btn-sm m-1 px-2" >删除</button>
            `;
        },
      },
    ],
    columns: [
      { title: '序号', data: 'Index', width: '50px'},
      { title: '数源类型', data: 'SOURCE_TYPE', width: '150px'},
      { title: '数源名称', data: 'SOURCE_NAME'},
      { title: '数源主页', data: 'SOURCE_URL'},
    //   { title: '数源状态', data: 'SOURCE_STATUS'},
    ],
    // scrollY: 'calc(100vh - 320px)', // 设置表格固定高度
});

// 重置表格属性
function Reload_Table_Source_Style() {
    $('#Manage_Source_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Manage_Source_Table td, #Manage_Source_Table th');
    cells.forEach(function(cell) {
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '数源类型') {
            cell.style.width = '150px';
        } else if (cell.textContent === '操作') {
            cell.style.width = '120px';
        } else {
            cell.style.width = '';
        };
    });
};

// 监听数源编辑触发
$('#Manage_Source_Table tbody').on('click', '.btn-edit', function () {
    // 获取整行数据
    let tr = $(this).closest('tr');
    let row = $Manage_Source_Table.row(tr).data();
    console.log('数源编辑:',row);
    Mamage_Source_Info_UUID = row.SOURCE_UUID;
    $('#Editor_SOURCE_TYPE').val(row.SOURCE_TYPE).trigger('change');
    $('#Editor_SOURCE_NAME').val(row.SOURCE_NAME);
    $('#Editor_SOURCE_URL').val(row.SOURCE_URL);
});


// 监听数源新增触发
$('.btn-addsource').on('click', function () {
    console.log('监听数源新增触发:');
    Mamage_Source_Info_UUID = '';
    $('#Editor_SOURCE_TYPE').val('敌对网站').trigger('change');
    $('#Editor_SOURCE_NAME').val('');
    $('#Editor_SOURCE_URL').val('');
});

// 数源新增/编辑modal 保存按钮触发
$('#Editor_Add_Source_Save_Button').on('click',function () {
    console.log('数源新增/编辑modal  保存按钮触发');
    let Editor_SOURCE_TYPE = $('#Editor_SOURCE_TYPE').val();
    let Editor_SOURCE_NAME = $('#Editor_SOURCE_NAME').val();
    let Editor_SOURCE_URL = $('#Editor_SOURCE_URL').val();
    console.log('Editor_SOURCE_TYPE:',Editor_SOURCE_TYPE);
    console.log('Editor_SOURCE_NAME:',Editor_SOURCE_NAME);
    console.log('Editor_SOURCE_URL:',Editor_SOURCE_URL);
    console.log('Mamage_Source_Info_UUID:',Mamage_Source_Info_UUID);
    if (Editor_SOURCE_NAME.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[数源名称]为空!'
        });
        return ;
    };
    if (Editor_SOURCE_URL.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[数源主页]为空!'
        });
        return ;
    };
    if (Mamage_Source_Info_UUID !== '') {
        // 编辑保存
        let indexToRemove = Manage_Source_Info_list.findIndex(item => item.SOURCE_UUID === Mamage_Source_Info_UUID);
        // console.log('indexToRemove:',indexToRemove)
        Manage_Source_Info_list[indexToRemove]['SOURCE_TYPE'] = Editor_SOURCE_TYPE;
        Manage_Source_Info_list[indexToRemove]['SOURCE_NAME'] = Editor_SOURCE_NAME;
        Manage_Source_Info_list[indexToRemove]['SOURCE_URL'] = Editor_SOURCE_URL;

    }else {
        // 新增保存
        // 判断用户名是否被使用
        let indexToRepect = Manage_Source_Info_list.findIndex(item => item.SOURCE_NAME === Editor_SOURCE_NAME);
        // console.log('indexToRepect:',indexToRepect)
        if (indexToRepect !== -1) {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '此[数源名称]已注册，请添加其他数源!'
            });
            return ;
        };
        let Source_Add_Dict = {
            'Index':Manage_Source_Info_list.length + 1,
            'ID':Manage_Source_Info_list.length + 1,
            'SOURCE_UUID':Manage_Source_Info_list.length + 1000,
            'SOURCE_URL':Editor_SOURCE_URL,
            'SOURCE_NAME':Editor_SOURCE_NAME,
            'SOURCE_TYPE':Editor_SOURCE_TYPE,
        };
        Manage_Source_Info_list.push(Source_Add_Dict);
    };
    // 关闭弹窗
    $('#Detail_Source_Modal').modal('hide');
    // console.log("修改后的表格信息:",User_Info_Dist.User_Info_list);
    Change_Source_Table_Info();
});

// 监听数源删除触发
$('#Manage_Source_Table tbody').on('click', '.btn-delect', function () {
    let tr = $(this).closest('tr');
    let row = $Manage_Source_Table.row(tr).data();
    console.log('账号删除触发:',row);
    let indexToRemove = Manage_Source_Info_list.findIndex(item => item.SOURCE_UUID === row.SOURCE_UUID);
    if (indexToRemove !== -1) {
        Manage_Source_Info_list.splice(indexToRemove, 1);
    };
    Change_Source_Table_Info();
});

// 表格变动 刷新到Table_UI
function Change_Source_Table_Info() {
    $Manage_Source_Table.clear();
    $Manage_Source_Table.rows.add(Manage_Source_Info_list);
    $Manage_Source_Table.draw();
};

// 数源提交
$('#Submit_Translate_Manage_Source').on('click',function () {
    console.log('执行数源提交整体提交:',Manage_Source_Info_list);
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_source_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_Source_Info_list':Manage_Source_Info_list,
            'Manage_Type':'Manage'
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '提交更新网络出错！'
            });
        });
});

// 初始化请求
function Requests_Manage_Source_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_source_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Source_Info_list = Result.Source_Info_list;
                $Manage_Source_Table.clear();
                $Manage_Source_Table.rows.add(Result.Source_Info_list);
                $Manage_Source_Table.draw();
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};
/****************************************用户管理***************************************************/ 
var Manage_User_Info_list = [];
var Mamage_User_Info_UUID = '';
var Mamage_User_Face_Info = '';
$('#user-tab').click(function() {
    console.log('用户管理');
    if (Manage_User_Info_list.length === 0) {
        Reload_Table_User_Style();
        Requests_Manage_User_Info();
    };
});

// 实例化表格
var $Manage_User_Table = $('#Manage_User_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 6,
        render: function (data, type, row) {
            return `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" data-toggle="modal" data-target="#Detail_User_Modal">编辑</button>
            `;
        },
      },
    ],
    columns: [
        { title: '序号', data: 'ID', width: '50px'},
        { title: '头像', data: 'USER_FACE', width: '80px', render: 
            function (data, type, row) {
                return `<img src="${row.USER_FACE}" width="80" height="80" class="rounded-circle p-1 border bg-white" alt="">`;
            },
        },
        { title: '用户昵称', data: 'USER_NICKNAME'},
        { title: '登录账号', data: 'USER_LOGIN_ACCOUNT'},
        { title: '登录密码', data: 'USER_LOGIN_PASSWORD'},
        { title: '所属单位', data: 'USER_UNIT'},
    ],
});

// 重置表格属性
function Reload_Table_User_Style() {
    $('#Manage_User_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Manage_User_Table td, #Manage_User_Table th');
    cells.forEach(function(cell) {
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '头像') {
            cell.style.width = '80px';
        } else {
            cell.style.width = '';
        };
    });
};

// 初始化请求
function Requests_Manage_User_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_user_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_User_Info_list = Result.User_Info_list;
                $Manage_User_Table.clear();
                $Manage_User_Table.rows.add(Result.User_Info_list);
                $Manage_User_Table.draw();
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

// 监听用户编辑触发
$('#Manage_User_Table tbody').on('click', '.btn-edit', function () {
    // 获取整行数据
    let tr = $(this).closest('tr');
    let row = $Manage_User_Table.row(tr).data();
    console.log('用户编辑:',row);
    Mamage_User_Info_UUID = row.USER_UUID;
    Mamage_User_Face_Info = row.USER_FACE;
    $('#Editor_USER_LOGIN_ACCOUNT').val(row.USER_LOGIN_ACCOUNT);
    $('#Editor_USER_LOGIN_PASSWORD').val(row.USER_LOGIN_PASSWORD);
    $('#Editor_USER_NICKNAME').val(row.USER_NICKNAME);
    $('#Editor_USER_UNIT').val(row.USER_UNIT);
});

// 用户编辑modal 保存按钮触发
$('#Editor_Add_User_Save_Button').on('click',function () {
    console.log('用户编辑modal  保存按钮触发');
    let Editor_USER_LOGIN_ACCOUNT = $('#Editor_USER_LOGIN_ACCOUNT').val();
    let Editor_USER_LOGIN_PASSWORD = $('#Editor_USER_LOGIN_PASSWORD').val();
    let Editor_USER_NICKNAME = $('#Editor_USER_NICKNAME').val();
    let Editor_USER_UNIT = $('#Editor_USER_UNIT').val();
    console.log('Editor_USER_LOGIN_ACCOUNT:',Editor_USER_LOGIN_ACCOUNT);
    console.log('Editor_USER_LOGIN_PASSWORD:',Editor_USER_LOGIN_PASSWORD);
    console.log('Editor_USER_NICKNAME:',Editor_USER_NICKNAME);
    console.log('Editor_USER_UNIT:',Editor_USER_UNIT);
    console.log('Mamage_User_Info_UUID:',Mamage_User_Info_UUID);
    if (Editor_USER_LOGIN_PASSWORD.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[登录密码]为空!'
        });
        return ;
    };
    let indexToRemove = Manage_User_Info_list.findIndex(item => item.USER_UUID === Mamage_User_Info_UUID);
    // console.log('indexToRemove:',indexToRemove)
    Manage_User_Info_list[indexToRemove]['USER_LOGIN_PASSWORD'] = Editor_USER_LOGIN_PASSWORD;
    Manage_User_Info_list[indexToRemove]['USER_NICKNAME'] = Editor_USER_NICKNAME;
    Manage_User_Info_list[indexToRemove]['USER_UNIT'] = Editor_USER_UNIT;
    Manage_User_Info_list[indexToRemove]['USER_FACE'] = Mamage_User_Face_Info;
    // 关闭弹窗
    $('#Detail_User_Modal').modal('hide');
    // console.log("修改后的表格信息:",User_Info_Dist.User_Info_list);
    Change_User_Table_Info();
});

// 用户头像上传
$('#Editor_User_Upload_Face').FancyFileUpload({
    url:'/CSC_file_upload', //上传路径
    maxfilesize: 1000 * 1024 * 1024,   //上传文件最大值，单位1000MB
    uploadcompleted: function(event, data) {
        // // console.log('uploadcompleted event:', event);
        // // console.log('uploadcompleted data:',  data);
        let res = data.result;
        if (res.success == true) {
            // 上传成功  修改Face的变量
            Mamage_User_Face_Info = `/static/images/User/Face/${res.file.save_name}`
        } else {
            // console.log('本地存储失败');
        };
    }, //上传成功后回调
    startupload : function(SubmitUpload, event, data) {
        data.formData = data.formData || {};
        data.formData.user_id = Mamage_User_Info_UUID;
        data.formData.file_time = 'User Face';
        // 调用 SubmitUpload() 开始上传
        SubmitUpload();
    }, // 上传时附加参数，如固定参数 使用params即可 无需使用startupload
});

// 表格变动 刷新到Table_UI
function Change_User_Table_Info() {
    $Manage_User_Table.clear();
    $Manage_User_Table.rows.add(Manage_User_Info_list);
    $Manage_User_Table.draw();
};

// 用户提交
$('#Submit_Translate_Manage_User').on('click',function () {
    console.log('执行用户整体提交:',Manage_User_Info_list);
    console.log('提交用户信息')
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_user_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_User_Info_list':Manage_User_Info_list,
            'Manage_Type':'Manage'
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '提交更新出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '提交更新网络出错！'
            });
        });
});
