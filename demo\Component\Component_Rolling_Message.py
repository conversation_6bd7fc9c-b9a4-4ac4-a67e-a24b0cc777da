import sys,time
from PySide6 import QtWidgets, QtGui, QtCore
import qtawesome as qta

class Component_Rolling_Message(QtWidgets.QLabel):
    def __init__(self):
        super().__init__()
        self.Count=0
        # self.setWindowTitle("动态添加 Line 并滚动")
        # self.setGeometry(100, 100, 400, 600)

        # 创建一个垂直布局
        self.v_layout = QtWidgets.QVBoxLayout(self)
        self.v_layout.setContentsMargins(0, 0, 0, 0)

        # 创建一个内容容器
        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(0)  # 内边界
        self.QVBoxLayout_Line.setAlignment(QtCore.Qt.AlignTop)
        self.QVBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边

        # 创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet("QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        self.QScrollArea_Line.setWidgetResizable(True)  # 关键：让内容可调整大小
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        self.v_layout.addWidget(self.QScrollArea_Line, 1)



    def Set_Line(self, line_info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(line_info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(line_info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        # 名称
        QLabel_Name = QtWidgets.QLabel(line_info["Line_Name"])
        QLabel_Name.setFixedWidth(55)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:white")
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # 内容
        QLabel_Content = QtWidgets.QLabel(line_info["Line_Content"])
        # QLabel_Content.setFixedWidth(120)
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content.setStyleSheet("background: transparent;color:white")
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # line_edit = QtWidgets.QLineEdit(line_info["Line_Content"])
        # line_edit.setStyleSheet("background: transparent; color: white;")

        __QHBoxLayout.addWidget(QLabel_Name, 0)
        __QHBoxLayout.addWidget(QLabel_Content)


        return __QLabel

    def Update_Message(self):
        # 模拟新数据



        NewTime =str(time.strftime('%H:%M:%S', time.localtime(time.time())))
        new_line_info = {
            "Line_Type": "Input_Line",
            "Line_Name":f"{ NewTime}",
            "Line_Content":f"雷达【沙河小区3栋9楼303】，暂时没有数据",
            "Line_Size": [28, 288],
            "Line_StyleSheet": """QLabel {
                background-color: rgba(0,0,0,0.8);
                border-radius: 3px;
                border: none;
                color: #1E90FF;
            }
            QLabel:hover {
                background-color: rgba(255,255,255,0.3);
                border: none;
            }""",

            "Line_Parameter": {},
        }
        new_line = self.Set_Line(new_line_info)
        self.QVBoxLayout_Line.addWidget(new_line)
        self.Count+=1

        # 滚动到最后一行
        QtCore.QTimer.singleShot(100, lambda: self.scroll_to_last_line(new_line))

    def scroll_to_last_line(self, new_line):
            # 滚动到最后一行
            self.QScrollArea_Line.ensureWidgetVisible(new_line)

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())