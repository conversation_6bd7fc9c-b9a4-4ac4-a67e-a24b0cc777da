import sys,time,os
from PySide6 import QtWidgets, QtGui, QtCore
import qtawesome as qta
from pydub import AudioSegment
from scipy.signal import butter, filtfilt
import pydub
import numpy as np
import pyloudnorm as pyln
from Bin.System.OS.Component import Component_Common
from Bin.Utils.UtilsCenter import *
class Component_Processing_EQ(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.resize(1280, 720)
        self.setAcceptDrops(True)
        # self.setStyleSheet("QLabel{background:rgba(55, 55, 55, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")
        self.setStyleSheet("QLabel{background:rgba(25,25,50,1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")



        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Content = QtWidgets.QLabel()
        # self.QLabel_Content.setFixedHeight(150)
        self.QLabel_Content.setStyleSheet("QLabel{background:transparent;border: none;}")
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("QLabel{background:transparent;}")

        __QVBoxLayout.addWidget(self.Set_Title(), 0)
        __QVBoxLayout.addWidget(self.Set_Content(),1)
        # __QVBoxLayout.addWidget(self.QLabel_Bottom, 1)

        self.EQFrequencyProcessor_List = {}


    def Set_Title(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(40)
        __QLabel.setStyleSheet("QLabel{background:transparent;}")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)  # 内边界
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setFixedWidth(80)
        QLabel_Title.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        # QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment(QtCore.Qt.AlignVCenter |QtCore.Qt.AlignLeft)
        # QLabel_Title.setMaximumSize(230, 30)
        QLabel_Title.setText("音频EQ处理")

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='white', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setFixedWidth(58)
        QPushButton_Close.setStyleSheet(
            '''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;font-weight: bold;}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())

        __QHBoxLayout.addWidget(QLabel_Icon,  0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter )
        __QHBoxLayout.addWidget(QLabel_Title, 9, alignment=QtCore.Qt.AlignLeft)
        # __QHBoxLayout.contentsRect()
        __QHBoxLayout.addWidget(QPushButton_Close,  1,alignment=QtCore.Qt.AlignRight)


        return __QLabel



    def Set_Content(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("background: transparent;")

        # 使用布局确保占据所有空间
        __QVBoxLayout = QtWidgets.QVBoxLayout(__QLabel)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(3)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)
        # __QVBoxLayout.addStretch(1)
        # 创建一个垂直布局
        # self.v_layout = QtWidgets.QVBoxLayout(self)
        # self.v_layout.setContentsMargins(0, 0, 0, 0)
        #
        # # 创建一个内容容器
        # self.QWidget_TopFiller = QtWidgets.QWidget()
        # self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        # self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        # self.QVBoxLayout_Line.setSpacing(0)  # 内边界
        # self.QVBoxLayout_Line.setAlignment(QtCore.Qt.AlignTop)
        # self.QVBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        #
        # # 创建一个滚动条
        # self.QScrollArea_Line = QtWidgets.QScrollArea()
        # self.QScrollArea_Line.setStyleSheet(
        #     "QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        # self.QScrollArea_Line.setWidgetResizable(True)  # 关键：让内容可调整大小
        # self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        # # self.v_layout.addWidget(self.QScrollArea_Line, 1)
        #
        #
        #
        # __QVBoxLayout.addWidget(self.QScrollArea_Line, 1)
        Line_StyleSheet = """QLabel {
                        background-color: rgba(25,25,50,1);
                        border-radius: 3px;
                        border: none;
                        color: #1E90FF;
            
                    }"""
        NewTime = str(time.strftime('%H:%M:%S', time.localtime(time.time())))
        # Line_Info = {"Line_Type": "Input_Line","Line_Name": f"{ NewTime}","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [28, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}
        #
        # self.QVBoxLayout_Line.addWidget(self.Set_Line_Audio_Address({"Line_Type": "Input_Line","Line_Name": f"{ NewTime}","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [28, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}))
        #
        # self.QScrollArea_Line.ensureWidgetVisible(new_line)

        __QVBoxLayout.addWidget(self.Set_Line_Audio_AddressInput({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Audio_AddressOuntput({"Line_Type": "Input_Line","Line_Name": f"处理结果位置","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)

        __QVBoxLayout.addWidget(self.Set_Line_Audio_EQSelect({"Line_Type": "Input_Line","Line_Name": f"选择音频波段","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)

        __QVBoxLayout.addWidget(self.Set_Line_Audio_EQshow({"Line_Type": "Input_Line","Line_Name": f"{ NewTime}","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [448, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),1)
        __QVBoxLayout.addWidget(self.Set_Line_Audio_Process({"Line_Type": "Input_Line","Line_Name": f"处理进度","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)







        return __QLabel


    def Set_Line_Audio_AddressInput(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        # 名称
        QLabel_Name = Component_Common.Component_Common_QLabel_Click()
        QLabel_Name.setText(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(88)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:white")
        # QLabel_Name.clicked.connect(self.AudioProcessor_Open_File)
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # 内容
        # QLabel_Content = QtWidgets.QLabel(Line_Info["Line_Content"])
        # # QLabel_Content.setFixedWidth(120)
        # QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Content.setStyleSheet("background: transparent;color:white")
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # line_edit = QtWidgets.QLineEdit(line_info["Line_Content"])
        # line_edit.setStyleSheet("background: transparent; color: white;")
        self.QLineEdit_Audio_Input = QtWidgets.QLineEdit()
        self.QLineEdit_Audio_Input.setPlaceholderText("当前已选择音频")
        # self.QLineEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        self.QLineEdit_Audio_Input.setStyleSheet("""
                           QLineEdit {
                               background-color: rgba(0, 0, 0, 0.8);
                               border: 1px solid rgba(255, 255, 255, 0.2);
                               border-radius: 3px;
                               color: white;
                               padding: 5px;
                           }

                       """)
        self.QLineEdit_Audio_Input.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        icon = qta.icon("mdi.file-find", color="white")  # 使用 FontAwesome 5 Solid 的 music 图标msc.file-directory
        action = QtGui.QAction(icon, "", self.QLineEdit_Audio_Input)  # 创建 QAction
        action.triggered.connect(self.AudioProcessor_Input_File)
        self.QLineEdit_Audio_Input.addAction(action, QtWidgets.QLineEdit.TrailingPosition)
        __QHBoxLayout.addWidget(QLabel_Name, 0)
        __QHBoxLayout.addWidget(self.QLineEdit_Audio_Input)

        return __QLabel

    def Set_Line_Audio_AddressOuntput(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        # 名称
        QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(88)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:white")
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # 内容
        self.QLineEdit_Audio_Output = QtWidgets.QLineEdit()
        self.QLineEdit_Audio_Output.setPlaceholderText("系统默认地址")
        # self.QLineEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        self.QLineEdit_Audio_Output.setStyleSheet("""
                               QLineEdit {
                                   background-color: rgba(0, 0, 0, 0.8);
                                   border: 1px solid rgba(255, 255, 255, 0.2);
                                   border-radius: 3px;
                                   color: white;
                                   padding: 5px;
                               }

                           """)
        self.QLineEdit_Audio_Output.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        icon = qta.icon("msc.file-directory", color="white")  # 使用 FontAwesome 5 Solid 的 music 图标
        action = QtGui.QAction(icon, "", self.QLineEdit_Audio_Output)  # 创建 QAction
        action.triggered.connect(self.AudioProcessor_Output_Folder)
        self.QLineEdit_Audio_Output.addAction(action, QtWidgets.QLineEdit.TrailingPosition)
        __QHBoxLayout.addWidget(QLabel_Name, 0)
        __QHBoxLayout.addWidget(self.QLineEdit_Audio_Output)

        return __QLabel

    def Set_Line_Audio_EQSelect(self, Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        # 名称
        QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(88)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:white")
        # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # 内容
        __QHBoxLayout.addWidget(QLabel_Name, 0)
        self.QCheckBox_EQFrequency_List = {}


        Select_List = [
            {"ID":"0","Name":"全选"},
            {"ID":"1","Name":"超低频(20-60Hz)"},
            {"ID":"2","Name":"低频(60-250Hz)"},
            {"ID":"3","Name":"中低频(250-500Hz)"},
            {"ID":"4","Name":"中频(500-1000Hz)"},
            {"ID":"5","Name":"中高频(1000-3000Hz)"},
            {"ID":"6","Name":"临场感(3000-6000Hz)"},
            {"ID":"7","Name":"明亮度(6000-12000Hz)"},
            {"ID":"8","Name":"空气感(12000-20000Hz)"},
        ]







        def Set_Select(Select):
            __QCheckBox = QtWidgets.QCheckBox(Select["Name"])

            __QCheckBox.setStyleSheet("""
                            QCheckBox {
                                background: transparent;
                                color: white;  /* 科技蓝文字 */
                                font-size: 12px;
                                padding-left: 10px;
                            }
                            QCheckBox::indicator {
                                width: 12px;
                                height: 12px;
                                border: 1px solid #1E90FF;  /* 科技蓝边框 */
                                border-radius: 6px;  /* 圆角 */
                                background-color: #1A1A1A;  /* 深色背景 */
                            }
                            QCheckBox::indicator:checked {
                                background-color: blue;  /* 选中时的科技蓝背景 */
                                border: 1px solid #0077CC;  /* 选中时的深科技蓝边框 */
                            }
                        """)
            __QCheckBox.stateChanged.connect(lambda: self.AudioProcessor_EQ_Select(Select))
            self.QCheckBox_EQFrequency_List[Select["ID"]] = __QCheckBox
            return __QCheckBox

        for Select in Select_List:

            # self.check_boxes.append(Set_Select())
            __QHBoxLayout.addWidget(Set_Select(Select))
            # self.h_layout.addWidget(check_box)




        return __QLabel


    def Set_Line_Audio_Process(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        self.QHBoxLayout_Process = QtWidgets.QHBoxLayout(__QLabel)
        self.QHBoxLayout_Process.setAlignment(QtCore.Qt.AlignCenter)
        self.QHBoxLayout_Process.setContentsMargins(0, 0, 0, 0)
        self.QHBoxLayout_Process.setSpacing(3)

        # 名称
        # QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        # QLabel_Name.setFixedWidth(88)
        # QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # QLabel_Name.setStyleSheet("background: transparent;color:white")
        # # __QHBoxLayout.addWidget(QLabel_Name, 1)
        # # 内容
        #
        # __QHBoxLayout.addWidget(QLabel_Name, 0)
        StyleSheet_QLabel = """
                                                                     QLabel {
                                                                         background-color:rgba(40, 52, 80, 0.3);
                                                                         border: 0px solid rgba(0, 180, 255, 60);
                                                                         border-radius: 3px;
                                                                         padding: 0px;
                                                                         font-size:13px;
                                                                         font-weight: bold;
                                                                         color:white;
                                                                     }
                                                                     QLabel:hover {
                                                                         background-color: rgba(0, 100, 150, 150);
                                                                     }
                                                                 """

        # def Set_Buttun(i):
        QLabel_Start = Component_Common.Component_Common_QLabel_Click()
        QLabel_Start.setFixedSize(88,30)
        # QLabel: hover
        # {
        #
        # }
        QLabel_Start.setStyleSheet("QLabel{background-color: rgba(255, 255, 255, 0.6);border: 1px;color:white,font-size:13px;border-radius: 3px;}hover{ background - color: rgba(0, 100, 150, 150);}")
        # QLabel_Start.setStyleSheet(StyleSheet_QLabel)
        QLabel_Start.setFont(QtGui.QFont("Microsoft YaHei", 13))
        QLabel_Start.setAlignment(QtCore.Qt.AlignCenter)

        QLabel_Start.setText("开始处理")
        QLabel_Start.clicked.connect(self.AudioProcessor_Start)

            # return _QLabel

        # for i in ["开始处理"]:
        self.QHBoxLayout_Process.addWidget(QLabel_Start)


        # __QHBoxLayout.addWidget(QLabel_Content)

        return __QLabel



    def Set_Line_Audio_EQshow(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        # __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        # __QLabel.setFixedWidth(line_info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        __QLabel.setStyleSheet("background: black;color:white")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout.setSpacing(0)

        self.tab_widget = QtWidgets.QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #4cc9f0;
                background-color: rgba(0, 0, 0, 0.8);
            }
            QTabBar::tab {
                background-color: black;
                color: white;
                padding: 10px;
                border: 1px solid #4cc9f0;
                border-radius: 1px;
            }
            QTabBar::tab:selected {
                background-color: #4cc9f0;
                color: black;
            }
        """)

        EQ_List = [ "超低频(20-60Hz)", "低频(60-250Hz)", "中低频(250-500Hz)", "中频(500-1000Hz)",
                       "中高频(1000-3000Hz)", "临场感(3000-6000Hz)", "明亮度(6000-12000Hz)", "空气感(12000-20000Hz)"]


        for  EQ in EQ_List:

            _QLabel = QtWidgets.QLabel()
            self.tab_widget.addTab(_QLabel, EQ)


        # __QLabel_2 = QtWidgets.QLabel()


        # self.tab_widget.addTab(__QLabel_2, "频率响应2")



        __QHBoxLayout.addWidget(self.tab_widget)

        return __QLabel


    def AudioProcessor_EQ_Select(self,EQFrequency ):
        print(EQFrequency)



        Select_List = {
            "0": "全选",
            "1": "超低频(20-60Hz)",
            "2": "低频(60-250Hz)",
            "3": "中低频(250-500Hz)",
            "4": "中频(500-1000Hz)}",
            "5": "中高频(1000-3000Hz)",
            "6": "临场感(3000-6000Hz)",
            "7": "明亮度(6000-12000Hz)",
            "8": "空气感(12000-20000Hz)",
        }
        isChecked = self.QCheckBox_EQFrequency_List[EQFrequency["ID"]].isChecked()
        print(isChecked)

        if isChecked:

            if EQFrequency["ID"] =="0":
                for ID , QCheckBox_EQFrequency in self.QCheckBox_EQFrequency_List.items():
                    QCheckBox_EQFrequency.setChecked(True)
                    self.EQFrequencyProcessor_List[ID] ="True"
            else:
                self.EQFrequencyProcessor_List[EQFrequency["ID"]]="True"

        else:

            if EQFrequency["ID"] == "0":
                for ID, QCheckBox_EQFrequency in self.QCheckBox_EQFrequency_List.items():
                    QCheckBox_EQFrequency.setChecked(False)
                    self.EQFrequencyProcessor_List[ID] = "False"
            else:
                    self.EQFrequencyProcessor_List[EQFrequency["ID"]] = "False"




        PJ(self.EQFrequencyProcessor_List)
        #
        #


        #


    def AudioProcessor_Start(self):
        StyleSheet_QLabel = """
                                                             QLabel {
                                                                 background-color:rgba(40, 52, 80, 1);
                                                                 border: 0px solid rgba(0, 180, 255, 60);
                                                                 border-radius: 2px;
                                                                 padding: 0px;
                                                                 font-size:13px;
                                                                 font-weight: bold;
                                                                 color:white;
                                                             }
                                                             QLabel:hover {
                                                                 background-color: rgba(255, 255, 255, 0.5);
                                                             }
                                                         """
        while self.QHBoxLayout_Process.count():
            item = self.QHBoxLayout_Process.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.QProgressBar_AudioProcessor = QtWidgets.QProgressBar()
        self.QProgressBar_AudioProcessor.setFixedHeight(13)
        self.QProgressBar_AudioProcessor.setRange(0, 100)  # 隐藏默认文本
        self.QProgressBar_AudioProcessor.setValue(10)
        self.QProgressBar_AudioProcessor.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self.QProgressBar_AudioProcessor.setStyleSheet("""
                            QProgressBar {
                                border: 1px solid #4cc9f0;
                                border-radius: 2px;
                                background-color: rgba(0, 0, 0, 0.1);
                                height: 3px;
                    
                            }
                            QProgressBar::chunk {
                                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4cc9f0, stop:1 #0077cc);
                                border-radius: 0px;
                            }
                        """)

        QLabel_Pause = Component_Common.Component_Common_QLabel_Click()
        QLabel_Pause.setStyleSheet(StyleSheet_QLabel)
        QLabel_Pause.setText("暂停")
        QLabel_Pause.setFixedSize(48, 18)

        QLabel_Stop = Component_Common.Component_Common_QLabel_Click()
        QLabel_Stop.setStyleSheet(StyleSheet_QLabel)
        QLabel_Stop.setText("取消")
        QLabel_Stop.setFixedSize(48, 18)
        QLabel_Stop.clicked.connect(self.AudioProcessor_Stop)


        self.QHBoxLayout_Process.addWidget(self.QProgressBar_AudioProcessor,1)
        self.QHBoxLayout_Process.addWidget(QLabel_Pause,0)
        self.QHBoxLayout_Process.addWidget(QLabel_Stop,0)












        # self.__AudioProcessor =AudioProcessor("","")
        # self.__AudioProcessor.Signals.Progress.connect(self.Update_Progress)
        # self.__AudioProcessor.start()

    def AudioProcessor_Stop(self):
        while self.QHBoxLayout_Process.count():
            item = self.QHBoxLayout_Process.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        QLabel_Start = Component_Common.Component_Common_QLabel_Click()
        QLabel_Start.setFixedSize(88, 30)
        QLabel_Start.setStyleSheet("QLabel{background-color: rgba(255, 255, 255, 0.6);border: 1px;color:white,font-size:13px;border-radius: 3px;}hover{ background - color: rgba(0, 100, 150, 150);}")
        QLabel_Start.setFont(QtGui.QFont("Microsoft YaHei", 13))
        QLabel_Start.setAlignment(QtCore.Qt.AlignCenter)

        QLabel_Start.setText("开始处理")
        QLabel_Start.clicked.connect(self.Start_AudioProcessor)
        self.QHBoxLayout_Process.addWidget(QLabel_Start)



    def AudioProcessor_Input_File(self):

        print('Signal')
        print("Open_LocalSource")
        File_Dialog = QtWidgets.QFileDialog()
        File_Path, _ = File_Dialog.getOpenFileName(
            self,
            "选择音视频文件",
            "",
            "音视频文件 (*.mp4 *.avi *.wav *.mp3 *.ogg *.flac *.aac *.m4a)"
        )

        if File_Path:
            self.Path_Audio = File_Path
            self.QLineEdit_Audio_Input.setText(File_Path)

            print(File_Path)

    def AudioProcessor_Output_Folder(self):
        Folder_Dialog = QtWidgets.QFileDialog()
        Folder_Path = Folder_Dialog.getExistingDirectory(
            self,
            "选择输出文件夹",  # 对话框标题
            "",  # 默认路径（空表示当前目录）
            QtWidgets.QFileDialog.ShowDirsOnly  # 只显示文件夹，不显示文件
        )

        if Folder_Path:
            self.Path_Audio_Folder = Folder_Path  # 存储文件夹路径
            self.QLineEdit_Audio_Output.setText(Folder_Path)  # 显示文件夹路径
            print(f"选择的文件夹路径: {Folder_Path}")


    def Update_Progress(self,Signal):
        print(Signal)


    def Dialog_Close(self):
        self.deleteLater()  # 安排销毁窗口
        self.close()  # 关闭窗口



class WorkerSignals(QtCore.QObject):
    """定义工作线程的信号"""
    Progress = QtCore.Signal(dict)
    finished = QtCore.Signal(str)
    error = QtCore.Signal(str)
    metrics_ready = QtCore.Signal(list)
    images_ready = QtCore.Signal(list)


class AudioProcessor(QtCore.QThread):
    """音频处理线程"""

    def __init__(self, input_file, output_dir):
        super().__init__()
        self.input_file = r"D:\M800001ziKgJ3o5Ipp.mp3"
        self.output_dir = r"D:\Data\Result"
        print("Loading")
        self.Signals = WorkerSignals()

    def run(self):
        print("Start")
        try:
            # 创建输出目录
            os.makedirs(self.output_dir, exist_ok=True)
            print("正在加载音频文件")
            # 加载原始音频
            self.Signals.Progress.emit({"Progress":10})
            # print("正在加载音频文件")
            audio = pydub.AudioSegment.from_file(self.input_file)
            sample_rate = audio.frame_rate

            # 转为单声道处理
            if audio.channels > 1:
                audio = audio.set_channels(1)
            print("正在加载音频文件")
            # 获取音频数据
            samples = np.array(audio.get_array_of_samples()).astype(np.float32)
            samples /= np.max(np.abs(samples))

            # 保存原始音频
            original_path = os.path.join(self.output_dir, "original.wav")
            self.save_audio(samples, sample_rate, original_path)

            # 存储所有处理结果
            processed_audio = [samples.copy()]
            labels = ["原始音频"]
            band_names = [
                "超低频(20-60Hz)", "低频(60-250Hz)", "中低频(250-500Hz)", "中频(500-1000Hz)",
                "中高频(1000-3000Hz)", "临场感(3000-6000Hz)", "明亮度(6000-12000Hz)", "空气感(12000-20000Hz)"
            ]

            # Step 1. 分别提升每个频段
            # for i in range(8):
            #     # self.signals.progress.emit(10 + i * 8, f"正在处理 {band_names[i]}...")
            #     processed = self.apply_band_eq(samples, sample_rate, i, gain_db=6.0)
            #     processed_audio.append(processed)
            #     labels.append(f"{band_names[i]} +6dB")
            #     output_path = os.path.join(self.output_dir, f"band_{i+1}_boosted.wav")
            #     self.save_audio(processed, sample_rate, output_path)

            # 2. 添加微笑曲线EQ
            # self.signals.progress.emit(80, "正在应用微笑曲线EQ...")
            # smile_curve = self.apply_smile_curve_eq(samples, sample_rate)
            # processed_audio.append(smile_curve)
            # labels.append("微笑曲线EQ")
            # self.save_audio(smile_curve, sample_rate, os.path.join(self.output_dir, "smile_curve_eq.wav"))
            #
            # # 3. 计算质量指标
            # self.signals.progress.emit(85, "正在计算音频指标...")
            # metrics = []
            # for i, data in enumerate(processed_audio):
            #     metrics.append({
            #         "label": labels[i],
            #         **self.calculate_audio_metrics(data, sample_rate)
            #     })
            # self.signals.metrics_ready.emit(metrics)
            #
            # # 4. 生成频谱分析图
            # self.signals.progress.emit(90, "正在生成频谱图...")
            # image_paths = []
            # for i, data in enumerate(processed_audio):
            #     plt.figure(figsize=(10, 6))
            #     plt.specgram(data, Fs=sample_rate, NFFT=4096, cmap='viridis')
            #     plt.ylim(20, 20000)
            #     plt.yscale('log')
            #     plt.colorbar(label='强度 (dB)')
            #     plt.title(labels[i])
            #     plt.xlabel("时间 (秒)")
            #     plt.ylabel("频率 (Hz)")
            #     img_path = os.path.join(self.output_dir, f"spectrum_{i}.png")
            #     plt.savefig(img_path)
            #     plt.close()
            #     image_paths.append(img_path)
            #
            # # 5. 生成频率响应对比图
            # plt.figure(figsize=(12, 8))
            # for i, data in enumerate(processed_audio):
            #     freqs = np.fft.rfftfreq(len(data), 1 / sample_rate)
            #     fft_result = np.fft.rfft(data)
            #     magnitudes = np.abs(fft_result)
            #     window_size = 50
            #     smoothed = np.convolve(magnitudes, np.ones(window_size) / window_size, mode='same')
            #     plt.plot(freqs, 20 * np.log10(smoothed), label=labels[i], alpha=0.8)
            #
            # plt.xscale('log')
            # plt.title('频率响应对比')
            # plt.xlabel('频率 (Hz)')
            # plt.ylabel('幅度 (dB)')
            # plt.grid(True, which="both", ls="-")
            # plt.legend()
            # plt.xlim(20, 20000)
            # freq_path = os.path.join(self.output_dir, "frequency_response_comparison.png")
            # plt.savefig(freq_path)
            # plt.close()
            # image_paths.append(freq_path)
            # self.signals.images_ready.emit(image_paths)
            #
            # # 6. 生成HTML报告
            # self.signals.progress.emit(95, "正在生成报告...")
            # self.generate_html_report(self.output_dir, metrics, os.path.basename(self.input_file))
            #
            # self.signals.progress.emit(100, "处理完成!")
            # self.signals.finished.emit(self.output_dir)

        except Exception as e:
            print(e)
            # self.signals.error.emit(str(e))

    def apply_band_eq(self, samples, sample_rate, band_idx, gain_db=6.0):
        """对特定频段应用EQ"""
        bands = [
            (20, 60), (60, 250), (250, 500), (500, 1000),
            (1000, 3000), (3000, 6000), (6000, 12000), (12000, 20000)
        ]

        low, high = bands[band_idx]
        b, a = butter(4, [low / (sample_rate / 2), high / (sample_rate / 2)], btype='band')
        band_data = filtfilt(b, a, samples)
        gain_linear = 10 ** (gain_db / 20)
        boosted_band = band_data * gain_linear
        return samples + (boosted_band - band_data)

    def apply_smile_curve_eq(self, samples, sample_rate):
        """应用微笑曲线EQ（提升低频和高频）"""
        bands = [
            (20, 60, 4.0), (60, 250, 3.0), (250, 500, -1.0), (500, 2000, 0),
            (2000, 4000, 0), (4000, 6000, 2.0), (6000, 12000, 3.0), (12000, 20000, 4.0)
        ]

        output = np.copy(samples)
        for low, high, gain_db in bands:
            b, a = butter(4, [low / (sample_rate / 2), high / (sample_rate / 2)], btype='band')
            band_data = filtfilt(b, a, samples)
            gain_linear = 10 ** (gain_db / 20)
            boosted_band = band_data * gain_linear
            output += (boosted_band - band_data)
        return output

    def save_audio(self, data, sample_rate, output_path):
        """保存音频文件"""
        data = data / np.max(np.abs(data)) * 0.99
        data_int16 = (data * 32767).astype(np.int16)
        audio = AudioSegment(
            data_int16.tobytes(),
            frame_rate=sample_rate,
            sample_width=2,
            channels=1
        )
        audio.export(output_path, format="wav")
    #
    # def calculate_audio_metrics(self, data, sample_rate):
    #     """计算音频质量指标"""
    #     metrics = {}
    #     meter = pyln.Meter(sample_rate)
    #     metrics['loudness'] = meter.integrated_loudness(data)
    #
    #     # 失真度计算
    #     def calculate_thd(signal):
    #         period = int(sample_rate / 1000)
    #         segment = signal[:period]
    #         fft_result = np.fft.rfft(segment)
    #         magnitudes = np.abs(fft_result)
    #         fundamental_idx = np.argmax(magnitudes[10:]) + 10
    #         fundamental = magnitudes[fundamental_idx]
    #         harmonics = magnitudes[fundamental_idx * 2:fundamental_idx * 6]
    #         thd = np.sqrt(np.sum(harmonics ** 2)) / fundamental
    #         return thd * 100
    #
    #     metrics['thd'] = calculate_thd(data)
    #     return metrics
    #
    # def generate_html_report(self, output_dir, metrics, filename):
    #     """生成HTML格式的分析报告"""
    #     img_grid = ""
    #     for i in range(len(metrics)):
    #         img_path = f"spectrum_{i}.png"
    #         img_grid += f'<div class="spectrum-item"><h3>{metrics[i]["label"]}</h3><img src="{img_path}" alt="{metrics[i]["label"]}频谱"></div>'
    #
    #     html = f"""<!DOCTYPE html>
    #     <html>
    #     <head>
    #         <title>EQ调音分析报告 - {filename}</title>
    #         <meta charset="utf-8">
    #         <style>
    #             /* 保持原有的CSS样式 */
    #             :root {{ --primary: #3498db; --secondary: #2c3e50; --light: #ecf0f1; --dark: #34495e; }}
    #             * {{ box-sizing: border-box; margin: 0; padding: 0; }}
    #             body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 20px; }}
    #             .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }}
    #             /* 其他样式保持不变... */
    #         </style>
    #     </head>
    #     <body>
    #         <div class="container">
    #             <header><h1>EQ调音分析报告</h1><div class="subtitle">音频文件: {filename}</div></header>
    #             <div class="content">
    #                 <div class="section"><h2>分析概览</h2><div class="summary-card"><p>本报告展示了同一音频文件在不同EQ设置下的处理效果对比...</div></div>
    #                 <div class="section"><h2>音频质量指标</h2><table><tr><th>EQ设置</th><th>动态范围 (LUFS)</th><th>失真度 (THD%)</th><th>评估</th></tr>"""
    #
    #     for item in metrics:
    #         loudness_eval = "✅ 正常" if abs(item['loudness']) > 20 else "⚠️ 偏低" if item['loudness'] > -30 else "⚠️ 偏高"
    #         thd_eval = "✅ 优秀" if item['thd'] < 0.1 else "⚠️ 良好" if item['thd'] < 0.5 else "❌ 失真严重"
    #         html += f"""<tr><td>{item['label']}</td><td>{item['loudness']:.2f}</td><td>{item['thd']:.4f}</td><td>{loudness_eval} | {thd_eval}</td></tr>"""
    #
    #     html += f"""</table>
    #                 <div class="metrics-grid">
    #                     <div class="metric-card"><h3>动态范围 (LUFS)</h3><div class="metric-value">{metrics[0]['loudness']:.2f}</div><p>目标范围: -14 到 -10 LUFS (音乐)</p></div>
    #                     <div class="metric-card"><h3>平均失真度 (THD%)</h3><div class="metric-value">{np.mean([m['thd'] for m in metrics]):.4f}</div><p>&lt; 0.1%: 专业质量</p></div>
    #                     <div class="metric-card"><h3>处理文件数</h3><div class="metric-value">{len(metrics)}</div><p>包含原始音频 + 9种EQ处理</p></div>
    #                 </div></div>
    #                 <div class="section"><h2>频率响应对比</h2><img src="frequency_response_comparison.png" alt="频率响应对比" class="full-width-img"></div>
    #                 <div class="section"><h2>频谱分析</h2><div class="spectrum-grid">{img_grid}</div></div>
    #             </div>
    #             <footer><p>EQ调音分析报告 | 生成时间: {np.datetime64('now')}</p></footer>
    #         </div>
    #     </body>
    #     </html>"""
    #
    #     with open(os.path.join(output_dir, "report.html"), "w", encoding="utf-8") as f:
    #         f.write(html)
