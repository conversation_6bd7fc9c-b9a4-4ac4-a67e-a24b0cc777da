﻿

class Service_Requests {
    constructor(Type,Data){
        this.Type = Type
        this.Data = Data
    };

    callMethod(){
        this.CONFIG();
        try {
            return eval(`this.${this.Type}()`);
        } catch (Error) {
            return {'Error': Error, "Type": this.Type, "Data": this.Data, "Status": "Failed"};
        }
    };
    
    /****************************************异步请求***************************************************/
    async Async(){
        this.Result = await this.Request_Interface();
        return this.Result;
    };
    Request_Interface() {
        const Service_Axios_Options = {
            type: 'POST',  // 请求类型
            dataType: 'json', // 期望的响应数据类型
            contentType: 'application/json', // 设置上传内容类型为 JSON
            async: true, // 是否异步请求 false 同步 true 异步
            data: JSON.stringify(this.Data)
        };
        return $.ajax($.extend({}, Service_Axios_Options, { url:`https://${window.location.host}/Service_Interface`}))
            .then(successResponse => {
                // 请求成功，处理响应
                console.log('Request_Interface 请求成功:', successResponse);
                return successResponse;
            })
            .catch(error => {
                // 请求失败，处理错误
                console.error('Request_Interface 请求失败:', error);
                throw error; // 抛出错误以便调用者可以捕获
            });
    };
    /****************************************同步请求,界面所有操作堵塞，堵塞中的操作还是会执行***************************************************/
    Sync(){
        var Response_Data;
        $.ajax({
            type: "POST",
            url: `https://${window.location.host}/Service_Interface`,
            dataType: 'json', // 期望的响应数据类型
            // 发送同步请求
            async: false,
            contentType: 'application/json',
            processData: false,
            data: JSON.stringify(this.Data),
            success: function (Response) {
                console.log('Sync同步 success Response:',Response);
                Response_Data = Response;
            },
            error: function(jqXHR, textStatus, errorThrown) { 
                console.log('Sync同步 error Respones:',textStatus);
                Response_Data =  {'Status': 'Failed'};
            }
        });
        return Response_Data;
    };

    CONFIG(){
        this.Result = {'Status': 'Failed'};
    };
};