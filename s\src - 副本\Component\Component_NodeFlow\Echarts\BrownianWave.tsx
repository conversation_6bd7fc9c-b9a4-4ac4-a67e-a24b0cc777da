import React, { useEffect, useRef, useCallback } from 'react';

interface BrownianWaveProps {
  width?: number;
  height?: number;
  color?: string;
  speed?: number;        // 移动速度 (0.1 = 很慢)
  waveHeight?: number;   // 波峰波谷高度
  randomness?: number;   // 波动随机性
  lineWidth?: number;
}

const BrownianWave: React.FC<BrownianWaveProps> = ({
  width = 800,
  height = 200,
  color = '#4fd1c5',
  speed = 3,          // 默认很慢的速度
  waveHeight = 50,
  randomness = 30,
  lineWidth = 2,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const offsetRef = useRef<number>(0);
  const pointsRef = useRef<{x: number, y: number}[]>([]);
  const lastTimeRef = useRef<number>(0);

  // 初始化点数组
  const initializePoints = useCallback(() => {
    const pointCount = Math.ceil(width / 5); // 更高密度点
    pointsRef.current = Array(pointCount).fill(0).map((_, i) => ({
      x: i * (width / pointCount),
      y: height / 2 + (Math.random() - 0.5) * waveHeight
    }));
  }, [width, height, waveHeight]);

  // 更新点位置 - 新实现方式
  const updatePoints = useCallback((deltaTime: number) => {
    const moveAmount = speed * deltaTime * 0.1; // 非常慢的移动
    
    // 1. 所有点向左移动
    pointsRef.current.forEach(point => {
      point.x -= moveAmount;
    });
    
    // 2. 移除屏幕外的点
    pointsRef.current = pointsRef.current.filter(point => point.x > -10);
    
    // 3. 添加新的右侧点
    const lastX = pointsRef.current[pointsRef.current.length - 1]?.x || width;
    if (lastX < width + 10) {
      const newX = lastX + (width / 50);
      const baseY = height / 2;
      const randomY = (Math.random() - 0.5) * randomness;
      const newY = Math.max(
        baseY - waveHeight,
        Math.min(baseY + waveHeight, baseY + randomY)
      );
      pointsRef.current.push({ x: newX, y: newY });
    }
    
    // 4. 添加一些随机波动
    pointsRef.current.forEach(point => {
      if (Math.random() < 0.02) { // 2%的几率微调点位置
        point.y += (Math.random() - 0.5) * 5;
        point.y = Math.max(height / 2 - waveHeight, Math.min(height / 2 + waveHeight, point.y));
      }
    });
    
    offsetRef.current += moveAmount;
  }, [width, height, speed, randomness, waveHeight]);

  // 绘制波形
  const drawWave = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.clearRect(0, 0, width, height);
    
    // 绘制波形线
    ctx.beginPath();
    ctx.lineWidth = lineWidth;
    ctx.strokeStyle = color;
    
    const points = pointsRef.current;
    if (points.length > 0) {
      ctx.moveTo(points[0].x, points[0].y);
      
      for (let i = 1; i < points.length; i++) {
        const x = points[i].x;
        const y = points[i].y;
        const cpX = (x + points[i-1].x) / 2;
        const cpY = (y + points[i-1].y) / 2;
        ctx.quadraticCurveTo(cpX, cpY, x, y);
      }
    }
    
    ctx.stroke();
    
    // 渐变填充
    ctx.globalCompositeOperation = 'destination-over';
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, `${color}30`);
    gradient.addColorStop(1, `${color}00`);
    ctx.fillStyle = gradient;
    ctx.lineTo(points[points.length-1]?.x || width, height);
    ctx.lineTo(0, height);
    ctx.closePath();
    ctx.fill();
    ctx.globalCompositeOperation = 'source-over';
  }, [width, height, color, lineWidth]);

  // 动画循环 - 使用时间差控制速度
  const animate = useCallback((timestamp: number) => {
    if (!lastTimeRef.current) lastTimeRef.current = timestamp;
    const deltaTime = timestamp - lastTimeRef.current;
    lastTimeRef.current = timestamp;
    
    updatePoints(deltaTime);
    drawWave();
    animationRef.current = requestAnimationFrame(animate);
  }, [updatePoints, drawWave]);

  useEffect(() => {
    initializePoints();
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [initializePoints, animate]);

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      style={{ display: 'block', background: 'transparent' }}
    />
  );
};

export default BrownianWave;