#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块发现和诊断脚本
自动发现项目中的所有自定义模块，生成正确的 PyInstaller 配置
"""

import os
import sys
import ast
import importlib.util
from pathlib import Path


def analyze_imports(file_path):
    """分析 Python 文件中的导入语句"""
    imports = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析 AST
        tree = ast.parse(content)

        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    base_module = node.module
                    for alias in node.names:
                        if alias.name == '*':
                            imports.append(base_module)
                        else:
                            imports.append(f"{base_module}.{alias.name}")
                else:
                    # 相对导入
                    for alias in node.names:
                        imports.append(alias.name)
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")

    return imports


def discover_project_modules(project_root):
    """发现项目中的所有模块"""
    project_path = Path(project_root)
    modules = {}
    all_imports = set()

    print(f"扫描项目目录: {project_root}")

    for py_file in project_path.rglob("*.py"):
        if '__pycache__' in str(py_file):
            continue

        print(f"分析文件: {py_file}")

        # 计算模块路径
        rel_path = py_file.relative_to(project_path)
        if rel_path.name == '__init__.py':
            module_parts = rel_path.parts[:-1]
        else:
            module_parts = rel_path.parts[:-1] + (rel_path.stem,)

        if module_parts:
            module_name = '.'.join(module_parts)
            modules[module_name] = py_file
            print(f"  发现模块: {module_name}")

        # 分析导入
        imports = analyze_imports(py_file)
        all_imports.update(imports)

    return modules, all_imports


def check_module_exists(module_name):
    """检查模块是否存在"""
    try:
        spec = importlib.util.find_spec(module_name)
        return spec is not None
    except (ImportError, ModuleNotFoundError, ValueError):
        return False


def generate_pyinstaller_config(project_root, modules, all_imports):
    """生成 PyInstaller 配置"""
    print("\n=== 生成 PyInstaller 配置 ===")

    # 分类导入
    custom_modules = []
    missing_modules = []
    system_modules = []

    for module_name in sorted(all_imports):
        if module_name.startswith(('Bin', 'Service_')):
            custom_modules.append(module_name)
        elif not check_module_exists(module_name):
            missing_modules.append(module_name)
        else:
            system_modules.append(module_name)

    # 添加发现的项目模块
    project_modules = list(modules.keys())

    print(f"\n发现的项目模块 ({len(project_modules)}):")
    for module in sorted(project_modules):
        print(f"  - {module}")

    print(f"\n自定义模块导入 ({len(custom_modules)}):")
    for module in sorted(custom_modules):
        print(f"  - {module}")

    print(f"\n可能缺失的模块 ({len(missing_modules)}):")
    for module in sorted(missing_modules):
        print(f"  - {module}")

    # 生成命令行参数
    hidden_imports = []
    hidden_imports.extend(project_modules)
    hidden_imports.extend(custom_modules)

    # 尝试添加可能的模块变体
    for module in custom_modules:
        if not module.startswith('Bin.'):
            hidden_imports.append(f"Bin.Utils.{module}")

    # 去重
    hidden_imports = sorted(set(hidden_imports))

    print(f"\n建议的 --hidden-import 参数:")
    for module in hidden_imports:
        print(f"--hidden-import {module} ^")

    return {
        'project_modules': project_modules,
        'custom_modules': custom_modules,
        'missing_modules': missing_modules,
        'hidden_imports': hidden_imports
    }


def create_fixed_spec_file(project_root, config):
    """创建修复后的 spec 文件"""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
import sys
import os
import site
import pkg_resources

block_cipher = None

# 项目根目录
project_root = r'{project_root}'

# 获取编码路径
encoding_paths = []
possible_paths = [
    os.path.join(sys.prefix, 'Lib', 'encodings'),
    os.path.join(sys.base_prefix, 'Lib', 'encodings'),
    os.path.join(sys.executable.replace('python.exe', ''), 'Lib', 'encodings'),
]

for path in site.getsitepackages():
    possible_paths.append(os.path.join(path, 'encodings'))

for path in possible_paths:
    if os.path.exists(path):
        encoding_paths.append((path, 'encodings'))
        print(f"找到编码路径: {{path}}")
        break

# 构建数据文件列表
datas_list = encoding_paths.copy()

# 添加你的Bin模块和Utils模块
bin_path = os.path.join(project_root, 'Bin')
if os.path.exists(bin_path):
    for root, dirs, files in os.walk(bin_path):
        for file in files:
            if file.endswith(('.py', '.pyd', '.dll', '.so')):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_root)
                target_dir = os.path.dirname(rel_path)
                datas_list.append((file_path, target_dir))
                print(f"添加文件: {{file_path}} -> {{target_dir}}")
    pathex=[

print(f"总共添加了 {{len(datas_list)}} 个数据文件")

a = Analysis(
    ['Windows_Anlysis.py'],
        '.',
        project_root,
        os.path.join(project_root, 'Bin'),
        os.path.join(project_root, 'Bin', 'Utils'),
    ],
    binaries=[],
    datas=datas_list,
    hiddenimports=[
        # 编码相关
        'encodings',
        'encodings.aliases',
        'encodings.utf_8',
        'encodings.ascii',
        'encodings.latin_1',
        'encodings.cp1252',
        'encodings.mbcs',

        # 发现的模块
'''

    # 添加所有发现的模块
    for module in config['hidden_imports']:
        spec_content += f"        '{module}',\n"

    spec_content += '''        
        # 加密相关模块
        'gmssl',
        'gmssl.sm2',
        'gmssl.sm3',
        'gmssl.sm4',
        'gmssl.func',
        'gmssl.sm9',
        'cryptography',
        'cryptography.hazmat',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.backends',
        'cryptography.hazmat.backends.openssl',

        # PySide6相关
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'PySide6.QtXml',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
    cipher=block_cipher,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Windows_Anlysis',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Logo.ico',
)
'''

    # 保存 spec 文件
    with open('Windows_Anlysis_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print(f"\\n✓ 创建修复后的 spec 文件: Windows_Anlysis_fixed.spec")


def main():
    """主函数"""
    print("=== 模块发现和诊断工具 ===")
    print("自动发现项目中的所有模块并生成 PyInstaller 配置\\n")

    project_root = r"D:\\Sentinel_Foundation"

    if not os.path.exists(project_root):
        print(f"错误: 项目根目录不存在: {project_root}")
        print("请修改 project_root 变量为正确的路径")
        return

    # 发现模块
    modules, all_imports = discover_project_modules(project_root)

    # 生成配置
    config = generate_pyinstaller_config(project_root, modules, all_imports)

    # 创建修复后的 spec 文件
    create_fixed_spec_file(project_root, config)

    print(f"\\n=== 建议的解决步骤 ===")
    print("1. 使用生成的 spec 文件重新打包:")
    print("   pyinstaller Windows_Anlysis_fixed.spec")
    print("\\n2. 或者使用以下命令行参数:")
    print("   pyinstaller --onefile --console --clean \\")
    for module in config['hidden_imports']:
        print(f"   --hidden-import {module} \\")
    print("   --collect-submodules Bin.Utils \\")
    print("   --collect-all gmssl \\")
    print("   Windows_Anlysis.py")

    if config['missing_modules']:
        print(f"\\n⚠️  注意: 发现 {len(config['missing_modules'])} 个可能缺失的模块")
        print("如果打包后仍有错误，请检查这些模块是否需要安装或添加到 hidden-imports 中")


if __name__ == "__main__":
    main()