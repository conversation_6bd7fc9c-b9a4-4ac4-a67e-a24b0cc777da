# -*- coding: utf-8 -*-
import time,os,sys
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
class PopupDialog_Test(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("提示")
        self.setFixedSize(348, 170)
        self.setStyleSheet("background:white")
        QVBoxLayout_Main = QtWidgets.QVBoxLayout()

        QLabel_Config_Copyright = QtWidgets.QLabel()
        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)
        QLabel_Config_Copyright.setStyleSheet('background:rgba(38, 38, 38, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;')

        QLabel_Config_Copyright.setText("抱歉，您是测试用户无法使用该功能。")

        QLabel_Config_Policy = QtWidgets.QLabel()
        # QLabel_Config_Policy.setMinimumSize(250, 80)
        # QLabel_Config_Policy.setMaximumSize(250, 80)
        QLabel_Config_Policy.setStyleSheet('background:rgba(38, 38, 38, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;')
        QLabel_Config_Policy.setText("请联系客服升级正式用户。")

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策
        QVBoxLayout_Main.addWidget(QLabel_Config_Copyright,alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(QLabel_Config_Policy,alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)


        self.setLayout(QVBoxLayout_Self)

class PopupDialog_About(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("关于哨兵")
        self.setFixedSize(380, 80)
        self.setStyleSheet("background:white")
        QVBoxLayout_Main = QtWidgets.QVBoxLayout()

        QLabel_Config_Copyright = QtWidgets.QLabel()
        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)
        QLabel_Config_Copyright.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        QLabel_Config_Copyright.setText("Copyright @2018-2025 Sentinel All rights reserved")

        QLabel_Config_Policy = QtWidgets.QLabel()
        # QLabel_Config_Policy.setMinimumSize(250, 80)
        # QLabel_Config_Policy.setMaximumSize(250, 80)
        QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        QLabel_Config_Policy.setText("Version 0.98.1|许可协议|保密政策")

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策
        QVBoxLayout_Main.addWidget(QLabel_Config_Copyright, alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(QLabel_Config_Policy, alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)

        self.setLayout(QVBoxLayout_Self)


class PopupDialog_Login_Status(QtWidgets.QDialog):
    def __init__(self,parent=None,*args ):
        super().__init__(parent)
        Message = args[0]
        self.setWindowTitle("提示")
        self.setFixedSize(380,80)
        self.setStyleSheet("background:white")
        QVBoxLayout_Main = QtWidgets.QVBoxLayout()

        Font = QtGui.QFont()
        Font.setPointSize(10)  # 设置字体大小为 16 点
        QLabel_Config_Copyright = QtWidgets.QLabel()
        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)

        if "成功" in Message["Title"]:
            Color ="blue"
        else:
            Color = "black"
        QLabel_Config_Copyright.setStyleSheet(f'background:#transparent;border:0px solid #002040;font-size:6px;color:{Color}')
        QLabel_Config_Copyright.setText(Message["Title"])
        QLabel_Config_Copyright.setFont(Font)

        QLabel_Config_Policy = QtWidgets.QLabel()
        # QLabel_Config_Policy.setMinimumSize(250, 80)
        # QLabel_Config_Policy.setMaximumSize(250, 80)
        QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        QLabel_Config_Policy.setText(Message["Content"])

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策
        QVBoxLayout_Main.addWidget(QLabel_Config_Copyright,alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(QLabel_Config_Policy,alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)

        self.setLayout(QVBoxLayout_Self)


class PopupDialog_Update(QtWidgets.QDialog):
    Signal_Command = QtCore.Signal(dict)

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.Page_Info = args[0]
        self.setWindowTitle("版本更新")
        self.setFixedSize(380, 80)
        self.setStyleSheet("background:white")
        # QVBoxLayout_Main = QtWidgets.QVBoxLayout(self)

        QVBoxLayout_Main = QtWidgets.QVBoxLayout()


        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)

        # if "成功" in Message["Title"]:
        #     Color = "blue"
        # else:
        #     Color = "black"

        Font_Update = QtGui.QFont()
        Font_Update.setPointSize(8)  # 设置字体大小为 16 点QLabel::hover {background-color:red}
        # self.QPushButton_Update = QPushButton("自动部署/更新", self)
        self.QPushButton_Update = QtWidgets.QPushButton("立即更新", self)
        self.QPushButton_Update.setFont(Font_Update)
        self.QPushButton_Update.setGeometry(198, 120, 108, 36)
        self.QPushButton_Update.setStyleSheet(
            '''QPushButton {background:rgba(25,25,112,0.6);border: none;border-radius: 3px;color:white} QPushButton:hover{background-color: rgba(220,120,160,0.3);border: 0px}''')
        self.QPushButton_Update.setIcon(qta.icon('fa.cloud-download', color=('blue', 70)))
        self.QPushButton_Update.setIconSize(QtCore.QSize(28, 20))  # 设置图标大小
        self.QPushButton_Update.clicked.connect(self.UPDATE_SERVICE)
        self.QPushButton_Update.hide()


        Font_Update = QtGui.QFont()
        Font_Update.setPointSize(8)  # 设置字体大小为 16 点QLabel::hover {background-color:red}
        # self.QPushButton_Update = QPushButton("自动部署/更新", self)
        self.QPushButton_Update_Check = QtWidgets.QPushButton("检查更新", self)
        self.QPushButton_Update_Check.setFont(Font_Update)
        self.QPushButton_Update_Check.setGeometry(198, 120, 108, 36)
        self.QPushButton_Update_Check.setStyleSheet('''QPushButton {background:rgba(25,25,112,0.6);border: none;border-radius: 3px;color:white} QPushButton:hover{background-color: rgba(220,120,160,0.3);border: 0px}''')
        self.QPushButton_Update_Check.setIcon(qta.icon('mdi6.cloud-refresh-variant', color=('white', 70)))
        self.QPushButton_Update_Check.setIconSize(QtCore.QSize(28, 20))  # 设置图标大小
        self.QPushButton_Update_Check.clicked.connect(self.UPDATE_CHECK_SERVICE)

        Font = QtGui.QFont()
        Font.setPointSize(8)  # 设置字体大小为 16 点
        self.QLabel_Config_Copyright = QtWidgets.QLabel()
        self.QLabel_Config_Copyright.setStyleSheet(f'background:#transparent;border:0px solid #002040;font-size:6px;color:blue')
        self.QLabel_Config_Copyright.setText(f"当前版本 Version {self.Page_Info['Version']}")
        self.QLabel_Config_Copyright.setFont(Font)

        # ------------------ 创建登录进度条
        self.QProgressBar_Update = QtWidgets.QProgressBar(self)
        self.QProgressBar_Update.setMinimumSize(250, 3)
        self.QProgressBar_Update.setMaximumSize(250, 3)
        # 设置样式表以隐藏文本并改变颜色
        self.QProgressBar_Update.setStyleSheet("""  
                                         QProgressBar {  
                                             border: 0px solid grey;  
                                             border-radius: 3px;  
                                             text-align: center;  
                                             color: transparent; /* 隐藏文本 */  
                                             background-color: transparent; /* 背景色 */  
                                         }  
                                         QProgressBar::chunk {  
                                             background-color: rgba(135,0,255,0.6); /* 已填充部分的颜色 */  
                                         }  
                                     """)
        self.QProgressBar_Update.setGeometry(10, 198, 318, 2)
        # self.QProgressBar_Update.setValue(30)
        self.QProgressBar_Update.setMinimum(0)
        self.QProgressBar_Update.setMaximum(0)
        self.QProgressBar_Update.hide()



        #
        # QLabel_Config_Policy = QtWidgets.QLabel()
        # # QLabel_Config_Policy.setMinimumSize(250, 80)
        # # QLabel_Config_Policy.setMaximumSize(250, 80)
        # QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Config_Policy.setText(Message["Content"])

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策

        QVBoxLayout_Main.addWidget( self.QPushButton_Update, alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget( self.QPushButton_Update_Check, alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(self.QLabel_Config_Copyright, alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(self.QProgressBar_Update, alignment=QtCore.Qt.AlignCenter)


        # QVBoxLayout_Main.addWidget(QLabel_Config_Policy, alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)

        self.setLayout(QVBoxLayout_Self)















        # 添加消息标签
        # message_label = QtWidgets.QLabel(message, self)
        # layout.addWidget(message_label)

        # 添加两个按钮：确认和取消
        # confirm_button = QtWidgets.QPushButton("确认", self)
        # cancel_button = QtWidgets.QPushButton("取消", self)
        #
        # # 连接确认按钮的点击信号到自定义的confirmed信号
        # confirm_button.clicked.connect(lambda :self.COMMAND_SERVICE({"Command":"COMMAND_SERVICE","Content":"Software_Update"}))
        #
        # # 你可以为取消按钮添加一个槽函数，例如关闭对话框
        # cancel_button.clicked.connect(self.reject)
        #
        # # 将按钮添加到布局中
        # QVBoxLayout_Main.addWidget(confirm_button)
        # QVBoxLayout_Main.addWidget(cancel_button)




        #
        # Font = QtGui.QFont()
        # Font.setPointSize(10)  # 设置字体大小为 16 点
        # QLabel_Config_Copyright = QtWidgets.QLabel()
        # # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # # QLabel_Config_Copyright.setMaximumSize(250, 80)
        #
        # if "成功" in Message["Title"]:
        #     Color = "blue"
        # else:
        #     Color = "black"
        # QLabel_Config_Copyright.setStyleSheet(
        #     f'background:#transparent;border:0px solid #002040;font-size:6px;color:{Color}')
        # QLabel_Config_Copyright.setText(Message["Title"])
        # QLabel_Config_Copyright.setFont(Font)
        #
        # QLabel_Config_Policy = QtWidgets.QLabel()
        # # QLabel_Config_Policy.setMinimumSize(250, 80)
        # # QLabel_Config_Policy.setMaximumSize(250, 80)
        # QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Config_Policy.setText(Message["Content"])
        #
        # # Copyright @2018-2024 Sentinel All rights reserved
        # # 许可协议 | 保密政策
        # QVBoxLayout_Main.addWidget(QLabel_Config_Copyright, alignment=QtCore.Qt.AlignCenter)
        # QVBoxLayout_Main.addWidget(QLabel_Config_Policy, alignment=QtCore.Qt.AlignCenter)
        #
        # QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        # QVBoxLayout_Self.addStretch(1)
        # QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        # QVBoxLayout_Self.addStretch(1)
        #
        # self.setLayout(QVBoxLayout_Self)
    #
    # def Update_Progress_Update(self):
    #     # global Page_Info
    #     # 更新进度条的值
    #     value = self.QProgressBar_Update.value()
    #     if value < 100:
    #         self.QProgressBar_Update.setValue(value + 1)
    #     else:
    #         # 进度完成后停止定时器
    #         self.QTimer_Logout.stop()
    #         self.QProgressBar_Logout.hide()
    #         try:
    #             self.QLineEdit_Login_Name.setText("")
    #             self.QLineEdit_Login_Password.setText("")
    #         except:
    #             pass
    #         screen_pos = self.mapToGlobal(self.rect().center())
    #         popup = Utils_PopupDialog.PopupDialog_Login_Status(self, {"Title": "信息完全清除!", "Content": "请稍后重新登录"})
    #         popup.resize(100, 80)
    #         # 设置弹出窗口的位置
    #         popup.move(screen_pos - QtCore.QPoint(popup.width() // 2, popup.height() // 2 + 20))
    #         popup.show()
    #         #
    #         self.COMMAND_SERVICE({"Command": "Page_Element_Hide", "Element": "QPushButton_ReBack"})

    def UPDATE_CHECK_SERVICE(self):

        self.QProgressBar_Update.show()

        self.Timer_Update = QtCore.QTimer()
        self.Timer_Update.timeout.connect(self.UPDATE_STATUS)

        #
        # PP(Command)
        self.Timer_Update.start(5000)

        # time.sleep(3)


        # for in i()


    def UPDATE_STATUS(self):
        Status ="Failed"
        Version="1.28.1"
        Could_Version = "1.28.1"
        if Version == Could_Version:
            pass
        else:
            Status = "Success"
        self.QLabel_Config_Copyright.setText(f"本地 Version {self.Page_Info['Version']}，云服务器 Version {Could_Version}")
        self.QPushButton_Update_Check.hide()
        self.QPushButton_Update.show()
        self.QProgressBar_Update.hide()
        return Status
    def UPDATE_SERVICE(self):

        # 使用 Popen 异步启动外部程序
        # 注意替换 'your_external_program.exe' 为你的程序路径
        self.external_process = Popen([f"{self.Page_Info['Sentinel_Path']}/Sentinel Foundation/bin/SentinelSetup.exe"], shell=True)
        print("外部程序已启动")

        self.Signal_Command.emit({"Command":"COMMAND_SERVICE","Content":"Close"})
        self.reject()


    def COMMAND_SERVICE(self,Command):
        self.Signal_Command.emit(Command)
        self.reject()



class PopupDialog(QtWidgets.QDialog):
    def __init__(self, parent=None,*args):
        super().__init__(parent)
        self.Title   = args[0]["Title"]
        self.Message_Name = args[0]["Message_Name"]
        try:
            self.self.Message_Content = args[0]["Message_Content"]
        except:
            self.Message_Content = ""
        self.setWindowTitle(self.Title)
        self.setFixedSize(380, 80)
        self.setStyleSheet("background:white")
        QVBoxLayout_Main = QtWidgets.QVBoxLayout()

        QLabel_Config_Copyright = QtWidgets.QLabel()
        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)
        QLabel_Config_Copyright.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        QLabel_Config_Copyright.setText(self.Message_Name)

        QLabel_Config_Policy = QtWidgets.QLabel()
        # QLabel_Config_Policy.setMinimumSize(250, 80)
        # QLabel_Config_Policy.setMaximumSize(250, 80)
        QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        QLabel_Config_Policy.setText(self.Message_Content)

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策
        QVBoxLayout_Main.addWidget(QLabel_Config_Copyright, alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(QLabel_Config_Policy, alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)

        self.setLayout(QVBoxLayout_Self)