# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
# from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.Utils.UtilsCenter import *


Page_Info={
    "Title":"哨兵核心服务配置",
    "Param": {},

}
PP(Page_Info)
# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_SentinelServer(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass
        self.initUI()

    def initUI(self):
        self.Set_Title()




    def Set_Title(self):
        Icon = qta.icon('ph.caret-left-light', scale_factor=1, color=('black'), color_active='black')
        Button_Back = QtWidgets.QPushButton(self)
        Button_Back.setIconSize(QtCore.QSize(25, 25))
        Button_Back.setStyleSheet("background:transparent")
        Button_Back.setIcon(Icon)
        Button_Back.setGeometry(QtCore.QRect(16, 38, 30, 30))
        Button_Back.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE({"Command": "Page_Change", "Page_Name": "Home"}))

        Label_Title = QtWidgets.QLabel(self)
        Label_Title.setStyleSheet("background:transparent")
        Label_Title.setFont(QtGui.QFont("Microsoft YaHei", 13))
        Label_Title.setGeometry(QtCore.QRect(36, 12, 280, 80))
        Label_Title.setText(Page_Info['Title'])


    def Set_Content(self):
        pass
    def Set_Bottom(self):
        pass

    def PAGE_HANDLER_EXECUTE(self, *args):
        try:
            Command = args[0]
            Command["Page_Param"] = Page_Info["Param"]
        except:pass
        self.Signal_Result.emit(args[0])  # 发送信号并传递数据



class Page_Mission_Execute(QtCore.QThread):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.CONFIG()
        self.Type = args[0]

    def run(self):
        # PP("Page_Update_Status",9)
        try:
            Method = getattr(self, f'{self.Type}', None)
            if Method and callable(Method):
                return Method()
            else:
                return f"{self.__class__.__name__} No Such Method: {self.Type}"
        except Exception as e:
            self.Signal_Result.emit({"Error": e})

    def Worker_Info(self):
        try:
            self.CacheStack_Data = {}
            self.CacheStack_Data['Method'] = "Key_Get"
            self.CacheStack_Data['Data'] = ["Worker_Info"]
            Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
            Worker_Info = json.loads(Response.text)
            # PJ(Worker_Info)
            self.Signal_Result.emit({"Worker_Info": Worker_Info})
        except:
            PT()
            self.Signal_Result.emit({"Worker_Info": "Failed"})




        pass

    def Mission_Update_Status(self):
        PP("Mission_Update_Status", 3)
        # PJ(CServer())

        # Server_Status_Info  =
        self.Result["Server_Status_Info"]    = CServer()
        self.Result["Status"]                = "Success"
        self.Result["Command"]               = "Page_Update"
        self.Signal_Result.emit(self.Result)
        # return self.Result




    def CONFIG(self):
        self.Result = {'Status': 'Failed'}








            # Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            # # PI(eval(Response.text))
            # Sentinel_Service = eval(Response.text)
            # if Sentinel_Service["Status"] == "Active":
            #     Page_Info["Sentinel_Service"] = "Success"

        #
        # try:
        #     self.CacheStack_Data = {}
        #     self.CacheStack_Data['Method'] = "Key_Get"
        #     self.CacheStack_Data['Data'] = ["System_Status"]
        #     Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
        #     System_Status = json.loads(Response.text)
        #     PJ(System_Status)
        #     self.Signal_Result.emit({"System_Status": System_Status})
        # except:
        #     PT()
        #     self.Signal_Result.emit({"System_Status": "Failed"})



            # Page_Info["Sentinel_Service"] = "Failed"






        # global Page_Info
        # while True:
        #     time.sleep(10)
        #     PP("Core Update")

            # self.Proxies =
            # Response
            # try:
            #     Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            #     # PI(eval(Response.text))
            #     Sentinel_Service = eval(Response.text)
            #     if Sentinel_Service["Status"] == "Active":
            #         Page_Info["Sentinel_Service"] = "Success"
            # except:
            #     Page_Info["Sentinel_Service"] = "Failed"

            # System_Status =  SCKeYGet({"Key":"System_Status"})
            # PJ(System_Status)
            # try:
            #     pass
            #     # Page_Info["System_Status"] = SCKey_Get("System_Status")
            # except:
            #     PT()

            # System_Status









if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Home()
    Page_Widget.show()
    sys.exit(app.exec())
