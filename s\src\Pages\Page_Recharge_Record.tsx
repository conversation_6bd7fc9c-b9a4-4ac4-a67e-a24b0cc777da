import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  DatePicker, 
  Space, 
  message,
  Tag,
  Typography
} from 'antd';
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import dayjs from 'dayjs';
import '../Styles/Page_Recharge_Record.css';

const { RangePicker } = DatePicker;
const { Text } = Typography;

// 数据类型定义
interface RechargeRecordData {
  id: string;
  LOG_TIME: string;           // 充值时间
  POINT: number;              // 充值金额
  STATUS: string;             // 订单状态
  DESCRIBE: string;           // 描述
  TRANSACTION_ID?: string;    // 交易ID
  PAYMENT_METHOD?: string;    // 支付方式
}

const Page_Recharge_Record: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [recordData, setRecordData] = useState<RechargeRecordData[]>([]);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 状态映射
  const statusMap: { [key: string]: string } = {
    'Finish': '已完成',
    'Active': '执行中',
    'Recharge': '已退款',
    'Failed': '异常中',
    'Execute': '执行中',
    'API': '执行中'
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Finish': return 'success';
      case 'Active': return 'processing';
      case 'Execute': return 'processing';
      case 'API': return 'processing';
      case 'Recharge': return 'warning';
      case 'Failed': return 'error';
      default: return 'default';
    }
  };

  // 初始化数据
  useEffect(() => {
    // 设置默认日期范围为本月
    const today = dayjs();
    const startOfMonth = today.startOf('month');
    const endOfMonth = today.endOf('month');
    setDateRange([startOfMonth, endOfMonth]);
    
    loadRecordData();
  }, []);

  // 加载充值记录数据
  const loadRecordData = async (startDate?: string, endDate?: string) => {
    setLoading(true);
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'User',
      //   data_type: 'Service',
      //   data_methods: 'return_history_pay',
      //   data_argument: '{}',
      //   data_kwargs: { start_date: startDate, end_date: endDate }
      // };
      // const response = await serviceRequests.Async(requestData);
      // setRecordData(response || []);

      // 模拟数据
      const mockData: RechargeRecordData[] = [
        {
          id: '1',
          LOG_TIME: '2024-01-15 10:30:00',
          POINT: 100,
          STATUS: 'Finish',
          DESCRIBE: '支付宝充值',
          TRANSACTION_ID: 'TXN20240115001',
          PAYMENT_METHOD: '支付宝'
        },
        {
          id: '2',
          LOG_TIME: '2024-01-14 14:20:00',
          POINT: 200,
          STATUS: 'Finish',
          DESCRIBE: '微信支付充值',
          TRANSACTION_ID: 'TXN20240114001',
          PAYMENT_METHOD: '微信支付'
        },
        {
          id: '3',
          LOG_TIME: '2024-01-13 09:15:00',
          POINT: 50,
          STATUS: 'Failed',
          DESCRIBE: '银行卡充值失败',
          TRANSACTION_ID: 'TXN20240113001',
          PAYMENT_METHOD: '银行卡'
        },
        {
          id: '4',
          LOG_TIME: '2024-01-12 16:45:00',
          POINT: 300,
          STATUS: 'Active',
          DESCRIBE: '支付宝充值处理中',
          TRANSACTION_ID: 'TXN20240112001',
          PAYMENT_METHOD: '支付宝'
        }
      ];
      setRecordData(mockData);
    } catch (error) {
      message.error('加载充值记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setDateRange([dates[0], dates[1]]);
    } else {
      setDateRange(null);
    }
  };

  // 查询按钮点击
  const handleQuery = () => {
    if (dateRange) {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');
      loadRecordData(startDate, endDate);
    } else {
      loadRecordData();
    }
  };

  // 导出CSV
  const handleExport = () => {
    try {
      // 准备导出数据
      const headers = ['序号', '充值时间', '充值金额', '订单状态', '描述', '交易ID', '支付方式'];
      const exportData = recordData.map((item, index) => [
        index + 1,
        item.LOG_TIME,
        `${item.POINT} 元`,
        statusMap[item.STATUS] || item.STATUS,
        item.DESCRIBE,
        item.TRANSACTION_ID || '-',
        item.PAYMENT_METHOD || '-'
      ]);

      // 创建CSV内容
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => row.join(','))
      ].join('\n');

      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const csvWithBOM = BOM + csvContent;

      // 创建下载链接
      const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      // 生成文件名
      const dateRangeStr = dateRange 
        ? `${dateRange[0].format('YYYY-MM-DD')}_至_${dateRange[1].format('YYYY-MM-DD')}`
        : `充值记录_${dayjs().format('YYYY-MM-DD')}`;
      const filename = `${dateRangeStr}.csv`;
      
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '充值时间',
      dataIndex: 'LOG_TIME',
      key: 'LOG_TIME',
      width: 160,
    },
    {
      title: '充值金额',
      dataIndex: 'POINT',
      key: 'POINT',
      width: 120,
      render: (point: number) => (
        <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ¥{point}
        </Text>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'STATUS',
      key: 'STATUS',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {statusMap[status] || status}
        </Tag>
      ),
    },
    {
      title: '支付方式',
      dataIndex: 'PAYMENT_METHOD',
      key: 'PAYMENT_METHOD',
      width: 120,
      render: (method: string) => method || '-',
    },
    {
      title: '交易ID',
      dataIndex: 'TRANSACTION_ID',
      key: 'TRANSACTION_ID',
      width: 150,
      render: (id: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {id || '-'}
        </Text>
      ),
    },
    {
      title: '描述',
      dataIndex: 'DESCRIBE',
      key: 'DESCRIBE',
      ellipsis: true,
    },
  ];

  return (
    <div className="recharge-record-page">
      {/* 查询条件 */}
      <Card style={{ marginBottom: 16 }}>
        <h6 style={{ marginBottom: 16 }}>查询条件：</h6>
        <Space wrap>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            placeholder={['开始日期', '结束日期']}
            style={{ width: 240 }}
          />
          
          <Button 
            type="primary" 
            icon={<SearchOutlined />}
            onClick={handleQuery}
          >
            查询
          </Button>
          
          <Button 
            type="default" 
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出
          </Button>
        </Space>
      </Card>

      {/* 充值记录列表 */}
      <div className="table-responsive">
        <Table
          columns={columns}
          dataSource={recordData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1000 }}
        />
      </div>
    </div>
  );
};

export default Page_Recharge_Record;
