/* flatpickr v3.1.4,, @license MIT */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.flatpickr=t()}(this,function(){"use strict";function e(e,t,n){return!1!==n?new Date(e.getTime()).setHours(0,0,0,0)-new Date(t.getTime()).setHours(0,0,0,0):e.getTime()-t.getTime()}function t(e,t,n){void 0===n&&(n=!1);var a;return function(){var i=this,o=arguments;null!==a&&clearTimeout(a),a=setTimeout(function(){a=null,n||e.apply(i,o)},t),n&&!a&&e.apply(i,o)}}function n(e){return(e.wheelDelta||-e.deltaY)>=0?1:-1}function a(e,t,n){if(!0===n)return e.classList.add(t);e.classList.remove(t)}function i(e,t,n){var a=window.document.createElement(e);return t=t||"",n=n||"",a.className=t,void 0!==n&&(a.textContent=n),a}function o(e){for(;e.firstChild;)e.removeChild(e.firstChild)}function r(e,t){return t(e)?e:e.parentNode?r(e.parentNode,t):void 0}function l(e){var t=i("div","numInputWrapper"),n=i("input","numInput "+e),a=i("span","arrowUp"),o=i("span","arrowDown");return n.type="text",n.pattern="\\d*",t.appendChild(n),t.appendChild(a),t.appendChild(o),t}function c(c,d){function m(){ye.utils={getDaysInMonth:function(e,t){return void 0===e&&(e=ye.currentMonth),void 0===t&&(t=ye.currentYear),1===e&&(t%4==0&&t%100!=0||t%400==0)?29:ye.l10n.daysInMonth[e]}}}function g(e){return e.bind(ye)}function D(e){ye.config.noCalendar&&!ye.selectedDates.length&&(ye.setDate((new Date).setHours(ye.config.defaultHour,ye.config.defaultMinute,ye.config.defaultSeconds),!1),E(),Ce()),be(e),0!==ye.selectedDates.length&&(!ye.minDateHasTime||"input"!==e.type||e.target.value.length>=2?(E(),Ce()):setTimeout(function(){E(),Ce()},1e3))}function y(e,t){return e%12+12*h("PM"===t)}function x(e){switch(e%24){case 0:case 12:return 12;default:return e%12}}function E(){if(void 0!==ye.hourElement&&void 0!==ye.minuteElement){var t=(parseInt(ye.hourElement.value.slice(-2),10)||0)%24,n=(parseInt(ye.minuteElement.value,10)||0)%60,a=void 0!==ye.secondElement?(parseInt(ye.secondElement.value,10)||0)%60:0;void 0!==ye.amPM&&(t=y(t,ye.amPM.textContent)),ye.config.minDate&&ye.minDateHasTime&&ye.latestSelectedDateObj&&0===e(ye.latestSelectedDateObj,ye.config.minDate)&&(t=Math.max(t,ye.config.minDate.getHours()))===ye.config.minDate.getHours()&&(n=Math.max(n,ye.config.minDate.getMinutes())),ye.config.maxDate&&ye.maxDateHasTime&&ye.latestSelectedDateObj&&0===e(ye.latestSelectedDateObj,ye.config.maxDate)&&(t=Math.min(t,ye.config.maxDate.getHours()))===ye.config.maxDate.getHours()&&(n=Math.min(n,ye.config.maxDate.getMinutes())),N(t,n,a)}}function k(e){var t=e||ye.latestSelectedDateObj;t&&N(t.getHours(),t.getMinutes(),t.getSeconds())}function N(e,t,n){void 0!==ye.latestSelectedDateObj&&ye.latestSelectedDateObj.setHours(e%24,t,n||0,0),ye.hourElement&&ye.minuteElement&&!ye.isMobile&&(ye.hourElement.value=p(ye.config.time_24hr?e:(12+e)%12+12*h(e%12==0)),ye.minuteElement.value=p(t),void 0!==ye.amPM&&(ye.amPM.textContent=e>=12?"PM":"AM"),void 0!==ye.secondElement&&(ye.secondElement.value=p(n)))}function S(e){var t=parseInt(e.target.value)+(e.delta||0);4!==t.toString().length&&"Enter"!==e.key||(ye.currentYearElement.blur(),/[^\d]/.test(t.toString())||Z(t))}function T(e,t,n){return t instanceof Array?t.forEach(function(t){return T(e,t,n)}):e instanceof Array?e.forEach(function(e){return T(e,t,n)}):(e.addEventListener(t,n),void ye._handlers.push({element:e,event:t,handler:n}))}function I(e){return function(t){return 1===t.which&&e(t)}}function Y(){ge("onChange")}function _(){if(ye.config.wrap&&["open","close","toggle","clear"].forEach(function(e){Array.prototype.forEach.call(ye.element.querySelectorAll("[data-"+e+"]"),function(t){return T(t,"click",ye[e])})}),ye.isMobile)me();else{var e=t(te,50);if(ye._debouncedChange=t(Y,300),"range"===ye.config.mode&&ye.daysContainer&&T(ye.daysContainer,"mouseover",function(e){return ee(e.target)}),T(window.document.body,"keydown",X),ye.config.static||T(ye._input,"keydown",X),ye.config.inline||ye.config.static||T(window,"resize",e),void 0!==window.ontouchstart&&T(window.document.body,"touchstart",V),T(window.document.body,"mousedown",I(V)),T(ye._input,"blur",V),!0===ye.config.clickOpens&&(T(ye._input,"focus",ye.open),T(ye._input,"mousedown",I(ye.open))),void 0!==ye.daysContainer&&(ye.monthNav.addEventListener("wheel",function(e){return e.preventDefault()}),T(ye.monthNav,"wheel",t(we,10)),T(ye.monthNav,"mousedown",I(Me)),T(ye.monthNav,["keyup","increment"],S),T(ye.daysContainer,"mousedown",I(le)),ye.config.animate&&(T(ye.daysContainer,["webkitAnimationEnd","animationend"],F),T(ye.monthNav,["webkitAnimationEnd","animationend"],A))),void 0!==ye.timeContainer&&void 0!==ye.minuteElement&&void 0!==ye.hourElement){T(ye.timeContainer,["wheel","input","increment"],D),T(ye.timeContainer,"mousedown",I(P)),T(ye.timeContainer,["wheel","increment"],ye._debouncedChange),T(ye.timeContainer,"input",Y),T([ye.hourElement,ye.minuteElement],["focus","click"],function(e){return e.target.select()}),void 0!==ye.secondElement&&T(ye.secondElement,"focus",function(){return ye.secondElement&&ye.secondElement.select()}),void 0!==ye.amPM&&T(ye.amPM,"mousedown",I(function(e){D(e),Y()}))}}}function O(){ye._animationLoop.forEach(function(e){return e()}),ye._animationLoop=[]}function F(e){if(ye.daysContainer&&ye.daysContainer.childNodes.length>1)switch(e.animationName){case"fpSlideLeft":ye.daysContainer.lastChild&&ye.daysContainer.lastChild.classList.remove("slideLeftNew"),ye.daysContainer.removeChild(ye.daysContainer.firstChild),ye.days=ye.daysContainer.firstChild,O();break;case"fpSlideRight":ye.daysContainer.firstChild&&ye.daysContainer.firstChild.classList.remove("slideRightNew"),ye.daysContainer.removeChild(ye.daysContainer.lastChild),ye.days=ye.daysContainer.firstChild,O()}}function A(e){switch(e.animationName){case"fpSlideLeftNew":case"fpSlideRightNew":ye.navigationCurrentMonth.classList.remove("slideLeftNew"),ye.navigationCurrentMonth.classList.remove("slideRightNew");for(var t=ye.navigationCurrentMonth;t.nextSibling&&/curr/.test(t.nextSibling.className);)ye.monthNav.removeChild(t.nextSibling);for(;t.previousSibling&&/curr/.test(t.previousSibling.className);)ye.monthNav.removeChild(t.previousSibling);ye.oldCurMonth=void 0}}function L(e){var t=void 0!==e?ue(e):ye.latestSelectedDateObj||(ye.config.minDate&&ye.config.minDate>ye.now?ye.config.minDate:ye.config.maxDate&&ye.config.maxDate<ye.now?ye.config.maxDate:ye.now);try{void 0!==t&&(ye.currentYear=t.getFullYear(),ye.currentMonth=t.getMonth())}catch(e){console.error(e.stack),console.warn("Invalid date supplied: "+t)}ye.redraw()}function P(e){~e.target.className.indexOf("arrow")&&j(e,e.target.classList.contains("arrowUp")?1:-1)}function j(e,t,n){var a=e&&e.target,i=n||a&&a.parentNode&&a.parentNode.firstChild,o=pe("increment");o.delta=t,i&&i.dispatchEvent(o)}function H(){var e=window.document.createDocumentFragment();if(ye.calendarContainer=i("div","flatpickr-calendar"),ye.calendarContainer.tabIndex=-1,!ye.config.noCalendar){if(e.appendChild(K()),ye.innerContainer=i("div","flatpickr-innerContainer"),ye.config.weekNumbers){var t=q(),n=t.weekWrapper,o=t.weekNumbers;ye.innerContainer.appendChild(n),ye.weekNumbers=o,ye.weekWrapper=n}ye.rContainer=i("div","flatpickr-rContainer"),ye.rContainer.appendChild($()),ye.daysContainer||(ye.daysContainer=i("div","flatpickr-days"),ye.daysContainer.tabIndex=-1),J(),ye.rContainer.appendChild(ye.daysContainer),ye.innerContainer.appendChild(ye.rContainer),e.appendChild(ye.innerContainer)}ye.config.enableTime&&e.appendChild(U()),a(ye.calendarContainer,"rangeMode","range"===ye.config.mode),a(ye.calendarContainer,"animate",ye.config.animate),ye.calendarContainer.appendChild(e);var r=void 0!==ye.config.appendTo&&ye.config.appendTo.nodeType;if((ye.config.inline||ye.config.static)&&(ye.calendarContainer.classList.add(ye.config.inline?"inline":"static"),ye.config.inline&&!r&&ye.element.parentNode&&ye.element.parentNode.insertBefore(ye.calendarContainer,ye._input.nextSibling),ye.config.static)){var l=i("div","flatpickr-wrapper");ye.element.parentNode&&ye.element.parentNode.insertBefore(l,ye.element),l.appendChild(ye.element),ye.altInput&&l.appendChild(ye.altInput),l.appendChild(ye.calendarContainer)}ye.config.static||ye.config.inline||(void 0!==ye.config.appendTo?ye.config.appendTo:window.document.body).appendChild(ye.calendarContainer)}function R(t,n,o,r){var l=Q(n,!0),c=i("span","flatpickr-day "+t,n.getDate().toString());return c.dateObj=n,c.$i=r,c.setAttribute("aria-label",ye.formatDate(n,ye.config.ariaDateFormat)),0===e(n,ye.now)&&(ye.todayDateElem=c,c.classList.add("today")),l?(c.tabIndex=-1,he(n)&&(c.classList.add("selected"),ye.selectedDateElem=c,"range"===ye.config.mode&&(a(c,"startRange",ye.selectedDates[0]&&0===e(n,ye.selectedDates[0])),a(c,"endRange",ye.selectedDates[1]&&0===e(n,ye.selectedDates[1]))))):(c.classList.add("disabled"),ye.selectedDates[0]&&ye.minRangeDate&&n>ye.minRangeDate&&n<ye.selectedDates[0]?ye.minRangeDate=n:ye.selectedDates[0]&&ye.maxRangeDate&&n<ye.maxRangeDate&&n>ye.selectedDates[0]&&(ye.maxRangeDate=n)),"range"===ye.config.mode&&(ve(n)&&!he(n)&&c.classList.add("inRange"),1===ye.selectedDates.length&&void 0!==ye.minRangeDate&&void 0!==ye.maxRangeDate&&(n<ye.minRangeDate||n>ye.maxRangeDate)&&c.classList.add("notAllowed")),ye.weekNumbers&&"prevMonthDay"!==t&&o%7==1&&ye.weekNumbers.insertAdjacentHTML("beforeend","<span class='disabled flatpickr-day'>"+ye.config.getWeek(n)+"</span>"),ge("onDayCreate",c),c}function W(e,t){var n=e+t||0,a=void 0!==e?ye.days.childNodes[n]:ye.selectedDateElem||ye.todayDateElem||ye.days.childNodes[0],i=function(){(a=a||ye.days.childNodes[n]).focus(),"range"===ye.config.mode&&ee(a)};if(void 0===a&&0!==t)return t>0?(ye.changeMonth(1,!0,void 0,!0),n%=42):t<0&&(ye.changeMonth(-1,!0,void 0,!0),n+=42),B(i);i()}function B(e){!0===ye.config.animate?ye._animationLoop.push(e):e()}function J(e){if(void 0!==ye.daysContainer){var t=(new Date(ye.currentYear,ye.currentMonth,1).getDay()-ye.l10n.firstDayOfWeek+7)%7,n="range"===ye.config.mode,a=ye.utils.getDaysInMonth((ye.currentMonth-1+12)%12),r=ye.utils.getDaysInMonth(),l=window.document.createDocumentFragment(),c=a+1-t,d=0;for(ye.weekNumbers&&ye.weekNumbers.firstChild&&(ye.weekNumbers.textContent=""),n&&(ye.minRangeDate=new Date(ye.currentYear,ye.currentMonth-1,c),ye.maxRangeDate=new Date(ye.currentYear,ye.currentMonth+1,(42-t)%r));c<=a;c++,d++)l.appendChild(R("prevMonthDay",new Date(ye.currentYear,ye.currentMonth-1,c),c,d));for(c=1;c<=r;c++,d++)l.appendChild(R("",new Date(ye.currentYear,ye.currentMonth,c),c,d));for(var s=r+1;s<=42-t;s++,d++)l.appendChild(R("nextMonthDay",new Date(ye.currentYear,ye.currentMonth+1,s%r),s,d));n&&1===ye.selectedDates.length&&l.childNodes[0]?(ye._hidePrevMonthArrow=ye._hidePrevMonthArrow||!!ye.minRangeDate&&ye.minRangeDate>l.childNodes[0].dateObj,ye._hideNextMonthArrow=ye._hideNextMonthArrow||!!ye.maxRangeDate&&ye.maxRangeDate<new Date(ye.currentYear,ye.currentMonth+1,1)):De();var u=i("div","dayContainer");if(u.appendChild(l),ye.config.animate&&void 0!==e)for(;ye.daysContainer.childNodes.length>1;)ye.daysContainer.removeChild(ye.daysContainer.firstChild);else o(ye.daysContainer);e&&e>=0?ye.daysContainer.appendChild(u):ye.daysContainer.insertBefore(u,ye.daysContainer.firstChild),ye.days=ye.daysContainer.childNodes[0]}}function K(){var e=window.document.createDocumentFragment();ye.monthNav=i("div","flatpickr-month"),ye.prevMonthNav=i("span","flatpickr-prev-month"),ye.prevMonthNav.innerHTML=ye.config.prevArrow,ye.currentMonthElement=i("span","cur-month"),ye.currentMonthElement.title=ye.l10n.scrollTitle;var t=l("cur-year");return ye.currentYearElement=t.childNodes[0],ye.currentYearElement.title=ye.l10n.scrollTitle,ye.config.minDate&&(ye.currentYearElement.min=ye.config.minDate.getFullYear().toString()),ye.config.maxDate&&(ye.currentYearElement.max=ye.config.maxDate.getFullYear().toString(),ye.currentYearElement.disabled=!!ye.config.minDate&&ye.config.minDate.getFullYear()===ye.config.maxDate.getFullYear()),ye.nextMonthNav=i("span","flatpickr-next-month"),ye.nextMonthNav.innerHTML=ye.config.nextArrow,ye.navigationCurrentMonth=i("span","flatpickr-current-month"),ye.navigationCurrentMonth.appendChild(ye.currentMonthElement),ye.navigationCurrentMonth.appendChild(t),e.appendChild(ye.prevMonthNav),e.appendChild(ye.navigationCurrentMonth),e.appendChild(ye.nextMonthNav),ye.monthNav.appendChild(e),Object.defineProperty(ye,"_hidePrevMonthArrow",{get:function(){return ye.__hidePrevMonthArrow},set:function(e){ye.__hidePrevMonthArrow!==e&&(ye.prevMonthNav.style.display=e?"none":"block"),ye.__hidePrevMonthArrow=e}}),Object.defineProperty(ye,"_hideNextMonthArrow",{get:function(){return ye.__hideNextMonthArrow},set:function(e){ye.__hideNextMonthArrow!==e&&(ye.nextMonthNav.style.display=e?"none":"block"),ye.__hideNextMonthArrow=e}}),De(),ye.monthNav}function U(){ye.calendarContainer.classList.add("hasTime"),ye.config.noCalendar&&ye.calendarContainer.classList.add("noCalendar"),ye.timeContainer=i("div","flatpickr-time"),ye.timeContainer.tabIndex=-1;var e=i("span","flatpickr-time-separator",":"),t=l("flatpickr-hour");ye.hourElement=t.childNodes[0];var n=l("flatpickr-minute");if(ye.minuteElement=n.childNodes[0],ye.hourElement.tabIndex=ye.minuteElement.tabIndex=-1,ye.hourElement.value=p(ye.latestSelectedDateObj?ye.latestSelectedDateObj.getHours():ye.config.time_24hr?ye.config.defaultHour:x(ye.config.defaultHour)),ye.minuteElement.value=p(ye.latestSelectedDateObj?ye.latestSelectedDateObj.getMinutes():ye.config.defaultMinute),ye.hourElement.step=ye.config.hourIncrement.toString(),ye.minuteElement.step=ye.config.minuteIncrement.toString(),ye.hourElement.min=ye.config.time_24hr?"0":"1",ye.hourElement.max=ye.config.time_24hr?"23":"12",ye.minuteElement.min="0",ye.minuteElement.max="59",ye.hourElement.title=ye.minuteElement.title=ye.l10n.scrollTitle,ye.timeContainer.appendChild(t),ye.timeContainer.appendChild(e),ye.timeContainer.appendChild(n),ye.config.time_24hr&&ye.timeContainer.classList.add("time24hr"),ye.config.enableSeconds){ye.timeContainer.classList.add("hasSeconds");var a=l("flatpickr-second");ye.secondElement=a.childNodes[0],ye.secondElement.value=p(ye.latestSelectedDateObj?ye.latestSelectedDateObj.getSeconds():ye.config.defaultSeconds),ye.secondElement.step=ye.minuteElement.step,ye.secondElement.min=ye.minuteElement.min,ye.secondElement.max=ye.minuteElement.max,ye.timeContainer.appendChild(i("span","flatpickr-time-separator",":")),ye.timeContainer.appendChild(a)}return ye.config.time_24hr||(ye.amPM=i("span","flatpickr-am-pm",ye.l10n.amPM[h((ye.latestSelectedDateObj?ye.hourElement.value:ye.config.defaultHour)>11)]),ye.amPM.title=ye.l10n.toggleTitle,ye.amPM.tabIndex=-1,ye.timeContainer.appendChild(ye.amPM)),ye.timeContainer}function $(){ye.weekdayContainer||(ye.weekdayContainer=i("div","flatpickr-weekdays"));var e=ye.l10n.firstDayOfWeek,t=ye.l10n.weekdays.shorthand.slice();return e>0&&e<t.length&&(t=t.splice(e,t.length).concat(t.splice(0,e))),ye.weekdayContainer.innerHTML="\n    <span class=flatpickr-weekday>\n      "+t.join("</span><span class=flatpickr-weekday>")+"\n    </span>\n    ",ye.weekdayContainer}function q(){ye.calendarContainer.classList.add("hasWeeks");var e=i("div","flatpickr-weekwrapper");e.appendChild(i("span","flatpickr-weekday",ye.l10n.weekAbbreviation));var t=i("div","flatpickr-weeks");return e.appendChild(t),{weekWrapper:e,weekNumbers:t}}function z(e,t,n,a){void 0===t&&(t=!0),void 0===n&&(n=ye.config.animate),void 0===a&&(a=!1);var i=t?e:e-ye.currentMonth;if(!(i<0&&ye._hidePrevMonthArrow||i>0&&ye._hideNextMonthArrow)){if(ye.currentMonth+=i,(ye.currentMonth<0||ye.currentMonth>11)&&(ye.currentYear+=ye.currentMonth>11?1:-1,ye.currentMonth=(ye.currentMonth+12)%12,ge("onYearChange")),J(n?i:void 0),!n)return ge("onMonthChange"),De();var o=ye.navigationCurrentMonth;if(i<0)for(;o.nextSibling&&/curr/.test(o.nextSibling.className);)ye.monthNav.removeChild(o.nextSibling);else if(i>0)for(;o.previousSibling&&/curr/.test(o.previousSibling.className);)ye.monthNav.removeChild(o.previousSibling);ye.oldCurMonth=ye.navigationCurrentMonth,ye.navigationCurrentMonth=ye.monthNav.insertBefore(ye.oldCurMonth.cloneNode(!0),i>0?ye.oldCurMonth.nextSibling:ye.oldCurMonth);var r=ye.daysContainer;if(r.firstChild&&r.lastChild&&(i>0?(r.firstChild.classList.add("slideLeft"),r.lastChild.classList.add("slideLeftNew"),ye.oldCurMonth.classList.add("slideLeft"),ye.navigationCurrentMonth.classList.add("slideLeftNew")):i<0&&(r.firstChild.classList.add("slideRightNew"),r.lastChild.classList.add("slideRight"),ye.oldCurMonth.classList.add("slideRight"),ye.navigationCurrentMonth.classList.add("slideRightNew"))),ye.currentMonthElement=ye.navigationCurrentMonth.firstChild,ye.currentYearElement=ye.navigationCurrentMonth.lastChild.childNodes[0],De(),ye.oldCurMonth.firstChild&&(ye.oldCurMonth.firstChild.textContent=u(ye.currentMonth-i,ye.config.shorthandCurrentMonth,ye.l10n)),ge("onMonthChange"),a&&document.activeElement&&document.activeElement.$i){var l=document.activeElement.$i;B(function(){W(l,0)})}}}function G(e){return!(!ye.config.appendTo||!ye.config.appendTo.contains(e))||ye.calendarContainer.contains(e)}function V(e){if(ye.isOpen&&!ye.config.inline){var t=G(e.target),n=e.target===ye.input||e.target===ye.altInput||ye.element.contains(e.target)||e.path&&e.path.indexOf&&(~e.path.indexOf(ye.input)||~e.path.indexOf(ye.altInput));("blur"===e.type?n&&e.relatedTarget&&!G(e.relatedTarget):!n&&!t)&&-1===ye.config.ignoredFocusElements.indexOf(e.target)&&(ye.close(),"range"===ye.config.mode&&1===ye.selectedDates.length&&(ye.clear(!1),ye.redraw()))}}function Z(e){if(!(!e||ye.currentYearElement.min&&e<parseInt(ye.currentYearElement.min)||ye.currentYearElement.max&&e>parseInt(ye.currentYearElement.max))){var t=e,n=ye.currentYear!==t;ye.currentYear=t||ye.currentYear,ye.config.maxDate&&ye.currentYear===ye.config.maxDate.getFullYear()?ye.currentMonth=Math.min(ye.config.maxDate.getMonth(),ye.currentMonth):ye.config.minDate&&ye.currentYear===ye.config.minDate.getFullYear()&&(ye.currentMonth=Math.max(ye.config.minDate.getMonth(),ye.currentMonth)),n&&(ye.redraw(),ge("onYearChange"))}}function Q(t,n){void 0===n&&(n=!0);var a=ye.parseDate(t,void 0,n);if(ye.config.minDate&&a&&e(a,ye.config.minDate,void 0!==n?n:!ye.minDateHasTime)<0||ye.config.maxDate&&a&&e(a,ye.config.maxDate,void 0!==n?n:!ye.maxDateHasTime)>0)return!1;if(!ye.config.enable.length&&!ye.config.disable.length)return!0;if(void 0===a)return!1;for(var i=ye.config.enable.length>0,o=i?ye.config.enable:ye.config.disable,r=0,l=void 0;r<o.length;r++){if("function"==typeof(l=o[r])&&l(a))return i;if(l instanceof Date&&void 0!==a&&l.getTime()===a.getTime())return i;if("string"==typeof l&&void 0!==a){var c=ye.parseDate(l,void 0,!0);return c&&c.getTime()===a.getTime()?i:!i}if("object"==typeof l&&void 0!==a&&l.from&&l.to&&a.getTime()>=l.from.getTime()&&a.getTime()<=l.to.getTime())return i}return!i}function X(e){var t=e.target===ye._input,n=G(e.target),a=ye.config.allowInput,i=ye.isOpen&&(!a||!t),o=ye.config.inline&&t&&!a;if("Enter"===e.key&&t){if(a)return ye.setDate(ye._input.value,!0,e.target===ye.altInput?ye.config.altFormat:ye.config.dateFormat),e.target.blur();ye.open()}else if(n||i||o){var r=!!ye.timeContainer&&ye.timeContainer.contains(e.target);switch(e.key){case"Enter":r?Ce():le(e);break;case"Escape":e.preventDefault(),ye.close();break;case"Backspace":case"Delete":t&&!ye.config.allowInput&&ye.clear();break;case"ArrowLeft":case"ArrowRight":if(r)ye.hourElement&&ye.hourElement.focus();else if(e.preventDefault(),ye.daysContainer){var l="ArrowRight"===e.key?1:-1;e.ctrlKey?z(l,!0,void 0,!0):W(e.target.$i,l)}break;case"ArrowUp":case"ArrowDown":e.preventDefault();var c="ArrowDown"===e.key?1:-1;ye.daysContainer&&void 0!==e.target.$i?e.ctrlKey?(Z(ye.currentYear-c),W(e.target.$i,0)):r||W(e.target.$i,7*c):ye.config.enableTime&&(!r&&ye.hourElement&&ye.hourElement.focus(),D(e),ye._debouncedChange());break;case"Tab":e.target===ye.hourElement?(e.preventDefault(),ye.minuteElement.select()):e.target===ye.minuteElement&&(ye.secondElement||ye.amPM)?(e.preventDefault(),void 0!==ye.secondElement?ye.secondElement.focus():void 0!==ye.amPM&&ye.amPM.focus()):e.target===ye.secondElement&&ye.amPM&&(e.preventDefault(),ye.amPM.focus());break;case"a":void 0!==ye.amPM&&e.target===ye.amPM&&(ye.amPM.textContent="AM",E(),Ce());break;case"p":void 0!==ye.amPM&&e.target===ye.amPM&&(ye.amPM.textContent="PM",E(),Ce())}ge("onKeyDown",e)}}function ee(e){if(1===ye.selectedDates.length&&e.classList.contains("flatpickr-day")&&void 0!==ye.minRangeDate&&void 0!==ye.maxRangeDate){for(var t=e.dateObj,n=ye.parseDate(ye.selectedDates[0],void 0,!0),a=Math.min(t.getTime(),ye.selectedDates[0].getTime()),i=Math.max(t.getTime(),ye.selectedDates[0].getTime()),o=!1,r=a;r<i;r+=f.DAY)if(!Q(new Date(r))){o=!0;break}for(var l=ye.days.childNodes[0].dateObj.getTime(),c=0;c<42;c++,l+=f.DAY)!function(r,l){var c=r<ye.minRangeDate.getTime()||r>ye.maxRangeDate.getTime(),d=ye.days.childNodes[l];if(c)return d.classList.add("notAllowed"),["inRange","startRange","endRange"].forEach(function(e){d.classList.remove(e)}),"continue";if(o&&!c)return"continue";["startRange","inRange","endRange","notAllowed"].forEach(function(e){d.classList.remove(e)});var s=Math.max(ye.minRangeDate.getTime(),a),u=Math.min(ye.maxRangeDate.getTime(),i);e.classList.add(t<ye.selectedDates[0]?"startRange":"endRange"),n<t&&r===n.getTime()?d.classList.add("startRange"):n>t&&r===n.getTime()&&d.classList.add("endRange"),r>=s&&r<=u&&d.classList.add("inRange")}(l,c)}}function te(){!ye.isOpen||ye.config.static||ye.config.inline||oe()}function ne(e){return function(t){var n=ye.config["_"+e+"Date"]=ye.parseDate(t),a=ye.config["_"+("min"===e?"max":"min")+"Date"];void 0!==n&&(ye["min"===e?"minDateHasTime":"maxDateHasTime"]=n.getHours()>0||n.getMinutes()>0||n.getSeconds()>0),ye.selectedDates&&(ye.selectedDates=ye.selectedDates.filter(function(e){return Q(e)}),ye.selectedDates.length||"min"!==e||k(n),Ce()),ye.daysContainer&&(re(),void 0!==n?ye.currentYearElement[e]=n.getFullYear().toString():ye.currentYearElement.removeAttribute(e),ye.currentYearElement.disabled=!!a&&void 0!==n&&a.getFullYear()===n.getFullYear())}}function ae(){var e=["wrap","weekNumbers","allowInput","clickOpens","time_24hr","enableTime","noCalendar","altInput","shorthandCurrentMonth","inline","static","enableSeconds","disableMobile"],t=["onChange","onClose","onDayCreate","onDestroy","onKeyDown","onMonthChange","onOpen","onParseConfig","onReady","onValueUpdate","onYearChange"];ye.config=s({},b.defaultConfig);var n=s({},d,JSON.parse(JSON.stringify(c.dataset||{}))),a={};Object.defineProperty(ye.config,"enable",{get:function(){return ye.config._enable||[]},set:function(e){ye.config._enable=de(e)}}),Object.defineProperty(ye.config,"disable",{get:function(){return ye.config._disable||[]},set:function(e){ye.config._disable=de(e)}}),!n.dateFormat&&n.enableTime&&(a.dateFormat=n.noCalendar?"H:i"+(n.enableSeconds?":S":""):b.defaultConfig.dateFormat+" H:i"+(n.enableSeconds?":S":"")),n.altInput&&n.enableTime&&!n.altFormat&&(a.altFormat=n.noCalendar?"h:i"+(n.enableSeconds?":S K":" K"):b.defaultConfig.altFormat+" h:i"+(n.enableSeconds?":S":"")+" K"),Object.defineProperty(ye.config,"minDate",{get:function(){return ye.config._minDate},set:ne("min")}),Object.defineProperty(ye.config,"maxDate",{get:function(){return ye.config._maxDate},set:ne("max")}),Object.assign(ye.config,a,n);for(i=0;i<e.length;i++)ye.config[e[i]]=!0===ye.config[e[i]]||"true"===ye.config[e[i]];for(i=t.length;i--;)void 0!==ye.config[t[i]]&&(ye.config[t[i]]=v(ye.config[t[i]]||[]).map(g));for(var i=0;i<ye.config.plugins.length;i++){var o=ye.config.plugins[i](ye)||{};for(var r in o)~t.indexOf(r)?ye.config[r]=v(o[r]).map(g).concat(ye.config[r]):void 0===n[r]&&(ye.config[r]=o[r])}ye.isMobile=!ye.config.disableMobile&&!ye.config.inline&&"single"===ye.config.mode&&!ye.config.disable.length&&!ye.config.enable.length&&!ye.config.weekNumbers&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),ge("onParseConfig")}function ie(){"object"!=typeof ye.config.locale&&void 0===b.l10ns[ye.config.locale]&&console.warn("flatpickr: invalid locale "+ye.config.locale),ye.l10n=s({},b.l10ns.default,"object"==typeof ye.config.locale?ye.config.locale:"default"!==ye.config.locale?b.l10ns[ye.config.locale]:void 0)}function oe(e){if(void 0===e&&(e=ye._positionElement),void 0!==ye.calendarContainer){var t=ye.calendarContainer.offsetHeight,n=ye.calendarContainer.offsetWidth,i=ye.config.position,o=e.getBoundingClientRect(),r=window.innerHeight-o.bottom,l="above"===i||"below"!==i&&r<t&&o.top>t,c=window.pageYOffset+o.top+(l?-t-2:e.offsetHeight+2);if(a(ye.calendarContainer,"arrowTop",!l),a(ye.calendarContainer,"arrowBottom",l),!ye.config.inline){var d=window.pageXOffset+o.left,s=window.document.body.offsetWidth-o.right,u=d+n>window.document.body.offsetWidth;a(ye.calendarContainer,"rightMost",u),ye.config.static||(ye.calendarContainer.style.top=c+"px",u?(ye.calendarContainer.style.left="auto",ye.calendarContainer.style.right=s+"px"):(ye.calendarContainer.style.left=d+"px",ye.calendarContainer.style.right="auto"))}}}function re(){ye.config.noCalendar||ye.isMobile||($(),De(),J())}function le(t){t.preventDefault(),t.stopPropagation();var n=r(t.target,function(e){return e.classList&&e.classList.contains("flatpickr-day")&&!e.classList.contains("disabled")&&!e.classList.contains("notAllowed")});if(void 0!==n){var a=n,i=ye.latestSelectedDateObj=new Date(a.dateObj.getTime()),o=i.getMonth()!==ye.currentMonth&&"range"!==ye.config.mode;if(ye.selectedDateElem=a,"single"===ye.config.mode)ye.selectedDates=[i];else if("multiple"===ye.config.mode){var l=he(i);l?ye.selectedDates.splice(parseInt(l),1):ye.selectedDates.push(i)}else"range"===ye.config.mode&&(2===ye.selectedDates.length&&ye.clear(),ye.selectedDates.push(i),0!==e(i,ye.selectedDates[0],!0)&&ye.selectedDates.sort(function(e,t){return e.getTime()-t.getTime()}));if(E(),o){var c=ye.currentYear!==i.getFullYear();ye.currentYear=i.getFullYear(),ye.currentMonth=i.getMonth(),c&&ge("onYearChange"),ge("onMonthChange")}if(J(),ye.config.minDate&&ye.minDateHasTime&&ye.config.enableTime&&0===e(i,ye.config.minDate)&&k(ye.config.minDate),Ce(),ye.config.enableTime&&setTimeout(function(){return ye.showTimeInput=!0},50),"range"===ye.config.mode&&(1===ye.selectedDates.length?(ee(a),ye._hidePrevMonthArrow=ye._hidePrevMonthArrow||void 0!==ye.minRangeDate&&ye.minRangeDate>ye.days.childNodes[0].dateObj,ye._hideNextMonthArrow=ye._hideNextMonthArrow||void 0!==ye.maxRangeDate&&ye.maxRangeDate<new Date(ye.currentYear,ye.currentMonth+1,1)):De()),ge("onChange"),o?B(function(){return ye.selectedDateElem&&ye.selectedDateElem.focus()}):W(a.$i,0),void 0!==ye.hourElement&&setTimeout(function(){return void 0!==ye.hourElement&&ye.hourElement.select()},451),ye.config.closeOnSelect){var d="single"===ye.config.mode&&!ye.config.enableTime,s="range"===ye.config.mode&&2===ye.selectedDates.length&&!ye.config.enableTime;(d||s)&&ye.close()}}}function ce(e,t){var n=[];if(e instanceof Array)n=e.map(function(e){return ye.parseDate(e,t)});else if(e instanceof Date||"number"==typeof e)n=[ye.parseDate(e,t)];else if("string"==typeof e)switch(ye.config.mode){case"single":n=[ye.parseDate(e,t)];break;case"multiple":n=e.split("; ").map(function(e){return ye.parseDate(e,t)});break;case"range":n=e.split(ye.l10n.rangeSeparator).map(function(e){return ye.parseDate(e,t)})}ye.selectedDates=n.filter(function(e){return e instanceof Date&&Q(e,!1)}),ye.selectedDates.sort(function(e,t){return e.getTime()-t.getTime()})}function de(e){return e.map(function(e){return"string"==typeof e||"number"==typeof e||e instanceof Date?ye.parseDate(e,void 0,!0):e&&"object"==typeof e&&e.from&&e.to?{from:ye.parseDate(e.from,void 0),to:ye.parseDate(e.to,void 0)}:e}).filter(function(e){return e})}function se(){ye.selectedDates=[],ye.now=new Date;var e=ye.config.defaultDate||ye.input.value;e&&ce(e,ye.config.dateFormat);var t=ye.selectedDates.length?ye.selectedDates[0]:ye.config.minDate&&ye.config.minDate.getTime()>ye.now.getTime()?ye.config.minDate:ye.config.maxDate&&ye.config.maxDate.getTime()<ye.now.getTime()?ye.config.maxDate:ye.now;ye.currentYear=t.getFullYear(),ye.currentMonth=t.getMonth(),ye.selectedDates.length&&(ye.latestSelectedDateObj=ye.selectedDates[0]),ye.minDateHasTime=!!ye.config.minDate&&(ye.config.minDate.getHours()>0||ye.config.minDate.getMinutes()>0||ye.config.minDate.getSeconds()>0),ye.maxDateHasTime=!!ye.config.maxDate&&(ye.config.maxDate.getHours()>0||ye.config.maxDate.getMinutes()>0||ye.config.maxDate.getSeconds()>0),Object.defineProperty(ye,"showTimeInput",{get:function(){return ye._showTimeInput},set:function(e){ye._showTimeInput=e,ye.calendarContainer&&a(ye.calendarContainer,"showTimeInput",e),oe()}})}function ue(e,t,n){if(0===e||e){var a,i=e;if(e instanceof Date)a=new Date(e.getTime());else if("string"!=typeof e&&void 0!==e.toFixed)a=new Date(e);else if("string"==typeof e){var o=t||(ye.config||b.defaultConfig).dateFormat,r=String(e).trim();if("today"===r)a=new Date,n=!0;else if(/Z$/.test(r)||/GMT$/.test(r))a=new Date(e);else if(ye.config&&ye.config.parseDate)a=ye.config.parseDate(e,o);else{a=ye.config&&ye.config.noCalendar?new Date((new Date).setHours(0,0,0,0)):new Date((new Date).getFullYear(),0,1,0,0,0,0);for(var l=void 0,c=[],d=0,s=0,u="";d<o.length;d++){var f=o[d],m="\\"===f,g="\\"===o[d-1]||m;if(w[f]&&!g){u+=w[f];var p=new RegExp(u).exec(e);p&&(l=!0)&&c["Y"!==f?"push":"unshift"]({fn:C[f],val:p[++s]})}else m||(u+=".");c.forEach(function(e){var t=e.fn,n=e.val;return a=t(a,n,ye.l10n)||a})}a=l?a:void 0}}return a instanceof Date?(!0===n&&a.setHours(0,0,0,0),a):(console.warn("flatpickr: invalid date "+i),void console.info(ye.element))}}function fe(){ye.input=ye.config.wrap?c.querySelector("[data-input]"):c,ye.input?(ye.input._type=ye.input.type,ye.input.type="text",ye.input.classList.add("flatpickr-input"),ye._input=ye.input,ye.config.altInput&&(ye.altInput=i(ye.input.nodeName,ye.input.className+" "+ye.config.altInputClass),ye._input=ye.altInput,ye.altInput.placeholder=ye.input.placeholder,ye.altInput.disabled=ye.input.disabled,ye.altInput.required=ye.input.required,ye.altInput.type="text",ye.input.type="hidden",!ye.config.static&&ye.input.parentNode&&ye.input.parentNode.insertBefore(ye.altInput,ye.input.nextSibling)),ye.config.allowInput||ye._input.setAttribute("readonly","readonly"),ye._positionElement=ye.config.positionElement||ye._input):console.warn("Error: invalid input element specified",ye.input)}function me(){var e=ye.config.enableTime?ye.config.noCalendar?"time":"datetime-local":"date";ye.mobileInput=i("input",ye.input.className+" flatpickr-mobile"),ye.mobileInput.step=ye.input.getAttribute("step")||"any",ye.mobileInput.tabIndex=1,ye.mobileInput.type=e,ye.mobileInput.disabled=ye.input.disabled,ye.mobileInput.placeholder=ye.input.placeholder,ye.mobileFormatStr="datetime-local"===e?"Y-m-d\\TH:i:S":"date"===e?"Y-m-d":"H:i:S",ye.selectedDates.length&&(ye.mobileInput.defaultValue=ye.mobileInput.value=ye.formatDate(ye.selectedDates[0],ye.mobileFormatStr)),ye.config.minDate&&(ye.mobileInput.min=ye.formatDate(ye.config.minDate,"Y-m-d")),ye.config.maxDate&&(ye.mobileInput.max=ye.formatDate(ye.config.maxDate,"Y-m-d")),ye.input.type="hidden",void 0!==ye.altInput&&(ye.altInput.type="hidden");try{ye.input.parentNode&&ye.input.parentNode.insertBefore(ye.mobileInput,ye.input.nextSibling)}catch(e){}ye.mobileInput.addEventListener("change",function(e){ye.setDate(e.target.value,!1,ye.mobileFormatStr),ge("onChange"),ge("onClose")})}function ge(e,t){var n=ye.config[e];if(void 0!==n&&n.length>0)for(var a=0;n[a]&&a<n.length;a++)n[a](ye.selectedDates,ye.input.value,ye,t);"onChange"===e&&(ye.input.dispatchEvent(pe("change")),ye.input.dispatchEvent(pe("input")))}function pe(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!0),t}function he(t){for(var n=0;n<ye.selectedDates.length;n++)if(0===e(ye.selectedDates[n],t))return""+n;return!1}function ve(t){return!("range"!==ye.config.mode||ye.selectedDates.length<2)&&(e(t,ye.selectedDates[0])>=0&&e(t,ye.selectedDates[1])<=0)}function De(){ye.config.noCalendar||ye.isMobile||!ye.monthNav||(ye.currentMonthElement.textContent=u(ye.currentMonth,ye.config.shorthandCurrentMonth,ye.l10n)+" ",ye.currentYearElement.value=ye.currentYear.toString(),ye._hidePrevMonthArrow=void 0!==ye.config.minDate&&(ye.currentYear===ye.config.minDate.getFullYear()?ye.currentMonth<=ye.config.minDate.getMonth():ye.currentYear<ye.config.minDate.getFullYear()),ye._hideNextMonthArrow=void 0!==ye.config.maxDate&&(ye.currentYear===ye.config.maxDate.getFullYear()?ye.currentMonth+1>ye.config.maxDate.getMonth():ye.currentYear>ye.config.maxDate.getFullYear()))}function Ce(e){if(void 0===e&&(e=!0),!ye.selectedDates.length)return ye.clear(e);void 0!==ye.mobileInput&&ye.mobileFormatStr&&(ye.mobileInput.value=void 0!==ye.latestSelectedDateObj?ye.formatDate(ye.latestSelectedDateObj,ye.mobileFormatStr):"");var t="range"!==ye.config.mode?ye.config.conjunction:ye.l10n.rangeSeparator;ye.input.value=ye.selectedDates.map(function(e){return ye.formatDate(e,ye.config.dateFormat)}).join(t),void 0!==ye.altInput&&(ye.altInput.value=ye.selectedDates.map(function(e){return ye.formatDate(e,ye.config.altFormat)}).join(t)),!1!==e&&ge("onValueUpdate")}function we(e){e.preventDefault();var t=ye.currentYearElement.parentNode&&ye.currentYearElement.parentNode.contains(e.target);if(e.target===ye.currentMonthElement||t){var a=n(e);t?(Z(ye.currentYear+a),e.target.value=ye.currentYear.toString()):ye.changeMonth(a,!0,!1)}}function Me(e){var t=ye.prevMonthNav.contains(e.target),n=ye.nextMonthNav.contains(e.target);t||n?z(t?-1:1):e.target===ye.currentYearElement?(e.preventDefault(),ye.currentYearElement.select()):"arrowUp"===e.target.className?ye.changeYear(ye.currentYear+1):"arrowDown"===e.target.className&&ye.changeYear(ye.currentYear-1)}function be(e){e.preventDefault();var t="keydown"===e.type,n=e.target;void 0!==ye.amPM&&e.target===ye.amPM&&(ye.amPM.textContent=ye.l10n.amPM["AM"===ye.amPM.textContent?1:0]);var a=Number(n.min),i=Number(n.max),o=Number(n.step),r=parseInt(n.value,10),l=r+o*(e.delta||(t?38===e.which?1:-1:Math.max(-1,Math.min(1,e.wheelDelta||-e.deltaY))||0));if(void 0!==n.value&&2===n.value.length){var c=n===ye.hourElement,d=n===ye.minuteElement;l<a?(l=i+l+h(!c)+(h(c)&&h(!ye.amPM)),d&&j(void 0,-1,ye.hourElement)):l>i&&(l=n===ye.hourElement?l-i-h(!ye.amPM):a,d&&j(void 0,1,ye.hourElement)),ye.amPM&&c&&(1===o?l+r===23:Math.abs(l-r)>o)&&(ye.amPM.textContent="PM"===ye.amPM.textContent?"AM":"PM"),n.value=p(l)}}var ye={};return ye.parseDate=ue,ye.formatDate=function(e,t){return void 0!==ye.config&&void 0!==ye.config.formatDate?ye.config.formatDate(e,t):t.split("").map(function(t,n,a){return M[t]&&"\\"!==a[n-1]?M[t](e,ye.l10n,ye.config):"\\"!==t?t:""}).join("")},ye._animationLoop=[],ye._handlers=[],ye._bind=T,ye._setHoursFromDate=k,ye.changeMonth=z,ye.changeYear=Z,ye.clear=function(e){void 0===e&&(e=!0),ye.input.value="",ye.altInput&&(ye.altInput.value=""),ye.mobileInput&&(ye.mobileInput.value=""),ye.selectedDates=[],ye.latestSelectedDateObj=void 0,ye.showTimeInput=!1,ye.redraw(),!0===e&&ge("onChange")},ye.close=function(){ye.isOpen=!1,ye.isMobile||(ye.calendarContainer.classList.remove("open"),ye._input.classList.remove("active")),ge("onClose")},ye._createElement=i,ye.destroy=function(){void 0!==ye.config&&ge("onDestroy");for(var e=ye._handlers.length;e--;){var t=ye._handlers[e];t.element.removeEventListener(t.event,t.handler)}ye._handlers=[],ye.mobileInput?(ye.mobileInput.parentNode&&ye.mobileInput.parentNode.removeChild(ye.mobileInput),ye.mobileInput=void 0):ye.calendarContainer&&ye.calendarContainer.parentNode&&ye.calendarContainer.parentNode.removeChild(ye.calendarContainer),ye.altInput&&(ye.input.type="text",ye.altInput.parentNode&&ye.altInput.parentNode.removeChild(ye.altInput),delete ye.altInput),ye.input&&(ye.input.type=ye.input._type,ye.input.classList.remove("flatpickr-input"),ye.input.removeAttribute("readonly"),ye.input.value=""),["_showTimeInput","latestSelectedDateObj","_hideNextMonthArrow","_hidePrevMonthArrow","__hideNextMonthArrow","__hidePrevMonthArrow","isMobile","isOpen","selectedDateElem","minDateHasTime","maxDateHasTime","days","daysContainer","_input","_positionElement","innerContainer","rContainer","monthNav","todayDateElem","calendarContainer","weekdayContainer","prevMonthNav","nextMonthNav","currentMonthElement","currentYearElement","navigationCurrentMonth","selectedDateElem","config"].forEach(function(e){try{delete ye[e]}catch(e){}})},ye.isEnabled=Q,ye.jumpToDate=L,ye.open=function(e,t){if(void 0===t&&(t=ye._input),ye.isMobile)return e&&(e.preventDefault(),e.target&&e.target.blur()),setTimeout(function(){void 0!==ye.mobileInput&&ye.mobileInput.click()},0),void ge("onOpen");ye.isOpen||ye._input.disabled||ye.config.inline||(ye.isOpen=!0,ye.calendarContainer.classList.add("open"),oe(t),ye._input.classList.add("active"),ge("onOpen"))},ye.redraw=re,ye.set=function(e,t){null!==e&&"object"==typeof e?Object.assign(ye.config,e):ye.config[e]=t,ye.redraw(),L()},ye.setDate=function(e,t,n){if(void 0===t&&(t=!1),void 0===n&&(n=void 0),0!==e&&!e)return ye.clear(t);ce(e,n),ye.showTimeInput=ye.selectedDates.length>0,ye.latestSelectedDateObj=ye.selectedDates[0],ye.redraw(),L(),k(),Ce(t),t&&ge("onChange")},ye.toggle=function(){if(ye.isOpen)return ye.close();ye.open()},ye.element=ye.input=c,ye.isOpen=!1,ae(),ie(),fe(),se(),m(),ye.isMobile||H(),_(),(ye.selectedDates.length||ye.config.noCalendar)&&(ye.config.enableTime&&k(ye.config.noCalendar?ye.latestSelectedDateObj||ye.config.minDate:void 0),Ce(!1)),ye.showTimeInput=ye.selectedDates.length>0||ye.config.noCalendar,void 0!==ye.weekWrapper&&void 0!==ye.daysContainer&&(ye.calendarContainer.style.width=ye.daysContainer.offsetWidth+ye.weekWrapper.offsetWidth+"px"),ye.isMobile||oe(),ge("onReady"),ye}function d(e,t){for(var n=Array.prototype.slice.call(e),a=[],i=0;i<n.length;i++){var o=n[i];try{if(null!==o.getAttribute("data-fp-omit"))continue;void 0!==o._flatpickr&&(o._flatpickr.destroy(),o._flatpickr=void 0),o._flatpickr=c(o,t||{}),a.push(o._flatpickr)}catch(e){console.warn(e,e.stack)}}return 1===a.length?a[0]:a}var s=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},u=function(e,t,n){return n.months[t?"shorthand":"longhand"][e]},f={DAY:864e5},m={_disable:[],_enable:[],allowInput:!1,altFormat:"F j, Y",altInput:!1,altInputClass:"form-control input",animate:window&&-1===window.navigator.userAgent.indexOf("MSIE"),ariaDateFormat:"F j, Y",clickOpens:!0,closeOnSelect:!0,conjunction:", ",dateFormat:"Y-m-d",defaultHour:12,defaultMinute:0,defaultSeconds:0,disable:[],disableMobile:!1,enable:[],enableSeconds:!1,enableTime:!1,getWeek:function(e){var t=new Date(e.getFullYear(),0,1);return Math.ceil(((e.getTime()-t.getTime())/864e5+t.getDay()+1)/7)},hourIncrement:1,ignoredFocusElements:[],inline:!1,locale:"default",minuteIncrement:5,mode:"single",nextArrow:"<svg version='1.1' xmlns='/www.w3.org/2000/svg' xmlns:xlink='/www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",noCalendar:!1,onChange:[],onClose:[],onDayCreate:[],onDestroy:[],onKeyDown:[],onMonthChange:[],onOpen:[],onParseConfig:[],onReady:[],onValueUpdate:[],onYearChange:[],plugins:[],position:"auto",positionElement:void 0,prevArrow:"<svg version='1.1' xmlns='/www.w3.org/2000/svg' xmlns:xlink='/www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",shorthandCurrentMonth:!1,static:!1,time_24hr:!1,weekNumbers:!1,wrap:!1},g={weekdays:{shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],longhand:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},months:{shorthand:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],longhand:["January","February","March","April","May","June","July","August","September","October","November","December"]},daysInMonth:[31,28,31,30,31,30,31,31,30,31,30,31],firstDayOfWeek:0,ordinal:function(e){var t=e%100;if(t>3&&t<21)return"th";switch(t%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},rangeSeparator:" to ",weekAbbreviation:"Wk",scrollTitle:"Scroll to increment",toggleTitle:"Click to toggle",amPM:["AM","PM"]},p=function(e){return("0"+e).slice(-2)},h=function(e){return!0===e?1:0},v=function(e){return e instanceof Array?e:[e]},D=function(){},C={D:D,F:function(e,t,n){e.setMonth(n.months.longhand.indexOf(t))},G:function(e,t){e.setHours(parseFloat(t))},H:function(e,t){e.setHours(parseFloat(t))},J:function(e,t){e.setDate(parseFloat(t))},K:function(e,t){e.setHours(e.getHours()%12+12*h(/pm/i.test(t)))},M:function(e,t,n){e.setMonth(n.months.shorthand.indexOf(t))},S:function(e,t){e.setSeconds(parseFloat(t))},U:function(e,t){return new Date(1e3*parseFloat(t))},W:function(e,t){var n=parseInt(t);return new Date(e.getFullYear(),0,2+7*(n-1),0,0,0,0)},Y:function(e,t){e.setFullYear(parseFloat(t))},Z:function(e,t){return new Date(t)},d:function(e,t){e.setDate(parseFloat(t))},h:function(e,t){e.setHours(parseFloat(t))},i:function(e,t){e.setMinutes(parseFloat(t))},j:function(e,t){e.setDate(parseFloat(t))},l:D,m:function(e,t){e.setMonth(parseFloat(t)-1)},n:function(e,t){e.setMonth(parseFloat(t)-1)},s:function(e,t){e.setSeconds(parseFloat(t))},w:D,y:function(e,t){e.setFullYear(2e3+parseFloat(t))}},w={D:"(\\w+)",F:"(\\w+)",G:"(\\d\\d|\\d)",H:"(\\d\\d|\\d)",J:"(\\d\\d|\\d)\\w+",K:"(am|AM|Am|aM|pm|PM|Pm|pM)",M:"(\\w+)",S:"(\\d\\d|\\d)",U:"(.+)",W:"(\\d\\d|\\d)",Y:"(\\d{4})",Z:"(.+)",d:"(\\d\\d|\\d)",h:"(\\d\\d|\\d)",i:"(\\d\\d|\\d)",j:"(\\d\\d|\\d)",l:"(\\w+)",m:"(\\d\\d|\\d)",n:"(\\d\\d|\\d)",s:"(\\d\\d|\\d)",w:"(\\d\\d|\\d)",y:"(\\d{2})"},M={Z:function(e){return e.toISOString()},D:function(e,t,n){return t.weekdays.shorthand[M.w(e,t,n)]},F:function(e,t,n){return u(M.n(e,t,n)-1,!1,t)},G:function(e,t,n){return p(M.h(e,t,n))},H:function(e){return p(e.getHours())},J:function(e,t){return void 0!==t.ordinal?e.getDate()+t.ordinal(e.getDate()):e.getDate()},K:function(e){return e.getHours()>11?"PM":"AM"},M:function(e,t){return u(e.getMonth(),!0,t)},S:function(e){return p(e.getSeconds())},U:function(e){return e.getTime()/1e3},W:function(e,t,n){return n.getWeek(e)},Y:function(e){return e.getFullYear()},d:function(e){return p(e.getDate())},h:function(e){return e.getHours()%12?e.getHours()%12:12},i:function(e){return p(e.getMinutes())},j:function(e){return e.getDate()},l:function(e,t){return t.weekdays.longhand[e.getDay()]},m:function(e){return p(e.getMonth()+1)},n:function(e){return e.getMonth()+1},s:function(e){return e.getSeconds()},w:function(e){return e.getDay()},y:function(e){return String(e.getFullYear()).substring(2)}};"undefined"!=typeof HTMLElement&&(HTMLCollection.prototype.flatpickr=NodeList.prototype.flatpickr=function(e){return d(this,e)},HTMLElement.prototype.flatpickr=function(e){return d([this],e)});var b;return b=function(e,t){return e instanceof NodeList?d(e,t):"string"==typeof e?d(window.document.querySelectorAll(e),t):d([e],t)},window.flatpickr=b,b.defaultConfig=m,b.l10ns={en:s({},g),default:s({},g)},b.localize=function(e){b.l10ns.default=s({},b.l10ns.default,e)},b.setDefaults=function(e){b.defaultConfig=s({},b.defaultConfig,e)},"undefined"!=typeof jQuery&&(jQuery.fn.flatpickr=function(e){return d(this,e)}),Date.prototype.fp_incr=function(e){return new Date(this.getFullYear(),this.getMonth(),this.getDate()+("string"==typeof e?parseInt(e,10):e))},b});