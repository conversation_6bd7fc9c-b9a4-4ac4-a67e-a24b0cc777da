<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- select2 -->
    <link href="/static/CSS/select2/css/select2.min.css" rel="stylesheet" />
    <link href="/static/CSS/select2/css/select2-bootstrap4.css" rel="stylesheet" />
    <!-- daterangepicker -->
    <link rel="stylesheet" href="/static/JavaScript/daterangepicker/daterangepicker.min.css">
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
    
    <style>
        .collapse-content {
          max-height: 9rem; /* 3行的高度，根据字体大小调整 */
          overflow: hidden;
          transition: max-height 0.3s ease;
        }
      
        .collapse-content.expanded {
          max-height: 70rem; /* 4行以上内容时的最大高度，根据实际内容调整 PC12就够 App*4 */
        }

        /* 屏蔽原来的focus样式 */
        .btn-no-focus:focus {
            outline: none; /* 移除轮廓 */
            box-shadow: none; /* 移除阴影 */
            color: #fff; /* 文字颜色 */
            background-color: #0075ff; /* 背景颜色 */
            border-color: rgb(255 255 255 / 3%); /* 边框颜色 */
        }

        /* 定义按钮选中时的样式 */
        .btn-active {
            background-color: #0075ff;
        }
        
        .select2-container--bootstrap4 .select2-selection--single {
            font-size: 0.875rem;
        }

        .pagination li.disabled a {
            pointer-events: none;
            cursor: default;
        }

        .extra {
            position: absolute;
            top: -1rem;
            right: 15px;
            font-weight: 400;
            color: #0960bd;
            cursor: pointer;
        }

        .details_info {
            padding-left: 10px;
            padding-right: 10px; 
            max-width:15rem;
            white-space:nowrap;
			overflow:hidden;
			text-overflow:ellipsis;
			box-sizing:border-box;
            display: flex;
            align-items: center; /* 垂直居中 */
        }

        .details_info .span_title_info {
            font-size: 14px;
            font-family: arial, sans-serif;
        }

        .details_info .span_content_info {
            font-size: 14px;
            font-family: arial, sans-serif;
            overflow: hidden; /* 隐藏超出部分 */
            text-overflow: ellipsis; /* 显示省略号 */
            margin-right: 10px; /* 可以根据需要调整间距 */
        }
        
        
    </style>
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
                    <div class="card">
						<div class="card-body">
                            <div class="collapse-content" id="collapseContent">
                                <div class="form-body">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <h4 id="Case_Name_Element">
                                                方案_默认
                                                <i class="fadeIn animated bx bx-edit" data-toggle="tooltip" data-placement="top" title="方案编辑" id="Editor_Case_Name_Element"></i>
                                                <i class="fadeIn animated bx bx-list-ul" data-toggle="tooltip" data-placement="top" title="方案列表" id="Show_Case_Name_Element"></i>
                                            </h4>
                                        </div>
                                        <div class="col-sm-6" id="View_Show_Element">
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-2" style="float: right;"><i class="lni lni-indent-decrease mr-1" style="font-size:0.875rem;"></i>OCR视图</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-2 btn-active" style="float: right;"><i class="lni lni-menu mr-1" style="font-size:0.875rem;"></i>普通视图</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-2" style="float: right;"><i class="lni lni-list mr-1" style="font-size:0.875rem;"></i>精简视图</button>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">时间范围:</label>
                                        <div class="col-sm-10" id="Sentiment_Public_Time_Element" style="display: flex;align-items: center;flex-wrap: wrap;">
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="TwoDay">24小时</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="Today">今天</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="Yesterday">昨天</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="ThreeDay">近三天</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="WeekDay">近七天</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="MouthDay">近30天</button>
                                            <button type="button" class="btn btn-light btn-sm btn-no-focus m-1 px-4" value="Other" id="Diy_Public_Time_Element">自定义</button>
                                            <div class="px-2">
                                                <input type="text" id="diy_time_list" class="form-control" placeholder="点击选择日期范围" style="display: none;font-size:0.875rem;width:240px">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">媒体类型:</label>
                                        <div class="col-sm-11" id="Sentiment_Source_Type_Element">
                                            <div class="form-check form-check-inline col-form-label">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_1" value="All">
                                                <label class="form-check-label" for="Source_Type_1">全部(<span id="Label_Source_Type_1">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_2" value="网页">
                                                <label class="form-check-label" for="Source_Type_2">网页(<span id="Label_Source_Type_2">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_3" value="微博">
                                                <label class="form-check-label" for="Source_Type_3">微博(<span id="Label_Source_Type_3">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_4" value="APP">
                                                <label class="form-check-label" for="Source_Type_4">APP(<span id="Label_Source_Type_4">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_5" value="论坛">
                                                <label class="form-check-label" for="Source_Type_5">论坛(<span id="Label_Source_Type_5">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_6" value="报刊">
                                                <label class="form-check-label" for="Source_Type_6">报刊(<span id="Label_Source_Type_6">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_7" value="视频">
                                                <label class="form-check-label" for="Source_Type_7">视频(<span id="Label_Source_Type_7">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_8" value="头条">
                                                <label class="form-check-label" for="Source_Type_8">头条(<span id="Label_Source_Type_8">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_9" value="搜狐">
                                                <label class="form-check-label" for="Source_Type_9">搜狐(<span id="Label_Source_Type_9">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_10" value="问答">
                                                <label class="form-check-label" for="Source_Type_10">问答(<span id="Label_Source_Type_10">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_11" value="评论">
                                                <label class="form-check-label" for="Source_Type_11">评论(<span id="Label_Source_Type_11">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_12" value="公众号">
                                                <label class="form-check-label" for="Source_Type_12">公众号(<span id="Label_Source_Type_12">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Type_13" value="其他类型">
                                                <label class="form-check-label" for="Source_Type_13">其他类型(<span id="Label_Source_Type_13">0</span>)</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">情感属性:</label>
                                        <div class="col-sm-10" id="Sentiment_Emotion_Type_Element">
                                            <div class="form-check form-check-inline col-form-label">
                                                <input class="form-check-input" type="checkbox" id="Emotion_Type_1" value="All">
                                                <label class="form-check-label" for="Emotion_Type_1">全部(<span id="Label_Emotion_Type_1">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Emotion_Type_2" value="正面">
                                                <label class="form-check-label" for="Emotion_Type_2">正面(<span id="Label_Emotion_Type_2">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Emotion_Type_3" value="中性">
                                                <label class="form-check-label" for="Emotion_Type_3">中性(<span id="Label_Emotion_Type_3">0</span>)</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Emotion_Type_4" value="负面">
                                                <label class="form-check-label" for="Emotion_Type_4">负面(<span id="Label_Emotion_Type_4">0</span>)</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">多媒属性:</label>
                                        <div class="col-sm-10" id="Sentiment_Media_Type_Element">
                                            <div class="form-check form-check-inline col-form-label">
                                                <input class="form-check-input" type="checkbox" id="Source_Media_Type_1" value="All">
                                                <label class="form-check-label" for="Source_Media_Type_1">全部</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Media_Type_2" value="音频">
                                                <label class="form-check-label" for="Source_Media_Type_2">音频</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Media_Type_3" value="视频">
                                                <label class="form-check-label" for="Source_Media_Type_3">视频</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Source_Media_Type_4" value="广播">
                                                <label class="form-check-label" for="Source_Media_Type_4">广播</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">重点数源:</label>
                                        <div class="col-sm-10" id="Sentiment_Monitor_Type_Element">
                                            <div class="form-check form-check-inline col-form-label">
                                                <input class="form-check-input" type="checkbox" id="Monitor_Type_1" value="All">
                                                <label class="form-check-label" for="Monitor_Type_1">全部</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Monitor_Type_2" value="重点关注">
                                                <label class="form-check-label" for="Monitor_Type_2">重点关注</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="Monitor_Type_3" value="不看屏蔽数源">
                                                <label class="form-check-label" for="Monitor_Type_3">不看屏蔽数源</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">推送条数:</label>
                                        <div class="col-sm-11" id="Sentiment_Show_Count_Element">
                                            <div class="form-check form-check-inline col-form-label">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_1" value="All">
                                                <label class="form-check-label" for="Source_Show_Count_1">全部</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_2" value="100">
                                                <label class="form-check-label" for="Source_Show_Count_2">100条</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_3" value="200">
                                                <label class="form-check-label" for="Source_Show_Count_3">200条</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_4" value="500">
                                                <label class="form-check-label" for="Source_Show_Count_4">500条</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_5" value="1000">
                                                <label class="form-check-label" for="Source_Show_Count_5">1000条</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_6" value="2000">
                                                <label class="form-check-label" for="Source_Show_Count_6">2000条</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="exampleRadios" id="Source_Show_Count_7" value="5000">
                                                <label class="form-check-label" for="Source_Show_Count_7">5000条</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">发布地区:</label>
                                        <div class="col-sm-2">
                                            <select class="single-select" id="Sentiment_Public_Area_Element"></select>
                                        </div>
                                        <label class="col-form-label" style="font-size: 1rem;">IP属地:</label>
                                        <div class="col-sm-2">
                                            <select class="single-select" id="Sentiment_Public_IP_Element"></select>
                                        </div>
                                        <label class="col-form-label" style="font-size: 1rem;">舆情类别:</label>
                                        <div class="col-sm-2">
                                            <select class="single-select" id="Sentiment_Unit_Type_Element">
                                                <option value="全部">全部</option>
                                                <option value="新闻媒体">新闻媒体</option>
                                                <option value="资讯媒体">资讯媒体</option>
                                                <option value="企业">企业</option>
                                                <option value="政务">政务</option>
                                                <option value="组织">组织</option>
                                                <option value="个人">个人</option>
                                                <option value="其他">其他</option>
                                            </select>
                                        </div>
                                        <label class="col-form-label" style="font-size: 1rem;">文章类别:</label>
                                        <div class="col-sm-2">
                                            <select class="single-select" id="Sentiment_Article_Type_Element">
                                                <option value="全部">全部</option>
                                                <option value="社会">社会</option>
                                                <option value="经济">经济</option>
                                                <option value="财经">财经</option>
                                                <option value="体育">体育</option>
                                                <option value="教育">教育</option>
                                                <option value="科技">科技</option>
                                                <option value="娱乐">娱乐</option>
                                                <option value="公益">公益</option>
                                                <option value="时政">时政</option>
                                                <option value="军事">军事</option>
                                                <option value="文化">文化</option>
                                                <option value="汽车">汽车</option>
                                                <option value="能源">能源</option>
                                                <option value="旅行">旅行</option>
                                                <option value="女性">女性</option>
                                                <option value="动漫">动漫</option>
                                                <option value="农业">农业</option>
                                                <option value="银行">银行</option>
                                                <option value="其他">其他</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-success btn-sm m-1 px-4" onclick="Save_Show_Table_Params()" >保存</button>
                                <button type="button" class="btn btn-light btn-sm m-1 px-4" onclick="toggleCollapse()" id="ToggleCollapseButton" style="float: right;">展开<i class="fadeIn animated bx bx-chevrons-down" id="ToggleCollapseIcon"></i></button>
                                <button type="button" class="btn btn-info btn-sm m-1 px-4" onclick="Requests_Sentiment_Info()" style="float: right;">筛选</button>
                                <button type="button" class="btn btn-warning btn-sm m-1 px-4" style="float: right;" onclick="Init_Show_Table_Params('reload')">重置</button>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-5">
                                    <div class="form-check form-check-inline col-form-label">
                                        <input class="form-check-input" type="checkbox" id="Article_Select_All" value="全选">
                                        <label class="form-check-label" for="Article_Select_All">全选</label>
                                    </div>
                                    <i class='fadeIn animated bx bx-star font-20 mx-2' data-toggle="tooltip" data-placement="top" title="批量收藏"></i>
                                    <i class='fadeIn animated bx bx-folder font-20 mx-2' data-toggle="tooltip" data-placement="top" title="批量加入素材库"></i>
                                    <i class='fadeIn animated bx bx-bell font-20 mx-2' data-toggle="tooltip" data-placement="top" title="批量报警"></i>
                                    <i class="fadeIn animated bx bx-download font-20 mx-2" data-toggle="tooltip" data-placement="top" title="批量下载"></i>
                                </div>
                                <div class="col-sm-4">
                                    <button type="button" class="btn btn-light btn-sm m-1 px-2 mr-2" style="float: right;"  onclick="Reload_Sentiment_Hander()"><i class="fadeIn animated bx bx-refresh mr-1" style="font-size:0.875rem;"></i>本地刷新</button>
                                    <button type="button" class="btn btn-light btn-sm m-1 px-2 ml-2" style="float: right;"> <i class="fadeIn animated bx bx-download mr-1" style="font-size:0.875rem;"></i>全部下载</button>
                                    <div class="form-check form-check-inline col-form-label" style="float: right;">
                                        <label class="form-check-label" id="Article_Loading_Count">共0条消息</label>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <select class="form-control" id="Article_Chioce_Element">
                                                <option value="All" selected>全部</option>
                                                <option value="Keyword">关键词</option>
                                                <option value="Title">标题</option>
                                                <option value="Content">内容</option>
                                                <option value="Source">来源</option>
                                                <option value="Link">链接</option>
                                            </select>
                                        </div>
                                        <input type="text" class="form-control" placeholder="信息标题/内容/来源查询" id="Article_Chioce_Input_Element">
                                        <div id="Article_Chioce_Select_Element" style="display:none;position: relative;-ms-flex: 1 1 auto;flex: 1 1 auto;width: 1%;min-width: 0;margin-bottom: 0;">
                                            <select class="single-select" id="Article_Chioce_Select_Value_Element">
                                            </select>
                                        </div>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-light" id="Article_Chioce_Search_Element">
                                                <i class="fadeIn animated bx bx-search-alt-2"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            
                            </div>
                            <hr>
                            <div id="Article_Body_Element">
                            </div>
                            <nav aria-label="Page navigation example">
								<ul class="pagination justify-content-end">
								</ul>
							</nav>
                        </div>
                    </div>
				</div>
			</div>
		</div>
		<div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
	</div>
    <div class="modal fade" id="loadingModal" backdrop="static" keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div style="width: 200px;height:20px; z-index: 20000; position: absolute; text-align: center; left: 50%; top: 50%;margin-left:-100px;margin-top:-10px">
                <div class="text-center">
                    <div class="spinner-border text-info spinner-border-sm" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                    <strong style="color:#198fed">Loading...</strong>
                </div>
            </div>   
        </div>
    </div>
    <div class="modal fade" id="Detail_Case_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticCaseBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h3 class="modal-title" id="staticCaseBackdropLabel">方案列表</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body" id="staticCaseBackdropContent">
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
              <button type="button" class="btn btn-light" id="Editor_Add_Case_Save_Button">保存</button>
            </div>
          </div>
        </div>
    </div>
    <div class="modal fade" id="Detail_Keyword_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticKeywordBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticKeywordBackdropLabel">方案编辑</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            方案名称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="请输入方案名称" id="Keyword_USER_NAME">
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            所属分类
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <select class="single-select" id="Keyword_USER_TYPE">
                                <option value="时事新闻">时事新闻</option>
                                <option value="聚焦热点">聚焦热点</option>
                                <option value="涉政信息">涉政信息</option>
                                <option value="涉邪消息">涉邪消息</option>
                                <option value="反动言论">反动言论</option>
                                <option value="重点人物">重点人物</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            关键词
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <textarea class="form-control" placeholder="请输入关键词，多个关键词使用中英文逗号','隔开。" rows="3" cols="3" id="Keyword_KEYWORD_INFO"></textarea>
                    </div>
                    <div class="form-group">
                        <label>特征词</label>
                        <textarea class="form-control" placeholder="请输入特征词，多个特征词使用中英文逗号','隔开，特征词与关键词是与关系，特征词之间是或关系。" rows="3" cols="3" id="Keyword_FEATUREWORD_INFO"></textarea>
                    </div>
                    <div class="form-group">
                        <label>停用词</label>
                        <textarea class="form-control" placeholder="请输入停用词，多个停用词使用中英文逗号','隔开，停用词与关键词是与关系，停用词之间是或关系。" rows="3" cols="3" id="Keyword_STOPWORD_INFO"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_Keyword_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
    <!-- select2 -->
    <script src="/static/JavaScript/select2/js/select2.min.js"></script>
    <!-- daterangepicker -->
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/daterangepicker/jquery.daterangepicker.min.js"></script>

	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Details.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Sentiment_Info();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>