import sys
import numpy as np
import librosa
from PySide6.QtWidgets import <PERSON>A<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QFileDialog
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QPainter, QPen, QColor

class PowerSpectrumWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setMinimumSize(800, 200)
        self.audio_data = np.zeros(1024)  # 默认空数据
        self.sample_rate = 44100
        self.frequencies = np.fft.rfftfreq(len(self.audio_data), d=1.0 / self.sample_rate)
        self.power_spectrum = np.zeros_like(self.frequencies)

    def set_audio_data(self, data, sample_rate):
        """设置音频数据并重新绘制"""
        self.audio_data = data
        self.sample_rate = sample_rate
        self.frequencies = np.fft.rfftfreq(len(self.audio_data), d=1.0 / self.sample_rate)
        self.power_spectrum = np.abs(np.fft.rfft(self.audio_data)) ** 2
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景
        painter.fillRect(self.rect(), QColor(20, 25, 30))

        if len(self.audio_data) == 0:
            return

        # 绘制功率谱
        width, height = self.width(), self.height()
        max_power = np.max(self.power_spectrum) or 1

        painter.setPen(QPen(QColor(0, 180, 255), 2, Qt.SolidLine))

        path = []
        for i, power in enumerate(self.power_spectrum):
            x = int(i * width / len(self.power_spectrum))
            y = height - int(power / max_power * height)
            path.append((x, y))

        for i in range(len(path) - 1):
            painter.drawLine(path[i][0], path[i][1], path[i + 1][0], path[i + 1][1])


class AudioAnalyzer(QThread):
    update_signal = Signal(np.ndarray)
    finished_signal = Signal()

    def __init__(self, file_path, sample_rate=44100, chunk_size=1024):
        super().__init__()
        self.file_path = file_path
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.running = False

    def run(self):
        self.running = True
        try:
            # 使用librosa加载音频文件
            y, sr = librosa.load(self.file_path, sr=self.sample_rate, mono=True)

            # 分块处理音频数据
            for i in range(0, len(y), self.chunk_size):
                if not self.running:
                    break

                chunk = y[i:i + self.chunk_size]
                if len(chunk) < self.chunk_size:
                    chunk = np.pad(chunk, (0, self.chunk_size - len(chunk)))

                self.update_signal.emit(chunk)
                QThread.msleep(int(self.chunk_size / self.sample_rate * 1000))

            self.finished_signal.emit()

        except Exception as e:
            print(f"音频分析错误: {e}")

    def stop(self):
        self.running = False
        self.wait()


class AudioWaveformApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("音频功率谱显示器")
        self.setGeometry(100, 100, 900, 400)

        # 主控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建功率谱显示
        self.power_spectrum = PowerSpectrumWidget()
        layout.addWidget(self.power_spectrum)

        # 控制按钮
        btn_load = QPushButton("加载音频文件")
        btn_load.clicked.connect(self.load_audio)
        layout.addWidget(btn_load, alignment=Qt.AlignCenter)

        # 音频分析线程
        self.audio_analyzer = None

    def load_audio(self):
        """加载音频文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "选择音频文件", "", "音频文件 (*.wav *.mp3)"
        )

        if file_path:
            if self.audio_analyzer and self.audio_analyzer.isRunning():
                self.audio_analyzer.stop()

            self.audio_analyzer = AudioAnalyzer(file_path)
            self.audio_analyzer.update_signal.connect(self.update_power_spectrum)
            self.audio_analyzer.finished_signal.connect(self.on_analysis_finished)
            self.audio_analyzer.start()

    def update_power_spectrum(self, chunk):
        self.power_spectrum.set_audio_data(chunk, self.audio_analyzer.sample_rate)

    def on_analysis_finished(self):
        print("音频分析完成")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = AudioWaveformApp()
    window.show()
    sys.exit(app.exec())