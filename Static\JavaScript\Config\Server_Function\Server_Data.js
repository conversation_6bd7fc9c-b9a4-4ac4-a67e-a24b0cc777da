var User_Token = localStorage.getItem("User_Token");
const queryString = window.location.search;
const params = new URLSearchParams(queryString);
const Page = params.get("Page");
if (!User_Token && Page != "Page_Login") {
    window.location.href = "/Service_Page?Page=Page_Login";
}
class Server_Data {
    constructor(class_type,class_data){
        this.class_type = class_type;
        this.class_data = class_data;
        this.Request_Base_Info = {
            Base_URL: "http://sccs.csc-tech.cn:8746/Service_Interface",
            Base_Transfer_URL: "http://sccs.csc-tech.cn:8746/CSC_Transfer",
            Async_Status: true,  // 是否异步请求 false 同步 true 异步
        };
    };
    // 原始请求 为了兼容未修改前的ajax请求
    run(){
        try {
            this.DATA_INIT_CONFIG()
        } catch (error) {
            return {'Error': error, "Type": this.class_type, "Data": this.class_data, "Status": "Failed"}
        }
        try {

            return eval(`this.${this.class_type}()`);
        } catch (error) {
            return {'Error': error, "Type": this.class_type, "Data": this.class_data, "Status": "Failed"}
        }
    };

    // 请求数据 (旧)
    Service_Requests_Data (){
        var Sentinel_BaseConfig ={}
        // console.log('Service_Requests_Data this.data:',this.class_data)
        let Requests_Data = {
            "user_id": "",
            "user_token":this.class_data.User_Token ,
            "data_class": this.class_data.data_class,
            "data_type": this.class_data.data_type,
            "data_methods": this.class_data.data_methods,
            "data_argument": this.class_data.data_argument,
            "data_kwargs":{}
        }
        try {
            for (let Key in this.class_data) {
                // const element = array[index];
                if (['Data_Name','User_Token','data_class','data_type','data_methods','data_argument'].indexOf(Key) !== -1 ) {
                    //
                }else{
                    Requests_Data.data_kwargs[Key] = this.class_data[Key]
                }
            }

        } catch (error) {
            console.log('error:',error)

        }
        Requests_Data.data_kwargs = JSON.stringify(Requests_Data.data_kwargs)
        // console.log('Requests_Data.data_argument:',typeof(Requests_Data.data_argument),Requests_Data.data_argument.length,Requests_Data.data_argument)
        if ( Object.keys(Requests_Data.data_argument).length === 0 ) {
            // console.log('直接访问')
            var Requests_Url = this.Request_Base_Info.Base_URL
        } else {
            // console.log('路由跳转')
            Requests_Data.user_id = '1'
            Requests_Data.data_argument = JSON.stringify(Requests_Data.data_argument)
            var Requests_Url = this.Request_Base_Info.Base_Transfer_URL
        }
        $.ajax({
            type: "POST",
            url: Requests_Url,
            timeout:30000,
             // 发送同步请求 false  异步true
            async: false,
            contentType: 'application/json',
            processData: false,
            data: JSON.stringify(Requests_Data),
            success: function (Responese) {
                // console.log('Service_Requests_Data Responese:',Responese)
                Sentinel_BaseConfig = Responese
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log('jqXHR:',jqXHR,textStatus,errorThrown)
                Sentinel_BaseConfig = {'Status':'Error'}
                // return Sentinel_BaseConfig
            }
        });
        return Sentinel_BaseConfig
    };
    // 获取基本配置 (旧)
    DATA_INIT_CONFIG(){
        // console.log('this.class_data:',this.class_data)
        var __class_data = this.class_data
        // console.log('DATA_INIT_CONFIG __class_data',__class_data)
        // 直接获取token 这样html访问就不用传递了
        // __class_data['User_Token'] = localStorage.getItem('User_Token')
        // console.log('DATA_INIT_CONFIG __class_data2',__class_data)
        let Requests_Data = {
            "user_id": "",
            "user_token": __class_data?.User_Token,
            "data_class": "Sentinel",
            "data_type": 'Service_Data',
            "data_methods": "return_web_function_info",
            "data_argument": `{}`,
            "data_kwargs": {'Function_Name':__class_data['Data_Name']}
        }
        // console.log('Requests_Data:',Requests_Data,)
        $.ajax({
            type: "POST",
            url: this.Request_Base_Info.Base_URL,
            // 发送同步请求
            contentType: 'application/json',
            processData: false,
            data: JSON.stringify(Requests_Data),
            async: false,
            success: function (Responese) {
                // console.log('return_web_function_info Responese:',Responese)
                if (Responese['Status'] === 'Success') {
                    __class_data['data_class']   = Responese['Web_Function_Info']['data_class']
                    __class_data['data_type']    = Responese['Web_Function_Info']['data_type']
                    __class_data['data_methods'] = Responese['Web_Function_Info']['data_methods']
                    __class_data['data_argument'] = Responese['Web_Function_Info']['data_argument']
                    __class_data['user_id'] = Responese['Web_Function_Info']['user_id']
                } else if (Responese['Status'] === 'NoVerify') {
                    console.log('Token验证失败，请检查！！！')
                } else {
                    console.log('获取请求基本信息出错')
                }
            }
        });
        this.class_data  = __class_data
    };

    async async_run() {
        try {
            // 数据访问基础数据组装
            const Responese = await this.DATA_INIT_CONFIG_Async();
            // console.log('Responese:',Responese)
            if (Responese['Status'] === 'Success') {
                this.class_data['data_class']    = Responese['Web_Function_Info']['data_class']
                this.class_data['data_type']     = Responese['Web_Function_Info']['data_type']
                this.class_data['data_methods']  = Responese['Web_Function_Info']['data_methods']
                this.class_data['data_argument'] = Responese['Web_Function_Info']['data_argument']
                this.class_data['user_id'] = Responese['Web_Function_Info']['user_id']
            } else if (Responese['Status'] === 'NoVerify') {
                // console.log('Token验证失败，请检查！！！')
                notify({
                    message: Responese['message'],
                    type: 'danger',
                    duration: 3000,
                })
                window.location.href = "/Service_Page?Page=Page_Login"
                return {'Error': 'Token Failure', "Type": this.class_type, "Data": this.class_data, "Status": "Failed"}
            } else {
                // console.log('获取请求基本信息出错')
                return {'Error': 'Network Error', "Type": this.class_type, "Data": this.class_data, "Status": "Failed"}
            }
            // 最终返回的数据
            const Sentinel_Response = await this.Service_Requests_Data_Async();
            // console.log('Sentinel_Response:',Sentinel_Response)
            return Sentinel_Response;
        } catch (error) {
            // 处理异步请求中的错误
            console.error('异步请求失败:', error);
            return {'Error': error, "Type": this.class_type, "Data": this.class_data, "Status": "Failed"}
        }
    };

    Service_Requests_Data_Async () {
        // console.log("run Service_Requests_Data")
        let Requests_Data = {
            "user_id": this.class_data.user_id,
            "user_token":this.class_data.User_Token,
            "data_class": this.class_data.data_class,
            "data_type": this.class_data.data_type,
            "data_methods": this.class_data.data_methods,
            "data_argument": this.class_data.data_argument,
            "data_kwargs":{}
        };
        try {
            Object.keys(this.class_data).forEach(key => {
                // console.log(`Key: ${key}, Value: ${this.class_data}`);
                if (['Data_Name','User_Token','data_class','data_type','data_methods','data_argument','Data_Async'].includes(key)) {
                    //
                }else{
                    Requests_Data.data_kwargs[key] = this.class_data[key]
                }
            });
        } catch (error) {
            console.log('组装data_kwargs error:',error)
        };
        const Service_Axios_Options = {
            type: 'POST',  // 请求类型
            dataType: 'json', // 期望的响应数据类型
            contentType: 'application/json', // 设置上传内容类型为 JSON
            async: this.Request_Base_Info.Async_Status, // 是否异步请求 false 同步 true 异步
            data: JSON.stringify(Requests_Data)
        }
        // Object.keys将字典Key值组装成一个列表返回
        if (Object.keys(Requests_Data.data_argument).length === 0 ) {
            return this.Request_Interface($.extend({}, Service_Axios_Options, { url:this.Request_Base_Info.Base_URL }));
        } else{
            return this.Request_Interface($.extend({}, Service_Axios_Options, { url:this.Request_Base_Info.Base_Transfer_URL }));
        }
    };

    DATA_INIT_CONFIG_Async() {
        // const url = this.Request_Base_Info.Base_URL
        const Config_Axios_Options = {
            type: 'POST',  // 请求类型
            dataType: 'json', // 期望的响应数据类型
            contentType: 'application/json', // 设置上传内容类型为 JSON
            async: this.Request_Base_Info.Async_Status, // 是否异步请求 false 同步 true 异步
            data: JSON.stringify(
                {
                    "user_id": "",
                    "user_token":this.class_data.User_Token,
                    "data_class": "Sentinel",
                    "data_type": 'Service_Data',
                    "data_methods": "return_web_function_info",
                    "data_argument": {},
                    "data_kwargs": { 'Function_Name' : this.class_data.Data_Name }
                }
            )
        };
        // console.log('DATA_INIT_CONFIG Config_Axios_Options:',Config_Axios_Options)
        return this.Request_Interface($.extend({}, Config_Axios_Options, { url:this.Request_Base_Info.Base_URL }));
    };

    // 封装 $.ajax 的异步接口
    Request_Interface(options) {
        return $.ajax(options)
            .then(successResponse => {
            // .done(successResponse => {
                // 请求成功，处理响应
                // console.log('请求成功:', successResponse);
                return successResponse;
            })
            .catch(error => {
            // .fail(error => {
                // 请求失败，处理错误
                // console.error('请求失败:', error);
                throw error; // 抛出错误以便调用者可以捕获
            });
    };

}