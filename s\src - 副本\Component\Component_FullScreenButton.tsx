import React from 'react'
import { Button } from 'antd'
import { FullscreenOutlined } from '@ant-design/icons'

interface CircleIconButtonProps {
  icon: React.ReactNode
  onClick?: () => void
}

const FullScreenButton: React.FC<CircleIconButtonProps> = ({ icon, onClick }) => {
  return (
    <Button
      type="text"
      shape="circle"
      icon={icon}
      onClick={onClick}
      style={{
        fontSize: 25,
        color: '#ffffff',
        backgroundColor: 'transparent',
        width: 32,
        height: 32,
        boxSizing: 'border-box',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'all 0.2s ease',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f0f0f0';
        e.currentTarget.style.color = '#1890ff';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.color = '#ffffff';
      }}
    />
  )
}

export default FullScreenButton