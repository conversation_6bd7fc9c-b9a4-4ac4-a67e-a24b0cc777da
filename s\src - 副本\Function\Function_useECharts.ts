import { useRef, useEffect } from 'react'
import { useDebounceFn } from 'ahooks' //防抖
import echarts from '../Utils/Utils_Echarts'

// 使用any类型避免版本兼容性问题
type EChartsOption = any

export function useECharts(
  options: EChartsOption,
  loading: boolean = true,
  theme: 'light' | 'dark' | 'default' = 'default'
) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<any>(null)
  const isInitialized = useRef<boolean>(false)

  const { run: resizeFn } = useDebounceFn(
    () => {
      chartInstance.current?.resize()
    },
    { wait: 200 }
  )

  useEffect(() => {
    initCharts()

    return () => {
      disposeCharts()
    }
  }, [theme])

  useEffect(() => {
    if (loading) return
    setOptions(options)
  }, [loading, options])

  const initCharts = (t = theme) => {
    try {
      const el = chartRef?.current
      if (!el || isInitialized.current) return

      // 先清理已存在的实例
      if (chartInstance.current) {
        try {
          chartInstance.current.dispose()
        } catch (e) {
          console.warn('清理图表实例时出错:', e)
        }
      }

      // 检查echarts是否可用
      if (!echarts || typeof echarts.init !== 'function') {
        console.error('ECharts未正确加载')
        return
      }

      chartInstance.current = echarts.init(el, t)
      isInitialized.current = true

      window.addEventListener('resize', resizeFn)
    } catch (error) {
      console.error('ECharts初始化失败:', error)
      isInitialized.current = false
    }
  }

  const setOptions = (options: EChartsOption) => {
    try {
      if (!chartInstance.current || !isInitialized.current) {
        initCharts()

        if (!chartInstance.current) return
      }

      chartInstance.current?.clear()
      chartInstance.current?.setOption(options)
    } catch (error) {
      console.error('设置图表选项失败:', error)
    }
  }

  const disposeCharts = () => {
    try {
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = null
      }
      window.removeEventListener('resize', resizeFn)
      isInitialized.current = false
    } catch (error) {
      console.warn('清理图表时出错:', error)
    }
  }

  const getInstance = (): echarts.ECharts | null => {
    if (!chartInstance.current) {
      initCharts()
    }
    return chartInstance.current
  }

  return { chartRef, getInstance }
}
