/****************************************图表初始化注册***************************************************/ 
// 初始化今日各时间段采集
var today_sentiment_time_crawl_info = c3.generate( {
    bindto:"#TodaySentimentTimeCrawl", 
    data: {
        x: 'x_seris',
        columns:[
            ["row_data", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            ["x_seris", '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'],
        ], 
        type:"bar", 
        names: {
            row_data: "各时段采集总数"
        }, 
        colors: {
            row_data: "#1d73bd"
        },
        groups:[["row_data"]],
    }
});

// 媒体类型分布
var today_sentiment_platform_type = c3.generate({
    bindto:"#TodaySentimentPlatformType", 
    data: {
        columns: [],
        type : 'pie',
    }
});

//情感属性
var today_sentiment_emotion_info=c3.generate({
    bindto:"#TodaySentimentEmotionInfo",
    data: {
        columns:[["正面", 32], ["中性", 51], ["负面", 87]], 
        type:"donut", 
        colors: {
                正面: "#30b817", 负面: "#dc3545", 中性: "#176eb8"
        }, 
        onclick:function(a, b) {
                console.log("onclick", a, b)
        },
        onmouseover:function(a, b) {
                console.log("onmouseover", a, b)
        },
        onmouseout:function(a, b) {
                console.log("onmouseout", a, b)
        }
    }
});

// 发布地区
var today_sentiment_region_area = c3.generate({
    bindto:"#TodaySentimentRegionArea", 
    data: {
        columns: [],
        type : 'pie',
    }
});

// 发布平台
var today_sentiment_platform = c3.generate({
    bindto:"#TodaySentimentPlatform", 
    data: {
        columns:[], 
        type:"donut", 
    }
});

// IP属地占比
var today_sentiment_region_ip_info = c3.generate({
    bindto:"#TodaySentimentRegionIPInfo", 
    data: {
        columns: [],
        type : 'pie',
    }
});

/****************************************初始化请求***************************************************/ 
function Requests_Sentiment_Info() {
    let Requests_Data = {
        "user_id": "CSC",
        "user_token": "csc13880906610",
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_domestic_visualization',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Sync",Requests_Data);
    Result  = __Service_Requests.callMethod();
    // console.log('Result222:',Result);
    // 初始化今日各时间段采集
    today_sentiment_time_crawl_info.load({
        columns:[
            Result['Today_Sentiment_Time_Crawl']
        ], 
        unload: ['row_data']
    })
    // 媒体类型分布
    today_sentiment_platform_type.load({
        columns:Result['Today_Sentiment_Platform_Type'],
    })
    //情感属性
    today_sentiment_emotion_info.load({
            columns: Result['Today_Sentiment_Emotion_Info'],
    })
    // 发布地区
    today_sentiment_region_area.load({
        columns:Result['Today_Sentiment_Region_Area'],
    })
    // 发布平台
    today_sentiment_platform.load({
        columns:Result['Today_Sentiment_Platform'],
    })
    // IP属地占比
    today_sentiment_region_ip_info.load({
        columns:Result['Today_Sentiment_Region_IP_Info']
    })
    // 中标词分析
    WordCloud.minFontSize = "15px"
    // 计算最大权重
    const maxWeight = Math.max(...Result['Today_Sentiment_Key'].map(item => item[1]));
    // console.log('maxWeight:',maxWeight)
    // 归一化权重，并将其缩放到0到40的范围
    const normalizedList = Result['Today_Sentiment_Key'].map(item => {
        const normalizedWeight = Math.round((item[1] / maxWeight) * 40);
        return [item[0], normalizedWeight]; // 返回包含单词和归一化权重的数组
    });
    WordCloud(document.getElementById('TodaySentimentKey'), { list: normalizedList} );
    // 最新文章
    var mission_active_totle =''
    for (var i = Result['Today_Sentiment_Hot_Article'].length-1;i>=0;i--) {
        var mission_info =  Result['Today_Sentiment_Hot_Article'][i]
        var mission_info_one = `
            <div class="news-list-item no-image">
                <div class="list-content">
                    <h2 class="list-title" style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;"><a href="javascript:;" onclick="cheack_url('${mission_info['Url']}')">${mission_info['Title']}</a></h2>
                    <a href="javascript:;" class="list-author">${mission_info['Platform']}</a> - <span class="list-time">${mission_info['Author']}</span>
                </div>
            </div>
            <div class="form-mini-divider"></div>`
        mission_active_totle += mission_info_one
    }
    var parNode = document.getElementById("TodaySentimentHotArticle"); //定位到div上
    parNode.innerHTML = mission_active_totle
    $('.sweet-loader').removeClass('show');
    $('.sweet-loader').attr('style','');
};
/****************************************界面数据刷新***************************************************/ 
function Refresh_Chart_Info() {
    $('.sweet-loader').show().addClass('show');
    Requests_Domestic_Visualization_Info();
};

/****************************************链接跳转***************************************************/ 
function cheack_url(url) {
    window.open(url);
};