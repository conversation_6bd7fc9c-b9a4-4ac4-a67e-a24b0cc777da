# Page_Login.tsx 类型错误修复

## 问题描述
TypeScript编译错误：`data_kwargs` 属性类型不匹配
- 期望类型：`string`
- 实际类型：`{ User_Name: string; User_Password: string; }`

## 修复方案
将 `data_kwargs` 对象转换为JSON字符串：

### 修复前
```typescript
"data_kwargs": {"User_Name": username, "User_Password": password},
```

### 修复后
```typescript
"data_kwargs": JSON.stringify({"User_Name": username, "User_Password": password}),
```

## 修复结果
✅ TypeScript编译错误已解决
✅ 保持与原始JavaScript代码的功能一致性
✅ 符合RequestsData接口的类型要求

现在Page_Login组件可以正常编译和运行。
