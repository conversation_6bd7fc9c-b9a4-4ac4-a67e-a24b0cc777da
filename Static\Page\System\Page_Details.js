/****************************************初始化变量***************************************************/ 
// 中国地区列表
const China_Map_List = [
    '全国',
    '北京',
    '天津',
    '上海',
    '重庆',
    '河北',
    '山西',
    '辽宁',
    '吉林',
    '黑龙江',
    '江苏',
    '浙江',
    '安徽',
    '福建',
    '江西',
    '山东',
    '河南',
    '湖北',
    '湖南',
    '广东',
    '海南',
    '四川',
    '贵州',
    '云南',
    '陕西',
    '甘肃',
    '青海',
    '内蒙古',
    '广西',
    '西藏',
    '宁夏',
    '新疆',
    '台湾',
    '香港',
    '澳门',
];
// 注册语种选择框的事件
const $Sentiment_Public_Area_Element = $('#Sentiment_Public_Area_Element');
const $Sentiment_Public_IP_Element = $('#Sentiment_Public_IP_Element');
// 表格内容总变量
var Sentiment_All_List = [];
var Sentiment_All_List_Init = [];
var Sentiment_Chioce_Key_Option = [
    // { id: '1', text: '新选项 1' },
    // { id: '2', text: '新选项 2' },
    // { id: '3', text: '新选项 3' }
];
var Sentiment_Chioce_Source_Option = [];
// 每页条数
var Page_Size = 10;
// 当前页码
var Page_current = 1;
// 总条数
var Page_TotleItems = 0;
// 计算总页数
var Page_Total = 0;
// 视图模式(普通视图 精简视图 OCR视图)
var View_Type = '普通视图';
// 筛选条件
var Article_Chioce_Value = 'All';
var Article_Chioce_Search_Value = '';
// 卡片表格数据请求总变量
var Table_condiction_param = {
    // 'Public_Time':'Today',
    // 'Public_Time_List':[],
    // 'Source_Type':['All'],
    // 'Emotion_Type':['All'],
    // 'Media_Type':[],
    // 'Monitor_Type':[],
    // 'Public_Area':'全国',
    // 'Public_IP':'全国',
    // 'Unit_Type':'全部',
    // 'Article_Type':'全部',
};
var Table_condiction_param_init = {
    // 'Public_Time':'Today',
    // 'Public_Time_List':[],
    // 'Source_Type':['All'],
    // 'Emotion_Type':['All'],
    // 'Media_Type':[],
    // 'Monitor_Type':[],
    // 'Public_Area':'全国',
    // 'Public_IP':'全国',
    // 'Unit_Type':'全部',
    // 'Article_Type':'全部',
};
var Table_Condiction_Param_Count = {
    'Source_Type_1': 0,
    'Source_Type_2': 0,
    'Source_Type_3': 0,
    'Source_Type_4': 0,
    'Source_Type_5': 0,
    'Source_Type_6': 0,
    'Source_Type_7': 0,
    'Source_Type_8': 0,
    'Source_Type_9': 0,
    'Source_Type_10': 0,
    'Source_Type_11': 0,
    'Source_Type_12': 0,
    'Source_Type_13': 0,
    'Emotion_Type_1': 0,
    'Emotion_Type_2': 0,
    'Emotion_Type_3': 0,
    'Emotion_Type_4': 0,
};
var Table_User_Alert_Case_Dict = {
    'CASE_UUID':'',
    'CASE_NAME':'',
    'CASE_EXPLAIN':{'KEYWORD_INFO': []},
};
// 判断是不是从其他界面过来的
var HanderDetailsEmotion = localStorage.getItem('HanderDetailsEmotion');
// console.log('HanderDetailsEmotion:',HanderDetailsEmotion);
if (!HanderDetailsEmotion) {
    HanderDetailsEmotion = '';
} else {
    localStorage.setItem('HanderDetailsEmotion','');
};
// console.log('HanderDetailsEmotion2:',HanderDetailsEmotion);
var HanderDetailsKeyword = localStorage.getItem('HanderDetailsKeyword');
// console.log('HanderDetailsEmotion:',HanderDetailsEmotion);
if (!HanderDetailsKeyword) {
    HanderDetailsKeyword = '';
} else {
    localStorage.setItem('HanderDetailsKeyword','');
};
// console.log('HanderDetailsEmotion2:',HanderDetailsEmotion);
/****************************************初始化插件***************************************************/ 
// 设置选择框样式
$('.single-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: Boolean($(this).data('allow-clear')),
});
// 注册选择框
China_Map_List.forEach(Item => {
    const option = $('<option>', {
        value: Item,
        text: Item
    });
    $Sentiment_Public_Area_Element.append(option);
});
China_Map_List.forEach(Item => {
    const option = $('<option>', {
        value: Item,
        text: Item
    });
    $Sentiment_Public_IP_Element.append(option);
});
// 初始化选中
$Sentiment_Public_Area_Element[0].value = '全国';
$Sentiment_Public_IP_Element[0].value = '全国';
$('#diy_time_list').dateRangePicker({
    language: 'cn',
    separator: ' 至 ',

}).bind('datepicker-apply', function (event, obj) {
    // console.log('datepicker-apply:',obj);
    Table_condiction_param.Public_Time_List = obj.value.split(' 至 ');
    // console.log('Table_condiction_param.Public_Time_List:',Table_condiction_param.Public_Time_List)
});
/****************************************初始化请求数据***************************************************/ 
function Requests_Sentiment_Info() {
    Loading_Show();
    // console.log('Table_condiction_param:',Table_condiction_param);
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'get_domestic_today_basic_list',
        "data_argument": `{}`,
        "data_kwargs": {
            'Table_condiction_param':Table_condiction_param,
            'HanderDetailsEmotion':HanderDetailsEmotion,
            'Case_UUID':Table_User_Alert_Case_Dict.CASE_UUID,
            'HanderDetailsKeyword':HanderDetailsKeyword,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.type === 'success') {
                Sentiment_All_List = Result.result.items.web_third_data;
                Sentiment_All_List_Init= Result.result.items.web_third_data;
                Sentiment_Chioce_Key_Option = Result.result.items.web_key;
                Sentiment_Chioce_Source_Option = Result.result.items.web_source;
                Table_condiction_param = Result.result.items.Table_condiction_param;
                Table_condiction_param_init = Result.result.items.Table_condiction_param;
                Table_Condiction_Param_Count = Result.result.items.Table_Condiction_Param_Count;
                if (Object.keys(Result.result.items.User_Alert_Case_Dict).length !== 0) {
                    console.log('包含方案名称',Result.result.items.User_Alert_Case_Dict.length)
                    // 方案名称
                    Table_User_Alert_Case_Dict = JSON.parse(JSON.stringify(Result.result.items.User_Alert_Case_Dict));
                    document.getElementById('Case_Name_Element').firstChild.nodeValue = '方案_' + Result.result.items.User_Alert_Case_Dict.CASE_NAME;
                } else {
                    // 无方案名称
                };
                // 初始化筛选条件
                Init_Show_Table_Params('Requests');
                // --------------------计算当前页的数据 请求数据的时候处理
                Page_TotleItems = Sentiment_All_List.length;
                Page_Total= Math.ceil(Page_TotleItems / Page_Size);
                document.getElementById('Article_Loading_Count').innerText = '共' + Page_TotleItems + '条消息';
                // 渲染界面情报信息
                updatePage();
                // 生成分页页码栏
                generatePagination(Page_current, Page_Total);
                Loading_Hide();
            } else {
                //
            }
        }).catch((err) => {
            console.log('err:',err);
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
};
/****************************************请求项展开或收起***************************************************/ 
function toggleCollapse() {
    var content = document.getElementById('collapseContent');
    if (content.classList.contains('expanded')) {
        document.getElementById('ToggleCollapseIcon').className = 'fadeIn animated bx bx-chevrons-down';
        document.getElementById('ToggleCollapseButton').firstChild.nodeValue = '展开';
        content.classList.remove('expanded');
    } else {
        document.getElementById('ToggleCollapseIcon').className = 'fadeIn animated bx bx-chevrons-up';
        document.getElementById('ToggleCollapseButton').firstChild.nodeValue = '收起';
        content.classList.add('expanded');
    };
};

/*-----------------------------------翻页后 动态计算此页的展示数据-------------------------------------------*/
function updatePage() {
    // 计算当前页的起始和结束索引
    var startIndex= (Page_current - 1) * Page_Size;
    var endIndex= startIndex + Page_Size - 1;
    // 处理边界情况
    endIndex = Math.min(endIndex, Page_TotleItems - 1);
    // 计算当前页的数据
    let pageData = Sentiment_All_List.slice(startIndex, endIndex + 1);
    renderPageData(pageData);
};

// 对列表中的每个词进行转义，以处理可能的正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式特殊字符
};
/*-----------------------------------翻页后 渲染此页的情报信息-------------------------------------------*/
function renderPageData(data) {
    console.log('run renderPageData',data)
    var container = document.getElementById('Article_Body_Element');
    container.innerHTML = ''; // 清空容器
    var element_Info = '';
    data.forEach(item => {
        // -----高亮标题
        // 按照字符串长度降序排序，确保较长的项排在前面
        let keywords_Info = item.KEYWORD.sort((a, b) => b.length - a.length);
        // console.log('keywords_Info:',keywords_Info)
        // 生成正则表达式字符串
        let regexStr = keywords_Info.map(word => escapeRegExp(word)).join('|');
        // console.log('regexStr:',regexStr)
        let regex = new RegExp(regexStr, 'g');
        // console.log('regex:',regex)
        item.TITLE = item.TITLE.replace(regex,(match) => {
            // return `<span style="color:#E74C3C;">${match}</span>`;
            return `<span style="color:#ff9800;">${match}</span>`;
        });
        item.CONTENT_SHOW = item.CONTENT_SHOW.replace(regex,(match) => {
            // return `<span style="color:#E74C3C;">${match}</span>`;
            return `<span style="color:#ff9800;">${match}</span>`;
        });
        if (View_Type === '普通视图') {
            // 普通模式
            const element = `
                <div class="media align-items-center mt-3" style="position: relative;">
                    <div class="form-check form-check-inline col-form-label mr-0">
                        <input class="form-check-input" type="checkbox" value="ID_Info">
                    </div>
                    <div class="media-body ml-1">
                        <div class="media align-items-center">
                            <div class="media-body">
                                <p class="font-weight-bolder mb-0 mt-2 text-white" style="font-size: 1rem;">${item.TITLE}</p>
                                <div class="extra">
                                    <i class='fadeIn animated bx bx-receipt font-20 mx-2' data-toggle="tooltip" data-placement="top" title="复核" style="color:#ffc107"  onclick="Review_Sentiment_Hander('${item.EMOTION}')"></i>
                                    <i class='fadeIn animated bx bx-star font-20 mx-2' data-toggle="tooltip" data-placement="top" title="收藏" style="color: #ff9900;"></i>
                                    <i class='fadeIn animated bx bx-bell font-20 mx-2' data-toggle="tooltip" data-placement="top" title="报警" style="color: #e71616;"></i>
                                    <i class='fadeIn animated bx bx-folder font-20 mx-2' data-toggle="tooltip" data-placement="top" title="加入素材库" style="color: #4caf50;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-plus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="设为重点网站" style="color: #2196f3;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-minus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此网站" style="color: #f02769;"></i>
                                    <i class='fadeIn animated bx bx-hide font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此作者" style="color: #e71616;"></i>
                                </div>
                                <p class="mb-0">${item.CONTENT_SHOW}</p>
                            </div>
                        </div>
                        <div class="row mt-2 ml-0">
                            <button type="button" class="btn ${
                                item.DATA_EMOTION === '中性' ? 'btn-outline-warning' :
                                item.DATA_EMOTION === '负面' ? 'btn-outline-danger' :
                                item.DATA_EMOTION === '正面' ? 'btn-outline-info' : 'btn-outline-secondary'
                            } btn-sm" style="padding: 0px 1rem;">${item.DATA_EMOTION}</button>
                            <div class="details_info ml-3">
                                <span class="span_title_info">情报来源:</span>
                                <span class="span_content_info">${item.PLATFORM}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布人:</span>
                                <span class="span_content_info">${item.AUTHOR}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布地区:</span>
                                <span class="span_content_info">${item.REGION_AREA}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">IP属地:</span>
                                <span class="span_content_info">${item.REGION_IP}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布时间:</span>
                                <span class="span_content_info">${item.TIME}</span>
                            </div>
                            <div class="details_info ml-3">
                                <span class="span_title_info">中标词:</span>
                                <span class="span_content_info">${item.KEYWORD_LIST_FULL}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>`
            element_Info += element;
            
        } else if (View_Type === '精简视图') {
            // 精简模式
            const element = `
                <div class="media align-items-center mt-3" style="position: relative;">
                    <div class="form-check form-check-inline col-form-label mr-0">
                        <input class="form-check-input" type="checkbox" value="ID_Info">
                    </div>
                    <div class="media-body ml-1">
                        <div class="media align-items-center">
                            <div class="media-body">
                                <p class="font-weight-bolder mb-0 mt-2 text-white" style="font-size: 1rem;">${item.TITLE}</p>
                                <div class="extra">
                                    <i class='fadeIn animated bx bx-receipt font-20 mx-2' data-toggle="tooltip" data-placement="top" title="复核" style="color:#ffc107"></i>
                                    <i class='fadeIn animated bx bx-star font-20 mx-2' data-toggle="tooltip" data-placement="top" title="收藏" style="color: #ff9900;"></i>
                                    <i class='fadeIn animated bx bx-bell font-20 mx-2' data-toggle="tooltip" data-placement="top" title="报警" style="color: #e71616;"></i>
                                    <i class='fadeIn animated bx bx-folder font-20 mx-2' data-toggle="tooltip" data-placement="top" title="加入素材库" style="color: #4caf50;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-plus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="设为重点网站" style="color: #2196f3;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-minus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此网站" style="color: #f02769;"></i>
                                    <i class='fadeIn animated bx bx-hide font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此作者" style="color: #e71616;"></i>
                                </div> 
                            </div>
                        </div>
                        <div class="row mt-2 ml-0">
                            <button type="button" class="btn ${
                                item.DATA_EMOTION === '中性' ? 'btn-outline-warning' :
                                item.DATA_EMOTION === '负面' ? 'btn-outline-danger' :
                                item.DATA_EMOTION === '正面' ? 'btn-outline-info' : 'btn-outline-secondary'
                            } btn-sm" style="padding: 0px 1rem;">${item.DATA_EMOTION}</button>
                            <div class="details_info ml-3">
                                <span class="span_title_info">情报来源:</span>
                                <span class="span_content_info">${item.PLATFORM}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布人:</span>
                                <span class="span_content_info">${item.AUTHOR}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布地区:</span>
                                <span class="span_content_info">${item.REGION_AREA}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">IP属地:</span>
                                <span class="span_content_info">${item.REGION_IP}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布时间:</span>
                                <span class="span_content_info">${item.TIME}</span>
                            </div>
                            <div class="details_info ml-3">
                                <span class="span_title_info">中标词:</span>
                                <span class="span_content_info">${item.KEYWORD_LIST_FULL}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>`
            element_Info += element;
        } else {
            // OCR模式
            const element = `
                <div class="media align-items-center mt-3" style="position: relative;">
                    <div class="form-check form-check-inline col-form-label mr-0">
                        <input class="form-check-input" type="checkbox" value="ID_Info">
                    </div>
                    <div class="media-body ml-1">
                        <div class="media align-items-center">
                            <img src="/static/images/User/Face/Examine.jpg"  width="86" height="86">
                            <div class="media-body ml-2">
                                <p class="font-weight-bolder mb-0 mt-2 text-white" style="font-size: 1rem;">${item.TITLE}</p>
                                <div class="extra">
                                    <i class='fadeIn animated bx bx-receipt font-20 mx-2' data-toggle="tooltip" data-placement="top" title="复核" style="color:#ffc107"></i>
                                    <i class='fadeIn animated bx bx-star font-20 mx-2' data-toggle="tooltip" data-placement="top" title="收藏" style="color: #ff9900;"></i>
                                    <i class='fadeIn animated bx bx-bell font-20 mx-2' data-toggle="tooltip" data-placement="top" title="报警" style="color: #e71616;"></i>
                                    <i class='fadeIn animated bx bx-folder font-20 mx-2' data-toggle="tooltip" data-placement="top" title="加入素材库" style="color: #4caf50;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-plus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="设为重点网站" style="color: #2196f3;"></i>
                                    <i class='fadeIn animated bx bx-bookmark-minus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此网站" style="color: #f02769;"></i>
                                    <i class='fadeIn animated bx bx-hide font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此作者" style="color: #e71616;"></i>
                                </div>
                                <p class="mb-0">${item.CONTENT_SHOW}</p>
                            </div>
                        </div>
                        <div class="row mt-2 ml-0">
                            <button type="button" class="btn ${
                                item.DATA_EMOTION === '中性' ? 'btn-outline-warning' :
                                item.DATA_EMOTION === '负面' ? 'btn-outline-danger' :
                                item.DATA_EMOTION === '正面' ? 'btn-outline-info' : 'btn-outline-secondary'
                            } btn-sm" style="padding: 0px 1rem;">${item.DATA_EMOTION}</button>
                            <div class="details_info ml-3">
                                <span class="span_title_info">情报来源:</span>
                                <span class="span_content_info">${item.PLATFORM}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布人:</span>
                                <span class="span_content_info">${item.AUTHOR}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">发布地区:</span>
                                <span class="span_content_info">${item.REGION_AREA}</span>
                            </div>

                            <div class="details_info ml-3">
                                <span class="span_title_info">IP属地:</span>
                                <span class="span_content_info">${item.REGION_IP}</span>
                            </div>
                            
                            <div class="details_info ml-3">
                                <span class="span_title_info">发布时间:</span>
                                <span class="span_content_info">${item.TIME}</span>
                            </div>
                            <div class="details_info ml-3">
                                <span class="span_title_info">中标词:</span>
                                <span class="span_content_info">${item.KEYWORD_LIST_FULL}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>`
            element_Info += element;
        };
    });
    container.innerHTML = element_Info;
};

/*-----------------------------------动态生成分页页码-------------------------------------------*/
function generatePagination(currentPage, totalPages) {
    const pagination = document.querySelector('.pagination');
    pagination.innerHTML = ''; // 清空现有的分页
  
    const maxVisiblePages = 10; // 最多显示的页码数（不包括前后导航和省略号）
  
    // 创建“Previous”按钮
    if (currentPage > 1) {
      const prevLi = document.createElement('li');
      prevLi.classList.add('page-item');
      const prevA = document.createElement('a');
      prevA.classList.add('page-link');
      prevA.href = '#';
      prevA.textContent = 'Previous';
      prevLi.appendChild(prevA);
      pagination.appendChild(prevLi);
    }
  
    // 计算开始和结束的页码
    let start = 1;
    let end = totalPages;
    if (totalPages > maxVisiblePages) {
      if (currentPage <= Math.ceil(maxVisiblePages / 2)) {
        end = maxVisiblePages;
      } else if (currentPage + Math.floor(maxVisiblePages / 2) > totalPages) {
        start = totalPages - maxVisiblePages + 1;
      } else {
        start = currentPage - Math.floor(maxVisiblePages / 2);
        end = currentPage + Math.ceil(maxVisiblePages / 2) - 1;
      }
  
      if (start > 1) {
        // 添加前面的省略号
        const ellipsisLi = document.createElement('li');
        ellipsisLi.classList.add('page-item', 'disabled');
        const ellipsisA = document.createElement('a');
        ellipsisA.classList.add('page-link');
        ellipsisA.href = '#';
        ellipsisA.textContent = '...';
        ellipsisLi.appendChild(ellipsisA);
        pagination.appendChild(ellipsisLi);
      }
    }
  
    // 添加页码
    for (let i = start; i <= end; i++) {
      const pageLi = document.createElement('li');
      pageLi.classList.add('page-item');
      if (i === currentPage) {
        pageLi.classList.add('active');
      }
      const pageA = document.createElement('a');
      pageA.classList.add('page-link');
      pageA.href = '#'; // 这里应该替换为实际的链接
      pageA.textContent = i;
      pageLi.appendChild(pageA);
      pagination.appendChild(pageLi);
    }
  
    if (end < totalPages) {
      // 添加后面的省略号
      const ellipsisLi = document.createElement('li');
      ellipsisLi.classList.add('page-item', 'disabled');
      const ellipsisA = document.createElement('a');
      ellipsisA.classList.add('page-link');
      ellipsisA.href = '#';
      ellipsisA.textContent = '...';
      ellipsisLi.appendChild(ellipsisA);
      pagination.appendChild(ellipsisLi);
    }
  
    // 创建“Next”按钮
    if (currentPage < totalPages) {
      const nextLi = document.createElement('li');
      nextLi.classList.add('page-item');
      const nextA = document.createElement('a');
      nextA.classList.add('page-link');
      nextA.href = '#';
      nextA.textContent = 'Next';
      nextLi.appendChild(nextA);
      pagination.appendChild(nextLi);
    }
};
  
/*-----------------------------------动态监听分页页码点击事件-------------------------------------------*/
const paginationContainer = document.querySelector('.pagination');
// 为分页容器添加点击事件监听器
paginationContainer.addEventListener('click', function(event) {
  // 检查被点击的元素是否是分页链接
  if (event.target && event.target.matches('.page-link')) {
    Loading_Show();
    // 阻止默认行为
    event.preventDefault();
    // 获取被点击的链接的页码
    const page = event.target.textContent;
    console.log('获取被点击的链接的页码 page',page);
    if (page === 'Previous') {
        Page_current = parseInt(Page_current - 1);
    } else if (page === 'Next') {
        Page_current = parseInt(Page_current + 1);
    } else {
        Page_current = parseInt(page)
    };

    // 动态生成分页符
    generatePagination(Page_current, Page_Total);
    
    // 页码变化 更新此页的展示情报信息
    updatePage();
    Loading_Hide();
  } else {
    //
  }
});

/*-----------------------------------动态监听视图切换点击事件-------------------------------------------*/
const $View_Buttons = document.querySelectorAll('#View_Show_Element .btn');

// 为每个按钮添加点击事件监听器
$View_Buttons.forEach(button => {
    button.addEventListener('click', function() {
        // 移除所有按钮的active类
        $View_Buttons.forEach(btn => btn.classList.remove('btn-active'));
        // 为当前点击的按钮添加active类
        this.classList.add('btn-active');
        // 获取点击按钮的文本内容
        const clickedButtonText = this.textContent || this.innerText;
        console.log(clickedButtonText); // 在控制台打印按钮文本内容
        if (clickedButtonText === View_Type) {
            //相同视图多次点击 不做处理
        } else {
            View_Type = clickedButtonText;
            // 渲染界面情报信息
            updatePage()
        };
    });
});

/*-----------------------------------动态监听时间选择点击事件-------------------------------------------*/
const $Public_Time_Buttons = document.querySelectorAll('#Sentiment_Public_Time_Element .btn');

// 为每个按钮添加点击事件监听器
$Public_Time_Buttons.forEach(button => {
    button.addEventListener('click', function() {
        // 移除所有按钮的active类
        $Public_Time_Buttons.forEach(btn => btn.classList.remove('btn-active'));
        // 为当前点击的按钮添加active类
        this.classList.add('btn-active');
        // 获取点击按钮的文本内容
        // const clickedButtonText = this.textContent || this.innerText;
        Table_condiction_param.Public_Time = this.value;
        console.log('现在选择的时间:',Table_condiction_param.Public_Time ); // 在控制台打印按钮文本内容
        if (this.value === 'Other') {
            console.log('打开日期选择框');
            $('#diy_time_list').show();
        } else {
            console.log('隐藏日期选择框');
            $('#diy_time_list').hide();
            $('#diy_time_list').val('');
            Table_condiction_param.Public_Time_List = [];
            // console.log('Table_condiction_param.Public_Time_List:',Table_condiction_param.Public_Time_List)
        }
    });
});
/*-----------------------------------动态监听媒体类型选择事件-------------------------------------------*/
const $Sentiment_Source_Type_Element = document.getElementById('Sentiment_Source_Type_Element');
const $Source_Type_1 = document.getElementById('Source_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Source_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框
        if (checkbox.id === 'Source_Type_1' && checkbox.checked) {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Source_Type_Element.querySelectorAll('.form-check-input:not(#Source_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Table_condiction_param.Source_Type = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Table_condiction_param.Source_Type = []; //更新筛选项
            };
        } else {
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Source_Type_1.checked = false;
                Table_condiction_param.Source_Type.push(checkbox.value); //更新筛选项
                const index = Table_condiction_param.Source_Type.indexOf('All');
                if (index !== -1) {
                    Table_condiction_param.Source_Type.splice(index, 1);
                };
            } else {
                const index = Table_condiction_param.Source_Type.indexOf(checkbox.value);
                if (index !== -1) {
                    Table_condiction_param.Source_Type.splice(index, 1);
                };
            };
        };
        console.log('现在选择的媒体类型:',Table_condiction_param.Source_Type); // 在控制台打印按钮文本内容
    };
});

/*-----------------------------------动态监听情感属性选择事件-------------------------------------------*/
const $Sentiment_Emotion_Type_Element = document.getElementById('Sentiment_Emotion_Type_Element');
const $Emotion_Type_1 = document.getElementById('Emotion_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Emotion_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框
        if (checkbox.id === 'Emotion_Type_1' && checkbox.checked) {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Emotion_Type_Element.querySelectorAll('.form-check-input:not(#Emotion_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Table_condiction_param.Emotion_Type = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Table_condiction_param.Emotion_Type = []; //更新筛选项
            };
        } else {
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Emotion_Type_1.checked = false;
                Table_condiction_param.Emotion_Type.push(checkbox.value); //更新筛选项
                const index = Table_condiction_param.Emotion_Type.indexOf('All');
                if (index !== -1) {
                    Table_condiction_param.Emotion_Type.splice(index, 1);
                };
            } else {
                const index = Table_condiction_param.Emotion_Type.indexOf(checkbox.value);
                if (index !== -1) {
                    Table_condiction_param.Emotion_Type.splice(index, 1);
                };
            };
        };
        console.log('现在选择的情感属性:',Table_condiction_param.Emotion_Type); // 在控制台打印按钮文本内容
    };
});

/*-----------------------------------动态监听多媒体类型属性选择事件-------------------------------------------*/
const $Sentiment_Media_Type_Element = document.getElementById('Sentiment_Media_Type_Element');
const $Source_Media_Type_1 = document.getElementById('Source_Media_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Media_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框  
        if (checkbox.id === 'Source_Media_Type_1') {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Media_Type_Element.querySelectorAll('.form-check-input:not(#Source_Media_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Table_condiction_param.Media_Type = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Table_condiction_param.Media_Type = []; //更新筛选项
            };
        } else {
            // 如果选中的是非“全部”多选框  
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Source_Media_Type_1.checked = false;
                Table_condiction_param.Media_Type.push(checkbox.value); //更新筛选项
                const index = Table_condiction_param.Media_Type.indexOf('All');
                if (index !== -1) {
                    Table_condiction_param.Media_Type.splice(index, 1);
                };
            } else {
                const index = Table_condiction_param.Media_Type.indexOf(checkbox.value);
                if (index !== -1) {
                    Table_condiction_param.Media_Type.splice(index, 1);
                };
            };
        };
        console.log('现在选择的多媒体类型属性:',Table_condiction_param.Media_Type); // 在控制台打印按钮文本内容
    };
});

/*-----------------------------------动态监听重点数源属性选择事件-------------------------------------------*/
const $Sentiment_Monitor_Type_Element = document.getElementById('Sentiment_Monitor_Type_Element');
const $Monitor_Type_1 = document.getElementById('Monitor_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Monitor_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框
        if (checkbox.id === 'Monitor_Type_1') {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Monitor_Type_Element.querySelectorAll('.form-check-input:not(#Monitor_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Table_condiction_param.Monitor_Type = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Table_condiction_param.Monitor_Type = []; //更新筛选项
            };
        } else {
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Monitor_Type_1.checked = false;
                Table_condiction_param.Monitor_Type.push(checkbox.value); //更新筛选项
                const index = Table_condiction_param.Monitor_Type.indexOf('All');
                if (index !== -1) {
                    Table_condiction_param.Monitor_Type.splice(index, 1);
                };
            } else {
                const index = Table_condiction_param.Monitor_Type.indexOf(checkbox.value);
                if (index !== -1) {
                    Table_condiction_param.Monitor_Type.splice(index, 1);
                };
            };
        };
        console.log('现在选择的重点数源属性:',Table_condiction_param.Monitor_Type); // 在控制台打印按钮文本内容
    };
});

/*-----------------------------------动态监听推送条数属性选择事件-------------------------------------------*/
const $Sentiment_Show_Count_Element = document.getElementById('Sentiment_Show_Count_Element');

// 在主节点上设置事件监听器
$Sentiment_Show_Count_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    // console.log('动态监听推送条数属性选择事件:',event.target);
    if (event.target.type === 'radio') {
        Table_condiction_param.Show_Count = event.target.value;
        console.log('现在选择的推送条数属性:',Table_condiction_param.Show_Count); // 在控制台打印按钮文本内容
    };
});

/*-----------------------------------动态监听发布地区选择事件-------------------------------------------*/
$('#Sentiment_Public_Area_Element').on('change',function() {
    Table_condiction_param.Public_Area = this.value;
    console.log('现在选择的发布地区属性:', Table_condiction_param.Public_Area);
});
/*-----------------------------------动态监听IP属地选择事件-------------------------------------------*/
$('#Sentiment_Public_IP_Element').on('change',function() {
    Table_condiction_param.Public_IP = this.value;
    console.log('现在选择的IP属地属性:', Table_condiction_param.Public_IP);
});
/*-----------------------------------动态监听舆情类别选择事件-------------------------------------------*/
$('#Sentiment_Unit_Type_Element').on('change',function() {
    Table_condiction_param.Unit_Type = this.value;
    console.log('现在选择的舆情类别属性:', Table_condiction_param.Unit_Type);
});
/*-----------------------------------动态监听文章类别选择事件-------------------------------------------*/
$('#Sentiment_Article_Type_Element').on('change',function() {
    Table_condiction_param.Article_Type = this.value;
    console.log('现在选择的文章类别属性:', Table_condiction_param.Article_Type);
});

/*-----------------------------------重置/初始化勾选当前筛选条件-------------------------------------------*/
function Init_Show_Table_Params(val) {
    // console.log('In Table_condiction_param_init:',Table_condiction_param_init);
    // console.log('In Table_condiction_param:',Table_condiction_param);
    if (Object.keys(Table_condiction_param_init).length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            sound: false,
            delay:2000,
            position: 'top right',
            msg: '等待已保存方案的同步！'
        });
        return ;
    };
    // 时间范围
    document.querySelectorAll('#Sentiment_Public_Time_Element .btn').forEach(function(btn){
        if (btn.value === Table_condiction_param_init.Public_Time) {
            btn.classList.add('btn-active');
        } else {
            btn.classList.remove('btn-active');
        };
    });
    if (Table_condiction_param_init.Public_Time === 'Other') {
        // console.log('打开日期选择框');
        $('#diy_time_list').show();
        $('#diy_time_list').dateRangePicker('setDateRange', Table_condiction_param_init.Public_Time_List[0], Table_condiction_param_init.Public_Time_List[1]);
        $('#diy_time_list').val(Table_condiction_param_init.Public_Time_List[0] + ' 至 ' + Table_condiction_param_init.Public_Time_List[1]);
    } else {
        $('#diy_time_list').hide();
        $('#diy_time_list').val('');
        Table_condiction_param_init.Public_Time_List = [];
    };
    // 媒体类型
    document.querySelectorAll('#Sentiment_Source_Type_Element .form-check-input').forEach(function(cb){
        const Type_Index = Table_condiction_param_init.Source_Type.findIndex(element => element === cb.value);
        if (Type_Index !== -1) {
            cb.checked = true;
        } else {
            cb.checked = false;
        };
    });
    // 情感属性
    document.querySelectorAll('#Sentiment_Emotion_Type_Element .form-check-input').forEach(function(cb){
        const Type_Index = Table_condiction_param_init.Emotion_Type.findIndex(element => element === cb.value);
        if (Type_Index !== -1) {
            cb.checked = true;
        } else {
            cb.checked = false;
        };
    });
    // 多媒体类型
    document.querySelectorAll('#Sentiment_Media_Type_Element .form-check-input').forEach(function(cb){
        const Type_Index = Table_condiction_param_init.Media_Type.findIndex(element => element === cb.value);
        if (Type_Index !== -1) {
            cb.checked = true;
        } else {
            cb.checked = false;
        };
    });
    // 重点数源
    document.querySelectorAll('#Sentiment_Monitor_Type_Element .form-check-input').forEach(function(cb){
        const Type_Index = Table_condiction_param_init.Monitor_Type.findIndex(element => element === cb.value);
        if (Type_Index !== -1) {
            cb.checked = true;
        } else {
            cb.checked = false;
        };
    });
    // 推送条数
    document.querySelectorAll('#Sentiment_Show_Count_Element .form-check-input').forEach(function(cb){
        if (Table_condiction_param_init.Show_Count === cb.value) {
            cb.checked = true;
        } else {
            cb.checked = false;
        };
    });
    // 发布地区
    $('#Sentiment_Public_Area_Element').val(Table_condiction_param_init.Public_Area).trigger('change');
    // IP属地
    $('#Sentiment_Public_IP_Element').val(Table_condiction_param_init.Public_IP).trigger('change');
    // 舆情类别
    $('#Sentiment_Unit_Type_Element').val(Table_condiction_param_init.Unit_Type).trigger('change');
    // 文章类别
    $('#Sentiment_Article_Type_Element').val(Table_condiction_param_init.Article_Type).trigger('change');
    // 深度拷贝值
    // Table_condiction_param = structuredClone(Table_condiction_param_init);
    Table_condiction_param = deepClone(Table_condiction_param_init);
    if (val === 'Requests') {
        // 媒体类型/情感属性这个内容的各项数量
        Object.keys(Table_Condiction_Param_Count).forEach(key => {
            document.getElementById('Label_' + key).innerText = Table_Condiction_Param_Count[key];
        });
    } else {
        // 媒体类型/情感属性这个内容的各项数量重置
        Object.keys(Table_Condiction_Param_Count).forEach(key => {
            document.getElementById('Label_' + key).innerText = 0;
        });
    };
};
/*----------------------------------深度拷贝-------------------------------------------*/
function deepClone(obj, hash = new WeakMap()) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof RegExp) return new RegExp(obj);
    if (hash.has(obj)) return hash.get(obj);
    let cloneObj = new obj.constructor();
    hash.set(obj, cloneObj);
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloneObj[key] = deepClone(obj[key], hash);
        }
    }
    return cloneObj;
};
/*----------------------------------保存当前筛选条件-------------------------------------------*/
function Save_Show_Table_Params() {
    console.log('Save_Show_Table_Params Table_condiction_param:',Table_condiction_param);
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'editor_detail_table_param',
        "data_argument": `{}`,
        "data_kwargs": {
            'Table_condiction_param':Table_condiction_param,
            'Table_User_Alert_Case_Dict':Table_User_Alert_Case_Dict
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '筛选方案保存成功！'
                });
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '筛选方案保存失败，请重试！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
};
/*----------------------------------选择当前查询条件-------------------------------------------*/
$('#Article_Chioce_Element').on('change',function() {
    Article_Chioce_Value = this.value;
    console.log('现在的筛选项属性:', Article_Chioce_Value);
    if (Article_Chioce_Value === 'Keyword' || Article_Chioce_Value === 'Source') {
        document.getElementById('Article_Chioce_Select_Element').style.display = 'block';
        document.getElementById('Article_Chioce_Input_Element').style.display = 'none';
        // 清除原来的Option
        $('#Article_Chioce_Select_Value_Element').empty();
        if (Article_Chioce_Value === 'Keyword') {
            $('#Article_Chioce_Select_Value_Element').select2({
                data: Sentiment_Chioce_Key_Option,
                theme: 'bootstrap4',
                width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                placeholder: $(this).data('placeholder'),
                allowClear: Boolean($(this).data('allow-clear')),
            });
        } else {
            $('#Article_Chioce_Select_Value_Element').select2({
                data: Sentiment_Chioce_Source_Option,
                theme: 'bootstrap4',
                width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                placeholder: $(this).data('placeholder'),
                allowClear: Boolean($(this).data('allow-clear')),
            });
        };
    } else {
        document.getElementById('Article_Chioce_Select_Element').style.display = 'none';
        document.getElementById('Article_Chioce_Input_Element').style.display = 'block';
    };
});

/*----------------------------------执行查询-------------------------------------------*/
$('#Article_Chioce_Search_Element').on('click',function() {
    if (Article_Chioce_Value === 'Keyword' || Article_Chioce_Value === 'Source') {
        Article_Chioce_Search_Value = $('#Article_Chioce_Select_Value_Element').val();
    } else {
        Article_Chioce_Search_Value = document.getElementById('Article_Chioce_Input_Element').value;
    };
    console.log('查询的内容:',Article_Chioce_Search_Value);
    if (Article_Chioce_Search_Value.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            sound: false,
            delay:2000,
            position: 'top right',
            msg: '请输入查询内容！'
        });
    };
    Sentiment_All_List = Sentiment_All_List.filter(Item => {
        // 根据不同的选项判断  多维判断 只要满足一项就返回
        if (Article_Chioce_Value === 'All' || Article_Chioce_Value === 'Keyword') {
            const index = Item.KEYWORD.indexOf(Article_Chioce_Search_Value);
            if (index !== -1) {
                return true
            };
        } else {
            // 不是关键词验证
        };
        if (Article_Chioce_Value === 'All' || Article_Chioce_Value === 'Source') {
            if (Item.PLATFORM === Article_Chioce_Search_Value) {
                return true
            };
        } else {
            // 不是来源验证
        };
        if (Article_Chioce_Value === 'All' || Article_Chioce_Value === 'Title') {
            if (Item.TITLE.includes(Article_Chioce_Search_Value)) {
                return true
            };
        } else {
            // 不是标题验证
        };
        if (Article_Chioce_Value === 'All' || Article_Chioce_Value === 'Content') {
            if (Item.CONTENT_CN.includes(Article_Chioce_Search_Value)) {
                return true
            };
        } else {
            // 不是内容验证
        };
        if (Article_Chioce_Value === 'All' || Article_Chioce_Value === 'Link') {
            if (Item.URL.includes(Article_Chioce_Search_Value)) {
                return true
            };
        } else {
            // 不是内容验证
        };
    });
    Page_TotleItems = Sentiment_All_List.length;
    Page_Total= Math.ceil(Page_TotleItems / Page_Size);
    document.getElementById('Article_Loading_Count').innerText = '共' + Page_TotleItems + '条消息';
    Page_current = 1;
    // 渲染界面情报信息
    updatePage();
    // 生成分页页码栏
    generatePagination(Page_current, Page_Total);
});

/*----------------------------------本地刷新-------------------------------------------*/
function Reload_Sentiment_Hander() {
    Sentiment_All_List = Sentiment_All_List_Init;
    Page_TotleItems = Sentiment_All_List.length;
    Page_Total= Math.ceil(Page_TotleItems / Page_Size);
    document.getElementById('Article_Loading_Count').innerText = '共' + Page_TotleItems + '条消息';
    Page_current = 1;
    // 渲染界面情报信息
    updatePage();
    // 生成分页页码栏
    generatePagination(Page_current, Page_Total);
};


/*----------------------------------复核-------------------------------------------*/
function Review_Sentiment_Hander(Review_UUID) {
    localStorage.setItem('Review_UUID',Review_UUID);
    // window.location.href="Service_Page?Page=Page_Review";
    window.open("Service_Page?Page=Page_Review", "_blank");
};

/*----------------------------------监听方案列表展开-------------------------------------------*/
var Manage_Keyword_Info_list = [];
$('#Show_Case_Name_Element').on('click',function() {
    // 获取当前的方案列表
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_keyword_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Keyword_Info_list = Result.Keyword_Info_list;
                var Element_Info_Totle = '';
                var Element_Info_One = '';
                Manage_Keyword_Info_list.filter(Item => {
                    // 判断是不是当前激活的方案
                    if (Item.USER_UUID === Table_User_Alert_Case_Dict.CASE_UUID) {
                        Element_Info_One = `
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="exampleRadios" id="Case_${Item.USER_UUID}" value="${Item.USER_UUID}" checked="">
                                <label class="form-check-label" for="Case_${Item.USER_UUID}">${Item.USER_NAME}</label>
                            </div>`
                    } else {
                        Element_Info_One = `
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="exampleRadios" id="Case_${Item.USER_UUID}" value="${Item.USER_UUID}">
                                <label class="form-check-label" for="Case_${Item.USER_UUID}">${Item.USER_NAME}</label>
                            </div>`
                    };
                    Element_Info_Totle += Element_Info_One;
                });
                document.getElementById("staticCaseBackdropContent").innerHTML = Element_Info_Totle;
            };
            $('#Detail_Case_Modal').modal('show');
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
});

/*----------------------------------监听方案列表保存按钮触发-------------------------------------------*/
$('#Editor_Add_Case_Save_Button').on('click',function () {
    console.log('监听方案列表保存按钮触发');
    // 获取父容器
    const container = document.getElementById('staticCaseBackdropContent');
    // 获取容器内的所有单选框
    const radios = container.querySelectorAll('input[type="radio"]');
    // 遍历单选框，查找被选中的那个
    let selectedValue = null;
    for (let radio of radios) {
        if (radio.checked) {
            selectedValue = radio.value;
            break; // 找到后退出循环
        };
    };
    // 输出结果
    if (selectedValue) {
        console.log(`有选中的方案: ${selectedValue}`);
        if (selectedValue === Table_User_Alert_Case_Dict.CASE_UUID) {
            console.log('方案列表未变化')
        } else {
            console.log('方案当前激活有变化，更改激活内容')
            let Requests_Data = {
                "user_id": User_Token,
                "user_token":User_Token ,
                "data_class": "Sentiment",
                "data_type": 'Service',
                "data_methods": 'section_manage_keyword_info',
                "data_argument": `{}`,
                "data_kwargs":{
                    'Section_Case_UUID':selectedValue,
                },
            };
            __Service_Requests = new Service_Requests("Async",Requests_Data);
            __Service_Requests.callMethod()
                .then((result) => {
                    if (result['Status'] === 'Success') {
                        Lobibox.notify('success', {
                            pauseDelayOnHover: true,
                            size: 'mini',
                            rounded: true,
                            delayIndicator: false,
                            continueDelayOnInactiveTab: false,
                            position: 'top right',
                            msg: '方案激活切换成功！'
                        });
                        // 关闭弹窗
                        $('#Detail_Case_Modal').modal('hide');
                        //更新方案
                        Table_User_Alert_Case_Dict.CASE_UUID = selectedValue;
                        // 重新请求
                        Requests_Sentiment_Info();
                    } else {
                        Lobibox.notify('warning', {
                            pauseDelayOnHover: true,
                            size: 'mini',
                            rounded: true,
                            delayIndicator: false,
                            continueDelayOnInactiveTab: false,
                            position: 'top right',
                            msg: '方案激活切换出错！'
                        });
                    };
                }).catch((err) => {
                    Lobibox.notify('warning', {
                        pauseDelayOnHover: true,
                        size: 'mini',
                        rounded: true,
                        delayIndicator: false,
                        continueDelayOnInactiveTab: false,
                        position: 'top right',
                        msg: '方案激活切换出错！'
                    });
                });
        };
    } else {
        console.log("无方案被选中");
    };
});


/*----------------------------------监听方案编辑展开-------------------------------------------*/
var Manage_Keyword_Info_Dict = {};
$('#Editor_Case_Name_Element').on('click',function() {
    // 获取当前的方案列表
    Manage_Keyword_Info_Dict = {};
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_manage_case_one_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Section_Case_UUID':Table_User_Alert_Case_Dict.CASE_UUID,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Manage_Keyword_Info_Dict = Result.Keyword_Info_Dict;
                $('#Keyword_USER_NAME').val(Manage_Keyword_Info_Dict.USER_NAME);
                $('#Keyword_USER_TYPE').val(Manage_Keyword_Info_Dict.USER_TYPE).trigger('change');
                $('#Keyword_KEYWORD_INFO').val(Manage_Keyword_Info_Dict.USER_USE.KEYWORD_INFO);
                $('#Keyword_FEATUREWORD_INFO').val(Manage_Keyword_Info_Dict.USER_USE.FEATUREWORD_INFO);
                $('#Keyword_STOPWORD_INFO').val(Manage_Keyword_Info_Dict.USER_USE.STOPWORD_INFO);
                $('#Detail_Keyword_Modal').modal('show');
            }; 
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
});

/*----------------------------------监听方案编辑保存按钮触发-------------------------------------------*/
$('#Editor_Add_Keyword_Save_Button').on('click',function () {
    console.log('方案编辑modal  保存按钮触发');
    let Editor_USER_NAME = $('#Keyword_USER_NAME').val();
    let Editor_USER_TYPE = $('#Keyword_USER_TYPE').val();
    let Editor_KEYWORD_INFO = $('#Keyword_KEYWORD_INFO').val();
    let Editor_FEATUREWORD_INFO = $('#Keyword_FEATUREWORD_INFO').val();
    let Editor_STOPWORD_INFO = $('#Keyword_STOPWORD_INFO').val();
    console.log('Editor_USER_NAME:',Editor_USER_NAME);
    console.log('Editor_USER_TYPE:',Editor_USER_TYPE);
    console.log('Editor_KEYWORD_INFO:',Editor_KEYWORD_INFO);
    console.log('Editor_FEATUREWORD_INFO:',Editor_FEATUREWORD_INFO);
    console.log('Editor_STOPWORD_INFO:',Editor_STOPWORD_INFO);
    if (Editor_USER_NAME.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[方案名称]为空!'
        });
        return ;
    };
    if (Editor_KEYWORD_INFO.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '必填项[关键词]为空!'
        });
        return ;
    };
    // 编辑保存
    Manage_Keyword_Info_Dict['USER_NAME'] = Editor_USER_NAME;
    Manage_Keyword_Info_Dict['USER_TYPE'] = Editor_USER_TYPE;
    Manage_Keyword_Info_Dict['USER_USE']['KEYWORD_INFO'] = Editor_KEYWORD_INFO;
    Manage_Keyword_Info_Dict['USER_USE']['FEATUREWORD_INFO'] = Editor_FEATUREWORD_INFO;
    Manage_Keyword_Info_Dict['USER_USE']['STOPWORD_INFO'] = Editor_STOPWORD_INFO;
    console.log('Manage_Keyword_Info_Dict:',Manage_Keyword_Info_Dict);
    let Requests_Data = {
        "user_id": User_Token,
        "user_token":User_Token ,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'replace_manage_case_one_info',
        "data_argument": `{}`,
        "data_kwargs":{
            'Manage_Keyword_Info_Dict':Manage_Keyword_Info_Dict,
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((result) => {
            if (result['Status'] === 'Success') {
                Lobibox.notify('success', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '方案编辑保存成功！'
                });
                // 关闭弹窗
                $('#Detail_Keyword_Modal').modal('hide');
                // 重新请求
                Requests_Sentiment_Info();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '方案编辑保存出错！'
                });
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '方案编辑保存出错！'
            });
        });
});

