{"Node_Type_List": {"Object": {"Name": "课题", "Title": "【未定义】", "Method": {}, "Explain": "本工作流的目标功能"}, "System": {"Name": "系统", "Title": "【未定义】", "Method": {"Start": "启动分析"}, "Explain": "工作流的启动控制"}, "Method": {"Name": "业务", "Title": "【未定义】", "Method": {}, "Explain": "工作流具体实现业务"}, "DataSource": {"Name": "数源", "Title": "【未定义】", "Method": {"Start": "国内数据"}, "Explain": "开源数据"}}, "Node_System_Function": {"ID": "", "Class": "System", "Type": "Functon", "Schemes": "Unkown", "Module": "<PERSON><PERSON><PERSON>", "Name": "【系统-开始】", "Method": "", "Explain": "系统分析启动控制节点", "Status": "", "Update": ""}, "Node_System_Start": {"ID": "", "Class": "System", "Type": "Start", "Schemes": "Unkown", "Module": "Start", "Name": "【系统-开始】", "Method": "", "Explain": "系统分析启动控制节点", "Status": "", "Update": ""}, "Node_System_Object": {"ID": "", "Class": "System", "Type": "Object", "Schemes": "Unkown", "Module": "Object", "Name": "【课题-配置】", "Method": "", "Explain": "课题分析配置节点", "Status": "", "Update": ""}, "Node_Parameter_Analysis": {"ID": "", "Class": "Parameter", "Type": "Analysis", "Schemes": "Unkown", "Module": "Analysis", "Name": "【参数-配置】", "Method": "", "Explain": "参数配置节点", "Status": "", "Update": ""}, "Node_DataSource_Rumor": {"ID": "", "Class": "DataSource", "Type": "OnceSource", "Schemes": "Unkown", "Module": "DataSource", "Name": "【数源-突出谣言】", "Method": "", "Explain": "突谣数据节点", "Status": "", "Update": ""}, "Node_System_Algorithm": {"ID": "", "Class": "Algorithm", "Type": "Algorithm", "Schemes": "Unkown", "Module": "Algorithm", "Name": "【算法-节点】", "Method": "", "Explain": "算法节点", "Status": "", "Update": ""}, "Node_AI_DeepSeek": {"ID": "", "Class": "AI", "Type": "AI", "Schemes": "Unkown", "Module": "AI", "Name": "【AI-DeepSeek】", "Method": "", "Explain": "AI大模型DeepSeek资源节点", "Status": "", "Update": ""}, "Node_AI_ChatGLM": {"ID": "", "Class": "AI", "Type": "ChatGLM", "Schemes": "Unkown", "Module": "ChatGLM", "Name": "【AI-ChatGLM】", "Method": "", "Explain": "AI大模型ChatGLM资源节点", "Status": "", "Update": ""}, "Node_System_Preview": {"ID": "", "Class": "System", "Type": "Preview", "Schemes": "Unkown", "Module": "AI", "Name": "【预览-突出谣言】", "Method": "", "Explain": "预览突出谣言节点", "Status": "", "Update": ""}, "Node_System_Output": {"ID": "", "Class": "System", "Type": "OnceOutput", "Schemes": "Unkown", "Module": "Output", "Name": "【输出-突出谣言汇总】", "Method": "", "Explain": "突出谣言汇总信息节点", "Status": "", "Update": ""}, "Node_DataSource_Statistic": {"ID": "", "Class": "DataSource", "Type": "Statistic", "Schemes": "Unkown", "Module": "Statistic", "Name": "【汇总-突出谣言】", "Method": "", "Explain": "突出谣言汇总信息节点", "Status": "", "Update": ""}, "Node_Model_Rumor": {"ID": "", "Class": "Model", "Type": "<PERSON><PERSON><PERSON>", "Schemes": "Unkown", "Module": "<PERSON><PERSON><PERSON>", "Name": "【模型-突出谣言】", "Method": "", "Explain": "突出谣言模型信息节点", "Status": "", "Update": ""}, "Node_System_Status": {"ID": "", "Class": "System", "Type": "OnceStatus", "Schemes": "Unkown", "Module": "OnceStatus", "Name": "【模型-模型性能】", "Method": "", "Explain": "突出谣言模型信息节点", "Status": "", "Update": ""}}