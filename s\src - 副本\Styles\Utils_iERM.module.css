/* ---------------------------------------------------------------------------------------- 右边抽屉容器 */
.iERM {
    position:absolute;
    top: 0;
    right: -500px; /* 初始位置：隐藏在右侧 */
    width: 500px; /* 抽屉宽度 */
    height: 100vh; /* 抽屉高度 */
    background-color: #f0f0f0;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease-in-out; /* 动画效果 */
    display: none;
  }
  
  
  /* 抽屉打开时的样式 */
  .iERM.open {
    right: 0; /* 滑出到屏幕右侧 */
    display: block;
  }
  
  /* 抽屉内容样式 */
  .iERM_Content {
    padding: 20px;
    font-size: 14px;
    color: #333;
  }
  
  
  /* * 容器样式 */ 
.tabsContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  /* Tab 标题按钮容器 */
  .tabHeader {
    display: flex;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ccc;
    padding: 2px;
  }
  
  /* Tab 按钮样式 */
  .tabButton {
    width: 38px;
    height: 20px;
    padding: 5px 10px;
    font-size: 8px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-radius: 4px;
    margin-right: 2px;
    transition: background-color 0.3s ease;
  }
  
  /* 激活的 Tab 按钮样式 */
  .tabButton.active {
    background-color: #1890ff;
    color: white;
  }
  
  /* Tab 内容区域样式 */
  .tabContent {
    flex: 1;
    height: 95vh;
    padding: 2px;
    background-color: #fff;
    overflow-y: auto; /* 如果内容过多，允许滚动 */
  }

  /* // */
  .Chat_Window {
    width: 100%;
    height: 90vh;
    max-height: 905vh;  /* 防止无限扩张 */
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .Chat_Header {
    height: 60px;
    background-color: #f5f5f5;
    padding: 2px;
    border-bottom: 1px solid #ccc;
  }
  
  .Chat_Messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    height:80vh;
    /* min-height: 900px;  */
    /* max-height: 125vh;  */
    padding: 10px;



  }
  
  .Message {
    margin-bottom: 15px;
    max-width: 80%;
    word-break: break-all;
  }
  
  .Message.user {
    align-self: flex-end;
    background-color: #007bff;
    color: white;
    border-radius: 10px 10px 10px 0;
  }
  
  .Message.bot {
    align-self: flex-start;
    background-color: #e9ecef;
    border-radius: 10px 10px 0 10px;
  }
  
  .Messages_Content {
    padding: 10px;
    position: relative;
  }
  
  .Message_Time {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
  }
  .Message_Text {
    font-size: 0.7rem;
    color: rgba(231, 74, 17, 0.7);
    margin-top: 5px;
  }
  
  .Chat_Input {
    justify-content: "top"; /*水平居中*/
    align-items: "left";   /* 垂直居中*/
    height: 140px;
    display: flex;
    padding: 2px;
    border-top: 1px solid #ccc;
    background-color: #f8f9fa;
  }
  
  .Chat_Input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 0px;
  }
  
  .Chat_Input button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .Chat_Input button:hover {
    background-color: #0056b3;
  }


.Chat_Send_Label{
    background: rgba(175, 152, 152, 0.4);
    display: "flex";         /*启用 Flex 布局*/
    justify-content: "center"; /*水平居中*/
    align-items: "center";   /* 垂直居中*/
    height: "100%";          /*确保容器有高度*/
    width: "100%";           /* 确保容器有宽度*/
    position: "relative";     /* 可选：为复杂布局准备*/
    display: -webkit-flex;


}

.Chat_Send_Label > button {
    margin: auto;            /* 终极居中保障 */
  }

.SPN{
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
}