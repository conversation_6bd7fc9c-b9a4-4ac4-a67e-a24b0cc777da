/* 平台配置页面样式 */
.platform-config-page {
  padding: 24px;
  min-height: 100vh;
}

/* Tab头部操作区域 */
.tab-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

/* 表格样式 */
.ant-table {
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
}

.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-tbody > tr:hover > td {
  transition: all 0.3s ease;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 8px 8px;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #ff4d4f;
  font-size: 16px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.ant-btn-link {
  padding: 4px 8px;
  height: auto;
}

/* 输入框样式 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.ant-input {
  resize: vertical;
}

/* 上传组件样式 */
.ant-upload {
  width: 100%;
}

.ant-upload-btn {
  width: 100%;
  height: 40px;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.ant-upload-btn:hover {
  border-color: #1890ff;
}

/* 选择器样式 */
.ant-select-multiple .ant-select-selection-item {
  border-radius: 4px;
  margin-right: 4px;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 确认框样式 */
.ant-popover-inner {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-config-page {
    padding: 16px;
  }
  
  .tab-header {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  
  .tab-header .ant-space {
    flex-wrap: wrap;
    gap: 8px !important;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .platform-config-page {
    padding: 12px;
  }
  
  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .ant-space-item {
    margin-bottom: 4px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* 表单必填标记样式 */
.ant-form-item-required::before {
  content: '*';
  color: #ff4d4f;
  font-size: 14px;
  margin-right: 4px;
}

/* 禁用状态样式 */
.ant-input[disabled],
.ant-select-disabled .ant-select-selector {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 成功状态样式 */
.ant-message-success .anticon {
  color: #52c41a;
}

/* 错误状态样式 */
.ant-message-error .anticon {
  color: #ff4d4f;
}

/* 警告状态样式 */
.ant-message-warning .anticon {
  color: #faad14;
}

/* 信息状态样式 */
.ant-message-info .anticon {
  color: #1890ff;
}
