
// let __params = getQueryParams()

// $(document).ready(function () {
//     const __System_Teble_Sider_Header = new System_BaseConfig('BaseConfig', {
//         Page: __params.Page
//     });
//     var Requests_Web_Sider_Header = __System_Teble_Sider_Header.run()
//     // console.log(Requests_Web_Sider_Header);
//     $('#Web_Header').html(Requests_Web_Sider_Header.Header_Element_Info)
//     $('#Web_Asidebar').html(Requests_Web_Sider_Header.Sidebar_Element_Info)
// });

// localStorage.setItem('cache_route', `${location.pathname}?Page=${__params.Page}`)

// const PNInfo = new Server_Print('Notify', { 'Type': 'info' });
// const PNWarning = new Server_Print('Notify', { 'Type': 'warning' });
// const HChange = new Server_Href('Change');


var loadingTime = 5
showLoading = function (loadText) {

    if (!loadText) {
        $("#loadText").html(loadText)
    }
    $('#loadingModal').modal({ backdrop: 'static', keyboard: false });
    // var loading_element = document.getElementById('loadingModal')
    // loading_element.style.display= 'block'
}

hideLoading = function () {
    // console.log('run 关闭loading')
    // var loading_element = document.getElementById('loadingModal')
    // loading_element.style.display= 'none'
    $('#loadingModal').modal('hide');
    // $(".modal-backdrop").remove();
}

function Change_Href(Href) {


    HChange.run({ 'Href_URL': Href })
}
function Jump_Link(Link) {
    window.open(Link)
}



function notify({ from, align, icon, type, animIn, title, animOut, message }) {
    $.notify({
        icon: icon,
        title: title,
        message: message || '',
        url: ''
    }, {
        element: 'body',
        type: type,
        allow_dismiss: true,
        placement: {
            from: from,
            align: align
        },
        offset: {
            x: 20,
            y: 20
        },
        spacing: 10,
        z_index: 1031,
        delay: 2500,
        timer: 1000,
        url_target: '_blank',
        mouse_over: false,
        animate: {
            enter: animIn,
            exit: animOut
        },
        template: '<div data-notify="container" class="alert alert-dismissible alert-{0} alert--notify" role="alert">' +
            '<span data-notify="icon"></span> ' +
            '<span data-notify="title">{1}</span> ' +
            '<span data-notify="message">{2}</span>' +
            '<div class="progress" data-notify="progressbar">' +
            '<div class="progress-bar progress-bar-{0}" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;"></div>' +
            '</div>' +
            '<a href="{3}" target="{4}" data-notify="url"></a>' +
            '<button type="button" aria-hidden="true" data-notify="dismiss" class="close"><span>×</span></button>' +
            '</div>'
    });
}
function Service_Alert(Alert_Info) {

    swal({
        title: Alert_Info['Title'],
        text: Alert_Info['Content'],
        type: Alert_Info['Type'],
        buttonsStyling: false,
        confirmButtonClass: 'btn btn-sm btn-light',
        background: 'rgba(0, 0, 0, 0.96)'
    })


}


$('#layout').on('click', layout)
function layout() {
    localStorage.removeItem('User_Token')
    location.href = '/Service_Page?Page=Page_Login'
}

// 处理在线客户
$('#toggle-chat').on('click', toggleChat);

async function toggleChat() {
    const __Server_Data = new Server_Data('Service_Requests_Data', {
        'Data_Name': 'User_Info',
        'User_Token': User_Token,
    });
    loaderShow();
    user_info = await __Server_Data.async_run()
    loaderHide();
    console.log(user_info);
    if ('Status' in user_info) {
        return notify({
            type: 'danger',
            message: '请重新登录'
        })
    }
    let html = `
        <!--在线客服-->
        <div class="card text-white" id="Page_User_Contact" style="width: 375px;position: fixed;bottom: -30px;right: -375px;z-index: 9999;">
            <div class="card-header d-flex align-content-center bg-blue">
                <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem" src="/static/images/App/Sball.gif" >
                <span class="d-inline-block ml-2 font-weight-bold text-white" style="line-height: 39px;">在线客服</span>
                <button class="btn btn-dark ml-auto" id="close">关 闭</button>
            </div>
            <iframe  src="/Service_Page?Page=Page_User_Contact&user_id=${user_info.user_id}&user_name=${user_info.user_name}" frameborder="0" scrolling="no" style="width: 100%; height: 688px;"></iframe>

        </div>
    `
    let chatBox = $('#Page_User_Contact');
    if (!chatBox.length) {
        $('body').append(html);
        chatBox = $('#Page_User_Contact');
    }

    if (chatBox.css('right') == '-375px') {
        chatBox.animate({ right: '0px' }, 500);
    } else {
        chatBox.animate({ right: '-375px' }, 500);
    }
    $('#Page_User_Contact #close').off('click').click(function () {
        $('#Page_User_Contact').animate({ right: '-375px' }, 500);
        let timer = setTimeout(function () {
            $('#Page_User_Contact').remove();
            clearTimeout(timer);
        }, 500);
    });

}

function getQueryParams() {
    let params = {};
    let queryString = window.location.search.slice(1);
    let pairs = queryString.split("&");

    pairs.forEach(function (pair) {
        let keyValue = pair.split("=");
        let key = decodeURIComponent(keyValue[0]);
        let value = decodeURIComponent(keyValue[1]);
        params[key] = value;
    });

    return params;
}

function callResourceAdapter(type, url, requestContent, successCallback, failCallback) {
    if (requestContent == null) {
        requestContent = {};
    }
    if (type == null) {
        type = 'POST';
    }
    function invocationSuccess(result) {
        console.log('invocationSuccess:', result)
        var resultJson = result;
        if (resultJson.msgCode == '800') {
            if (successCallback && typeof (successCallback) == "function") {
                successCallback(resultJson.rows, resultJson.map, resultJson.vo, resultJson.msg);
            }
        }
        if (resultJson.msgCode == '801') {
            showToast(resultJson.msg);
        }
        if (resultJson.msgCode == '900') {
            var message = '系统错误，请联系管理员';
            if (resultJson.msg) {
                message = resultJson.msg;
            }
            showToast(message);
            if (failCallback && typeof (failCallback) == "function") {
                failCallback();
            }
        }
    }
    function invocationFailure(error) {
        showToast('无法连接至服务器,请稍后再试');
        if (failCallback && typeof (failCallback) == "function") {
            failCallback();
        }
    }
    $.ajax({
        type: type,
        url: serverUrl + url,
        data: JSON.stringify(requestContent),
        crossDomain: true,
        contentType: "application/json; charset=utf-8",
        // dataType: "json",
        timeout: 120000,
        statusCode: {
            404: function () {
                showToast('服务器无响应,请稍后再试')
            },
            422: function () {
                showToast('应用程序请求对象错误,请稍后再试')
            },
            403: function () {
                showToast('无访问权限')
            },
            400: function () {
                showToast('应用程序请求无效,请稍后再试')
            },
        },
        success: function (data, textStatus, jqXHR) {
            switch (jqXHR.status) {
                case 200:
                    invocationSuccess(data);
                    break;
                default:
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            invocationFailure(jqXHR);
        }
    })
}

function showToast(text, timeout) {
    $(".yui-toast-mask").remove();
    var html = [
        '<div class="yui-toast-mask">',
        '<div class="yui-toast">',
        '<div class="yui-toast-text">' + text + '</div>',
        '</div>',
        '</div>',
    ];
    var dom = $(html.join(''));
    $("body").append(dom);
    if (timeout && typeof (timeout) == 'number') {
        setTimeout(function () {
            dom.remove();
        }, timeout);
    }
    $(".yui-toast-mask").click(function () {
        dom.remove();
    });
}

function hideToast() {
    $(".yui-toast-mask").remove();
}

function loaderShow() {
    let html = `<div id="loading" class="page-loader flex-column" style="background-color: rgba(0,0,0,0.34)">
       <div class="page-loader__spinner">
           <svg viewBox="25 25 50 50">
               <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
           </svg>
       </div>
       <div>数据加载中...</div>
   </div>`
    $("body").append(html)
}

function loaderHide() {
    $("#loading").remove();
}

function getLocationDom() {
    var that = this;
    var html = "<div id='breadcrumb'>";
    $.each(LOCATION, function (index, data) {
        if (index == 0) {
            html += '<a href="javascript:void(0)" class="tip-bottom"><i class="fa fa-home"></i>' + data + '</a>';
            return;
        }
        if (index == LOCATION.length - 1) {
            html += '<a class="current">' + data + '</a>';
            return;
        }
        html += "<a href='javascript:void(0)'>" + data + "</a>";
    });
    html += "</div>";
    $("#content-header").html(html);
}

function getNav(successCallback) {
    var postNav = [];
    postNav.push('<li class="submenu" data-for= "home">', '<a href="javascript:void(0)"><span>首页</span></a>');
    postNav.push('<li class="submenu" data-for= "analysis">', '<a href="javascript:void(0)"><span>指标统计</span></a>');
    postNav.push('<li class="submenu" data-for= "trend">', '<a href="javascript:void(0)"><span>趋势排名</span></a>');
    $("#sidebar ul").html(postNav.join(''));

    if (successCallback && typeof (successCallback) == 'function') {
        successCallback();
    }
}

function bindNav(navId) {
    var HEIGHT = $("body").height() - 100;
    $("#content").height(HEIGHT);

    $(".container-fluid").slimScroll({
        height: HEIGHT + 'px', //容器高度,默认250px
        size: '5px', //滚动条宽度,默认7px
        railColor: '#000000', //滚动条背景轨迹颜色,默认#333333
        railOpacity: 0.3, //滚动条背景轨迹透明度,默认0.2
        wheelStep: 10, //滚动条滚动值,默认20
        disableFadeOut: false //是否禁用鼠标在内容处一定时间不动隐藏滚动条,当设置alwaysVisible为true时该参数无效,默认false
    });
    $("#" + navId).delegate("li[data-for='home']", "click", function () {

        window.location.href = "home.html"

    }).delegate("li[data-for='analysis']", "click", function () {

        window.location.href = "analysis.html"

    }).delegate("li[data-for='trend']", "click", function () {

        window.location.href = "trend.html"

    })
}

function last_year_month() {
    var list = [];
    var date = getFormatDate(new Date());
    var year = date.split("-")[0];
    var mouth = date.split("-")[1];
    for (var i = 0; i < 12; i++) {
        var objM = mouth - i;
        var objY = year;
        if (objM <= 0) {
            objM = objM + 12;
            objY = year - 1;
        }
        if (objM < 10) {
            objM = "0" + objM;
        }
        var obj = objY + "-" + objM;
        list.push(obj)

    }
    return list;
}

function getFormatDate(date) {
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    if (month < 10) {
        month = '0' + month.toString();
    }
    month = month.toString();
    if (day < 10) {
        day = '0' + day.toString();
    }
    day = day.toString();
    return year + '-' + month + '-' + day;
}

function last_month_day() {
    var list = [];
    var date = getFormatDate(new Date());
    var year = date.split("-")[0];
    var mouth = date.split("-")[1];
    var day = date.split("-")[2] - 1;
    for (var i = 0; i < 30; i++) {
        var objM = mouth;
        var objD = day - i;
        if (objD <= 0) {
            objD = objD + 30;
            objM = mouth - 1;
            objM = "0" + objM
        }

        var obj = year + "-" + objM + "-" + objD;
        list.push(obj)
    }
    return list;
}

function getFormatMonth(date) {
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    if (month < 10) {
        month = '0' + month.toString();
    }
    month = month.toString();
    if (day < 10) {
        day = '0' + day.toString();
    }
    day = day.toString();
    return year + '-' + month;
}

//分页
function paging(totalPage, currentPage) {
    $("#pagination").pagination({
        currentPage: currentPage,
        totalPage: totalPage,
        isShow: true,
        count: 8,
        homePageText: "首页",
        endPageText: "尾页",
        prevPageText: "上一页",
        nextPageText: "下一页",
        callback: function (current) {
            $("#current").text(current)
        }
    });
}

// 创建一个函数，用于创建对象的 Proxy
function createProxy(obj, onChange, parentIndex = null) {
    return new Proxy(obj, {
        set(target, property, value, receiver) {
            const oldValue = target[property];
            const result = Reflect.set(target, property, value, receiver);
            if (oldValue !== value) {
                onChange(target, property, oldValue, value, parentIndex);
            }
            return result;
        }
    });
}

// 创建一个函数，用于创建数组的 Proxy
function createArrayProxy(arr, onChange) {
    return new Proxy(arr, {
        get(target, property, receiver) {
            const value = Reflect.get(target, property, receiver);
            // 如果是对象，则递归代理，并传递索引
            if (typeof value === 'object' && value !== null) {
                return createProxy(value, onChange, property);
            }
            return value;
        },
        set(target, property, value, receiver) {
            const oldValue = target[property];
            const result = Reflect.set(target, property, value, receiver);
            if (oldValue !== value) {
                onChange(target, property, oldValue, value, property);
            }
            return result;
        }
    });
}

function escapeHtml(str) {
    return str.replace(/[&<>"']/g, function (match) {
        switch (match) {
            case '&': return '&amp;';
            case '<': return '&lt;';
            case '>': return '&gt;';
            case '"': return '&quot;';
            case "'": return '&#39;';
            default: return match;
        }
    });
}

function getType(value) {
    return Object.prototype.toString.call(value).slice(8, -1);
}
function debounce(func, wait) {
    let timeout;
    return function () {
        const context = this, args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}
function copyEvent(text){
    console.log(text);
    const textarea = document.createElement("textarea");
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('Oops, unable to copy', err);
    }
    document.body.removeChild(textarea);
    notify({
        type: 'success',
        message: '复制成功'
    })
}

function exportTxt(text) {
    const textContent = text
    const blob = new Blob([textContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'example.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}