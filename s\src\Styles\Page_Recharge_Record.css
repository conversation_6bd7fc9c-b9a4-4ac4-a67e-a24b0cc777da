/* 充值记录页面样式 */
.recharge-record-page {
  padding: 24px;
  min-height: 100vh;
}

/* 查询条件卡片 */
.recharge-record-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.recharge-record-page h6 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

/* 表格容器 */
.table-responsive {
  overflow-x: auto;
}

.table-responsive .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.table-responsive .ant-table-thead > tr > th {

  font-weight: 600;
 
  text-align: center;
}

.table-responsive .ant-table-tbody > tr > td {
  text-align: center;
  padding: 12px 16px;
}

.table-responsive .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.ant-btn-default {
  border-color: #d9d9d9;
}

.ant-btn-default:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 日期选择器样式 */
.ant-picker {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-picker:focus,
.ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 空间组件样式 */
.ant-space {
  width: 100%;
}

.ant-space-item {
  display: flex;
  align-items: center;
}

/* 金额样式 */
.amount-text {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #52c41a;
}

/* 交易ID样式 */
.transaction-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

/* 状态标签特殊样式 */
.status-success {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.status-processing {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.status-warning {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.status-error {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recharge-record-page {
    padding: 16px;
  }
  
  .recharge-record-page h6 {
    font-size: 14px;
  }
  
  .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .table-responsive .ant-table {
    font-size: 12px;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .ant-picker {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .recharge-record-page {
    padding: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
  
  .transaction-id {
    font-size: 10px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 成功状态样式 */
.ant-message-success .anticon {
  color: #52c41a;
}

/* 错误状态样式 */
.ant-message-error .anticon {
  color: #ff4d4f;
}

/* 警告状态样式 */
.ant-message-warning .anticon {
  color: #faad14;
}

/* 信息状态样式 */
.ant-message-info .anticon {
  color: #1890ff;
}

/* 表格滚动条样式 */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* 查询条件标签样式 */
.recharge-record-page .ant-space > .ant-space-item > span {
  font-weight: 500;
  color: #595959;
  white-space: nowrap;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 金额高亮样式 */
.amount-highlight {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

/* 支付方式标签样式 */
.payment-method-tag {
  background: #f0f2f5;
  color: #595959;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

/* 描述文本样式 */
.description-text {
  max-width: 200px;
  word-break: break-all;
  white-space: normal;
}

/* 日期范围选择器样式 */
.ant-picker-range {
  border-radius: 6px;
}

.ant-picker-range-separator {
  color: #bfbfbf;
}

/* 表格头部样式 */
.ant-table-thead > tr > th {
  position: relative;
}

.ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 1px;
  height: 1.6em;
  background: #f0f0f0;
  transform: translateY(-50%);
}

.ant-table-thead > tr > th:last-child::before {
  display: none;
}

/* 数据为空时的样式 */
.ant-table-placeholder {
  padding: 40px 0;
  text-align: center;
  color: #999;
}

/* 充值金额特殊样式 */
.recharge-amount {
  font-size: 14px;
  font-weight: 600;
  color: #52c41a;
}

.recharge-amount::before {
  content: '¥';
  margin-right: 2px;
}
