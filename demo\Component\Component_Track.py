import sys
from PySide6.QtCore import QUrl
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEngineSettings,QWebEngineProfile
from PySide6.QtNetwork import QNetworkProxy
from  PySide6 import QtWidgets
class Component_Track(QtWidgets.QLabel):
    def __init__(self):
        super().__init__()
        # self.setWindowTitle("百度地图浏览器")
        # self.resize(1200, 800)

        # 主窗口设置
        # central_widget = QWidget()
        # self.setCentralWidget(central_widget)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建浏览器实例
        self.browser = QWebEngineView()
        self.configure_webengine()
        layout.addWidget(self.browser)
        self.load_test_page()

    def configure_webengine(self):
        """配置WebEngine解决地图交互问题"""
        # 禁用代理
        QNetworkProxy.setApplicationProxy(QNetworkProxy(QNetworkProxy.ProxyType.NoProxy))

        # 关键设置
        settings = self.browser.settings()
        settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
        settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.Accelerated2dCanvasEnabled, True)
        settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
        settings.setAttribute(QWebEngineSettings.PluginsEnabled, True)

        # 禁用 WebRTC
        settings.setAttribute(QWebEngineSettings.WebRTCPublicInterfacesOnly, True)

        # 配置Profile
        profile = QWebEngineProfile.defaultProfile()
        profile.setHttpCacheType(QWebEngineProfile.NoCache)
        profile.setPersistentCookiesPolicy(QWebEngineProfile.NoPersistentCookies)

    def load_test_page(self):
        """加载测试页面"""
        self.browser.load(QUrl("http://localhost:9300/Page_Track_Media"))

    def handle_console_message(self, level, message, line, source):
        """处理控制台消息"""
        level_str = ["Info", "Warning", "Error"][level]
        print(f"JS [{level_str}]: {message} (Line {line}, {source})")

if __name__ == "__main__":
    app = QApplication([])
    browser = MapBrowser()
    browser.show()
    sys.exit(app.exec())