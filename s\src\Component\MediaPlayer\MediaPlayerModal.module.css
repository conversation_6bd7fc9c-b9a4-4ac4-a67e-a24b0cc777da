.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
  }
  
  .modalContent {
    background-color: #2A3147;
    color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 90%;
    width: 700px;
    max-height: 100vh;
    overflow-y: auto;
    cursor: default;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .mediaContainer {
    width: 100%;
    margin-bottom: 20px;
    display: block; /* 确保块级元素 */
  }
  
  .mediaElement {
    width: 100%;
    max-width: 800px;
    height: auto;
    display: block; /* 确保块级元素 */
    border-radius: 8px;
    box-shadow: 0 0 10px #00ffff;
  }
  
  .visualization {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 20px;
  }
  
  .visualization canvas {
    border: 1px solid #ccc;
    background: #1a1a1a;
  }
  
  .actions {
    margin-top: 20px;
  }
  
  .errorMessage {
    color: red;
    margin-top: 10px;
  }

  .canvasWrapper {
    position: relative;
    width: 300px;
    text-align: center;
  }

  .canvas {
    width: 100%;
    height: auto;
    border-radius: 6px;
    display: block;
    margin: 0 auto;
  }
  
  .waveformCanvas {
    border: 2px solid #00ffff;
    box-shadow: 0 0 8px #00ffff;
  }
  
  .spectrogramCanvas {
    border: 2px solid #ff00ff;
    box-shadow: 0 0 8px #ff00ff;
  }

  .graphicLabel {
    margin-top: 20px;
  }
  