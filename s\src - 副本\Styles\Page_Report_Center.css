/* 预警中心页面样式 */
.alarm-center-page {
  padding: 24px;
  min-height: 100vh;
}

.alarm-center-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 筛选区域样式 */
.alarm-center-page .ant-space {
  flex-wrap: wrap;
}

.alarm-center-page .ant-space-item {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.alarm-center-page .ant-table-thead > tr > th {
  font-weight: 600;
}

.alarm-center-page .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.alarm-center-page .ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮样式 */
.alarm-center-page .ant-btn {
  border-radius: 4px;
}

.alarm-center-page .ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.alarm-center-page .ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 输入框样式 */
.alarm-center-page .ant-input {
  border-radius: 4px;
}

.alarm-center-page .ant-select {
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alarm-center-page {
    padding: 16px;
  }

  .alarm-center-page .ant-space {
    width: 100%;
  }

  .alarm-center-page .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 报告中心页面样式 */
.report-center-page {
  padding: 24px;
  min-height: 100vh;
}

.report-center-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 筛选区域样式 */
.report-center-page .ant-space {
  flex-wrap: wrap;
}

.report-center-page .ant-space-item {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.report-center-page .ant-table-thead > tr > th {
  font-weight: 600;
}

.report-center-page .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.report-center-page .ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮样式 */
.report-center-page .ant-btn {
  border-radius: 4px;
}

.report-center-page .ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.report-center-page .ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 文件图标样式 */
.report-center-page .anticon-file-text {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-center-page {
    padding: 16px;
  }

  .report-center-page .ant-space {
    width: 100%;
  }

  .report-center-page .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
}

