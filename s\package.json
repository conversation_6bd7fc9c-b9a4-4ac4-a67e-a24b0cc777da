{"name": "sentienl", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/plots": "^2.4.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@tsparticles/react": "^3.0.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "ahooks": "^3.8.5", "antd": "^5.25.3", "axios": "^1.9.0", "crypto-js": "^4.2.0", "customize-cra": "^1.0.0", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lottie-react": "^2.4.1", "react": "^18.3.1", "react-app-rewired": "^2.2.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-responsive": "^10.0.1", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "rete": "^2.0.5", "rete-auto-arrange-plugin": "^2.0.2", "rete-connection-plugin": "^2.0.5", "rete-context-menu-plugin": "^2.0.6", "rete-engine": "^2.1.1", "rete-react-plugin": "^2.0.7", "slick-carousel": "^1.8.1", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/lodash.clonedeep": "^4.5.9", "@types/react": "^18.3.1", "@types/react-countup": "^4.3.0", "@types/react-dom": "^18.3.1", "@types/react-slick": "^0.23.13", "less": "^4.3.0", "less-loader": "^12.3.0", "terser-webpack-plugin": "^5.3.14"}}