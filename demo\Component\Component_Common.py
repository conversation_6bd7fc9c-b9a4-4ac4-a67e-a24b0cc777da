from PySide6 import QtWidgets,QtCore,QtGui


class Component_Common_QLabel_Click(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)  # 启用鼠标追踪
        self.setAlignment(QtCore.Qt.AlignCenter)

        # self.setStyleSheet("""QLabel {background: transparent;color: white;border: 0px;} QLabel:hover {color:#87cefa} """)

        self.setStyleSheet("""
            QLabel{
                    background:transparent;
                    border:0px solid #002040;
                    color:white;
                     border-radius: 4px
                    }  
                    
            QLabel:hover {
                background:rgba(0,0,0,0.6)}
            """)

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

