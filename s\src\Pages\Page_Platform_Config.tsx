import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Button, 
  Table, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Upload, 
  message,
  Space,
  Popconfirm
} from 'antd';
import { PlusOutlined, ReloadOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { useServiceRequests } from '@/Core/Core_Control';
import '../Styles/Page_Platform_Config.css';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// 数据类型定义
interface KeywordData {
  ID: number;
  XID: string;
  USER_CLASS: string;
  USER_TYPE: string;
  USER_UUID: string;
  USER_NAME: string;
  USER_SIGN: string;
  USER_EXPLAIN: string;
  USER_USE: {
    KEYWORD_INFO: string;
    FEATUREWORD_INFO: string;
    STOPWORD_INFO: string;
  };
  USER_STATUS: string;
  USER_OTHER: string;
  USER_UPDATE: string;
  Index: number;
}

interface AlarmData {
  Index: number;
  ID: number;
  ALARM_NAME: string;
  ALARM_TYPE: string[];
  ALARM_INFO: string;
  ALARM_UUID: string;
}

interface AccountData {
  ACCOUNT_PLATFORM: string;
  ACCOUNT_TYPE: string;
  ACCOUNT_NAME: string;
  ACCOUNT_URL: string;
  ACCOUNT_UUID: string;
  ID: number;
  Index: number;
}

interface SourceData {
  SOURCE_TYPE: string;
  SOURCE_NAME: string;
  SOURCE_URL: string;
  SOURCE_UUID: string;
  ID: number;
  Index: number;
}

interface UserData {
  ID: number;
  USER_FACE: string;
  USER_UUID: string;
  USER_LOGIN_ACCOUNT: string;
  USER_LOGIN_PASSWORD: string;
  USER_NICKNAME: string;
  USER_UNIT: string;
}

const Page_Platform_Config: React.FC = () => {
  const { AsyncTokenRequests } = useServiceRequests();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('keyword');
  
  // 数据状态
  const [keywordData, setKeywordData] = useState<KeywordData[]>([]);
  const [alarmData, setAlarmData] = useState<AlarmData[]>([]);
  const [accountData, setAccountData] = useState<AccountData[]>([]);
  const [sourceData, setSourceData] = useState<SourceData[]>([]);
  const [userData, setUserData] = useState<UserData[]>([]);

  // 模态框状态管理
  // 模态框状态
  const [keywordModalVisible, setKeywordModalVisible] = useState(false);
  const [alarmModalVisible, setAlarmModalVisible] = useState(false);
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [sourceModalVisible, setSourceModalVisible] = useState(false);
  const [userModalVisible, setUserModalVisible] = useState(false);

  // 表单实例
  const [keywordForm] = Form.useForm();
  const [alarmForm] = Form.useForm();
  const [accountForm] = Form.useForm();
  const [sourceForm] = Form.useForm();
  const [userForm] = Form.useForm();

  // 编辑状态管理
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [editingUUID, setEditingUUID] = useState<string>('');

  // 初始化数据
  useEffect(() => {
    loadKeywordData();
  }, []);

  // 加载方案数据
  const loadKeywordData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_manage_keyword_info',
        data_argument: '{}',
        data_kwargs: '{}'
      };
      const response = await AsyncTokenRequests(requestData);
      if (response && response.Status === 'Success') {
        // 从API响应中提取Keyword_Info_list数据
        const keywordList = response.Keyword_Info_list || [];
        setKeywordData(keywordList);
        message.success(`成功加载${keywordList.length}条方案数据`);
      } else {
        message.error('加载方案数据失败');
        setKeywordData([]);
      }
    } catch (error) {
      message.error('加载方案数据失败');
      setKeywordData([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载预警数据
  const loadAlarmData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_manage_alarm_info',
        data_argument: '{}',
        data_kwargs: '{}'
      };
      const response = await AsyncTokenRequests(requestData);
      if (response && response.Status === 'Success') {
        // 从API响应中提取Alarm_Info_list数据
        const alarmList = response.Alarm_Info_list || [];
        setAlarmData(alarmList);
        message.success(`成功加载${alarmList.length}条预警数据`);
      } else {
        message.error('加载预警数据失败');
        setAlarmData([]);
      }
    } catch (error) {
      message.error('加载预警数据失败');
      setAlarmData([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载账号数据
  const loadAccountData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_manage_account_info',
        data_argument: '{}',
        data_kwargs: '{}'
      };
      const response = await AsyncTokenRequests(requestData);
      if (response && response.Status === 'Success') {
        // 从API响应中提取Account_Info_list数据
        const accountList = response.Account_Info_list || [];
        setAccountData(accountList);
        message.success(`成功加载${accountList.length}条账号数据`);
      } else {
        message.error('加载账号数据失败');
        setAccountData([]);
      }
    } catch (error) {
      message.error('加载账号数据失败');
      setAccountData([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载数源数据
  const loadSourceData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_manage_source_info',
        data_argument: '{}',
        data_kwargs: '{}'
      };
      const response = await AsyncTokenRequests(requestData);
      if (response && response.Status === 'Success') {
        // 从API响应中提取Source_Info_list数据
        const sourceList = response.Source_Info_list || [];
        setSourceData(sourceList);
        message.success(`成功加载${sourceList.length}条数源数据`);
      } else {
        message.error('加载数源数据失败');
        setSourceData([]);
      }
    } catch (error) {
      message.error('加载数源数据失败');
      setSourceData([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载用户数据
  const loadUserData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_manage_user_info',
        data_argument: '{}',
        data_kwargs: '{}'
      };
      const response = await AsyncTokenRequests(requestData);
      if (response && response.Status === 'Success') {
        // 从API响应中提取User_Info_list数据
        const userList = response.User_Info_list || [];
        setUserData(userList);
        message.success(`成功加载${userList.length}条用户数据`);
      } else {
        message.error('加载用户数据失败');
        setUserData([]);
      }
    } catch (error) {
      message.error('加载用户数据失败');
      setUserData([]);
    } finally {
      setLoading(false);
    }
  };

  // ========== 方案设置增删改功能 ==========
  
  // 新增方案
  const handleAddKeyword = () => {
    setEditingRecord(null);
    setEditingUUID('');
    keywordForm.resetFields();
    setKeywordModalVisible(true);
  };
  
  // 编辑方案
  const handleEditKeyword = (record: KeywordData) => {
    setEditingRecord(record);
    setEditingUUID(record.USER_UUID || '');
    keywordForm.setFieldsValue({
      name: record.USER_NAME,
      category: record.USER_TYPE,
      keywords: record.USER_USE?.KEYWORD_INFO || '',
      featureWords: record.USER_USE?.FEATUREWORD_INFO || '',
      stopWords: record.USER_USE?.STOPWORD_INFO || '',
    });
    setKeywordModalVisible(true);
  };
  
  // 删除方案
  const handleDeleteKeyword = (record: KeywordData) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除方案"${record.USER_NAME}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newData = keywordData.filter(item => item.ID !== record.ID);
        setKeywordData(newData);
        message.success('删除成功！');
      },
    });
  };
  
  // 方案表单提交
  const handleKeywordSubmit = async () => {
    try {
      const values = await keywordForm.validateFields();
      
      if (editingUUID) {
        // 编辑保存
        const index = keywordData.findIndex(item => item.USER_UUID === editingUUID);
        if (index !== -1) {
          const updatedData = [...keywordData];
          updatedData[index] = {
            ...updatedData[index],
            USER_NAME: values.name,
            USER_TYPE: values.category,
            USER_USE: {
              KEYWORD_INFO: values.keywords,
              FEATUREWORD_INFO: values.featureWords || '',
              STOPWORD_INFO: values.stopWords || '',
            },
          };
          setKeywordData(updatedData);
          message.success('编辑成功！');
        }
      } else {
        // 新增保存
        // 检查方案名称是否重复
        const existingName = keywordData.find(item => item.USER_NAME === values.name);
        if (existingName) {
          message.warning('此方案名称已被使用，请更换！');
          return;
        }
        
        const newRecord: KeywordData = {
          Index: keywordData.length + 1,
          ID: keywordData.length + 1,
          XID: '',
          USER_CLASS: '',
          USER_UUID: (keywordData.length + 1000).toString(),
          USER_NAME: values.name,
          USER_TYPE: values.category,
          USER_SIGN: '',
          USER_EXPLAIN: '',
          USER_USE: {
            KEYWORD_INFO: values.keywords,
            FEATUREWORD_INFO: values.featureWords || '',
            STOPWORD_INFO: values.stopWords || '',
          },
          USER_STATUS: 'Active',
          USER_OTHER: '',
          USER_UPDATE: '',
        };
        
        setKeywordData([...keywordData, newRecord]);
        message.success('新增成功！');
      }
      
      setKeywordModalVisible(false);
      keywordForm.resetFields();
      setEditingRecord(null);
      setEditingUUID('');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  
  // 方案数据提交到服务器
  const handleSubmitKeywordData = async () => {
    try {
      setLoading(true);
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'replace_manage_keyword_info',
        data_argument: '{}',
        data_kwargs: {
          Manage_Keyword_Info_list: keywordData,
          Manage_Type: 'Manage'
        },
      };
      
      const response = await AsyncTokenRequests(requestData);
      if (response?.Status === 'Success') {
        message.success('提交更新成功！');
        // 重新加载数据
        await loadKeywordData();
      } else {
        message.error('提交更新出错！');
      }
    } catch (error) {
      console.error('提交数据失败:', error);
      message.error('提交更新网络出错！');
    } finally {
      setLoading(false);
    }
  };

  // ========== 预警设置增删改功能 ==========
  
  // 新增预警
  const handleAddAlarm = () => {
    setEditingRecord(null);
    setEditingUUID('');
    alarmForm.resetFields();
    setAlarmModalVisible(true);
  };
  
  // 编辑预警
  const handleEditAlarm = (record: AlarmData) => {
    setEditingRecord(record);
    setEditingUUID(record.ALARM_UUID || '');
    alarmForm.setFieldsValue({
      name: record.ALARM_NAME,
      type: record.ALARM_TYPE,
      content: record.ALARM_INFO,
    });
    setAlarmModalVisible(true);
  };
  
  // 删除预警
  const handleDeleteAlarm = (record: AlarmData) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除预警"${record.ALARM_NAME}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newData = alarmData.filter(item => item.ID !== record.ID);
        setAlarmData(newData);
        message.success('删除成功！');
      },
    });
  };
  
  // 预警表单提交
  const handleAlarmSubmit = async () => {
    try {
      const values = await alarmForm.validateFields();
      
      if (editingUUID) {
        // 编辑保存
        const index = alarmData.findIndex(item => item.ALARM_UUID === editingUUID);
        if (index !== -1) {
          const updatedData = [...alarmData];
          updatedData[index] = {
            ...updatedData[index],
            ALARM_NAME: values.name,
            ALARM_TYPE: values.type,
            ALARM_INFO: values.content,
          };
          setAlarmData(updatedData);
          message.success('编辑成功！');
        }
      } else {
        // 新增保存
        const existingName = alarmData.find(item => item.ALARM_NAME === values.name);
        if (existingName) {
          message.warning('此预警名称已被使用，请更换！');
          return;
        }
        
        const newRecord: AlarmData = {
          Index: alarmData.length + 1,
          ID: alarmData.length + 1,
          ALARM_UUID: (alarmData.length + 1000).toString(),
          ALARM_NAME: values.name,
          ALARM_TYPE: values.type,
          ALARM_INFO: values.content,
        };
        
        setAlarmData([...alarmData, newRecord]);
        message.success('新增成功！');
      }
      
      setAlarmModalVisible(false);
      alarmForm.resetFields();
      setEditingRecord(null);
      setEditingUUID('');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  
  // 预警数据提交到服务器
  const handleSubmitAlarmData = async () => {
    try {
      setLoading(true);
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'replace_manage_alarm_info',
        data_argument: '{}',
        data_kwargs: {
          Manage_Alarm_Info_list: alarmData,
          Manage_Type: 'Manage'
        },
      };
      
      const response = await AsyncTokenRequests(requestData);
      if (response?.Status === 'Success') {
        message.success('提交更新成功！');
        await loadAlarmData();
      } else {
        message.error('提交更新出错！');
      }
    } catch (error) {
      console.error('提交数据失败:', error);
      message.error('提交更新网络出错！');
    } finally {
      setLoading(false);
    }
  };

  // ========== 账号管理增删改功能 ==========
  
  // 新增账号
  const handleAddAccount = () => {
    setEditingRecord(null);
    setEditingUUID('');
    accountForm.resetFields();
    setAccountModalVisible(true);
  };
  
  // 编辑账号
  const handleEditAccount = (record: AccountData) => {
    setEditingRecord(record);
    setEditingUUID(record.ACCOUNT_UUID || '');
    accountForm.setFieldsValue({
      platform: record.ACCOUNT_PLATFORM,
      type: record.ACCOUNT_TYPE,
      name: record.ACCOUNT_NAME,
      url: record.ACCOUNT_URL,
    });
    setAccountModalVisible(true);
  };
  
  // 删除账号
  const handleDeleteAccount = (record: AccountData) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除账号"${record.ACCOUNT_NAME}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newData = accountData.filter(item => item.ID !== record.ID);
        setAccountData(newData);
        message.success('删除成功！');
      },
    });
  };
  
  // 账号表单提交
  const handleAccountSubmit = async () => {
    try {
      const values = await accountForm.validateFields();
      
      if (editingUUID) {
        // 编辑保存
        const index = accountData.findIndex(item => item.ACCOUNT_UUID === editingUUID);
        if (index !== -1) {
          const updatedData = [...accountData];
          updatedData[index] = {
            ...updatedData[index],
            ACCOUNT_PLATFORM: values.platform,
            ACCOUNT_TYPE: values.type,
            ACCOUNT_NAME: values.name,
            ACCOUNT_URL: values.url,
          };
          setAccountData(updatedData);
          message.success('编辑成功！');
        }
      } else {
        // 新增保存
        const newRecord: AccountData = {
          Index: accountData.length + 1,
          ID: accountData.length + 1,
          ACCOUNT_UUID: (accountData.length + 1000).toString(),
          ACCOUNT_PLATFORM: values.platform,
          ACCOUNT_TYPE: values.type,
          ACCOUNT_NAME: values.name,
          ACCOUNT_URL: values.url,
        };
        
        setAccountData([...accountData, newRecord]);
        message.success('新增成功！');
      }
      
      setAccountModalVisible(false);
      accountForm.resetFields();
      setEditingRecord(null);
      setEditingUUID('');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  
  // 账号数据提交到服务器
  const handleSubmitAccountData = async () => {
    try {
      setLoading(true);
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'replace_manage_account_info',
        data_argument: '{}',
        data_kwargs: {
          Manage_Account_Info_list: accountData,
          Manage_Type: 'Manage'
        },
      };
      
      const response = await AsyncTokenRequests(requestData);
      if (response?.Status === 'Success') {
        message.success('提交更新成功！');
        await loadAccountData();
      } else {
        message.error('提交更新出错！');
      }
    } catch (error) {
      console.error('提交数据失败:', error);
      message.error('提交更新网络出错！');
    } finally {
      setLoading(false);
    }
  };

  // ========== 数源管理增删改功能 ==========
  
  // 新增数源
  const handleAddSource = () => {
    setEditingRecord(null);
    setEditingUUID('');
    sourceForm.resetFields();
    setSourceModalVisible(true);
  };
  
  // 编辑数源
  const handleEditSource = (record: SourceData) => {
    setEditingRecord(record);
    setEditingUUID(record.SOURCE_UUID || '');
    sourceForm.setFieldsValue({
      type: record.SOURCE_TYPE,
      name: record.SOURCE_NAME,
      url: record.SOURCE_URL,
    });
    setSourceModalVisible(true);
  };
  
  // 删除数源
  const handleDeleteSource = (record: SourceData) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除数源"${record.SOURCE_NAME}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newData = sourceData.filter(item => item.ID !== record.ID);
        setSourceData(newData);
        message.success('删除成功！');
      },
    });
  };
  
  // 数源表单提交
  const handleSourceSubmit = async () => {
    try {
      const values = await sourceForm.validateFields();
      
      if (editingUUID) {
        // 编辑保存
        const index = sourceData.findIndex(item => item.SOURCE_UUID === editingUUID);
        if (index !== -1) {
          const updatedData = [...sourceData];
          updatedData[index] = {
            ...updatedData[index],
            SOURCE_TYPE: values.type,
            SOURCE_NAME: values.name,
            SOURCE_URL: values.url,
          };
          setSourceData(updatedData);
          message.success('编辑成功！');
        }
      } else {
        // 新增保存
        const newRecord: SourceData = {
          Index: sourceData.length + 1,
          ID: sourceData.length + 1,
          SOURCE_UUID: (sourceData.length + 1000).toString(),
          SOURCE_TYPE: values.type,
          SOURCE_NAME: values.name,
          SOURCE_URL: values.url,
        };
        
        setSourceData([...sourceData, newRecord]);
        message.success('新增成功！');
      }
      
      setSourceModalVisible(false);
      sourceForm.resetFields();
      setEditingRecord(null);
      setEditingUUID('');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  
  // 数源数据提交到服务器
  const handleSubmitSourceData = async () => {
    try {
      setLoading(true);
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'replace_manage_source_info',
        data_argument: '{}',
        data_kwargs: {
          Manage_Source_Info_list: sourceData,
          Manage_Type: 'Manage'
        },
      };
      
      const response = await AsyncTokenRequests(requestData);
      if (response?.Status === 'Success') {
        message.success('提交更新成功！');
        await loadSourceData();
      } else {
        message.error('提交更新出错！');
      }
    } catch (error) {
      console.error('提交数据失败:', error);
      message.error('提交更新网络出错！');
    } finally {
      setLoading(false);
    }
  };

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    switch (key) {
      case 'alarm':
        loadAlarmData();
        break;
      case 'account':
        loadAccountData();
        break;
      case 'source':
        loadSourceData();
        break;
      case 'user':
        loadUserData();
        break;
      default:
        break;
    }
  };

  // 方案设置相关
  const keywordColumns = [
    {
      title: '序号',
      dataIndex: 'Index',
      key: 'Index',
      width: 80,
    },
    {
      title: '方案名称',
      dataIndex: 'USER_NAME',
      key: 'USER_NAME',
    },
    {
      title: '所属分类',
      dataIndex: 'USER_CLASS',
      key: 'USER_CLASS',
    },
    {
      title: '预设条件',
      dataIndex: ['USER_USE', 'KEYWORD_INFO'],
      key: 'conditions',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '方案状态',
      dataIndex: 'USER_STATUS',
      key: 'USER_STATUS',
      render: (status: string) => (
        <span style={{ color: status === 'Active' ? '#52c41a' : '#faad14' }}>
          {status === 'Active' ? '激活' : status === 'Section' ? '部分' : status}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: KeywordData) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditKeyword(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个方案吗？"
            onConfirm={() => handleDeleteKeyword(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="platform-config-page">
      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="方案设置" key="keyword">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadKeywordData}
                  loading={loading}
                >
                  方案刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddKeyword}
                >
                  新增方案
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={keywordColumns}
              dataSource={keywordData}
              rowKey="ID"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="预警设置" key="alarm">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAlarmData}
                  loading={loading}
                >
                  预警刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddAlarm}
                >
                  新增预警
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '序号', dataIndex: 'Index', key: 'Index', width: 80 },
                { title: '预警名称', dataIndex: 'ALARM_NAME', key: 'ALARM_NAME' },
                { title: '预警方式', dataIndex: 'ALARM_TYPE', key: 'ALARM_TYPE', render: (types: string[]) => types.join(', ') },
                { title: '预警内容', dataIndex: 'ALARM_INFO', key: 'ALARM_INFO', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  render: (_: any, record: AlarmData) => (
                    <Space size="middle">
                      <Button 
                        type="link" 
                        icon={<EditOutlined />} 
                        onClick={() => handleEditAlarm(record)}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定删除这个预警吗？"
                        onConfirm={() => handleDeleteAlarm(record)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button type="link" danger icon={<DeleteOutlined />}>
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
              dataSource={alarmData}
              rowKey="ID"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="账号管理" key="account">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAccountData}
                  loading={loading}
                >
                  账号刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddAccount}
                >
                  新增账号
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '序号', dataIndex: 'Index', key: 'Index', width: 80 },
                { title: '账号平台', dataIndex: 'ACCOUNT_PLATFORM', key: 'ACCOUNT_PLATFORM' },
                { title: '账号类型', dataIndex: 'ACCOUNT_TYPE', key: 'ACCOUNT_TYPE' },
                { title: '账号名称', dataIndex: 'ACCOUNT_NAME', key: 'ACCOUNT_NAME' },
                { title: '账号主页', dataIndex: 'ACCOUNT_URL', key: 'ACCOUNT_URL', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  render: (_: any, record: AccountData) => (
                    <Space size="middle">
                      <Button 
                        type="link" 
                        icon={<EditOutlined />} 
                        onClick={() => handleEditAccount(record)}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定删除这个账号吗？"
                        onConfirm={() => handleDeleteAccount(record)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button type="link" danger icon={<DeleteOutlined />}>
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
              dataSource={accountData}
              rowKey="ID"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="数源管理" key="source">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadSourceData}
                  loading={loading}
                >
                  数源刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddSource}
                >
                  新增数源
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '序号', dataIndex: 'Index', key: 'Index', width: 80 },
                { title: '数源类型', dataIndex: 'SOURCE_TYPE', key: 'SOURCE_TYPE' },
                { title: '数源名称', dataIndex: 'SOURCE_NAME', key: 'SOURCE_NAME' },
                { title: '数源主页', dataIndex: 'SOURCE_URL', key: 'SOURCE_URL', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  render: (_: any, record: SourceData) => (
                    <Space size="middle">
                      <Button 
                        type="link" 
                        icon={<EditOutlined />} 
                        onClick={() => handleEditSource(record)}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定删除这个数源吗？"
                        onConfirm={() => handleDeleteSource(record)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button type="link" danger icon={<DeleteOutlined />}>
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
              dataSource={sourceData}
              rowKey="ID"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="用户管理" key="user">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadUserData}
                  loading={loading}
                >
                  用户刷新
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '序号', dataIndex: 'ID', key: 'ID', width: 80 },
                { 
                  title: '头像', 
                  dataIndex: 'USER_FACE', 
                  key: 'USER_FACE', 
                  width: 80,
                  render: (avatar: string) => (
                    avatar ? <img src={avatar} alt="头像" style={{ width: 40, height: 40, borderRadius: '50%' }} /> : '-'
                  )
                },
                { title: '用户昵称', dataIndex: 'USER_NICKNAME', key: 'USER_NICKNAME' },
                { title: '登录账号', dataIndex: 'USER_LOGIN_ACCOUNT', key: 'USER_LOGIN_ACCOUNT' },
                { title: '登录密码', dataIndex: 'USER_LOGIN_PASSWORD', key: 'USER_LOGIN_PASSWORD' },
                { title: '所属单位', dataIndex: 'USER_UNIT', key: 'USER_UNIT' },
                {
                  title: '操作',
                  key: 'action',
                  width: 100,
                  render: (_: any, record: UserData) => (
                    <Space size="middle">
                      <Button type="link" icon={<EditOutlined />}>编辑</Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={userData}
              rowKey="ID"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 方案编辑模态框 */}
      <Modal
        title={editingRecord ? "方案编辑" : "新增方案"}
        open={keywordModalVisible}
        onOk={handleKeywordSubmit}
        onCancel={() => {
          setKeywordModalVisible(false);
          keywordForm.resetFields();
          setEditingRecord(null);
        }}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={keywordForm} layout="vertical">
          <Form.Item
            label="方案名称"
            name="name"
            rules={[{ required: true, message: '请输入方案名称' }]}
          >
            <Input placeholder="请输入方案名称" />
          </Form.Item>
          <Form.Item
            label="所属分类"
            name="category"
            rules={[{ required: true, message: '请选择所属分类' }]}
          >
            <Select placeholder="请选择所属分类">
              <Option value="时事新闻">时事新闻</Option>
              <Option value="聚焦热点">聚焦热点</Option>
              <Option value="涉政信息">涉政信息</Option>
              <Option value="涉邪消息">涉邪消息</Option>
              <Option value="反动言论">反动言论</Option>
              <Option value="重点人物">重点人物</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="关键词"
            name="keywords"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入关键词，多个关键词使用中英文逗号','隔开。"
            />
          </Form.Item>
          <Form.Item
            label="特征词"
            name="featureWords"
          >
            <TextArea
              rows={3}
              placeholder="请输入特征词，多个特征词使用中英文逗号','隔开，特征词与关键词是与关系，特征词之间是或关系。"
            />
          </Form.Item>
          <Form.Item
            label="停用词"
            name="stopWords"
          >
            <TextArea
              rows={3}
              placeholder="请输入停用词，多个停用词使用中英文逗号','隔开，停用词与关键词是与关系，停用词之间是或关系。"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 预警编辑模态框 */}
      <Modal
        title={editingRecord ? "预警编辑" : "新增预警"}
        open={alarmModalVisible}
        onOk={() => setAlarmModalVisible(false)}
        onCancel={() => setAlarmModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={alarmForm} layout="vertical">
          <Form.Item
            label="预警名称"
            name="name"
            rules={[{ required: true, message: '请输入预警名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="预警方式"
            name="type"
            rules={[{ required: true, message: '请选择预警方式' }]}
          >
            <Select mode="multiple" placeholder="请选择预警方式">
              <Option value="平台预警">平台预警</Option>
              <Option value="APP预警">APP预警</Option>
              <Option value="微信公众号" disabled>微信公众号</Option>
              <Option value="钉钉预警" disabled>钉钉预警</Option>
              <Option value="短信预警" disabled>短信预警</Option>
              <Option value="邮箱预警" disabled>邮箱预警</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="预警内容"
            name="content"
            rules={[{ required: true, message: '请输入预警内容' }]}
          >
            <TextArea rows={3} placeholder="请参照下方的公式 设置预警内容" />
          </Form.Item>
          <Form.Item
            label="公式详解"
            name="formula"
          >
            <TextArea
              rows={5}
              value="{title} - 标题&#10;{content} - 内容&#10;{time} - 时间&#10;{platform} - 平台&#10;{author} - 作者"
              disabled
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 账号编辑模态框 */}
      <Modal
        title={editingRecord ? "账号编辑" : "新增账号"}
        open={accountModalVisible}
        onOk={() => setAccountModalVisible(false)}
        onCancel={() => setAccountModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={accountForm} layout="vertical">
          <Form.Item
            label="账号类型"
            name="type"
            rules={[{ required: true, message: '请选择账号类型' }]}
          >
            <Select>
              <Option value="敌对账号">敌对账号</Option>
              <Option value="仇视言论">仇视言论</Option>
              <Option value="谣言转发">谣言转发</Option>
              <Option value="意见领袖">意见领袖</Option>
              <Option value="政治宣传">政治宣传</Option>
              <Option value="网络水军">网络水军</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="账号平台"
            name="platform"
            rules={[{ required: true, message: '请选择账号平台' }]}
          >
            <Select>
              <Option value="抖音">抖音</Option>
              <Option value="快手">快手</Option>
              <Option value="哔哩哔哩">哔哩哔哩</Option>
              <Option value="知乎">知乎</Option>
              <Option value="微博">微博</Option>
              <Option value="小红书">小红书</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="账号名称"
            name="name"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="账号主页"
            name="url"
            rules={[{ required: true, message: '请输入账号主页' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 数源编辑模态框 */}
      <Modal
        title={editingRecord ? "数源编辑" : "新增数源"}
        open={sourceModalVisible}
        onOk={() => setSourceModalVisible(false)}
        onCancel={() => setSourceModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={sourceForm} layout="vertical">
          <Form.Item
            label="数源类型"
            name="type"
            rules={[{ required: true, message: '请选择数源类型' }]}
          >
            <Select>
              <Option value="敌对网站">敌对网站</Option>
              <Option value="仇视言论">仇视言论</Option>
              <Option value="谣言转发">谣言转发</Option>
              <Option value="意见领袖">意见领袖</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="数源名称"
            name="name"
            rules={[{ required: true, message: '请输入数源名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="数源主页"
            name="url"
            rules={[{ required: true, message: '请输入数源主页' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户编辑模态框 */}
      <Modal
        title="用户编辑"
        open={userModalVisible}
        onOk={() => setUserModalVisible(false)}
        onCancel={() => setUserModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={userForm} layout="vertical">
          <Form.Item label="用户头像自定义上传">
            <Upload
              name="file"
              accept=".jpg,.png,.jpeg"
              beforeUpload={() => false}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>
          <Form.Item
            label="登录账号"
            name="loginAccount"
            rules={[{ required: true, message: '请输入登录账号' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            label="登录密码"
            name="password"
            rules={[{ required: true, message: '请输入登录密码' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            label="用户昵称"
            name="nickname"
            rules={[{ required: true, message: '请输入用户昵称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="用户单位"
            name="unit"
            rules={[{ required: true, message: '请输入用户单位' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Page_Platform_Config;
