# -*- coding: utf-8 -*-
import time,os,sys
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
import sys
import cv2
from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget
from PySide6.QtGui import QImage, QPixmap
from PySide6.QtCore import QTimer, Qt
from PySide6.QtCore import QThread, Signal, Slot
import cv2





class PopupDialog_Test(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("提示")
        self.setFixedSize(348, 170)
        self.setStyleSheet("background:rgba(38, 38, 38, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;")
        QVBoxLayout_Main = QtWidgets.QVBoxLayout()

        QLabel_Config_Copyright = QtWidgets.QLabel()
        # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # QLabel_Config_Copyright.setMaximumSize(250, 80)
        QLabel_Config_Copyright.setStyleSheet('background:rgba(38, 38, 38, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;')
        QLabel_Config_Copyright.setText("抱歉，您是测试用户无法使用该功能。")

        QLabel_Config_Policy = QtWidgets.QLabel()
        # QLabel_Config_Policy.setMinimumSize(250, 80)
        # QLabel_Config_Policy.setMaximumSize(250, 80)
        QLabel_Config_Policy.setStyleSheet('background:rgba(38, 38, 38, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;')
        QLabel_Config_Policy.setText("请联系客服升级正式用户。")

        # Copyright @2018-2024 Sentinel All rights reserved
        # 许可协议 | 保密政策
        QVBoxLayout_Main.addWidget(QLabel_Config_Copyright,alignment=QtCore.Qt.AlignCenter)
        QVBoxLayout_Main.addWidget(QLabel_Config_Policy,alignment=QtCore.Qt.AlignCenter)

        QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        QVBoxLayout_Self.addStretch(1)
        QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        QVBoxLayout_Self.addStretch(1)


        self.setLayout(QVBoxLayout_Self)


class Video_1(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("视频Channel003")
        self.setFixedSize(640, 380)
        self.setStyleSheet("background: #002040")
        # QVBoxLayout_Main = QtWidgets.QVBoxLayout(self)
        # QVBoxLayout_Main.setSpacing(0)  # 内边界
        # QVBoxLayout_Main.setContentsMargins(0, 0, 0, 0)  # 外边
        self.video_source =""
        self.video_label = QLabel(self)
        self.video_label.setGeometry(10, 10, 640, 380)
        self.video_label.setFixedSize(640, 380)

        self.stop_button = QtWidgets.QPushButton('Stop', self)
        self.stop_button.setGeometry(0, 0, 100, 30)
        self.stop_button.clicked.connect(self.stop_video)

        self.video_thread = VideoCaptureThread(self.video_source)
        self.video_thread.frame_updated.connect(self.update_frame)
        self.video_thread.start()

    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()

    @Slot(QImage)
    def update_frame(self, frame):
        self.video_label.setPixmap(QPixmap.fromImage(frame))
        # pix = QPixmap.fromImage(frame)
        # self.video_label.setPixmap(pix.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))



















        # self.VideoPlayer = VideoPlayer("1.mp4")
        # self.VideoPlayer.show()

        # QVBoxLayout_Main.addWidget(self.VideoPlayer)

        # QLabel_Config_Copyright = QtWidgets.QLabel()
        # # QLabel_Config_Copyright.setMinimumSize(250, 80)
        # # QLabel_Config_Copyright.setMaximumSize(250, 80)
        # QLabel_Config_Copyright.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Config_Copyright.setText("抱歉，您是测试用户无法使用该功能。")
        #
        # QLabel_Config_Policy = QtWidgets.QLabel()
        # # QLabel_Config_Policy.setMinimumSize(250, 80)
        # # QLabel_Config_Policy.setMaximumSize(250, 80)
        # QLabel_Config_Policy.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Config_Policy.setText("请联系客服升级正式用户。")
        #
        # # Copyright @2018-2024 Sentinel All rights reserved
        # # 许可协议 | 保密政策
        # QVBoxLayout_Main.addWidget(QLabel_Config_Copyright,alignment=QtCore.Qt.AlignCenter)
        # QVBoxLayout_Main.addWidget(QLabel_Config_Policy,alignment=QtCore.Qt.AlignCenter)
        #
        # QVBoxLayout_Self = QtWidgets.QVBoxLayout()
        # QVBoxLayout_Self.addStretch(1)
        # QVBoxLayout_Self.addLayout(QVBoxLayout_Main)
        # QVBoxLayout_Self.addStretch(1)
        #
        #
        # self.setLayout(QVBoxLayout_Self)


class VideoCaptureThread(QtCore.QThread):
    frame_updated = QtCore.Signal(QImage)

    def __init__(self, video_source):
        super().__init__()
        # self.video_source = r"D:\Sentinel Foundation\Bin\System\OS\Page\Page_Utils\1.mp4"
        self.video_source = "rtsp://admin:csc888888!@192.168.123.119:554/Streaming/Channels/101"
        self.cap = cv2.VideoCapture( self.video_source)
        self.running = True

    def run(self):
        while self.running and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break
            # Convert the frame from BGR to RGB format
            frame_resized = cv2.resize(frame, (640, 380))
            rgb_frame = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            # Convert the frame to QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            # convert_to_Qt_format = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            convert_to_Qt_format = QImage(rgb_frame.data, 640, 380, bytes_per_line, QImage.Format_RGB888)
            # Emit the frame to the main thread
            qimg_resized = convert_to_Qt_format.scaled(640, 380, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.frame_updated.emit(qimg_resized)

    def stop(self):
        self.running = False
        self.cap.release()


class VideoPlayer(QWidget):
    def __init__(self, video_source):
        super().__init__()
        self.video_source = video_source
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Video Player')
        self.setGeometry(0, 0, 320, 240)

        self.video_label = QLabel(self)
        self.video_label.setGeometry(0, 0, 320, 240)

        self.stop_button = QtWidgets.QPushButton('Stop', self)
        self.stop_button.setGeometry(0, 0, 100, 30)
        self.stop_button.clicked.connect(self.stop_video)

        self.video_thread = VideoCaptureThread(self.video_source)
        self.video_thread.frame_updated.connect(self.update_frame)
        self.video_thread.start()

    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()

    @Slot(QImage)
    def update_frame(self, frame):
        # self.video_label.setPixmap(QPixmap.fromImage(frame))
        pix = QPixmap.fromImage(frame)
        self.video_label.setPixmap(pix.scaled(320,240))

class VideoPlayer_1(QWidget):
    def __init__(self, *args):
        super().__init__()
        print(args)
        self.initUI()
        self.cap = cv2.VideoCapture(r"D:\Sentinel Foundation\Bin\System\OS\Page\Page_Utils\2.wmv")

        if not self.cap.isOpened():
            print("Error: Cannot open video.")
            sys.exit()

        self.timer = QTimer()
        self.timer.timeout.connect(self.updateFrame)
        self.timer.start(40)  # Set timer for 30ms (approx. 33 fps)

    def initUI(self):
        self.setWindowTitle('Video Player')
        self.setGeometry(0, 0, 320, 240)

        self.layout = QVBoxLayout()

        self.label = QLabel(self)
        self.label.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.label)

        self.setLayout(self.layout)

    def updateFrame(self):
        ret, frame = self.cap.read()

        if ret:
            # Convert the frame from BGR color space to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Convert the frame to a QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            convert_to_Qt_format = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)

            # Convert QImage to QPixmap and set it to the label
            pix = QPixmap.fromImage(convert_to_Qt_format)
            self.label.setPixmap(pix.scaled(self.label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Restart the video if it reaches the end

    def closeEvent(self, event):
        # Release the video capture object when the window is closed
        self.cap.release()
        super().closeEvent(event)