import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "./Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "./Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");



export class ButtonControl extends ClassicPreset.Control {
    onClick: () => void; // 必须为公共可写属性
  
    constructor(
      public label: string,
      onClick: () => void
    ) {
      super();
      this.onClick = onClick;
    }
  }


export class ProgressControl_Object extends ClassicPreset.Control {
    constructor(public percent: number) {
      super();
    }

    setPercent(newPercent: number) {
        this.percent = newPercent;
      }
  }
  

  export class ButtonControl_Start extends ClassicPreset.Control {
    // onClick: () => void; // 必须为公共可写属性

    // constructor(
    //   public label: string,
    //   onClick: () => void
    // ) {
    //   super();
    //   this.onClick = onClick;
    // }
    public onClick?: (nodeId: string, action: string) => void;
    constructor(
      public label: string,
      // onClick: (action: "detail" | "edit" | "other") => void
    ) {
      super();
      // this.onClick = onClick;
    }
    public setButtonAction(callback: (nodeId: string, action: string) => void) {
      this.onClick = callback;
    }
  }



  export class ButtonControl_Release extends ClassicPreset.Control {
    onClick: () => void; // 必须为公共可写属性
  
    constructor(
      public label: string,
      onClick: () => void
    ) {
      super();
      this.onClick = onClick;
    }
  }



  export class ButtonControl_More extends ClassicPreset.Control {
    // onClick:(action: "detail" | "edit" | "other") => void; // 必须为公共可写属性
    public onClick?: (nodeId: string, action: string) => void;
    constructor(
      public label: string,
      // onClick: (action: "detail" | "edit" | "other") => void
    ) {
      super();
      // this.onClick = onClick;
    }
    public setButtonAction(callback: (nodeId: string, action: string) => void) {
      this.onClick = callback;
    }
  
  }

  
export class ImageControl_Face extends ClassicPreset.Control {
    constructor(
      public label: string, 
      public onClick: () => void) 
      // public onChange: (value: string) => void)
      {
      super();
    }
  }
  
export class AIControl_DeepSeek extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
      this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
      this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
      this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
      this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }
export class SystemControl_Object extends ClassicPreset.Control {
    constructor(
      public Title: string, 

      public Percent: number,
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Object: Record<string, any>) {
      const safeGet = (key: string) => Object?.[key] || "未知";

      const safeParseInt = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
      };



      this.Title         = safeGet("Title");
      this.Percent       = safeParseInt(safeGet("Percent"));
   
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }



  export class SystemControl_Output extends ClassicPreset.Control {
    constructor(
      public Title: string, 

      public Percent: number,
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Object: Record<string, any>) {
      const safeGet = (key: string) => Object?.[key] || "未知";

      const safeParseInt = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
      };



      this.Title         = safeGet("Title");
      this.Percent       = safeParseInt(safeGet("Percent"));
   
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }





export class DataSourceControl_Rumor extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public language: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = (safeGet("INTELLIGENCE_TITLE") || "").slice(0, 30)
      this.date        = safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = safeGet("INTELLIGENCE_NAME_CN");
      this.language    = safeGet("INTELLIGENCE_SOURCE_TYPE");
      this.author      = safeGet("INTELLIGENCE_AUTHOR");
      this.image       = safeGet("INTELLIGENCE_TYPE");
      this.url         = safeGet("INTELLIGENCE_URL");
      this.content     = "\n"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }
export class PreviewControl_Rumor extends ClassicPreset.Control {
    
    constructor(
      public UUID_Source: string, 
      public Method: string, 
      public Type: string, 
      public Score: string, 
      public Direction: string, 
      public Theme: string, 
      public Object: string, 
      public Image: string, 
      public Bidding: string, 
      public Keyword: string, 
      public Title: string, 
      public Content: string, 

      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {

      const Score_Info = (key: string) => Rumor?.[key] || "未知";
      this.UUID_Source              = `【UUID】:${Score_Info("UUID")}  【数源】:${Score_Info("Source")}`;
      this.Method                   = Score_Info("Method")
      this.Type                     = Score_Info("Type")
      this.Score                    = Score_Info("Score")
      this.Direction                = Score_Info("Direction")
      this.Theme                    = Score_Info("Theme")
      this.Object                   = Score_Info("Object");
      this.Image                    = `【图片】:${Score_Info("Image")}`;
      this.Bidding                  = `【中标】:${Score_Info("Bidding")}`;
      this.Keyword                  = `【关键词】:\n${Score_Info("Keyword")}`;
      this.Title                    = (Score_Info("Title") || "").slice(0, 30)
      this.Content                  = `\n${Score_Info("Content")}`;
      // this.Content                  = `\n${Score_Info("Content")}`;
      
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }
export class ModelControl_Rumor extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
      this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
      this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
      this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
      this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

export class DataSourceControl_Statistic extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
      this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
      this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
      this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
      this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }







  export class SystemControl_Status extends ClassicPreset.Control {
    constructor(
      public Title: string, 

      public Percent: number,
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Object: Record<string, any>) {
      const safeGet = (key: string) => Object?.[key] || "未知";

      const safeParseInt = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
      };



      this.Title         = safeGet("Title");
      this.Percent       = safeParseInt(safeGet("Percent"));
   
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }



  























  export function Progress_Object(props: { data: ProgressControl_Object }) {
    // return <Progress type="circle" percent={props.data.percent} />;
    return <Progress 
    style={{
        background:"rgba(255,255,255,0.0)",
        width: "358px", // 设置进度条的长度
        height: "20px", // 设置进度条的高度
        border: "0px solid green", // 设置边框颜色为绿色
        borderRadius: "5px", // 可选，让进度条的边角更圆润
        lineHeight: "10px", // 设置行高与进度条高度一致
        padding: 0,          // 清除默认内边距
        margin: 0            // 清除默认外边距
    }}
    // type="dashboard"
    type="line"
     strokeColor="#52c41a"
    trailColor="rgba(255,255,255,0.6)"
    
    percent={props.data.percent} 
    // 添加文字样式覆盖👇
    format={(percent) => (
        <div style={{ 
          position: "relative",
          top: "0px",       // 反向偏移量
          fontSize: "12px",
          lineHeight: "10px",

          color: "#fff"
        }}>
          {percent}%
        </div>
      )}

    />;
  }

  export function Node_System_Object(props: { data: SystemControl_Object }) {
    return (
    
    <Layout style={{ width: '100%',background:"transparent" }}>


      <Space direction="vertical" size="middle" style={{ width: '100%'}}>


      <Layout   style={{height:40,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 4,background:"#666",flex:"1",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} > 课题:《境外突出谣言预警Agent》 </span></Layout>


                      
      </Space>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Progress 
              style={{
                  background:"rgba(255,255,255,0.0)",
                  width: "358px", // 设置进度条的长度
                  height: "20px", // 设置进度条的高度
                  border: "0px solid green", // 设置边框颜色为绿色
                  borderRadius: "5px", // 可选，让进度条的边角更圆润
                  lineHeight: "10px", // 设置行高与进度条高度一致
                  padding: 0,          // 清除默认内边距
                  margin: 0            // 清除默认外边距
              }}
              // type="dashboard"
              type="line"
              strokeColor="#52c41a"
              trailColor="rgba(255,255,255,0.6)"
              
              percent={props.data.Percent} 
              // 添加文字样式覆盖👇
              format={(percent) => (
                  <div style={{ 
                    position: "relative",
                    top: "0px",       // 反向偏移量
                    fontSize: "12px",
                    lineHeight: "80px",

                    color: "#fff"
                  }}>
                    {percent}%
                  </div>
                )}

              />
              </Space>

        
    </Layout>
    );
    // return (
    
    
  // <Layout>

  //     <Row>
  //       <Col>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //       </Col>
  //     </Row>


  //   </Layout>)
  }

  export function Node_DataSource_Statistic(props: { data: DataSourceControl_Statistic }) {
    // return <Progress type="circle" percencolort={props.data.percent} />;

    const Title_1=<span style={{color:"white"}} >今日数据</span>
    const Title_2=<span style={{color:"white"}} >待处理</span>
    const Title_3=<span style={{color:"white"}} >已分析</span>
    const Title_4=<span style={{color:"white"}} >突出谣言</span>


    const Title_5=<span style={{color:"white"}} >错误</span>
    const Title_6=<span style={{color:"white"}} >成功</span>
    const Title_7=<span style={{color:"white"}} >Token</span>
    const Title_8=<span style={{color:"white"}} >大模型</span>




    const Layout_1 = <Layout style={{background:"transparent"}}>
    <Row gutter={16}>
        <Col span={12}>
          <Card variant="borderless" style={{background:"rgba(0,0,0,0.3)"}}>
        
            <Statistic
                 title={Title_1}
              value={1128}
              // precision={2}
              valueStyle={{ color: 'rgba(237, 236, 243, 0.8)' }}
              prefix={<CloudServerOutlined />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
            
            title={Title_2}
              value={45}
              // precision={2}
              valueStyle={{ color: "blue" }}
              prefix={<CheckOutlined />}
              suffix="条"
             
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={16}  style={{marginTop:8}} >

        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
              title={Title_3}
              value={789}
              precision={0}
              valueStyle={{ color: "green" }}
              prefix={<CheckOutlined />}
              suffix="条"
            
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
              title={Title_4}
              value={5}
              precision={0}
              valueStyle={{ color: "red" }}
              prefix={<AuditOutlined />}
              suffix="条"
              
            />
          </Card>
        </Col>
      </Row>

  
    </Layout>
    const Layout_2 = <Layout style={{background:"transparent"}}>
    <Row gutter={16}>
       <Col span={12}>
         <Card variant="borderless" style={{background:"rgba(0,0,0,0.3)"}}>
       
           <Statistic
                title={Title_5}
             value={91128}
             // precision={2}
             valueStyle={{ color: 'red' }}
             prefix={<ExclamationCircleOutlined />}
             suffix="条"
           />
         </Card>
       </Col>
       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
           
           title={Title_6}
             value={1232345}
             // precision={2}
             valueStyle={{ color: "purple" }}
             prefix={<CheckCircleOutlined />}
             suffix="条"
            
           />
         </Card>
       </Col>
     </Row>
     <Row gutter={16}  style={{marginTop:8}} >

       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
             title={Title_7}
             value={22119}
             precision={0}
             valueStyle={{ color: "yellow" }}
             prefix={<FieldStringOutlined />}
             suffix="个"
           
           />
         </Card>
       </Col>
       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
             title={Title_8}
             value={2}
             precision={0}
             valueStyle={{ color: "cyan" }}
             prefix={<ApartmentOutlined />}
             suffix="个"
             
           />
         </Card>
       </Col>
     </Row>

 
 </Layout>



    const contentStyle: React.CSSProperties = {
      height: '100%',
      color: '#fff',
      // lineHeight: '160px',
      marginTop:-10,
      textAlign: 'center',
      background: 'tranparent',
    };










    return (
     <Carousel 
        autoplay
        autoplaySpeed={5000}
        speed={500}
        >
            <div>
             
              <h3 style={contentStyle}> {Layout_1}</h3>
            </div>
            <div>
              <h3 style={contentStyle}>{Layout_2}</h3>
            </div> 
      
          </Carousel>
    
  )
  }

  


export  function CustomButton(props: { data: ButtonControl }) {
    return (
      <Button
        onPointerDown={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
        // onClick={props.data.onClick}
        onClick={props.data.onClick}
        style={{
            position: 'absolute',
            left:"35%",
            top:"50%",
            backgroundColor: '#1890ff', // 背景颜色
            color: '#fff', // 文字颜色
            border: 'none', // 去掉边框
            borderRadius: '4px', // 圆角
            padding: '8px 16px', // 内边距
            fontSize: '8px', // 字体大小
            fontWeight: 'bold', // 字体粗细
            cursor: 'pointer', // 鼠标指针样式
          }}
      >
        {props.data.label}
      </Button>
    );
  }
export  function Button_Release(props: { data: ButtonControl_Release }) {
    return (
      <Button
        onPointerDown={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
        // onClick={props.data.onClick}
        onClick={props.data.onClick}
        style={{
            position: 'absolute',
            right:"8px",
            bottom:"8px",
            backgroundColor: '#1890ff', // 背景颜色
            color: '#fff', // 文字颜色
            border: 'none', // 去掉边框
            borderRadius: '4px', // 圆角
            padding: '8px 16px', // 内边距
            fontSize: '8px', // 字体大小
            fontWeight: 'bold', // 字体粗细
            cursor: 'pointer', // 鼠标指针样式
          }}
      >
        {props.data.label}

       
      </Button>
    );
  }

export  function Button_More(props: { data: ButtonControl_More }) {



    
    const items: MenuProps['items'] = [
      {
        key: '1',
        label: (
          <a target="_blank" rel="noopener noreferrer"  
          // onClick={props.data.onClick("detail")} 
          onClick={() => props.data.onClick?.("","detail") }// 触发回调
          >
            详情
          </a>
        ),
      },
      {
        key: '2',
        label: (
          <a target="_blank" rel="noopener noreferrer"  
          onClick={() => props.data.onClick?.("","edit") }// 触发回调
          >
            编辑
          </a>
        ),
      },
      {
        key: '3',
        label: (
          <a target="_blank" rel="noopener noreferrer" 
          onClick={() => props.data.onClick?.("","other") }// 触发回调
          >
           其他
          </a>
        ),
      },
    ];
    return (

      <Flex 
      style={{
              position: 'absolute',
              right:"8px",
              top:"8px",
              backgroundColor: 'rgba(0,0,0,0.1)', // 背景颜色
              color: '#fff', // 文字颜色
              border: 'none', // 去掉边框
              borderRadius: '4px', // 圆角
              // padding: '8px 16px', // 内边距
              fontSize: '18px', // 字体大小
              fontWeight: 'bold', // 字体粗细
              cursor: 'pointer', // 鼠标指针样式
            }}
      >

      <Dropdown menu={{ items }} placement="bottomLeft" arrow>
              <Button 
              style={{
                color: '#fff', // 文字颜色
                backgroundColor: 'rgba(0,0,0,0.1)',
                border: 'none', // 去掉边框
              }}
              
              ><EllipsisOutlined   style={{color:"white",fontSize: '28px',}}/></Button>
            </Dropdown>


      </Flex>

    );
  }



export  function Image_Face(props: { data:   ImageControl_Face }) {
    
    // const options = props.data.options.map(opt => ({
    //   value: opt.value,
    //   label: opt.label
    // }));
  
    return (
      
      <div  style={{ background:"",width: '20%' ,height:'20px'}}>
        <label style={{ color:"white"}}>{props.data.label}</label>
        {/* <Image
          src='https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'
          style={{ background:"red",width: '100%' }}
  
  
        /> */}

      <SnippetsOutlined />
      </div>
    );
  }
  

const IntegerStep: React.FC = () => {
    const [inputValue, setInputValue] = useState(1);
  
    const onChange: InputNumberProps['onChange'] = (newValue) => {
      setInputValue(newValue as number);
    };
  
    return (
      <Row style={{ display: 'flex', alignItems: 'center', justifyContent: 'start' }}>
      <Col span={2}    >
         <p>数据</p>
       
      </Col>
        <Col span={12}>
          <Slider
            min={1}
            max={20}
            onChange={onChange}
            value={typeof inputValue === 'number' ? inputValue : 0}
          />
        </Col>
        <Col span={4}>
          <InputNumber
            min={1}
            max={20}
            style={{ margin: '0 16px' }}
            value={inputValue}
            onChange={onChange}
          />
        </Col>
      </Row>
    );
  };
  
const DecimalStep: React.FC = () => {
const [inputValue, setInputValue] = useState(0);
const onChange: InputNumberProps['onChange'] = (value) => {
      if (Number.isNaN(value)) {
        return;
      }
      setInputValue(value as number);
    };
  
    return (
      <Row style={{ display: 'flex', alignItems: 'center', justifyContent: 'start', width: '100%' }}>
        <Col span={2}    >
           <p>数据</p>
         
        </Col>
        <Col span={15} >
          <Slider
            min={0}
            max={1}
            onChange={onChange}
            value={typeof inputValue === 'number' ? inputValue : 0}
            step={0.01}
          />
        </Col>
        <Col span={6}>
          <InputNumber
            min={0}
            max={1}
            style={{ margin: '0 16px' ,width:50}}
            step={0.01}
            value={inputValue}
            onChange={onChange}
          />
        </Col>
      </Row>
    );
  };







const gutters: Record<PropertyKey, number> = {};
const vgutters: Record<PropertyKey, number> = {};
const colCounts: Record<PropertyKey, number> = {};
[8, 16, 24, 32, 40, 48].forEach((value, i) => {
  gutters[i] = value;
});
[8, 16, 24, 32, 40, 48].forEach((value, i) => {
  vgutters[i] = value;
});
[2, 3, 4, 6, 8, 12].forEach((value, i) => {
  colCounts[i] = value;
});


let Layout_List=[
  <Layout  style={{background:"red"}}>

        <Card title="数源:" variant="borderless" style={{ width: "100%",height:30 }}>

          <p>电报数据</p>

        </Card>



  </Layout>,
    <Layout  style={{background:"red"}}>

    你好



  </Layout>,
    <Layout  style={{background:"red"}}>

    你好



  </Layout>,
    <Layout  style={{background:"red"}}>

    你好



  </Layout>,
  
]



export  function Node_DataSource_Rumor(props: { data:DataSourceControl_Rumor }) {
  

  
    // 主题配置
const theme: ThemeConfig = {
  components: {
    Carousel: {
      // 核心配置
      motionDurationSlow: "0.1s",      // 文字颜色
      
    },
  },
};

// this.title       = safeGet("INTELLIGENCE_TITLE");
// this.date        = safeGet("INTELLIGENCE_WEB_TIME");
// this.source      = safeGet("INTELLIGENCE_NAME_CN");
// this.author      = safeGet("INTELLIGENCE_AUTHOR");
// this.image       = safeGet("INTELLIGENCE_TYPE");
// this.url         = safeGet("INTELLIGENCE_URL");
// this.content     = safeGet("INTELLIGENCE_CONTENT");
  return (
    
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>

      <Space direction="horizontal" size="middle" style={{ width: '100%' }} > 
        <Badge.Ribbon text="数源" placement="start"  color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.source}</span></Layout></Badge.Ribbon>
        <Badge.Ribbon text="类型" placement="start"  color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >境外开源数源</span></Layout></Badge.Ribbon>
      
     </Space>

     <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="日期" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.date}</span></Layout></Badge.Ribbon>
        <Badge.Ribbon text="作者" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.author}</span></Layout></Badge.Ribbon>
      
     </Space>
     {/* <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="图片" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.image}</span></Layout></Badge.Ribbon>
        <Badge.Ribbon text="作者" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.author}</span></Layout></Badge.Ribbon>
      
     </Space> */}

     <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="标题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:450,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.title}</span></Layout></Badge.Ribbon>
     </Space>
     <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
        <Badge.Ribbon text="内容" placement="start" color="#3e6ae1">
          <TextArea
                value= {props.data.content} 
                onChange={(e) => {
                  const newValue = e.target.value;
 
                }}
                rows={7}
                style={{ background:"rgba(248, 240, 240, 0.3)",width: 450,color:"white" }}
              />
        </Badge.Ribbon>
        </Space>
      
    
    {/* <Badge.Ribbon text="Hippies">
      <Card title="Pushes open the window" size="small">
       
      </Card>
    </Badge.Ribbon> */}
    
    </Space>
  );
}



export  function Node_Preview_Rumor(props: { data:PreviewControl_Rumor }) {
  const [value, setValue] = useState(3);

  const [tags, setTags] = useState(['Custom Tag 1', 'Custom Tag 2']);
  // this.Tags.push(props.data.Keyword)
  // 主题配置
  const desc = ['', '', '', '', ''];
const theme: ThemeConfig = {
components: {
  Carousel: {
    // 核心配置
    motionDurationSlow: "0.1s",      // 文字颜色
    
  },

  Rate:{
    starBg:"#000",
    starSize:15,
  }
},
};


return (
  
  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
       <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
       <Badge.Ribbon text="Token" placement="start" color="blue">
       <Layout   style={{height:150,width:450,  borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"center"}}  > 
              <Layout style={{marginTop:40,marginLeft:8,background:"rgba(117, 101, 207, 0.0)"}}>
                  {/* <Component_Keys 
                  
                  initialTags={tags} 
                  onTagsChange={(newTags) => {
                    console.log('Tags changed:', newTags);
                    setTags(newTags);
                  }} 
                  /> */}


                  {props.data.Keyword} 


              </Layout>
       </Layout>
             
        </Badge.Ribbon>





                                                                                                                                                                                               
    </Space>

    <Space direction="horizontal" size="middle" style={{ width: '100%' }} > 
      <Badge.Ribbon text="方向" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Direction}</span></Layout></Badge.Ribbon>
      <Badge.Ribbon text="主题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Theme}</span></Layout></Badge.Ribbon>
    
   </Space>

   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
      <Badge.Ribbon text="分类" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Type}</span></Layout></Badge.Ribbon>
      <Badge.Ribbon text="课题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Object}</span></Layout></Badge.Ribbon>
    
   </Space>
   {/* <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Score}</span> */}

   {/* const [value, setValue] = useState(3);
  return (
    <Flex gap="middle" vertical>
      <Rate tooltips={desc} onChange={setValue} value={value} />{value ? <span>{desc[value - 1]}</span> : null}
      
    </Flex> */}
 <ConfigProvider theme={theme}>

   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
      <Badge.Ribbon text="评分" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", justifyItems:"conter"}}  >  <Rate disabled  count={5} defaultValue={3}  style={{marginTop:10,marginLeft:102,color:"white"}}/></Layout></Badge.Ribbon>
      <Badge.Ribbon text="结果" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >无效</span></Layout></Badge.Ribbon>
    
   </Space>
  </ConfigProvider>
   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="标题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:450,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Title}</span></Layout></Badge.Ribbon>
     </Space>
   <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
        <Badge.Ribbon text="内容" placement="start" color="#3e6ae1">
          <TextArea
                value= {props.data.Content} 
                onChange={(e) => {
                  const newValue = e.target.value;
 
                }}
                rows={7}
                style={{ background:"rgba(248, 240, 240, 0.3)",width: 450,color:"white" }}
              />
        </Badge.Ribbon>
        </Space>
      

    
  
  {/* <Badge.Ribbon text="Hippies">
    <Card title="Pushes open the window" size="small">
     
    </Card>
  </Badge.Ribbon> */}
  
  </Space>
);
}





interface StepModelProps {
  label?: string;
  value?: number;
  onChange?: (value: number) => void;
}

const Step_Model: React.FC<StepModelProps> = (props) => {
  const [inputValue, setInputValue] = useState(props.value || 1);

  const onChange: InputNumberProps['onChange'] = (newValue) => {
    const val = newValue as number;
    setInputValue(val);
    props.onChange?.(val);
  };
  const textStyle = {
    color: 'white',
    margin: 0
  };
  // 主题配置
const theme: ThemeConfig = {
  components: {
    InputNumber: {
      // 核心配置
      colorText: "#ffffff",      // 文字颜色
      colorBorder: "#1890ff",     // 默认边框色
      colorPrimaryHover: "#40a9ff", // 悬浮态边框色
      colorPrimary: "#1677ff",    // 聚焦态边框色
      fontSize: 16,               // 字号（覆盖 inputFontSize）
      handleBg:"rgba(255,255,255,0.1)",
      handleBorderColor:"rgba(255,255,255,0.1)",
      // 其他扩展配置
      colorError: "#ff4d4f",      // 错误状态颜色
      colorBgContainer: "#f6ffed", // 背景色
      // paddingBlock: -28,            // 垂直内边距
      // paddingInline: -26           // 水平内边距
    },
  },
};

  return (
    <Row style={{ display: 'flex', alignItems: 'center', justifyContent: 'start', background: "transparent" ,color: 'white' }}>
      <Col span={4} style={{ background: "transparent" }}>
      <p style={textStyle}>{props.label || "数据"}</p>
      </Col>
      <Col style={{ background: "transparent" }} span={13}>
        <Slider
        style={{color:"white"}}
          min={0}
          max={2}
          step={0.1}
          onChange={onChange}
          value={inputValue}
          // trackStyle={{ background: 'white' }}
          handleStyle={{ 
            borderColor: 'white',
            color: 'white'
          }}
          railStyle={{ background: 'rgba(255,255,255,0.3)' }}
        />
      </Col>
      <Col span={3} style={{ background: "transparent",color: 'white'  }}>
      <ConfigProvider theme={theme}>
        <InputNumber
         suffix="W" 
          min={0}
          max={2}
          step={0.1}
          style={{
            // 外层容器样式
            color: "white",
        
            // display: 'flex',
            // alignItems: 'start', justifyContent: 'start', 
            width:90,
            height:30,
            background:"transparent",
            border: 'none',
            padding:"10 -10",
            position: 'relative',  // 为伪元素定位准备
            marginTop:0,
          }}
          value={inputValue}
          onChange={onChange}
          className={styles.customInput}
         
        />
        </ConfigProvider>
      </Col>
    </Row>
  );
};



export  function Node_Model_Rumor(props: { data:ModelControl_Rumor }) {

  const [inputValue, setInputValue] = useState(1);
// 若需要同步数值到父组件
  const handleValueChange = (newValue: number) => {
    setInputValue(newValue);
    // 这里可以更新 ModelControl_Rumor 的数据
    // props.data.value = newValue;
  };
  const onChange: InputNumberProps['onChange'] = (newValue) => {
    setInputValue(newValue as number);
  };

  return (





    <Layout style={{zIndex:99999, background: "transparent"}}>
    <Space style={{ width: '100%', background: "transparent" }} direction="vertical">
      <Step_Model label="关键字" value={inputValue}  onChange={handleValueChange} />
      <Step_Model label="权限词" value={inputValue}  onChange={handleValueChange} />

      <Step_Model label="数源类型" value={inputValue}  onChange={handleValueChange} />


      <Step_Model label="媒体类型" value={inputValue}  onChange={handleValueChange} />
      <Step_Model label="排除条件" value={inputValue}  onChange={handleValueChange} />

    </Space>
  </Layout>
    

  );

}



export  function Node_Preview_Rumor_1(props: { data:PreviewControl_Rumor }) {

  const [inputValue, setInputValue] = useState(1);
// 若需要同步数值到父组件
  const handleValueChange = (newValue: number) => {
    setInputValue(newValue);
    // 这里可以更新 ModelControl_Rumor 的数据
    // props.data.value = newValue;
  };
  const onChange: InputNumberProps['onChange'] = (newValue) => {
    setInputValue(newValue as number);
  };

  return (





    <Layout style={{zIndex:99999, background: "transparent"}}>
    <Space style={{ width: '100%', background: "transparent" }} direction="vertical">
      <Step_Model label="关键字" value={inputValue}  onChange={handleValueChange} />
      <Step_Model label="权限词" value={inputValue}  onChange={handleValueChange} />

      <Step_Model label="数源类型" value={inputValue}  onChange={handleValueChange} />


      <Step_Model label="媒体类型" value={inputValue}  onChange={handleValueChange} />
      <Step_Model label="排除条件" value={inputValue}  onChange={handleValueChange} />

    </Space>
  </Layout>
    

  );

}


export  function Node_System_Start(props: { data:ButtonControl_Start }) {
  

  const [isRunning, setIsRunning] = useState(false);
  
  const toggleSwitch = () => {

    setIsRunning(!isRunning)
    // <PauseOutlined />
  };
  // style={{background:"red",width:400,height:40}}
  return (

<Flex  justify={"center"} align={"center"} style={{width:"100%"}}>
    <Button
    onPointerDown={(e) => e.stopPropagation()}
    onDoubleClick={(e) => e.stopPropagation()}
    // onClick={props.data.onClick}
    onClick={() => props.data.onClick?.("","Start") }// 触发回调
    style={{
        position: 'absolute',
        left:"15%",
        top:"50%",
        backgroundColor: '#666', // 背景颜色
        color: '#fff', // 文字颜色
        border: 'none', // 去掉边框
        borderRadius: '4px', // 圆角
        padding: '8px 16px', // 内边距
        fontSize: '8px', // 字体大小
        fontWeight: 'bold', // 字体粗细
        cursor: 'pointer', // 鼠标指针样式
      }}
  >
    启动
  </Button>
  <Button
  onPointerDown={(e) => e.stopPropagation()}
  onDoubleClick={(e) => e.stopPropagation()}
  // onClick={props.data.onClick}
  onClick={() => props.data.onClick?.("","Stop") }// 触发回调
  style={{
          position: 'absolute',
      left:"55%",
      top:"50%",
      backgroundColor: '#666', // 背景颜色
      color: '#fff', // 文字颜色
      border: 'none', // 去掉边框
      borderRadius: '4px', // 圆角
      padding: '8px 16px', // 内边距
      fontSize: '8px', // 字体大小
      fontWeight: 'bold', // 字体粗细
      cursor: 'pointer', // 鼠标指针样式
    }}
>
  停止
</Button>
</Flex>

    // <Flex  justify={"center"} align={"center"} style={{width:"100%"}}>
    //          <Button  onClick={toggleSwitch } size="small"  color = 'volcano'  style={{ marginTop:3,width: 80  , color:"white", backgroundColor: '#666',borderColor: 'rgba(0,0,0,0.8)',}} >{isRunning? (<><PauseOutlined /> 暂停 </>): (<> <CaretRightOutlined /> 启动</>)}</Button>
    //          <Button  size="small"  color = 'volcano'  style={{ marginTop:3,width: 80  , color:"white", backgroundColor: '#666',borderColor: 'rgba(0,0,0,0.8)',}} ><StopOutlined />停止</Button>
       
    //             {/* <Radio.Group onChange={onChange} defaultValue="a" size={"small"}>
    //                   <Radio.
    //                   <Radio.Button  style={{background:"#3e6ae1",width:55,color:"white" }}  value="b">暂停</Radio.Button>
    //                   <Radio.Button  style={{background:"#3e6ae1",width:55,color:"white"}}  value="c">停止</Radio.Button>

    //                 </Radio.Group> */}


    // </Flex>
    );

};


export  function Node_System_Output(props: { data:SystemControl_Output }) {
  

  return (

      <Flex  justify={"center"} align={"center"} style={{width:"100%"}}>
          【略】
      </Flex>


    );

};





export  function Node_System_Status(props: { data:SystemControl_Status }) {

  const [inputValue, setInputValue] = useState(1);
// 若需要同步数值到父组件
  const handleValueChange = (newValue: number) => {
    setInputValue(newValue);
    // 这里可以更新 ModelControl_Rumor 的数据
    // props.data.value = newValue;
  };
  const onChange: InputNumberProps['onChange'] = (newValue) => {
    setInputValue(newValue as number);
  };

  return (





    <Layout style={{zIndex:99999, background: "transparent"}}>
    <Space style={{ width: '100%', background: "transparent" ,marginTop:-30}} direction="vertical">
            <BrownianWave 
            width={800}
            height={150}
            color="#68d391"
            speed={2}     // 降低速度
            waveHeight={90} // 较大波幅
            randomness={112} // 高随机性
            lineWidth={3}
          />

    </Space>
  </Layout>
    

  );

}
