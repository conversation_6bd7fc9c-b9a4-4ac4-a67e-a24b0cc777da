export const Style_Background = {
    borderRadius: 8,
    overflow: 'hidden',
    width: '100%',
    maxWidth: '100%',
    height: '80vh',
    maxHeight: '80vh',

  };


export const Style_Header: React.CSSProperties = {
    textAlign: 'center',
    color: '#fff',
    height: "5%",

    paddingInline: 48,
    lineHeight: "5%",
    // backgroundColor: '#4096ff',
    backgroundColor: 'white',
  };

export const Style_Content: React.CSSProperties = {
    textAlign: 'center',
    height: "50%",
    lineHeight: "50%",
    // minHeight: "50vh",
    // lineHeight: "50vh",
    color: '#fff',
    // backgroundColor: '#0958d9',
    backgroundColor: 'white',
  };
export const Style_Footer: React.CSSProperties = {
    textAlign: 'center',
    color: '#fff',
    backgroundColor: '#4096ff',
  };


export const Style_AccountTag: React.CSSProperties = {
    display: 'inline-block',
    height: '32px',
    padding: '0 12px',
    marginLeft: '5px',
    marginTop: '5px',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '32px',
    color: '#FFC107',
    cursor: 'pointer',
    backgroundColor: 'rgba(255, 255, 255, 0.14)',
    border: '1px solid rgba(255, 255, 255, 0.22)',
    borderRadius: '16px',
    transition: 'all 0.3s linear',
    boxShadow: 'none'
    // display: 'inline-block',
    // backgroundColor: '#1890ff',
    // color: '#fff',
    // padding: '2px 8px',
    // borderRadius: '4px',
    // marginRight: '5px',
};
  
