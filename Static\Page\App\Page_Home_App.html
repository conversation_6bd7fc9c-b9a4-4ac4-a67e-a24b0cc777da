﻿<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0">
	<title>哨兵导控</title>
	<!-- Google font file. If you want you can change. -->
	<!-- <link href="https://fonts.googleapis.com/css?family=Nunito:300,400,600,700,900" rel="stylesheet"> -->
	<link href="/static/CSS/App/font-googleeapis.css" rel="stylesheet">
	<!-- Fontawesome font file css -->
	<!-- <link rel="stylesheet" href="https://www.jq22.com/jquery/font-awesome.4.7.0.css"> -->
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/font-awesome.min.css">
	<!-- Animate css file for css3 animations. for more : https://daneden.github.io/animate.css -->
	<!-- Only use animate action. If you dont use animation, you don't have to add.-->
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/animate.css">
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/cryptocoins.css">
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/plugins/c3-chart/c3.css">
	<!-- Template global css file. Requared all pages -->
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/global.style.css">
	<!-- Swiper slider css file -->
	<link rel="stylesheet" href="/static/CSS/App/swiper.min.css">
	<!--turbo slider plugin css file -->
	<!-- <link rel="stylesheet" href="https://www.jq22.com/jquery/bootstrap-4.2.1.css"> -->
	<link rel="stylesheet" href="/static/CSS/App/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css" href="/static/CSS/App/plugins/turbo-slider/turbo.css">
</head>

<body>
	<div class="wrapper ">
		<div class="nav-menu">
			<nav class="menu">
				<!-- Menu navigation start -->
				<div class="nav-container">
					<ul class="main-menu">
					</ul>
				</div>
				<!-- Menu navigation end -->
			</nav>
		</div>
		<div class="wrapper-inline">
			<main class="margin mt-0">
				<div class="dash-balance">
					<div class="dash-content relative">
						<h3 class="w-text">数据分析</h3>
						<div class="notification">
							<a href="javascript:;" onclick="Refresh_Chart_Info()"><i class="fa fa-refresh"></i></a>
						</div>
					</div>
				</div>

				<div class="bal-section container">
					<div class="content"> 
						<div class="c-panel pl-0">
							<div class="row align-items-center">
								<div class="col-12">
									<div class="advertising-wrapper">
										<div id="TodaySentimentTimeCrawl"></div>
									</div>
								</div>
							</div>     
						</div>
					</div>
				</div>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">媒体类型分布</h4>
						<div class="c-panel">
								<div class="row align-items-center">
									<div class="col-12 col-sm-6">
										<div class="advertising-wrapper">
											<div id="TodaySentimentPlatformType"></div>
										</div>
									</div>
									
								</div>     
						</div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">发布平台</h4>
						<div class="c-panel">
								<div class="row align-items-center">
									<div class="col-12 col-sm-6">
										<div class="advertising-wrapper">
											<div id="TodaySentimentPlatform"></div>
										</div>
									</div>
									
								</div>     
						</div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">IP属地</h4>
						<div class="c-panel">
								<div class="row align-items-center">
									<div class="col-12 col-sm-6">
										<div class="advertising-wrapper">
											<div id="TodaySentimentRegionIPInfo"></div>
										</div>
									</div>
									
								</div>     
						</div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">中标词分析</h4>
						<div class="c-panel">
								<div class="row align-items-center">
									<div class="col-12 col-sm-6">
										<div class="advertising-wrapper">
											<canvas id="TodaySentimentKey"></canvas>
											<!-- <div id="TodaySentimentKey"></div> -->
										</div>
									</div>
									
								</div>
						</div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">最热文章</h4>
						<div class="search-result-container" id="TodaySentimentHotArticle"></div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">情感属性</h4>
						<div class="c-panel">
							<div class="row align-items-center">
								<div class="col-12">
									<div class="advertising-wrapper">
										<div id="TodaySentimentEmotionInfo" ></div>
									</div>
								</div>
							</div>     
						</div>
					</div>
				</section>
				<section class="container">
					<div class="content"> 
						<h4 class="title-main">发布地区</h4>
						<div class="c-panel">
								<div class="row align-items-center">
									<div class="col-12 col-sm-6">
										<div class="advertising-wrapper">
											<div id="TodaySentimentRegionArea"></div>
										</div>
									</div>
									
								</div>     
						</div>
					</div>
				</section>
				<footer>
					<div class="container">
					</div>
				</footer>
			</main>
		</div>
	</div>

	<!--Page loader DOM Elements. Requared all pages-->
	<div class="sweet-loader">
		<div class="box">
		  	<div class="circle1"></div>
		  	<div class="circle2"></div>
		  	<div class="circle3"></div>
		</div>
	</div>
	<!-- JQuery library file. requared all pages -->
	<!-- <script src="https://www.jq22.com/jquery/jquery-1.10.2.js"></script> -->
	<script src="/static/JavaScript/jquery.min.js"></script>
	<!-- Swiper JS -->
  	<script src="/static/JavaScript/App/swiper.min.js"></script>
  	<!-- Flot Charts -->
	<script src="/static/CSS/App/plugins/c3-chart/c3.min.js"></script>
	<script src="/static/CSS/App/plugins/c3-chart/d3.min.js"></script>
	<script src="/static/CSS/App/plugins/c3-chart/c3.custom.js"></script>
	<!-- Flot Charts -->
	<script src="/static/CSS/App/plugins/flot/jquery.flot.min.js"></script>
	<script src="/static/CSS/App/plugins/flot/jquery.flot.time.min.js"></script>
	<script src="/static/CSS/App/plugins/flot/jquery.flot.pie.min.js"></script>
	<script src="/static/CSS/App/plugins/flot/jquery.flot.tooltip.min.js"></script>
	<script src="/static/CSS/App/plugins/flot/jquery.flot.resize.min.js"></script>
	<!-- word cloud -->
	<script type="text/javascript" src="/static/JavaScript/App/wordcloud2.js"></script>
	<!-- Sparkline-->
	<script src="/static/CSS/App/plugins/jquery-sparkline/jquery.sparkline.js"></script>
	<!-- Template global script file. requared all pages -->
	<script src="/static/JavaScript/App/app-charts.js"></script>
	<script src="/static/JavaScript/App/global.script.js"></script>

	<script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/App/Page_Home_App.js"></script>

	<!-- ------------------------------------------------------------------------全局变量初始化 -->
	<script>
		function Page_Init(){
            $('.sweet-loader').show().addClass('show');
            Requests_Sentiment_Info();
        };

		window.onload = function(){
			Page_Init();
		};
	</script>
</body>

</html>
