# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
# from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.Utils.UtilsCenter import *


Page_Info={
    "Title":"哨兵核心服务配置",
    "Param": {},

}
PP(Page_Info)
# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_SentinelServer(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass
        self.initUI()

    def initUI(self):
        self.Set_Title()
        self.Set_Content()




    def Set_Title(self):
        Icon = qta.icon('ph.caret-left-light', scale_factor=1, color=('black'), color_active='black')
        Button_Back = QtWidgets.QPushButton(self)
        Button_Back.setIconSize(QtCore.QSize(25, 25))
        Button_Back.setStyleSheet("background:transparent")
        Button_Back.setIcon(Icon)
        Button_Back.setGeometry(QtCore.QRect(16, 38, 30, 30))
        Button_Back.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE({"Command": "Page_Change", "Page_Name": "Home"}))

        Label_Title = QtWidgets.QLabel(self)
        Label_Title.setStyleSheet("background:transparent")
        Label_Title.setFont(QtGui.QFont("Microsoft YaHei", 13))
        Label_Title.setGeometry(QtCore.QRect(36, 12, 280, 80))
        Label_Title.setText(Page_Info['Title'])


    def Set_Content(self):
        self.Set_Mission_Content()

    def Set_Mission_Content(self):
        QLabel_Mission_List = QtWidgets.QLabel(self)
        QLabel_Mission_List.setStyleSheet("background:rgba(255,255,255,0);border-radius:6px;")
        QLabel_Mission_List.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Mission_List.setGeometry(QtCore.QRect(2, 88, 378, 398))

        QVBoxLayout_Mission_List = QtWidgets.QVBoxLayout()
        QVBoxLayout_Mission_List.setSpacing(0)  # 内边界
        QVBoxLayout_Mission_List.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Mission_List.setLayout(QVBoxLayout_Mission_List)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setMinimumSize(390, 2000)  #######设置滚动条的尺寸

        self.Line_Height = 40
        self.Line_Margin = 5

        Chrome_Config = [
            # {'Mission_ID': '1'},
            {'Mission_ID': '2'},
            {'Mission_ID': '3'},
            {'Mission_ID': '4'},
            {'Mission_ID': '5'},
            {'Mission_ID': '6'},
            {'Mission_ID': '7'},
            {'Mission_ID': '8'},
            {'Mission_ID': '9'},
            {'Mission_ID': '10'},
            {'Mission_ID': '11'},
            {'Mission_ID': '12'},
            {'Mission_ID': '13'},
        ]
        # self.Mission_Machine_ID(Chrome_Config[0])
        # self.Mission_Platform_Type(Chrome_Config[1])
        # self.Mission_Device_List(Chrome_Config[2])
        # self.Mission_Count_Info(Chrome_Config[3])
        # self.Mission_Control_Status(Chrome_Config[4])
        # self.Mission_Delect_Status(Chrome_Config[5])
        # self.Mission_Screenshot_Status(Chrome_Config[6])

        self.Mission_Sentinel_UUID({"Line_ID":1})
        self.Mission_Deployment_Method({"Line_ID":2})
        self.Mission_Server_IP({"Line_ID":3})
        self.Mission_Server_Port({"Line_ID":4})
        # self.Mission_Worker_Method({"Line_ID":2})
        ##创建一个滚动条
        self.QScrollArea_Mission_List = QtWidgets.QScrollArea()
        self.QScrollArea_Mission_List.setStyleSheet("QScrollBar {height:0px;}")
        self.QScrollArea_Mission_List.setWidget(self.QWidget_TopFiller)
        QVBoxLayout_Mission_List.addWidget(self.QScrollArea_Mission_List, 1)

    def Mission_Sentinel_UUID(self,Line_Info):
        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)
        QLabel_Line.setStyleSheet("""QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;color:black;border-style:solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        QLabel_Line.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))

        QHBoxLayout_Line = QtWidgets.QHBoxLayout()
        QHBoxLayout_Line.setSpacing(0)  # 内边界
        QHBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line.setLayout(QHBoxLayout_Line)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setAlignment(QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter )
        # QLabel_Title.setStyleSheet("background:transparent;color:blue")
        # QLabel_Title.setStyleSheet("QLabel{background:rgba(0,0,0,0.2);color:blue;border-radius:6px 0px 0px 6px;padding:0px;}")
        QLabel_Title.setFont(QtGui.QFont("Microsoft YaHei", 10))
        QLabel_Title.setText('UUID')


        QLabel_Name = QtWidgets.QLabel()
        QLabel_Name.setAlignment(QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter )
        QLabel_Name.setStyleSheet("background:transparent;color:blue")
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        QLabel_Name.setText('UU-764646-575675-7ID')
        QHBoxLayout_Line.addWidget(QLabel_Title,1)
        QHBoxLayout_Line.addWidget(QLabel_Name,2)
        # QLabel_Line.move(10,  (int(Line_Info['Line_ID']) - 1) * self.Line_Height)

        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1)  *(self.Line_Height + self.Line_Margin))



    def Mission_Deployment_Method(self, Line_Info):
        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)
        QLabel_Line.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;color:black;border-style:solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        QLabel_Line.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))

        QHBoxLayout_Line = QtWidgets.QHBoxLayout()
        QHBoxLayout_Line.setSpacing(0)  # 内边界
        QHBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line.setLayout(QHBoxLayout_Line)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        # QLabel_Title.setStyleSheet("background:transparent;color:blue")
        # QLabel_Title.setStyleSheet("QLabel{background:rgba(0,0,0,0.2);color:blue;border-radius:6px 0px 0px 6px;padding:0px;}")
        QLabel_Title.setFont(QtGui.QFont("Microsoft YaHei", 10))
        QLabel_Title.setText('部署方式')

        QComboBox_Control = QtWidgets.QComboBox()
        QComboBox_Control.setStyleSheet("QComboBox{border:0px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        QComboBox_Control.addItems(['联网', '本地'])

        QHBoxLayout_Line.addWidget(QLabel_Title, 1)
        QHBoxLayout_Line.addWidget(QComboBox_Control, 2)

        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1) *(self.Line_Height + self.Line_Margin))

        # QLabel_Mission.move(10,  (5+(int(Line_Info['Line_ID']) - 1) * self.Line_Height))



    def Mission_Server_IP(self, Line_Info):
        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)
        QLabel_Line.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;color:black;border-style:solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        QLabel_Line.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))

        QHBoxLayout_Line = QtWidgets.QHBoxLayout()
        QHBoxLayout_Line.setSpacing(0)  # 内边界
        QHBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line.setLayout(QHBoxLayout_Line)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        # QLabel_Title.setStyleSheet("background:transparent;color:blue")
        # QLabel_Title.setStyleSheet("QLabel{background:rgba(0,0,0,0.2);color:blue;border-radius:6px 0px 0px 6px;padding:0px;}")
        QLabel_Title.setFont(QtGui.QFont("Microsoft YaHei", 10))
        QLabel_Title.setText('服务器地址')



        QLineEdit_Line = QtWidgets.QLineEdit()
        QLineEdit_Line.setPlaceholderText("默认")

        QHBoxLayout_Line.addWidget(QLabel_Title, 1)
        QHBoxLayout_Line.addWidget(QLineEdit_Line, 2)
        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1) *(self.Line_Height + self.Line_Margin))

    def Mission_Server_Port(self, Line_Info):
        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)
        QLabel_Line.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;color:black;border-style:solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        QLabel_Line.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))

        QHBoxLayout_Line = QtWidgets.QHBoxLayout()
        QHBoxLayout_Line.setSpacing(0)  # 内边界
        QHBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line.setLayout(QHBoxLayout_Line)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        # QLabel_Title.setStyleSheet("background:transparent;color:blue")
        # QLabel_Title.setStyleSheet("QLabel{background:rgba(0,0,0,0.2);color:blue;border-radius:6px 0px 0px 6px;padding:0px;}")
        QLabel_Title.setFont(QtGui.QFont("Microsoft YaHei", 10))
        QLabel_Title.setText('服务端口')
        QLineEdit_Line = QtWidgets.QLineEdit()
        QLineEdit_Line.setPlaceholderText("默认")


        QHBoxLayout_Line.addWidget(QLabel_Title, 1)
        QHBoxLayout_Line.addWidget(QLineEdit_Line, 2)
        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1) * (self.Line_Height + self.Line_Margin))












    def Mission_Chrome_UUID(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet("""QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) )

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('UUID')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.QLineEdit_Chrome_UUID = QtWidgets.QLineEdit(Label_Mission)
        self.QLineEdit_Chrome_UUID.setStyleSheet("background:white;color:blue")
        self.QLineEdit_Chrome_UUID.setGeometry(QtCore.QRect(258, 5, 88, 18))
        self.QLineEdit_Chrome_UUID.setText('未分配')



    def Mission_Machine_ID(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348,self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('本机Machine_ID')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.QLineEdit_Machine_ID = QtWidgets.QLineEdit(Label_Mission)
        self.QLineEdit_Machine_ID.setStyleSheet("background:white;color:blue")
        self.QLineEdit_Machine_ID.setGeometry(QtCore.QRect(258, 5, 88, 18))
        # self.QLineEdit_Machine_ID.setGeometry(QtCore.QRect(58, 5, 288, 18))
        self.QLineEdit_Machine_ID.setText('f87191e9-a181-7191e9a1814f4e-9e67-b7824468ad9b')
        # self.QLineEdit_Machine_ID.setText('未分配')

        # use

    def Mission_Platform_Type(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('平台类型')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.ComboBox_Platform_Type = QtWidgets.QComboBox(Label_Mission)
        self.ComboBox_Platform_Type.setStyleSheet(
            "QComboBox{border:0px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        self.ComboBox_Platform_Type.setGeometry(QtCore.QRect(258, 5, 88, 18))
        self.ComboBox_Platform_Type.addItems(['Youtube', 'Facebook', 'Twitter', 'Niche_Website'])
        # ComboBox_Data_Type.currentIndexChanged[str].connect(self.Chaneg_Data_Type)

        # use

    def Mission_Device_List(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, 28+self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('执行镜像')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.QLineEdit_Device_List = QtWidgets.QLineEdit(Label_Mission)
        self.QLineEdit_Device_List.setPlaceholderText("1/1,2/1-10")
        self.QLineEdit_Device_List.setStyleSheet("background:white;color:blue")
        self.QLineEdit_Device_List.setGeometry(QtCore.QRect(258, 5, 88, 18))

        # use

    def Mission_Count_Info(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, 28+self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('每个镜像执行次数')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.QLineEdit_Mission_Count = QtWidgets.QLineEdit(Label_Mission)
        self.QLineEdit_Mission_Count.setStyleSheet("background:white;color:blue")
        self.QLineEdit_Mission_Count.setGeometry(QtCore.QRect(258, 5, 88, 18))

        # use

    def Mission_Control_Status(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, 28+self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('任务是否重复可执行')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.ComboBox_Control_Info = QtWidgets.QComboBox(Label_Mission)
        self.ComboBox_Control_Info.setStyleSheet(
            "QComboBox{border:0px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        self.ComboBox_Control_Info.setGeometry(QtCore.QRect(258, 5, 88, 18))
        self.ComboBox_Control_Info.addItems(['是', '否'])

        # use

    def Mission_Delect_Status(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, 28+self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('任务完成是否执行删除')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.ComboBox_Delect_Status_Info = QtWidgets.QComboBox(Label_Mission)
        self.ComboBox_Delect_Status_Info.setStyleSheet(
            "QComboBox{border:0px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        self.ComboBox_Delect_Status_Info.setGeometry(QtCore.QRect(258, 5, 88, 18))
        self.ComboBox_Delect_Status_Info.addItems(['是', '否'])

        # use

    def Mission_Screenshot_Status(self, Mission_Info):
        Label_Mission = QtWidgets.QLabel(self.QWidget_TopFiller)
        Label_Mission.setStyleSheet(
            """QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        # "background:rgba(255,255,255,0.3);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        Label_Mission.setFont(QtGui.QFont("Microsoft YaHei", 20))
        Label_Mission.setGeometry(QtCore.QRect(0, 208, 348, 28+self.Line_Height))
        Label_Mission.move(10, (int(Mission_Info['Mission_ID']) - 1) * self.Line_Height)

        Label_Fields_Name = QtWidgets.QLabel(Label_Mission)
        Label_Fields_Name.setAlignment(QtCore.Qt.AlignLeft)
        Label_Fields_Name.setStyleSheet("background:transparent;color:blue")
        Label_Fields_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
        Label_Fields_Name.setText('是否截图')
        Label_Fields_Name.setGeometry(QtCore.QRect(8, 3, 350, 50))

        self.ComboBox_Screenshot_Status_Info = QtWidgets.QComboBox(Label_Mission)
        self.ComboBox_Screenshot_Status_Info.setStyleSheet(
            "QComboBox{border:0px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        self.ComboBox_Screenshot_Status_Info.setGeometry(QtCore.QRect(258, 5, 88, 18))
        self.ComboBox_Screenshot_Status_Info.addItems(['是', '否'])

    def Set_Bottom(self):
        pass

    def PAGE_HANDLER_EXECUTE(self, *args):
        try:
            Command = args[0]
            Command["Page_Param"] = Page_Info["Param"]
        except:pass
        self.Signal_Result.emit(args[0])  # 发送信号并传递数据



class Page_Mission_Execute(QtCore.QThread):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.CONFIG()
        self.Type = args[0]

    def run(self):
        # PP("Page_Update_Status",9)
        try:
            Method = getattr(self, f'{self.Type}', None)
            if Method and callable(Method):
                return Method()
            else:
                return f"{self.__class__.__name__} No Such Method: {self.Type}"
        except Exception as e:
            self.Signal_Result.emit({"Error": e})

    def Worker_Info(self):
        try:
            self.CacheStack_Data = {}
            self.CacheStack_Data['Method'] = "Key_Get"
            self.CacheStack_Data['Data'] = ["Worker_Info"]
            Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
            Worker_Info = json.loads(Response.text)
            # PJ(Worker_Info)
            self.Signal_Result.emit({"Worker_Info": Worker_Info})
        except:
            PT()
            self.Signal_Result.emit({"Worker_Info": "Failed"})




        pass

    def Mission_Update_Status(self):
        PP("Mission_Update_Status", 3)
        # PJ(CServer())

        # Server_Status_Info  =
        self.Result["Server_Status_Info"]    = CServer()
        self.Result["Status"]                = "Success"
        self.Result["Command"]               = "Page_Update"
        self.Signal_Result.emit(self.Result)
        # return self.Result




    def CONFIG(self):
        self.Result = {'Status': 'Failed'}








            # Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            # # PI(eval(Response.text))
            # Sentinel_Service = eval(Response.text)
            # if Sentinel_Service["Status"] == "Active":
            #     Page_Info["Sentinel_Service"] = "Success"

        #
        # try:
        #     self.CacheStack_Data = {}
        #     self.CacheStack_Data['Method'] = "Key_Get"
        #     self.CacheStack_Data['Data'] = ["System_Status"]
        #     Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
        #     System_Status = json.loads(Response.text)
        #     PJ(System_Status)
        #     self.Signal_Result.emit({"System_Status": System_Status})
        # except:
        #     PT()
        #     self.Signal_Result.emit({"System_Status": "Failed"})



            # Page_Info["Sentinel_Service"] = "Failed"






        # global Page_Info
        # while True:
        #     time.sleep(10)
        #     PP("Core Update")

            # self.Proxies =
            # Response
            # try:
            #     Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            #     # PI(eval(Response.text))
            #     Sentinel_Service = eval(Response.text)
            #     if Sentinel_Service["Status"] == "Active":
            #         Page_Info["Sentinel_Service"] = "Success"
            # except:
            #     Page_Info["Sentinel_Service"] = "Failed"

            # System_Status =  SCKeYGet({"Key":"System_Status"})
            # PJ(System_Status)
            # try:
            #     pass
            #     # Page_Info["System_Status"] = SCKey_Get("System_Status")
            # except:
            #     PT()

            # System_Status









if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Home()
    Page_Widget.show()
    sys.exit(app.exec())
