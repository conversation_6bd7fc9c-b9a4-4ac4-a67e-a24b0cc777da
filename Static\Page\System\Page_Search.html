<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
	<style>
		.notice {
			width:100%;
			white-space:nowrap;
			overflow:hidden;
			text-overflow:ellipsis;
			box-sizing:border-box;
			padding-right:10px;
		};
	</style>
    <style>
        .collapse-content {
          max-height: 9rem; /* 3行的高度，根据字体大小调整 */
          overflow: hidden;
          transition: max-height 0.3s ease;
        }
      
        .collapse-content.expanded {
          max-height: 70rem; /* 4行以上内容时的最大高度，根据实际内容调整 PC12就够 App*4 */
        }

        /* 屏蔽原来的focus样式 */
        .btn-no-focus:focus {
            outline: none; /* 移除轮廓 */
            box-shadow: none; /* 移除阴影 */
            color: #fff; /* 文字颜色 */
            background-color: #0075ff; /* 背景颜色 */
            border-color: rgb(255 255 255 / 3%); /* 边框颜色 */
        }

        /* 定义按钮选中时的样式 */
        .btn-active {
            background-color: #0075ff;
        }
        
        .select2-container--bootstrap4 .select2-selection--single {
            font-size: 0.875rem;
        }

        .pagination li.disabled a {
            pointer-events: none;
            cursor: default;
        }

        .extra {
            position: absolute;
            top: -1rem;
            right: 15px;
            font-weight: 400;
            color: #0960bd;
            cursor: pointer;
        }

        .details_info {
            padding-left: 10px;
            padding-right: 10px; 
            max-width:15rem;
            white-space:nowrap;
			overflow:hidden;
			text-overflow:ellipsis;
			box-sizing:border-box;
            display: flex;
            align-items: center; /* 垂直居中 */
        }

        .details_info .span_title_info {
            font-size: 14px;
            font-family: arial, sans-serif;
        }

        .details_info .span_content_info {
            font-size: 14px;
            font-family: arial, sans-serif;
            overflow: hidden; /* 隐藏超出部分 */
            text-overflow: ellipsis; /* 显示省略号 */
            margin-right: 10px; /* 可以根据需要调整间距 */
        }
        
        

    </style>
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-12 col-lg-10">
                            <div class="card">
								<div class="card-body">
                                    <h1 class="display-5 mt-8 text-center">元引擎搜索</h1>
                                    <div class="input-group input-group-lg" style="margin-top: 3rem !important;">
										<input type="text" class="form-control" placeholder="请输入搜索关键词,多个关键词之间空格隔开(如:杭州 2022 亚运会)" aria-label="" aria-describedby="" id="Search_Sentiment_Key_Element">
										<div class="input-group-append">
											<button class="btn btn-info " type="button" id="Search_Sentiment_Element">搜索一下</button>
										</div>
                                    </div>
                                    <div id="Search_Show_Body_Element" style="display: none;" >
                                        <div class="row mt-1">
                                            <div class="col-sm-8">
                                                <label class="col-form-label" style="float: left;font-size: 1rem;">情感属性:</label>
                                                <div class="col-sm-10" id="Sentiment_Emotion_Type_Element" style="float: left;">
                                                    <div class="form-check form-check-inline col-form-label">
                                                        <input class="form-check-input" type="checkbox" id="Emotion_Type_1" value="All" checked="">
                                                        <label class="form-check-label" for="Emotion_Type_1">全部(<span id="Label_Emotion_Type_1">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Emotion_Type_2" value="正面">
                                                        <label class="form-check-label" for="Emotion_Type_2">正面(<span id="Label_Emotion_Type_2">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Emotion_Type_3" value="中性">
                                                        <label class="form-check-label" for="Emotion_Type_3">中性(<span id="Label_Emotion_Type_3">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Emotion_Type_4" value="负面">
                                                        <label class="form-check-label" for="Emotion_Type_4">负面(<span id="Label_Emotion_Type_4">0</span>)</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-4">
                                                <button type="button" class="btn btn-light btn-sm m-1 px-2 ml-2" style="float: right;"> <i class="fadeIn animated bx bx-download mr-1" style="font-size:0.875rem;"></i>全部下载</button>
                                                <div class="form-check form-check-inline col-form-label" style="float: right;">
                                                    <label class="form-check-label" id="Article_Loading_Count">共0条消息</label>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div id="Article_Body_Element">
                                        </div>
                                        <nav aria-label="Page navigation example">
                                            <ul class="pagination justify-content-end">
                                            </ul>
                                        </nav>
                                    </div>
                                    <div class="d-flex align-items-center mt-6" id="Hot_Show_Body_Element">
										<div>
											<h5 class="mb-0">热点动态</h5>
										</div>
										<div class="ml-auto"><a href="javascript:;" class="btn btn-sm btn-light" id="Reload_Hot_Botice_Element"><i class="fadeIn animated bx bx-refresh"></i>换一换</a>
										</div>
									</div>
									<div class="mt-3" style="min-height: 417px;" id="Hot_Notice_Element">
									</div>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
			</div>
		</div>
		<div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
	</div>
    <div class="modal fade" id="loadingModal" backdrop="static" keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div style="width: 200px;height:20px; z-index: 20000; position: absolute; text-align: center; left: 50%; top: 50%;margin-left:-100px;margin-top:-10px">
                <div class="text-center">
                    <div class="spinner-border text-info spinner-border-sm" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                    <strong style="color:#198fed">Loading...</strong>
                </div>
            </div>   
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>

	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Search.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        // var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
			Requests_Hot_Notice_Info();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>