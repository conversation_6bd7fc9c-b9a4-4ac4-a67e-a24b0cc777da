// src/Components/ErrorBoundary.tsx
import React, { Component } from 'react';
import { Navigate } from 'react-router-dom';

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React 组件错误:', error, errorInfo);
  }

  render() {
    if ((this.state as ErrorBoundaryState).hasError) {
      return <Navigate to="*" replace />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;