/* 预警中心页面样式 */
.alarm-center-container {
  padding: 1rem;
}

/* 卡片样式 */
.card {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.card-body {
  padding: 1.25rem;
}

/* 筛选区域样式 */
.row {
  margin-left: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
}

.col-form-label {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  font-weight: 500;
  color: #fff;
  white-space: nowrap;
  flex-shrink: 0;
}

.col-sm-2 {
  flex: 0 0 auto;
  width: 16.666667%;
  margin-right: 1rem;
}

.form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid #ced4da;
  color: #fff;
  font-size: 0.875rem;
}

.form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  color: #fff;
}

.form-control::placeholder {
  color: #adb5bd;
}

.form-control option {
  background-color: #343a40;
  color: #fff;
}

/* 按钮样式 */
.btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  margin: 0.125rem;
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-outline-success {
  color: #28a745;
  border-color: #28a745;
  background-color: transparent;
}

.btn-outline-success:hover {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
  background-color: transparent;
}

.btn-outline-info:hover {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

/* 表格样式 */
.table-responsive {
  border-radius: 0.375rem;
  overflow: hidden;
}

.alarm-table {
  table-layout: fixed;
  width: 100%;
  margin-bottom: 0;
  background-color: transparent;
}

.alarm-table th,
.alarm-table td {
  border: 1px solid #dee2e6;
  padding: 0.75rem;
  vertical-align: middle;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.alarm-table thead th {
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #dee2e6;
}

.alarm-table tbody tr {
  background-color: rgba(255, 255, 255, 0.05);
}

.alarm-table tbody tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.1);
}

.alarm-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.alarm-table tbody td {
  color: #fff;
  border-color: rgba(255, 255, 255, 0.2);
}

/* 序号、时间、预警方式、处置状态、操作列居中 */
.alarm-table th:nth-child(1),
.alarm-table td:nth-child(1),
.alarm-table th:nth-child(2),
.alarm-table td:nth-child(2),
.alarm-table th:nth-child(3),
.alarm-table td:nth-child(3),
.alarm-table th:nth-child(4),
.alarm-table td:nth-child(4),
.alarm-table th:nth-child(7),
.alarm-table td:nth-child(7) {
  text-align: center;
}

/* 预警标题和内容列左对齐 */
.alarm-table th:nth-child(5),
.alarm-table td:nth-child(5),
.alarm-table th:nth-child(6),
.alarm-table td:nth-child(6) {
  text-align: left;
}

/* 加载动画样式 */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-center {
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .alarm-center-container {
    padding: 0.5rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .col-form-label {
    font-size: 0.875rem;
  }
  
  .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .alarm-table th,
  .alarm-table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

/* 筛选行间距 */
.mt-4 {
  margin-top: 1.5rem !important;
}

/* 按钮右对齐 */
.btn[style*="float: right"] {
  margin-left: 0.5rem;
}

/* 表格固定高度 */
.table-responsive {
  max-height: calc(100vh - 320px);
  overflow-y: auto;
}

/* 滚动条样式 */
.table-responsive::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
