import React, { useState } from 'react';
import styles from '../Styles/Page_ReteTest.module.css';


interface DrawerProps {
    isOpen_Left: boolean; // 接收 isOpen 状态
    toggleDrawer_Left: () => void; // 接收 toggleDrawer 函数
  }



const Drawer_Left: React.FC<DrawerProps> = ({ isOpen_Left, toggleDrawer_Left }) => {





   


  return (
    <div className={`${styles.drawer_left} ${isOpen_Left ? styles.open : ''}`}>
          <div className={styles.drawer_left_content}>
            这是抽屉内容


                    

          </div>
      </div>
   
  );
};

export default Drawer_Left;