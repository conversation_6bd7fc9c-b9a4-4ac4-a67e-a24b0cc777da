.background {
  background-color: rgba(0,0,0,1);
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

.menu_title {
  height: 40px;
  width: 100vw;
  display: flex;
  padding: '0 5px';
  background-color: rgba(255,255,255,0.3);
 

}




.container {
  overflow: hidden;
  position: relative; /* 确保子元素可以相对于它定位 */
  min-height: 100vh;
  background-color: #262626;
  opacity: 1;
  background-image: linear-gradient(#0f0f0f 1.5px,transparent 0),linear-gradient(90deg,#0f0f0f 1.5px,transparent 0),linear-gradient(#333 1.5px,transparent 0),linear-gradient(90deg,#333 1.5px,#262626 0);
  background-size: 200px 200px,200px 200px,25px 25px,25px 25px;
  background-position: -1.5px -1.5px,-1.5px -1.5px,-1.5px -1.5px,-1.5px -1.5px;
}