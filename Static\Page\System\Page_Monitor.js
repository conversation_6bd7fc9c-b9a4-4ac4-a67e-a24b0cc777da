﻿/****************************************初始化插件***************************************************/ 
// $('.single-select').select2({
//     theme: 'bootstrap4',
//     width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
//     placeholder: $(this).data('placeholder'),
//     allowClear: Boolean($(this).data('allow-clear')),
// });

// var Choice_Date_Content = 'HalfYear'
var Choice_Date_List = [];
var Report_Info_List = [];
var QQ_Info_List = [];
var Monitor_Type = 'Wechat';  // Wechat QQ
// $('#Sentiment_Alarm_Time_Element').val(Choice_Date_Content).trigger('change');
/**********************************初始化Wechattable***************************************************/
var $table = $('#Reports_table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
    ],
    columns: [
      { title: '序号', data: 'ID', width: '93px' },
      { title: '创建时间', data: 'SOURCE_WEB_DATE', width: '193px'  },
      { title: '消息作者', data: 'SOURCE_AUTHOR'},
      { title: '消息内容', data: 'SOURCE_TITLE'},
    ],
    scrollY: 'calc(100vh - 420px)', // 设置表格固定高度
});
/**********************************初始化QQtable***************************************************/
// var $QQ_table = $('#QQ_table').DataTable({
//     language: {
//       url: '/static/JavaScript/datatable/js/zh-CN.json',
//     },
//     columnDefs: [
//       { className: 'text-center', targets: '_all' },
//     ],
//     columns: [
//       { title: '序号', data: 'ID', width: '92px' },
//       { title: '创建时间', data: 'SOURCE_WEB_DATE', width: '192px'},
//       { title: '消息作者', data: 'SOURCE_AUTHOR', width: '192px'},
//       { title: '消息内容', data: 'SOURCE_TITLE'},
//     ],
// });

function Table_Click_Event(Type) {
    Monitor_Type = Type;
    if (Type === 'Wechat') {
        Requests_Sentiment_Wchat_Group();
    } else {
        Requests_Sentiment_QQ_Group();
    };
};

function Requests_Sentiment_Message(Index,Group_ID) {
    if (Monitor_Type === 'Wechat') {
        Requests_Sentiment_Wchat_Message(Index,Group_ID);
    } else {
        Requests_Sentiment_QQ_Message(Index,Group_ID);
    };
};

function Chioce_Group_Totle_Event() {
    if (Monitor_Type === 'Wechat') {
        Chioce_Group_Event();
    } else {
        Chioce_QQ_Group_Event();
    };
}

/****************************************初始化请求数据***************************************************/ 
function Requests_Sentiment_Wchat_Group() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_wechat_info',
        "data_argument": `{}`,
        "data_kwargs": {
            // 'Choice_Date_Content':Choice_Date_Content,
            // 'Choice_Date_List':Choice_Date_List,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                var mission_active_totle =''
                for (let i = 0; i < Result.Wechat_Group_List.length; i++) {
                    var mission_info =  Result.Wechat_Group_List[i]
                    var mission_info_one = `
                       <a href="javascript:;" class="list-group-item" onclick="Requests_Sentiment_Message('${i}','${mission_info['GROUP_NUMBER']}')">
                            <div class="media">
                                <div class="chat-user-online">
                                    <img src="${mission_info['GROUP_IMAGE']}" width="42" height="42" class="rounded-circle" alt="" />
                                </div>
                                <div class="media-body ml-2">
                                    <h6 class="mb-0 chat-title">${mission_info['GROUP_NAME']}</h6>
                                    <p class="mb-0 chat-msg"><i class='bx bx-user mr-1'></i>${mission_info['GROUP_MEMBER']}</p>
                                </div>
                                <div class="chat-time">${mission_info['GROUP_SHOW_LASTTIME']}</div>
                            </div>
                        </a>`
                    mission_active_totle += mission_info_one
                }
                document.getElementById("Wechat_Group_Element").innerHTML = mission_active_totle;
                Report_Info_List = Result.Wechat_Group_List;
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

function Requests_Sentiment_Wchat_Message(Index,GROUP_NUMBER) {
    // console.log('Requests_Sentiment_Wchat_Message:',Index,GROUP_NUMBER)
    Loading_Show();

    // ----------- 设置a标签高亮
    var element_Info = document.getElementById('Wechat_Group_Element');
    var links = element_Info.getElementsByTagName('a');
    for (var i = 0; i < links.length; i++) {
        if (i.toString() === Index.toString()) {
            links[i].classList.add('active');
        } else {
            links[i].classList.remove('active');
        };
    };
    // -------设置聊天内容抬头
    for (var i = 0; i < Report_Info_List.length; i++) { 
        if (Report_Info_List[i]['GROUP_NUMBER'] === GROUP_NUMBER) {
            document.getElementById('Wechat_Group_Name_Element').innerHTML = Report_Info_List[i]['GROUP_NAME'];
        };
    };

    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_wechat_message',
        "data_argument": `{}`,
        "data_kwargs": {
            'GROUP_NUMBER': GROUP_NUMBER
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                $table.clear();
                $table.rows.add(Result.Group_Content_List);
                $table.draw();
                // Report_Info_List = Result.Group_Content_List;
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

/****************************************筛选时间变化***************************************************/ 
// $('#Sentiment_Alarm_Time_Element').on('change',function() {
//     Choice_Date_Content = this.value;
//     console.log('当前筛选的报警请求时间:', Choice_Date_Content);
//     if (this.value === 'Other') {
//         console.log('打开日期选择框')
//     } else {
//         console.log('隐藏日期选择框')
//     };
// });

/****************************************筛选***************************************************/ 
function Chioce_Group_Event() {
    let Chioce_Input_Info = document.getElementById('Chioce_Group_Element').value;
    console.log('群组名称:',Chioce_Input_Info)
    var Chioce_Group_List = []
    if (Chioce_Input_Info.length === 0){
        Chioce_Group_List = Report_Info_List
    } else {
        for (var i = 0; i < Report_Info_List.length; i++) { 
            if (Report_Info_List[i]['GROUP_NAME'].indexOf(Chioce_Input_Info) != -1) {
                Chioce_Group_List.push(Report_Info_List[i]);
            };
        };
    };
    // 渲染数据
    var mission_active_totle =''
    for (let i = 0; i < Chioce_Group_List.length; i++) {
        var mission_info =  Chioce_Group_List[i]
        var mission_info_one = `
            <a href="javascript:;" class="list-group-item" onclick="Requests_Sentiment_Message('${i}','${mission_info['GROUP_NUMBER']}')">
                <div class="media">
                    <div class="chat-user-online">
                        <img src="${mission_info['GROUP_IMAGE']}" width="42" height="42" class="rounded-circle" alt="" />
                    </div>
                    <div class="media-body ml-2">
                        <h6 class="mb-0 chat-title">${mission_info['GROUP_NAME']}</h6>
                        <p class="mb-0 chat-msg"><i class='bx bx-user mr-1'></i>${mission_info['GROUP_MEMBER']}</p>
                    </div>
                    <div class="chat-time">${mission_info['GROUP_SHOW_LASTTIME']}</div>
                </div>
            </a>`
        mission_active_totle += mission_info_one
    }
    document.getElementById("Wechat_Group_Element").innerHTML = mission_active_totle;
        
};


/****************************************初始化请求QQ群组数据***************************************************/ 
function Requests_Sentiment_QQ_Group() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_qq_info',
        "data_argument": `{}`,
        "data_kwargs": {
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                var mission_active_totle =''
                for (let i = 0; i < Result.QQ_Group_List.length; i++) {
                    var mission_info =  Result.QQ_Group_List[i]
                    var mission_info_one = `
                       <a href="javascript:;" class="list-group-item" onclick="Requests_Sentiment_Message('${i}','${mission_info['GROUP_NUMBER']}')">
                            <div class="media">
                                <div class="chat-user-online">
                                    <img src="${mission_info['GROUP_IMAGE']}" width="42" height="42" class="rounded-circle" alt="" />
                                </div>
                                <div class="media-body ml-2">
                                    <h6 class="mb-0 chat-title">${mission_info['GROUP_NAME']}</h6>
                                    <p class="mb-0 chat-msg"><i class='bx bx-user mr-1'></i>${mission_info['GROUP_MEMBER']}</p>
                                </div>
                                <div class="chat-time">${mission_info['GROUP_SHOW_LASTTIME']}</div>
                            </div>
                        </a>`
                    mission_active_totle += mission_info_one
                }
                document.getElementById("Wechat_Group_Element").innerHTML = mission_active_totle;
                QQ_Info_List = Result.QQ_Group_List;
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};


function Requests_Sentiment_QQ_Message(Index,GROUP_NUMBER) {
    // console.log('Requests_Sentiment_Wchat_Message:',Index,GROUP_NUMBER)
    Loading_Show();

    // ----------- 设置a标签高亮
    var element_Info = document.getElementById('Wechat_Group_Element');
    var links = element_Info.getElementsByTagName('a');
    for (var i = 0; i < links.length; i++) {
        if (i.toString() === Index.toString()) {
            links[i].classList.add('active');
        } else {
            links[i].classList.remove('active');
        };
    };
    // -------设置聊天内容抬头
    for (var i = 0; i < QQ_Info_List.length; i++) { 
        if (QQ_Info_List[i]['GROUP_NUMBER'] === GROUP_NUMBER) {
            document.getElementById('Wechat_Group_Name_Element').innerHTML = QQ_Info_List[i]['GROUP_NAME'];
        };
    };

    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_qq_message',
        "data_argument": `{}`,
        "data_kwargs": {
            'GROUP_NUMBER': GROUP_NUMBER
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                $table.clear();
                $table.rows.add(Result.Group_Content_List);
                $table.draw();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

/****************************************筛选***************************************************/ 
function Chioce_QQ_Group_Event() {
    let Chioce_Input_Info = document.getElementById('Chioce_Group_Element').value;
    console.log('群组名称:',Chioce_Input_Info)
    var Chioce_QQ_Group_List = []
    if (Chioce_Input_Info.length === 0){
        Chioce_QQ_Group_List = QQ_Info_List
    } else {
        for (var i = 0; i < QQ_Info_List.length; i++) { 
            if (QQ_Info_List[i]['GROUP_NAME'].indexOf(Chioce_Input_Info) != -1) {
                Chioce_QQ_Group_List.push(QQ_Info_List[i]);
            };
        };
    };
    // 渲染数据
    var mission_active_totle =''
    for (let i = 0; i < Chioce_QQ_Group_List.length; i++) {
        var mission_info =  Chioce_QQ_Group_List[i]
        var mission_info_one = `
            <a href="javascript:;" class="list-group-item" onclick="Requests_Sentiment_Message('${i}','${mission_info['GROUP_NUMBER']}')">
                <div class="media">
                    <div class="chat-user-online">
                        <img src="${mission_info['GROUP_IMAGE']}" width="42" height="42" class="rounded-circle" alt="" />
                    </div>
                    <div class="media-body ml-2">
                        <h6 class="mb-0 chat-title">${mission_info['GROUP_NAME']}</h6>
                        <p class="mb-0 chat-msg"><i class='bx bx-user mr-1'></i>${mission_info['GROUP_MEMBER']}</p>
                    </div>
                    <div class="chat-time">${mission_info['GROUP_SHOW_LASTTIME']}</div>
                </div>
            </a>`
        mission_active_totle += mission_info_one
    }
    document.getElementById("Wechat_Group_Element").innerHTML = mission_active_totle;
        
};