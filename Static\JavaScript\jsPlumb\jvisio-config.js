var visioConfig = {
  visioTemplate: {}
}
// <!-- 
// 	Circle
// 	Ellipse
// Triangle
// Diamond
// Rectangle
// Square -->
// 基本模板节点
visioConfig.visioTemplate.nodeStart = '<div class="jnode-panel" id="{{id}}" style="top:{{top}}px;left:{{left}}px"><div class="jnode-box jnode-radius bdc-success"><span>开始</span></div></div>'
visioConfig.visioTemplate.nodeTask = '<div class="jnode-panel" id="{{id}}" style="top:{{top}}px;left:{{left}}px"><div class="jnode-box jnode-task bdc-primary"><span>节点</span></div></div>'
visioConfig.visioTemplate.nodeJudge = '<div class="jnode-panel" id="{{id}}" style="top:{{top}}px;left:{{left}}px"><div class="jnode-box jnode-diamond jnode-judge bdc-warning"><span>判断</span></div></div>'
visioConfig.visioTemplate.nodeEnd = '<div class="jnode-panel" id="{{id}}" style="top:{{top}}px;left:{{left}}px"><div class="jnode-box jnode-radius bdc-danger"><span>结束</span></div></div>'
visioConfig.visioTemplate.nodeElement = '<div class="jnode-panel" id="{{id}}" style="top:{{top}}px;left:{{left}}px"><div class="jnode-box jnode-square bdc-danger"><span>结束</span></div></div>'

// 基本锚点方位
visioConfig.baseArchors = ['TopCenter', 'RightMiddle', 'BottomCenter','LeftMiddle'];
//visioConfig.baseArchors = ['TopCenter', 'RightMiddle', 'BottomCenter','LeftMiddle','TopLeft', 'TopRight','BottomLeft','BottomRight'];

// 基本连接线样式
visioConfig.connectorPaintStyle = {
  lineWidth: 2,
  strokeStyle: '#337ab7',//#61B7CF',
  joinstyle: 'round',
  outlineColor: 'transparent',
  outlineWidth: 2
}
// 鼠标悬浮在连接线上的样式
visioConfig.connectorHoverStyle = {
  lineWidth: 2,
  strokeStyle: '#d58512',
  outlineWidth: 2,
  outlineColor: 'transparent', 
  
  strokeWidth: 2, 
  dashstyle: 0
}
// 鼠标悬浮连接线上两端锚点样式
visioConfig.endpointHoverStyle = {
	/*lineWidth: 2,
  strokeStyle: '#d58512',
  outlineWidth: 2,
  outlineColor: '#d58512', */
  
  strokeStyle: '#d58512',
  strokeWidth: 2, 
  dashstyle: 0
}
//锚点样式
visioConfig.hollowCircle = {
  // 是否可以拖动（作为连线起点）
  isSource: true,
  // 是否可以放置（连线终点）
  isTarget: true,
  // 连接线的样式种类有[Bezier],[Flowchart],[StateMachine],[Straight]
  connector: ['Flowchart', { stub: [5, 10], gap: 5, cornerRadius: 5, alwaysRespectStubs: true }],
  //端点的颜色样式
  paintStyle: {
    strokeStyle: '#1e8151',
    fillStyle: 'transparent',
    radius: 2,
    lineWidth: 2
  },
  //端点的形状[Dot,Rectangle],设置端点的大小、css类名、浮动上去的css类名
  //endpoint: ['Dot', { radius: 8 }],
  endpoint: ["Dot",{radius: 2,dashstyle: 0,cssClass:"initial_endpoint",hoverClass:"hover_endpoint"}],
  // 连接线的颜色，大小样式
  connectorStyle: visioConfig.connectorPaintStyle,
  //设置连线hover颜色
  connectorHoverStyle: visioConfig.connectorHoverStyle,
  // 鼠标悬浮连接线上两端锚点样式
  hoverPaintStyle: visioConfig.endpointHoverStyle,
  // 设置连接点最多可以连接几条线
  maxConnections: -1,
  // anchor: "AutoDefault",
  // 设置连线中间的自定义节点和箭头
  connectorOverlays: [['Arrow', { width: 10, length: 10, location: 1 }]]
}
