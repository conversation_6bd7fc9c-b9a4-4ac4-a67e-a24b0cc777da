import React, { useState, useEffect } from 'react';
import { Card, Table, Select, Button, Input, Space, Tag, message, Spin } from 'antd';
import { SearchOutlined, AlertOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import '../Styles/Page_Alarm_Center.css';

const { Option } = Select;

interface AlarmRecord {
  id: string;
  alertTime: string;
  alertClass: string;
  alertStatus: 'Active' | 'Finish';
  alertTitle: string;
  alertContent: string;
}

const Page_Alarm_Center: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [alarmData, setAlarmData] = useState<AlarmRecord[]>([]);
  const [originalData, setOriginalData] = useState<AlarmRecord[]>([]);

  // 筛选条件
  const [dateFilter, setDateFilter] = useState('Day');
  const [alertClassFilter, setAlertClassFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');
  const [contentFilter, setContentFilter] = useState('');

  // 日期筛选选项
  const dateOptions = [
    { value: 'Day', label: '今日' },
    { value: 'TwoDay', label: '近两日' },
    { value: 'Mouth', label: '本月' },
    { value: 'HalfYear', label: '近半年' },
    { value: 'Year', label: '近一年' },
    { value: 'All', label: '所有' },
    { value: 'Other', label: '自定义' }
  ];

  // 预警方式选项
  const alertClassOptions = [
    { value: 'All', label: '全部' },
    { value: '平台', label: '平台' },
    { value: 'App', label: 'App' },
    { value: '微信', label: '微信' },
    { value: '短信', label: '短信' },
    { value: '钉钉', label: '钉钉' },
    { value: '公众号', label: '公众号' }
  ];

  // 处置状态选项
  const statusOptions = [
    { value: 'All', label: '全部' },
    { value: 'Active', label: '未处置' },
    { value: 'Finish', label: '已处置' }
  ];

  // 获取预警数据
  const fetchAlarmData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockData: AlarmRecord[] = [
        {
          id: '1',
          alertTime: '2025-01-09 18:45:11',
          alertClass: '平台预警',
          alertStatus: 'Active',
          alertTitle: '微博一条【高关心度舆情】',
          alertContent: '详情（略）'
        },
        {
          id: '2',
          alertTime: '2025-01-02 08:30:29',
          alertClass: '平台预警',
          alertStatus: 'Active',
          alertTitle: 'Youtube一条【高关心度舆情】',
          alertContent: '详情（略）'
        },
        {
          id: '3',
          alertTime: '2024-12-28 00:56:32',
          alertClass: '平台预警',
          alertStatus: 'Active',
          alertTitle: '微博一条【高关心度舆情】',
          alertContent: '详情（略）'
        },
        {
          id: '4',
          alertTime: '2024-12-22 14:50:10',
          alertClass: '平台预警',
          alertStatus: 'Active',
          alertTitle: '微博一条【高关心度舆情】',
          alertContent: '详情（略）'
        },
        {
          id: '5',
          alertTime: '2024-12-18 18:22:33',
          alertClass: '平台预警',
          alertStatus: 'Active',
          alertTitle: 'Youtube一条【高关心度舆情】',
          alertContent: '详情（略）'
        },
        {
          id: '6',
          alertTime: '2024-12-17 16:22:46',
          alertClass: '平台预警',
          alertStatus: 'Finish',
          alertTitle: '微博一条【高关心度舆情】',
          alertContent: '详情（略）'
        }
      ];

      setAlarmData(mockData);
      setOriginalData(mockData);
    } catch (error) {
      console.error('获取预警数据失败:', error);
      message.error('获取预警数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 筛选数据
  const handleFilter = () => {
    fetchAlarmData();
  };

  // 查询数据
  const handleSearch = () => {
    let filteredData = [...originalData];

    // 预警方式筛选
    if (alertClassFilter !== 'All') {
      filteredData = filteredData.filter(item =>
        item.alertClass.includes(alertClassFilter)
      );
    }

    // 处置状态筛选
    if (statusFilter !== 'All') {
      filteredData = filteredData.filter(item =>
        item.alertStatus === statusFilter
      );
    }

    // 内容筛选
    if (contentFilter.trim()) {
      filteredData = filteredData.filter(item =>
        (item.alertTitle + item.alertContent).includes(contentFilter.trim())
      );
    }

    setAlarmData(filteredData);
  };

  // 重置筛选条件
  const handleReset = () => {
    setAlertClassFilter('All');
    setStatusFilter('All');
    setContentFilter('');
    setAlarmData(originalData);
  };

  // 处置操作
  const handleDispose = (id: string) => {
    console.log('处置预警:', id);
    message.success('预警已处置');
    // 更新状态
    const updatedData = alarmData.map(item =>
      item.id === id ? { ...item, alertStatus: 'Finish' as const } : item
    );
    setAlarmData(updatedData);
  };

  // 复核操作
  const handleReview = (id: string) => {
    console.log('复核预警:', id);
    message.info('正在复核预警信息');
  };

  // 表格列定义
  const columns: ColumnsType<AlarmRecord> = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'alertTime',
      key: 'alertTime',
      width: 160,
      align: 'center',
    },
    {
      title: '预警方式',
      dataIndex: 'alertClass',
      key: 'alertClass',
      width: 120,
      align: 'center',
    },
    {
      title: '处置状态',
      dataIndex: 'alertStatus',
      key: 'alertStatus',
      width: 120,
      align: 'center',
      render: (status: string) => (
        <Tag
          icon={status === 'Active' ? <ExclamationCircleOutlined /> : <CheckCircleOutlined />}
          color={status === 'Active' ? 'warning' : 'success'}
        >
          {status === 'Active' ? '未处置' : '已处置'}
        </Tag>
      ),
    },
    {
      title: '预警标题',
      dataIndex: 'alertTitle',
      key: 'alertTitle',
      ellipsis: true,
    },
    {
      title: '预警内容',
      dataIndex: 'alertContent',
      key: 'alertContent',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          {record.alertStatus === 'Active' && (
            <Button
              type="primary"
              size="small"
              onClick={() => handleDispose(record.id)}
            >
              处置
            </Button>
          )}
          <Button
            type="default"
            size="small"
            onClick={() => handleReview(record.id)}
          >
            复核
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchAlarmData();
  }, []);

  return (
    <div className="alarm-center-page">
      <Card style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <span>日期筛选:</span>
            <Select
              value={dateFilter}
              onChange={setDateFilter}
              style={{ width: 120 }}
            >
              {dateOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Button type="primary" onClick={handleFilter}>
              筛选
            </Button>
          </Space>
        </div>

        <Space wrap>
          <span>预警方式:</span>
          <Select
            value={alertClassFilter}
            onChange={setAlertClassFilter}
            style={{ width: 120 }}
          >
            {alertClassOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>

          <span>处置状态:</span>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>

          <span>预警内容:</span>
          <Input
            placeholder="预警标题/内容查询"
            value={contentFilter}
            onChange={(e) => setContentFilter(e.target.value)}
            style={{ width: 200 }}
            suffix={<SearchOutlined />}
          />

          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>
            重置
          </Button>
        </Space>
      </Card>

      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={alarmData}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 1000 }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default Page_Alarm_Center;
