// 获取当前日期
var today = moment();

// 获取当月的第一天
var startOfMonth = today.clone().startOf('month').format('YYYY-MM-DD');

// 获取当月的最后一天
// var endOfMonth = today.clone().endOf('month').format('YYYY-MM-DD');
// 获取今天的日期
var today = today.format('YYYY-MM-DD');
$('#diy_time_list').dateRangePicker({
    language: 'cn',
    separator: ' 至 ',
    endDate: new Date()

}).bind('datepicker-apply', function (event, obj) {

    console.log(obj);
})
var status_Emus = {
    'Finish': '已完成',
    'Active': '执行中',
    'Recharge': '已退款',
    'Failed': '异常中',
    'Execute': '执行中',
    'API': '执行中'
}
var $table = $('#table').DataTable({
    autoWidth: false,
    "columns": [
        { "title": "序号", width: '80px', "data": null, render: function (data, type, row, meta) { return meta.row + 1; } },
        { "title": "充值时间", "data": "LOG_TIME" },
        { "title": "充值金额", "data": "POINT", render: function (data, type, row, meta) { return data + ' 元'; } },
        { "title": "订单状态", "data": "STATUS", render: function (data, type, row, meta) { return status_Emus[data] } },
        { "title": "描述", "data": "DESCRIBE" },
    ],
    "columnDefs": [
        { className: 'text-center', targets: '_all' },
    ],
    "oLanguage": { //国际化配置
        "sProcessing": "正在获取数据，请稍后...",
        "sLengthMenu": "显示 _MENU_ 条",
        "sZeroRecords": "没有您要搜索的内容",
        "sInfo": "从 _START_ 到  _END_ 条记录 总记录数为 _TOTAL_ 条",
        "sInfoEmpty": "记录数为0",
        "sInfoFiltered": "(全部记录数 _MAX_ 条)",
        "sInfoPostFix": "",
        "sSearch": "搜索",
        "sUrl": "",
        "oPaginate": {
            "sFirst": "第一页",
            "sPrevious": "上一页",
            "sNext": "下一页",
            "sLast": "最后一页"
        }
    },
});
function query_table() {
    let diy_time_list = $('#diy_time_list').val().split(' 至 ')
    let start_time = diy_time_list[0]
    let end_time = diy_time_list[1]
    loaderShow()
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'History_Pay',
    //     'User_Token': User_Token,
    //     'start_date': start_time,
    //     'end_date': end_time
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'User', 
        'data_type': 'Service', 
        'data_methods': 'return_history_pay',
        "data_argument": `{}`,
        "data_kwargs":{'start_date': start_time,'end_date': end_time},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
    .then(res => {
        $table.clear().draw();
        if (res.length > 0) {
            $table.rows.add(res).draw();
        }
    }).finally(() => {
        loaderHide()
    })
}
// 查询
$('#query_table').on('click', function () {
    query_table()
})
// 导出
$('#export_table').on('click', function () {
    let filename = $('#diy_time_list').val()
    exportTableToExcel('table', filename)
})
function exportTableToExcel(tableID, filename = '') {
    let downloadLink;
    let dataType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    let tableSelect = document.getElementById(tableID);
    let tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');

    let wb = XLSX.utils.table_to_book(tableSelect, { sheet: "Sheet JS" });
    let wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }

    let blob = new Blob([s2ab(wbout)], { type: dataType });
    downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = filename ? filename + '.xlsx' : 'excel_data.xlsx';

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}