import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Row, Col, Card, Typography } from 'antd';
import { useECharts } from '../Function/Function_useECharts';
import { useServiceRequests } from '@/Core/Core_Control';
import '../Styles/Page_Home.css';

const { Title, Text } = Typography;

interface OverviewData {
  totalCount: number;
  positiveCount: number;
  neutralCount: number;
  negativeCount: number;
  todayPercent: {
    all: string;
    positive: string;
    neutral: string;
    negative: string;
  };
}

interface HotArticle {
  id: string;
  title: string;
  source: string;
  publishTime: string;
  heat: string;
  url?: string;
}

const Page_Home: React.FC = () => {
  const navigate = useNavigate();
  const { AsyncTokenRequests } = useServiceRequests();
  const [overviewData, setOverviewData] = useState<OverviewData>({
    totalCount: 0,
    positiveCount: 0,
    neutralCount: 0,
    negativeCount: 0,
    todayPercent: {
      all: '+0%',
      positive: '+0%',
      neutral: '-0%',
      negative: '+0%'
    }
  });

  const [hotArticles, setHotArticles] = useState<HotArticle[]>([]);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [scrollInterval, setScrollInterval] = useState<NodeJS.Timeout | null>(null);

  // 图表配置状态
  const [trendOptions, setTrendOptions] = useState({});
  const [platformOptions, setPlatformOptions] = useState({});
  const [emotionOptions, setEmotionOptions] = useState({});
  const [wordCloudOptions, setWordCloudOptions] = useState({});

  // 图表容器引用
  const trendChartRef = useECharts(trendOptions, false);
  const platformChartRef = useECharts(platformOptions, false);
  const emotionChartRef = useECharts(emotionOptions, false);
  const wordCloudRef = useECharts(wordCloudOptions, false);

  // 跳转到详情页面
  const handleToDetails = (emotion?: string) => {
    console.log('跳转到详情页面，情感筛选:', emotion);
    const searchParams = new URLSearchParams();
    if (emotion && emotion !== 'All') {
      searchParams.set('emotion', emotion);
    }
    const queryString = searchParams.toString();
    navigate(`/Page_Information_Monitor${queryString ? `?${queryString}` : ''}`);
  };

  // 处理文章链接点击
  const handleArticleClick = (url: string) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  // 滚动动画
  const startScroll = () => {
    // 清理之前的interval
    if (scrollInterval) {
      clearInterval(scrollInterval);
    }

    if (hotArticles.length > 0) {
      console.log('启动滚动，文章数量:', hotArticles.length);

      const newInterval = setInterval(() => {
        setScrollPosition(prev => {
          const scrollContainer = document.getElementById('scroll-content');
          const containerElement = document.querySelector('.scroll-container');

          if (!scrollContainer || !containerElement) {
            console.log('滚动容器未找到');
            return prev;
          }

          const containerHeight = containerElement.clientHeight;
          const contentHeight = scrollContainer.scrollHeight;

          console.log('容器高度:', containerHeight, '内容高度:', contentHeight, '当前位置:', prev);

          // 如果内容高度小于等于容器高度，不需要滚动
          if (contentHeight <= containerHeight) {
            return prev;
          }

          // 计算新位置
          const newPosition = prev - 1;

          // 如果滚动到底部（内容完全移出视图），重置到顶部
          if (Math.abs(newPosition) >= contentHeight) {
            console.log('重置滚动位置');
            return 0;
          }

          return newPosition;
        });
      }, 30); // 减少间隔时间，让滚动更流畅

      setScrollInterval(newInterval);
    }
  };

  // 初始化趋势图表
  const initTrendChart = () => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: function(params: any) {
          return `${params[0].name}: ${params[0].value} 条`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'],
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.65)',
          fontSize: 12
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.65)',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        }
      },
      series: [{
        name: '舆情走势',
        type: 'line',
        data: [], // 空数据，等待API数据更新
        smooth: true,
        lineStyle: {
          color: '#667eea',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(102, 126, 234, 0.4)'
            }, {
              offset: 1, color: 'rgba(102, 126, 234, 0.05)'
            }]
          }
        },
        itemStyle: {
          color: '#667eea',
          borderWidth: 2,
          borderColor: '#fff'
        },
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          showSymbol: true,
          symbolSize: 8
        }
      }]
    };

    setTrendOptions(option);
  };

  // 初始化平台分布图表
  const initPlatformChart = () => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)',
          fontSize: 12
        },
        itemGap: 15
      },
      series: [{
        name: '媒体类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        data: [], // 空数据，等待API数据更新
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: 'rgba(255, 255, 255, 0.87)'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        }
      }]
    };

    setPlatformOptions(option);
  };

  // 初始化情感分类图表
  const initEmotionChart = () => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)',
          fontSize: 12
        },
        itemGap: 20
      },
      series: [{
        name: '情感分类',
        type: 'pie',
        radius: ['50%', '80%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        data: [], // 空数据，等待API数据更新
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: 'rgba(255, 255, 255, 0.87)'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        }
      }]
    };

    setEmotionOptions(option);
  };

  // 初始化词云图表
  const initWordCloudChart = () => {
    const keywords: any[] = []; // 空数据，等待API数据更新

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: function(params: any) {
          return `${params.data.name}: ${params.data.value}`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: 100
      },
      yAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: 100
      },
      series: [{
        type: 'scatter',
        symbolSize: function(data: any) {
          return Math.sqrt(data[2]) * 3;
        },
        data: keywords.map((item, index) => {
          const colors = ['#667eea', '#4facfe', '#fa709a', '#ffecd2', '#a8edea', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#6c5ce7'];
          return {
            name: item.name,
            value: [
              Math.random() * 80 + 10,
              Math.random() * 80 + 10,
              item.value
            ],
            itemStyle: {
              color: colors[index % colors.length],
              opacity: 0.8
            },
            label: {
              show: true,
              formatter: '{b}',
              position: 'inside',
              textStyle: {
                color: '#fff',
                fontSize: Math.max(10, Math.min(18, item.value / 8)),
                fontWeight: 'bold',
                textShadowColor: 'rgba(0, 0, 0, 0.5)',
                textShadowBlur: 2
              }
            }
          };
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowOffsetY: 5,
            opacity: 1
          }
        }
      }]
    };

    setWordCloudOptions(option);
  };

  // 更新趋势图表数据
  const updateTrendChart = (data: number[]) => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: function(params: any) {
          return `${params[0].name}: ${params[0].value} 条`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'],
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.65)',
          fontSize: 12
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.65)',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        }
      },
      series: [{
        name: '舆情走势',
        type: 'line',
        data: data,
        smooth: true,
        lineStyle: {
          color: '#667eea',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(102, 126, 234, 0.4)'
            }, {
              offset: 1, color: 'rgba(102, 126, 234, 0.05)'
            }]
          }
        },
        itemStyle: {
          color: '#667eea',
          borderWidth: 2,
          borderColor: '#fff'
        },
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          showSymbol: true,
          symbolSize: 8
        }
      }]
    };

    setTrendOptions(option);
  };

  // 更新平台分布图表数据
  const updatePlatformChart = (data: any[]) => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)',
          fontSize: 12
        },
        itemGap: 15
      },
      series: [{
        name: '媒体类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        data: data.map((item, index) => {
          const colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'];
          return {
            value: item[1],
            name: item[0],
            itemStyle: { color: colors[index % colors.length] }
          };
        }),
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: 'rgba(255, 255, 255, 0.87)'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        }
      }]
    };

    setPlatformOptions(option);
  };

  // 更新情感分类图表数据
  const updateEmotionChart = (data: any[]) => {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)',
          fontSize: 12
        },
        itemGap: 20
      },
      series: [{
        name: '情感分类',
        type: 'pie',
        radius: ['50%', '80%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        data: data.map((item) => {
          let itemStyle;
          if (item[0] === '正面') {
            itemStyle = {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#4facfe' },
                  { offset: 1, color: '#00f2fe' }
                ]
              }
            };
          } else if (item[0] === '中性') {
            itemStyle = {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#ffecd2' },
                  { offset: 1, color: '#fcb69f' }
                ]
              }
            };
          } else {
            itemStyle = {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#fa709a' },
                  { offset: 1, color: '#fee140' }
                ]
              }
            };
          }
          return {
            value: item[1],
            name: item[0],
            itemStyle
          };
        }),
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: 'rgba(255, 255, 255, 0.87)'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        }
      }]
    };

    setEmotionOptions(option);
  };

  // 更新词云图表数据
  const updateWordCloudChart = (data: any[]) => {
    const keywords = data.map(item => ({
      name: item[0],
      value: item[1]
    }));

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(44, 62, 80, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.87)'
        },
        formatter: function(params: any) {
          return `${params.data.name}: ${params.data.value}`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: 100
      },
      yAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: 100
      },
      series: [{
        type: 'scatter',
        symbolSize: function(data: any) {
          return Math.sqrt(data[2]) * 3;
        },
        data: keywords.map((item, index) => {
          const colors = ['#667eea', '#4facfe', '#fa709a', '#ffecd2', '#a8edea', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#6c5ce7'];
          return {
            name: item.name,
            value: [
              Math.random() * 80 + 10,
              Math.random() * 80 + 10,
              item.value
            ],
            itemStyle: {
              color: colors[index % colors.length],
              opacity: 0.8
            },
            label: {
              show: true,
              formatter: '{b}',
              position: 'inside',
              textStyle: {
                color: '#fff',
                fontSize: Math.max(10, Math.min(18, item.value / 8)),
                fontWeight: 'bold',
                textShadowColor: 'rgba(0, 0, 0, 0.5)',
                textShadowBlur: 2
              }
            }
          };
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowOffsetY: 5,
            opacity: 1
          }
        }
      }]
    };

    setWordCloudOptions(option);
  };

  // 获取舆情信息数据
  const requestsSentimentInfo = async () => {
    try {
      const requestsData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_domestic_visualization',
        data_argument: '{}',
        data_kwargs: JSON.stringify({})
        // data_kwargs:  '{}',
      };

      const result = await AsyncTokenRequests(requestsData);
      console.log('API请求结果:', result);

      if (result && result.Status === 'Success' && result.Data) {
        console.log('开始处理API数据:', result.Data);

        // 处理情感分类数据
        const emotionData = {
          totalCount: result.Data.Today_Sentiment_Count || 0,
          positiveCount: 0,
          neutralCount: 0,
          negativeCount: 0,
          todayPercent: {
            all: result.Data.Today_Sentiment_Time_Percent?.All || '+0',
            positive: result.Data.Today_Sentiment_Time_Percent?.Positive || '+0',
            neutral: result.Data.Today_Sentiment_Time_Percent?.Neutral || '+0',
            negative: result.Data.Today_Sentiment_Time_Percent?.Negative || '+0'
          }
        };

        // 解析情感分类数据
        if (result.Data.Today_Sentiment_Emotion_Info) {
          result.Data.Today_Sentiment_Emotion_Info.forEach((item: any) => {
            if (item[0] === '正面') {
              emotionData.positiveCount = item[1];
            } else if (item[0] === '中性') {
              emotionData.neutralCount = item[1];
            } else if (item[0] === '负面') {
              emotionData.negativeCount = item[1];
            }
          });
        }

        console.log('处理后的情感数据:', emotionData);
        setOverviewData(emotionData);

        // 处理热门文章数据
        if (result.Data.Today_Sentiment_Hot_Article) {
          const articles = result.Data.Today_Sentiment_Hot_Article.map((article: any, index: number) => ({
            id: index.toString(),
            title: article.Title || '',
            source: article.Platform || '',
            publishTime: article.PublishTime || '',
            heat: article.Heat || '',
            url: article.Url || ''
          }));
          console.log('处理后的热门文章:', articles);
          setHotArticles(articles);
        }

        // 延迟更新图表数据，确保图表已经初始化
        setTimeout(() => {
          // 更新趋势图表数据
          if (result.Data.Today_Sentiment_Time_Crawl) {
            console.log('更新趋势图表:', result.Data.Today_Sentiment_Time_Crawl);
            updateTrendChart(result.Data.Today_Sentiment_Time_Crawl);
          }

          // 更新平台分布图表数据
          if (result.Data.Today_Sentiment_Platform_Type) {
            console.log('更新平台分布图表:', result.Data.Today_Sentiment_Platform_Type);
            updatePlatformChart(result.Data.Today_Sentiment_Platform_Type);
          }

          // 更新情感分类图表数据
          if (result.Data.Today_Sentiment_Emotion_Info) {
            console.log('更新情感分类图表:', result.Data.Today_Sentiment_Emotion_Info);
            updateEmotionChart(result.Data.Today_Sentiment_Emotion_Info);
          }

          // 更新关键词词云数据
          if (result.Data.Today_Sentiment_Key) {
            console.log('更新关键词词云:', result.Data.Today_Sentiment_Key);
            updateWordCloudChart(result.Data.Today_Sentiment_Key);
          }
        }, 600); // 确保在图表初始化之后更新数据
      } else {
        console.error('API请求失败或返回状态不正确，result:', result);
        // 设置空数据状态
        setOverviewData({
          totalCount: 0,
          positiveCount: 0,
          neutralCount: 0,
          negativeCount: 0,
          todayPercent: {
            all: '+0',
            positive: '+0',
            neutral: '+0',
            negative: '+0'
          }
        });
        setHotArticles([]);
      }
    } catch (error) {
      console.error('获取舆情信息失败:', error);
      // 设置空数据状态
      setOverviewData({
        totalCount: 0,
        positiveCount: 0,
        neutralCount: 0,
        negativeCount: 0,
        todayPercent: {
          all: '+0',
          positive: '+0',
          neutral: '+0',
          negative: '+0'
        }
      });
      setHotArticles([]);
    }
  };

  // 页面初始化
  const pageInit = () => {
    requestsSentimentInfo();
    // 不在这里启动滚动，等数据加载完成后再启动
  };

  useEffect(() => {
    pageInit();

    // 增加延迟时间，确保DOM完全渲染
    const initCharts = () => {
      setTimeout(() => {
        initTrendChart();
        initPlatformChart();
        initEmotionChart();
        initWordCloudChart();
      }, 500); // 增加到500ms
    };

    // 立即初始化
    initCharts();

    // 监听窗口大小变化，重新初始化图表
    const handleResize = () => {
      setTimeout(() => {
        initTrendChart();
        initPlatformChart();
        initEmotionChart();
        initWordCloudChart();
      }, 100);
    };

    // 监听页面可见性变化
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('页面重新可见，重新加载数据和图表');
        // 重新初始化图表
        setTimeout(() => {
          initTrendChart();
          initPlatformChart();
          initEmotionChart();
          initWordCloudChart();
        }, 200);

        // 重新加载数据
        setTimeout(() => {
          requestsSentimentInfo();
        }, 300);
      }
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 监听数据变化，确保图表正确渲染
  useEffect(() => {
    if (overviewData.totalCount > 0) {
      setTimeout(() => {
        // 数据更新后重新调整图表大小
        window.dispatchEvent(new Event('resize'));
      }, 100);
    }
  }, [overviewData, hotArticles]);

  // 监听热门文章数据变化，重新启动滚动
  useEffect(() => {
    if (hotArticles.length > 0) {
      console.log('文章数据更新，准备启动滚动');
      setTimeout(() => {
        startScroll();
      }, 500); // 减少延迟时间
    }
  }, [hotArticles]);

  // 组件卸载时清理interval
  useEffect(() => {
    return () => {
      if (scrollInterval) {
        clearInterval(scrollInterval);
      }
    };
  }, [scrollInterval]);

  return (
    <div style={{ padding: '1rem' }}>
      {/* 信息总览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '1rem' }}>
        <Col xs={24} lg={6}>
          <Card
            className="radius-15 stats-card"
            style={{
              cursor: 'pointer',
              background: 'linear-gradient(135deg, rgba(18, 150, 219, 0.1) 0%, rgba(18, 150, 219, 0.05) 100%)',
              border: '1px solid rgba(18, 150, 219, 0.2)',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(18, 150, 219, 0.3)';
              e.currentTarget.style.borderColor = 'rgba(18, 150, 219, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.borderColor = 'rgba(18, 150, 219, 0.2)';
            }}
            onClick={() => handleToDetails('All')}
          >
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-50%',
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle, rgba(18, 150, 219, 0.1) 0%, transparent 70%)',
              pointerEvents: 'none'
            }} />

            <div style={{ display: 'flex', alignItems: 'center', position: 'relative', zIndex: 1 }}>
              <div style={{ flex: 1 }}>
                <Title level={2} style={{
                  margin: 0,
                  color: '#ffffff',
                  fontSize: '2.2rem',
                  fontWeight: '600',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}>
                  <span style={{
                    background: 'linear-gradient(45deg, #1296db, #4facfe)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}>
                    {overviewData.totalCount}
                  </span>
                  <span style={{
                    fontSize: '14px',
                    marginLeft: '8px',
                    color: overviewData.todayPercent.all.includes('+') ? '#52c41a' : '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }}>
                    {overviewData.todayPercent.all.includes('+') ? '↗' : '↘'}
                  </span>
                </Title>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '35px',
                color: '#ffffff',
                background: 'linear-gradient(135deg, rgba(18, 150, 219, 0.2), rgba(18, 150, 219, 0.1))',
                borderRadius: '12px',
                padding: '12px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(18, 150, 219, 0.3)'
              }}>
                <svg width="35" height="35" viewBox="0 0 1024 1024" fill="url(#totalGradient)">
                  <defs>
                    <linearGradient id="totalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#1296db" />
                      <stop offset="100%" stopColor="#4facfe" />
                    </linearGradient>
                  </defs>
                  <path d="M239.296 533.312h545.408a32.384 32.384 0 0 0 32.768-32c0-17.664-14.72-32-32.768-32H239.36a32.384 32.384 0 0 0-32.768 32c0 17.728 14.72 32 32.768 32z m0-234.624h545.408a32.384 32.384 0 0 0 32.768-32c0-17.664-14.72-32-32.768-32H239.36a32.384 32.384 0 0 0-32.768 32c0 17.664 14.72 32 32.768 32zM479.296 704h-240a32.384 32.384 0 0 0-32.768 32c0 17.664 14.72 32 32.768 32h240a32.384 32.384 0 0 0 32.704-32c0-17.664-14.656-32-32.704-32z m240-64c-66.304 0-120 52.48-120 117.312s53.76 117.376 120 117.376c66.24 0 120-52.544 120-117.376 0-64.768-53.76-117.312-120-117.312z m0 170.688a53.952 53.952 0 0 1-54.592-53.376c0-29.44 24.448-53.312 54.592-53.312 30.08 0 54.528 23.872 54.528 53.312 0 29.44-24.448 53.376-54.528 53.376zM882.88 0H141.12C80.832 0 32 47.744 32 106.688v810.624C32 976.192 80.832 1024 141.12 1024h741.76c60.288 0 109.12-47.744 109.12-106.688V106.688C992 47.808 943.168 0 882.88 0z m43.648 917.312a43.136 43.136 0 0 1-43.648 42.688H141.12a43.136 43.136 0 0 1-43.648-42.688V106.688c0-23.552 19.52-42.688 43.648-42.688h741.76c24.128 0 43.648 19.136 43.648 42.688v810.624z"/>
                </svg>
              </div>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginTop: '12px',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <Text style={{
                  margin: 0,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>信息总量</Text>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '14px',
                color: overviewData.todayPercent.all.includes('+') ? '#52c41a' : '#ff4d4f',
                fontWeight: '600',
                background: overviewData.todayPercent.all.includes('+')
                  ? 'rgba(82, 196, 26, 0.1)'
                  : 'rgba(255, 77, 79, 0.1)',
                padding: '4px 8px',
                borderRadius: '6px',
                border: `1px solid ${overviewData.todayPercent.all.includes('+') ? 'rgba(82, 196, 26, 0.3)' : 'rgba(255, 77, 79, 0.3)'}`
              }}>
                {overviewData.todayPercent.all}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card
            className="radius-15 stats-card"
            style={{
              cursor: 'pointer',
              background: 'linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0.05) 100%)',
              border: '1px solid rgba(82, 196, 26, 0.2)',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(82, 196, 26, 0.3)';
              e.currentTarget.style.borderColor = 'rgba(82, 196, 26, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.borderColor = 'rgba(82, 196, 26, 0.2)';
            }}
            onClick={() => handleToDetails('正面')}
          >
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-50%',
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, transparent 70%)',
              pointerEvents: 'none'
            }} />

            <div style={{ display: 'flex', alignItems: 'center', position: 'relative', zIndex: 1 }}>
              <div style={{ flex: 1 }}>
                <Title level={2} style={{
                  margin: 0,
                  color: '#ffffff',
                  fontSize: '2.2rem',
                  fontWeight: '600',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}>
                  <span style={{
                    background: 'linear-gradient(45deg, #52c41a, #73d13d)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}>
                    {overviewData.positiveCount}
                  </span>
                  <span style={{
                    fontSize: '14px',
                    marginLeft: '8px',
                    color: overviewData.todayPercent.positive.includes('+') ? '#52c41a' : '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }}>
                    {overviewData.todayPercent.positive.includes('+') ? '↗' : '↘'}
                  </span>
                </Title>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '35px',
                color: '#ffffff',
                background: 'linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1))',
                borderRadius: '12px',
                padding: '12px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(82, 196, 26, 0.3)'
              }}>
                <svg width="42" height="42" viewBox="0 0 1024 1024" fill="url(#positiveGradient)">
                  <defs>
                    <linearGradient id="positiveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#52c41a" />
                      <stop offset="100%" stopColor="#73d13d" />
                    </linearGradient>
                  </defs>
                  <path d="M0 512A512.1536 512.1536 0 0 1 512 0a512.1536 512.1536 0 0 1 512 512 512.1536 512.1536 0 0 1-512 512A512.1536 512.1536 0 0 1 0 512z m972.8 0c0-60.928-11.776-119.808-35.072-176.384-23.552-56.32-56.832-106.24-99.84-149.504a464.1792 464.1792 0 0 0-149.504-99.84C631.808 62.976 572.928 51.2 512 51.2s-119.808 11.776-176.384 35.072c-56.32 23.552-106.24 56.832-149.504 99.84a464.1792 464.1792 0 0 0-99.84 149.504C62.976 392.192 51.2 451.072 51.2 512s11.776 119.808 35.072 176.384c23.552 56.32 56.832 106.24 99.84 149.504 43.264 43.008 93.184 76.288 149.504 99.84C392.192 961.024 451.072 972.8 512 972.8s119.808-11.776 176.384-35.072c56.32-23.552 106.24-56.832 149.504-99.84 43.008-43.264 76.288-93.184 99.84-149.504C961.024 631.808 972.8 572.928 972.8 512z m-650.752-214.528c0.512-1.536 1.28-3.072 2.304-4.608l3.072-3.84 3.84-3.072c1.536-1.024 3.072-1.792 4.608-2.304 1.536-0.768 3.072-1.28 4.608-1.536 1.792-0.256 3.328-0.512 5.12-0.512h358.4c1.792 0 3.328 0.256 5.12 0.512 1.536 0.256 3.072 0.768 4.608 1.536 1.536 0.512 3.072 1.28 4.608 2.304l3.84 3.072 3.072 3.84c1.024 1.536 1.792 3.072 2.304 4.608 0.768 1.536 1.28 3.072 1.536 4.608 0.256 1.792 0.512 3.328 0.512 5.12s-0.256 3.328-0.512 5.12c-0.256 1.536-0.768 3.072-1.536 4.608-0.512 1.536-1.28 3.072-2.304 4.608l-3.072 3.84-3.84 3.072c-1.536 1.024-3.072 1.792-4.608 2.304-1.536 0.768-3.072 1.28-4.608 1.536-1.792 0.256-3.328 0.512-5.12 0.512h-153.6v128h126.208c3.328 0 6.656 0.768 9.728 2.048 3.328 1.28 5.888 3.072 8.448 5.376 2.304 2.56 4.096 5.12 5.376 8.448 1.28 3.072 2.048 6.4 2.048 9.728 0 1.792-0.256 3.328-0.512 4.864-0.256 1.792-0.768 3.328-1.536 4.864-0.512 1.536-1.28 3.072-2.304 4.608-0.768 1.28-2.048 2.56-3.072 3.84l-3.84 3.072c-1.536 1.024-3.072 1.792-4.608 2.304-1.536 0.768-3.072 1.28-4.608 1.536-1.792 0.256-3.328 0.512-5.12 0.512H550.4v179.2h153.6c1.792 0 3.328 0.256 5.12 0.512 1.536 0.256 3.072 0.768 4.608 1.536 1.536 0.512 3.072 1.28 4.608 2.304 1.28 0.768 2.56 2.048 3.84 3.072l3.072 3.84c1.024 1.536 1.792 3.072 2.304 4.608 0.768 1.536 1.28 3.072 1.536 4.608 0.256 1.792 0.512 3.328 0.512 5.12 0 3.328-0.768 6.656-2.048 9.728-1.28 3.328-3.072 5.888-5.376 8.448-2.56 2.304-5.12 4.096-8.448 5.376-3.072 1.28-6.4 2.048-9.728 2.048h-384c-1.792 0-3.328-0.256-5.12-0.512a16.2816 16.2816 0 0 1-4.608-1.536 19.3792 19.3792 0 0 1-4.608-2.304c-1.28-0.768-2.56-2.048-3.84-3.072l-3.072-3.84a19.3792 19.3792 0 0 1-2.304-4.608 16.2816 16.2816 0 0 1-1.536-4.608c-0.256-1.792-0.512-3.328-0.512-5.12 0-3.328 0.768-6.656 2.048-9.728s3.072-5.888 5.376-8.448c2.56-2.304 5.12-4.096 8.448-5.376 3.072-1.28 6.4-2.048 9.728-2.048h51.2V435.2c0-3.328 0.768-6.656 2.048-9.728 1.28-3.328 3.072-5.888 5.376-8.448 2.56-2.304 5.12-4.096 8.448-5.376 3.072-1.28 6.4-2.048 9.728-2.048s6.656 0.768 9.728 2.048c3.328 1.28 5.888 3.072 8.448 5.376 2.304 2.56 4.096 5.12 5.376 8.448 1.28 3.072 2.048 6.4 2.048 9.728v256h76.8v-195.584c-1.024-3.072-1.792-6.144-1.792-9.216 0-1.536 0.256-3.328 0.512-4.864s0.512-3.072 1.28-4.352V332.8h-153.6c-1.792 0-3.328-0.256-5.12-0.512a16.2816 16.2816 0 0 1-4.608-1.536 19.3792 19.3792 0 0 1-4.608-2.304l-3.84-3.072-3.072-3.84a19.3792 19.3792 0 0 1-2.304-4.608 16.2816 16.2816 0 0 1-1.536-4.608c-0.256-1.792-0.512-3.328-0.512-5.12s0.256-3.328 0.512-5.12c0.256-1.536 0.768-3.072 1.536-4.608z"/>
                </svg>
              </div>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginTop: '12px',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <Text style={{
                  margin: 0,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>正面信息</Text>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '14px',
                color: overviewData.todayPercent.positive.includes('+') ? '#52c41a' : '#ff4d4f',
                fontWeight: '600',
                background: overviewData.todayPercent.positive.includes('+')
                  ? 'rgba(82, 196, 26, 0.1)'
                  : 'rgba(255, 77, 79, 0.1)',
                padding: '4px 8px',
                borderRadius: '6px',
                border: `1px solid ${overviewData.todayPercent.positive.includes('+') ? 'rgba(82, 196, 26, 0.3)' : 'rgba(255, 77, 79, 0.3)'}`
              }}>
                {overviewData.todayPercent.positive}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card
            className="radius-15 stats-card"
            style={{
              cursor: 'pointer',
              background: 'linear-gradient(135deg, rgba(255, 162, 69, 0.1) 0%, rgba(255, 162, 69, 0.05) 100%)',
              border: '1px solid rgba(255, 162, 69, 0.2)',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 162, 69, 0.3)';
              e.currentTarget.style.borderColor = 'rgba(255, 162, 69, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.borderColor = 'rgba(255, 162, 69, 0.2)';
            }}
            onClick={() => handleToDetails('中性')}
          >
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-50%',
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle, rgba(255, 162, 69, 0.1) 0%, transparent 70%)',
              pointerEvents: 'none'
            }} />

            <div style={{ display: 'flex', alignItems: 'center', position: 'relative', zIndex: 1 }}>
              <div style={{ flex: 1 }}>
                <Title level={2} style={{
                  margin: 0,
                  color: '#ffffff',
                  fontSize: '2.2rem',
                  fontWeight: '600',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}>
                  <span style={{
                    background: 'linear-gradient(45deg, #FFA245, #ffb347)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}>
                    {overviewData.neutralCount}
                  </span>
                  <span style={{
                    fontSize: '14px',
                    marginLeft: '8px',
                    color: overviewData.todayPercent.neutral.includes('+') ? '#52c41a' : '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }}>
                    {overviewData.todayPercent.neutral.includes('+') ? '↗' : '↘'}
                  </span>
                </Title>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '35px',
                color: '#ffffff',
                background: 'linear-gradient(135deg, rgba(255, 162, 69, 0.2), rgba(255, 162, 69, 0.1))',
                borderRadius: '12px',
                padding: '12px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 162, 69, 0.3)'
              }}>
                <svg width="42" height="42" viewBox="0 0 1024 1024" fill="url(#neutralGradient)">
                  <defs>
                    <linearGradient id="neutralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#FFA245" />
                      <stop offset="100%" stopColor="#ffb347" />
                    </linearGradient>
                  </defs>
                  <path d="M691.2 384h-153.6v-102.4c0-7.68-2.56-12.8-7.68-17.92-5.12-5.12-10.24-7.68-17.92-7.68s-12.8 2.56-17.92 7.68c-5.12 5.12-7.68 10.24-7.68 17.92v102.4h-153.6c-12.8 0-25.6 12.8-25.6 25.6v179.2c0 12.8 12.8 25.6 25.6 25.6h153.6v128c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-128h153.6c12.8 0 25.6-12.8 25.6-25.6v-179.2c0-12.8-12.8-25.6-25.6-25.6z m-204.8 179.2h-102.4c-12.8 0-25.6-12.8-25.6-25.6v-76.8c0-12.8 12.8-25.6 25.6-25.6h102.4v128z m179.2-25.6c0 12.8-12.8 25.6-25.6 25.6h-102.4v-128h102.4c12.8 0 25.6 12.8 25.6 25.6v76.8z"/>
                  <path d="M872.96 151.04C778.24 53.76 647.68 0 512 0S245.76 53.76 151.04 151.04 0 376.32 0 512s53.76 266.24 151.04 360.96S376.32 1024 512 1024s266.24-53.76 360.96-151.04S1024 647.68 1024 512 970.24 245.76 872.96 151.04z m-35.84 686.08c-43.52 43.52-92.16 76.8-148.48 99.84-56.32 23.04-115.2 35.84-176.64 35.84s-120.32-12.8-176.64-35.84c-56.32-23.04-104.96-56.32-148.48-99.84-43.52-43.52-76.8-92.16-99.84-148.48C64 632.32 51.2 573.44 51.2 512s12.8-120.32 35.84-176.64c23.04-56.32 56.32-104.96 99.84-148.48 43.52-43.52 92.16-76.8 148.48-99.84C391.68 64 450.56 51.2 512 51.2s120.32 12.8 176.64 35.84c56.32 23.04 104.96 56.32 148.48 99.84 43.52 43.52 76.8 92.16 99.84 148.48 23.04 56.32 35.84 115.2 35.84 176.64s-12.8 120.32-35.84 176.64c-23.04 56.32-56.32 104.96-99.84 148.48z"/>
                </svg>
              </div>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginTop: '12px',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <Text style={{
                  margin: 0,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>中性信息</Text>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '14px',
                color: overviewData.todayPercent.neutral.includes('+') ? '#52c41a' : '#ff4d4f',
                fontWeight: '600',
                background: overviewData.todayPercent.neutral.includes('+')
                  ? 'rgba(82, 196, 26, 0.1)'
                  : 'rgba(255, 77, 79, 0.1)',
                padding: '4px 8px',
                borderRadius: '6px',
                border: `1px solid ${overviewData.todayPercent.neutral.includes('+') ? 'rgba(82, 196, 26, 0.3)' : 'rgba(255, 77, 79, 0.3)'}`
              }}>
                {overviewData.todayPercent.neutral}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card
            className="radius-15 stats-card"
            style={{
              cursor: 'pointer',
              background: 'linear-gradient(135deg, rgba(216, 30, 6, 0.1) 0%, rgba(216, 30, 6, 0.05) 100%)',
              border: '1px solid rgba(216, 30, 6, 0.2)',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(216, 30, 6, 0.3)';
              e.currentTarget.style.borderColor = 'rgba(216, 30, 6, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.borderColor = 'rgba(216, 30, 6, 0.2)';
            }}
            onClick={() => handleToDetails('负面')}
          >
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-50%',
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle, rgba(216, 30, 6, 0.1) 0%, transparent 70%)',
              pointerEvents: 'none'
            }} />

            <div style={{ display: 'flex', alignItems: 'center', position: 'relative', zIndex: 1 }}>
              <div style={{ flex: 1 }}>
                <Title level={2} style={{
                  margin: 0,
                  color: '#ffffff',
                  fontSize: '2.2rem',
                  fontWeight: '600',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}>
                  <span style={{
                    background: 'linear-gradient(45deg, #d81e06, #ff4d4f)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}>
                    {overviewData.negativeCount}
                  </span>
                  <span style={{
                    fontSize: '14px',
                    marginLeft: '8px',
                    color: overviewData.todayPercent.negative.includes('+') ? '#52c41a' : '#ff4d4f',
                    animation: 'pulse 2s infinite'
                  }}>
                    {overviewData.todayPercent.negative.includes('+') ? '↗' : '↘'}
                  </span>
                </Title>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '35px',
                color: '#ffffff',
                background: 'linear-gradient(135deg, rgba(216, 30, 6, 0.2), rgba(216, 30, 6, 0.1))',
                borderRadius: '12px',
                padding: '12px',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(216, 30, 6, 0.3)'
              }}>
                <svg width="42" height="42" viewBox="0 0 1024 1024" fill="url(#negativeGradient)">
                  <defs>
                    <linearGradient id="negativeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#d81e06" />
                      <stop offset="100%" stopColor="#ff4d4f" />
                    </linearGradient>
                  </defs>
                  <path d="M872.96 151.04C778.24 53.76 647.68 0 512 0S245.76 53.76 151.04 151.04 0 376.32 0 512s53.76 266.24 151.04 360.96S376.32 1024 512 1024s266.24-53.76 360.96-151.04S1024 647.68 1024 512 970.24 245.76 872.96 151.04z m-35.84 686.08c-43.52 43.52-92.16 76.8-148.48 99.84-56.32 23.04-115.2 35.84-176.64 35.84s-120.32-12.8-176.64-35.84c-56.32-23.04-104.96-56.32-148.48-99.84-43.52-43.52-76.8-92.16-99.84-148.48C64 632.32 51.2 573.44 51.2 512s12.8-120.32 35.84-176.64c23.04-56.32 56.32-104.96 99.84-148.48 43.52-43.52 92.16-76.8 148.48-99.84C391.68 64 450.56 51.2 512 51.2s120.32 12.8 176.64 35.84c56.32 23.04 104.96 56.32 148.48 99.84 43.52 43.52 76.8 92.16 99.84 148.48 23.04 56.32 35.84 115.2 35.84 176.64s-12.8 120.32-35.84 176.64c-23.04 56.32-56.32 104.96-99.84 148.48z"/>
                  <path d="M706.56 716.8H663.04c-23.04-2.56-40.96-7.68-56.32-17.92-43.52-25.6-64-84.48-66.56-168.96 0-7.68-7.68-15.36-17.92-15.36h-12.8c-12.8 0-23.04 10.24-23.04 23.04-2.56 81.92-25.6 138.24-66.56 163.84-15.36 10.24-33.28 15.36-56.32 17.92h-53.76L307.2 768h28.16c12.8 0 23.04 0 33.28-2.56 30.72-2.56 56.32-12.8 79.36-25.6 28.16-17.92 51.2-46.08 66.56-79.36 15.36 35.84 38.4 61.44 66.56 79.36 23.04 15.36 48.64 23.04 79.36 25.6 10.24 0 23.04 2.56 33.28 2.56h17.92c2.56 0 5.12 0 7.68-2.56 2.56-2.56 2.56-5.12 2.56-7.68l-5.12-33.28c0-5.12-5.12-7.68-10.24-7.68z"/>
                  <path d="M340.48 458.24c7.68-2.56 12.8-2.56 20.48-7.68 0 2.56-2.56 5.12-2.56 7.68v153.6c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-102.4c0-12.8 12.8-25.6 25.6-25.6h153.6c12.8 0 25.6 12.8 25.6 25.6v102.4c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-153.6c0-12.8-12.8-25.6-25.6-25.6h-33.28c10.24-12.8 17.92-28.16 23.04-43.52 5.12-12.8 7.68-25.6 10.24-40.96 0-7.68 2.56-12.8 2.56-17.92v-12.8c0-2.56 0-7.68-5.12-7.68-2.56-2.56-7.68-2.56-10.24-2.56h-148.48l7.68-30.72c0-5.12-2.56-10.24-5.12-12.8-5.12-7.68-7.68-7.68-12.8-7.68H460.8c-12.8 0-23.04 10.24-25.6 23.04 2.56-7.68 0 10.24-7.68 28.16-7.68 17.92-17.92 35.84-30.72 51.2-12.8 15.36-25.6 28.16-38.4 38.4-7.68 5.12-12.8 7.68-17.92 10.24-7.68 2.56-15.36 10.24-15.36 20.48v15.36c0 5.12 2.56 10.24 5.12 12.8 0 2.56 5.12 2.56 10.24 2.56z m94.72-69.12c7.68-10.24 15.36-20.48 20.48-33.28h122.88c2.56 0 2.56 0 5.12 2.56v5.12c0 2.56-2.56 5.12-2.56 7.68-7.68 25.6-23.04 43.52-40.96 56.32-2.56 2.56-5.12 2.56-5.12 5.12h-145.92c15.36-10.24 30.72-25.6 46.08-43.52z"/>
                </svg>
              </div>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginTop: '12px',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <Text style={{
                  margin: 0,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>负面信息</Text>
              </div>
              <div style={{
                marginLeft: 'auto',
                fontSize: '14px',
                color: overviewData.todayPercent.negative.includes('+') ? '#52c41a' : '#ff4d4f',
                fontWeight: '600',
                background: overviewData.todayPercent.negative.includes('+')
                  ? 'rgba(82, 196, 26, 0.1)'
                  : 'rgba(255, 77, 79, 0.1)',
                padding: '4px 8px',
                borderRadius: '6px',
                border: `1px solid ${overviewData.todayPercent.negative.includes('+') ? 'rgba(82, 196, 26, 0.3)' : 'rgba(255, 77, 79, 0.3)'}`
              }}>
                {overviewData.todayPercent.negative}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 数据汇总曲线图 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '1rem' }}>
        <Col xs={24} lg={16}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0, marginBottom: '1rem' }}>今日舆情走势</Title>
                </div>
              </div>
            </div>
            <div>
              <div ref={trendChartRef.chartRef} style={{ height: '300px' }} id="chart1"></div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card className="radius-15">
            <div style={{ borderBottom: 'none' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div>
                  <Title level={5} style={{ margin: 0, marginBottom: '1rem' }}>最热文章</Title>
                </div>
              </div>
            </div>
            <div>
              <div
                className="scroll-container"
                id="scroll-container"
                style={{
                  height: '300px',
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                <div
                  id="scroll-content"
                  style={{
                    position: 'absolute',
                    top: scrollPosition,
                    width: '100%'
                  }}
                >
                  {/* 渲染文章列表，重复两次确保有足够内容滚动 */}
                  {[...hotArticles, ...hotArticles].map((article, index) => (
                    <div
                      key={`article-${index}`}
                      className="item"
                      style={{
                        width: '100%',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        boxSizing: 'border-box',
                        paddingRight: '10px',
                        marginBottom: '8px',
                        cursor: article.url ? 'pointer' : 'default',
                        height: '32px',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                      onClick={() => article.url && handleArticleClick(article.url)}
                    >
                      <Text style={{ color: '#ffffff' }}>
                        <span style={{ color: '#ffc107' }}>{article.source} - </span>
                        {article.title}
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 媒体类型 情感分类 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card className="radius-15">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>媒体类型</Title>
              </div>
            </div>
            <div ref={platformChartRef.chartRef} style={{ height: '400px' }} id="Today_Sentiment_Platform_Type_Bar"></div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card className="radius-15">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>情感分类</Title>
              </div>
            </div>
            <div ref={emotionChartRef.chartRef} style={{ height: '400px' }} id="Today_Sentiment_Emotion_Bar"></div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card className="radius-15">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>中标词分析</Title>
              </div>
            </div>
            <div
              ref={wordCloudRef.chartRef}
              style={{ height: '400px' }}
              id="Today_Sentiment_Keyword_WordCloud"
            ></div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Page_Home;
