﻿<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
    <!-- select2 -->
    <link href="/static/CSS/select2/css/select2.min.css" rel="stylesheet" />
    <link href="/static/CSS/select2/css/select2-bootstrap4.css" rel="stylesheet" />
    <!-- table -->
    <link rel="stylesheet" href="/static/CSS/datatable/css/buttons.bootstrap4.min.css" />
    <link rel="stylesheet" href="/static/CSS/datatable/css/dataTables.bootstrap4.min.css" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content" style="height: 880px;">
                    <!-- <div class="card"> -->
						<ul class="nav nav-tabs" id="myTab" role="tablist">
							<li class="nav-item" role="presentation" onclick="Table_Click_Event('Wechat')"> <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">微信</a>
							</li>
							<li class="nav-item" role="presentation" onclick="Table_Click_Event('QQ')"> <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">QQ</a>
							</li>
						</ul>
						<!-- <div class="tab-content p-3" id="myTabContent">
							<div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
								<div class="chat-wrapper" style="height:760px">
									<div class="chat-sidebar">
										<div class="chat-sidebar-header">
											<div class="input-group input-group-sm">
												<input type="text" class="form-control border-left-0" id="Chioce_Group_Element" placeholder="群组名">
												<div class="input-group-prepend" onclick="Chioce_Group_Event()">	
													<span class="input-group-text bg-transparent"><i class='bx bx-search'></i></span>
												</div>
												
											</div>
										</div>
										<div class="chat-sidebar-content">
											<div class="chat-list" style="height:660px">
												<div class="list-group list-group-flush" id="Wechat_Group_Element">
												</div>
											</div>
										</div>
									</div>
									<div class="chat-header d-flex align-items-center">
										<div class="chat-toggle-btn"><i class='bx bx-menu-alt-left'></i>
										</div>
										<div>
											<h4 class="mb-1 font-weight-bold" id="Wechat_Group_Name_Element"></h4>
										</div>
									</div>
									<div class="chat-content" style="height:760px">
										<table
											id="Reports_table"
											class="table table-striped table-bordered"
										></table>
									</div>
								</div>
							</div>
							<div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
								<div class="chat-wrapper" style="height:760px">
									<div class="chat-sidebar">
										<div class="chat-sidebar-header">
											<div class="input-group input-group-sm">
												<input type="text" class="form-control border-left-0" id="Chioce_QQ_Group_Element" placeholder="群组名">
												<div class="input-group-prepend" onclick="Chioce_QQ_Group_Event()">	
													<span class="input-group-text bg-transparent"><i class='bx bx-search'></i></span>
												</div>
												
											</div>
										</div>
										<div class="chat-sidebar-content">
											<div class="chat-list" style="height:660px; overflow: auto;">
												<div class="list-group list-group-flush" id="QQ_Group_Element">
												</div>
											</div>
										</div>
									</div>
									<div class="chat-header d-flex align-items-center">
										<div class="chat-toggle-btn"><i class='bx bx-menu-alt-left'></i>
										</div>
										<div>
											<h4 class="mb-1 font-weight-bold" id="QQ_Group_Name_Element"></h4>
										</div>
									</div>
									<div class="chat-content" style="height:760px">
										<table
											id="QQ_table"
											class="table table-striped table-bordered"
										></table>
									</div>
								</div>
							</div>
						</div> -->
						<div class="chat-wrapper" style="height:760px">
							<div class="chat-sidebar">
								<div class="chat-sidebar-header">
									<div class="input-group input-group-sm">
										<input type="text" class="form-control border-left-0" id="Chioce_Group_Element" placeholder="群组名">
										<div class="input-group-prepend" onclick="Chioce_Group_Totle_Event()">	
											<span class="input-group-text bg-transparent"><i class='bx bx-search'></i></span>
										</div>
										
									</div>
								</div>
								<div class="chat-sidebar-content">
									<div class="chat-list" style="height:660px">
										<div class="list-group list-group-flush" id="Wechat_Group_Element">
										</div>
									</div>
								</div>
							</div>
							<div class="chat-header d-flex align-items-center">
								<div class="chat-toggle-btn"><i class='bx bx-menu-alt-left'></i>
								</div>
								<div>
									<h4 class="mb-1 font-weight-bold" id="Wechat_Group_Name_Element"></h4>
								</div>
							</div>
							<div class="chat-content" style="height:760px">
								<table
									id="Reports_table"
									class="table table-striped table-bordered"
								></table>
							</div>
						</div>
				</div>
			</div>
		</div>
		<!-- <div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div> -->
	</div>
    <div class="modal fade" id="loadingModal" backdrop="static" keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div style="width: 200px;height:20px; z-index: 20000; position: absolute; text-align: center; left: 50%; top: 50%;margin-left:-100px;margin-top:-10px">
                <div class="text-center">
                    <div class="spinner-border text-info spinner-border-sm" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                    <strong style="color:#198fed">Loading...</strong>
                </div>
            </div>   
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
    <script src="/static/JavaScript/select2/js/select2.min.js"></script>
    <script src="/static/JavaScript/datatable/js/jquery.dataTables.min.js"></script>
	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Monitor.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <script>
		new PerfectScrollbar('.chat-list');
		new PerfectScrollbar('.chat-content');
	</script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Sentiment_Wchat_Group();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>