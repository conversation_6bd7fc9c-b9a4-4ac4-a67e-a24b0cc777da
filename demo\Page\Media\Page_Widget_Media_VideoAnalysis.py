# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
# from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.Utils.UtilsCenter import *
import numpy as np
import librosa

Page_Info={
    "Title":"哨兵核心服务配置",
    "Param": {},

}

from Bin.System.OS.Component import Component_Common, Component_AudioPlay,Component_AudioEQ,Component_AudioNoise,Component_AudioChat,Component_AudioTranslate,Component_Processing_EQ

from Bin.System.OS.Component import Component_Player_Multimedia,Component_LyricsPlayer,Component_Chat

PP(Page_Info)
# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_Media_VideoAnalysis(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass

        Page_Info["Page_Element_List"]={}
        Page_Info["StyleSheet"]={}
        Page_Info["StyleSheet"]["Value_Color"] ="rgba(0, 255, 136, 255)"

        self.initUI()


    def initUI(self):
        # self.Set_Title()
        self.Set_Content()

        # self.setStyleSheet("background-color: rgba(40, 52, 80, 0.3)")





    def Set_Content(self):
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(3, 3, 3, 3)


        self.QSplitter_Root = QtWidgets.QSplitter(Qt.Vertical)
        # 上部水平分割器（左 + 右）
        self.QSplitter_HBox= QtWidgets.QSplitter(Qt.Horizontal)


        self.QLabel_Right = QtWidgets.QLabel()
        self.QLabel_Right.setStyleSheet("background:#000;color:#fff;")
        self.QLabel_Right.setMinimumWidth(300)  # 确保最小宽度 100px
        # self.QLabel_Right.hide()  # 初始隐藏

        # 添加到水平分割器
        self.QSplitter_HBox.addWidget(self.Set_Left())
        self.QSplitter_HBox.addWidget(self.Set_Right())
        self.QSplitter_HBox.setSizes([self.width(), 0])  # 初始右侧宽度 0


        # self.QLabel_Bottom.hide()  # 初始隐藏

        # 添加到垂直分割器
        self.QSplitter_Root.addWidget(self.QSplitter_HBox)
        self.QSplitter_Root.addWidget(self.Set_Bottom())
        self.QSplitter_Root.setSizes([300, 0])  # 初始底部高度 0

        # 主布局

        __QVBoxLayout.addWidget(self.QSplitter_Root)

        # 动画（右侧）
        self.QPropertyAnimation_Right = QtCore.QPropertyAnimation(self.QSplitter_HBox, b"sizes")
        # self.anim_right.setDuration(300)
        self.right_expanded = False

        # 动画（底部）
        self.QPropertyAnimation_Bottom = QtCore.QPropertyAnimation(self.QSplitter_Root, b"sizes")
        # self.anim_bottom.setDuration(300)
        self.bottom_expanded = False

        self.Toggle_Right()
        self.Toggle_Bottom()



    def Set_Left(self):
        # 左侧区域
        # self.QLabel_Left = QLabel_Video_Analysis(self)
        # self.QLabel_Left.setStyleSheet("background:rgba(18, 27, 53, 255)")
        # self.QLabel_Left = QLabel_Function({"Title_Name":"视频播放"})
        self.QLabel_Left = QLabel_Function({"Title_Name": "视频播放"})
        self.QLabel_Left.setStyleSheet("background:rgba(18, 27, 53, 255)")

        # self.QVBoxLayout_Left = QtWidgets.QVBoxLayout(self.QLabel_Left)
        # self.QVBoxLayout_Left.setContentsMargins(0, 0, 0, 0)
        self.QHBoxLayout_Left = QtWidgets.QHBoxLayout(self.QLabel_Left.QLabel_Content)
        self.QHBoxLayout_Left.setSpacing(8)
        self.QHBoxLayout_Left.setContentsMargins(8, 20, 8, 20)


        # Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{Channel_ID}"]
        Channel_ID="1"

        # __QLabel_Video_Channel = QLabel_Video_Show({"Channel_ID": Channel_ID})
        # __QLabel_Video_Channel.setStyleSheet("background:rgba(18, 27, 53, 255)")
        # QHBoxLayout_Content.addWidget(__QLabel_Video_Channel)
        __Component_Player_Multimedia_O =Component_Player_Multimedia.Component_Player_Multimedia(self,{})
        __Component_Player_Multimedia_A =Component_Player_Multimedia.Component_Player_Multimedia(self,{})


        self.QHBoxLayout_Left.addWidget(__Component_Player_Multimedia_O)
        self.QHBoxLayout_Left.addWidget(__Component_Player_Multimedia_A)

        # self.QVBoxLayout_Left.addWidget(self.Set_Video_Source())
        return self.QLabel_Left



    def Set_Right(self):
        # 左侧区域
        # self.QLabel_Right = QLabel_Function({"Title_Name":"文字时间线"})
        self.QLabel_Right = QLabel_Function({"Title_Name":"iERM 智能体分析"})
        self.QLabel_Right.setStyleSheet("background:rgba(18, 27, 53, 255)")
        # self.QVBoxLayout_Left = QtWidgets.QVBoxLayout(self.QLabel_Left)
        # self.QVBoxLayout_Left.setContentsMargins(0, 0, 0, 0)
        QHBoxLayout_Content = QtWidgets.QHBoxLayout(self.QLabel_Right.QLabel_Content)
        QHBoxLayout_Content.setSpacing(8)
        QHBoxLayout_Content.setContentsMargins(8, 20, 8, 20)

        # Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{Channel_ID}"]
        Channel_ID = "1"

        # __QLabel_Video_Channel = QLabel_Video_Show({"Channel_ID": Channel_ID})
        # __QLabel_Video_Channel.setStyleSheet("background:rgba(18, 27, 53, 255)")
        # QHBoxLayout_Content.addWidget(__QLabel_Video_Channel)
        # __Component_LyricsPlayer =Component_LyricsPlayer.Component_LyricsPlayer()
        # QHBoxLayout_Content.addWidget(__Component_LyricsPlayer)
        __Component_Chat=Component_Chat.Component_Chat()

        QHBoxLayout_Content.addWidget(__Component_Chat)
        # self.QVBoxLayout_Left.addWidget(self.Set_Video_Source())
        return self.QLabel_Right



    def Set_Bottom(self):
        # 底部区域
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("background:transparent;")
        self.QLabel_Bottom.setMinimumHeight(400)
        QGridLayout_Content = QtWidgets.QGridLayout(self.QLabel_Bottom)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(0,0, 0, 8)

        StyleSheet_QLabel = """
                                                 QLabel {
                                                   background-color: rgba(40, 52, 80, 0.3);;
                                                     border: 1px solid rgba(0, 180, 255, 60);
                                                     border-radius: 4px;

                                                     color:rgba(0, 255, 136, 255);

                                                 }
                                                 QLabel:hover {
                                                     background-color: rgba(0, 100, 150, 150);
                                                 }
                                             """
        # self.Function_List=[]
        Function_List = {

            "1": {"Function_Text": "目标检测与追踪", "Function_Value": "18人"},
            "2": {"Function_Text": "特征处理", "Function_Value": "18人"},
            "3": {"Function_Text": "行为分析", "Function_Value": "18人"},
            "4": {"Function_Text": "人脸特征检测", "Function_Value": "18人"},
            "5": {"Function_Text": "视频采集", "Function_Value": "0人"},
            "6": {"Function_Text": "视频预处理", "Function_Value": "0人"},
            "7": {"Function_Text": "视频增益", "Function_Value": "8人"},
            "8": {"Function_Text": "视频AI工具", "Function_Value": "8人"},


        }



        # for in
        Page_Info["Page_Element_List"]["Function_QLabel_List"]      = {}
        Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"] = {}


        Count = 1
        for Row in range(2):
            for Col in range(4):
                _QLabel =  QtWidgets.QLabel()
                # _QLabel.setAlignment(QtCore.Qt.AlignCenter)  # 设置文本居中
                # _QLabel.setFixedHeight(60)
                # _QLabel.setStyleSheet(StyleSheet_QLabel)  # 设置边框
                # _QLabel.setText(f'''<div style="text-align:center;">
                #                     <span style="font-size:13pt;font-weight: bold">{Function_List[Count]["Function_Value"]}</span><br>
                #                     <span style="font-size:8pt;color:#1E90FF;font-weight: bold">{Function_List[Count]["Function_Text"]}</span>
                #                     </div>''')
                _QHBoxLayout = QtWidgets.QHBoxLayout(_QLabel)
                _QHBoxLayout.setSpacing(0)
                _QHBoxLayout.setContentsMargins(0, 0, 0, 0)
                Page_Info["Page_Element_List"]["Function_QLabel_List"][f"{Count}"]      =  _QLabel
                Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"][f"{Count}"] =  _QHBoxLayout
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1


        PJ(Page_Info["Page_Element_List"])
        Function_QHBoxLayout_List={}
        Function_QHBoxLayout_List["1"] = self.Set_Analysis_Function("目标检测与追踪")
        Function_QHBoxLayout_List["2"] = self.Set_Analysis_Function("特征处理")
        Function_QHBoxLayout_List["3"] = self.Set_Analysis_Function("行为分析")
        Function_QHBoxLayout_List["4"] = self.Set_Analysis_Function("人脸特征检测")
        Function_QHBoxLayout_List["5"] = self.Set_Analysis_Function("视频采集")
        Function_QHBoxLayout_List["6"] = self.Set_Analysis_Function("视频预处理")
        Function_QHBoxLayout_List["7"] = self.Set_Analysis_Function("视频增益")
        Function_QHBoxLayout_List["8"] = self.Set_Analysis_Function("视频AI工具")


        for Name, _QHBoxLayout in Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"].items():
            _QHBoxLayout.addWidget( Function_QHBoxLayout_List[Name])

        return self.QLabel_Bottom

    def Set_Analysis_Function(self,Title):
        # QLabel_Info = {
        #     "Title_Name": Title
        # }
        # __QLabel = QLabel_Function(QLabel_Info)
        # QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
        # QGridLayout_Content.setSpacing(8)
        # QGridLayout_Content.setContentsMargins(8, 20, 8, 20)
        # return __QLabel

        QLabel_Info = {
            "Title_Name": Title
        }
        __QLabel = QLabel_Function(QLabel_Info)


        Function_List = {
            "目标检测与追踪": lambda: self.Set_Object_Detection(__QLabel),
            "特征处理": lambda: self.Set_Feature_Processing(__QLabel),
            "行为分析": lambda: self.Set_Behavior_Analysis(__QLabel),
            "人脸特征检测": lambda: self.Set_Face_Detection(__QLabel),
            "视频采集": lambda: self.Set_Video_Capture(__QLabel),
            "视频预处理": lambda: self.Set_Video_Preprocessing(__QLabel),
            "视频增益": lambda: self.Set_Video_Enhancement(__QLabel),
            "视频AI工具": lambda: self.Set_Video_AI_Tools(__QLabel)
        }

        def Set_Default():
            QGridLayout_Content = QtWidgets.QGridLayout(__QLabel.QLabel_Content)
            QGridLayout_Content.setSpacing(8)
            QGridLayout_Content.setContentsMargins(8, 20, 8, 20)
            return __QLabel

        return Function_List.get(Title, Set_Default)()

    def Set_Object_Detection(self, __QLabel):
        """目标检测与追踪功能模块"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 检测状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 实时检测信息
        # detection_info = QtWidgets.QLabel("实时检测中...")
        # detection_info.setAlignment(QtCore.Qt.AlignCenter)
        # detection_info.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        frame_info = QtWidgets.QLabel("帧率: 25 FPS | 分辨率: 1920x1080")
        frame_info.setAlignment(QtCore.Qt.AlignCenter)
        frame_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(detection_info)
        status_layout.addWidget(frame_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 检测统计数据
        stats_layout = QtWidgets.QHBoxLayout()

        # 人员检测
        person_count_label = QtWidgets.QLabel("18")
        person_count_label.setAlignment(QtCore.Qt.AlignCenter)
        person_count_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        person_desc = QtWidgets.QLabel("人员")
        person_desc.setAlignment(QtCore.Qt.AlignCenter)
        person_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 车辆检测
        vehicle_count_label = QtWidgets.QLabel("5")
        vehicle_count_label.setAlignment(QtCore.Qt.AlignCenter)
        vehicle_count_label.setStyleSheet(
            "color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        vehicle_desc = QtWidgets.QLabel("车辆")
        vehicle_desc.setAlignment(QtCore.Qt.AlignCenter)
        vehicle_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 其他目标
        other_count_label = QtWidgets.QLabel("3")
        other_count_label.setAlignment(QtCore.Qt.AlignCenter)
        other_count_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        other_desc = QtWidgets.QLabel("其他")
        other_desc.setAlignment(QtCore.Qt.AlignCenter)
        other_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        person_layout = QtWidgets.QVBoxLayout()
        person_layout.addWidget(person_count_label)
        person_layout.addWidget(person_desc)

        vehicle_layout = QtWidgets.QVBoxLayout()
        vehicle_layout.addWidget(vehicle_count_label)
        vehicle_layout.addWidget(vehicle_desc)

        other_layout = QtWidgets.QVBoxLayout()
        other_layout.addWidget(other_count_label)
        other_layout.addWidget(other_desc)

        stats_layout.addLayout(person_layout)
        stats_layout.addLayout(vehicle_layout)
        stats_layout.addLayout(other_layout)

        QVBoxLayout_Content.addLayout(stats_layout)

        # 追踪状态指示器
        tracking_widget = QtWidgets.QWidget()
        tracking_widget.setFixedHeight(30)
        tracking_layout = QtWidgets.QHBoxLayout(tracking_widget)
        tracking_layout.setContentsMargins(5, 5, 5, 5)

        # 活跃追踪数
        active_tracks = QtWidgets.QLabel("活跃追踪: 12个目标")
        active_tracks.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 检测精度
        accuracy = QtWidgets.QLabel("精度: 94.2%")
        accuracy.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        tracking_layout.addWidget(active_tracks)
        tracking_layout.addStretch()
        tracking_layout.addWidget(accuracy)

        QVBoxLayout_Content.addWidget(tracking_widget)

        return __QLabel

    def Set_Feature_Processing(self, __QLabel):
        """特征处理功能模块"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 特征提取状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 处理状态信息
        # processing_info = QtWidgets.QLabel("特征提取进行中...")
        # processing_info.setAlignment(QtCore.Qt.AlignCenter)
        # processing_info.setStyleSheet("color: #FFD700; font-size: 12px; font-weight: bold; background: transparent;")

        model_info = QtWidgets.QLabel("模型: ResNet50 | 维度: 2048")
        model_info.setAlignment(QtCore.Qt.AlignCenter)
        model_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(processing_info)
        status_layout.addWidget(model_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 特征统计数据
        stats_layout = QtWidgets.QHBoxLayout()

        # 已提取特征
        extracted_label = QtWidgets.QLabel("2847")
        extracted_label.setAlignment(QtCore.Qt.AlignCenter)
        extracted_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        extracted_desc = QtWidgets.QLabel("已提取")
        extracted_desc.setAlignment(QtCore.Qt.AlignCenter)
        extracted_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 待处理
        pending_label = QtWidgets.QLabel("156")
        pending_label.setAlignment(QtCore.Qt.AlignCenter)
        pending_label.setStyleSheet("color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        pending_desc = QtWidgets.QLabel("待处理")
        pending_desc.setAlignment(QtCore.Qt.AlignCenter)
        pending_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 异常数据
        error_label = QtWidgets.QLabel("12")
        error_label.setAlignment(QtCore.Qt.AlignCenter)
        error_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        error_desc = QtWidgets.QLabel("异常")
        error_desc.setAlignment(QtCore.Qt.AlignCenter)
        error_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        extracted_layout = QtWidgets.QVBoxLayout()
        extracted_layout.addWidget(extracted_label)
        extracted_layout.addWidget(extracted_desc)

        pending_layout = QtWidgets.QVBoxLayout()
        pending_layout.addWidget(pending_label)
        pending_layout.addWidget(pending_desc)

        error_layout = QtWidgets.QVBoxLayout()
        error_layout.addWidget(error_label)
        error_layout.addWidget(error_desc)

        stats_layout.addLayout(extracted_layout)
        stats_layout.addLayout(pending_layout)
        stats_layout.addLayout(error_layout)

        QVBoxLayout_Content.addLayout(stats_layout)

        # 处理进度和性能指标
        progress_widget = QtWidgets.QWidget()
        progress_widget.setFixedHeight(40)
        progress_layout = QtWidgets.QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(5, 2, 5, 2)

        # 进度条
        progress_bar = QtWidgets.QProgressBar()
        progress_bar.setFixedHeight(8)
        progress_bar.setRange(0, 100)
        progress_bar.setValue(73)
        progress_bar.setTextVisible(False)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #FFD700;
                border-radius: 3px;
                background-color: rgba(0, 0, 0, 0.1);
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #FFD700, stop:1 #FFA500);
                border-radius: 0px;
            }
        """)

        # 性能指标
        metrics_layout = QtWidgets.QHBoxLayout()

        throughput = QtWidgets.QLabel("处理速度: 128帧/秒")
        throughput.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        accuracy = QtWidgets.QLabel("特征质量: 96.8%")
        accuracy.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        metrics_layout.addWidget(throughput)
        metrics_layout.addStretch()
        metrics_layout.addWidget(accuracy)

        progress_layout.addWidget(progress_bar)
        progress_layout.addLayout(metrics_layout)

        QVBoxLayout_Content.addWidget(progress_widget)

        return __QLabel

    def Set_Behavior_Analysis(self, __QLabel):
        """行为分析功能模块"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 分析状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 实时分析信息
        # analysis_info = QtWidgets.QLabel("行为模式分析中...")
        # analysis_info.setAlignment(QtCore.Qt.AlignCenter)
        # analysis_info.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        model_info = QtWidgets.QLabel("AI模型: YOLOv8 + LSTM | 置信度: 89.5%")
        model_info.setAlignment(QtCore.Qt.AlignCenter)
        model_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(analysis_info)
        status_layout.addWidget(model_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 行为检测统计
        behavior_stats_layout = QtWidgets.QHBoxLayout()

        # 正常行为
        normal_count_label = QtWidgets.QLabel("24")
        normal_count_label.setAlignment(QtCore.Qt.AlignCenter)
        normal_count_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        normal_desc = QtWidgets.QLabel("正常")
        normal_desc.setAlignment(QtCore.Qt.AlignCenter)
        normal_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 异常行为
        abnormal_count_label = QtWidgets.QLabel("2")
        abnormal_count_label.setAlignment(QtCore.Qt.AlignCenter)
        abnormal_count_label.setStyleSheet(
            "color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        abnormal_desc = QtWidgets.QLabel("异常")
        abnormal_desc.setAlignment(QtCore.Qt.AlignCenter)
        abnormal_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 可疑行为
        suspicious_count_label = QtWidgets.QLabel("1")
        suspicious_count_label.setAlignment(QtCore.Qt.AlignCenter)
        suspicious_count_label.setStyleSheet(
            "color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        suspicious_desc = QtWidgets.QLabel("可疑")
        suspicious_desc.setAlignment(QtCore.Qt.AlignCenter)
        suspicious_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        normal_layout = QtWidgets.QVBoxLayout()
        normal_layout.addWidget(normal_count_label)
        normal_layout.addWidget(normal_desc)

        abnormal_layout = QtWidgets.QVBoxLayout()
        abnormal_layout.addWidget(abnormal_count_label)
        abnormal_layout.addWidget(abnormal_desc)

        suspicious_layout = QtWidgets.QVBoxLayout()
        suspicious_layout.addWidget(suspicious_count_label)
        suspicious_layout.addWidget(suspicious_desc)

        behavior_stats_layout.addLayout(normal_layout)
        behavior_stats_layout.addLayout(abnormal_layout)
        behavior_stats_layout.addLayout(suspicious_layout)

        QVBoxLayout_Content.addLayout(behavior_stats_layout)

        # 具体行为类型显示
        behavior_types_widget = QtWidgets.QWidget()
        behavior_types_widget.setFixedHeight(60)
        behavior_types_widget.setStyleSheet("background: rgba(0, 0, 0, 0.05); border-radius: 4px;")

        behavior_types_layout = QtWidgets.QVBoxLayout(behavior_types_widget)
        behavior_types_layout.setContentsMargins(8, 5, 8, 5)



        behavior_list = QtWidgets.QLabel("行走(15) • 站立(7) • 跑步(2) • 聚集(2) • 徘徊(1)")
        behavior_list.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")
        behavior_list.setWordWrap(True)

        behavior_types_layout.addWidget(behavior_list)

        QVBoxLayout_Content.addWidget(behavior_types_widget)

        # 分析状态指示器
        analysis_status_widget = QtWidgets.QWidget()
        analysis_status_widget.setFixedHeight(25)
        analysis_status_layout = QtWidgets.QHBoxLayout(analysis_status_widget)
        analysis_status_layout.setContentsMargins(5, 3, 5, 3)

        # 处理帧数
        processed_frames = QtWidgets.QLabel("已处理: 1,247帧")
        processed_frames.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 响应时间
        response_time = QtWidgets.QLabel("响应: <50ms")
        response_time.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 准确率
        accuracy_rate = QtWidgets.QLabel("准确率: 92.8%")
        accuracy_rate.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        analysis_status_layout.addWidget(processed_frames)
        analysis_status_layout.addStretch()
        analysis_status_layout.addWidget(response_time)
        analysis_status_layout.addStretch()
        analysis_status_layout.addWidget(accuracy_rate)

        QVBoxLayout_Content.addWidget(analysis_status_widget)

        return __QLabel

    def Set_Face_Detection(self, __QLabel):
        """人脸特征检测功能模块"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 检测状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 人脸检测状态
        # detection_status = QtWidgets.QLabel("人脸特征分析中...")
        # detection_status.setAlignment(QtCore.Qt.AlignCenter)
        # detection_status.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        model_info = QtWidgets.QLabel("模型: FaceNet v2.1 | 置信度阈值: 0.85")
        model_info.setAlignment(QtCore.Qt.AlignCenter)
        model_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(detection_status)
        status_layout.addWidget(model_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 人脸统计数据
        stats_layout = QtWidgets.QHBoxLayout()

        # 检测到的人脸数
        face_count_label = QtWidgets.QLabel("8")
        face_count_label.setAlignment(QtCore.Qt.AlignCenter)
        face_count_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        face_desc = QtWidgets.QLabel("检测人脸")
        face_desc.setAlignment(QtCore.Qt.AlignCenter)
        face_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 已识别人脸
        recognized_label = QtWidgets.QLabel("5")
        recognized_label.setAlignment(QtCore.Qt.AlignCenter)
        recognized_label.setStyleSheet("color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        recognized_desc = QtWidgets.QLabel("已识别")
        recognized_desc.setAlignment(QtCore.Qt.AlignCenter)
        recognized_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 未知人脸
        unknown_label = QtWidgets.QLabel("3")
        unknown_label.setAlignment(QtCore.Qt.AlignCenter)
        unknown_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        unknown_desc = QtWidgets.QLabel("未知")
        unknown_desc.setAlignment(QtCore.Qt.AlignCenter)
        unknown_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        face_layout = QtWidgets.QVBoxLayout()
        face_layout.addWidget(face_count_label)
        face_layout.addWidget(face_desc)

        recognized_layout = QtWidgets.QVBoxLayout()
        recognized_layout.addWidget(recognized_label)
        recognized_layout.addWidget(recognized_desc)

        unknown_layout = QtWidgets.QVBoxLayout()
        unknown_layout.addWidget(unknown_label)
        unknown_layout.addWidget(unknown_desc)

        stats_layout.addLayout(face_layout)
        stats_layout.addLayout(recognized_layout)
        stats_layout.addLayout(unknown_layout)

        QVBoxLayout_Content.addLayout(stats_layout)

        # 特征分析详情
        feature_widget = QtWidgets.QWidget()
        feature_widget.setFixedHeight(50)
        feature_widget.setStyleSheet("background: rgba(0, 0, 0, 0.05); border-radius: 4px;")

        feature_layout = QtWidgets.QVBoxLayout(feature_widget)
        feature_layout.setContentsMargins(8, 5, 8, 5)

        # 特征提取信息
        feature_stats = QtWidgets.QHBoxLayout()

        # 年龄检测
        age_info = QtWidgets.QLabel("年龄分析: 启用")
        age_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 性别检测
        gender_info = QtWidgets.QLabel("性别识别: 启用")
        gender_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 情绪检测
        emotion_info = QtWidgets.QLabel("情绪分析: 启用")
        emotion_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        feature_stats.addWidget(age_info)
        feature_stats.addWidget(gender_info)
        feature_stats.addWidget(emotion_info)



        # 底部状态指示
        bottom_status = QtWidgets.QWidget()
        bottom_status.setFixedHeight(25)
        bottom_layout = QtWidgets.QHBoxLayout(bottom_status)
        bottom_layout.setContentsMargins(5, 5, 5, 5)

        # 人脸库状态
        database_status = QtWidgets.QLabel("人脸库: 1,247个样本")
        database_status.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")
        # # 识别精度
        # accuracy_info = QtWidgets.QLabel("平均识别精度: 92.8%")
        # accuracy_info.setAlignment(QtCore.Qt.AlignCenter)
        # accuracy_info.setStyleSheet("color: #1E90FF; font-size: 12px; font-weight: bold; background: transparent;")
        #
        # feature_layout.addLayout(feature_stats)
        # feature_layout.addWidget(accuracy_info)
        #
        # QVBoxLayout_Content.addWidget(feature_widget)

        # 实时更新状态
        update_status = QtWidgets.QLabel("最后更新: 2秒前")
        update_status.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        bottom_layout.addWidget(database_status)
        bottom_layout.addStretch()
        bottom_layout.addWidget(update_status)

        QVBoxLayout_Content.addWidget(bottom_status)

        return __QLabel

    def Set_Video_Capture(self, __QLabel):
        """视频采集功能模块 - 仅展示"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 采集状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 采集状态信息
        # capture_info = QtWidgets.QLabel("多源采集中...")
        # capture_info.setAlignment(QtCore.Qt.AlignCenter)
        # capture_info.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        device_info = QtWidgets.QLabel("设备: USB摄像头 + 网络摄像头 + 屏幕录制")
        device_info.setAlignment(QtCore.Qt.AlignCenter)
        device_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(capture_info)
        status_layout.addWidget(device_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 采集源统计数据
        sources_layout = QtWidgets.QHBoxLayout()

        # USB摄像头
        usb_count_label = QtWidgets.QLabel("3")
        usb_count_label.setAlignment(QtCore.Qt.AlignCenter)
        usb_count_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        usb_desc = QtWidgets.QLabel("USB摄像头")
        usb_desc.setAlignment(QtCore.Qt.AlignCenter)
        usb_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 网络摄像头
        network_count_label = QtWidgets.QLabel("8")
        network_count_label.setAlignment(QtCore.Qt.AlignCenter)
        network_count_label.setStyleSheet(
            "color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        network_desc = QtWidgets.QLabel("网络摄像头")
        network_desc.setAlignment(QtCore.Qt.AlignCenter)
        network_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 屏幕录制
        screen_count_label = QtWidgets.QLabel("2")
        screen_count_label.setAlignment(QtCore.Qt.AlignCenter)
        screen_count_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        screen_desc = QtWidgets.QLabel("屏幕录制")
        screen_desc.setAlignment(QtCore.Qt.AlignCenter)
        screen_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        usb_layout = QtWidgets.QVBoxLayout()
        usb_layout.addWidget(usb_count_label)
        usb_layout.addWidget(usb_desc)

        network_layout = QtWidgets.QVBoxLayout()
        network_layout.addWidget(network_count_label)
        network_layout.addWidget(network_desc)

        screen_layout = QtWidgets.QVBoxLayout()
        screen_layout.addWidget(screen_count_label)
        screen_layout.addWidget(screen_desc)

        sources_layout.addLayout(usb_layout)
        sources_layout.addLayout(network_layout)
        sources_layout.addLayout(screen_layout)

        QVBoxLayout_Content.addLayout(sources_layout)

        # 采集质量和性能指标
        quality_widget = QtWidgets.QWidget()
        quality_widget.setFixedHeight(50)
        quality_widget.setStyleSheet("background: rgba(0, 0, 0, 0.05); border-radius: 4px;")

        quality_layout = QtWidgets.QVBoxLayout(quality_widget)
        quality_layout.setContentsMargins(8, 5, 8, 5)

        # 质量指标
        quality_stats = QtWidgets.QHBoxLayout()

        # 分辨率
        resolution_info = QtWidgets.QLabel("分辨率: 1920×1080")
        resolution_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 帧率
        fps_info = QtWidgets.QLabel("帧率: 30 FPS")
        fps_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 码率
        bitrate_info = QtWidgets.QLabel("码率: 2.5 Mbps")
        bitrate_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        quality_stats.addWidget(resolution_info)
        quality_stats.addWidget(fps_info)
        quality_stats.addWidget(bitrate_info)

        # # 存储信息
        # storage_info = QtWidgets.QLabel("存储路径: /data/video/ | 可用空间: 2.5TB")
        # storage_info.setAlignment(QtCore.Qt.AlignCenter)
        # storage_info.setStyleSheet("color: #1E90FF; font-size: 12px; font-weight: bold; background: transparent;")
        #
        # quality_layout.addLayout(quality_stats)
        # quality_layout.addWidget(storage_info)
        #
        # QVBoxLayout_Content.addWidget(quality_widget)

        # 底部实时状态
        realtime_status = QtWidgets.QWidget()
        realtime_status.setFixedHeight(25)
        realtime_layout = QtWidgets.QHBoxLayout(realtime_status)
        realtime_layout.setContentsMargins(5, 5, 5, 5)

        # 数据流量
        data_flow = QtWidgets.QLabel("数据流量: 156.8 MB/s")
        data_flow.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 缓冲状态
        buffer_status = QtWidgets.QLabel("缓冲: 正常")
        buffer_status.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 录制时长
        record_duration = QtWidgets.QLabel("录制时长: 02:15:43")
        record_duration.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        realtime_layout.addWidget(data_flow)
        realtime_layout.addStretch()
        realtime_layout.addWidget(buffer_status)
        realtime_layout.addStretch()
        realtime_layout.addWidget(record_duration)

        QVBoxLayout_Content.addWidget(realtime_status)

        return __QLabel

    def Set_Video_Preprocessing(self, __QLabel):
        """视频预处理功能模块 - 仅展示"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 预处理状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 预处理信息
        # preprocessing_info = QtWidgets.QLabel("智能预处理管道运行中...")
        # preprocessing_info.setAlignment(QtCore.Qt.AlignCenter)
        # preprocessing_info.setStyleSheet("color: #FFD700; font-size: 12px; font-weight: bold; background: transparent;")

        pipeline_info = QtWidgets.QLabel("降噪 → 增强 → 稳定 → 色彩校正 → 格式转换")
        pipeline_info.setAlignment(QtCore.Qt.AlignCenter)
        pipeline_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(preprocessing_info)
        status_layout.addWidget(pipeline_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 处理步骤统计
        steps_layout = QtWidgets.QHBoxLayout()

        # 降噪处理
        denoise_label = QtWidgets.QLabel("2847")
        denoise_label.setAlignment(QtCore.Qt.AlignCenter)
        denoise_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        denoise_desc = QtWidgets.QLabel("降噪处理")
        denoise_desc.setAlignment(QtCore.Qt.AlignCenter)
        denoise_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 图像增强
        enhance_label = QtWidgets.QLabel("2847")
        enhance_label.setAlignment(QtCore.Qt.AlignCenter)
        enhance_label.setStyleSheet("color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        enhance_desc = QtWidgets.QLabel("图像增强")
        enhance_desc.setAlignment(QtCore.Qt.AlignCenter)
        enhance_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 稳定处理
        stabilize_label = QtWidgets.QLabel("2835")
        stabilize_label.setAlignment(QtCore.Qt.AlignCenter)
        stabilize_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        stabilize_desc = QtWidgets.QLabel("稳定处理")
        stabilize_desc.setAlignment(QtCore.Qt.AlignCenter)
        stabilize_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        denoise_layout = QtWidgets.QVBoxLayout()
        denoise_layout.addWidget(denoise_label)
        denoise_layout.addWidget(denoise_desc)

        enhance_layout = QtWidgets.QVBoxLayout()
        enhance_layout.addWidget(enhance_label)
        enhance_layout.addWidget(enhance_desc)

        stabilize_layout = QtWidgets.QVBoxLayout()
        stabilize_layout.addWidget(stabilize_label)
        stabilize_layout.addWidget(stabilize_desc)

        steps_layout.addLayout(denoise_layout)
        steps_layout.addLayout(enhance_layout)
        steps_layout.addLayout(stabilize_layout)

        QVBoxLayout_Content.addLayout(steps_layout)

        # 处理进度和质量指标
        progress_widget = QtWidgets.QWidget()
        progress_widget.setFixedHeight(50)
        progress_layout = QtWidgets.QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(5, 2, 5, 2)

        # 进度条
        progress_bar = QtWidgets.QProgressBar()
        progress_bar.setFixedHeight(8)
        progress_bar.setRange(0, 100)
        progress_bar.setValue(89)
        progress_bar.setTextVisible(False)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #FFD700;
                border-radius: 3px;
                background-color: rgba(0, 0, 0, 0.1);
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #FFD700, stop:1 #FFA500);
                border-radius: 0px;
            }
        """)

        # 性能指标
        metrics_layout = QtWidgets.QHBoxLayout()

        processing_speed = QtWidgets.QLabel("处理速度: 45帧/秒")
        processing_speed.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        quality_score = QtWidgets.QLabel("质量评分: 94.2%")
        quality_score.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        cpu_usage = QtWidgets.QLabel("CPU使用: 68%")
        cpu_usage.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        metrics_layout.addWidget(processing_speed)
        metrics_layout.addWidget(quality_score)
        metrics_layout.addStretch()
        metrics_layout.addWidget(cpu_usage)

        progress_layout.addWidget(progress_bar)
        progress_layout.addLayout(metrics_layout)

        QVBoxLayout_Content.addWidget(progress_widget)

        # 底部算法状态
        algorithm_status = QtWidgets.QWidget()
        algorithm_status.setFixedHeight(25)
        algorithm_layout = QtWidgets.QHBoxLayout(algorithm_status)
        algorithm_layout.setContentsMargins(5, 3, 5, 3)

        # 当前算法
        current_algorithm = QtWidgets.QLabel("算法: 自适应降噪v3.2")
        current_algorithm.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 处理延迟
        processing_delay = QtWidgets.QLabel("延迟: <25ms")
        processing_delay.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        algorithm_layout.addWidget(current_algorithm)
        algorithm_layout.addStretch()
        algorithm_layout.addWidget(processing_delay)

        QVBoxLayout_Content.addWidget(algorithm_status)

        return __QLabel

    def Set_Video_Enhancement(self, __QLabel):
        """视频增益功能模块 - 仅展示"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 增益状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # 增益处理信息
        # enhancement_info = QtWidgets.QLabel("智能增益优化中...")
        # enhancement_info.setAlignment(QtCore.Qt.AlignCenter)
        # enhancement_info.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        algorithm_info = QtWidgets.QLabel("自适应亮度 + 对比度增强 + HDR处理")
        algorithm_info.setAlignment(QtCore.Qt.AlignCenter)
        algorithm_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(enhancement_info)
        status_layout.addWidget(algorithm_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # 增益参数统计
        params_layout = QtWidgets.QHBoxLayout()

        # 亮度增益
        brightness_label = QtWidgets.QLabel("+15%")
        brightness_label.setAlignment(QtCore.Qt.AlignCenter)
        brightness_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        brightness_desc = QtWidgets.QLabel("亮度增益")
        brightness_desc.setAlignment(QtCore.Qt.AlignCenter)
        brightness_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 对比度增强
        contrast_label = QtWidgets.QLabel("+22%")
        contrast_label.setAlignment(QtCore.Qt.AlignCenter)
        contrast_label.setStyleSheet("color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        contrast_desc = QtWidgets.QLabel("对比度")
        contrast_desc.setAlignment(QtCore.Qt.AlignCenter)
        contrast_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 饱和度调整
        saturation_label = QtWidgets.QLabel("+8%")
        saturation_label.setAlignment(QtCore.Qt.AlignCenter)
        saturation_label.setStyleSheet("color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        saturation_desc = QtWidgets.QLabel("饱和度")
        saturation_desc.setAlignment(QtCore.Qt.AlignCenter)
        saturation_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        brightness_layout = QtWidgets.QVBoxLayout()
        brightness_layout.addWidget(brightness_label)
        brightness_layout.addWidget(brightness_desc)

        contrast_layout = QtWidgets.QVBoxLayout()
        contrast_layout.addWidget(contrast_label)
        contrast_layout.addWidget(contrast_desc)

        saturation_layout = QtWidgets.QVBoxLayout()
        saturation_layout.addWidget(saturation_label)
        saturation_layout.addWidget(saturation_desc)

        params_layout.addLayout(brightness_layout)
        params_layout.addLayout(contrast_layout)
        params_layout.addLayout(saturation_layout)

        QVBoxLayout_Content.addLayout(params_layout)

        # 处理效果评估
        evaluation_widget = QtWidgets.QWidget()
        evaluation_widget.setFixedHeight(60)
        evaluation_widget.setStyleSheet("background: rgba(0, 0, 0, 0.05); border-radius: 4px;")

        evaluation_layout = QtWidgets.QVBoxLayout(evaluation_widget)
        evaluation_layout.setContentsMargins(8, 5, 8, 5)

        # 效果指标
        effect_stats = QtWidgets.QHBoxLayout()

        # 清晰度提升
        clarity_info = QtWidgets.QLabel("清晰度: +28%")
        clarity_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 噪点抑制
        noise_info = QtWidgets.QLabel("噪点: -45%")
        noise_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 色彩还原
        color_info = QtWidgets.QLabel("色彩: 96.8%")
        color_info.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        effect_stats.addWidget(clarity_info)
        effect_stats.addWidget(noise_info)
        effect_stats.addWidget(color_info)

        # # 综合评分
        # overall_score = QtWidgets.QLabel("视觉质量综合评分: 9.2/10")
        # overall_score.setAlignment(QtCore.Qt.AlignCenter)
        # overall_score.setStyleSheet("color: #1E90FF; font-size: 10px; font-weight: bold; background: transparent;")

        # # 适用场景
        # scene_info = QtWidgets.QLabel("当前优化场景: 低光环境 | 室内监控")
        # scene_info.setAlignment(QtCore.Qt.AlignCenter)
        # scene_info.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")
        #
        # evaluation_layout.addLayout(effect_stats)
        # # evaluation_layout.addWidget(overall_score)
        # evaluation_layout.addWidget(scene_info)
        #
        # QVBoxLayout_Content.addWidget(evaluation_widget)

        # 底部处理状态
        processing_status = QtWidgets.QWidget()
        processing_status.setFixedHeight(25)
        processing_layout = QtWidgets.QHBoxLayout(processing_status)
        processing_layout.setContentsMargins(5, 5, 5, 5)

        # 处理帧数
        processed_frames = QtWidgets.QLabel("已处理: 8,564帧")
        processed_frames.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # # GPU使用率
        # gpu_usage = QtWidgets.QLabel("GPU: 72%")
        # gpu_usage.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        # 实时性能
        realtime_perf = QtWidgets.QLabel("实时率: 98.5%")
        realtime_perf.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        processing_layout.addWidget(processed_frames)
        processing_layout.addStretch()
        # processing_layout.addWidget(gpu_usage)
        processing_layout.addStretch()
        processing_layout.addWidget(realtime_perf)

        QVBoxLayout_Content.addWidget(processing_status)

        return __QLabel

    def Set_Video_AI_Tools(self, __QLabel):
        """视频AI工具功能模块 - 仅展示"""
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # AI工具状态显示区域
        status_widget = QtWidgets.QWidget()
        status_widget.setFixedHeight(40)
        status_widget.setStyleSheet("background: rgba(0, 0, 0, 0.1); border-radius: 4px;")

        status_layout = QtWidgets.QVBoxLayout(status_widget)
        status_layout.setContentsMargins(10, 5, 10, 5)

        # AI处理信息
        # ai_info = QtWidgets.QLabel("多模型AI分析引擎运行中...")
        # ai_info.setAlignment(QtCore.Qt.AlignCenter)
        # ai_info.setStyleSheet("color: #00FF88; font-size: 12px; font-weight: bold; background: transparent;")

        models_info = QtWidgets.QLabel("YOLO + ResNet + Transformer + GAN")
        models_info.setAlignment(QtCore.Qt.AlignCenter)
        models_info.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # status_layout.addWidget(ai_info)
        status_layout.addWidget(models_info)

        QVBoxLayout_Content.addWidget(status_widget)

        # AI工具模块统计
        tools_layout = QtWidgets.QHBoxLayout()

        # 超分辨率
        super_res_label = QtWidgets.QLabel("启用")
        super_res_label.setAlignment(QtCore.Qt.AlignCenter)
        super_res_label.setStyleSheet("color: #00FF88; font-size: 16px; font-weight: bold; background: transparent;")
        super_res_desc = QtWidgets.QLabel("超分辨率")
        super_res_desc.setAlignment(QtCore.Qt.AlignCenter)
        super_res_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 智能填充
        inpainting_label = QtWidgets.QLabel("启用")
        inpainting_label.setAlignment(QtCore.Qt.AlignCenter)
        inpainting_label.setStyleSheet("color: #FFD700; font-size: 16px; font-weight: bold; background: transparent;")
        inpainting_desc = QtWidgets.QLabel("智能填充")
        inpainting_desc.setAlignment(QtCore.Qt.AlignCenter)
        inpainting_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 风格转换
        style_transfer_label = QtWidgets.QLabel("待机")
        style_transfer_label.setAlignment(QtCore.Qt.AlignCenter)
        style_transfer_label.setStyleSheet(
            "color: #FF6347; font-size: 16px; font-weight: bold; background: transparent;")
        style_transfer_desc = QtWidgets.QLabel("风格转换")
        style_transfer_desc.setAlignment(QtCore.Qt.AlignCenter)
        style_transfer_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 布局组织
        super_res_layout = QtWidgets.QVBoxLayout()
        super_res_layout.addWidget(super_res_label)
        super_res_layout.addWidget(super_res_desc)

        inpainting_layout = QtWidgets.QVBoxLayout()
        inpainting_layout.addWidget(inpainting_label)
        inpainting_layout.addWidget(inpainting_desc)

        style_layout = QtWidgets.QVBoxLayout()
        style_layout.addWidget(style_transfer_label)
        style_layout.addWidget(style_transfer_desc)

        tools_layout.addLayout(super_res_layout)
        tools_layout.addLayout(inpainting_layout)
        tools_layout.addLayout(style_layout)

        QVBoxLayout_Content.addLayout(tools_layout)

        # AI处理性能指标
        performance_widget = QtWidgets.QWidget()
        performance_widget.setFixedHeight(60)
        performance_widget.setStyleSheet("background: rgba(0, 0, 0, 0.05); border-radius: 4px;")

        performance_layout = QtWidgets.QVBoxLayout(performance_widget)
        performance_layout.setContentsMargins(8, 5, 8, 5)

        # 性能指标
        perf_stats = QtWidgets.QHBoxLayout()

        # 推理速度
        inference_speed = QtWidgets.QLabel("推理: 15ms/帧")
        inference_speed.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        # 内存使用
        memory_usage = QtWidgets.QLabel("显存: 4.2GB")
        memory_usage.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        # 模型精度
        model_accuracy = QtWidgets.QLabel("精度: 97.8%")
        model_accuracy.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        perf_stats.addWidget(inference_speed)
        perf_stats.addWidget(memory_usage)
        perf_stats.addWidget(model_accuracy)

        # # 当前任务
        # current_task = QtWidgets.QLabel("当前任务: 4K视频超分辨率重建")
        # current_task.setAlignment(QtCore.Qt.AlignCenter)
        # current_task.setStyleSheet("color: #1E90FF; font-size: 12px; font-weight: bold; background: transparent;")
        #
        # # AI模型状态
        # model_status = QtWidgets.QLabel("模型版本: v2.1.3 | 最后训练: 2024-01-15")
        # model_status.setAlignment(QtCore.Qt.AlignCenter)
        # model_status.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")
        #
        # performance_layout.addLayout(perf_stats)
        # performance_layout.addWidget(current_task)
        # performance_layout.addWidget(model_status)
        #
        # QVBoxLayout_Content.addWidget(performance_widget)

        # 底部AI状态
        ai_status = QtWidgets.QWidget()
        ai_status.setFixedHeight(25)
        ai_layout = QtWidgets.QHBoxLayout(ai_status)
        ai_layout.setContentsMargins(5, 5, 5, 5)

        # 队列任务
        queue_tasks = QtWidgets.QLabel("队列任务: 23个")
        queue_tasks.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        # 处理进度
        processing_progress = QtWidgets.QLabel("总进度: 67%")
        processing_progress.setStyleSheet("color: #FFD700; font-size: 12px; background: transparent;")

        # 预计完成时间
        estimated_time = QtWidgets.QLabel("预计完成: 15分钟")
        estimated_time.setStyleSheet("color: #00FF88; font-size: 12px; background: transparent;")

        ai_layout.addWidget(queue_tasks)
        ai_layout.addStretch()
        ai_layout.addWidget(processing_progress)
        ai_layout.addStretch()
        ai_layout.addWidget(estimated_time)

        QVBoxLayout_Content.addWidget(ai_status)

        return __QLabel

    def Toggle_Right(self):
        total_width = self.QSplitter_HBox.width()
        if self.right_expanded:
            # 收起右侧
            self.QPropertyAnimation_Right.setStartValue([total_width - 100, 100])
            self.QPropertyAnimation_Right.setEndValue([total_width, 0])
            self.QPropertyAnimation_Right.finished.connect(lambda: self.QLabel_Left.hide())
            self.QSplitter_HBox.setSizes([self.width(), 0])  # 初始右侧宽度 0
            self.right_expanded = False
        else:
            # 展开右侧
            self.QLabel_Left.show()
            self.QPropertyAnimation_Right.setStartValue([total_width, 0])
            self.QPropertyAnimation_Right.setEndValue([total_width - 100, 100])
            self.QSplitter_HBox.setSizes([self.width(), 300])  # 初始右侧宽度 0
            self.right_expanded = True

        # 断开之前的 finished 信号
        # try:
        #     self.anim_right.finished.disconnect()
        # except:
        #     pass
        self.QPropertyAnimation_Right.start()

    def Toggle_Bottom(self):
        total_height = self.QSplitter_Root.height()
        if self.bottom_expanded:
            # 收起底部
            self.QPropertyAnimation_Bottom.setStartValue([total_height - 100, 100])
            self.QPropertyAnimation_Bottom.setEndValue([total_height, 0])
            self.QPropertyAnimation_Bottom.finished.connect(lambda: self.QLabel_Bottom.hide())
            self.QPropertyAnimation_Bottom.setSizes([self.height(), 0])
            self.bottom_expanded = False
        else:
            # 展开底部
            self.QLabel_Bottom.show()
            self.QPropertyAnimation_Bottom.setStartValue([total_height, 0])
            self.QPropertyAnimation_Bottom.setEndValue([total_height - 100, 100])
            self.QSplitter_Root.setSizes([self.height(), 400])
            self.bottom_expanded = True

        # 断开之前的 finished 信号
        # try:
        #     self.anim_bottom.finished.disconnect()
        # except:
        #     pass
        self.QPropertyAnimation_Bottom.start()



    def Page_Update(self):
       pass









    def PAGE_HANDLER_EXECUTE(self, CommandParameter):



        if "Self_" in CommandParameter["Type"]:
            PP(CommandParameter)
            self.Open_Processing(CommandParameter)

        else:

            self.Signal_Result.emit(CommandParameter)  # 发送信号并传递数据


    def Open_Processing(self,ProcessingParameter):
        PP(ProcessingParameter)
        # {"Command": "Media_PlayerConfig", "Channel_ID": self.Channel_Info["Channel_ID"]})

        # PopupDialog = PopupParameter["Command"]


        # __Component_Processing_EQ  =Component_Processing_EQ.Component_Processing_EQ()
        # __Component_Processing_EQ.show()
        # Dialog_Type, Dialog_Name = (f"_{Parts[0]}", Parts[1]) if (Parts := PopupDialog.split("_")) and len(Parts) > 1 else ("", PopupDialog)
        exec(f"QLabel_Component= Component_{ProcessingParameter['Processing']}.Component_{ProcessingParameter['Processing']}(self,ProcessingParameter)")
        exec("QLabel_Component.Signal_Result.connect(self.PAGE_HANDLER_EXECUTE)")
        exec("QLabel_Component.move(self.geometry().center() - QLabel_Component.rect().center())")
        exec("QLabel_Component.show()")








class QLabel_Function(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:self.QLabel_Info = args[0]
        except:pass

        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        # QLabel_Icon.setGeometry(0,0,18, 18)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        # Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        # QLabel_Icon.setPixmap(Image_Logo)

        Icon_List = {

            "视频播放": "video_playback.png",
            "iERM 智能体分析": "agent.png",
            "目标检测与追踪": "object_detection.png",
            "特征处理": "feature.png",
            "行为分析": "behavioral.png",
            "人脸特征检测": "facial_feature.png",
            "视频采集": "video_capture.png",
            "视频预处理": "preprocessing.png",
            "视频增益": "video_gain.png",
            "视频AI工具": "video_AI.png",


        }


        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.QLabel_Info.get("Title_Name", "")

        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)
        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        QLabel_Title_Name.setText(self.QLabel_Info["Title_Name"])

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")


        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)




class QLabel_Video_Analysis(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.QWidget_Parent = parent
        try:
            self.Channel_Info = args[0]
            self.Control_Info = {
                "Channel_ID": self.Channel_Info.get("Channel_ID", ""),
                # "Drawer_Type": "",
                # "Drawer_Title": ""
            }
        except:
            pass
        # self.CHANNEL_PLAY_SOURCE = Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"]["Video_Source"]
        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 8, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        # QLabel_Icon.setGeometry(0,0,18, 18)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        QLabel_Icon.setPixmap(Image_Logo)

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        # QLabel_Title_Name.setText(self.Channel_Info["Title_Name"])

        self.QLabel_Title_Menu = QtWidgets.QLabel()
        # self.QLabel_Title_Menu.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.QLabel_Title_Menu.setVisible(False)
        QHBoxLayout_Menu = QtWidgets.QHBoxLayout(self.QLabel_Title_Menu)
        QHBoxLayout_Menu.setAlignment( QtCore.Qt.AlignRight)
        QHBoxLayout_Menu.setSpacing(8)
        QHBoxLayout_Menu.setContentsMargins(13,3, 14, 3)

        # Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)

        self.menu_button = QtWidgets.QPushButton()
        self.menu_button.setIcon(qta.icon('fa6s.display', color='white', ))
        self.menu_button.setFixedSize(38, 15)
        self.menu_button.setStyleSheet("""QPushButton { background-color: transparent;border: none; }QPushButton::menu-indicator {image: none;width: 0;height: 0;}QPushButton:hover {
                                                                   background-color: rgba(0, 100, 150, 150);border-radius: 3px;
                                                               }""")
        # 创建一个菜单
        Menu_Box = QtWidgets.QMenu(self)  # 第一个参数是菜单的标题，第二个参数是父对象
        Menu_Box.setStyleSheet("""
                    QMenu {
                        background-color: rgba(0, 0, 0, 0.3);
                        border: 0px solid rgba(255, 255, 255, 0.2);
                        border-radius: 8px;
                        color: white; /* 设置菜单项文字颜色为白色 */
                    }
                    QMenu::item {
                        padding: 8px 16px;
                        background-color: transparent;
                    }
                    QMenu::item:selected {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                    QMenu::separator {
                        height: 1px;
                        background: rgba(255, 255, 255, 0.2);
                    }
                    QMenu::indicator {
                        width: 0px;
                        height: 0px;
                        image: none;
                        subcontrol-position: right;
                    }
                """)
        # Menu_Box.resize(68, 68)
        Menu_Box.setIcon(qta.icon('ri.menu-line',color='white',options=[{'scale_factor': 1.2}]))

        Menu_List = [
            {"Name": "超高清(1920*1080)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 1},
            {"Name": "高清(720*576)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},
            {"Name": "标清(640*480)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},
            {"Name": "省流(480*320)", "Command": "Page_Change", "Icon": "ei.list-alt", "Page_Index": 4},

        ]

        for Menu in Menu_List:

                exec("Action_%s = QtGui.QAction(Menu['Name'], self)" % Menu['Command'])
                exec("Menu_Box.addAction(Action_%s)" % Menu['Command'])

        # 将菜单添加到菜单栏中
        # QMenuBar_Menu.addMenu(Menu_Box)
        self.menu_button.setMenu(Menu_Box)
        QHBoxLayout_Menu.addWidget( self.menu_button)








        Menu_List=[
            {"Menu_Name":"Play",         "Menu_Icon":"fa6.circle-play","Menu_Size":18,          "Menu_Command":{"Command":"Play"}},
            {"Menu_Name":"Save",         "Menu_Icon":"mdi.database-arrow-down","Menu_Size":18,  "Menu_Command":{"Command":"Save"}},
            {"Menu_Name":"ScreenShot",   "Menu_Icon":"fa5s.camera","Menu_Size":18,              "Menu_Command":{"Command":"ScreenShot"}},
            {"Menu_Name":"Chat",         "Menu_Icon":"fa6s.walkie-talkie","Menu_Size":18,       "Menu_Command":{"Command":"Chat"}},
            {"Menu_Name":"Voice",        "Menu_Icon":"ri.volume-mute-fill","Menu_Size":18,      "Menu_Command":{"Command":"Voice"}},
            {"Menu_Name": "Control",      "Menu_Icon": "mdi.arrow-all", "Menu_Size": 18,         "Menu_Command": {"Command": "Control"}},
            {"Menu_Name":"More",         "Menu_Icon":"ri.more-fill","Menu_Size":18,             "Menu_Command":{"Command":"More"}},
            # {"Menu_Icon":"mdi.stop-circle-outline","Menu_Size":18,"Menu_Command":{"Command":"Player"}},
        ]

        StyleSheet_QLabel = """
                                                               QLabel {
                                                                   background-color:rgba(40, 52, 80, 0.1);
                                                                   border: 0px solid rgba(0, 180, 255, 60);
                                                                   border-radius: 3px;
                                                                   padding: 0px;
                                                                   font-size:13px;
                                                                   font-weight: bold;
                                                                   color:white;
                                                               }
                                                               QLabel:hover {
                                                                   background-color: rgba(0, 100, 150, 150);
                                                               }
                                                           """
        # Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]={}





        def Set_Menu(Menu_Info):
            Icon = qtawesome.icon(Menu_Info["Menu_Icon"], color='white', size=Menu_Info["Menu_Size"])
            QLabel_Menu = Component_Common.Component_Common_QLabel_Click()
            QLabel_Menu.setFixedSize(38, 20)
            QLabel_Menu.setStyleSheet(StyleSheet_QLabel)
            QLabel_Menu.setPixmap(Icon.pixmap(18, 18))
            QLabel_Menu.clicked.connect(lambda :self.Menu_Command(Menu_Info["Menu_Command"]))
            # Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"][Menu_Info["Menu_Name"]] = QLabel_Menu
            # Page_Info["Page_Element_List"][f"Channel_Menu_{self.Control_Info['Channel_ID']}"][Menu_Info["Menu_Name"]] = QLabel_Menu

            return QLabel_Menu


        for Menu_Info in Menu_List:
            QHBoxLayout_Menu.addWidget(Set_Menu(Menu_Info))







        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 2)


        # btn_layout = QtWidgets.QHBoxLayout()
        QHBoxLayout_Title.addStretch()
        QHBoxLayout_Title.addWidget(self.QLabel_Title_Menu,1)





        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")




        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)
        __QVBoxLayout.addWidget(self.Set_Bottom())



    def Set_Bottom(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        __QLabel.setMinimumHeight(30)
        __QLabel.setMaximumHeight(30)
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QLabel_Status = QtWidgets.QLabel()
        self.QLabel_Status.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status.hide()

        self.QHBoxLayout_Status = QtWidgets.QHBoxLayout(self.QLabel_Status)
        self.QHBoxLayout_Status.setAlignment(QtCore.Qt.AlignLeft)
        self.QHBoxLayout_Status.setSpacing(0)
        self.QHBoxLayout_Status.setContentsMargins(8, 0, 0, 0)

        # Page_Info["Page_Channel_List"]["Channel_1"]["Video_Source"]
        # PP(self.Channel_Info)

        self.CHANNEL_PLAY_SOURCE="-------"
        self.QLabel_Status_Source = QtWidgets.QLabel()
        self.QLabel_Status_Source.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status_Source.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Status_Source.setText(
            f'''<div style="text-align:center;">
            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">视频源：{self.CHANNEL_PLAY_SOURCE}</span>
            </div>'''
        )
        # 标记各通道播放源
        # Page_Info["Page_Element_List"][f"QLabel_Status_Source_{self.Channel_Info['Channel_ID']}"] = self.QLabel_Status_Source
        self.QHBoxLayout_Status.addWidget(self.QLabel_Status_Source)



        __QHBoxLayout.addWidget(self.QLabel_Status)

        return __QLabel






    def Menu_Command(self,Command):
        PP(("ReSet",Command))
        #
        # match Command["Command"]:
        #     # case "Play":
        #     #     Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
        #     #     Icon = qtawesome.icon("fa5.stop-circle", color='red', size=18)
        #     #     Menu_Play.setPixmap(Icon.pixmap(18, 18))
        #     #     try:Menu_Play.clicked.disconnect()
        #     #     except:pass
        #     #     Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Stop"}))
        #     #
        #     #     # Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source":r"D:\45M.mp4"}
        #     #     Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source":"https://42.193.47.16:443/rtp/34020000001320000001_34020000001320000001.live.flv?originTypeStr=rtp_push"}
        #     #     Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Play()
        #     case "Play":
        #         Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
        #         Icon = qtawesome.icon("fa5.stop-circle", color='red', size=18)
        #         Menu_Play.setPixmap(Icon.pixmap(18, 18))
        #         try:Menu_Play.clicked.disconnect()
        #         except:pass
        #         Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Stop"}))
        #
        #         # 获取当前通道的视频源，如果没有设置则使用默认值
        #         current_video_source = Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"].get("Video_Source", "未知")
        #
        #         # 如果视频源是"未知"，使用默认的测试视频源
        #         if current_video_source == "未知":
        #             current_video_source = "https://42.193.47.16:443/rtp/34020000001320000001_34020000001320000001.live.flv?originTypeStr=rtp_push"
        #             Page_Info["Page_Channel_List"][f"Channel_{self.Channel_Info['Channel_ID']}"] = {"Video_Source": current_video_source}
        #
        #         Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Play()
        #
        #
        #
        #         # PP(Command*8)


        self.QWidget_Parent.Toggle_Right()
        self.QWidget_Parent.Toggle_Bottom()

            # case "Stop":
            #     Menu_Play = Page_Info["Page_Element_List"][f"Channel_Menu_{self.Channel_Info['Channel_ID']}"]["Play"]
            #     Icon = qtawesome.icon("fa6.circle-play", color='white', size=18)
            #     Menu_Play.setPixmap(Icon.pixmap(18, 18))
            #     try:Menu_Play.clicked.disconnect()
            #     except:pass
            #     Menu_Play.clicked.connect(lambda: self.Menu_Command({"Command":"Play"}))
            #     # PP(Command*8)
            #
            #
            #     Page_Info["Page_Element_List"][f"QLabel_Video_Channel_{self.Channel_Info['Channel_ID']}"].Video_Stop()
            #
            #
            # case "More":
            #     PP("More")
            #     self.Channel_Info["Drawer_Type"]  = "Config_Channel"
            #     self.Channel_Info["Drawer_Title"] = "视频通道设置"
            #     Page_Info["Page_Def_List"]["Set_DrawerSetting"](self.Channel_Info)
            #
            # case "Control":
            #     PP("Control")
            #     self.Control_Info["Drawer_Type"]  = "Config_Control"
            #     self.Control_Info["Drawer_Title"] = "云台控制视频"
            #     # self.Control_Info["Channel_ID"] = self.Channel_Info["Channel_ID"]
            #     Page_Info["Page_Def_List"]["Set_DrawerSetting"](self.Control_Info)
            #
            #
            # case _:print("unknown")











    # 鼠标事件
    def enterEvent(self, event):
        self.QLabel_Title_Menu.setVisible(True)
        self.QLabel_Status.setVisible(True)


        super().enterEvent(event)

    def leaveEvent(self, event):
        self.QLabel_Title_Menu.setVisible(False)
        self.QLabel_Status.setVisible(False)

        super().leaveEvent(event)


class QLabel_Video_Show(QtWidgets.QLabel):
    Clicked_More = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__(parent)
        self.Channel_Info = args[0]
        self.setMouseTracking(True)
        self._is_hovered = False
        self._is_Recode = False
        self.setAlignment(QtCore.Qt.AlignCenter)

        self.initUI()

    def initUI(self):
        self.__QVBoxLayout = QtWidgets.QVBoxLayout(self)
        self.__QVBoxLayout.setAlignment(QtCore.Qt.AlignCenter)
        self.__QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.__QVBoxLayout.setSpacing(0)



        # self.QLabel_Play_Status.clicked.connect(lambda :Page_Info["Page_Def_List"]["Set_PopupDialog"]("Normal"))
        # self.QLabel_Play_Status.clicked.connect(self.Play_Video)

        # Page_Info["Page_Def_List"]["Set_PopupDialog"]


        self.__QVBoxLayout.addWidget(self.Status_Default())

    def Status_Default(self):

        StyleSheet_QLabel = """
                    QLabel {
                        background-color: rgba(40, 52, 80, 0.13);
                        border: 0px solid rgba(0, 180, 255, 60);
                        border-radius: 4px;
                        color:rgba(0, 255, 136, 255);
                    }
                    QLabel:hover {
                        background-color: rgba(0, 100, 150, 150);
                    }
                """
        self.QLabel_Play_Status = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Play_Status.setMinimumSize(120, 60)
        self.QLabel_Play_Status.setMaximumSize(120, 60)
        self.QLabel_Play_Status.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Play_Status.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Play_Status.setText(
            f'''<div style="text-align:center;">
            <span style="font-size:13pt;font-weight: bold">源视频</span><br>
            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">No Signal</span>
            </div>'''
        )
        # self.QLabel_Play_Status.clicked.connect(lambda :Page_Info["Page_Def_List"]["Set_PopupDialog"]("Play"))
        # self.QLabel_Play_Status.clicked.connect(lambda: Page_Info["Page_Def_List"]["Set_PopupDialog"]({"Command": "Media_PlayerConfig", "Channel_ID": self.Channel_Info["Channel_ID"]}))

        return self.QLabel_Play_Status


    def Video_Play(self):

        self.Count = 9
        self.Set_Loading()


    def Set_Loading(self):
        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        self.QProgressBar_Loading = QtWidgets.QProgressBar()
        self.QProgressBar_Loading.setFixedSize(188,12)
        # self.progress_bar.setRange(0, 100)
        # self.progress_bar.setValue(0)
        # self.QProgressBar_Loading.setTextVisible(False)  # 隐藏默认文本
        self.QProgressBar_Loading.setRange(0, 0)  # 隐藏默认文本
        self.QProgressBar_Loading.setStyleSheet("""
                    QProgressBar {
                        border: 1px solid #4cc9f0;
                        border-radius: 3px;
                        background-color: rgba(0, 0, 0, 0.1);
                        height: 10px;
                    }
                    QProgressBar::chunk {
                        background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4cc9f0, stop:1 #0077cc);
                        border-radius: 0px;
                    }
                """)
        self.__QVBoxLayout.addWidget(self.QProgressBar_Loading)



        self.QTimer_Player = QtCore.QTimer(self)
        self.QTimer_Player.setSingleShot(True)  # 设置为单次触发
        self.QTimer_Player.timeout.connect(self.Execute_Play)  # 连接到要执行的函数
        self.QTimer_Player.start(5000)



        # self.Eexecute_Play()

        # QtCore.QTimer.singleShot(5000, lambda: self.Execute_Play())

    def Execute_Play(self):

        Video_Info = Page_Info["Page_Channel_List"][f'Channel_{self.Channel_Info["Channel_ID"]}']
        self.__Component_VideoPlay = Component_VideoPlay.Component_VideoPlay(Video_Info)

        Page_Info["Page_Element_List"][f"QLabel_Status_Source_{self.Channel_Info['Channel_ID']}"].setText(
                f'''<div style="text-align:center;">
                <span style="font-size:8pt;color:#1E90FF;font-weight: bold">视频源：{Video_Info["Video_Source"]}</span>
                </div>'''
        )

        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        self.__QVBoxLayout.addWidget( self.__Component_VideoPlay)

    def Video_Stop(self):
        try: self.QTimer_Player.stop()
        except:pass
        while self.__QVBoxLayout.count():
            item = self.__QVBoxLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.__QVBoxLayout.addWidget(self.Status_Default())




    # 鼠标事件
    def enterEvent(self, event):

        super().enterEvent(event)

    def leaveEvent(self, event):

        super().leaveEvent(event)









if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Analysis()
    Page_Widget.show()
    sys.exit(app.exec())
