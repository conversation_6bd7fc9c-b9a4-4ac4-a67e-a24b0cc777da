# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.UtilsCenter import *
import numpy as np
import math

Page_Info={
    "Title":"哨兵视频监控主页",
    "Param": {},

}

from Bin.System.OS.Component import Component_Common

PP(Page_Info)


# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_Media_VideoHome(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass

        Page_Info["Page_Element_List"]={}
        Page_Info["StyleSheet"]={}
        Page_Info["StyleSheet"]["Value_Color"] ="rgba(0, 255, 136, 255)"

        # 初始化数据存储
        self.init_data()
        self.initUI()

    def init_data(self):
        """初始化数据存储"""
        # 系统监控数据
        self.system_data = {
            "cpu_history": [15, 18, 22, 19, 25, 30, 28, 24, 20, 15],  # CPU使用率历史
            "memory_history": [45, 48, 52, 49, 55, 60, 58, 54, 50, 45],  # 内存使用率历史
            "network_in": [120, 135, 150, 140, 160, 180, 170, 155, 145, 130],  # 网络入流量 MB/s
            "network_out": [80, 85, 95, 90, 100, 110, 105, 95, 85, 75],  # 网络出流量 MB/s
            "storage_used": 68,  # 存储使用率
            "storage_total": 2048,  # 总存储 GB
            "storage_free": 655,  # 剩余存储 GB
        }

        # 国标设备数据
        self.device_data = {
            "total_devices": 156,  # 总设备数
            "online_devices": 142,  # 在线设备数
            "offline_devices": 14,  # 离线设备数
            "total_channels": 624,  # 总通道数
            "active_channels": 589,  # 活跃通道数
            "inactive_channels": 35,  # 非活跃通道数
        }

        # 告警数据
        self.alarm_data = {
            "today_alarms": 23,  # 今日告警
            "critical_alarms": 3,  # 严重告警
            "warning_alarms": 12,  # 警告告警
            "info_alarms": 8,  # 信息告警
            "alarm_trend": [5, 8, 12, 15, 18, 23, 20, 17, 14, 11],  # 告警趋势
        }

    def initUI(self):
        self.Set_Content()


    def Set_Content(self):
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(3, 3, 3, 3)

        self.QSplitter_Root = QtWidgets.QSplitter(Qt.Vertical)
        # 上部水平分割器（左 + 右）
        self.QSplitter_HBox= QtWidgets.QSplitter(Qt.Horizontal)

        self.QLabel_Right = QtWidgets.QLabel()
        self.QLabel_Right.setStyleSheet("background:#000;color:#fff;")
        self.QLabel_Right.setMinimumWidth(300)  # 确保最小宽度 100px

        # 添加到水平分割器 - 只显示系统状态监控，占据整个第一行
        self.QSplitter_HBox.addWidget(self.Set_Right())
        # 不添加左侧区域，让系统状态监控占据整个宽度

        # 添加到垂直分割器
        self.QSplitter_Root.addWidget(self.QSplitter_HBox)
        self.QSplitter_Root.addWidget(self.Set_Bottom())
        self.QSplitter_Root.setSizes([300, 0])  # 初始底部高度 0

        # 主布局
        __QVBoxLayout.addWidget(self.QSplitter_Root)

        # 动画（右侧）
        self.QPropertyAnimation_Right = QtCore.QPropertyAnimation(self.QSplitter_HBox, b"sizes")
        self.right_expanded = False

        # 动画（底部）
        self.QPropertyAnimation_Bottom = QtCore.QPropertyAnimation(self.QSplitter_Root, b"sizes")
        self.bottom_expanded = False

        self.Toggle_Right()
        self.Toggle_Bottom()

    def Set_Left(self):
        # 左侧区域 - 不显示任何内容
        self.QLabel_Left = QtWidgets.QLabel()
        self.QLabel_Left.setStyleSheet("background:transparent;")
        self.QLabel_Left.hide()  # 隐藏左侧区域
        return self.QLabel_Left

    def Set_Right(self):
        # 系统状态监控区域 - 现在占据整个第一行，不需要滚动
        self.QLabel_Right = QLabel_Function({"Title_Name":"系统状态监控"})
        self.QLabel_Right.setStyleSheet("background:rgba(18, 27, 53, 255)")

        # 直接创建内容容器，不使用滚动区域
        content_widget = QtWidgets.QWidget()
        content_layout = QtWidgets.QGridLayout(content_widget)
        content_layout.setSpacing(8)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # 设置系统状态信息 - 使用网格布局
        self.Set_System_Status_Grid(content_layout)

        # 主布局
        main_layout = QtWidgets.QVBoxLayout(self.QLabel_Right.QLabel_Content)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(content_widget)

        return self.QLabel_Right

    def Set_System_Status_Grid(self, layout):
        """设置系统状态网格布局 - 3行布局"""
        # 第1行：时间显示（跨越整行）
        self.QLabel_Show_Time = QtWidgets.QLabel()
        self.QLabel_Show_Time.setFixedHeight(40)  # 紧凑的时间显示
        self.QLabel_Show_Time.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 6px;
            }
        """)
        self.QLabel_Show_Time.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Show_Time.setText(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))
        layout.addWidget(self.QLabel_Show_Time, 0, 0, 1, 4)  # 跨越4列

        # 第2行：设备统计信息（跨越整行）
        device_stats_widget = self.Create_Device_Stats_Widget()
        device_stats_widget.setFixedHeight(180)  
        layout.addWidget(device_stats_widget, 1, 0, 1, 4)  # 跨越4列

        # 第3行：核心监控指标（4列）
        # CPU使用率
        cpu_widget = self.Create_CPU_Chart_Widget()
        cpu_widget.setFixedHeight(180)  
        layout.addWidget(cpu_widget, 2, 0)  # 第1列

        # 内存使用率
        memory_widget = self.Create_Memory_Chart_Widget()
        memory_widget.setFixedHeight(180)  
        layout.addWidget(memory_widget, 2, 1)  # 第2列

        # 存储使用情况
        storage_widget = self.Create_Storage_Widget()
        storage_widget.setFixedHeight(180)  
        layout.addWidget(storage_widget, 2, 2)  # 第3列

        # 网络流量监控
        network_widget = self.Create_Network_Chart_Widget()
        network_widget.setFixedHeight(180)  
        layout.addWidget(network_widget, 2, 3)  # 第4列

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 1)
        layout.setColumnStretch(1, 1)
        layout.setColumnStretch(2, 1)
        layout.setColumnStretch(3, 1)

        # 设置定时器
        self.QTimer_Count = QtCore.QTimer(self)
        self.QTimer_Count.timeout.connect(self.Page_Update_Timer_Date)
        self.QTimer_Count.start(1000)  # 每秒更新一次

    def Create_Device_Stats_Widget(self):
        """创建设备统计信息组件 - 跨越整行的水平布局"""
        widget = QtWidgets.QLabel()
        widget.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 8px;
            }
        """)

        layout = QtWidgets.QHBoxLayout(widget)
        layout.setContentsMargins(20, 8, 20, 8)
        layout.setSpacing(40)

        # 在线设备
        online_layout = QtWidgets.QVBoxLayout()
        online_layout.setSpacing(5)

        online_label = QtWidgets.QLabel(f"{self.device_data['online_devices']}")
        online_label.setAlignment(QtCore.Qt.AlignCenter)
        online_label.setStyleSheet("color: #00ff88; font-size: 24px; font-weight: bold; background: transparent;")

        online_desc = QtWidgets.QLabel("在线设备")
        online_desc.setAlignment(QtCore.Qt.AlignCenter)
        online_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        online_layout.addWidget(online_label)
        online_layout.addWidget(online_desc)

        # 活跃通道
        channel_layout = QtWidgets.QVBoxLayout()
        channel_layout.setSpacing(5)

        channel_label = QtWidgets.QLabel(f"{self.device_data['active_channels']}")
        channel_label.setAlignment(QtCore.Qt.AlignCenter)
        channel_label.setStyleSheet("color: #FFB347; font-size: 24px; font-weight: bold; background: transparent;")

        channel_desc = QtWidgets.QLabel("活跃通道")
        channel_desc.setAlignment(QtCore.Qt.AlignCenter)
        channel_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        channel_layout.addWidget(channel_label)
        channel_layout.addWidget(channel_desc)

        # 在线率
        rate_layout = QtWidgets.QVBoxLayout()
        rate_layout.setSpacing(5)

        online_rate = (self.device_data['online_devices'] / self.device_data['total_devices']) * 100
        rate_label = QtWidgets.QLabel(f"{online_rate:.1f}%")
        rate_label.setAlignment(QtCore.Qt.AlignCenter)
        rate_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 24px; font-weight: bold; background: transparent;")

        rate_desc = QtWidgets.QLabel("设备在线率")
        rate_desc.setAlignment(QtCore.Qt.AlignCenter)
        rate_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        rate_layout.addWidget(rate_label)
        rate_layout.addWidget(rate_desc)

        # 总设备数
        total_layout = QtWidgets.QVBoxLayout()
        total_layout.setSpacing(5)

        total_label = QtWidgets.QLabel(f"{self.device_data['total_devices']}")
        total_label.setAlignment(QtCore.Qt.AlignCenter)
        total_label.setStyleSheet("color: #87CEEB; font-size: 24px; font-weight: bold; background: transparent;")

        total_desc = QtWidgets.QLabel("总设备数")
        total_desc.setAlignment(QtCore.Qt.AlignCenter)
        total_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        total_layout.addWidget(total_label)
        total_layout.addWidget(total_desc)

        # 添加到主布局
        layout.addLayout(online_layout)
        layout.addLayout(channel_layout)
        layout.addLayout(rate_layout)
        layout.addLayout(total_layout)

        return widget







    def Create_CPU_Chart_Widget(self):
        """创建CPU使用率图表组件"""
        widget = QtWidgets.QLabel()
        widget.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 8px;
            }
        """)

        layout = QtWidgets.QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题
        title = QtWidgets.QLabel("CPU使用率趋势")
        title.setStyleSheet("color: rgba(22, 175, 252, 255); font-weight: bold; font-size: 12px; background: transparent; border: none;")
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        # 图表区域
        chart_widget = CPUChartWidget(self.system_data['cpu_history'])
        chart_widget.setFixedHeight(60)
        layout.addWidget(chart_widget)

        # 当前值和统计信息
        stats_layout = QtWidgets.QHBoxLayout()

        current_cpu = self.system_data['cpu_history'][-1]
        current_label = QtWidgets.QLabel(f"当前: {current_cpu}%")
        current_label.setAlignment(QtCore.Qt.AlignCenter)
        current_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 14px; font-weight: bold; background: transparent;")

        avg_cpu = sum(self.system_data['cpu_history']) / len(self.system_data['cpu_history'])
        avg_label = QtWidgets.QLabel(f"平均: {avg_cpu:.1f}%")
        avg_label.setAlignment(QtCore.Qt.AlignCenter)
        avg_label.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        stats_layout.addWidget(current_label)
        stats_layout.addWidget(avg_label)
        layout.addLayout(stats_layout)

        return widget

    def Create_Memory_Chart_Widget(self):
        """创建内存使用率图表组件"""
        widget = QtWidgets.QLabel()
        widget.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 8px;
            }
        """)

        layout = QtWidgets.QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题
        title = QtWidgets.QLabel("内存使用率趋势")
        title.setStyleSheet("color: rgba(22, 175, 252, 255); font-weight: bold; font-size: 12px; background: transparent; border: none;")
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        # 图表区域
        chart_widget = MemoryChartWidget(self.system_data['memory_history'])
        chart_widget.setFixedHeight(60)
        layout.addWidget(chart_widget)

        # 当前值和统计信息
        stats_layout = QtWidgets.QHBoxLayout()

        current_memory = self.system_data['memory_history'][-1]
        current_label = QtWidgets.QLabel(f"当前: {current_memory}%")
        current_label.setAlignment(QtCore.Qt.AlignCenter)
        current_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 14px; font-weight: bold; background: transparent;")

        avg_memory = sum(self.system_data['memory_history']) / len(self.system_data['memory_history'])
        avg_label = QtWidgets.QLabel(f"平均: {avg_memory:.1f}%")
        avg_label.setAlignment(QtCore.Qt.AlignCenter)
        avg_label.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        stats_layout.addWidget(current_label)
        stats_layout.addWidget(avg_label)
        layout.addLayout(stats_layout)

        return widget

    def Create_Storage_Widget(self):
        """创建存储使用情况组件"""
        widget = QtWidgets.QLabel()
        widget.setFixedHeight(120)
        widget.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 8px;
            }
        """)

        layout = QtWidgets.QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题
        title = QtWidgets.QLabel("存储使用情况")
        title.setStyleSheet("color: rgba(22, 175, 252, 255); font-weight: bold; font-size: 12px; background: transparent; border: none;")
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        # 存储进度条
        progress_widget = StorageProgressWidget(self.system_data['storage_used'])
        layout.addWidget(progress_widget)

        # 存储详情
        used_gb = self.system_data['storage_total'] - self.system_data['storage_free']
        detail_label = QtWidgets.QLabel(f"已用: {used_gb}GB / 总计: {self.system_data['storage_total']}GB")
        detail_label.setAlignment(QtCore.Qt.AlignCenter)
        detail_label.setStyleSheet("color: #1E90FF; font-size: 10px; background: transparent;")
        layout.addWidget(detail_label)

        return widget

    def Create_Network_Chart_Widget(self):
        """创建网络流量图表组件"""
        widget = QtWidgets.QLabel()
        widget.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 8px;
            }
        """)

        layout = QtWidgets.QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题
        title = QtWidgets.QLabel("网络流量监控")
        title.setStyleSheet("color: rgba(22, 175, 252, 255); font-weight: bold; font-size: 12px; background: transparent; border: none;")
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)

        # 图表区域
        chart_widget = NetworkChartWidget(self.system_data['network_in'], self.system_data['network_out'])
        chart_widget.setFixedHeight(80)
        layout.addWidget(chart_widget)

        # 当前流量和统计信息
        stats_layout = QtWidgets.QHBoxLayout()

        current_in = self.system_data['network_in'][-1]
        current_out = self.system_data['network_out'][-1]

        # 入站流量
        in_label = QtWidgets.QLabel(f"入站: {current_in}MB/s")
        in_label.setAlignment(QtCore.Qt.AlignCenter)
        in_label.setStyleSheet("color: #00ff88; font-size: 12px; font-weight: bold; background: transparent;")

        # 出站流量
        out_label = QtWidgets.QLabel(f"出站: {current_out}MB/s")
        out_label.setAlignment(QtCore.Qt.AlignCenter)
        out_label.setStyleSheet("color: #FF6347; font-size: 12px; font-weight: bold; background: transparent;")

        # 总流量
        total_label = QtWidgets.QLabel(f"总计: {current_in + current_out}MB/s")
        total_label.setAlignment(QtCore.Qt.AlignCenter)
        total_label.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        stats_layout.addWidget(in_label)
        stats_layout.addWidget(out_label)
        stats_layout.addWidget(total_label)
        layout.addLayout(stats_layout)

        return widget

    def Set_Bottom(self):
        # 底部区域
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("background:transparent;")
        self.QLabel_Bottom.setMinimumHeight(400)
        QGridLayout_Content = QtWidgets.QGridLayout(self.QLabel_Bottom)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(0,0, 0, 8)

        # 功能模块数据
        Function_List = {
            "1": {"Function_Text": "今日告警", "Function_Value": "3次"},
            "2": {"Function_Text": "设备在线", "Function_Value": "23台"},
            "3": {"Function_Text": "通道活跃", "Function_Value": "16路"},
            "4": {"Function_Text": "存储使用", "Function_Value": "68%"},
            "5": {"Function_Text": "网络状态", "Function_Value": "正常"},
            "6": {"Function_Text": "系统负载", "Function_Value": "低"},
            "7": {"Function_Text": "录像状态", "Function_Value": "正常"},
            "8": {"Function_Text": "报警处理", "Function_Value": "及时"},
        }

        Page_Info["Page_Element_List"]["Function_QLabel_List"]      = {}
        Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"] = {}

        Count = 1
        for Row in range(2):
            for Col in range(4):
                _QLabel =  QtWidgets.QLabel()
                _QHBoxLayout = QtWidgets.QHBoxLayout(_QLabel)
                _QHBoxLayout.setSpacing(0)
                _QHBoxLayout.setContentsMargins(0, 0, 0, 0)
                Page_Info["Page_Element_List"]["Function_QLabel_List"][f"{Count}"]      =  _QLabel
                Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"][f"{Count}"] =  _QHBoxLayout
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1

        Function_QHBoxLayout_List={}
        Function_QHBoxLayout_List["1"] = self.Set_Alarm_Chart_Module()
        Function_QHBoxLayout_List["2"] = self.Set_Device_Chart_Module()
        Function_QHBoxLayout_List["3"] = self.Set_Channel_Chart_Module()
        Function_QHBoxLayout_List["4"] = self.Set_Storage_Chart_Module()
        Function_QHBoxLayout_List["5"] = self.Set_Performance_Chart_Module()
        Function_QHBoxLayout_List["6"] = self.Set_Network_Status_Module()
        Function_QHBoxLayout_List["7"] = self.Set_Recording_Status_Module()
        Function_QHBoxLayout_List["8"] = self.Set_System_Health_Module()

        for Name, _QHBoxLayout in Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"].items():
            _QHBoxLayout.addWidget( Function_QHBoxLayout_List[Name])

        return self.QLabel_Bottom

    def Set_Function_Module(self, title, value):
        QLabel_Info = {
            "Title_Name": title
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(8)
        QVBoxLayout_Content.setContentsMargins(8, 20, 8, 20)

        # 数值显示
        value_label = QtWidgets.QLabel()
        value_label.setAlignment(QtCore.Qt.AlignCenter)
        value_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 18px; font-weight: bold; background: transparent;")
        value_label.setText(value)
        QVBoxLayout_Content.addWidget(value_label)

        return __QLabel

    def Set_Alarm_Chart_Module(self):
        """告警统计图表模块"""
        QLabel_Info = {"Title_Name": "告警统计"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 告警趋势图
        chart_widget = AlarmTrendWidget(self.alarm_data['alarm_trend'])
        QVBoxLayout_Content.addWidget(chart_widget)

        # 告警统计
        stats_layout = QtWidgets.QHBoxLayout()
        critical_label = QtWidgets.QLabel(f"{self.alarm_data['critical_alarms']}")
        critical_label.setAlignment(QtCore.Qt.AlignCenter)
        critical_label.setStyleSheet("color: #FF6347; font-size: 14px; font-weight: bold; background: transparent;")
        critical_desc = QtWidgets.QLabel("严重")
        critical_desc.setAlignment(QtCore.Qt.AlignCenter)
        critical_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        warning_label = QtWidgets.QLabel(f"{self.alarm_data['warning_alarms']}")
        warning_label.setAlignment(QtCore.Qt.AlignCenter)
        warning_label.setStyleSheet("color: #FFD700; font-size: 14px; font-weight: bold; background: transparent;")
        warning_desc = QtWidgets.QLabel("警告")
        warning_desc.setAlignment(QtCore.Qt.AlignCenter)
        warning_desc.setStyleSheet("color: #1E90FF; font-size: 12px; background: transparent;")

        critical_layout = QtWidgets.QVBoxLayout()
        critical_layout.addWidget(critical_label)
        critical_layout.addWidget(critical_desc)

        warning_layout = QtWidgets.QVBoxLayout()
        warning_layout.addWidget(warning_label)
        warning_layout.addWidget(warning_desc)

        stats_layout.addLayout(critical_layout)
        stats_layout.addLayout(warning_layout)
        QVBoxLayout_Content.addLayout(stats_layout)

        return __QLabel

    def Set_Device_Chart_Module(self):
        """设备状态饼图模块"""
        QLabel_Info = {"Title_Name": "设备状态"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 设备饼图
        pie_widget = DevicePieWidget(self.device_data['online_devices'], self.device_data['offline_devices'])
        QVBoxLayout_Content.addWidget(pie_widget)

        # 设备统计
        total_label = QtWidgets.QLabel(f"总计: {self.device_data['total_devices']}台")
        total_label.setAlignment(QtCore.Qt.AlignCenter)
        total_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(total_label)

        return __QLabel

    def Set_Channel_Chart_Module(self):
        """通道状态图表模块"""
        QLabel_Info = {"Title_Name": "通道状态"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 通道状态条形图
        bar_widget = ChannelBarWidget(self.device_data['active_channels'], self.device_data['inactive_channels'])
        QVBoxLayout_Content.addWidget(bar_widget)

        # 活跃率
        active_rate = (self.device_data['active_channels'] / self.device_data['total_channels']) * 100
        rate_label = QtWidgets.QLabel(f"活跃率: {active_rate:.1f}%")
        rate_label.setAlignment(QtCore.Qt.AlignCenter)
        rate_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(rate_label)

        return __QLabel

    def Set_Storage_Chart_Module(self):
        """存储使用图表模块"""
        QLabel_Info = {"Title_Name": "存储监控"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 存储环形图
        ring_widget = StorageRingWidget(self.system_data['storage_used'])
        QVBoxLayout_Content.addWidget(ring_widget)

        # 存储详情
        used_gb = self.system_data['storage_total'] - self.system_data['storage_free']
        detail_label = QtWidgets.QLabel(f"{used_gb}GB / {self.system_data['storage_total']}GB")
        detail_label.setAlignment(QtCore.Qt.AlignCenter)
        detail_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(detail_label)

        return __QLabel

    def Set_Performance_Chart_Module(self):
        """系统性能图表模块"""
        QLabel_Info = {"Title_Name": "系统性能"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 性能雷达图
        radar_widget = PerformanceRadarWidget()
        QVBoxLayout_Content.addWidget(radar_widget)

        # 性能评分
        score_label = QtWidgets.QLabel("性能评分: 85分")
        score_label.setAlignment(QtCore.Qt.AlignCenter)
        score_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(score_label)

        return __QLabel

    def Set_Network_Status_Module(self):
        """网络状态模块"""
        QLabel_Info = {"Title_Name": "网络状态"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 网络状态指示器
        status_widget = NetworkStatusWidget()
        QVBoxLayout_Content.addWidget(status_widget)

        # 延迟显示
        latency_label = QtWidgets.QLabel("延迟: 12ms")
        latency_label.setAlignment(QtCore.Qt.AlignCenter)
        latency_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(latency_label)

        return __QLabel

    def Set_Recording_Status_Module(self):
        """录像状态模块"""
        QLabel_Info = {"Title_Name": "录像状态"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 录像状态图
        recording_widget = RecordingStatusWidget()
        QVBoxLayout_Content.addWidget(recording_widget)

        # 录像统计
        stats_label = QtWidgets.QLabel("正在录像: 589路")
        stats_label.setAlignment(QtCore.Qt.AlignCenter)
        stats_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(stats_label)

        return __QLabel

    def Set_System_Health_Module(self):
        """系统健康度模块"""
        QLabel_Info = {"Title_Name": "系统健康"}
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(4)
        QVBoxLayout_Content.setContentsMargins(8, 10, 8, 10)

        # 健康度仪表盘
        health_widget = SystemHealthWidget()
        QVBoxLayout_Content.addWidget(health_widget)

        # 健康状态
        health_label = QtWidgets.QLabel("状态: 良好")
        health_label.setAlignment(QtCore.Qt.AlignCenter)
        health_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 12px; background: transparent;")
        QVBoxLayout_Content.addWidget(health_label)

        return __QLabel

    def Toggle_Right(self):
        # 现在系统状态监控已经占据整个第一行，不需要切换
        pass

    def Toggle_Bottom(self):
        total_height = self.QSplitter_Root.height()
        if self.bottom_expanded:
            # 收起底部
            self.QPropertyAnimation_Bottom.setStartValue([total_height - 100, 100])
            self.QPropertyAnimation_Bottom.setEndValue([total_height, 0])
            self.QPropertyAnimation_Bottom.finished.connect(lambda: self.QLabel_Bottom.hide())
            self.QPropertyAnimation_Bottom.setSizes([self.height(), 0])
            self.bottom_expanded = False
        else:
            # 展开底部
            self.QLabel_Bottom.show()
            self.QPropertyAnimation_Bottom.setStartValue([total_height, 0])
            self.QPropertyAnimation_Bottom.setEndValue([total_height - 100, 100])
            self.QSplitter_Root.setSizes([self.height(), 400])
            self.bottom_expanded = True

        self.QPropertyAnimation_Bottom.start()

    def Page_Update_Timer_Date(self):
        """更新时间显示和数据"""
        current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        self.QLabel_Show_Time.setText(current_time)

        # 模拟数据更新
        self.update_system_data()

    def update_system_data(self):
        """更新系统数据（模拟实时数据）"""
        # 更新CPU数据
        new_cpu = random.randint(10, 35)
        self.system_data['cpu_history'].append(new_cpu)
        if len(self.system_data['cpu_history']) > 10:
            self.system_data['cpu_history'].pop(0)

        # 更新内存数据
        new_memory = random.randint(40, 65)
        self.system_data['memory_history'].append(new_memory)
        if len(self.system_data['memory_history']) > 10:
            self.system_data['memory_history'].pop(0)

        # 更新网络数据
        new_net_in = random.randint(100, 200)
        new_net_out = random.randint(60, 120)
        self.system_data['network_in'].append(new_net_in)
        self.system_data['network_out'].append(new_net_out)
        if len(self.system_data['network_in']) > 10:
            self.system_data['network_in'].pop(0)
            self.system_data['network_out'].pop(0)

        # 更新告警数据
        if random.random() < 0.1:  # 10%概率产生新告警
            self.alarm_data['today_alarms'] += 1
            if random.random() < 0.3:
                self.alarm_data['critical_alarms'] += 1
            else:
                self.alarm_data['warning_alarms'] += 1

        # 更新设备在线状态
        if random.random() < 0.05:  # 5%概率设备状态变化
            if self.device_data['online_devices'] < self.device_data['total_devices']:
                self.device_data['online_devices'] += 1
                self.device_data['offline_devices'] -= 1

    def Page_Update(self):
       pass

    def PAGE_HANDLER_EXECUTE(self, CommandParameter):
        if "Self_" in CommandParameter["Type"]:
            PP(CommandParameter)
            self.Open_Processing(CommandParameter)
        else:
            self.Signal_Result.emit(CommandParameter)  # 发送信号并传递数据

    def Open_Processing(self,ProcessingParameter):
        PP(ProcessingParameter)
        exec(f"QLabel_Component= Component_{ProcessingParameter['Processing']}.Component_{ProcessingParameter['Processing']}(self,ProcessingParameter)")
        exec("QLabel_Component.Signal_Result.connect(self.PAGE_HANDLER_EXECUTE)")
        exec("QLabel_Component.move(self.geometry().center() - QLabel_Component.rect().center())")
        exec("QLabel_Component.show()")


class QLabel_Function(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:self.QLabel_Info = args[0]
        except:pass

        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")

        Icon_List = {
            "系统状态监控": "system_status.png",
            "告警统计": "alarm.png",
            "设备状态": "device_status.png",
            "通道状态": "channel.png",
            "存储监控": "storage.png",
            "系统性能": "hashrate.png",
            "网络状态": "bandwidth.png",
            "录像状态": "video.png",
            "系统健康": "system_health.png",
        }

        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.QLabel_Info.get("Title_Name", "")
        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)
        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 12px; }")

        QLabel_Title_Name.setText(self.QLabel_Info["Title_Name"])

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")

        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)



# ========================================================================================
# 可视化图表组件类
# ========================================================================================

class CPUChartWidget(QtWidgets.QLabel):
    """CPU使用率趋势图组件"""
    def __init__(self, data):
        super().__init__()
        self.data = data
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 绘制背景
        rect = self.rect()
        painter.fillRect(rect, QtGui.QColor(0, 0, 0, 50))

        # 绘制网格线
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 30), 1))
        for i in range(5):
            y = rect.height() * i / 4
            painter.drawLine(0, y, rect.width(), y)

        # 绘制数据线
        if len(self.data) > 1:
            painter.setPen(QtGui.QPen(QtGui.QColor(0, 255, 136), 2))
            points = []
            for i, value in enumerate(self.data):
                x = rect.width() * i / (len(self.data) - 1)
                y = rect.height() * (1 - value / 100)
                points.append(QtCore.QPointF(x, y))

            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])

            # 绘制数据点
            painter.setBrush(QtGui.QBrush(QtGui.QColor(0, 255, 136)))
            for point in points:
                painter.drawEllipse(point, 3, 3)

class MemoryChartWidget(QtWidgets.QLabel):
    """内存使用率趋势图组件"""
    def __init__(self, data):
        super().__init__()
        self.data = data
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 绘制背景
        rect = self.rect()
        painter.fillRect(rect, QtGui.QColor(0, 0, 0, 50))

        # 绘制网格线
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 30), 1))
        for i in range(5):
            y = rect.height() * i / 4
            painter.drawLine(0, y, rect.width(), y)

        # 绘制数据线
        if len(self.data) > 1:
            painter.setPen(QtGui.QPen(QtGui.QColor(255, 215, 0), 2))
            points = []
            for i, value in enumerate(self.data):
                x = rect.width() * i / (len(self.data) - 1)
                y = rect.height() * (1 - value / 100)
                points.append(QtCore.QPointF(x, y))

            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])

            # 绘制数据点
            painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 215, 0)))
            for point in points:
                painter.drawEllipse(point, 3, 3)

class StorageProgressWidget(QtWidgets.QLabel):
    """存储使用进度条组件"""
    def __init__(self, percentage):
        super().__init__()
        self.percentage = percentage
        self.setMinimumHeight(30)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect().adjusted(5, 5, -5, -5)

        # 绘制背景
        painter.setBrush(QtGui.QBrush(QtGui.QColor(40, 52, 80, 100)))
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 60), 1))
        painter.drawRoundedRect(rect, 5, 5)

        # 绘制进度
        progress_width = rect.width() * self.percentage / 100
        progress_rect = QtCore.QRect(rect.x(), rect.y(), int(progress_width), rect.height())

        # 根据使用率选择颜色
        if self.percentage < 60:
            color = QtGui.QColor(0, 255, 136)
        elif self.percentage < 80:
            color = QtGui.QColor(255, 215, 0)
        else:
            color = QtGui.QColor(255, 99, 71)

        painter.setBrush(QtGui.QBrush(color))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawRoundedRect(progress_rect, 5, 5)

        # 绘制百分比文字
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 1))
        painter.drawText(rect, QtCore.Qt.AlignCenter, f"{self.percentage}%")

class NetworkChartWidget(QtWidgets.QLabel):
    """网络流量图表组件"""
    def __init__(self, data_in, data_out):
        super().__init__()
        self.data_in = data_in
        self.data_out = data_out
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 绘制背景
        rect = self.rect()
        painter.fillRect(rect, QtGui.QColor(0, 0, 0, 50))

        # 绘制网格线
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 30), 1))
        for i in range(5):
            y = rect.height() * i / 4
            painter.drawLine(0, y, rect.width(), y)

        # 绘制入流量线
        if len(self.data_in) > 1:
            painter.setPen(QtGui.QPen(QtGui.QColor(0, 255, 136), 2))
            max_value = max(max(self.data_in), max(self.data_out))
            points = []
            for i, value in enumerate(self.data_in):
                x = rect.width() * i / (len(self.data_in) - 1)
                y = rect.height() * (1 - value / max_value)
                points.append(QtCore.QPointF(x, y))

            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])

        # 绘制出流量线
        if len(self.data_out) > 1:
            painter.setPen(QtGui.QPen(QtGui.QColor(255, 99, 71), 2))
            max_value = max(max(self.data_in), max(self.data_out))
            points = []
            for i, value in enumerate(self.data_out):
                x = rect.width() * i / (len(self.data_out) - 1)
                y = rect.height() * (1 - value / max_value)
                points.append(QtCore.QPointF(x, y))

            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])

class AlarmTrendWidget(QtWidgets.QLabel):
    """告警趋势图组件"""
    def __init__(self, data):
        super().__init__()
        self.data = data
        self.setMinimumHeight(60)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        painter.fillRect(rect, QtGui.QColor(0, 0, 0, 50))

        # 绘制柱状图
        if self.data:
            max_value = max(self.data)
            bar_width = rect.width() / len(self.data)

            for i, value in enumerate(self.data):
                x = i * bar_width
                height = rect.height() * value / max_value if max_value > 0 else 0
                y = rect.height() - height

                # 根据值选择颜色
                if value > 15:
                    color = QtGui.QColor(255, 99, 71)  # 红色
                elif value > 10:
                    color = QtGui.QColor(255, 215, 0)  # 黄色
                else:
                    color = QtGui.QColor(0, 255, 136)  # 绿色

                painter.setBrush(QtGui.QBrush(color))
                painter.setPen(QtCore.Qt.NoPen)
                painter.drawRect(int(x), int(y), int(bar_width - 2), int(height))

class DevicePieWidget(QtWidgets.QLabel):
    """设备状态饼图组件"""
    def __init__(self, online, offline):
        super().__init__()
        self.online = online
        self.offline = offline
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 3

        total = self.online + self.offline
        if total > 0:
            # 在线设备扇形
            online_angle = int(360 * self.online / total)
            painter.setBrush(QtGui.QBrush(QtGui.QColor(0, 255, 136)))
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawPie(center.x() - radius, center.y() - radius,
                          radius * 2, radius * 2, 0, online_angle * 16)

            # 离线设备扇形
            painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 99, 71)))
            painter.drawPie(center.x() - radius, center.y() - radius,
                          radius * 2, radius * 2, online_angle * 16, (360 - online_angle) * 16)

class ChannelBarWidget(QtWidgets.QLabel):
    """通道状态条形图组件"""
    def __init__(self, active, inactive):
        super().__init__()
        self.active = active
        self.inactive = inactive
        self.setMinimumHeight(60)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect().adjusted(10, 10, -10, -10)
        total = self.active + self.inactive

        if total > 0:
            # 活跃通道条
            active_width = rect.width() * self.active / total
            painter.setBrush(QtGui.QBrush(QtGui.QColor(0, 255, 136)))
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawRect(rect.x(), rect.y(), int(active_width), rect.height() // 2)

            # 非活跃通道条
            inactive_width = rect.width() * self.inactive / total
            painter.setBrush(QtGui.QBrush(QtGui.QColor(128, 128, 128)))
            painter.drawRect(rect.x() + int(active_width), rect.y(),
                           int(inactive_width), rect.height() // 2)

            # 标签
            painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 1))
            painter.drawText(rect.x(), rect.y() + rect.height() // 2 + 15, f"活跃: {self.active}")
            painter.drawText(rect.x() + int(active_width), rect.y() + rect.height() // 2 + 15, f"非活跃: {self.inactive}")

class StorageRingWidget(QtWidgets.QLabel):
    """存储使用环形图组件"""
    def __init__(self, percentage):
        super().__init__()
        self.percentage = percentage
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        center = rect.center()
        outer_radius = min(rect.width(), rect.height()) // 3
        inner_radius = outer_radius - 10

        # 背景环
        painter.setBrush(QtGui.QBrush(QtGui.QColor(40, 52, 80, 100)))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawEllipse(center.x() - outer_radius, center.y() - outer_radius,
                          outer_radius * 2, outer_radius * 2)

        # 内部空心
        painter.setBrush(QtGui.QBrush(QtGui.QColor(18, 27, 53)))
        painter.drawEllipse(center.x() - inner_radius, center.y() - inner_radius,
                          inner_radius * 2, inner_radius * 2)

        # 使用量扇形
        if self.percentage > 0:
            angle = int(360 * self.percentage / 100)
            if self.percentage < 60:
                color = QtGui.QColor(0, 255, 136)
            elif self.percentage < 80:
                color = QtGui.QColor(255, 215, 0)
            else:
                color = QtGui.QColor(255, 99, 71)

            painter.setBrush(QtGui.QBrush(color))
            painter.drawPie(center.x() - outer_radius, center.y() - outer_radius,
                          outer_radius * 2, outer_radius * 2, 90 * 16, -angle * 16)

            # 内部空心
            painter.setBrush(QtGui.QBrush(QtGui.QColor(18, 27, 53)))
            painter.drawEllipse(center.x() - inner_radius, center.y() - inner_radius,
                              inner_radius * 2, inner_radius * 2)

        # 中心文字
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 1))
        painter.drawText(rect, QtCore.Qt.AlignCenter, f"{self.percentage}%")

class PerformanceRadarWidget(QtWidgets.QLabel):
    """系统性能雷达图组件"""
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")
        self.values = [85, 78, 92, 88, 75]  # CPU, 内存, 磁盘, 网络, 稳定性

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 3

        # 绘制背景网格
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 60), 1))
        for i in range(1, 4):
            r = radius * i / 3
            painter.drawEllipse(center.x() - r, center.y() - r, r * 2, r * 2)

        # 绘制数据多边形
        if self.values:
            points = []
            for i, value in enumerate(self.values):
                angle = 2 * math.pi * i / len(self.values) - math.pi / 2
                r = radius * value / 100
                x = center.x() + r * math.cos(angle)
                y = center.y() + r * math.sin(angle)
                points.append(QtCore.QPointF(x, y))

            # 填充多边形
            painter.setBrush(QtGui.QBrush(QtGui.QColor(0, 255, 136, 100)))
            painter.setPen(QtGui.QPen(QtGui.QColor(0, 255, 136), 2))
            polygon = QtGui.QPolygonF(points)
            painter.drawPolygon(polygon)

class NetworkStatusWidget(QtWidgets.QLabel):
    """网络状态指示器组件"""
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(60)
        self.setStyleSheet("background: transparent;")
        self.status = "正常"  # 正常, 警告, 异常

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        center = rect.center()

        # 状态指示灯
        if self.status == "正常":
            color = QtGui.QColor(0, 255, 136)
        elif self.status == "警告":
            color = QtGui.QColor(255, 215, 0)
        else:
            color = QtGui.QColor(255, 99, 71)

        painter.setBrush(QtGui.QBrush(color))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawEllipse(center.x() - 15, center.y() - 15, 30, 30)

        # 状态文字
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 1))
        painter.drawText(rect.adjusted(0, 0, 0, 0), QtCore.Qt.AlignCenter, self.status)

class RecordingStatusWidget(QtWidgets.QLabel):
    """录像状态组件"""
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(60)
        self.setStyleSheet("background: transparent;")
        self.recording_channels = [True, True, False, True, True, False, True, True]

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect().adjusted(10, 10, -10, -10)

        # 绘制录像状态网格
        cols = 4
        rows = 2
        cell_width = rect.width() / cols
        cell_height = rect.height() / rows

        for i, recording in enumerate(self.recording_channels):
            row = i // cols
            col = i % cols
            x = rect.x() + col * cell_width
            y = rect.y() + row * cell_height

            color = QtGui.QColor(0, 255, 136) if recording else QtGui.QColor(128, 128, 128)
            painter.setBrush(QtGui.QBrush(color))
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawRect(int(x + 2), int(y + 2), int(cell_width - 4), int(cell_height - 4))

class SystemHealthWidget(QtWidgets.QLabel):
    """系统健康度仪表盘组件"""
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(80)
        self.setStyleSheet("background: transparent;")
        self.health_score = 85

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 3

        # 绘制仪表盘背景
        painter.setBrush(QtGui.QBrush(QtGui.QColor(40, 52, 80, 100)))
        painter.setPen(QtGui.QPen(QtGui.QColor(0, 180, 255, 60), 2))
        painter.drawEllipse(center.x() - radius, center.y() - radius, radius * 2, radius * 2)

        # 绘制健康度扇形
        if self.health_score > 0:
            angle = int(270 * self.health_score / 100)  # 270度为满分

            if self.health_score >= 80:
                color = QtGui.QColor(0, 255, 136)
            elif self.health_score >= 60:
                color = QtGui.QColor(255, 215, 0)
            else:
                color = QtGui.QColor(255, 99, 71)

            painter.setBrush(QtGui.QBrush(color))
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawPie(center.x() - radius, center.y() - radius,
                          radius * 2, radius * 2, 135 * 16, -angle * 16)

        # 中心分数
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 1))
        painter.drawText(rect, QtCore.Qt.AlignCenter, f"{self.health_score}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Media_VideoHome()
    Page_Widget.show()
    sys.exit(app.exec())
