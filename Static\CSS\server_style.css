.log_status {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid #2b92d4;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:log;
	-webkit-animation-duration:2700ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}

@-webkit-keyframes log {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(255,255,255,0.1);
}
100% {
	opacity:1;
	border:1px solid rgba(59,235,235,1);
	box-shadow:0 1px 30px rgba(59,255,255,1);
}
}
.server_status {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid #2b92d4;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:server;
	-webkit-animation-duration:2700ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}

@-webkit-keyframes server {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(255,255,255,0.1);
}
100% {
	opacity:1;
	border:1px solid rgba(59,235,235,1);
	box-shadow:0 1px 30px rgba(59,255,255,1);
}
}

.alert_status {
    position:relative;
    /* background-color:transparent !important; */
    border-radius: 50%;
	line-height:40px;
	border:1px solid #c7f34e;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#f1e1d3),to(#d6f77b));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:alert;
	-webkit-animation-duration:1100ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}


@-webkit-keyframes alert {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(233, 247, 36, 0.1);
}
100% {
	opacity:1;
	border:1px solid rgb(202, 226, 63);
	box-shadow:0 1px 30px rgb(206, 243, 104);
}
}



.status_error {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid #e90e0e;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#f5760e),to(#f10f0f));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:error;
	-webkit-animation-duration:100ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}



@-webkit-keyframes error {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(248, 10, 30, 0.1);
}
100% {
	opacity:1;
	border:1px solid rgb(243, 148, 84);
	box-shadow:0 1px 30px rgb(240, 8, 8);
}
}





.status_alert {
    position:relative;
    /* background-color:transparent !important; */
    border-radius: 50%;
	line-height:40px;
	border:1px solid #c7f34e;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#f1e1d3),to(#d6f77b));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:alert;
	-webkit-animation-duration:1100ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}


@-webkit-keyframes alert {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(233, 247, 36, 0.1);
}
100% {
	opacity:1;
	border:1px solid rgb(202, 226, 63);
	box-shadow:0 1px 30px rgb(206, 243, 104);
}
}







.status_normal {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid rgb(14, 36, 233);
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#8fccf5),to(#d6dee0));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:normal;
	-webkit-animation-duration:3333ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}



@-webkit-keyframes normal {
	0% {
        opacity:.2;
        box-shadow:0 1px 2px rgba(255,255,255,0.1);
    }
    100% {
        opacity:1;
        border:1px solid rgba(59,235,235,1);
        box-shadow:0 1px 30px rgba(59,255,255,1);
    }
}







.server_status_btn{
    
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: cyan
}
.alert_status_btn{
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: rgb(190, 235, 132)
  
}
.failed_status_btn{
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: rgb(241, 12, 12)
}






.spider_log{
    float: left;
   /* position: absolute; */
   padding:1px;
   border:1px groove white;
   margin-top:0px;
   margin-left:0px;
   border-radius:50%;
   width:60px;
   height:60px;
   filter:progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=4);/*ie*/
   -moz-box-shadow: 2px #909090;/*firefox*/
   -webkit-box-shadow: 2px  #fefeff;/*safari或chrome*/
   box-shadow:0 0 5px #4E4E4E ;/*opera或ie9*/
}


.Echat_Dialog{-webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: 100%;
    font-family: Nunito,sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: rgba(255,255,255,.85);
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    position: fixed;
    top: 0;
    right: 0;
    height: 100%;
    width: 250px;
    background-color: rgba(0,0,0,.96);
    z-index: 101;
    transition: box-shadow .3s,transform .3s,opacity .3s,-webkit-transform .3s;
    box-shadow: -5px 0 10px rgba(0,0,0,.25);
    opacity: 1;
    transform: translate3d(0,0,0);
}

.csc_themes{-webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: 100%;
    font-family: Nunito,sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: rgba(255,255,255,.85);
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: hidden !important;
    padding: 0 !important;
    height: 100%;
    position: relative;
}
.csc_inner{
    -webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: 100%;
    font-family: Nunito,sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: rgba(255,255,255,.85);
    -webkit-font-smoothing: antialiased;
    border: none !important;
    box-sizing: content-box !important;
    left: 0;
    margin: 0;
    max-width: none !important;
    overflow: scroll !important;
    padding: 0;
    position: relative !important;
    top: 0;
    width: auto !important;
    height: auto;
    margin-bottom: 0px;
    margin-right: 0px;
    max-height: 198px;
}
