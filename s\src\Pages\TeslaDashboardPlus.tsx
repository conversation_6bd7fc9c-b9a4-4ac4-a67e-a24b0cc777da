import React, { useState } from 'react';
import { Layout, Menu, Switch, Slider, <PERSON>, Button, Divider } from 'antd';
import {
  CarOutlined,
  SettingOutlined,
  WifiOutlined,
  LockOutlined,
  SafetyOutlined,
  PoweroffOutlined,
} from '@ant-design/icons';
// import '../Styles/TeslaDashboardPlus.css'; // 20250523 这个样式影响到了侧边栏 所以注释了
// D:\ERMission\Bin\System\Web\React\sentienl_analysis\src\Pages\TeslaDashboardPlus.tsx
const { Header, Sider, Content } = Layout;
const { Option } = Select;

const TeslaDashboardPlus = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [speed, setSpeed] = useState(75);
  const [batteryLevel, setBatteryLevel] = useState(82);
  const [range, setRange] = useState(325);
  const [temperature, setTemperature] = useState(22);
  const [isAutopilot, setIsAutopilot] = useState(true);
  const [lights, setLights] = useState(true);
  const [climateMode, setClimateMode] = useState('auto');
  const [suspensionHeight, setSuspensionHeight] = useState(2);
  const [selectedMenu, setSelectedMenu] = useState('1');

  const handleSpeedChange = () => {
    setSpeed(speed);
  };

  const handleBatteryChange = () => {
    setBatteryLevel(range);
    setRange(Math.floor(range * 4));
  };

  const handleTemperatureChange = () => {
    setTemperature(temperature);
  };

  const handleSuspensionChange = () => {
    setSuspensionHeight(suspensionHeight);
  };

  const renderContent = () => {
    switch (selectedMenu) {
      case '1': // 驾驶
        return (
          <div className="control-panel">
            <h3>驾驶设置</h3>
            <div className="control-item">
              <span>自动驾驶</span>
              <Switch 
                checked={isAutopilot} 
                onChange={setIsAutopilot}
                checkedChildren="开启" 
                unCheckedChildren="关闭" 
              />
            </div>
            <div className="control-item">
              <span>速度限制</span>
              <Slider
                min={0}
                max={150}
                onChange={handleSpeedChange}
                value={speed}
                marks={{
                  0: '0',
                  50: '50',
                  100: '100',
                  150: '150'
                }}
              />
            </div>
            <div className="control-item">
              <span>悬挂高度</span>
              <Slider
                min={1}
                max={5}
                onChange={handleSuspensionChange}
                value={suspensionHeight}
                marks={{
                  1: '低',
                  3: '中',
                  5: '高'
                }}
              />
            </div>
          </div>
        );
      case '2': // 气候
        return (
          <div className="control-panel">
            <h3>气候控制</h3>
            <div className="control-item">
              <span>温度</span>
              <Slider
                min={16}
                max={30}
                onChange={handleTemperatureChange}
                value={temperature}
              />
            </div>
            <div className="control-item">
              <span>模式</span>
              <Select
                value={climateMode}
                style={{ width: 120 }}
                onChange={setClimateMode}
              >
                <Option value="auto">自动</Option>
                <Option value="manual">手动</Option>
                <Option value="eco">节能</Option>
              </Select>
            </div>
            <div className="control-item">
              <span>座椅加热</span>
              <Select
                defaultValue="off"
                style={{ width: 120 }}
              >
                <Option value="off">关闭</Option>
                <Option value="low">低温</Option>
                <Option value="medium">中温</Option>
                <Option value="high">高温</Option>
              </Select>
            </div>
          </div>
        );
      case '3': // 灯光
        return (
          <div className="control-panel">
            <h3>灯光设置</h3>
            <div className="control-item">
              <span>大灯</span>
              <Switch 
                checked={lights} 
                onChange={setLights}
                checkedChildren="开启" 
                unCheckedChildren="关闭" 
              />
            </div>
            <div className="control-item">
              <span>氛围灯亮度</span>
              <Slider
                min={0}
                max={100}
                defaultValue={50}
              />
            </div>
            <div className="control-item">
              <span>氛围灯颜色</span>
              <Select
                defaultValue="blue"
                style={{ width: 120 }}
              >
                <Option value="blue">蓝色</Option>
                <Option value="red">红色</Option>
                <Option value="green">绿色</Option>
                <Option value="white">白色</Option>
              </Select>
            </div>
          </div>
        );
      case '4': // 安全
        return (
          <div className="control-panel">
            <h3>安全设置</h3>
            <div className="control-item">
              <span>哨兵模式</span>
              <Switch 
                defaultChecked
                checkedChildren="开启" 
                unCheckedChildren="关闭" 
              />
            </div>
            <div className="control-item">
              <span>PIN码驾驶</span>
              <Switch 
                checkedChildren="开启" 
                unCheckedChildren="关闭" 
              />
            </div>
            <Divider />
            <Button type="primary" icon={<PoweroffOutlined />}>
              紧急断电
            </Button>
          </div>
        );
      default:
        return <div className="control-panel">请选择菜单项</div>;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#000' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        width={250}
        theme="dark"
      >
        <div className="logo">
          {collapsed ? 'T' : 'TESLA'}
        </div>
        <Menu 
          theme="dark" 
          mode="inline" 
          defaultSelectedKeys={['1']}
          onSelect={({ key }) => setSelectedMenu(key)}
        >
          <Menu.Item key="1" icon={<CarOutlined />}>
            驾驶
          </Menu.Item>
          <Menu.Item key="2" icon={<WifiOutlined />}>
            气候
          </Menu.Item>
          <Menu.Item key="3" icon={<SettingOutlined />}>
            灯光
          </Menu.Item>
          <Menu.Item key="4" icon={<SafetyOutlined />}>
            安全
          </Menu.Item>
          <Menu.Item key="5" icon={<LockOutlined />}>
            锁
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-header">
          <div className="top-bar">
            <div className="time">{new Date().toLocaleTimeString()}</div>
            <div className="status-icons">
              <span className="signal">📶</span>
              <span className="bluetooth">🔵</span>
              <span className="battery-icon">🔋 {batteryLevel}%</span>
            </div>
          </div>
        </Header>
        <Content className="site-layout-content">
          <div className="dashboard-container">
            <div className="main-display">
              {/* 左侧车辆状态 */}
              <div className="left-panel">
                <div className="speed-display">
                  <div className="speed-value">{speed}</div>
                  <div className="speed-unit">km/h</div>
                </div>
                
                <div className="autopilot-indicator">
                  {isAutopilot ? 'AUTOPILOT ENGAGED' : 'AUTOPILOT OFF'}
                </div>
              </div>
              
              {/* 中央车辆图形 */}
              <div className="center-panel">
                <div className="car-graphic">
                  <div className="car-outline">
                    <div className="car-front"></div>
                    <div className="car-body"></div>
                    <div className="car-rear"></div>
                  </div>
                  <div className="car-status">
                    <div className="tire-pressure">
                      <span>TP</span> 2.8
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 右侧信息面板 */}
              <div className="right-panel">
                <div className="battery-display">
                  <div className="battery-level">
                    <div 
                      className="battery-fill" 
                      style={{ width: `${batteryLevel}%` }}
                    ></div>
                  </div>
                  <div className="battery-info">
                    <span className="range">{range} km</span>
                    <span className="percentage">{batteryLevel}%</span>
                  </div>
                </div>
                
                <div className="temperature-display">
                  <div className="temperature-value">
                    {temperature}°C
                  </div>
                  <div className="climate-control">
                    {climateMode.toUpperCase()}
                  </div>
                </div>
                
                <div className="additional-info">
                  <div className="info-item">
                    <span>Range</span>
                    <span>{range} km</span>
                  </div>
                  <div className="info-item">
                    <span>Consumption</span>
                    <span>152 Wh/km</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 控制面板区域 */}
            <div className="control-display">
              {renderContent()}
            </div>
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default TeslaDashboardPlus;