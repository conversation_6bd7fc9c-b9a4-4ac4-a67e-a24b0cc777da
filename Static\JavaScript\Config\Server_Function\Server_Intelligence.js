console.log('Server_Intelligence')






 // 组织所有数据请求
function Get_Organizational_Info() {

    const __Server_Data = new Server_Data('Service_Requests_Data', {
        'Data_Name': 'Organizational_Info',
        'User_Token': this.User_Token
    })
    this.Organizational_Info_List = __Server_Data.run()
    $('#data-table').bootstrapTable('load', this.Organizational_Info_List)
}


 // 组织编辑
 function Organizational_Editor(ID) {
    console.log('Server_Intelligence',ID)
    localStorage.setItem('Group_ID', ID.toString())
    window.location.href="Service_Page?Page=Page_Analysis_Source_Organizational_Editor"
}




