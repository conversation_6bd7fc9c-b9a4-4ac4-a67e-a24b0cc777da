# -*- coding: utf-8 -*-
import time,os,sys
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtCore import (Slot,Property)

class Utils_Dialog_Media_Play(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.Dialog_Info={"Dialog_Title":"哨兵提示","Dialog_Content":"未激活，请您先登录哨兵平台","Dialog_Execute":""}
        try:self.Dialog_Info.update(args[0])
        except:pass
        print(self.Dialog_Info)
        QVBoxLayout_Dialog = QtWidgets.QVBoxLayout()
        QVBoxLayout_Dialog.setSpacing(0)  # 内边界
        QVBoxLayout_Dialog.setContentsMargins(0, 0, 0, 0)  # 外边


        self.QLabel_Title   = QtWidgets.QLabel()
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Bottom  = QtWidgets.QLabel()

        QVBoxLayout_Dialog.addWidget(self.QLabel_Title,1)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Content,3)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Bottom,1)


        self.setLayout(QVBoxLayout_Dialog)

        self.Set_Title()
        self.Set_Content()
        self.Set_Bottom()

    def Set_Title(self):
        QHBoxLayout_Title = QtWidgets.QHBoxLayout()
        QHBoxLayout_Title.setSpacing(0)  # 内边界
        QHBoxLayout_Title.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Title.setLayout(QHBoxLayout_Title)


        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setMinimumSize(30, 30)
        QLabel_Icon.setMaximumSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("background: transparent; border: none;")
        QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment( QtCore.Qt.AlignVCenter)
        # QLabel_Title.setMaximumSize(230, 30)
        QLabel_Title.setText(self.Dialog_Info["Dialog_Title"])

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='black', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setStyleSheet('''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(220,20,60,0.3);border: 0px}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())


        QHBoxLayout_Title.addWidget(QLabel_Icon, 1,alignment=QtCore.Qt.AlignLeft,)
        QHBoxLayout_Title.addWidget(QLabel_Title, 9,alignment=QtCore.Qt.AlignLeft)
        QHBoxLayout_Title.addWidget(QPushButton_Close, 1,alignment=QtCore.Qt.AlignLeft)


        #
        #
        # QLabel_Name = QtWidgets.QLabel()
        # QLabel_Name.setMinimumSize(250, 30)
        # QLabel_Name.setMaximumSize(250, 30)
        # QLabel_Name.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Name.setText("抱歉，您是测试用户无法使用该功能。")
        #


    def Set_Content(self):
        QHBoxLayout_Content = QtWidgets.QHBoxLayout()
        QHBoxLayout_Content.setSpacing(0)  # 内边界
        QHBoxLayout_Content.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Content.setLayout(QHBoxLayout_Content)

        QLabel_Content = QtWidgets.QLabel()
        QLabel_Content.setStyleSheet("background: transparent; border: none;")
        QLabel_Content.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 14))
        QLabel_Content.setText(self.Dialog_Info["Dialog_Content"])
        # QLabel_Content.clicked.connect(lambda: self.Dialog_Close())

        QHBoxLayout_Content.addWidget(QLabel_Content)



    def Set_Bottom(self):
        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout()
        QHBoxLayout_Bottom.setSpacing(0)  # 内边界
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Bottom.setLayout(QHBoxLayout_Bottom)

        QLabel_Yes = QLabel_Click()
        QLabel_Yes.setStyleSheet("background: transparent; border: none;")
        QLabel_Yes.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Yes.setText("确定")
        QLabel_Yes.clicked.connect(lambda: self.Dialog_Emit())



        QLabel_No = QLabel_Click()
        QLabel_No.setStyleSheet("background: transparent; border: none;")

        QLabel_No.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_No.setText("取消")
        QLabel_No.clicked.connect(lambda: self.Dialog_Close())



        QHBoxLayout_Bottom.addWidget(QLabel_Yes,5,alignment=QtCore.Qt.AlignRight,)
        QHBoxLayout_Bottom.addWidget(QLabel_No,1,alignment=QtCore.Qt.AlignRight,)

    def Dialog_Close(self):
        self.deleteLater()  # 安排销毁窗口
        self.close()  # 关闭窗口


    def Dialog_Emit(self):
        # print("Dialog_Emit")
        self.Signal_Result.emit({"Command":"Dialog_Emit","Dialog_Info":{}})
        self.Dialog_Close()









class Utils_Dialog_Media_PlayerConfig(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.Dialog_Info={"Dialog_Title":"视频控制","Dialog_Content":"","Dialog_Execute":"",}
        try:self.Dialog_Info.update(args[0])
        except:pass
        print(self.Dialog_Info)
        self.resize(600, 320)
        self.setAcceptDrops(True)
        # self.setStyleSheet("QLabel{ background-color:rgba(18, 27, 53, 255)}")
        self.setStyleSheet("QLabel{background:rgba(55, 55, 55, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")
        # self.setStyleSheet("QLabel{background:rgba(18, 27, 53, 0.95);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边


        self.QLabel_Title   = QtWidgets.QLabel()
        self.QLabel_Title.setStyleSheet("QLabel{background:transparent;}")
        self.QLabel_Content = QtWidgets.QLabel()
        # self.QLabel_Content.setFixedHeight(150)
        self.QLabel_Content.setStyleSheet("QLabel{background:transparent;border: none;}")
        self.QLabel_Bottom  = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("QLabel{background:transparent;}")

        __QVBoxLayout.addWidget(self.QLabel_Title,1)
        __QVBoxLayout.addWidget(self.QLabel_Content,5)
        __QVBoxLayout.addWidget(self.QLabel_Bottom,1)

        self.Play_Source = ""
        self.Play_Type   = "Sentinel"
        self.Set_Title()
        self.Set_Content()
        self.Set_Bottom()
        # self.Toggle_Change_Source(0)
    def Set_Title(self):
        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Title)
        __QHBoxLayout.setSpacing(0)  # 内边界
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边


        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setMinimumSize(30, 30)
        QLabel_Icon.setMaximumSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        # QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment( QtCore.Qt.AlignVCenter)
        # QLabel_Title.setMaximumSize(230, 30)
        QLabel_Title.setText(self.Dialog_Info["Dialog_Title"])

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='black', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setStyleSheet('''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())

        __QHBoxLayout.addWidget(QLabel_Icon, 1,alignment=QtCore.Qt.AlignLeft,)
        __QHBoxLayout.addWidget(QLabel_Title, 9,alignment=QtCore.Qt.AlignLeft)
        __QHBoxLayout.contentsRect()
        __QHBoxLayout.addWidget(QPushButton_Close, 1)

    def Set_Content(self):
        # 创建主滚动区域
        self.scrollArea = QtWidgets.QScrollArea(self.QLabel_Content)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setStyleSheet("background:transparent;border-radius:6px;border: none;")

        # 创建内容容器
        self.contentWidget = QtWidgets.QWidget()
        self.contentWidget.setStyleSheet("background:transparent;border: none;")

        # 设置主布局
        self.mainLayout = QtWidgets.QVBoxLayout(self.contentWidget)
        self.mainLayout.setSpacing(0)
        self.mainLayout.setContentsMargins(18, 28, 18, 28)

        # 添加顶部填充部件
        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(6)
        self.QVBoxLayout_Line.setContentsMargins(0, 8, 0, 0)
        Line_StyleSheet = """QLabel {
                               background-color: rgba(0,0,0,0.8);
                               border-radius: 3px;
                               border: none;
                               color: #1E90FF;

                           }"""
        for i in range(20):  # 添加20行示例内容
            # label = QtWidgets.QLabel(f"Line {i+1}")
            self.QVBoxLayout_Line.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}))
        # 将顶部填充部件添加到主布局
        self.mainLayout.addWidget(self.QWidget_TopFiller)

        # 设置滚动区域的内容
        self.scrollArea.setWidget(self.contentWidget)

        # 如果需要，可以在这里添加更多内容到QVBoxLayout_Line中

        # 确保QLabel_Content有正确的布局
        if self.QLabel_Content.layout() is None:
            self.QLabel_Content.setLayout(QtWidgets.QVBoxLayout())
        self.QLabel_Content.layout().addWidget(self.scrollArea)

    def Set_Content1(self):

        __QLabel = QtWidgets.QLabel( self.QLabel_Content)
        __QLabel.setStyleSheet("background:transparent;border-radius:6px;border: none;")

        # __QLabel.setFont(QtGui.QFont("Microsoft YaHei", 20))
        # __QLabel.setGeometry(QtCore.QRect(2, 188, 378, 388))

        __QVBoxLayout = QtWidgets.QVBoxLayout( self.QLabel_Content)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(18,28, 18, 28)  # 外边


        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(6)  # 内边界
        self.QVBoxLayout_Line.setContentsMargins(0, 8, 0, 0)  # 外边

        StyleSheet_Line ="""QLabel {
                                       background-color: rgba(255,255,255,0.8);
                                       border-radius: 6px;
                                       border: none;
                                   }
                                   QLabel:hover {
                                       background-color: rgba(255,255,255,0.3);
                                       border: none;
                                   }
                               """
        # Line_Info = {"Line_Name": "视频来源", "Line_Size":[48,528],"Line_Status": "未启动", "Line_Icon": {"Icon": "mdi.server-security", "Size": 0.8},"Line_StyleSheet":StyleSheet_Line}
        # self.QLabel_Line_List = {}
        #
        # self.QLabel_Line_List["Set_Line_Source_Select"]    = self.Set_Line_Source_Select(Line_Info)
        # self.QLabel_Line_List["Set_Line_Source_Address"]   = self.Set_Line_Source_Address(Line_Info)
        # self.QLabel_Line_List["Set_Line_Source_Token"]     = self.Set_Line_Source_Token(Line_Info)
        # self.QLabel_Line_List["Set_Line_Source_OpenFile"]   = self.Set_Line_Source_OpenFile(Line_Info)
        #
        #
        #
        # # __QVBoxLayout1.addWidget(self.QLabel_Line_List["Set_Line_Source_Select"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Address"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        #
        #
        #
        # ##创建一个滚动条
        # self.QScrollArea_Line = QtWidgets.QScrollArea()
        # self.QScrollArea_Line.setStyleSheet("QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        # self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        # __QVBoxLayout.addWidget(self.QLabel_Line_List["Set_Line_Source_Select"] )
        # __QVBoxLayout.addWidget(self.QScrollArea_Line, 1)。。..
        Line_StyleSheet = """QLabel {
                        background-color: rgba(0,0,0,0.8);
                        border-radius: 3px;
                        border: none;
                        color: #1E90FF;
            
                    }"""
        NewTime = str(time.strftime('%H:%M:%S', time.localtime(time.time())))
        # Line_Info = {"Line_Type": "Input_Line","Line_Name": f"{ NewTime}","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [28, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}
        #
        # self.QVBoxLayout_Line.addWidget(self.Set_Line_Audio_Address({"Line_Type": "Input_Line","Line_Name": f"{ NewTime}","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [28, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}))
        #
        # self.QScrollArea_Line.ensureWidgetVisible(new_line)

        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)
        __QVBoxLayout.addWidget(self.Set_Line_Source_Select({"Line_Type": "Input_Line","Line_Name": "音频目标文件","Line_Content": f"雷达【沙河小区3栋9楼303】，暂时没有数据","Line_Size": [48, 288],"Line_StyleSheet":Line_StyleSheet , "Line_Parameter": {},}),0)





    def Set_Line_Source_Select(self,Line_Info):

        self.QButtonGroup_Source = QtWidgets.QButtonGroup(self)
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 10, 0)
        __QHBoxLayout.setSpacing(12)

        # 名称
        QLabel_Name = QtWidgets.QLabel("视频来源")
        QLabel_Name.setFixedWidth(120)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Name.setStyleSheet("background: transparent;")
        __QHBoxLayout.addWidget(QLabel_Name, 1)


        for i, Option in enumerate(["前端数源", "网络视频", "本地视频", "其他来源"]):
            Radio = QRadioButton_TechtStyleSheet(Option)
            Radio.setStyleSheet("padding:0px; margin-top: 0px;")
            Radio.setFixedHeight(68)
            self.QButtonGroup_Source.addButton(Radio, i)
            __QHBoxLayout.addWidget(Radio)
        if self.QButtonGroup_Source.buttons():
            self.QButtonGroup_Source.buttons()[0].setChecked(True)
        self.QButtonGroup_Source.idClicked.connect(self.Toggle_Change_Source)
        return __QLabel

    def Set_Line_Source_Address(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 10, 0)
        __QHBoxLayout.setSpacing(12)

        # 名称
        QLabel_Name = QtWidgets.QLabel("视频地址")
        QLabel_Name.setFixedWidth(120)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Name.setStyleSheet("background: transparent;")
        __QHBoxLayout.addWidget(QLabel_Name, 1)

        self.QLineEdit_SourceAddress = QtWidgets.QLineEdit()
        # __QLineEdit.setText("rtsp://admin:csc888888!@**************:554/Streaming/Channels/101")
        self.QLineEdit_SourceAddress.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        self.QLineEdit_SourceAddress.setStyleSheet("""
    QLineEdit {
        background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
        border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
        border-radius:3px;                            /* 圆角 */
        padding: 6px 10px;                             /* 内边距 */
        color: #e0e0e0;                                /* 文字颜色 */
        font-family: "Microsoft YaHei";
        font-size: 13px;
        selection-background-color: #000000;          /* 选中文本背景 */
        selection-color: #ffffff;                     /* 选中文本颜色 */
    }

    /* 鼠标悬停状态 */
    QLineEdit:hover {
        border: 1px solid rgba(76, 201, 240, 0.7);
        background-color: rgba(255, 255, 255, 0.12);
           selection-background-color: #000000;          /* 选中文本背景 */
        selection-color: #ffffff;                     /* 选中文本颜色 */
        color: #000000; 
    }

    /* 聚焦状态（键盘输入时） */
    QLineEdit:focus {
        border: 1px solid #4cc9f0;
        background-color: rgba(255, 255, 255, 0.15);
    }

    /* 占位提示文字 */
    QLineEdit[placeholderText] {
        color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
    }
""")
        self.QLineEdit_SourceAddress.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,  # 水平方向扩展
            QtWidgets.QSizePolicy.Fixed  # 垂直方向固定
        )
        __QHBoxLayout.addWidget(self.QLineEdit_SourceAddress)

        return __QLabel

    def Set_Line_Source_Token(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 10, 0)
        __QHBoxLayout.setSpacing(12)

        # 名称
        QLabel_Name = QtWidgets.QLabel("前端授权")
        QLabel_Name.setFixedWidth(120)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Name.setStyleSheet("background: transparent;")
        __QHBoxLayout.addWidget(QLabel_Name, 1)

        __QLineEdit = QtWidgets.QLineEdit()
        __QLineEdit.setStyleSheet("""
            QLineEdit {
                background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
                border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
                border-radius:3px;                            /* 圆角 */
                padding: 6px 10px;                             /* 内边距 */
                color: #000000;                                /* 文字颜色 */
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #4cc9f0;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
            }

            /* 鼠标悬停状态 */
            QLineEdit:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
            }

            /* 聚焦状态（键盘输入时） */
            QLineEdit:focus {
                border: 1px solid #4cc9f0;
                background-color: rgba(255, 255, 255, 0.15);
            }

            /* 占位提示文字 */
            QLineEdit[placeholderText] {
                color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
            }
        """)
        __QLineEdit.setPlaceholderText("请输入前端Token")
        __QLineEdit.setText("")
        __QLineEdit.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,  # 水平方向扩展
            QtWidgets.QSizePolicy.Fixed  # 垂直方向固定
        )
        __QHBoxLayout.addWidget(__QLineEdit)

        return __QLabel

    def Set_Line_Source_OpenFile(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 10, 0)
        __QHBoxLayout.setSpacing(12)

        # 名称
        QLabel_Name = QLabel_Click()
        QLabel_Name.setFixedWidth(120)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Name.setStyleSheet("background: transparent;")
        QLabel_Name.setText("本地文件")
        QLabel_Name.clicked.connect(self.Open_LocalSource)
        __QHBoxLayout.addWidget(QLabel_Name, 1)

        self.QLineEdit_LocalSource = QtWidgets.QLineEdit()
        self.QLineEdit_LocalSource.setStyleSheet("""
            QLineEdit {
                background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
                border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
                border-radius:3px;                            /* 圆角 */
                padding: 6px 10px;                             /* 内边距 */
                color: #000000;                                /* 文字颜色 */
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #4cc9f0;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
            }

            /* 鼠标悬停状态 */
            QLineEdit:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
            }

            /* 聚焦状态（键盘输入时） */
            QLineEdit:focus {
                border: 1px solid #4cc9f0;
                background-color: rgba(255, 255, 255, 0.15);
            }

            /* 占位提示文字 */
            QLineEdit[placeholderText] {
                color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
            }
        """)
        self.QLineEdit_LocalSource.setPlaceholderText("可以拖入文件播放")
        self.QLineEdit_LocalSource.setText("")
        self.QLineEdit_LocalSource.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,  # 水平方向扩展
            QtWidgets.QSizePolicy.Fixed  # 垂直方向固定
        )
        __QHBoxLayout.addWidget(self.QLineEdit_LocalSource)

        return __QLabel

    # def Toggle_LoaclFile(self):
    def Toggle_Change_Source(self,ID):
        print("Toggle_Change_Source")
        # print(ID)
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Address"] )
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"] )
        StyleSheet_Line = """QLabel {
                                            background-color: rgba(255,255,255,0.8);
                                            border-radius: 6px;
                                            border: none;
                                        }
                                        QLabel:hover {
                                            background-color: rgba(255,255,255,0.3);
                                            border: none;
                                        }
                                    """
        Line_Info = {"Line_Name": "视频来源", "Line_Size": [48, 528], "Line_Status": "未启动","Line_Icon": {"Icon": "mdi.server-security", "Size": 0.8}, "Line_StyleSheet": StyleSheet_Line}
        while self.QVBoxLayout_Line.count():
            item = self.QVBoxLayout_Line.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        match ID:
            case 0:
                self.Play_Type ="Sentinel"
                self.QVBoxLayout_Line.addWidget(self.Set_Line_Source_Address(Line_Info))
                self.QVBoxLayout_Line.addWidget(self.Set_Line_Source_Token(Line_Info))
            case 1:
                self.Play_Type = "Internet"
                self.QVBoxLayout_Line.addWidget(self.Set_Line_Source_Address(Line_Info))
            case 2:
                self.Play_Type = "Local"
                self.QVBoxLayout_Line.addWidget(self.Set_Line_Source_OpenFile(Line_Info))
            case 3:pass
            case _:print("unknown")


    def Open_LocalSource(self):
        print("Open_LocalSource")
        file_dialog = QtWidgets.QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择音视频文件",
            "",
            "音视频文件 (*.mp4 *.avi *.wav *.mp3 *.ogg *.flac *.aac *.m4a)"
        )

        if file_path:
            self.Play_Source = file_path
            self.QLineEdit_LocalSource.setText(file_path)


            print(file_path)





    def Set_Bottom(self):
        __QHBoxLayout = QtWidgets.QHBoxLayout( self.QLabel_Bottom)
        __QHBoxLayout.setSpacing(8)  # 内边界
        __QHBoxLayout.setAlignment(QtCore.Qt.AlignRight)
        __QHBoxLayout.setContentsMargins(0, 0, 38,0)  # 外边
        StyleSheet_QLabel = """QLabel {
                                               background-color: rgba(255,255,255,0.8);
                                               border-radius: 6px;
                                               border: none;
                                           }
                                           QLabel:hover {
                                               background-color: rgba(255,255,255,0.3);
                                               border: none;
                                           }
                                       """

        QLabel_Yes = QLabel_Click()
        QLabel_Yes.setFixedSize(80,30)
        QLabel_Yes.setStyleSheet(StyleSheet_QLabel)
        QLabel_Yes.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_Yes.setText("确定")
        QLabel_Yes.clicked.connect(lambda: self.Dialog_Emit())



        QLabel_No = QLabel_Click()
        QLabel_No.setFixedSize(80,30)
        QLabel_No.setStyleSheet(StyleSheet_QLabel)
        QLabel_No.setAlignment(QtCore.Qt.AlignCenter)
        QLabel_No.setText("取消")
        QLabel_No.clicked.connect(lambda: self.Dialog_Close())
        __QHBoxLayout.contentsRect()
        __QHBoxLayout.addWidget(QLabel_Yes,)
        __QHBoxLayout.addWidget(QLabel_No,)

    def Dialog_Close(self):
        self.deleteLater()  # 安排销毁窗口
        self.close()  # 关闭窗口


    def Dialog_Emit(self):
        # print("Dialog_Emit")
        "rtsp://admin:csc888888!@**************:554/Streaming/Channels/101"
        if self.Play_Type=="Sentinel":
            self.Play_Source =  self.QLineEdit_SourceAddress.text()

        Dialog_Info={"Play_Type":self.Play_Type,"Play_Source":self.Play_Source,"Play_Channel":self.Dialog_Info["Channel_ID"]}
        self.Signal_Result.emit({"Command":"Dialog_Emit","Dialog_Info":Dialog_Info})
        self.Dialog_Close()


    def dragEnterEvent(self, event:QtGui.QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()


    def dropEvent(self, event: QtGui.QDropEvent):
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            self.Play_Source = file_path
            self.QLineEdit_LocalSource.setText(f"拖拽的文件: {file_path}")
        event.acceptProposedAction()


class QLabel_Click(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class QCheckBox_AnimatedToggle(QtWidgets.QCheckBox):

    _transparent_pen = QtGui.QPen(QtCore.Qt.transparent)
    _light_grey_pen = QtGui.QPen(QtCore.Qt.lightGray)

    def __init__(self,
        parent=None,
        bar_color=QtCore.Qt.gray,
        checked_color="#00B0FF",
        handle_color=QtCore.Qt.white,
        pulse_unchecked_color="#44999999",
        pulse_checked_color="#4400B0EE"
        ):
        super().__init__(parent)

        # Save our properties on the object via self, so we can access them later
        # in the paintEvent.
        self._bar_brush = QtGui.QBrush(bar_color)
        self._bar_checked_brush = QtGui.QBrush(QtGui.QColor(checked_color).lighter())

        self._handle_brush = QtGui.QBrush(handle_color)
        self._handle_checked_brush = QtGui.QBrush(QtGui.QColor(checked_color))

        self._pulse_unchecked_animation = QtGui.QBrush(QtGui.QColor(pulse_unchecked_color))
        self._pulse_checked_animation = QtGui.QBrush(QtGui.QColor(pulse_checked_color))

        # Setup the rest of the widget.

        self.setContentsMargins(8, 0, 8, 0)
        self._handle_position = 0

        self._pulse_radius = 0

        self.animation = QtCore.QPropertyAnimation(self, b"handle_position", self)
        self.animation.setEasingCurve(QtCore.QEasingCurve.InOutCubic)
        self.animation.setDuration(200)  # time in ms

        self.pulse_anim = QtCore.QPropertyAnimation(self, b"pulse_radius", self)
        self.pulse_anim.setDuration(350)  # time in ms
        self.pulse_anim.setStartValue(10)
        self.pulse_anim.setEndValue(20)

        self.animations_group = QtCore.QSequentialAnimationGroup()
        self.animations_group.addAnimation(self.animation)
        self.animations_group.addAnimation(self.pulse_anim)

        self.stateChanged.connect(self.setup_animation)

    def sizeHint(self):
        return QtCore.QSize(58, 45)

    def hitButton(self, pos: QtCore.QPoint):
        return self.contentsRect().contains(pos)

    @Slot(int)
    def setup_animation(self, value):
        self.animations_group.stop()
        if value:
            self.animation.setEndValue(1)
        else:
            self.animation.setEndValue(0)
        self.animations_group.start()

    def paintEvent(self, e: QtGui.QPaintEvent):

        contRect = self.contentsRect()
        handleRadius = round(0.24 * contRect.height())

        p = QtGui.QPainter(self)
        p.setRenderHint(QtGui.QPainter.Antialiasing)

        p.setPen(self._transparent_pen)
        barRect = QtCore.QRectF(
            0, 0,
            contRect.width() - handleRadius, 0.40 * contRect.height()
        )
        barRect.moveCenter(contRect.center())
        rounding = barRect.height() / 2

        # the handle will move along this line
        trailLength = contRect.width() - 2 * handleRadius

        xPos = contRect.x() + handleRadius + trailLength * self._handle_position

        if self.pulse_anim.state() == QtCore.QPropertyAnimation.Running:
            p.setBrush(
                self._pulse_checked_animation if
                self.isChecked() else self._pulse_unchecked_animation)
            p.drawEllipse(QtCore.QPointF(xPos, barRect.center().y()),
                          self._pulse_radius, self._pulse_radius)

        if self.isChecked():
            p.setBrush(self._bar_checked_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setBrush(self._handle_checked_brush)

        else:
            p.setBrush(self._bar_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setPen(self._light_grey_pen)
            p.setBrush(self._handle_brush)

        p.drawEllipse(
            QtCore.QPointF(xPos, barRect.center().y()),
            handleRadius, handleRadius)

        p.end()

    @Property(float)
    def handle_position(self):
        return self._handle_position

    @handle_position.setter
    def handle_position(self, pos):
        """change the property
        we need to trigger QWidget.update() method, either by:
            1- calling it here [ what we're doing ].
            2- connecting the QPropertyAnimation.valueChanged() signal to it.
        """
        self._handle_position = pos
        self.update()

    @Property(float)
    def pulse_radius(self):
        return self._pulse_radius

    @pulse_radius.setter
    def pulse_radius(self, pos):
        self._pulse_radius = pos
        self.update()


class QRadioButton_TechtStyleSheet(QtWidgets.QRadioButton):
    """科技感单选按钮"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QRadioButton {
                color: #e9ecef;
                font-size: 14px;
                spacing: 8px;
                padding: 0px;
                margin: 0px;
    
            }
            QRadioButton::indicator {
                width: 0px;
                height: 0px;
            }
        """)
        # self.setFixedHeight(30)
        self.setCursor(QtCore.Qt.PointingHandCursor)

        # 自定义指示器
        self.indicator_size = QtCore.QSize(15, 15)
        self.checked_color = QtGui.QColor(0, 195, 255)  # 科技蓝
        self.unchecked_color = QtGui.QColor(100, 100, 100)  # 深灰

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 计算指示器位置
        x = 3
        y = (self.height() - self.indicator_size.height()) // 3

        # 绘制外环
        pen = QtGui.QPen(self.unchecked_color if not self.isChecked() else self.checked_color)
        pen.setWidth(2)
        painter.setPen(pen)
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.drawEllipse(x, y, self.indicator_size.width(), self.indicator_size.height())

        # 绘制内圆（选中状态）
        if self.isChecked():
            painter.setBrush(QtGui.QBrush(self.checked_color))
            painter.drawEllipse(x +3, y + 3, 9, 9)

        # 绘制文本
        painter.setPen(QtGui.QColor("#000000"))
        painter.drawText(x + 20, y + 12, self.text())

class QScrollArea_Elastic(QtWidgets.QScrollArea):
    """支持弹性滚动的 QScrollArea"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)  # 垂直滚动条按需显示
        self.setStyleSheet("QScrollArea { border: none; }")

        # 启用触摸惯性滚动（Qt5/6 自带）
        self.setAttribute(QtCore.Qt.WA_AcceptTouchEvents)
        QtWidgets.QScroller.grabGesture(self.viewport(), QtWidgets.QScroller.LeftMouseButtonGesture)

    def setWidget(self, widget):
        super().setWidget(widget)
        # 让内容在垂直方向也能“撑开”
        widget.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)