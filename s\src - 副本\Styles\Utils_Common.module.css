

/* ---------------------------------------------------------------------------------------- 弹窗会话 */
 .Dialog_Common {
    position: fixed;
    top: 0;
    left: 0;
    /* transform: translate(-50%, -50%) scale(0); 初始缩放为 0 */
    width:100vw;
    height: 80vh;
    background-color: #000000;
    border-radius: 8px;
    box-shadow: 0px 0px 5px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease-in-out; /* 动画效果 */
    z-index: 1000;
    visibility: hidden; /* 默认不可见 */
    opacity: 0; /* 初始透明度为 0 */
    
  }
  
  
  .Dialog_Common.Open {
    visibility: visible; /* 显示对话框 */
    opacity: 1; /* 透明度变为 1 */
    /* transform: translate(-50%, -50%) scale(1); 缩放恢复到 1 */
  }
  
  .Dialog_Content {
    padding: 20px;
    text-align: center;
  }



  .Dialog_Common_Langer {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0); /* 初始缩放为 0 */
    width: 70vw;
    height: 70vh;
    background-color: #000000;
    border-radius: 8px;
    box-shadow: 0px 0px 5px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease-in-out; /* 动画效果 */
    z-index: 1000;
    visibility: hidden; /* 默认不可见 */
    opacity: 0; /* 初始透明度为 0 */
    
  }
  
  
  .Dialog_Common_Langer.Open {
    visibility: visible; /* 显示对话框 */
    opacity: 1; /* 透明度变为 1 */
    transform: translate(-50%, -50%) scale(1); /* 缩放恢复到 1 */
  }
  









  .Dialog_Float_Btn{
    background-color: rgba(255,255,255,0.1) !important;
    transition: background-color 0.3s;
  }



  /* 移除原生按钮效果 */
  .custom_white_tabs {
    color: rgba(255,255,255,0.85) !important;
    transition: color 0.3s;
  }



  .ant-tabs-tab-btn {
    color: white !important;
  }

  .ant-tabs-ink-bar {
    background: #1890ff !important;
  }





  
  /* * 容器样式 */ 
  .tabsContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Tab 标题按钮容器 */
  .tabHeader {
    display: flex;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ccc;
    padding: 2px;
  }

  /* Tab 按钮样式 */
  .tabButton {
    width: 38px;
    height: 20px;
    padding: 5px 10px;
    font-size: 8px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-radius: 4px;
    margin-right: 2px;
    transition: background-color 0.3s ease;
  }

  /* 激活的 Tab 按钮样式 */
  .tabButton.active {
    background-color: #1890ff;
    color: white;
  }

  /* Tab 内容区域样式 */
  .tabContent {
    flex: 1;
    padding: 20px;
    background-color: #fff;
    overflow-y: auto; /* 如果内容过多，允许滚动 */
  }



  /* 在你的 CSS 文件中 */
  /* .custom_select .ant-select-selector{
    background-color: #000000 !important;
  } */

  /* .custom-select .ant-select-selector {
    border: 0px;
    background-color: #f0f8ff !important;
  } */
  /* .ant-select-selector{
    background-color: #8c4949 !important;
    border-color: #333333 !important;
    color: #fff !important;
  }
  .ant-select-selection-item{
    background-color: #8c4949 !important;
    border-color: #333333 !important;
    color: #fff !important;
  } */

  /* .ant-select-selector {
    background-color: white !important;
    border-color: transparent !important;
    color: black !important;
  } */

  /* .ant-select-selector {
    border: 0px !important;
    background: #d41010 !important;
  }

  .DoubleArrowSelector .ant-select-selector{
      border: 1px solid #3ccc1f !important;
  }

  .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
    border: 1px solid #ff0000 !important; 
  } */
  /* .ant-select.ant-select-sm.ant-select-outlined.custom-double-select .ant-select-selector {
    border: 0;
  } */
  /* .custom-double-select{ */
  /* .custom-double-select { */
    /* border: 2px solid #ff0000 !important;
    border-radius: 8px !important; */
    /* text-align: center; */
    /* background-color: aqua; */
  /* } */

  /* .custom-double-select .ant-select-selection-item {
    text-align: center;
  }

  .custom-double-select{
    height: 300px;
    background-color: red;
  }

  .custom-double-select .ant-select-selector {
    border: 2px solid #ff0000; 
    border-radius: 8px; 
  } */
