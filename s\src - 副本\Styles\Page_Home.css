/* 首页样式 - 使用默认背景 */

/* 圆角样式 */
.radius-15 {
  border-radius: 15px !important;
}

/* 统计卡片样式 */
.stats-card {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
  pointer-events: none;
}

.stats-card:hover::before {
  left: 100%;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(18, 150, 219, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(18, 150, 219, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(18, 150, 219, 0.3);
  }
}

/* 卡片进入动画 */
.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) {
  animation-delay: 0.1s;
}

.stats-card:nth-child(2) {
  animation-delay: 0.2s;
}

.stats-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stats-card:nth-child(4) {
  animation-delay: 0.4s;
}

/* 数字计数动画 */
.count-number {
  animation: slideInRight 0.8s ease-out;
}

/* 图标旋转效果 */
.stats-card svg {
  transition: transform 0.3s ease;
}

.stats-card:hover svg {
  transform: rotate(5deg) scale(1.1);
}

/* 滚动容器样式 */
#scroll-container {
  height: 400px;
  overflow: hidden;
  position: relative;
}

#scroll-content {
  position: absolute;
  top: 0;
}

.item {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  padding-right: 10px;
  margin-bottom: 20px;
  animation: fadeInUp 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 16px;
  }

  .stats-card .ant-typography {
    font-size: 1.8rem !important;
  }
}

/* 深色主题优化 */
.stats-card .ant-card-body {
  background: transparent;
  border: none;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(45deg, #1296db, #4facfe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 百分比标签样式 */
.percentage-badge {
  position: relative;
  overflow: hidden;
}

.percentage-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

.percentage-badge:hover::before {
  left: 100%;
}
