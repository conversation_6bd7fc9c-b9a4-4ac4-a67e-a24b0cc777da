<!DOCTYPE html>
<html lang="en">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<title>哨兵导控</title>
	<!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!-- loader-->
	<link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
	<link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
	<link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
	<link rel="stylesheet" href="/static/CSS/app.css" />
	<link rel="stylesheet" href="/static/CSS/login.css" />
</head>

<body class="bg-theme bg-theme1">
	<!-- wrapper -->
	<div class="wrapper">
		<div class="authentication-forgot d-flex align-items-center justify-content-center">
			<div class="card shadow-lg forgot-box">
				<div class="card-body p-md-5">
                    <div class="text-center">
						<img src="/static/Images/Logo.png" class="log_status" width="80" alt="" />
					</div>
					<h3 class="mt-4 font-weight-bold" style="text-align: center;">哨兵导控</h3>
	
                    <div class="form-group mt-4">
                        <label>登录账号</label>
                        <input type="text" class="form-control" id="username" placeholder="请输入登录账号" />
                    </div>
                    <div class="form-group">
                        <label>登录密码</label>
                        <input type="password" class="form-control" id="password" placeholder="请输入登录密码" />
                    </div>
                    <div class="btn-group mt-3 w-100">
                        <button type="button" class="btn btn-light btn-block" id="CSC_Login">Log In</button>
                        <button type="button" class="btn btn-light" id="CSC_Login_Icon"><i class="lni lni-arrow-right"></i>
                        </button>
                    </div>
				</div>
			</div>
		</div>
	</div>
	<!-- end wrapper -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <script src="/static/JavaScript/pace.min.js"></script>
	<script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Login.js"></script>
	<script>
        function Page_Init(){
            let Login_Out = localStorage.getItem('Login_Out');
			if (Login_Out === 'Cookie Expire') {
				Lobibox.notify('warning', {
					pauseDelayOnHover: true,
					size: 'mini',
					rounded: true,
					delayIndicator: false,
					continueDelayOnInactiveTab: false,
					position: 'top right',
					msg: '登录过期，请重新登录！'
				});
			};
		};

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>