// Component_FullScreenExitButton.tsx
import { FullscreenExitOutlined } from '@ant-design/icons'
import styles from '@/Styles/FullScreenExitButton.module.css'

interface Props {
  onClick: () => void
}

const FullScreenExitButton = ({ onClick }: Props) => {
  return (
    <div className="fullscreen-exit-button">
      <div className="fullscreen-button-content" onClick={onClick}>
        {/* <FullscreenExitOutlined className={styles.icon} /> */}
        <span className={styles.textSpan}>退出全屏</span>
        <div className={styles.iconWrapper}>
          <FullscreenExitOutlined className={styles.icon} />
        </div>
        
      </div>
    </div>
  )
}

export default FullScreenExitButton