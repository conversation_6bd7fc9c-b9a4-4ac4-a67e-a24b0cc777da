import React from 'react';
import { Link } from 'react-router-dom';
import <PERSON><PERSON> from 'lottie-react';

// ✅ 导入本地 Lottie 动画 JSON 文件
import NOT_FOUND_ANIMATION from '@/Assets/CSS/404.json';

const Page404: React.FC = () => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      backgroundColor: '#f8f9fa',
      textAlign: 'center'
    }}>
      <h1 style={{ fontSize: '5rem', fontWeight: 'bold', color: '#dc3545' }}>404</h1>
      <p>您访问的页面不存在。</p>

      {/* ✅ 使用本地导入的 Lottie 动画 */}
      <Lottie
        animationData={NOT_FOUND_ANIMATION}
        style={{ width: 300, height: 300 }}
      />

      <Link to="/Page_Home" style={{
        marginTop: '2rem',
        padding: '12px 24px',
        backgroundColor: '#007bff',
        color: 'white',
        borderRadius: '5px',
        textDecoration: 'none'
      }}>
        返回首页
      </Link>
    </div>
  );
};

export default Page404;