import React from 'react';
import * as Icon from '@ant-design/icons';
import { Layout, Menu } from 'antd';
import { useMenuList  } from '@/Core/Core_Router'
import { useNavigate  } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import './index.css'

const { Sider } = Layout;

type MenuItem = {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: MenuItem[];
};

const Aside = (props: { collapsed: boolean }) => {
  const menuList = useMenuList();
  // console.log('权限清洗后的菜单列表:',menuList)
  const { collapsed } = props;
  const navigate = useNavigate();
  // 点击菜单项触发路由跳转
  const clickMenu = (e: { key: string }) => {
    // console.log('key:', e.key,e)
    navigate(e.key);
  };

  /**
   * 判断当前打开的界面 然后刷新下菜单栏的高亮和展开
   */
  const location = useLocation();
  const selectedKey = location.pathname;
  // console.log('selectedKey:',selectedKey)

  // 查找当前选中菜单的父级 key
  const findParentKey = (menuItems: MenuItem[], targetKey: string): string | null => {
    for (const item of menuItems) {
      if (item.children) {
        const found = item.children.some(child => child.key === targetKey);
        if (found) return item.key;
      }
    }
    return null;
  };

  const parentKey = findParentKey(menuList, selectedKey);
  const [openKeys, setOpenKeys] = React.useState<string[]>(parentKey ? [parentKey] : []);

  // 动态获取 icon
  const iconToElement = (icon: React.ReactNode | string) => {
    if (React.isValidElement(icon)) {
      return icon; // 如果已经是 JSX 元素，直接返回
    }
    if (typeof icon === 'string') {
      const IconComponent = (Icon as Record<string, any>)[icon];
      return IconComponent ? <IconComponent /> : null;
    }
    return null;
  };

  // 菜单数据处理
  const getItems = (items: MenuItem[], list: MenuItem[] = []): MenuItem[] => {
    items.forEach((item) => {
      list.push({
        key: item.key,
        ...(item.icon ? { icon: iconToElement(item?.icon) } : {}),
        label: item.label,
        ...(item.children ? { children: getItems(item.children) } : {}),
      });
    });
    return list;
  };

  return (
    <Sider trigger={null} collapsible collapsed={collapsed} style={{ height: '100vh' }} className="aside-container">
      <h3 className="app-name">
        {collapsed ? (
          <img src="/favicon.ico" alt="Menu Fold" style={{ width: '28px', height: '28px', color: '#fff', verticalAlign: 'middle', }} />
        ) : (
          <>
            <img src="/favicon.ico" alt="Menu Fold" style={{ width: '28px', height: '28px', marginRight: '8px', color: '#fff', verticalAlign: 'middle', }} />
            哨兵验证平台
          </>
        )}
      </h3>
      <Menu
        theme="dark"
        mode="inline"
        // defaultSelectedKeys={['/Page_Home']}
        selectedKeys={[selectedKey]}
        openKeys={openKeys}
        onOpenChange={(keys) => setOpenKeys(keys as string[])}
        items={getItems(menuList)}
        // style={{
        //   height: '100%',
        // }}
        onClick={clickMenu}
      />
    </Sider>
  );
};

export default Aside;