#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入问题修复脚本
用于检查和修复PyInstaller打包时的模块导入问题
"""

import os
import sys
import shutil


def check_and_fix_imports():
    """检查并修复导入问题"""

    project_root = r'D:\Sentinel_Foundation'

    # 1. 检查所有关键文件是否存在
    critical_files = [
        'Bin/Utils/Sentinel/Core_Sentinel.py',
        'Bin/Utils/Sentinel/Utils/Service_Html/Service_Html_Element.py',
        'Bin/Utils/Function/Function_Core_Config.py',
        'Bin/Utils/Function/Function_Core_Router.py',
        'Bin/Utils/Toolkit/Service_Print.py',
        'Bin/System/OS/Component/Component_Common.py',
        'Bin/System/OS/Component/Component_VideoPlay.py',
    ]

    print("=== 检查关键文件 ===")
    for file_path in critical_files:
        full_path = os.path.join(project_root, file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 文件不存在！")

    # 2. 检查__init__.py文件
    init_dirs = [
        'Bin',
        'Bin/Utils',
        'Bin/Utils/Toolkit',
        'Bin/Utils/Sentinel',
        'Bin/Utils/Sentinel/Utils',
        'Bin/Utils/Sentinel/Utils/Service_Html',
        'Bin/Utils/Function',
        'Bin/System',
        'Bin/System/OS',
        'Bin/System/OS/Component',
        'Bin/System/OS/Page',
        'Bin/System/OS/Page/Media',
        'Bin/System/OS/Utils',
        'Bin/System/OS/Windows',
        'Bin/Resource',
        'Bin/Resource/CSS',
    ]

    print("\n=== 检查__init__.py文件 ===")
    for dir_path in init_dirs:
        full_dir = os.path.join(project_root, dir_path)
        init_file = os.path.join(full_dir, '__init__.py')

        if os.path.exists(full_dir):
            if os.path.exists(init_file):
                print(f"✓ {dir_path}/__init__.py")
            else:
                print(f"✗ {dir_path}/__init__.py - 创建中...")
                # 创建__init__.py文件
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write('# -*- coding: utf-8 -*-\n')
                print(f"✓ 已创建 {dir_path}/__init__.py")
        else:
            print(f"✗ {dir_path} - 目录不存在！")

    # 3. 创建符号链接或复制关键模块到根目录
    print("\n=== 创建模块副本 ===")
    modules_to_copy = [
        ('Bin/Utils/Sentinel/Core_Sentinel.py', 'Core_Sentinel.py'),
        ('Bin/Utils/Sentinel/Utils/Service_Html/Service_Html_Element.py', 'Service_Html_Element.py'),
        ('Bin/Utils/Function/Function_Core_Config.py', 'Function_Core_Config.py'),
        ('Bin/Utils/Function/Function_Core_Router.py', 'Function_Core_Router.py'),
        ('Bin/Utils/Toolkit/Service_Print.py', 'Service_Print.py'),
        ('Bin/System/OS/Component/Component_Common.py', 'Component_Common.py'),
        ('Bin/System/OS/Component/Component_VideoPlay.py', 'Component_VideoPlay.py'),
    ]

    for src, dst in modules_to_copy:
        src_path = os.path.join(project_root, src)
        dst_path = os.path.join(project_root, dst)

        if os.path.exists(src_path):
            try:
                shutil.copy2(src_path, dst_path)
                print(f"✓ 复制 {src} -> {dst}")
            except Exception as e:
                print(f"✗ 复制失败 {src}: {e}")
        else:
            print(f"✗ 源文件不存在: {src}")

    # 4. 检查Python路径
    print("\n=== 检查Python路径 ===")
    print(f"Python可执行文件: {sys.executable}")
    print(f"Python版本: {sys.version}")
    print("Python路径:")
    for path in sys.path:
        print(f"  {path}")

    # 5. 测试导入
    print("\n=== 测试导入 ===")
    sys.path.insert(0, project_root)

    test_imports = [
        'Core_Sentinel',
        'Service_Html_Element',
        'Function_Core_Config',
        'Function_Core_Router',
        'Service_Print',
        'Component_Common',
        'Component_VideoPlay',
    ]

    for module in test_imports:
        try:
            __import__(module)
            print(f"✓ 成功导入: {module}")
        except ImportError as e:
            print(f"✗ 导入失败: {module} - {e}")

    print("\n=== 修复完成 ===")
    print("请重新运行PyInstaller打包命令。")


if __name__ == '__main__':
    check_and_fix_imports()