
.server_status {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid #2b92d4;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:server;
	-webkit-animation-duration:2700ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}

@-webkit-keyframes server {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(255,255,255,0.1);
}
100% {
	opacity:1;
	border:1px solid rgba(59,235,235,1);
	box-shadow:0 1px 30px rgba(59,255,255,1);
}
}

.alert_status {
    position:relative;
    /* background-color:transparent !important; */
    border-radius: 50%;
	line-height:40px;
	border:1px solid #c7f34e;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#f1e1d3),to(#d6f77b));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:alert;
	-webkit-animation-duration:1100ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}


@-webkit-keyframes alert {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(233, 247, 36, 0.1);
}
100% {
	opacity:1;
	border:1px solid rgb(202, 226, 63);
	box-shadow:0 1px 30px rgb(206, 243, 104);
}
}



.failed_status {
	position:relative;
    border-radius: 50%;
	line-height:40px;
	border:1px solid #e90e0e;
	color:#fff;
	font-size:20px;
	text-align:center;
	cursor:pointer;
	box-shadow:0 1px 2px rgba(0,0,0,.3);
	overflow:hidden;
    /* background-image:-webkit-gradient(linear,left top,left bottom,from(#6cc3fe),to(#21a1d0)); */
    background-image:-webkit-gradient(linear,left top,left bottom,from(#f5760e),to(#f10f0f));
	-webkit-animation-timing-function:ease-in-out;
	-webkit-animation-name:failed;
	-webkit-animation-duration:100ms;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-direction:alternate;
	
	
}



@-webkit-keyframes failed {
	0% {
	opacity:.2;
	box-shadow:0 1px 2px rgba(248, 10, 30, 0.1);
}
100% {
	opacity:1;
	border:1px solid rgb(243, 148, 84);
	box-shadow:0 1px 30px rgb(240, 8, 8);
}
}









.server_status_btn{
    
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: cyan
}
.alert_status_btn{
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: rgb(190, 235, 132)
  
}
.failed_status_btn{
    -webkit-tap-highlight-color: transparent;
    box-sizing: inherit;
    -webkit-font-smoothing: antialiased;
    overflow: visible;
    touch-action: manipulation;
    margin: 0;
    font-size: 8px;
    -webkit-appearance: button;
    font-family: Nunito,sans-serif;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s,color .3s;
    background-color: rgba(255,255,255,.08);
    color: rgb(241, 12, 12)
}






.spider_log{
    float: left;
   /* position: absolute; */
   padding:1px;
   border:1px groove white;
   margin-top:0px;
   margin-left:0px;
   border-radius:50%;
   width:60px;
   height:60px;
   filter:progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=4);/*ie*/
   -moz-box-shadow: 2px #909090;/*firefox*/
   -webkit-box-shadow: 2px  #fefeff;/*safari或chrome*/
   box-shadow:0 0 5px #4E4E4E ;/*opera或ie9*/
}   
