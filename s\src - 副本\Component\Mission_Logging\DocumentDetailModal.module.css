.loadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
  }
  
  .loadingContainer {
    text-align: center;
  }
  
  .spinner {
    width: 48px;
    height: 48px;
    border: 5px solid #f0f0f0;
    border-top: 5px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s infinite linear;
    margin: 0 auto;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }