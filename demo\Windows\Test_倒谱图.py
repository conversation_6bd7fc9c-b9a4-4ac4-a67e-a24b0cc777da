import sys
# import pydub
from PySide6.QtWidgets import <PERSON>A<PERSON>lication, QMainWindow, QPushButton, QFileDialog, QVBoxLayout
from PySide6.QtMultimedia import QMediaPlayer
from PySide6.QtCore import QUrl

class AudioPlayer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Audio Player")
        self.setGeometry(100, 100, 400, 300)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()
        btn_load = QPushButton("Load Audio File")
        btn_load.clicked.connect(self.load_audio)
        layout.addWidget(btn_load)
        self.setLayout(layout)

    def load_audio(self):
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.mp3)")
        if file_path:
            self.media_player = QMediaPlayer()
            self.media_player.setSource(QUrl.fromLocalFile(file_path))
            self.media_player.setVolume(50)
            self.media_player.play()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    player = AudioPlayer()
    player.show()
    sys.exit(app.exec())