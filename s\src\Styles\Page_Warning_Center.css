.warning-center {
  padding: 1rem;
  background-color: #343a40;
  min-height: calc(100vh - 70px);
  color: #e5e9ec;
  font-family: 'Open Sans', sans-serif;
}

.card {
  border: 1px solid rgba(255, 255, 255, 0.125);
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
}

.card-header {
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.125);
  color: rgba(255, 255, 255, 0.87);
}

.card-body {
  color: rgba(255, 255, 255, 0.87);
}

.filters-section {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 0.5rem;
  margin-bottom: 20px;
}

.form-label {
  color: rgba(255, 255, 255, 0.87);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.87);
}

.form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: #0075ff;
  color: rgba(255, 255, 255, 0.87);
  box-shadow: 0 0 0 0.2rem rgba(0, 117, 255, 0.25);
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-control option {
  background-color: #2c3e50;
  color: rgba(255, 255, 255, 0.87);
}

.warnings-list {
  margin-top: 20px;
}

.warning-item {
  border: 1px solid rgba(255, 255, 255, 0.125);
  background-color: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.warning-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-title {
  color: rgba(255, 255, 255, 0.87);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card-text {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.text-muted {
  color: rgba(255, 255, 255, 0.5) !important;
}

.badge {
  padding: 0.25em 0.4em;
  font-size: 0.75em;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-danger {
  color: #fff;
  background-color: #dc3545;
}

.badge-warning {
  color: #212529;
  background-color: #ffc107;
}

.badge-info {
  color: #fff;
  background-color: #17a2b8;
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}

.badge-secondary {
  color: #fff;
  background-color: #6c757d;
}

.btn-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.btn-primary {
  background-color: #0075ff;
  border-color: #0075ff;
  color: #fff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}

.btn-outline-secondary {
  color: rgba(255, 255, 255, 0.6);
  border-color: rgba(255, 255, 255, 0.2);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.87);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

.pagination {
  margin-top: 2rem;
}

.page-link {
  color: #0075ff;
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.page-link:hover {
  color: #0056b3;
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.page-item.active .page-link {
  background-color: #0075ff;
  border-color: #0075ff;
  color: #fff;
}

.page-item.disabled .page-link {
  color: rgba(255, 255, 255, 0.3);
  pointer-events: none;
  cursor: auto;
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 模态框样式 */
.modal-content {
  background-color: #2c3e50;
  border: 1px solid rgba(255, 255, 255, 0.125);
  color: rgba(255, 255, 255, 0.87);
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

.modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.125);
}

.modal-title {
  color: rgba(255, 255, 255, 0.87);
}

.close {
  color: rgba(255, 255, 255, 0.87);
  opacity: 0.8;
}

.close:hover {
  color: rgba(255, 255, 255, 0.87);
  opacity: 1;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  color: rgba(255, 255, 255, 0.87);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 动画效果 */
.warning-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal.show .modal-dialog {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .warning-center {
    padding: 15px;
  }
  
  .filters-section {
    padding: 15px;
  }
  
  .row .col-md-3 {
    margin-bottom: 15px;
  }
  
  .btn-group-vertical {
    flex-direction: row;
    gap: 3px;
  }
  
  .btn-sm {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}

@media (max-width: 576px) {
  .warning-center {
    padding: 10px;
  }
  
  .filters-section {
    padding: 10px;
  }
  
  .warning-item .row {
    flex-direction: column;
  }
  
  .warning-item .col-md-8,
  .warning-item .col-md-4 {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .btn-group-vertical {
    justify-content: center;
  }
  
  .text-right {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .warning-center {
    padding: 30px;
  }
  
  .filters-section {
    padding: 25px;
  }
}

/* 深色主题适配 */
.bg-theme.bg-theme1 .warning-center {
  color: rgba(255, 255, 255, 0.87);
}

.bg-theme.bg-theme1 .card {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.125);
}

.bg-theme.bg-theme1 .card-header {
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.125);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* 文本选择样式 */
::selection {
  background-color: rgba(0, 117, 255, 0.3);
  color: rgba(255, 255, 255, 0.87);
}

::-moz-selection {
  background-color: rgba(0, 117, 255, 0.3);
  color: rgba(255, 255, 255, 0.87);
}
