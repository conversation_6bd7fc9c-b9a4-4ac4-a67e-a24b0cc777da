var Sentiment_Account_Rumor_List = [];
var Sentiment_Subject_Rumor_List = [];
var Sentiment_Event_Rumor_List = [];
var Statistics_Data = {
    total: 0,
    positive: 0,
    neutral: 0,
    negative: 0,
    weibo: 0,
    wechat: 0,
    x<PERSON><PERSON><PERSON><PERSON>: 0
};
$('.single-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: Boolean($(this).data('allow-clear')),
});

/****************************************获取统计数据***************************************************/
function Requests_Statistics_Data() {
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token": this.User_Token,
        "data_class": "Insight",
        "data_type": 'Statistics',
        "data_methods": 'GetOverview',
        "data_argument": `{}`,
        "data_kwargs": `{}`
    };
    __Service_Requests = new Service_Requests("Async", Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Statistics_Data = Result.Data || Statistics_Data;
                updateStatisticsDisplay();
            } else {
                console.error('获取统计数据失败:', Result);
                // 使用模拟数据
                loadMockStatistics();
            }
        }).catch((err) => {
            console.error('获取统计数据网络错误:', err);
            // 使用模拟数据
            loadMockStatistics();
        });
}

/****************************************更新统计数据显示***************************************************/
function updateStatisticsDisplay() {
    // 如果元素存在才更新
    const elements = {
        'stats-total': Statistics_Data.total,
        'stats-positive': Statistics_Data.positive,
        'stats-neutral': Statistics_Data.neutral,
        'stats-negative': Statistics_Data.negative,
        'stats-weibo': Statistics_Data.weibo,
        'stats-wechat': Statistics_Data.wechat,
        'stats-xiaohongshu': Statistics_Data.xiaohongshu
    };

    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = `(${elements[id]})`;
        }
    });
}

/****************************************模拟统计数据（用于测试）***************************************************/
function loadMockStatistics() {
    Statistics_Data = {
        total: 1256,
        positive: 456,
        neutral: 500,
        negative: 300,
        weibo: 400,
        wechat: 350,
        xiaohongshu: 506
    };
    updateStatisticsDisplay();
}

/****************************************初始化请求洞察账号***************************************************/
var Source_Type_List = ['All'];
function Requests_Sentiment_Rumor_Info() {
    // Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_rumor_account_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Sentiment_Account_Rumor_List = Result.Account_List;
                Object.keys(Result.Table_Condiction_Param_Count).forEach(key => {
                    document.getElementById('Label_' + key).innerText = Result.Table_Condiction_Param_Count[key];
                });
                renderAccountPageData();
                // Loading_Hide();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    sound: false,
                    delay:2000,
                    position: 'top right',
                    msg: '网络出错，请重试！'
                });
                // Loading_Hide();
                return ;
            };
            
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            // Loading_Hide();
        });
};

/****************************************组装账号分析***************************************************/
function renderAccountPageData() {
    var container = document.getElementById('Account_Body_Element');
    container.innerHTML = ''; // 清空容器
    var element_Info = '';
    Sentiment_Account_Rumor_List.forEach(item => {
        // 临时添加一个数源项判断 ALL或者 平台被选择
        if (Source_Type_List.indexOf('All') !== -1 || Source_Type_List.indexOf(item.SOURCE_PLATFORM) !== -1) {
            //
        } else {
            return ;
        };
        var Tag_Element = '';
        for (Tag in item.Tag_List) {
            Tag_Element += `<div class="account-tag">${item.Tag_List[Tag]}</div>`;
        };
        const element = `
            <div class="col-lg-4">
                <div class="card radius-15">
                    <div class="card-body">
                        <div class="media align-items-top">
                        
                            <a href="${item.SOURCE_CASE_LINK}" target="_blank">
                                <img src="${item.SOURCE_FACE}" width="80" height="80" class="rounded-circle p-1 border bg-white" alt="" />
                            </a>
                            <div class="media-body">
                                <h5 class="ml-3">${item.SOURCE_NICKNAME}</h5>
                                <div class="list-inline contacts-social list-introduction mt-1 ml-3" title="${item.SOURCE_BIOGRAPHY}">${item.SOURCE_BIOGRAPHY}</div>
                                <p class="mt-1" style="margin-left: -80px;">
                                    <i class='bx bx-bookmark'></i>${item.SOURCE_PLATFORM}
                                    <i class='bx bx-map' style="margin-left: 10px;"></i>${item.SOURCE_IP}
                                    <i class='bx bx-show' style="margin-left: 10px;"></i>${item.SOURCE_FANS}
                                    <i class='bx bx-group' style="margin-left: 10px;"></i>${item.SOURCE_FOLLOW}
                                    <i class='bx bx-paragraph' style="margin-left: 10px;"></i>${item.SOURCE_TYPE}
                                    <i class='bx bx-anchor' style="margin-left: 10px;"></i>涉${item.SOURCE_DIRECTION}
                                </p>
                                <div class="list-inline contacts-social mt-3" style="margin-left: -85px;"> 
                                    ${Tag_Element}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`
        element_Info += element;
    });
    container.innerHTML = element_Info;
};

/*-----------------------------------动态监听媒体类型选择事件-------------------------------------------*/
const $Sentiment_Source_Type_Element = document.getElementById('Sentiment_Source_Type_Element');
const $Source_Type_1 = document.getElementById('Source_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Source_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框
        if (checkbox.id === 'Source_Type_1' && checkbox.checked) {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Source_Type_Element.querySelectorAll('.form-check-input:not(#Source_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Source_Type_List = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Source_Type_List = []; //更新筛选项
            };
        } else {
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Source_Type_1.checked = false;
                Source_Type_List.push(checkbox.value); //更新筛选项
                const index = Source_Type_List.indexOf('All');
                if (index !== -1) {
                    Source_Type_List.splice(index, 1);
                };
            } else {
                const index = Source_Type_List.indexOf(checkbox.value);
                if (index !== -1) {
                    Source_Type_List.splice(index, 1);
                };
            };
        };
        console.log('现在选择的媒体类型:',Source_Type_List); // 在控制台打印按钮文本内容
        renderAccountPageData();
    };
});

/**********************************初始化table***************************************************/
var $table = $('#Rumor_Info_Table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 5,
        render: function (data, type, row) {
            return `
                <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" >复核</button>
            `;
        },
      },
    ],
    columns: [
      { title: '序号', data: 'ID', width: '50px' },
      { title: '发帖时间', data: 'TIME', width: '150px' },
      { title: '发贴平台', data: 'PLATFORM', width: '150px'},
      { title: '文章标题', data: 'TITLE'},
      { title: '文章内容', data: 'CONTENT_SHOW'},
    ],
    // scrollY: 'calc(100vh - 320px)', // 设置表格固定高度
});

$('#profile-tab').click(function() {
    console.log('专题点击')
    if (Sentiment_Subject_Rumor_List.length === 0) {
        Reload_Table_Style();
        Requests_Sentiment_Subject_Info();
    };
});

$('#Account-tab').click(function() {
    console.log('专题点击')
    if (Sentiment_Account_Rumor_List.length === 0) {
        Requests_Sentiment_Rumor_Info();
    };
});

$('#user-tab').click(function() {
    console.log('历史舆情大事纪点击')
    if (Sentiment_Event_Rumor_List.length === 0) {
        Requests_Rumor_Event_Info();
    };
});

// 重置表格属性
function Reload_Table_Style() {
    // 弹窗表格表头和行都被添加了width:0px,统一清除
    $('#Rumor_Info_Table')[0].style.width='';
    var cells = document.querySelectorAll('#Rumor_Info_Table td, #Rumor_Info_Table th');
    cells.forEach(function(cell) {
        // console.log('cell:',cell)
        // console.log('cell:',cell.textContent)
        if (cell.textContent === '序号') {
            cell.style.width = '50px';
        } else if (cell.textContent === '发帖时间') {
            cell.style.width = '150px';
        } else if (cell.textContent === '发贴平台') {
            cell.style.width = '150px';
        } else if (cell.textContent === '操作') {
            cell.style.width = '120px';
        } else {
            cell.style.width = '';
        };
    });
};

/****************************************初始化请求专题***************************************************/ 
function Requests_Sentiment_Subject_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_rumor_subject_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Sentiment_Subject_Rumor_List = Result.Sentiment_Info;
                $table.clear();
                $table.rows.add(Sentiment_Subject_Rumor_List);
                $table.draw();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    sound: false,
                    delay:2000,
                    position: 'top right',
                    msg: '网络出错，请重试！'
                });
                return ;
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};
/****************************************初始化请求事件***************************************************/ 
function Requests_Rumor_Event_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_rumor_event_info',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Sentiment_Event_Rumor_List = Result.Event_List;
                Loading_Hide();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    sound: false,
                    delay:2000,
                    position: 'top right',
                    msg: '网络出错，请重试！'
                });
                return ;
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

/****************************************搜索功能***************************************************/
// 搜索输入框事件监听
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');

    // 搜索按钮点击事件
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            performSearch();
        });
    }

    // 搜索框回车事件
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // 统计标签页点击事件
    const statisticsTabs = document.querySelectorAll('.statistics-tab');
    statisticsTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有active类
            statisticsTabs.forEach(t => t.classList.remove('active'));
            // 添加active类到当前点击的标签
            this.classList.add('active');
        });
    });

    // 统计计数项点击事件
    const countItems = document.querySelectorAll('.count-item');
    countItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有active类
            countItems.forEach(i => i.classList.remove('active'));
            // 添加active类到当前点击的项
            this.classList.add('active');

            // 根据点击的项进行筛选
            const label = this.querySelector('.count-label').textContent;
            filterByCategory(label);
        });
    });
});

// 执行搜索
function performSearch() {
    const searchInput = document.getElementById('search-input');
    const keyword = searchInput ? searchInput.value.trim() : '';

    if (keyword) {
        console.log('搜索关键词:', keyword);
        // 根据当前活跃的标签页执行相应的搜索
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('href');
            if (tabId === '#Account') {
                searchAccounts(keyword);
            } else if (tabId === '#profile') {
                searchTopics(keyword);
            } else if (tabId === '#user') {
                searchEvents(keyword);
            }
        }
    } else {
        // 如果搜索框为空，显示所有数据
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('href');
            if (tabId === '#Account') {
                renderAccountPageData();
            } else if (tabId === '#profile') {
                updateTableWithData(Sentiment_Subject_Rumor_List);
            }
        }
    }
}

// 搜索账号
function searchAccounts(keyword) {
    const filteredAccounts = Sentiment_Account_Rumor_List.filter(account =>
        account.SOURCE_NICKNAME.includes(keyword) ||
        account.SOURCE_BIOGRAPHY.includes(keyword) ||
        account.SOURCE_PLATFORM.includes(keyword)
    );
    renderFilteredAccounts(filteredAccounts);
}

// 搜索专题
function searchTopics(keyword) {
    const filteredTopics = Sentiment_Subject_Rumor_List.filter(topic =>
        topic.TITLE.includes(keyword) ||
        topic.CONTENT.includes(keyword)
    );
    updateTableWithData(filteredTopics);
}

// 搜索事件
function searchEvents(keyword) {
    console.log('搜索历史事件:', keyword);
}

// 根据类别筛选
function filterByCategory(category) {
    console.log('按类别筛选:', category);
}

// 渲染筛选后的账号
function renderFilteredAccounts(accounts) {
    var container = document.getElementById('Account_Body_Element');
    container.innerHTML = '';
    var element_Info = '';

    accounts.forEach(item => {
        if (Source_Type_List.indexOf('All') !== -1 || Source_Type_List.indexOf(item.SOURCE_PLATFORM) !== -1) {
            var Tag_Element = '';
            for (Tag in item.Tag_List) {
                Tag_Element += `<div class="account-tag">${item.Tag_List[Tag]}</div>`;
            };

            const element = `
                <div class="col-lg-4">
                    <div class="card radius-15">
                        <div class="card-body">
                            <div class="media align-items-top">
                                <a href="${item.SOURCE_CASE_LINK}" target="_blank">
                                    <img src="${item.SOURCE_FACE}" width="80" height="80" class="rounded-circle p-1 border bg-white" alt="" />
                                </a>
                                <div class="media-body">
                                    <h5 class="ml-3">${item.SOURCE_NICKNAME}</h5>
                                    <div class="list-inline contacts-social list-introduction mt-1 ml-3" title="${item.SOURCE_BIOGRAPHY}">${item.SOURCE_BIOGRAPHY}</div>
                                    <p class="mt-1" style="margin-left: -80px;">
                                        <i class='bx bx-bookmark'></i>${item.SOURCE_PLATFORM}
                                        <i class='bx bx-map' style="margin-left: 10px;"></i>${item.SOURCE_IP}
                                        <i class='bx bx-show' style="margin-left: 10px;"></i>${item.SOURCE_FANS}
                                        <i class='bx bx-group' style="margin-left: 10px;"></i>${item.SOURCE_FOLLOW}
                                        <i class='bx bx-paragraph' style="margin-left: 10px;"></i>${item.SOURCE_TYPE}
                                        <i class='bx bx-anchor' style="margin-left: 10px;"></i>涉${item.SOURCE_DIRECTION}
                                    </p>
                                    <div class="list-inline contacts-social mt-3" style="margin-left: -85px;">
                                        ${Tag_Element}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`
            element_Info += element;
        }
    });
    container.innerHTML = element_Info;
}

// 更新表格数据
function updateTableWithData(data) {
    $table.clear();
    $table.rows.add(data);
    $table.draw();
}