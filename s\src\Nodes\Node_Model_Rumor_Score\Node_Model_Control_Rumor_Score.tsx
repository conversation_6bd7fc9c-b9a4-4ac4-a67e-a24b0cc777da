import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,Cascader,Checkbox,CheckboxProps,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "../Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "../Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps, } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");

export class ModelControl_Rumor_Score extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public language: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = (safeGet("INTELLIGENCE_TITLE") || "").slice(0, 30)
      this.date        = safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = safeGet("INTELLIGENCE_NAME_CN");
      this.language    = safeGet("INTELLIGENCE_SOURCE_TYPE");
      this.author      = safeGet("INTELLIGENCE_AUTHOR");
      this.image       = safeGet("INTELLIGENCE_TYPE");
      this.url         = safeGet("INTELLIGENCE_URL");
      this.content     = "\n"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

export  function Node_Model_Rumor_Score(props: { data:ModelControl_Rumor_Score }) {
  

  
    // 主题配置
const theme: ThemeConfig = {
  components: {
    Carousel: {
      // 核心配置
      motionDurationSlow: "0.1s",      // 文字颜色
      
    },
  },
};
const onChange: CheckboxProps['onChange'] = (e) => {
  console.log(`checked = ${e.target.checked}`);
};

// this.title       = safeGet("INTELLIGENCE_TITLE");
// this.date        = safeGet("INTELLIGENCE_WEB_TIME");
// this.source      = safeGet("INTELLIGENCE_NAME_CN");
// this.author      = safeGet("INTELLIGENCE_AUTHOR");
// this.image       = safeGet("INTELLIGENCE_TYPE");
// this.url         = safeGet("INTELLIGENCE_URL");
// this.content     = safeGet("INTELLIGENCE_CONTENT");
  return (
    
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
    
          <Space direction="horizontal" size="middle" style={{ width: '100%' }} > 
            <Badge.Ribbon text="数源" placement="start"  color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.source}</span></Layout></Badge.Ribbon>
            <Badge.Ribbon text="类型" placement="start"  color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >微信群聊</span></Layout></Badge.Ribbon>
          
         </Space>
    
         <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
            <Badge.Ribbon text="日期" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.date}</span></Layout></Badge.Ribbon>
            <Badge.Ribbon text="作者" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.author}</span></Layout></Badge.Ribbon>
          
         </Space>
         {/* <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
            <Badge.Ribbon text="图片" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.image}</span></Layout></Badge.Ribbon>
            <Badge.Ribbon text="作者" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.author}</span></Layout></Badge.Ribbon>
          
         </Space> */}
    
         <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
            <Badge.Ribbon text="性质" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:450,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.title}</span></Layout></Badge.Ribbon>
         </Space>
         <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
            <Badge.Ribbon text="内容" placement="start" color="#3e6ae1">
              <TextArea
                    value= {props.data.content} 
                    onChange={(e) => {
                      const newValue = e.target.value;
     
                    }}
                    rows={7}
                    style={{ background:"rgba(248, 240, 240, 0.3)",width: 450,color:"white" }}
                  />
            </Badge.Ribbon>
            </Space>
      
    
    {/* <Badge.Ribbon text="Hippies">
      <Card title="Pushes open the window" size="small">
       
      </Card>
    </Badge.Ribbon> */}
    
    </Space>
  );
}
