<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
    <link rel="stylesheet" href="/static/CSS/bootstrap-fileinput/fileinput.min.css">
    <link rel="stylesheet" href="/static/CSS/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/CSS/animate/animate.min.css">
    <link rel="stylesheet" href="/static/CSS/jquery/fullcalendar/fullcalendar.min.css">
    <!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />

    <link href="/static/CSS/font-awesome/all.min.css" rel="stylesheet" />
    <!-- 加载 Select2 -->
    <link href="/static/CSS/select2/select2.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="/static/CSS/jquery/jquery.scrollbar.css">
    <!--     dialog   -->
    <link rel="stylesheet" href="/static/CSS/trumbowyg/trumbowyg.min.css">
    <link rel="stylesheet" href="/static/CSS/sweetalert/sweetalert2.min.css">
    <link rel="stylesheet" href="/static/CSS/server_style.css">

    <!-- data-table CSS ============================================ -->
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-table.css">
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-editable.css">
    <!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
    <!-- App styles -->
    <link rel="stylesheet" href="/static/CSS/app/app.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />


    <style type="text/css">
        .table {
            table-layout: fixed;
            word-break: break-all;
        }

        .nav-link {
            display: block;
            padding: 0rem 1.5rem;
        }
    </style>
    <title>哨兵导控</title>
</head>

<body class="bg-theme bg-theme1">

    <div class="wrapper">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>

        <div class="page-wrapper">
            <div class="page-content-wrapper">
                <div class="page-content">
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-success btn-lg" id="Point" data-toggle="modal" data-target="#modal-point">
                            <i class="zmdi zmdi-card"></i>
                            信用点 <span id="user_charger"></span>
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table id="table" class="table table-hover"></table>
                    </div>

                    <!-- 详情 弹窗-->
                    <div class="modal fade" id="modal-detail" tabindex="-1" data-backdrop="false">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/images/Background/10.jpg'); ">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/images/App/Sball.gif">
                                        服务详情
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">

                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                                    <button type="button" class="btn btn-success" id="submit_editor">提交支付</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Ignore backdrop click 信用点表单 -->
                    <div class="modal fade" id="modal-point" style="z-index: 1051;" data-backdrop="static" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/images/App/Sball.gif">
                                        哨兵信用点信息
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">
                                    <form id="Recharge_Points">
                                        <div class="form-row my-4">
                                            <div class="form-inline col">
                                                <label for="username" class="col-form-label mr-2">用户名称</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="username"
                                                    placeholder="用户名称" disabled>
                                            </div>
                                            <div class="form-inline col">
                                                <label for="usertype" class="col-form-label mr-2">用户类型</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="usertype"
                                                    placeholder="用户类型" disabled>
                                            </div>
                                        </div>
                                        <div class="form-row my-4">
                                            <div class="form-inline col">
                                                <label for="phone" class="col-form-label mr-2">联系电话</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="phone"
                                                    placeholder="联系电话" disabled>
                                            </div>
                                            <div class="form-inline col">
                                                <label for="email" class="col-form-label mr-2">联系邮箱</label>
                                                <input type="email" class="form-control w-75 border rounded p-2" id="email"
                                                    placeholder="联系邮箱" disabled>
                                            </div>
                                        </div>
                                        <!-- <div class="form-row my-4">
                                            <div class="form-inline col">
                                                <label for="price" class="col-form-label mr-2"
                                                    style="color: #dff516">充值金额</label>
                                                <input type="text" class="form-control w-75 border rounded p-2"
                                                    id="rechange_price" placeholder="充值金额">
                                            </div>
                                            <div class="form-inline col">
                                                <label for="credit_points" class="col-form-label mr-2">信用点数</label>
                                                <input type="email" class="form-control w-75 border rounded p-2"
                                                    id="credit_points" placeholder="信用点数" disabled>
                                            </div>
                                        </div> -->
                                        <div style="color: #4dd9d5;margin-bottom: 40px;">确定充值金额后等待支付二维码</div>
                                        <div class="d-flex my-4" style="flex-direction: column;align-items: center;">
                                            <div class="form-inline mb-5" style="flex-wrap: nowrap;">
                                                <label for="credit_points" class="col-form-label mr-2"
                                                    style="font-size: 28px;flex-shrink: 0;">信用点数</label>
                                                <input type="text" class="form-control w-75 rounded p-2 border-none"
                                                    id="credit_points" placeholder="信用点数" disabled
                                                    style="font-size: 28px;color: #E67E22;opacity: 1;">
                                                <button class="btn btn-success" type="button"
                                                    onclick="Get_User_CreditPoints()">刷新</button>
                                            </div>
                                            <div class="form-inline" style="flex-wrap: nowrap;">
                                                <label for="price" class="col-form-label mr-2"
                                                    style="font-size: 28px;flex-shrink: 0;">充值金额</label>
                                                <input type="text" class="form-control w-75  rounded p-2" id="rechange_price"
                                                    placeholder="请输入充值金额" style="font-size: 28px;">
                                                <button type="button" class="btn btn-light" style="opacity: 0;">占位</button>
                                            </div>
                                        </div>
                                        <div class="vpn_flow">
                                            <div class="dv-border-box-1 mx-auto"
                                                style="width: 268px; height: 268px;position: relative">
                                                <style>
                                                    .position-absolute {
                                                        position: absolute;
                                                    }
                                                </style>
                                                <svg width="268" height="268">
                                                    <polygon fill="transparent" points="10, 27 10, 241 13, 244 13, 247 24, 257
                        38, 257 41, 260 73, 260 75, 258 81, 258
                        85, 262 183, 262 187, 258 193, 258
                        195, 260 227, 260 230, 257
                        244, 257 255, 247 255, 244
                        258, 241 258, 27 255, 25 255, 21
                        244, 11 230, 11 227, 8 195, 8 193, 10
                        187, 10 183, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24"></polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="left-top position-absolute"
                                                    style="left: 0;top: 0;">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="right-top  position-absolute"
                                                    style="right: 0;top: 0;transform: rotateZ(180deg) rotateX(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="left-bottom position-absolute"
                                                    style="right: 0;bottom: 0;transform: rotateZ(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="right-bottom position-absolute"
                                                    style="left: 0;bottom: 0;transform:  rotateX(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <div style="position: absolute;top: 0;width: 100%;text-align: center">
                                                    <hr class="hr_top" style="margin-top: 0px; margin-left: 0px; opacity: 0;">
                                                    <p style="margin: 45% 5%; font-size: 18px; color: cyan;">
                                                        <span style="margin: 0px 18%;"> 确认后显示付款码</span>
                                                    <div id="qrcode"
                                                        style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                                                    </div>
                                                    </p>
                                                </div>
                                            </div>
                                            <div id="pay_order_status" class="text-center"></div>
                                        </div>
                                        <div class="modal-footer justify-content-center mt-4">
                                            <button id="qr_submit" class="btn btn-light mx-5" type="submit">确认充值</button>
                                            <button id="cancel_pay" class="btn btn-light mx-5"
                                                data-dismiss="modal">取消退出</button>
                                            <button class="btn btn-light mx-5" style="display: none;" type="button"
                                                id="reflush_points">刷新点数</button>
                                        </div>
                                    </form>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/daterangepicker/jquery.daterangepicker.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/fileinput.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/zh.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/theme.min.js"></script>
    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>
    <script src="/static/JavaScript/select2/select2.full.min.js"></script>
    <script src="/static/JavaScript/select2/zh-CN.js"></script>
    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>
    <!-- data-table  ============================================ -->
    <script src="/static/JavaScript/data-table/bootstrap-table.js"></script>
    <script src="/static/JavaScript/data-table/bootstrap-table-zh-CN.js"></script>

    <!-- App functions and dialog -->
    <script src="/static/JavaScript/App/clamp.js"></script>
    <script src="/static/JavaScript/App/trumbowyg.min.js"></script>

    <!-- App functions and notify -->
    <script src="/static/JavaScript/App/bootstrap-notify.min.js"></script>

    <!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>

    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <script src="/static/JavaScript/clipboard/cilpboard.min.js"></script>

    <script src="/static/JavaScript/QRcode/qrcode.min.js"></script>
    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/App/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/App/dataTables.buttons.min.js"></script>
    <script src="/static/JavaScript/App/buttons.print.min.js"></script>
    <script src="/static/JavaScript/App/jszip.min.js"></script>
    <script src="/static/JavaScript/App/buttons.html5.min.js"></script>
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <script src="/static/Page/Market/Page_Home_Worker_List.js"></script>

    <script>
		var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>

    <script>
        function Page_Init() {
            Element_Sidebar_Header();
            Query_Work_Orders();
            Get_User_CreditPoints();
        };

        window.onload = function () {
            Page_Init();
        }
    </script>
    
</body>

</html>
