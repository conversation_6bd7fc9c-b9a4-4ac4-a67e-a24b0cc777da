/* compact-table.css */
/* .compact-row > td {
  padding: 4px 8px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
}

.ant-table-thead > tr > th {
  padding: 8px 8px !important;
  font-size: 12px !important;
} */

.custom-table-row {
  padding: -10px 0px !important;
  font-size: 12px !important;
  height: -30px !important; /* 强制设置行高 */
  line-height: 0px !important; /* 调整行内内容的垂直居中 */
  max-height: -30px !important; /* 调整行内内容的垂直居中 */
}