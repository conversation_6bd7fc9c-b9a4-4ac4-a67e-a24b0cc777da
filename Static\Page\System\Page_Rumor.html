<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>信息洞察 - 哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
    <!-- select2 -->
    <link href="/static/CSS/select2/css/select2.min.css" rel="stylesheet" />
    <link href="/static/CSS/select2/css/select2-bootstrap4.css" rel="stylesheet" />
    <!-- table -->
    <link rel="stylesheet" href="/static/CSS/datatable/css/buttons.bootstrap4.min.css" />
    <link rel="stylesheet" href="/static/CSS/datatable/css/dataTables.bootstrap4.min.css" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />

    <style>
		.account-tag {
			display: inline-block;
			height: 32px;
			padding: 0 12px;
            margin-left:5px;
			font-size: 14px;
			font-weight: 500;
			line-height: 32px;
			color: #FFC107;
			cursor: pointer;
			background-color: rgb(255 255 255 / 14%);
			border: 1px solid rgb(255 255 255 / 22%);
			border-radius: 16px;
			-webkit-transition: all .3s linear;
			transition: all .3s linear;
			box-shadow: none;
		}

        .list-introduction {
            height: 60px;
            overflow: hidden;
        }

		/* 顶部统计标签页样式 */
		.statistics-tabs-container {
			background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
			border-radius: 8px;
			padding: 1rem;
			margin-bottom: 1.5rem;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
		}

		.statistics-tabs {
			display: flex;
			gap: 2rem;
			margin-bottom: 1rem;
		}

		.statistics-tab {
			color: rgba(255, 255, 255, 0.8);
			font-size: 0.9rem;
			font-weight: 500;
			cursor: pointer;
			transition: color 0.3s ease;
		}

		.statistics-tab:hover {
			color: rgba(255, 255, 255, 1);
		}

		.statistics-tab.active {
			color: #ffffff;
			font-weight: 600;
		}

		.statistics-counts {
			display: flex;
			gap: 1.5rem;
			flex-wrap: wrap;
			align-items: center;
		}

		.count-item {
			display: flex;
			align-items: center;
			gap: 0.25rem;
			padding: 0.4rem 0.8rem;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 20px;
			border: 1px solid rgba(255, 255, 255, 0.2);
			cursor: pointer;
			transition: all 0.3s ease;
			font-size: 0.85rem;
		}

		.count-item:hover {
			background: rgba(255, 255, 255, 0.15);
			border-color: rgba(255, 255, 255, 0.3);
			transform: translateY(-1px);
		}

		.count-item.active {
			background: rgba(255, 255, 255, 0.2);
			border-color: rgba(255, 255, 255, 0.4);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}

		.count-label {
			color: rgba(255, 255, 255, 0.9);
			font-weight: 500;
		}

		.count-number {
			color: #ffffff;
			font-weight: 600;
		}

		/* 响应式设计 */
		@media (max-width: 768px) {
			.statistics-tabs-container {
				padding: 0.75rem;
				margin-bottom: 1rem;
			}

			.statistics-tabs {
				flex-direction: column;
				gap: 0.5rem;
				margin-bottom: 0.75rem;
			}

			.statistics-counts {
				gap: 0.75rem;
				justify-content: center;
			}

			.count-item {
				padding: 0.3rem 0.6rem;
				font-size: 0.8rem;
			}
		}

		@media (max-width: 576px) {
			.statistics-tabs-container {
				padding: 0.5rem;
			}

			.statistics-tabs {
				gap: 0.25rem;
			}

			.statistics-tab {
				font-size: 0.8rem;
			}

			.statistics-counts {
				gap: 0.5rem;
			}

			.count-item {
				padding: 0.25rem 0.5rem;
				font-size: 0.75rem;
			}
		}

	</style>
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
					<!-- 顶部统计标签页 -->
					<div class="statistics-tabs-container">
						<div class="statistics-tabs">
							<div class="statistics-tab active">
								<span class="tab-label">数据分析</span>
							</div>
							<div class="statistics-tab">
								<span class="tab-label">存储监控</span>
							</div>
							<div class="statistics-tab">
								<span class="tab-label">历史数据大事纪</span>
							</div>
						</div>

						<div class="statistics-counts">
							<div class="count-item active">
								<span class="count-label">全部</span>
								<span class="count-number" id="stats-total">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">正面</span>
								<span class="count-number" id="stats-positive">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">中性</span>
								<span class="count-number" id="stats-neutral">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">负面</span>
								<span class="count-number" id="stats-negative">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">微博</span>
								<span class="count-number" id="stats-weibo">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">微信</span>
								<span class="count-number" id="stats-wechat">(0)</span>
							</div>
							<div class="count-item">
								<span class="count-label">小红书</span>
								<span class="count-number" id="stats-xiaohongshu">(0)</span>
							</div>
						</div>
					</div>

                    <div class="row">
                        <div class="col-12 col-lg-12">
                            <div class="card shadow-none">
                                <div class="card-body">
								<div class="d-flex justify-content-between align-items-center mb-3">
									<h5 class="mb-0">信息洞察</h5>
									<div class="d-flex align-items-center">
										<!-- 搜索框 -->
										<div class="input-group mr-3" style="width: 300px;">
											<input
												type="text"
												class="form-control"
												placeholder="搜索账号、专题或事件..."
												id="search-input"
											/>
											<div class="input-group-append">
												<button class="btn btn-outline-secondary" id="search-btn">
													<i class="bx bx-search"></i>
												</button>
											</div>
										</div>
									</div>
								</div>
                                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link active" id="Account-tab"  data-toggle="tab" href="#Account" role="tab" aria-controls="Account" aria-selected="true">
                                                账号分析
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="profile-tab"  data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">
                                                专题推送
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="user-tab"  data-toggle="tab" href="#user" role="tab" aria-controls="user" aria-selected="false">
                                                历史舆情大事纪
                                            </a>
                                        </li>
                                        
                                    </ul>
                                    <div class="tab-content p-3" id="RumorTabContent">
                                        <div class="tab-pane fade show active" id="Account" role="tabpanel" aria-labelledby="Account-tab">
                                            <div class="row">
                                                <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">账号平台:</label>
                                                <div class="col-sm-11" id="Sentiment_Source_Type_Element">
                                                    <div class="form-check form-check-inline col-form-label">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_1" value="All" checked="">
                                                        <label class="form-check-label" for="Source_Type_1">全部(<span id="Label_Source_Type_1">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_2" value="抖音">
                                                        <label class="form-check-label" for="Source_Type_2">抖音(<span id="Label_Source_Type_2">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_3" value="快手">
                                                        <label class="form-check-label" for="Source_Type_3">快手(<span id="Label_Source_Type_3">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_4" value="哔哩哔哩">
                                                        <label class="form-check-label" for="Source_Type_4">哔哩哔哩(<span id="Label_Source_Type_4">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_5" value="知乎">
                                                        <label class="form-check-label" for="Source_Type_5">知乎(<span id="Label_Source_Type_5">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_6" value="微博">
                                                        <label class="form-check-label" for="Source_Type_6">微博(<span id="Label_Source_Type_6">0</span>)</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="Source_Type_7" value="小红书">
                                                        <label class="form-check-label" for="Source_Type_7">小红书(<span id="Label_Source_Type_7">0</span>)</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row" id="Account_Body_Element">
                                                <!-- <div class="col-lg-4">
                                                    <div class="card radius-15">
                                                        <div class="card-body">
                                                            <div class="media align-items-top">
                                                                <img src="https://p2-pro.a.yximgs.com/uhead/AB/2024/06/27/16/BMjAyNDA2MjcxNjI5MzRfMjEyMzg4MzIyN18yX2hkMTExXzE3NQ==_s.jpg" width="80" height="80" class="rounded-circle p-1 border bg-white" alt="" />
                                                                <div class="media-body ml-3">
                                                                    <h5 class="mb-0">财经小辉辉</h5>
                                                                    <div class="list-inline contacts-social list-introduction mt-1" title="“股市老司机：聊聊股票开开车每周二、周四晚7点直播：分享投资知识”">“股市老司机：聊聊股票开开车每周二、周四晚7点直播：分享投资知识” </div>
                                                                    <p>
                                                                        <i class='bx bx-bookmark'></i> 快手
                                                                        <i class='bx bx-map'></i> 湖南
                                                                        <i class='bx bx-show'></i> 254.9万
                                                                        <i class='bx bx-group'></i> 59
                                                                        <i class='bx bx-paragraph'></i> 政治引导
                                                                        <i class='bx bx-anchor'></i> 涉政
                                                                    </p>
                                                                    <div class="list-inline contacts-social mt-3"> 
                                                                        <div class="account-tag">二手信源</div>
                                                                        <div class="account-tag">网络热点</div>
                                                                        <div class="account-tag">网络大V</div>
                                                                        <div class="account-tag">意见领袖</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div> -->
                                            </div>
                                        </div>

                                        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                                            <table
                                                id="Rumor_Info_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                        
                                        <div class="tab-pane fade" id="user" role="tabpanel" aria-labelledby="user-tab">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">事件筛选:</label>
                                                        <div class="col-sm-2">
                                                            <select class="single-select" id="Event_Body_Select_Element">
                                                                <option value="092348oe89243hj">丰县生育八孩女子</option>
                                                            </select>
                                                        </div>
                                                        <button type="button" class="btn btn-info btn-sm m-1 px-4"  style="float: right;" onclick="Requests_Rumor_Event_Info()">筛选</button>
                                                    </div>
                                                    <hr>
                                                    <div>
                                                        <div class="container py-2">
                                                            <div class="row">
                                                                <div class="col-auto text-center flex-column d-none d-sm-flex">
                                                                    <div class="row h-50">
                                                                        <div class="col">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                    <h5 class="m-2"><span class="badge badge-pill bg-light border">&nbsp;</span></h5>
                                                                    <div class="row h-50">
                                                                        <div class="col border-right">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                </div>
                                             
                                                                <div class="col py-2">
                                                                    <div class="card radius-15">
                                                                        <div class="card-body">
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【线索缘起】【1月27日前】疑似涉事董某民的抖音号@八个孩子的爸爸 发布孩子日常生活视频(目前账号已被查询不到)，评论中对孩子妈妈好奇
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【短视频平台关注】【1月27日】西瓜视频@徐州一修哥发布两则拍摄于1月26日视频“八个孩子的妈妈，#可怜的孩子#(目前账号已被查询不到)
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【微博平台关注】【1月28日】“八个孩子的妈妈精神失常被铁链子拴着”相关内容在微博平台升温，丰县妇联回应称多个部门已介入调查
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-auto text-center flex-column d-none d-sm-flex">
                                                                    <div class="row h-50">
                                                                        <div class="col border-right">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                    <h5 class="m-2"><span class="badge badge-pill bg-white">&nbsp;</span></h5>
                                                                    <div class="row h-50">
                                                                        <div class="col border-right">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                </div>
                                                                <div class="col py-2">
                                                                    <div class="card border-white shadow radius-15">
                                                                        <div class="card-body">
                                                                            <div class="alert bg-info text-white alert-dismissible fade show" role="alert">
                                                                                【丰县首次通报】【1月28日18:47】丰县县委宣传部通过公众号“丰县发布”发布关于网民反映“生育八孩女子”的情况说明发布
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【舆情爆发高峰】【1月29日】官方首次通报遭到舆论质疑，相关舆情在当日爆发
                                                                            </div>
                                                                            <div class="alert bg-info text-white alert-dismissible fade show" role="alert">
                                                                                【丰县二次通报】【1月30日23:46】公众号“丰县发布”发布“丰县联合调查组”关于网民反映“生育八孩女子”情况的调查通报
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col-auto text-center flex-column d-none d-sm-flex">
                                                                    <div class="row h-50">
                                                                        <div class="col border-right">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                    <h5 class="m-2"><span class="badge badge-pill bg-light border">&nbsp;</span></h5>
                                                                    <div class="row h-50">
                                                                        <div class="col border-right">&nbsp;</div>
                                                                        <div class="col">&nbsp;</div>
                                                                    </div>
                                                                </div>
                                                                <div class="col py-2">
                                                                    <div class="card radius-15">
                                                                        <div class="card-body">
                                                                            <div class="alert bg-info text-white alert-dismissible fade show" role="alert">
                                                                                【徐州公布调查进展】【2月7日23:00】微博@徐州发布 公布徐州市委市政府联合调查组“丰县生育八孩女子”调查进展情况
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【舆情热度开始回落】【2月9日】联合调查组调查情况公布后舆情热度在小幅上升后开始回落舆情焦点转向由此反映的人口、婚配等社会问题
                                                                            </div>
                                                                            <div class="alert bg-info text-white alert-dismissible fade show" role="alert">
                                                                                【徐州公布调查处理情况】【2月10日19:47】微博@徐州发布公布徐州市委市政府联合调查组“丰县生育八孩女子”事件调查处理情况，奥情再度高热
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【负面舆情有所缓和】【2月11日】讨论热度持续，舆情热度有所回落，但仍持续在较高水平
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【舆情反弹走高】【2月15日】凤凰周刊前编委邓飞曝出一张1998年8月签发的结婚证:浙大等高校学生群接龙呼吁中央介入调查，热度再次反弹。
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【舆情热度持续高位】【2月16日】环球时报》特约评论员胡锡进发文呼吁国家级媒体记者组深入开展调查。
                                                                            </div>
                                                                            <div class="alert bg-secondary text-white alert-dismissible fade show" role="alert">
                                                                                【舆情热度持续高位】【2月71日】江苏省委省政府决定成立调查组，对生育八孩女子事件责任人严肃追责
                                                                            </div>
                                                                            
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
			</div>
		</div>
		<div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
	</div>
    <div class="modal fade" id="loadingModal" backdrop="static" keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div style="width: 200px;height:20px; z-index: 20000; position: absolute; text-align: center; left: 50%; top: 50%;margin-left:-100px;margin-top:-10px">
                <div class="text-center">
                    <div class="spinner-border text-info spinner-border-sm" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                    <strong style="color:#198fed">Loading...</strong>
                </div>
            </div>   
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>

    <script src="/static/JavaScript/select2/js/select2.min.js"></script>
    <script src="/static/JavaScript/datatable/js/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/input-tags/js/tagsinput.js"></script>

	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Rumor.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Statistics_Data(); // 获取统计数据
            Requests_Sentiment_Rumor_Info();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>