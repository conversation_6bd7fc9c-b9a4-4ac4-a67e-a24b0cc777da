import React, { useState, useEffect } from 'react';
import { Card, Table, Select, Button, Space, Tag, message, Spin, Modal } from 'antd';
import { DownloadOutlined, DeleteOutlined, FileTextOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import '../Styles/Page_Report_Center.css';

const { Option } = Select;
const { confirm } = Modal;

interface ReportRecord {
  id: string;
  reportTime: string;
  reportUuid: string;
  reportName: string;
  reportStatus: 'Active' | 'Finish' | 'Download';
  reportFileType?: string;
  reportSaveName?: string;
}

const Page_Report_Center: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportRecord[]>([]);

  // 筛选条件
  const [dateFilter, setDateFilter] = useState('HalfYear');

  // 日期筛选选项
  const dateOptions = [
    { value: 'Day', label: '今日' },
    { value: 'TwoDay', label: '近两日' },
    { value: 'Mouth', label: '本月' },
    { value: 'HalfYear', label: '近半年' },
    { value: 'Year', label: '近一年' },
    { value: 'All', label: '所有' }
  ];

  // 获取报告数据
  const fetchReportData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockData: ReportRecord[] = [
        {
          id: '1',
          reportTime: '2025-01-09 14:30:25',
          reportUuid: 'RPT-2025-001',
          reportName: '舆情监测周报_2025年第1周',
          reportStatus: 'Finish',
          reportFileType: 'pdf',
          reportSaveName: '舆情监测周报_2025年第1周.pdf'
        },
        {
          id: '2',
          reportTime: '2025-01-08 16:45:12',
          reportUuid: 'RPT-2025-002',
          reportName: '热点事件分析报告_突发事件',
          reportStatus: 'Download',
          reportFileType: 'pdf',
          reportSaveName: '热点事件分析报告_突发事件.pdf'
        },
        {
          id: '3',
          reportTime: '2025-01-07 10:20:33',
          reportUuid: 'RPT-2025-003',
          reportName: '品牌声誉监测月报_12月',
          reportStatus: 'Active',
          reportFileType: 'pdf',
          reportSaveName: '品牌声誉监测月报_12月.pdf'
        },
        {
          id: '4',
          reportTime: '2025-01-06 09:15:44',
          reportUuid: 'RPT-2025-004',
          reportName: '竞品分析报告_Q4季度',
          reportStatus: 'Finish',
          reportFileType: 'pdf',
          reportSaveName: '竞品分析报告_Q4季度.pdf'
        },
        {
          id: '5',
          reportTime: '2025-01-05 15:30:21',
          reportUuid: 'RPT-2025-005',
          reportName: '危机预警报告_紧急',
          reportStatus: 'Active',
          reportFileType: 'pdf',
          reportSaveName: '危机预警报告_紧急.pdf'
        }
      ];

      setReportData(mockData);
    } catch (error) {
      console.error('获取报告数据失败:', error);
      message.error('获取报告数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 筛选数据
  const handleFilter = () => {
    fetchReportData();
  };

  // 下载报告
  const handleDownload = (report: ReportRecord) => {
    console.log('下载报告:', report.reportUuid);

    // 创建表单进行下载
    const form = document.createElement('form');
    form.action = `https://${window.location.host}/CSC_file_download`;
    form.method = 'POST';

    // 添加参数
    const params = [
      { name: 'file_type', value: report.reportFileType || 'pdf' },
      { name: 'file_time', value: report.reportTime },
      { name: 'save_name', value: report.reportSaveName || report.reportName }
    ];

    params.forEach(param => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = param.name;
      input.value = param.value;
      form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    // 更新状态为已下载
    setReportData(prev =>
      prev.map(item =>
        item.reportUuid === report.reportUuid
          ? { ...item, reportStatus: 'Download' as const }
          : item
      )
    );

    message.success('报告下载已开始');
  };

  // 删除报告
  const handleDelete = async (reportUuid: string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个报告吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 模拟删除API调用
          await new Promise(resolve => setTimeout(resolve, 500));

          setReportData(prev => prev.filter(item => item.reportUuid !== reportUuid));
          message.success('报告删除成功');
          console.log('删除报告成功:', reportUuid);
        } catch (error) {
          console.error('删除报告失败:', error);
          message.error('删除报告失败');
        }
      }
    });
  };

  // 表格列定义
  const columns: ColumnsType<ReportRecord> = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 160,
      align: 'center',
    },
    {
      title: '报告标识',
      dataIndex: 'reportUuid',
      key: 'reportUuid',
      width: 150,
      align: 'center',
    },
    {
      title: '报告名称',
      dataIndex: 'reportName',
      key: 'reportName',
      ellipsis: true,
      render: (text: string) => (
        <span title={text}>
          <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          {text}
        </span>
      ),
    },
    {
      title: '处置状态',
      dataIndex: 'reportStatus',
      key: 'reportStatus',
      width: 120,
      align: 'center',
      render: (status: string) => {
        const statusConfig = {
          Active: { color: 'processing', text: '执行中' },
          Finish: { color: 'success', text: '已完成' },
          Download: { color: 'default', text: '已下载' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          {(record.reportStatus === 'Finish' || record.reportStatus === 'Download') && (
            <Button
              type="primary"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            >
              下载
            </Button>
          )}
          {record.reportStatus === 'Active' && (
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.reportUuid)}
            >
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchReportData();
  }, []);

  return (
    <div className="report-center-page">
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <span>日期筛选:</span>
          <Select
            value={dateFilter}
            onChange={setDateFilter}
            style={{ width: 120 }}
          >
            {dateOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Button type="primary" onClick={handleFilter}>
            筛选
          </Button>
        </Space>
      </Card>

      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={reportData}
            rowKey="reportUuid"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 800 }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default Page_Report_Center;
