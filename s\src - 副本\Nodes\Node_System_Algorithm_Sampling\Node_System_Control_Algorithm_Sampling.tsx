import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
// import styles from "../Nodes.module.css";
import  {useEffect } from 'react';
// import "./Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

// import '../Styles/Utils_Common.module.css'
import SelectStyles from '../../Styles/Utils_Common.module.css'
// import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");





export class SystemControl_Algorithm_Sampling extends ClassicPreset.Control {
  constructor(
    public Title: string, 
    public AI_Computing: string, 
    public AI_Version: string, 
    public AI_Model: string, 
 
    // public onClick: () => void) 
    public onChange: (value: string) => void)
    {
    super();

    
  }
  setContent(Config: Record<string, any>) {
    const SafeGet = (key: string) => Config?.[key] || "未知";

    this.Title         = SafeGet("Title")
    this.AI_Computing  = SafeGet("AI_Computing")
    this.AI_Version    = SafeGet("AI_Version")
    this.AI_Model      = SafeGet("AI_Model")
  //    
    // try {
    //   this.AI_Computing       =  Number(Config?.["AI_Computing"] || 0);
    // } catch (error) {
    //   this.AI_Computing       = 0
    // }
    

    console.log('AI_Computing :',this.Title, this.AI_Computing, typeof(this.AI_Computing));

    // this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
    // this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
    // this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
    // this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
    // this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
    // this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
    // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
    // 
  }
  
}


interface Option {
  value: string;
  label: string;
}

  interface Props_Select {
    Options_Select: Option[];
    Options_Index: number;     // Changed from Number to number
    onChange?: (value: string) => void; 
  }
  
   const AI_Select: React.FC<Props_Select> = ({ 
      Options_Select,Options_Index,onChange  
  
    }) => {
  
  
  
      const options  = Options_Select
    // 选项数据
  //   const options = [
  //     { value: 'option1', label: '选项1' },
  //     { value: 'option2', label: '选项2' },
  //     { value: 'option3', label: '选项3' },
  //     { value: 'option4', label: '选项4' },
  //   ];
  
    const [selectedValue, setSelectedValue] = useState<string>(Options_Select[Options_Index]?.value);
  
    // 获取当前选中的索引
  //   @ts-ignore - 忽略下一行的类型检查
    const currentIndex = options.findIndex(opt => opt.value === selectedValue);
  
    // 左箭头点击 - 切换到上一个选项
    const handleLeftClick = () => {
      const newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
      setSelectedValue(options[newIndex].value);
      // onChange?.(value);
    };
  
    // 右箭头点击 - 切换到下一个选项
    const handleRightClick = () => {
      const newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
      setSelectedValue(options[newIndex].value);
      // onChange?.(value);
    };
  
    // Select 选择变化
    const handleSelectChange = (value: string) => {
      setSelectedValue(value);
      onChange?.(value);
    };
      const theme: ThemeConfig = {
          components: {
              Select: {
                  // 核心配置
                  multipleItemBg: 'transparent',
                  selectorBg: 'transparent',
                  multipleSelectorBgDisabled: 'transparent',
                  hoverBorderColor: 'transparent',
                  multipleItemBorderColor: 'transparent',
                  activeBorderColor: 'transparent',
                  activeOutlineColor: 'transparent',
                  multipleItemColorDisabled:"red",
                  colorText:"white",
                  colorBorder:"transparent",
                  // optionPadding: '55px 12px',
                  // selectorBg: '#ffffff',
  
                  
              },
  
  
              },
          };
  
      // <Space wrap>
    return (
      <Space style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        height:20,
        width:440,
        background:"transparent",
        gap: '8px'
      }}>
        <Button 
            onPointerDown={(e) => e.stopPropagation()}
            onDoubleClick={(e) => e.stopPropagation()}
          type="text" 
          icon={<LeftOutlined />} 
          onClick={handleLeftClick}
          style={{ fontSize: '16px',color:"white" }}
        />
        <ConfigProvider theme={theme}>
          <Select
              // className="custom-double-select"
              className={SelectStyles['custom-double-select']}
              size={"small"}
              value={selectedValue}
              onChange={handleSelectChange}
              // style={{ flex: 1, width: '120px' , backgroundColor: '#f0f8ff', alignItems:"center",justifyItems:"center"}}
              style={{ flex: 1, width: '330px' , alignItems:"center",justifyItems:"center",color:"white"}}
              options={options}
              suffixIcon={<></>}
         
          />
  
  
   
  
        </ConfigProvider>
        <Button 
            onPointerDown={(e) => e.stopPropagation()}
            onDoubleClick={(e) => e.stopPropagation()}
          type="text" 
          icon={<RightOutlined />} 
          onClick={handleRightClick}
          style={{ fontSize: '16px',color:"white" }}
        />
      </Space>
  
  
  
  
  
  
    );
  };
  

  interface StepModelProps {
    label?: string;
    value?: number;
    onChange?: (value: number) => void;
    onModelChange?: (value: number) => void;
    
  }

const AI_Step: React.FC<StepModelProps> = (props) => {



  // onChange





    const [inputValue, setInputValue] = useState(props.value || 1);
  
    const onChange: InputNumberProps['onChange'] = (newValue) => {
      const val = newValue as number;
      setInputValue(val);
      props.onChange?.(val);
      props.onModelChange?.(val);
      // onChange1(String(val));
      // onChange1?.(val);
    };
    const textStyle = {
      color: 'white',
      margin: 0
    };
    // 主题配置
  const theme: ThemeConfig = {
    components: {
      InputNumber: {
        // 核心配置
        colorText: "#ffffff",      // 文字颜色
        colorBorder: "#1890ff",     // 默认边框色
        colorPrimaryHover: "#40a9ff", // 悬浮态边框色
        colorPrimary: "#1677ff",    // 聚焦态边框色
        fontSize: 16,               // 字号（覆盖 inputFontSize）
        handleBg:"rgba(255,255,255,0.1)",
        handleBorderColor:"rgba(255,255,255,0.1)",
        // 其他扩展配置
        colorError: "#ff4d4f",      // 错误状态颜色
        colorBgContainer: "#f6ffed", // 背景色
        // paddingBlock: -28,            // 垂直内边距
        // paddingInline: -26           // 水平内边距
      },
    },
  };
  
    return (
      <Row style={{ display: 'flex', alignItems: 'center', justifyContent: 'start', background: "transparent" ,color: 'white' ,marginLeft:18}}>
        <Col span={4} style={{ background: "transparent" }}>
        <p style={textStyle}>{props.label || "数据"}</p>
        </Col>
        <Col style={{ background: "transparent" }} span={13}>
          <Slider
          style={{color:"white", width:230,}}
            min={0}
            max={2}
            step={0.1}
            onChange={onChange}
            value={inputValue}
            // trackStyle={{ background: 'white' }}
            handleStyle={{ 
              borderColor: 'white',
              color: 'white'
            }}
            railStyle={{ background: 'rgba(255,255,255,0.3)' }}
          />
        </Col>
        <Col span={3} style={{ background: "transparent",color: 'white',marginLeft:18  }}>
        <ConfigProvider theme={theme}>
          <InputNumber
           suffix="W" 
            min={0}
            max={2}
            step={0.1}
            style={{
              // 外层容器样式
              color: "white",
          
              // display: 'flex',
              // alignItems: 'start', justifyContent: 'start', 
              width:90,
              height:30,
              background:"transparent",
              border: 'none',
              padding:"10 -10",
              position: 'relative',  // 为伪元素定位准备
              marginTop:0,
            }}
            value={inputValue}
            onChange={onChange}
            // className={styles.customInput}
           
          />
          </ConfigProvider>
        </Col>
      </Row>
    );
  };
  

export  function Node_System_Algorithm_Sampling(props: { data: SystemControl_Algorithm_Sampling }) {
  console.log('data :', Number(props.data.AI_Computing));
  
  // console.log('props :', aiComputingValue);
  // const [selectedValue1, setSelectedValue1] = useState<number>(Number(props.Data.AI_Computing));

  
    const Options_Select_AI_Computing = [
      { value: '0', label: '20' },

    ];

  // const [selected_AI_Computing, setSelected_AI_Computing] = useState<string>);



  const handleAIComputingChange = (value: string) => {
    // Update the control's AI_Computing value
    props.data.AI_Computing = value;
    
    // If you need to trigger the onChange from AIControl_DeepSeek
    // props.data.onChange(value);
    
    // console.log('Updated AI_Computing:', value);
  };

  const handleAIVersionChange = (value: string) => {
    // Update the control's AI_Computing value
    props.data.AI_Version = value;
    
    // If you need to trigger the onChange from AIControl_DeepSeek
    // props.data.onChange(value);
    
    // console.log('Updated AI_Computing:', value);
  };

  const handleAIModelChange = (value: number) => {
    props.data.AI_Model = String(value);
    
    console.log('Updated AI_Model:', value);
  }
    const Options_Select_AI_Version = [
      { value: '0', label: '8.0' },

    ];
    const Options_Select_3 = [
      { value: 'option1', label: 'euler' },

    ];
    const Options_Select_4 = [
      { value: 'option1', label: 'normal' },
    ];
    const Options_Select_5 = [
      { value: 'option1', label: '1.00' },
    ];
    const Options_Select_6 =[
      { value: 'option1', label: 'Hide'},
    ];
    const Options_Select_7 =[
      { value: 'option1', label: '371588656777590'},
    ];
    const Options_Select_8 =[
      { value: 'option1', label: 'randomize'},
    ];


    const [selectedValue, setSelectedValue] = useState<string>(Options_Select_6[0].value);

    const handleSelectChange = (value: string) => {
      setSelectedValue(value);
    };

    const SwitchAiChange = (checked: boolean) => {
      console.log(`switch to ${checked}`);
    };
  
    return (
      <Layout style={{zIndex:99999,background:"transparent",}}>
        <Space direction="vertical" size="middle" style={{ width: '100%'}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_AI_Computing} Options_Index={ Number(props.data.AI_Computing)}  onChange={handleAIComputingChange}  /> </Layout>     
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_AI_Version} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_3} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_4} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_5} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_6} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_7} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 10,background:"#666",flex:"1",justifyItems:"center"}}  >
          <AI_Select Options_Select={Options_Select_8} Options_Index={ Number(props.data.AI_Version)}  onChange={handleAIVersionChange}   /></Layout>
        </Space>
        {/* <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 4,background:"#666",flex:"1",justifyItems:"center"}}  ><Component_Select Options_Select ={Options_Select_3}></Component_Select></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 4,background:"#666",flex:"1",justifyItems:"center"}}  ><Component_Select Options_Select ={Options_Select_4}></Component_Select></Layout>
        </Space>
        <Space direction="vertical" size="middle" style={{ width: '100%',marginTop:8}}>
          <Layout   style={{height:22,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 4,background:"#666",flex:"1",justifyItems:"center"}}  ><Component_Select Options_Select ={Options_Select_5}></Component_Select></Layout>
        </Space> */}

      </Layout>
  
    );
}

