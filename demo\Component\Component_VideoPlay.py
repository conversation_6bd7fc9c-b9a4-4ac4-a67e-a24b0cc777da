# -*- coding: utf-8 -*-
import time,os,sys,cv2
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget
from PySide6.QtGui import QImage, QPixmap
from PySide6.QtCore import QTimer, Qt
from PySide6.QtCore import QThread, Signal, Slot
class Component_VideoPlay(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        self.Video_Info = args[0]
        print(self.Video_Info)

        self.Video_Info["Video_Size"] = [640,480]
        self.Video_Info["Video_Size"] = [1920,1080]

        self.VIDEO_WIDTH  = self.Video_Info["Video_Size"][0]
        self.VIDEO_HEIGHT = self.Video_Info["Video_Size"][1]
        self.initUI()
        # self.resize(800,600)



    def initUI(self):
        __QVBoxLayout = QtWidgets.QHBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.video_source = self.Video_Info['Video_Source']

        self.video_label = QtWidgets.QLabel()
        # self.video_label.setFixedSize(self.VIDEO_WIDTH, self.VIDEO_HEIGHT)  # 设置固定大小
        # self.video_label.setGeometry(0, 0, 320, 240)

        # self.stop_button = QtWidgets.QPushButton('Stop', self)
        # self.stop_button.setGeometry(0, 0, 100, 30)
        # self.stop_button.clicked.connect(self.stop_video)
        # __VideoPlayer = VideoPlayer(r"D:\Data\1.mp4")

        __QVBoxLayout.addWidget( self.video_label)



        self.video_thread = VideoCaptureThread(self.Video_Info)
        self.video_thread.frame_updated.connect(self.update_frame)
        self.video_thread.start()


    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()


    @Slot(QImage)
    def update_frame(self, frame):
        # self.video_label.setPixmap(QPixmap.fromImage(frame))
        # pix = QPixmap.fromImage(frame)
        # self.video_label.setPixmap(pix.scaled(self.rect()))
        target_size = self.video_label.size()

        # 2. 使用缓存的QPixmap避免重复创建
        if not hasattr(self, '_cached_pixmap'):
            self._cached_pixmap = QtGui.QPixmap()

        # 3. 直接缩放并设置（保持纵横比）
        self._cached_pixmap = QPixmap.fromImage(frame).scaled(
            target_size,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        self.video_label.setPixmap(self._cached_pixmap)




class VideoCaptureThread(QtCore.QThread):
    frame_updated = QtCore.Signal(QImage)

    def __init__(self, *args):
        super().__init__()
        self.Video_Info = args[0]
        # self.video_source = "rtsp://admin:csc888888!@192.168.123.119:554/Streaming/Channels/101"
        self.cap = cv2.VideoCapture( self.Video_Info["Video_Source"])
        self.running = True

        self.VIDEO_WIDTH    =    self.Video_Info["Video_Size"][0]
        self.VIDEO_HEIGHT   =    self.Video_Info["Video_Size"][1]

    def run(self):
        while self.running and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break
            # Convert the frame from BGR to RGB format
            frame_resized = cv2.resize(frame, (self.VIDEO_WIDTH, self.VIDEO_HEIGHT))
            rgb_frame = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            # Convert the frame to QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            # convert_to_Qt_format = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            convert_to_Qt_format = QImage(rgb_frame.data, self.VIDEO_WIDTH, self.VIDEO_HEIGHT, bytes_per_line, QImage.Format_RGB888)
            # Emit the frame to the main thread
            qimg_resized = convert_to_Qt_format.scaled(self.VIDEO_WIDTH, self.VIDEO_HEIGHT, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.frame_updated.emit(qimg_resized)
            time.sleep(0.01)

    def stop(self):
        self.running = False
        self.cap.release()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    Window = Component_VideoPlay({"Video_Source":"rtsp://admin:csc888888!@192.168.123.57:554/Streaming/Channels/101"})
    Window.show()
    sys.exit(app.exec())