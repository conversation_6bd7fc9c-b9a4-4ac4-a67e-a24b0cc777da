/****************************************初始化地图***************************************************/ 
var map = new BMapGL.Map('sentiment-map'); // 创建Map实例
map.centerAndZoom(new BMapGL.Point(116.404, 39.915), 4); // 初始化地图,设置中心点坐标和地图级别
map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
var citys = ['北京市', '成都市', '广东市', '上海市'];
// 创建点标记
var marker1 = new BMapGL.Marker(new BMapGL.Point(116.408293,39.912057));
var marker2 = new BMapGL.Marker(new BMapGL.Point(104.071595,30.661785));
var marker3 = new BMapGL.Marker(new BMapGL.Point(113.262652,23.133429));
var marker4 = new BMapGL.Marker(new BMapGL.Point(121.474216,31.23173));
// 在地图上添加点标记
map.addOverlay(marker1);
map.addOverlay(marker2);
map.addOverlay(marker3);
map.addOverlay(marker4);

/****************************************近30天平台分布***************************************************/
var Today_Sentiment_Platform_Type_Bar_Options = {
    series: [],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        height: 380,
        type: 'pie',
    },
    // colors: ["#673ab7", "#32ab13", "#f02769", "#ffc107", "#198fed"],
    colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"],
    labels: [],
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                height: 360
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};
var Today_Sentiment_Platform_Type_Bar = new ApexCharts(document.querySelector("#Today_Sentiment_Platform_Type_Bar"), Today_Sentiment_Platform_Type_Bar_Options);
Today_Sentiment_Platform_Type_Bar.render();

// ---------------------------------------近30天情感属性 饼状图
var Today_Sentiment_Emotion_Bar_Options = {
    series: [],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        height: 380,
        type: 'donut',
    },
    // colors: ["#673ab7", "#32ab13", "#f02769", "#ffc107", "#198fed"],
    colors: ["#30b817", "#176eb8", "#dc3545"],
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                height: 320
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};
var Today_Sentiment_Emotion_Bar = new ApexCharts(document.querySelector("#Today_Sentiment_Emotion_Bar"), Today_Sentiment_Emotion_Bar_Options);
Today_Sentiment_Emotion_Bar.render();
// --------------------------------------- 近30天舆情趋势
var Mouth_Sentiment_Time_Crawl_Line_Options = {
    series: [
        // {
        //     name: '今日汇总',
        //     data: [408105, 580416, 208587, 553209, 272070, 235794, 480657, 743658, 489726, 761796, 816210, 471588, 562278, 698313, 516933, 99759, 326484, 163242, 344622, 625761, 235794, 317415, 516933, 462519, 272070, 789003, 81621, 154173, 535071, 63483, 389967]
        // }
    ],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        type: 'area',
        height: 340,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        },
        dropShadow: {
            enabled: false,
            top: 3,
            left: 14,
            blur: 4,
            opacity: 0.10,
        }
    },
    legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: -25
    },
    dataLabels: {
        enabled: false
    },
    stroke: {
        show: true,
        width: 3,
        curve: 'smooth'
    },
    tooltip: {
        theme: 'dark',
        y: {
            formatter: function (val) {
                return val + " 条 "
            }
        }
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            gradientToColors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"],
            shadeIntensity: 1,
            type: 'vertical',
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0.1,
            //stops: [0, 50, 65, 91]
        },
    },
    grid: {
        show: true,
        borderColor: 'rgba(255, 255, 255, 0.12)',
        strokeDashArray: 5,
    },
    colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"],
    yaxis: {
        labels: {
            formatter: function (value) {
                return value + "条";
            }
        },
    },
    xaxis: {
        categories: []
    }
};
var Mouth_Sentiment_Time_Crawl_Line= new ApexCharts(document.querySelector("#Mouth_Sentiment_Time_Crawl"), Mouth_Sentiment_Time_Crawl_Line_Options);
Mouth_Sentiment_Time_Crawl_Line.render();

/****************************************初始化请求***************************************************/
function Requests_Sentiment_Info() {
    // Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": "return_overview_info",
        "data_argument": `{}`,
        "data_kwargs":{},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                var Today_Sentiment_Emotion_Bar_Label = [];
                var Today_Sentiment_Emotion_Bar_Value = [];
                Result.Today_Sentiment_Emotion_Info.map(item => {
                    Today_Sentiment_Emotion_Bar_Label.push(item[0]);
                    Today_Sentiment_Emotion_Bar_Value.push(item[1]);
                });
                // -------------------------------近30天平台分布 图表渲染
                var Today_Sentiment_Platform_Type_Bar_Label = [];
                var Today_Sentiment_Platform_Type_Bar_value = [];
                Result.Today_Sentiment_Platform_Type.map(item => {
                    Today_Sentiment_Platform_Type_Bar_Label.push(item[0]);
                    Today_Sentiment_Platform_Type_Bar_value.push(item[1]);
                });
                // console.log('近30天平台分布 图表渲染:')
                // 如果需要更新 labels，可以使用以下方法
                Today_Sentiment_Platform_Type_Bar.updateOptions({
                    labels: Today_Sentiment_Platform_Type_Bar_Label
                }, false, false);
                // 重新渲染图表
                Today_Sentiment_Platform_Type_Bar.updateSeries(Today_Sentiment_Platform_Type_Bar_value);
                // // -------------------------------情感类型 图表渲染
                Today_Sentiment_Emotion_Bar.updateOptions({
                    labels: Today_Sentiment_Emotion_Bar_Label
                }, false, false);
                Today_Sentiment_Emotion_Bar.updateSeries(Today_Sentiment_Emotion_Bar_Value);
                // --------------------------------最热文章 内容渲染
                var mission_active_totle =''
                for (var i = Result.Today_Sentiment_Hot_Article.length-1;i>=0;i--) {
                    var mission_info =  Result.Today_Sentiment_Hot_Article[i]
                    var mission_info_one = `
                        <div class="item" onclick="cheack_url('${mission_info['Url']}')">
                            <span style="color:#ffc107;">${mission_info['Platform']} - </span>${mission_info['Title']}
                        </div>`
                    mission_active_totle += mission_info_one
                }
                document.getElementById("Today-Sentiment-Hot-Article-content").innerHTML = mission_active_totle;
                // --------------------------------负面文章 内容渲染
                var mission_active_totle =''
                for (var i = Result.Today_Sentiment_Negative_Article.length-1;i>=0;i--) {
                    var mission_info =  Result.Today_Sentiment_Negative_Article[i]
                    var mission_info_one = `
                        <div class="item" onclick="cheack_url('${mission_info['Url']}')">
                            <span style="color:#ffc107;">${mission_info['Platform']} - </span>${mission_info['Title']}
                        </div>`
                    mission_active_totle += mission_info_one
                }
                document.getElementById("Today-Sentiment-Negative-Article-content").innerHTML = mission_active_totle;
                // --------------------------------今日舆情总量
                updateOdometer(28561);
                // ------------------------------汇总曲线图数据渲染
                Mouth_Sentiment_Time_Crawl_Line.updateOptions({
                    xaxis: {
                        categories: Result.Mouth_Sentiment_Time_Crawl.Label_X
                    },
                });
                Mouth_Sentiment_Time_Crawl_Line.updateSeries(Result.Mouth_Sentiment_Time_Crawl.Value_Y,true);
                // Loading_Hide();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            }
        }).catch((err) => {
            console.log('err:',err);
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
};

/****************************************最热文章***************************************************/ 
// 开始滚动
function startScroll() {
    // 滚动速度（毫秒）
    let scrollSpeed = 1000;
    var scrollContainer = $('#Today-Sentiment-Hot-Article-container');
    var scrollContent = $('#Today-Sentiment-Hot-Article-content');
    var firstItemHeight = scrollContent.children('.item').first().outerHeight(true); // 获取第一行内容的高度
    function scroll() {
        scrollContent.animate({top: '-=' + firstItemHeight + 'px'}, scrollSpeed, 'linear', function(){
            var firstItem = $(this).children('.item').first(); // 获取第一行内容
            $(this).append(firstItem); // 将第一行内容追加到最后
            $(this).css('top', 0); // 重置滚动内容的位置
            setTimeout(scroll, 0); // 递归调用滚动函数，实现无缝滚动
        });
    };
    scroll(); // 开始滚动
};

// 停止滚动
function stopScroll() {
    $('#Today-Sentiment-Hot-Article-content').stop(); // 停止滚动动画
};

// 当鼠标悬停在区域时停止滚动，移开时继续滚动
$('#Today-Sentiment-Hot-Article-container').hover(stopScroll, startScroll);

/****************************************负面文章***************************************************/ 
// 开始滚动
function startScroll_Negative() {
    // 滚动速度（毫秒）
    let scrollSpeed_1 = 3000;
    var scrollContainer_1 = $('#Today-Sentiment-Negative-Article-container');
    var scrollContent_1 = $('#Today-Sentiment-Negative-Article-content');
    var firstItemHeight_1 = scrollContent_1.children('.item').first().outerHeight(true); // 获取第一行内容的高度
    function scroll_1() {
        scrollContent_1.animate({top: '-=' + firstItemHeight_1 + 'px'}, scrollSpeed_1, 'linear', function(){
            var firstItem_1 = $(this).children('.item').first(); // 获取第一行内容
            $(this).append(firstItem_1); // 将第一行内容追加到最后
            $(this).css('top', 0); // 重置滚动内容的位置
            setTimeout(scroll_1, 0); // 递归调用滚动函数，实现无缝滚动
        });
    };
    scroll_1(); // 开始滚动
};

// 停止滚动
function stopScroll_Negative() {
    $('#Today-Sentiment-Negative-Article-content').stop(); // 停止滚动动画
};

// 当鼠标悬停在区域时停止滚动，移开时继续滚动
$('#Today-Sentiment-Negative-Article-container').hover(stopScroll_Negative, startScroll_Negative);

/****************************************链接跳转***************************************************/ 
function cheack_url(url) {
    window.open(url);
};
