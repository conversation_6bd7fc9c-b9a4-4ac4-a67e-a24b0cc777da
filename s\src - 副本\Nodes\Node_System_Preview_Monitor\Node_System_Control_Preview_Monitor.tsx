import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "../Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "../Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");

export class SystemControl_Preview_Monitor extends ClassicPreset.Control {
  constructor(
    public Title: string, 

    public Percent: number,
    // public onClick: () => void) 
    public onChange: (value: string) => void)
    {
    super();

    
  }
  setContent(Object: Record<string, any>) {
    const safeGet = (key: string) => Object?.[key] || "未知";

    const safeParseInt = (value: any): number => {
      if (value === null || value === undefined) {
        return 0;
      }
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? 0 : parsed;
    };



    this.Title         = safeGet("Title");
    this.Percent       = safeParseInt(safeGet("Percent"));
  
    // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
    // this.update(); // 触发控件更新
  }
    
}


export function Node_System_Preview_Monitor(props: { data:SystemControl_Preview_Monitor }) {
  

  return (<Flex justify={"center"} align={"center"} style={{width:"100%"}}>
      <Space direction="vertical" size="middle">
        <Space.Compact>
          <div style={{
            width: "340px",
            height: "180px",
            overflow: "hidden",
            position: "relative",
            border: "1px solid #d9d9d9",
            borderRadius: "8px",
          }}>
            <div style={{
              position: "absolute",
              width: "100%",
              animation: "scroll 10s linear infinite",
              color: "white",
              fontFamily: "monospace",
              lineHeight: "1",
              padding: "8px",
              whiteSpace: "pre-wrap",
            }}>
              {`
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
                测试
              `.trim()}
            </div>
          </div>
        </Space.Compact>
      </Space>
      <style>{`
        @keyframes scroll {
          0% {
            transform: translateY(0);
          }
          100% {
            transform: translateY(-100%);
          }
        }
      `}</style>
    </Flex>
  );
};

