var Sentiment_All_List = [];
var Sentiment_All_SnapShot_List = [];
// 每页条数
var Page_Size = 10;
// 当前页码
var Page_current = 1;
// 总条数
var Page_TotleItems = 0;
// 计算总页数
var Page_Total = 0;
var Table_condiction_param = {
    'Emotion_Type':['All'],
};
var Table_Condiction_Param_Count = {
    'Emotion_Type_1': 0,
    'Emotion_Type_2': 0,
    'Emotion_Type_3': 0,
    'Emotion_Type_4': 0,
};
/****************************************初始化请求热点数据***************************************************/ 
function Requests_Hot_Notice_Info() {
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_search_hotnotice',
        "data_argument": `{}`,
        "data_kwargs":`{}`
    };
    __Service_Requests = new Service_Requests("Sync",Requests_Data);
    Result  = __Service_Requests.callMethod();
    if (Result.Status === 'Success') {
        var ParNode_Element = '';
        Result.Hot_Notice_List.forEach(Item => {
            if (Item.Type === 1) {
                var Class_Name = 'hot-notices notices-level1'
            } else if(Item.Type === 2) {
                var Class_Name = 'hot-notices notices-level2'
            } else if(Item.Type === 3) {
                var Class_Name = 'hot-notices notices-level3'
            } else {
                var Class_Name = 'hot-notices'
            };
            ParNode_Element += `
                <div class="${Class_Name}">
                    <div class="notice" onclick="Router_Redirection('${Item.Url}')">
                        ${Item.Content}
                    </div>
                </div>`;
        });
        document.getElementById('Hot_Notice_Element').innerHTML = ParNode_Element;
    } else {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            sound: false,
            delay:2000,
            position: 'top right',
            msg: '网络出错，请重试！'
        });
        return ;
    };
};

/****************************************刷新监控***************************************************/ 
$('#Reload_Hot_Botice_Element').click(function() {
    Requests_Hot_Notice_Info();
});

/****************************************热点跳转***************************************************/ 
function Router_Redirection(Url) {
    window.open(Url);
};

// 对列表中的每个词进行转义，以处理可能的正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式特殊字符
};
/****************************************点击搜索***************************************************/ 
$('#Search_Sentiment_Element').click(function() {
    Submit_Search_Sentiment();
});

function Submit_Search_Sentiment() {
    Loading_Show();
    var Search_Sentiment_Key_Info = document.getElementById('Search_Sentiment_Key_Element').value;
    console.log('Search_Sentiment_Key_Info:',Search_Sentiment_Key_Info);
    if (Search_Sentiment_Key_Info.length === 0) {
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            sound: false,
            delay:2000,
            position: 'top right',
            msg: '请输入搜索的关键词内容！'
        });
        Loading_Hide();
        return ;
    };
    
    let elementToDelete = document.getElementById('Hot_Show_Body_Element');
    if (elementToDelete) {
        document.getElementById('Search_Show_Body_Element').style.display = 'block';
        elementToDelete.remove();
        document.getElementById('Hot_Notice_Element').remove();
    };
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_search_sentiment',
        "data_argument": `{}`,
        "data_kwargs":{
            'Search_Info':Search_Sentiment_Key_Info,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                Sentiment_All_List = Result.Sentiment_Info;
                Sentiment_All_SnapShot_List = Result.Sentiment_Info;
                Table_Condiction_Param_Count = Result.Table_Condiction_Param_Count;
                // --------------------计算当前页的数据 请求数据的时候处理
                Page_TotleItems = Sentiment_All_List.length;
                Page_Total= Math.ceil(Page_TotleItems / Page_Size);
                document.getElementById('Article_Loading_Count').innerText = '共' + Page_TotleItems + '条消息';
                // 情感属性这个内容的各项数量
                Object.keys(Table_Condiction_Param_Count).forEach(key => {
                    document.getElementById('Label_' + key).innerText = Table_Condiction_Param_Count[key];
                });
                // 渲染界面情报信息
                updatePage();
                // 生成分页页码栏
                generatePagination(Page_current, Page_Total);
                Loading_Hide();
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    sound: false,
                    delay:2000,
                    position: 'top right',
                    msg: '网络出错，请重试！'
                });
                return ;
            };
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
        });
};

function updatePage() {
    // 计算当前页的起始和结束索引
    var startIndex= (Page_current - 1) * Page_Size;
    var endIndex= startIndex + Page_Size - 1;
    // 处理边界情况
    endIndex = Math.min(endIndex, Page_TotleItems - 1);
    // 计算当前页的数据
    let pageData = Sentiment_All_List.slice(startIndex, endIndex + 1);
    renderPageData(pageData);
};
/****************************************监听搜索框Enter事件***************************************************/ 
// 获取 input 元素
const $Search_Sentiment_Key_Element = document.getElementById('Search_Sentiment_Key_Element');

// 监听 keydown 事件
$Search_Sentiment_Key_Element.addEventListener('keydown', function(event) {
  // 检测是否按下回车键
  if (event.key === 'Enter') {
    event.preventDefault();
    Submit_Search_Sentiment();
  };
});
/*-----------------------------------翻页后 渲染此页的情报信息-------------------------------------------*/
function renderPageData(data) {
    console.log('run renderPageData',data)
    var container = document.getElementById('Article_Body_Element');
    container.innerHTML = ''; // 清空容器
    var element_Info = '';
    data.forEach(item => {
        // -----高亮标题
        // 按照字符串长度降序排序，确保较长的项排在前面
        let keywords_Info = item.KEYWORD.sort((a, b) => b.length - a.length);
        // console.log('keywords_Info:',keywords_Info)
        // 生成正则表达式字符串
        let regexStr = keywords_Info.map(word => escapeRegExp(word)).join('|');
        // console.log('regexStr:',regexStr)
        let regex = new RegExp(regexStr, 'g');
        // console.log('regex:',regex)
        item.TITLE = item.TITLE.replace(regex,(match) => {
            // return `<span style="color:#E74C3C;">${match}</span>`;
            return `<span style="color:#ff9800;">${match}</span>`;
        });
        item.CONTENT_SHOW = item.CONTENT_SHOW.replace(regex,(match) => {
            // return `<span style="color:#E74C3C;">${match}</span>`;
            return `<span style="color:#ff9800;">${match}</span>`;
        });
        // 普通模式
        const element = `
            <div class="media align-items-center mt-3" style="position: relative;">
                <div class="media-body ml-1">
                    <div class="media align-items-center">
                        <div class="media-body">
                            <p class="font-weight-bolder mb-0 mt-2 text-white" style="font-size: 1rem;">${item.TITLE}</p>
                            <div class="extra">
                                <i class='fadeIn animated bx bx-receipt font-20 mx-2' data-toggle="tooltip" data-placement="top" title="复核" style="color:#ffc107"  onclick="Review_Sentiment_Hander('${item.EMOTION}')"></i>
                                <i class='fadeIn animated bx bx-star font-20 mx-2' data-toggle="tooltip" data-placement="top" title="收藏" style="color: #ff9900;"></i>
                                <i class='fadeIn animated bx bx-bell font-20 mx-2' data-toggle="tooltip" data-placement="top" title="报警" style="color: #e71616;"></i>
                                <i class='fadeIn animated bx bx-folder font-20 mx-2' data-toggle="tooltip" data-placement="top" title="加入素材库" style="color: #4caf50;"></i>
                                <i class='fadeIn animated bx bx-bookmark-plus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="设为重点网站" style="color: #2196f3;"></i>
                                <i class='fadeIn animated bx bx-bookmark-minus font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此网站" style="color: #f02769;"></i>
                                <i class='fadeIn animated bx bx-hide font-20 mx-2' data-toggle="tooltip" data-placement="top" title="屏蔽此作者" style="color: #e71616;"></i>
                            </div>
                            <p class="mb-0">${item.CONTENT_SHOW}</p>
                        </div>
                    </div>
                    <div class="row mt-2 ml-0">
                        <button type="button" class="btn ${
                            item.DATA_EMOTION === '中性' ? 'btn-outline-warning' :
                            item.DATA_EMOTION === '负面' ? 'btn-outline-danger' :
                            item.DATA_EMOTION === '正面' ? 'btn-outline-info' : 'btn-outline-secondary'
                        } btn-sm" style="padding: 0px 1rem;">${item.DATA_EMOTION}</button>
                        <div class="details_info ml-3">
                            <span class="span_title_info">情报来源:</span>
                            <span class="span_content_info">${item.PLATFORM}</span>
                        </div>

                        <div class="details_info ml-3">
                            <span class="span_title_info">发布人:</span>
                            <span class="span_content_info">${item.AUTHOR}</span>
                        </div>

                        <div class="details_info ml-3">
                            <span class="span_title_info">发布地区:</span>
                            <span class="span_content_info">${item.REGION_AREA}</span>
                        </div>

                        <div class="details_info ml-3">
                            <span class="span_title_info">IP属地:</span>
                            <span class="span_content_info">${item.REGION_IP}</span>
                        </div>

                        <div class="details_info ml-3">
                            <span class="span_title_info">发布时间:</span>
                            <span class="span_content_info">${item.TIME}</span>
                        </div>
                        <div class="details_info ml-3">
                            <span class="span_title_info">中标词:</span>
                            <span class="span_content_info">${item.KEYWORD_LIST_FULL}</span>
                        </div>
                    </div>
                </div>
            </div>
            <hr>`
        element_Info += element;
    });
    container.innerHTML = element_Info;
};

/*-----------------------------------动态生成分页页码-------------------------------------------*/
function generatePagination(currentPage, totalPages) {
    const pagination = document.querySelector('.pagination');
    pagination.innerHTML = ''; // 清空现有的分页
  
    const maxVisiblePages = 10; // 最多显示的页码数（不包括前后导航和省略号）
  
    // 创建“Previous”按钮
    if (currentPage > 1) {
      const prevLi = document.createElement('li');
      prevLi.classList.add('page-item');
      const prevA = document.createElement('a');
      prevA.classList.add('page-link');
      prevA.href = '#';
      prevA.textContent = 'Previous';
      prevLi.appendChild(prevA);
      pagination.appendChild(prevLi);
    }
  
    // 计算开始和结束的页码
    let start = 1;
    let end = totalPages;
    if (totalPages > maxVisiblePages) {
      if (currentPage <= Math.ceil(maxVisiblePages / 2)) {
        end = maxVisiblePages;
      } else if (currentPage + Math.floor(maxVisiblePages / 2) > totalPages) {
        start = totalPages - maxVisiblePages + 1;
      } else {
        start = currentPage - Math.floor(maxVisiblePages / 2);
        end = currentPage + Math.ceil(maxVisiblePages / 2) - 1;
      }
  
      if (start > 1) {
        // 添加前面的省略号
        const ellipsisLi = document.createElement('li');
        ellipsisLi.classList.add('page-item', 'disabled');
        const ellipsisA = document.createElement('a');
        ellipsisA.classList.add('page-link');
        ellipsisA.href = '#';
        ellipsisA.textContent = '...';
        ellipsisLi.appendChild(ellipsisA);
        pagination.appendChild(ellipsisLi);
      }
    }
  
    // 添加页码
    for (let i = start; i <= end; i++) {
      const pageLi = document.createElement('li');
      pageLi.classList.add('page-item');
      if (i === currentPage) {
        pageLi.classList.add('active');
      }
      const pageA = document.createElement('a');
      pageA.classList.add('page-link');
      pageA.href = '#'; // 这里应该替换为实际的链接
      pageA.textContent = i;
      pageLi.appendChild(pageA);
      pagination.appendChild(pageLi);
    }
  
    if (end < totalPages) {
      // 添加后面的省略号
      const ellipsisLi = document.createElement('li');
      ellipsisLi.classList.add('page-item', 'disabled');
      const ellipsisA = document.createElement('a');
      ellipsisA.classList.add('page-link');
      ellipsisA.href = '#';
      ellipsisA.textContent = '...';
      ellipsisLi.appendChild(ellipsisA);
      pagination.appendChild(ellipsisLi);
    }
  
    // 创建“Next”按钮
    if (currentPage < totalPages) {
      const nextLi = document.createElement('li');
      nextLi.classList.add('page-item');
      const nextA = document.createElement('a');
      nextA.classList.add('page-link');
      nextA.href = '#';
      nextA.textContent = 'Next';
      nextLi.appendChild(nextA);
      pagination.appendChild(nextLi);
    }
};

/*-----------------------------------动态监听情感属性选择事件-------------------------------------------*/
const $Sentiment_Emotion_Type_Element = document.getElementById('Sentiment_Emotion_Type_Element');
const $Emotion_Type_1 = document.getElementById('Emotion_Type_1');

// 在主节点上设置事件监听器
$Sentiment_Emotion_Type_Element.addEventListener('change', function(event) {
    // 检查事件的触发元素是否是复选框
    if (event.target.type === 'checkbox') {
        const checkbox = event.target;
        // console.log('checkbox:',checkbox);
        // 如果选中的是“全部”多选框
        if (checkbox.id === 'Emotion_Type_1' && checkbox.checked) {
            // “全部”多选框被选中
            if (checkbox.checked) {
                // 取消其他多选框的选中状态
                const otherCheckboxes = $Sentiment_Emotion_Type_Element.querySelectorAll('.form-check-input:not(#Emotion_Type_1)');
                otherCheckboxes.forEach(function(cb) {
                    cb.checked = false;
                });
                Table_condiction_param.Emotion_Type = [checkbox.value]; //更新筛选项
            } else {
                // “全部”多选框取消选中
                Table_condiction_param.Emotion_Type = []; //更新筛选项
            };
        } else {
            // 如果选中了其他多选框，则取消“全部”多选框的选中状态
            if (checkbox.checked) {
                $Emotion_Type_1.checked = false;
                Table_condiction_param.Emotion_Type.push(checkbox.value); //更新筛选项
                const index = Table_condiction_param.Emotion_Type.indexOf('All');
                if (index !== -1) {
                    Table_condiction_param.Emotion_Type.splice(index, 1);
                };
            } else {
                const index = Table_condiction_param.Emotion_Type.indexOf(checkbox.value);
                if (index !== -1) {
                    Table_condiction_param.Emotion_Type.splice(index, 1);
                };
            };
        };
        console.log('现在选择的情感属性:',Table_condiction_param.Emotion_Type); // 在控制台打印按钮文本内容
        Loading_Show();
        Sentiment_All_List = [];
        console.log(Table_condiction_param.Emotion_Type)
        Sentiment_All_SnapShot_List.forEach(Sentiment => {
            if (Table_condiction_param.Emotion_Type.includes('All') || Table_condiction_param.Emotion_Type.includes(Sentiment.DATA_EMOTION)) {
                Sentiment_All_List.push(Sentiment);
            } else {
                //
            };
        });
        // --------------------计算当前页的数据 请求数据的时候处理
        Page_TotleItems = Sentiment_All_List.length;
        Page_Total= Math.ceil(Page_TotleItems / Page_Size);
        document.getElementById('Article_Loading_Count').innerText = '共' + Page_TotleItems + '条消息';
        Page_current = 1;
        // 渲染界面情报信息
        updatePage();
        // 生成分页页码栏
        generatePagination(Page_current, Page_Total);
        Loading_Hide();
    };
});

/*-----------------------------------动态监听分页页码点击事件-------------------------------------------*/
const paginationContainer = document.querySelector('.pagination');
// 为分页容器添加点击事件监听器
paginationContainer.addEventListener('click', function(event) {
  // 检查被点击的元素是否是分页链接
  if (event.target && event.target.matches('.page-link')) {
    Loading_Show();
    // 阻止默认行为
    event.preventDefault();
    // 获取被点击的链接的页码
    const page = event.target.textContent;
    console.log('获取被点击的链接的页码 page',page);
    if (page === 'Previous') {
        Page_current = parseInt(Page_current - 1);
    } else if (page === 'Next') {
        Page_current = parseInt(Page_current + 1);
    } else {
        Page_current = parseInt(page)
    };

    // 动态生成分页符
    generatePagination(Page_current, Page_Total);
    
    // 页码变化 更新此页的展示情报信息
    updatePage();
    Loading_Hide();
  } else {
    //
  }
});

/*----------------------------------复核-------------------------------------------*/
function Review_Sentiment_Hander(Review_UUID) {
    localStorage.setItem('Review_UUID',Review_UUID);
    // window.location.href="Service_Page?Page=Page_Review";
    window.open("Service_Page?Page=Page_Review", "_blank");
};