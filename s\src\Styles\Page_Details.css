/* 信息监测页面样式 */
.details-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.details-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 筛选区域样式 */
.details-page .ant-space {
  flex-wrap: wrap;
}

.details-page .ant-space-item {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.details-page .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.details-page .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 标签样式 */
.details-page .ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮样式 */
.details-page .ant-btn {
  border-radius: 4px;
}

.details-page .ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.details-page .ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 输入框样式 */
.details-page .ant-input {
  border-radius: 4px;
}

.details-page .ant-select {
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .details-page {
    padding: 16px;
  }

  .details-page .ant-space {
    width: 100%;
  }

  .details-page .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
}


