import { ClassicPreset } from "rete";
import { Progress ,Input,Layout,Space,} from "antd";
import "../Nodes.module.css";

export const Node_Socket = new ClassicPreset.Socket("socket");

export class SystemControl_Object_Inspection extends ClassicPreset.Control {
    constructor(
      public Title: string, 

      public Percent: number,
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Object: Record<string, any>) {
      const safeGet = (key: string) => Object?.[key] || "未知";

      const safeParseInt = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
      };



      this.Title         = safeGet("Title");
      this.Percent       = safeParseInt(safeGet("Percent"));
   
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

  export function Node_System_Object_Inspection(props: { data: SystemControl_Object_Inspection }) {
    return (
    
    <Layout style={{ width: '100%',background:"transparent" }}>


      <Space direction="vertical" size="middle" style={{ width: '100%'}}>


      <Layout   style={{height:40,width:"100%", display: 'flex', alignItems: 'center', justifyContent: 'start', borderRadius: 4,background:"#666",flex:"1",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} > 课题:《境内社群巡查预警》 </span></Layout>


                      
      </Space>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Progress 
              style={{
                  background:"rgba(255,255,255,0.0)",
                  width: "358px", // 设置进度条的长度
                  height: "20px", // 设置进度条的高度
                  border: "0px solid green", // 设置边框颜色为绿色
                  borderRadius: "5px", // 可选，让进度条的边角更圆润
                  lineHeight: "10px", // 设置行高与进度条高度一致
                  padding: 0,          // 清除默认内边距
                  margin: 0            // 清除默认外边距
              }}
              // type="dashboard"
              type="line"
              strokeColor="#52c41a"
              trailColor="rgba(255,255,255,0.6)"
              
              percent={props.data.Percent} 
              // 添加文字样式覆盖👇
              format={(percent) => (
                  <div style={{ 
                    position: "relative",
                    top: "0px",       // 反向偏移量
                    fontSize: "12px",
                    lineHeight: "80px",

                    color: "#fff"
                  }}>
                    {percent}%
                  </div>
                )}

              />
              </Space>

        
    </Layout>
    );
    // return (
    
    
  // <Layout>

  //     <Row>
  //       <Col>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //           <Button style={{color:"red"}}>数源</Button>
  //       </Col>
  //     </Row>


  //   </Layout>)
  }
