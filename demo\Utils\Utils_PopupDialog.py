# -*- coding: utf-8 -*-
import time,os,sys
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtCore import (Slot,Property)

class Utils_Dialog_Normal(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.Dialog_Info={"Dialog_Title":"哨兵提示","Dialog_Content":"未激活，请您先登录哨兵平台","Dialog_Execute":""}
        try:self.Dialog_Info.update(args[0])
        except:pass
        self.resize(348, 170)
        #
        # self.setStyleSheet("QLabel{ background-color:rgba(18, 27, 53, 255)}")
        # self.setStyleSheet("QLabel{background:rgba(255, 255, 255, 1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}")
        self.setStyleSheet("QLabel{background:rgba(25,25,50,1);border-width:0px;border-radius:6px;padding:2px 4px;color:white;border-style: solid;}")
        QVBoxLayout_Dialog = QtWidgets.QVBoxLayout()
        QVBoxLayout_Dialog.setSpacing(0)  # 内边界
        QVBoxLayout_Dialog.setContentsMargins(0, 0, 0, 0)  # 外边


        self.QLabel_Title   = QtWidgets.QLabel()
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Bottom  = QtWidgets.QLabel()

        QVBoxLayout_Dialog.addWidget(self.QLabel_Title,1)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Content,3)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Bottom,1)


        self.setLayout(QVBoxLayout_Dialog)

        self.Set_Title()
        self.Set_Content()
        self.Set_Bottom()

    def Set_Title(self):
        QHBoxLayout_Title = QtWidgets.QHBoxLayout()
        QHBoxLayout_Title.setSpacing(8)  # 内边界
        QHBoxLayout_Title.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Title.setLayout(QHBoxLayout_Title)


        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setMinimumSize(30, 30)
        QLabel_Icon.setMaximumSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment( QtCore.Qt.AlignVCenter)
        # QLabel_Title.setMaximumSize(230, 30)
        QLabel_Title.setText(self.Dialog_Info["Dialog_Title"])

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='black', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setStyleSheet('''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())


        QHBoxLayout_Title.addWidget(QLabel_Icon, 1,alignment=QtCore.Qt.AlignLeft,)
        QHBoxLayout_Title.addWidget(QLabel_Title, 9,alignment=QtCore.Qt.AlignLeft)
        QHBoxLayout_Title.addWidget(QPushButton_Close, 1,alignment=QtCore.Qt.AlignLeft)


        #
        #
        # QLabel_Name = QtWidgets.QLabel()
        # QLabel_Name.setMinimumSize(250, 30)
        # QLabel_Name.setMaximumSize(250, 30)
        # QLabel_Name.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Name.setText("抱歉，您是测试用户无法使用该功能。")
        #


    def Set_Content(self):
        QHBoxLayout_Content = QtWidgets.QHBoxLayout()
        QHBoxLayout_Content.setSpacing(0)  # 内边界
        QHBoxLayout_Content.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Content.setLayout(QHBoxLayout_Content)

        QLabel_Content = QtWidgets.QLabel()
        QLabel_Content.setStyleSheet("background: transparent; border: none;")
        QLabel_Content.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 14))
        QLabel_Content.setText(self.Dialog_Info["Dialog_Content"])
        # QLabel_Content.clicked.connect(lambda: self.Dialog_Close())

        QHBoxLayout_Content.addWidget(QLabel_Content)



    def Set_Bottom(self):
        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout()
        QHBoxLayout_Bottom.setSpacing(0)  # 内边界
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Bottom.setLayout(QHBoxLayout_Bottom)

        QLabel_Yes = QLabel_Click()
        QLabel_Yes.setStyleSheet("background: transparent; border: none;")
        QLabel_Yes.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Yes.setText("确定")
        QLabel_Yes.clicked.connect(lambda: self.Dialog_Emit())



        QLabel_No = QLabel_Click()
        QLabel_No.setStyleSheet("background: transparent; border: none;")

        QLabel_No.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_No.setText("取消")
        QLabel_No.clicked.connect(lambda: self.Dialog_Close())



        QHBoxLayout_Bottom.addWidget(QLabel_Yes,5,alignment=QtCore.Qt.AlignRight,)
        QHBoxLayout_Bottom.addWidget(QLabel_No,1,alignment=QtCore.Qt.AlignRight,)

    def Dialog_Close(self):
        self.deleteLater()  # 安排销毁窗口
        self.close()  # 关闭窗口


    def Dialog_Emit(self):
        # print("Dialog_Emit")
        self.Signal_Result.emit({"Command":"Dialog_Emit","Dialog_Info":{}})
        self.Dialog_Close()

class Utils_Dialog_Input(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.Dialog_Info={"Dialog_Title":"修改数据","Dialog_Content":"声纹模型参数","Dialog_Input":"1000","Dialog_Execute":""}
        try:self.Dialog_Info.update(args[0])
        except:pass
        self.resize(348, 170)
        QVBoxLayout_Dialog = QtWidgets.QVBoxLayout()
        QVBoxLayout_Dialog.setSpacing(0)  # 内边界
        QVBoxLayout_Dialog.setContentsMargins(0, 0, 0, 0)  # 外边


        self.QLabel_Title   = QtWidgets.QLabel()
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Bottom  = QtWidgets.QLabel()

        QVBoxLayout_Dialog.addWidget(self.QLabel_Title,1)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Content,3)
        QVBoxLayout_Dialog.addWidget(self.QLabel_Bottom,1)


        self.setLayout(QVBoxLayout_Dialog)

        self.Set_Title()
        self.Set_Content()
        self.Set_Bottom()

    def Set_Title(self):
        QHBoxLayout_Title = QtWidgets.QHBoxLayout()
        QHBoxLayout_Title.setSpacing(0)  # 内边界
        QHBoxLayout_Title.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Title.setLayout(QHBoxLayout_Title)


        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setMinimumSize(30, 30)
        QLabel_Icon.setMaximumSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("background: transparent; border: none;")
        QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment( QtCore.Qt.AlignVCenter)
        # QLabel_Title.setMaximumSize(230, 30)
        QLabel_Title.setText(self.Dialog_Info["Dialog_Title"])

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='black', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setStyleSheet('''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(220,20,60,0.3);border: 0px}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())


        QHBoxLayout_Title.addWidget(QLabel_Icon, 1,alignment=QtCore.Qt.AlignLeft,)
        QHBoxLayout_Title.addWidget(QLabel_Title, 9,alignment=QtCore.Qt.AlignLeft)
        QHBoxLayout_Title.addWidget(QPushButton_Close, 1,alignment=QtCore.Qt.AlignLeft)


        #
        #
        # QLabel_Name = QtWidgets.QLabel()
        # QLabel_Name.setMinimumSize(250, 30)
        # QLabel_Name.setMaximumSize(250, 30)
        # QLabel_Name.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
        # QLabel_Name.setText("抱歉，您是测试用户无法使用该功能。")
        #


    def Set_Content(self):
        QHBoxLayout_Content = QtWidgets.QHBoxLayout()
        QHBoxLayout_Content.setSpacing(0)  # 内边界
        QHBoxLayout_Content.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Content.setLayout(QHBoxLayout_Content)

        QLabel_Content = QtWidgets.QLabel()
        QLabel_Content.setStyleSheet("background: transparent; border: none;")
        QLabel_Content.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 14))
        QLabel_Content.setText(self.Dialog_Info["Dialog_Content"])
        # QLabel_Content.clicked.connect(lambda: self.Dialog_Close())




        QHBoxLayout_Content.addWidget(QLabel_Content,alignment=QtCore.Qt.AlignCenter,)
        QHBoxLayout_Content.addWidget(eval(f"self.Input_Select()"),alignment=QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter,)


    def Input_Select(self):
        QComboBox_Input = QtWidgets.QComboBox()
        QComboBox_Input.setStyleSheet("QComboBox{border:1px solid rgb(204,204,204);border-radius:3px;height:28px;background:rgba(255,255,255,1);color:#1E90FF}")
        # QComboBox_Input.
        QComboBox_Input.setMinimumSize(88, 30)
        QComboBox_Input.setMaximumSize(88,30)
        QComboBox_Input.addItems(['联网', '本地'])

        return QComboBox_Input


    def Input_Bool(self):
        Toggle_Input = QCheckBox_AnimatedToggle()

        Toggle_Input.setMinimumSize(68, 40)
        Toggle_Input.setMaximumSize(68,40)

        return Toggle_Input

    #

    def Input_Text(self):
        QLineEdit_Input = QtWidgets.QLineEdit()
        QLineEdit_Input.setText(self.Dialog_Info["Dialog_Input"])
        # QLineEdit_Content.
        QLineEdit_Input.setMinimumSize(58, 20)
        QLineEdit_Input.setMaximumSize(58,20)

        return QLineEdit_Input
    #




    def Input_Number(self):

        QSpinBox_Input  = QtWidgets.QSpinBox()
        QSpinBox_Input.setRange(0, 1000)  # 设置取值范围
        QSpinBox_Input.setSingleStep(1)  # 设置步长
        QSpinBox_Input.setMinimumSize(58,20)
        QSpinBox_Input.setMaximumSize(58,20)
        QSpinBox_Input.setValue(int(str(self.Dialog_Info["Dialog_Input"])))  # 设置默认值

        return QSpinBox_Input




    def Set_Bottom(self):
        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout()
        QHBoxLayout_Bottom.setSpacing(0)  # 内边界
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QLabel_Bottom.setLayout(QHBoxLayout_Bottom)

        QLabel_Yes = QLabel_Click()
        QLabel_Yes.setStyleSheet("background: transparent; border: none;")
        QLabel_Yes.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_Yes.setText("确定")
        QLabel_Yes.clicked.connect(lambda: self.Dialog_Emit())



        QLabel_No = QLabel_Click()
        QLabel_No.setStyleSheet("background: transparent; border: none;")

        QLabel_No.setAlignment(QtCore.Qt.AlignVCenter)
        QLabel_No.setText("取消")
        QLabel_No.clicked.connect(lambda: self.Dialog_Close())



        QHBoxLayout_Bottom.addWidget(QLabel_Yes,5,alignment=QtCore.Qt.AlignRight,)
        QHBoxLayout_Bottom.addWidget(QLabel_No,1,alignment=QtCore.Qt.AlignRight,)

    def Dialog_Close(self):
        self.deleteLater()  # 安排销毁窗口
        self.close()  # 关闭窗口


    def Dialog_Emit(self):
        # print("Dialog_Emit")
        self.Signal_Result.emit({"Command":"Dialog_Emit","Dialog_Info":{}})
        self.Dialog_Close()

















class QLabel_Click(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class QCheckBox_AnimatedToggle(QtWidgets.QCheckBox):

    _transparent_pen = QtGui.QPen(QtCore.Qt.transparent)
    _light_grey_pen = QtGui.QPen(QtCore.Qt.lightGray)

    def __init__(self,
        parent=None,
        bar_color=QtCore.Qt.gray,
        checked_color="#00B0FF",
        handle_color=QtCore.Qt.white,
        pulse_unchecked_color="#44999999",
        pulse_checked_color="#4400B0EE"
        ):
        super().__init__(parent)

        # Save our properties on the object via self, so we can access them later
        # in the paintEvent.
        self._bar_brush = QtGui.QBrush(bar_color)
        self._bar_checked_brush = QtGui.QBrush(QtGui.QColor(checked_color).lighter())

        self._handle_brush = QtGui.QBrush(handle_color)
        self._handle_checked_brush = QtGui.QBrush(QtGui.QColor(checked_color))

        self._pulse_unchecked_animation = QtGui.QBrush(QtGui.QColor(pulse_unchecked_color))
        self._pulse_checked_animation = QtGui.QBrush(QtGui.QColor(pulse_checked_color))

        # Setup the rest of the widget.

        self.setContentsMargins(8, 0, 8, 0)
        self._handle_position = 0

        self._pulse_radius = 0

        self.animation = QtCore.QPropertyAnimation(self, b"handle_position", self)
        self.animation.setEasingCurve(QtCore.QEasingCurve.InOutCubic)
        self.animation.setDuration(200)  # time in ms

        self.pulse_anim = QtCore.QPropertyAnimation(self, b"pulse_radius", self)
        self.pulse_anim.setDuration(350)  # time in ms
        self.pulse_anim.setStartValue(10)
        self.pulse_anim.setEndValue(20)

        self.animations_group = QtCore.QSequentialAnimationGroup()
        self.animations_group.addAnimation(self.animation)
        self.animations_group.addAnimation(self.pulse_anim)

        self.stateChanged.connect(self.setup_animation)

    def sizeHint(self):
        return QtCore.QSize(58, 45)

    def hitButton(self, pos: QtCore.QPoint):
        return self.contentsRect().contains(pos)

    @Slot(int)
    def setup_animation(self, value):
        self.animations_group.stop()
        if value:
            self.animation.setEndValue(1)
        else:
            self.animation.setEndValue(0)
        self.animations_group.start()

    def paintEvent(self, e: QtGui.QPaintEvent):

        contRect = self.contentsRect()
        handleRadius = round(0.24 * contRect.height())

        p = QtGui.QPainter(self)
        p.setRenderHint(QtGui.QPainter.Antialiasing)

        p.setPen(self._transparent_pen)
        barRect = QtCore.QRectF(
            0, 0,
            contRect.width() - handleRadius, 0.40 * contRect.height()
        )
        barRect.moveCenter(contRect.center())
        rounding = barRect.height() / 2

        # the handle will move along this line
        trailLength = contRect.width() - 2 * handleRadius

        xPos = contRect.x() + handleRadius + trailLength * self._handle_position

        if self.pulse_anim.state() == QtCore.QPropertyAnimation.Running:
            p.setBrush(
                self._pulse_checked_animation if
                self.isChecked() else self._pulse_unchecked_animation)
            p.drawEllipse(QtCore.QPointF(xPos, barRect.center().y()),
                          self._pulse_radius, self._pulse_radius)

        if self.isChecked():
            p.setBrush(self._bar_checked_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setBrush(self._handle_checked_brush)

        else:
            p.setBrush(self._bar_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setPen(self._light_grey_pen)
            p.setBrush(self._handle_brush)

        p.drawEllipse(
            QtCore.QPointF(xPos, barRect.center().y()),
            handleRadius, handleRadius)

        p.end()

    @Property(float)
    def handle_position(self):
        return self._handle_position

    @handle_position.setter
    def handle_position(self, pos):
        """change the property
        we need to trigger QWidget.update() method, either by:
            1- calling it here [ what we're doing ].
            2- connecting the QPropertyAnimation.valueChanged() signal to it.
        """
        self._handle_position = pos
        self.update()

    @Property(float)
    def pulse_radius(self):
        return self._pulse_radius

    @pulse_radius.setter
    def pulse_radius(self, pos):
        self._pulse_radius = pos
        self.update()



# class Utils_Dialog_Normal(QtWidgets.QLabel):
#     Signal_Result = QtCore.Signal(dict)
#
#     def __init__(self, parent=None, *args):
#         super().__init__(parent)
#         QVBoxLayout_Dialog = QtWidgets.QVBoxLayout()
#         QVBoxLayout_Dialog.setSpacing(0)  # 内边界
#         QVBoxLayout_Dialog.setContentsMargins(0, 0, 0, 0)  # 外边
#
#
#         self.QLabel_Title   = QtWidgets.QLabel()
#         self.QLabel_Content = QtWidgets.QLabel()
#         self.QLabel_Bottom  = QtWidgets.QLabel()
#
#         QVBoxLayout_Dialog.addWidget(self.QLabel_Title,1)
#         QVBoxLayout_Dialog.addWidget(self.QLabel_Content,3)
#         QVBoxLayout_Dialog.addWidget(self.QLabel_Bottom,1)
#
#
#         self.setLayout(QVBoxLayout_Dialog)
#
#         self.Set_Title()
#         self.Set_Content()
#         self.Set_Bottom()
#
#     def Set_Title(self):
#         QHBoxLayout_Title = QtWidgets.QHBoxLayout()
#         QHBoxLayout_Title.setSpacing(0)  # 内边界
#         QHBoxLayout_Title.setContentsMargins(0, 0, 0, 0)  # 外边
#         self.QLabel_Title.setLayout(QHBoxLayout_Title)
#
#
#         QLabel_Icon = QtWidgets.QLabel()
#         QLabel_Icon.setMinimumSize(30, 30)
#         QLabel_Icon.setMaximumSize(30, 30)
#         QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
#         Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
#         Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
#         QLabel_Icon.resize(28, 28)
#         QLabel_Icon.setScaledContents(True)
#         QLabel_Icon.setPixmap(Image_Logo)
#         # QLabel_Icon.setText("提示222")
#
#         QLabel_Title = QtWidgets.QLabel()
#         QLabel_Title.setStyleSheet("background: transparent; border: none;")
#         QLabel_Title.setMinimumSize(230, 30)
#         QLabel_Title.setAlignment( QtCore.Qt.AlignVCenter)
#         # QLabel_Title.setMaximumSize(230, 30)
#         QLabel_Title.setText("提示222")
#
#         Icon = qta.icon('ph.x-thin', scale_factor=1, color='black', color_active='blue')
#         QPushButton_Close = QtWidgets.QPushButton(Icon, '')
#         QPushButton_Close.setStyleSheet('''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(220,20,60,0.3);border: 0px}''')
#         # QPushButton_Exit.setSizePolicy(Button_Adaptive)
#         QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())
#
#
#         QHBoxLayout_Title.addWidget(QLabel_Icon, 1,alignment=QtCore.Qt.AlignLeft,)
#         QHBoxLayout_Title.addWidget(QLabel_Title, 9,alignment=QtCore.Qt.AlignLeft)
#         QHBoxLayout_Title.addWidget(QPushButton_Close, 1,alignment=QtCore.Qt.AlignLeft)
#
#
#         #
#         #
#         # QLabel_Name = QtWidgets.QLabel()
#         # QLabel_Name.setMinimumSize(250, 30)
#         # QLabel_Name.setMaximumSize(250, 30)
#         # QLabel_Name.setStyleSheet('background:#transparent;border:0px solid #002040;font-size:6px')
#         # QLabel_Name.setText("抱歉，您是测试用户无法使用该功能。")
#         #
#
#
#     def Set_Content(self):
#         QHBoxLayout_Content = QtWidgets.QHBoxLayout()
#         QHBoxLayout_Content.setSpacing(0)  # 内边界
#         QHBoxLayout_Content.setContentsMargins(0, 0, 0, 0)  # 外边
#         self.QLabel_Content.setLayout(QHBoxLayout_Content)
#
#         QLabel_Content = QtWidgets.QLabel()
#         QLabel_Content.setStyleSheet("background: transparent; border: none;")
#         QLabel_Content.setAlignment(QtCore.Qt.AlignVCenter)
#         QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 14))
#         QLabel_Content.setText("您是否确定执行？")
#         # QLabel_Content.clicked.connect(lambda: self.Dialog_Close())
#
#         QHBoxLayout_Content.addWidget(QLabel_Content)
#
#
#
#     def Set_Bottom(self):
#         QHBoxLayout_Bottom = QtWidgets.QHBoxLayout()
#         QHBoxLayout_Bottom.setSpacing(0)  # 内边界
#         QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边
#         self.QLabel_Bottom.setLayout(QHBoxLayout_Bottom)
#
#         QLabel_Yes = QLabel_Click()
#         QLabel_Yes.setStyleSheet("background: transparent; border: none;")
#         QLabel_Yes.setAlignment(QtCore.Qt.AlignVCenter)
#         QLabel_Yes.setText("确定")
#         QLabel_Yes.clicked.connect(lambda: self.Dialog_Emit())
#
#
#
#         QLabel_No = QLabel_Click()
#         QLabel_No.setStyleSheet("background: transparent; border: none;")
#
#         QLabel_No.setAlignment(QtCore.Qt.AlignVCenter)
#         QLabel_No.setText("取消")
#         QLabel_No.clicked.connect(lambda: self.Dialog_Close())
#
#
#
#         QHBoxLayout_Bottom.addWidget(QLabel_Yes,5,alignment=QtCore.Qt.AlignRight,)
#         QHBoxLayout_Bottom.addWidget(QLabel_No,1,alignment=QtCore.Qt.AlignRight,)
#
#     def Dialog_Close(self):
#         self.deleteLater()  # 安排销毁窗口
#         self.close()  # 关闭窗口
#
#
#     def Dialog_Emit(self):
#         # print("Dialog_Emit")
#         self.Signal_Result.emit({"Command":"Dialog_Emit","Dialog_Info":{}})
#         self.Dialog_Close()