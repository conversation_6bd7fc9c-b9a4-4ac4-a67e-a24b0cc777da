'use strict';

$(document).ready(function () {
    /*---------------------------------------
        Peity
    ----------------------------------------*/

    // Bar
    if($('.peity-bar')[0]) {
        $('.peity-bar').each(function() {
            var peityWidth = ($(this).attr('data-width')) ? $(this).attr('data-width') : 65;

            $(this).peity('bar', {
                height: 36,
                fill: ['rgba(255,255,255,0.85)'],
                width: peityWidth,
                padding: 0.2
            });
        });
    }

    // Line
    if($('.peity-line')[0]) {
        $('.peity-line').each(function() {
            var peityWidth = ($(this).attr('data-width')) ? $(this).attr('data-width') : 65;

            $(this).peity('line', {
                height: 50,
                fill: 'rgba(255,255,255,0.8)',
                stroke: 'rgba(255,255,255,0)',
                width: peityWidth,
                padding: 0.2
            });
        });
    }
 
    // Pie
    if($('.peity-pie')[0]) {
        $('.peity-pie').each(function() {
            $(this).peity('pie', {
                fill: ['#fff', 'rgba(255,255,255,0.6)', 'rgba(255,255,255,0.2)'],
                height: 50,
                width: 50
            });
        });
    }

    // Donut
    if($('.peity-donut')[0]) {
        $('.peity-donut').each(function() {
            $(this).peity('donut', {
                fill: ['#fff', 'rgba(255,255,255,0.6)', 'rgba(255,255,255,0.2)'],
                height: 50,
                width: 50
            });
        });
    }


    /*---------------------------------------
        Easy Pie Charts
    ----------------------------------------*/
    if($('.easy-pie-chart')[0]) {
        $('.easy-pie-chart').each(function () {
            var value = $(this).data('value');
            var size = $(this).data('size');
            var trackColor = $(this).data('track-color');
            var barColor = $(this).data('bar-color');

            $(this).find('.easy-pie-chart__value').css({
                lineHeight: (size - 2)+'px',
                fontSize: (size/5)+'px',
                color: barColor
            });

            $(this).easyPieChart ({
                easing: 'easeOutBounce',
                barColor: barColor,
                trackColor: trackColor,
                scaleColor: 'rgba(255,255,255,0.15)',
                lineCap: 'round',
                lineWidth: 1,
                size: size,
                animate: 3000,
                onStep: function(from, to, percent) {
                    $(this.el).find('.percent').text(Math.round(percent));
                }
            })
        });
    }
});