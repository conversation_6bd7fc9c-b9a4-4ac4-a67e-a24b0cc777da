/* 已购服务页面样式 */
.purchased-service-page {
  padding: 24px;
  min-height: 100vh;
}

/* 查询条件卡片 */
.purchased-service-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.purchased-service-page h6 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

/* 表格容器 */
.table-responsive {
  overflow-x: auto;
}

.table-responsive .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.table-responsive .ant-table-thead > tr > th {

  font-weight: 600;

  text-align: center;
}

.table-responsive .ant-table-tbody > tr > td {
  text-align: center;
  padding: 12px 16px;
}

.table-responsive .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.ant-btn-link {
  padding: 4px 8px;
  height: auto;
}

/* 选择器样式 */
.ant-select {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 日期选择器样式 */
.ant-picker {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-picker:focus,
.ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 8px 8px;
}

.ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

/* 模态框内表格样式 */
.ant-modal .ant-table-small {
  font-size: 12px;
}

.ant-modal .ant-table-small .ant-table-thead > tr > th,
.ant-modal .ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

/* 账号列表样式 */
.ant-typography {
  margin-bottom: 8px;
}

.ant-typography code {
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  font-family: 'Courier New', monospace;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 空间组件样式 */
.ant-space {
  width: 100%;
}

.ant-space-item {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .purchased-service-page {
    padding: 16px;
  }
  
  .purchased-service-page h6 {
    font-size: 14px;
  }
  
  .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .table-responsive .ant-table {
    font-size: 12px;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .ant-modal-content {
    max-height: calc(100vh - 32px);
    overflow-y: auto;
  }
}

@media (max-width: 480px) {
  .purchased-service-page {
    padding: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
  
  .ant-select,
  .ant-picker {
    font-size: 12px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 成功状态样式 */
.ant-message-success .anticon {
  color: #52c41a;
}

/* 错误状态样式 */
.ant-message-error .anticon {
  color: #ff4d4f;
}

/* 警告状态样式 */
.ant-message-warning .anticon {
  color: #faad14;
}

/* 信息状态样式 */
.ant-message-info .anticon {
  color: #1890ff;
}

/* 表格滚动条样式 */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 账号详情滚动区域样式 */
.ant-modal .ant-typography code {
  word-break: break-all;
  white-space: pre-wrap;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* 模态框按钮组样式 */
.ant-modal-footer .ant-btn + .ant-btn {
  margin-left: 8px;
}

/* 查询条件标签样式 */
.purchased-service-page .ant-space > .ant-space-item > span {
  font-weight: 500;
  white-space: nowrap;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 详情按钮样式 */
.ant-btn-primary.ant-btn-sm {
  padding: 4px 12px;
  height: auto;
  font-size: 12px;
}
