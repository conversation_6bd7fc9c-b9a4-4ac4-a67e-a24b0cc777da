import { createRoot } from "react-dom/client";
import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { AreaPlugin, AreaExtensions,Zoom } from "rete-area-plugin";
import {
  BidirectFlow, //多联
  ConnectionPlugin,
  Presets as ConnectionPresets
} from "rete-connection-plugin";
import { ReactPlugin, Presets, ReactArea2D } from "rete-react-plugin";
import {
  AutoArrangePlugin,
  Presets as ArrangePresets
} from "rete-auto-arrange-plugin";
import { DataflowEngine } from "rete-engine";
import {

  ContextMenuExtra,
  ContextMenuPlugin,
  Presets as ContextMenuPresets
} from "rete-context-menu-plugin";
// import { ContextMenuPlugin, Presets as ContextMenuPresets } from 'rete-context-menu-plugin';
// import { ContextMenuContext } from 'rete-context-menu-plugin';
import { eventBus } from '@/Function/Function_nodeEventBus'; // 全局事件总线
// ---------------------------------------------------------------------------------------- 自定义组件
 // @ts-ignore - 忽略下一行的类型检查
import { CustomSwitch,CustomSelect1,CustomNumberInput,CustomTextArea,CustomImage,CustomTextArea_Rumor,Preview_Score,} from "@/Component/Component_NodeFlow/MyButtonComponent";
import { NumberInputControl,SelectControl,TextareaControl,Textarea_Rumor_Control,Control_Preview_Score} from "@/Component/Component_NodeFlow/MyButtonComponent";
// import { TextAreaControl }  from "./TextareaControl";
// import { NumberInputControl} from "./MyButtonComponent";
 // @ts-ignore - 忽略下一行的类型检查
import { CustomNodeComponent } from "./CustomNode";


import { Node_Styles_System,Node_Styles_Object,Node_Styles_Model }from "@/Nodes/Node_Styles"
import { Node_System_Component_Start }from "@/Nodes/Node_System_Component_Start"
import { Node_System_Component_Object }from "@/Nodes/Node_System_Component_Object"
import { Node_System_Component_Output }from "@/Nodes/Node_System_Component_Output"
import { Node_AI_Component_DeepSeek }from "@/Nodes/Node_AI_DeepSeek/Node_AI_Component_DeepSeek"
import { Node_AI_DeepSeek, AIControl_DeepSeek}from "@/Nodes/Node_AI_DeepSeek/Node_AI_Control_DeepSeek"

import {Node_System_Component_Start_WeChat}from "@/Nodes/Node_System_Start_WeChat/Node_System_Component_Start_WeChat"
import {Node_System_Start_WeChat,SystemControl_Start_WeChat}from "@/Nodes/Node_System_Start_WeChat/Node_System_Control_Start_WeChat"

import {Node_System_Component_Algorithm_Load}from "@/Nodes/Node_System_Algorithm_Load/Node_System_Component_Algorithm_Load"
import {Node_System_Algorithm_Load,SystemControl_Algorithm_Load}from "@/Nodes/Node_System_Algorithm_Load/Node_System_Control_Algorithm_Load"
import {Node_System_Component_Algorithm_Sampling}from "@/Nodes/Node_System_Algorithm_Sampling/Node_System_Component_Algorithm_Sampling"
import {Node_System_Algorithm_Sampling,SystemControl_Algorithm_Sampling}from "@/Nodes/Node_System_Algorithm_Sampling/Node_System_Control_Algorithm_Sampling"

import {Node_System_Component_Preview_Dashboard}from "@/Nodes/Node_System_Preview_Dashboard/Node_System_Component_Preview_Dashboard"
import {Node_System_Preview_Dashboard,SystemControl_Preview_Dashboard}from "@/Nodes/Node_System_Preview_Dashboard/Node_System_Control_Preview_Dashboard"
import {Node_System_Component_Preview_Efficient} from "@/Nodes/Node_System_Preview_Efficient/Node_System_Component_Preview_Efficient"
import {Node_System_Preview_Efficient,SystemControl_Preview_Efficient} from "@/Nodes/Node_System_Preview_Efficient/Node_System_Control_Preview_Efficient"
import {Node_System_Component_Preview_Monitor}from "@/Nodes/Node_System_Preview_Monitor/Node_System_Component_Preview_Monitor"
import {Node_System_Preview_Monitor,SystemControl_Preview_Monitor}from "@/Nodes/Node_System_Preview_Monitor/Node_System_Control_Preview_Monitor"
import {Node_System_Component_Preview_Image}from "@/Nodes/Node_System_Preview_Image/Node_System_Component_Preview_Image"
import {Node_System_Preview_Image,SystemControl_Preview_Image}from "@/Nodes/Node_System_Preview_Image/Node_System_Control_Preview_Image"

import {Node_System_Component_Object_Inspection}from "@/Nodes/Node_System_Object_Inspection/Node_System_Component_Object_Inspection"
import {Node_System_Object_Inspection,SystemControl_Object_Inspection}from "@/Nodes/Node_System_Object_Inspection/Node_System_Control_Object_Inspection"

import {Node_Model_Component_Rumor_Community}from "@/Nodes/Node_Model_Rumor_Community/Node_Model_Component_Rumor_Community"
import {Node_Model_Rumor_Community,ModelControl_Rumor_Community}from "@/Nodes/Node_Model_Rumor_Community/Node_Model_Control_Rumor_Community"
import {Node_Model_Component_Rumor_Score} from "@/Nodes/Node_Model_Rumor_Score/Node_Model_Component_Rumor_Score"
import {Node_Model_Rumor_Score,ModelControl_Rumor_Score} from "@/Nodes/Node_Model_Rumor_Score/Node_Model_Control_Rumor_Score"
import {Node_Model_Component_Rumor_Inspection}from "@/Nodes/Node_Model_Rumor_Inspection/Node_Model_Component_Rumor_Inspection"
import {Node_Model_Rumor_Inspection,ModelControl_Rumor_Inspection}from "@/Nodes/Node_Model_Rumor_Inspection/Node_Model_Control_Rumor_Inspection"
import {Node_Model_Component_Rumor_Keywords}from "@/Nodes/Node_Model_Rumor_Keywords/Node_Model_Component_Rumor_Keywords"
import {Node_Model_Rumor_Keywords,ModelControl_Rumor_Keywords} from "@/Nodes/Node_Model_Rumor_Keywords/Node_Model_Control_Rumor_Keywords"
import {Node_Model_Component_Rumor_Source}from "@/Nodes/Node_Model_Rumor_Source/Node_Model_Component_Rumor_Source"
import {Node_Model_Rumor_Source,ModelControl_Rumor_Source} from "@/Nodes/Node_Model_Rumor_Source/Node_Model_Control_Rumor_Source"
import {Node_Model_Component_Rumor_Nature}from "@/Nodes/Node_Model_Rumor_Nature/Node_Model_Component_Rumor_Nature"
import {Node_Model_Rumor_Nature,ModelControl_Rumor_Nature} from "@/Nodes/Node_Model_Rumor_Nature/Node_Model_Control_Rumor_Nature"
import {Node_Model_Component_Rumor_OCR}from "@/Nodes/Node_Model_Rumor_OCR/Node_Model_Component_Rumor_OCR"
import {Node_Model_Rumor_OCR,ModelControl_Rumor_OCR} from "@/Nodes/Node_Model_Rumor_OCR/Node_Model_Control_Rumor_OCR"
import {Node_Model_Component_Rumor_ASR}from "@/Nodes/Node_Model_Rumor_ASR/Node_Model_Component_Rumor_ASR"
import {Node_Model_Rumor_ASR,ModelControl_Rumor_ASR} from "@/Nodes/Node_Model_Rumor_ASR/Node_Model_Control_Rumor_ASR"
import {Node_Model_Component_Rumor_DeepSeek}from "@/Nodes/Node_Model_Rumor_DeepSeek/Node_Model_Component_Rumor_DeepSeek"
import {Node_Model_Rumor_DeepSeek,ModelControl_Rumor_DeepSeek}from "@/Nodes/Node_Model_Rumor_DeepSeek/Node_Model_Control_Rumor_DeepSeek"
import {Node_Model_Component_Rumor_Noise} from "@/Nodes/Node_Model_Rumor_Noise/Node_Model_Component_Rumor_Noise"
import {Node_Model_Rumor_Noise,ModelControl_Rumor_Noise} from "@/Nodes/Node_Model_Rumor_Noise/Node_Model_Control_Rumor_Noise"
import {Node_Model_Component_Rumor_Output}from "@/Nodes/Node_Model_Rumor_Output/Node_Model_Component_Rumor_Output"
import {Node_Model_Rumor_Output,ModelControl_Rumor_Output}from "@/Nodes/Node_Model_Rumor_Output/Node_Model_Control_Rumor_Output"

import {Node_Parameter_Component_Analysis_Length}from "@/Nodes/Node_Parameter_Analysis_Length/Node_Parameter_Component_Analysis_Length"
import {Node_Parameter_Analysis_Length,ParameterControl_Analysis_Length}from "@/Nodes/Node_Parameter_Analysis_Length/Node_Parameter_Control_Analysis_Length"
import {Node_Parameter_Component_Analysis_Tfs_Z}from "@/Nodes/Node_Parameter_Analysis_Tfs_Z/Node_Parameter_Component_Analysis_Tfs_Z"
import {Node_Parameter_Analysis_Tfs_Z,ParameterControl_Analysis_Tfs_Z}from "@/Nodes/Node_Parameter_Analysis_Tfs_Z/Node_Parameter_Control_Analysis_Tfs_Z"
import {Node_Parameter_Component_Analysis_Repeat}from "@/Nodes/Node_Parameter_Analysis_Repeat/Node_Parameter_Component_Analysis_Repeat"
import {Node_Parameter_Analysis_Repeat,ParameterControl_Analysis_Repeat}from "@/Nodes/Node_Parameter_Analysis_Repeat/Node_Parameter_Control_Analysis_Repeat"
import {Node_Parameter_Component_Analysis_Punish}from "@/Nodes/Node_Parameter_Analysis_Punish/Node_Parameter_Component_Analysis_Punish"
import {Node_Parameter_Analysis_Punish,ParameterControl_Analysis_Punish}from "@/Nodes/Node_Parameter_Analysis_Punish/Node_Parameter_Control_Analysis_Punish"
import {Node_Parameter_Component_Analysis_P_value}from "@/Nodes/Node_Parameter_Analysis_P_value/Node_Parameter_Component_Analysis_P_value"
import {Node_Parameter_Analysis_P_value,ParameterControl_Analysis_P_value}from "@/Nodes/Node_Parameter_Analysis_P_value/Node_Parameter_Control_Analysis_P_value"
import {Node_Parameter_Component_Analysis_Top_P}from "@/Nodes/Node_Parameter_Analysis_Top_P/Node_Parameter_Component_Analysis_Top_P"
import {Node_Parameter_Analysis_Top_P,ParameterControl_Analysis_Top_P}from "@/Nodes/Node_Parameter_Analysis_Top_P/Node_Parameter_Control_Analysis_Top_P"
import {Node_Parameter_Component_Analysis_Top_K}from "@/Nodes/Node_Parameter_Analysis_Top_K/Node_Parameter_Component_Analysis_Top_K"
import {Node_Parameter_Analysis_Top_K,ParameterControl_Analysis_Top_K}from "@/Nodes/Node_Parameter_Analysis_Top_K/Node_Parameter_Control_Analysis_Top_K"
import {Node_Parameter_Component_Analysis_Mirostat_Tau}from "@/Nodes/Node_Parameter_Analysis_Mirostat_Tau/Node_Parameter_Component_Analysis_Mirostat_Tau"
import {Node_Parameter_Analysis_Mirostat_Tau,ParameterControl_Analysis_Mirostat_Tau}from "@/Nodes/Node_Parameter_Analysis_Mirostat_Tau/Node_Parameter_Control_Analysis_Mirostat_Tau"
import {Node_Parameter_Component_Analysis_Mirostat_Eta}from "@/Nodes/Node_Parameter_Analysis_Mirostat_Eta/Node_Parameter_Component_Analysis_Mirostat_Eta"
import {Node_Parameter_Analysis_Mirostat_Eta,ParameterControl_Analysis_Mirostat_Eta}from "@/Nodes/Node_Parameter_Analysis_Mirostat_Eta/Node_Parameter_Control_Analysis_Mirostat_Eta"
import {Node_Parameter_Component_Analysis_Mirostat}from "@/Nodes/Node_Parameter_Analysis_Mirostat/Node_Parameter_Component_Analysis_Mirostat"
import {Node_Parameter_Analysis_Mirostat,ParameterControl_Analysis_Mirostat}from "@/Nodes/Node_Parameter_Analysis_Mirostat/Node_Parameter_Control_Analysis_Mirostat"
import {Node_Parameter_Component_Analysis_Medium}from "@/Nodes/Node_Parameter_Analysis_Medium/Node_Parameter_Component_Analysis_Medium"
import {Node_Parameter_Analysis_Medium,ParameterControl_Analysis_Medium}from "@/Nodes/Node_Parameter_Analysis_Medium/Node_Parameter_Control_Analysis_Medium"
import {Node_Parameter_Component_Analysis_Reasoning}from "@/Nodes/Node_Parameter_Analysis_Reasoning/Node_Parameter_Component_Analysis_Reasoning"
import {Node_Parameter_Analysis_Reasoning,ParameterControl_Analysis_Reasoning}from "@/Nodes/Node_Parameter_Analysis_Reasoning/Node_Parameter_Control_Analysis_Reasoning"
import {Node_Parameter_Component_Analysis_Temp}from "@/Nodes/Node_Parameter_Analysis_Temp/Node_Parameter_Component_Analysis_Temp"
import {Node_Parameter_Analysis_Temp,ParameterControl_Analysis_Temp}from "@/Nodes/Node_Parameter_Analysis_Temp/Node_Parameter_Control_Analysis_Temp"
import {Node_Parameter_Component_Analysis_Sequence}from "@/Nodes/Node_Parameter_Analysis_Sequence/Node_Parameter_Component_Analysis_Sequence"
import {Node_Parameter_Analysis_Sequence,ParameterControl_Analysis_Sequence}from "@/Nodes/Node_Parameter_Analysis_Sequence/Node_Parameter_Control_Analysis_Sequence"
import {Node_Parameter_Component_Analysis_Seed}from "@/Nodes/Node_Parameter_Analysis_Seed/Node_Parameter_Component_Analysis_Seed"
import {Node_Parameter_Analysis_Seed,ParameterControl_Analysis_Seed}from "@/Nodes/Node_Parameter_Analysis_Seed/Node_Parameter_Control_Analysis_Seed"

import { Node_DataSource_Component_Rumor }from "@/Nodes/Node_DataSource_Component_Rumor"
import { Node_DataSource_Component_Statistic }from "@/Nodes/Node_DataSource_Component_Statistic"
import { Node_Model_Component_Rumor }from "@/Nodes/Node_Model_Component_Rumor"
import { Node_Preview_Component_Rumor }from "@/Nodes/Node_Preview_Component_Rumor"
import { Node_System_Component_Status }from "@/Nodes/Node_System_Component_Status"
// import { Node_System_Component_Status }from "../Nodes/Node_System_Component_Status"


// import { Node_System_Component } from  "./Node_System";
//  // @ts-ignore - 忽略下一行的类型检查
// import { Node_Process_Component} from  "./Node_Process";
// import { Node_DataSource_Component } from "./Node_DataSource";
// import { Node_Analysis_Component } from "./Node_Analysis";
// import { Node_Model_Component } from "./Node_Model";
// import { Node_Template_Component } from "./Node_Template";

import {Node_System,Node_Process,Node_DataSource,
  Node_DataSource_Score,
  Node_Model,Node_Analysis,Node_Template,Node_Echart,Node_End,Node_Start,Node_Object,Node_Preview} from "@/Component/Component_NodeFlow/Nodes"
import{SelectOption} from "@/Component/Component_NodeFlow/Nodes"


// import{ButtonControl} from "@/Component/Component_NodeFlow/Node_System_Component_Start"
import{CustomButton,ProgressControl_Object,ButtonControl_Start, ButtonControl,ButtonControl_More,ButtonControl_Release,ImageControl_Face, DataSourceControl_Rumor,SystemControl_Object ,DataSourceControl_Statistic,
  ModelControl_Rumor,PreviewControl_Rumor,SystemControl_Output,SystemControl_Status,
  
} from "@/Nodes/Node_Controls"
import{Progress_Object,Button_Release,Button_More,Image_Face,Node_DataSource_Rumor,Node_System_Object,Node_DataSource_Statistic,Node_Preview_Rumor,Node_System_Start,
  Node_Model_Rumor,Node_System_Output,Node_System_Status,
} from "@/Nodes/Node_Controls"



import { CustomSocket } from "@/Nodes/CustomSocket";
import { CustomConnection } from "@/Nodes/CustomConnection";






import { start } from "repl";
import { Service_Requests } from "@/Core/Core_Control"
import CoreConfig from "@/Component/Component_NodeFlow/CoreConfig_Monitor.json"




// ---------------------------------------------------------------------------------------- 配置数据类型
export interface Type_Json {[key: string]: string | any; }
// ---------------------------- 节点列表库
var CoreNodeTree: Type_Json = {}
CoreNodeTree = {"Nodes":{},"Connections":{}}
// ---------------------------- 节点类型列表
var Node_Type_List = CoreConfig.Node_Type_List


// ---------------------------- 课题方案
var WorkFolw_Schems: Type_Json = {}
WorkFolw_Schems={
  "Start":{},
  "Object":{},
  "DataSource":[],
  "Parameter":[],
  "Preview":[],
  "Analysis":[],
  "Model":[],
  "AI":[],
  "Algorithm":[],
  "Template":[],
  "End":[],
  "OnceSource":{},
  "OncePreview":{},
  "OnceOutput":{},
  "OnceModel":{},
  "Statistic":{},

}



// CoreNodeTree["Nodes"]["q"]={}
                           
// CoreNodeTree["Nodes"][String(a.id)]=  {"Class":"System","Type":"系统","Method":"Start",
//   "Explain":"工作流的启动控制",
//   "Status":"未启用",
//   "Schemes":"情报+AI测试",
// }


// Node_Type_List={
//   "Object":    {"Name":"课题", "Title":"【未定义】","Method":{},"Explain":"本工作流的目标功能",},
//   "System":    {"Name":"系统", "Title":"【未定义】","Method":{"Start":"启动分析"},"Explain":"工作流的启动控制",},
//   "Method":    {"Name":"业务", "Title":"【未定义】","Method":{},"Explain":"工作流具体实现业务",},
//   "DataSource":{"Name":"数源", "Title":"【未定义】","Method":{"Start":"国内数据"},"Explain":"开源数据",}

// }
// Node_Type_List =





const socket = new ClassicPreset.Socket("socket");

class NumberNode extends ClassicPreset.Node<
  {},
  { value: ClassicPreset.Socket },
  { value: ClassicPreset.InputControl<"number"> }
> {
  height = 120;
  width = 180;

  constructor(initial: number, change?: () => void) {
    super("Number");
    this.addControl(
      "value",
      new ClassicPreset.InputControl("number", { initial, change })
    );
    this.addOutput("value", new ClassicPreset.Output(socket, "Number"));
  }

  data(): { value: number } {
    return {
      value: this.controls.value.value || 0
    };
  }
}

class AddNode extends ClassicPreset.Node<
  { left: ClassicPreset.Socket; right: ClassicPreset.Socket },
  { value: ClassicPreset.Socket },
  { value: ClassicPreset.InputControl<"number"> }
> {
  height = 190;
  width = 180;

  constructor(
    change?: () => void,
    private update?: (control: ClassicPreset.InputControl<"number">) => void
  ) {
    super("Add");
    const left = new ClassicPreset.Input(socket, "Left");
    const right = new ClassicPreset.Input(socket, "Right");

    left.addControl(
      new ClassicPreset.InputControl("number", { initial: 0, change })
    );
    right.addControl(
      new ClassicPreset.InputControl("number", { initial: 0, change })
    );

    this.addInput("left", left);
    this.addInput("right", right);
    this.addControl(
      "value",
      new ClassicPreset.InputControl("number", {
        readonly: true
      })
    );
    this.addOutput("value", new ClassicPreset.Output(socket, "Number"));
  }

  data(inputs: { left?: number[]; right?: number[] }): { value: number } {
    const leftControl = this.inputs.left?.control as ClassicPreset.InputControl<
      "number"
    >;
    const rightControl = this.inputs.right
      ?.control as ClassicPreset.InputControl<"number">;

    const { left, right } = inputs;
    const value =
      (left ? left[0] : leftControl.value || 0) +
      (right ? right[0] : rightControl.value || 0);

    this.controls.value.setValue(value);

    if (this.update) this.update(this.controls.value);

    return { value };
  }
}




// ---------------------------------------------------------------------------------------- 配置变量

// class ButtonControl extends ClassicPreset.Control {
//   constructor(public label: string, public onClick: () => void) {
//     super();
//   }
// }

// class ProgressControl extends ClassicPreset.Control {
//   constructor(public percent: number) {
//     super();
//   }
// }








// export type SelectOption<T extends string | number> = {
//   value: T;
//   label: string;
// };

// export class SelectControl<T extends string | number = string | number> 
//   extends ClassicPreset.Control {
//   constructor(
//     public options: SelectOption<T>[],
//     public selected: T,
//     public onChange: (value: T) => void
//   ) {
//     super();
//   }
// }









class Connection<
  A extends Node,
  B extends Node
> extends ClassicPreset.Connection<A, B> {}

type Node = NumberNode | AddNode  ;
type ConnProps = Connection<NumberNode, AddNode> | Connection<AddNode, AddNode> ;







type Schemes = GetSchemes<
  Node | Node_Echart  | Node_Template | Node_Model ,
  ConnProps
>;
// type Schemes = GetSchemes<Node, ConnProps>;

type AreaExtra = ReactArea2D<any> | ContextMenuExtra;




export interface EditorApi {
  destroy: () => void;
  FunctionService: (config: Type_Json) => void;
}


export async function createEditor_Monitor(container: HTMLElement) {
  const editor = new NodeEditor<Schemes>();
  const area = new AreaPlugin<Schemes, AreaExtra>(container);
  const connection = new ConnectionPlugin<Schemes, AreaExtra>();
  const render = new ReactPlugin<Schemes, AreaExtra>({ createRoot });
    // @ts-ignore - 忽略下一行的类型检查
  const arrange = new AutoArrangePlugin<Schemes>();
    // @ts-ignore - 忽略下一行的类型检查
  const engine = new DataflowEngine<Schemes>();
  // @ts-ignore - 忽略下一行的类型检查
  const zoom = new Zoom<Schemes>();

  connection.addPreset(() => new BidirectFlow());
  // 定义菜单项的正确方式
type MenuItem = {
  label: string;
  // handler: (context: ContextMenuContext<Schemes>) => void; // 使用 ContextMenuContext
};

// 示例配置
const items: MenuItem[] = [
  {
    label: "删除节点",
    // handler: ({ node, editor }) => {
    //   if (node && editor) {
    //     editor.removeNode(node.id);
    //   }
    // }
  }
];
 





  function process() {
    engine.reset();

    editor
      .getNodes()
      .filter((n) => n instanceof AddNode)
      .forEach((n) => engine.fetch(n.id));
  }


// ---------------------------------------------------------------------------------------- 设置自定义样式

render.addPreset(
  Presets.classic.setup({
    customize: {
       // @ts-ignore - 忽略下一行的类型检查
      connection(data) {
        return CustomConnection;
      },
      // @ts-ignore - 忽略下一行的类型检查
      control(data) {
        if (data.payload instanceof ButtonControl_Start) {
          return Node_System_Start;
        }
        if (data.payload instanceof ButtonControl_Release) {
          return Button_Release;
        }
        if (data.payload instanceof ButtonControl_More) {
          return Button_More;
        }
        if (data.payload instanceof ProgressControl_Object) {
          return Progress_Object;
        }
        if (data.payload instanceof SystemControl_Object) {
          return Node_System_Object;
        }
        if (data.payload instanceof DataSourceControl_Statistic) {
          return Node_DataSource_Statistic;
        }
        if (data.payload instanceof AIControl_DeepSeek) {
          return Node_AI_DeepSeek;
        }
        if (data.payload instanceof ImageControl_Face) {
          return Image_Face;
        }
        if (data.payload instanceof DataSourceControl_Rumor) {
          return Node_DataSource_Rumor;
        }
        if (data.payload instanceof PreviewControl_Rumor) {
          return Node_Preview_Rumor;
        }
        if (data.payload instanceof ModelControl_Rumor) {
          return Node_Model_Rumor;
        }

        if (data.payload instanceof SystemControl_Output) {
          return Node_System_Output;
        }
        if (data.payload instanceof SystemControl_Status) {
          return Node_System_Status;
        }
        if (data.payload instanceof SystemControl_Start_WeChat) {
          return Node_System_Start_WeChat;
        }
        if (data.payload instanceof ModelControl_Rumor_Keywords) {
          return Node_Model_Rumor_Keywords;
        }
        if (data.payload instanceof ModelControl_Rumor_ASR) {
          return Node_Model_Rumor_ASR;
        }
        if (data.payload instanceof ModelControl_Rumor_OCR) {
          return Node_Model_Rumor_OCR;
        }
        if (data.payload instanceof ModelControl_Rumor_Source) {
          return Node_Model_Rumor_Source;
        }
        if (data.payload instanceof ModelControl_Rumor_Nature) {
          return Node_Model_Rumor_Nature;
        }
        if (data.payload instanceof ModelControl_Rumor_Inspection) {
          return Node_Model_Rumor_Inspection;
        }
        if (data.payload instanceof ModelControl_Rumor_Output) {
          return Node_Model_Rumor_Output;
        }
        if (data.payload instanceof SystemControl_Preview_Monitor) {
          return Node_System_Preview_Monitor;
        }
        if (data.payload instanceof SystemControl_Preview_Dashboard) {
          return Node_System_Preview_Dashboard;
        }
        if (data.payload instanceof ModelControl_Rumor_DeepSeek) {
          return Node_Model_Rumor_DeepSeek;
        }
        if (data.payload instanceof SystemControl_Object_Inspection) {
          return Node_System_Object_Inspection;
        }
        if (data.payload instanceof ModelControl_Rumor_Score) {
          return Node_Model_Rumor_Score;
        }
        if (data.payload instanceof SystemControl_Preview_Efficient) {
          return Node_System_Preview_Efficient;
        }
        if (data.payload instanceof SystemControl_Preview_Image) {
          return Node_System_Preview_Image;
        }
        if (data.payload instanceof ModelControl_Rumor_Noise) {
          return Node_Model_Rumor_Noise;
        }
        if (data.payload instanceof ModelControl_Rumor_Community) {
          return Node_Model_Rumor_Community;
        }
        if (data.payload instanceof ParameterControl_Analysis_Length) {
          return Node_Parameter_Analysis_Length;
        }
        if (data.payload instanceof ParameterControl_Analysis_Tfs_Z) {
          return Node_Parameter_Analysis_Tfs_Z;
        }
        if (data.payload instanceof ParameterControl_Analysis_Repeat) {
          return Node_Parameter_Analysis_Repeat;
        }
        if (data.payload instanceof ParameterControl_Analysis_Punish) {
          return Node_Parameter_Analysis_Punish;
        }
        if (data.payload instanceof ParameterControl_Analysis_P_value) {
          return Node_Parameter_Analysis_P_value;
        }
        if (data.payload instanceof ParameterControl_Analysis_Top_P) {
          return Node_Parameter_Analysis_Top_P;
        }
        if (data.payload instanceof ParameterControl_Analysis_Top_K) {
          return Node_Parameter_Analysis_Top_K;
        }
        if (data.payload instanceof ParameterControl_Analysis_Mirostat_Tau) {
          return Node_Parameter_Analysis_Mirostat_Tau;
        }
        if (data.payload instanceof ParameterControl_Analysis_Mirostat_Eta) {
          return Node_Parameter_Analysis_Mirostat_Eta;
        }
        if (data.payload instanceof ParameterControl_Analysis_Mirostat) {
          return Node_Parameter_Analysis_Mirostat;
        }
        if (data.payload instanceof ParameterControl_Analysis_Medium) {
          return Node_Parameter_Analysis_Medium;
        }
        if (data.payload instanceof ParameterControl_Analysis_Reasoning) {
          return Node_Parameter_Analysis_Reasoning;
        }
        if (data.payload instanceof ParameterControl_Analysis_Temp) {
          return Node_Parameter_Analysis_Temp;
        }
        if (data.payload instanceof ParameterControl_Analysis_Sequence) {
          return Node_Parameter_Analysis_Sequence;
        }
        if (data.payload instanceof ParameterControl_Analysis_Seed) {
          return Node_Parameter_Analysis_Seed;
        }
        if (data.payload instanceof SystemControl_Algorithm_Load) {
          return Node_System_Algorithm_Load;
        }
        if (data.payload instanceof SystemControl_Algorithm_Sampling) {
          return Node_System_Algorithm_Sampling;
        }
        
        
        // if (data.payload instanceof SwitchControl) {

        //   console.log('检测到SwitchControl实例');
        //   // return <CustomSwitch data={data.payload as SwitchControl} />;
        //   return CustomSwitch;
        // }
        if (data.payload instanceof SelectControl) {
          return CustomSelect1;
        }
        if (data.payload instanceof NumberInputControl) {
          return CustomNumberInput;
        }
        if (data.payload instanceof ClassicPreset.InputControl) {
          return Presets.classic.Control;
        }

        
        if (data.payload instanceof TextareaControl) {
          return CustomImage;
        }

        if (data.payload instanceof Textarea_Rumor_Control) {
          return CustomTextArea_Rumor;
        }
        if (data.payload instanceof Control_Preview_Score) {
          return Preview_Score;
        }
        // if (data.payload instanceof TextareaControl) {
        //   return CustomTextArea;

        //   // return data.payload.render();
        // }
        return null;
      },


      // @ts-ignore - 忽略下一行的类型检查
      node(data) {

        if (data.payload instanceof Node_System_Component_Start) {
          return Node_Styles_System;
        }

        if (data.payload instanceof Node_System_Component_Object) {
          return Node_Styles_Object;
        }

        if (data.payload instanceof Node_DataSource_Component_Rumor) {
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_DataSource_Component_Statistic) {
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_Model_Component_Rumor) {
          return Node_Styles_Model;
        }
        if (data.payload instanceof Node_Preview_Component_Rumor) {
          return Node_Styles_Model;
        }

        if (data.payload instanceof Node_AI_Component_DeepSeek) {
          return Node_Styles_System;
        }

        if (data.payload instanceof Node_System_Component_Output) {
          return Node_Styles_System;
        }
        if (data.payload instanceof Node_System_Component_Object_Inspection) {
          return Node_Styles_System;
        }

        const nodeTypeToComponent = [
          { type: Node_System       , component: Node_Styles_System },
          { type: Node_Process      , component: Node_Styles_System },
          { type: Node_DataSource   , component: Node_Styles_System },
          { type: Node_Analysis     , component: Node_Styles_System },
          { type: Node_Model        , component: Node_Styles_System },
          { type: Node_Template     , component: Node_Styles_System }
        ];
        
        for (const { type, component } of nodeTypeToComponent) {
          if (data.payload instanceof type) {
            return component;
          }
        }

        const nodeTypeParameter = [
          { type: Node_Parameter_Component_Analysis_Length        , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Tfs_Z         , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Repeat        , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Punish        , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_P_value       , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Top_P         , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Top_K         , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Mirostat_Tau  , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Mirostat_Eta  , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Mirostat      , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Medium        , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Reasoning     , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Temp          , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Sequence      , component: Node_Styles_System },
          { type: Node_Parameter_Component_Analysis_Seed          , component: Node_Styles_System },
        ];
        
        for (const { type, component } of nodeTypeParameter) {
          if (data.payload instanceof type) {
            return component;
          }
        }


        const nodeTypeModel = [
          { type: Node_Model_Component_Rumor_Community      , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Score          , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Inspection     , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Keywords       , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Source         , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Nature         , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_OCR            , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_ASR            , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_DeepSeek       , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Noise          , component: Node_Styles_System },
          { type: Node_Model_Component_Rumor_Output         , component: Node_Styles_System },
        ];
        
        for (const { type, component } of nodeTypeModel) {
          if (data.payload instanceof type) {
            return component;
          }
        }


        const nodeTypePreview = [
          { type: Node_System_Component_Preview_Dashboard   , component: Node_Styles_System },
          { type: Node_System_Component_Preview_Efficient   , component: Node_Styles_System },
          { type: Node_System_Component_Preview_Monitor     , component: Node_Styles_System },
          { type: Node_System_Component_Preview_Image       , component: Node_Styles_System },
        ];
        
        for (const { type, component } of nodeTypePreview) {
          if (data.payload instanceof type) {
            return component;
          }
        }


        const nodeTypeAlgorithm = [
          { type: Node_System_Component_Algorithm_Load      , component: Node_Styles_System },
          { type: Node_System_Component_Algorithm_Sampling  , component: Node_Styles_System },
        ];
        
        for (const { type, component } of nodeTypeAlgorithm) {
          if (data.payload instanceof type) {
            return component;
          }
        }


        return null;

        
      },
      socket(data) {
        return CustomSocket;
      },
      // connection(data) {
      //   return CustomConnection;
      // }
      
    },
 
  })
);
connection.addPreset(ConnectionPresets.classic.setup());

// ---------------------------------------------------------------------------------------- 右键创建节点

const contextMenu = new ContextMenuPlugin<Schemes>({
    items: ContextMenuPresets.classic.setup([


  

      ["系统",[

        // @ts-ignore - 忽略下一行的类型检查
        ["拷贝", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["复制", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["删除", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["排列整理", () =>  {RightMenu_ReSet_Arrange();}],
        // @ts-ignore - 忽略下一行的类型检查
        ["清理缓存", () =>  {RightMenu_System("");}],
        // @ts-ignore - 忽略下一行的类型检查
        ["设置课题", () =>  {RightMenu_System("");}],
      ]],

      ["模板", [
        // @ts-ignore - 忽略下一行的类型检查
        // ["突出谣言", () =>  {
        //   try {
        //     RightMenu_Model("Model_Rumor");
        //     return null;
        //   } catch (error) {
            
        //   }
         
        
        
        // }],

        ["突出谣言", () =>  {
          let NodeNew = new Node_Object("突谣")
          CoreConfig.Node_System_Function["ID"]    = String(NodeNew.id);
          RightMenu_Node_Add(CoreConfig.Node_System_Function);
          RightMenu_Model("Model_Rumor");
          return NodeNew
        }],

        ["数据巡查", () =>  {
          let NodeNew = new Node_Object("数据巡查")
          CoreConfig.Node_System_Function["ID"]    = String(NodeNew.id);
          RightMenu_Node_Add(CoreConfig.Node_System_Function);
          RightMenu_Model("Model_Local_Source");
          return NodeNew
        }],
      
      ]],
                  // Node_Preview_Score
      // -------------------------------------- 右键创建节点
      ["流程",[
        // @ts-ignore - 忽略下一行的类型检查
        ["开始", () => {

          let nodeNew = new Node_System_Component_Start_WeChat("【系统-开始】")
          // let nodeNew = new Node_System_Component_Start("【系统-开始】")
          // nodeNew.setButtonAction(() => {
          //   // alert("新的按钮逻辑被触发！");
          //   Schemes_Start();
          //   // 这里可以访问节点数据或其他逻辑
          //   // console.log("当前节点数据:", nodeNew.data());
          // });
          return nodeNew
        } ],
        
        // @ts-ignore - 忽略下一行的类型检查
        ["课题", () =>  {
          let NodeNew = new Node_System_Component_Object("【课题-未知】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Object);
          return NodeNew
        }],
        ["境内社群巡查", () => {
          let nodeNew = new Node_System_Component_Object_Inspection("【课题-境内社群巡查】")
          return nodeNew
        } ],
       ]],


      ["参数",[
        ["上下文长度",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Length("【上下文长度】") 
        return nodeNew
      } ],
      ["Tfs_Z",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Tfs_Z("【Tfs_Z】") 
        return nodeNew
      } ],
      ["重复最后N个",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Repeat("【重复最后N个】") 
        return nodeNew
      } ],
      ["频率惩罚",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Punish("【频率惩罚】") 
        return nodeNew
      } ],
      ["最小P值",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_P_value("【最小P值】") 
        return nodeNew
      } ],
      ["Top_P",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Top_P("【Top_P】") 
        return nodeNew
      } ],
      ["Top_K",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Top_K("【Top_K】") 
        return nodeNew
      } ],
      ["Mirostat_Tau",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Mirostat_Tau("【Mirostat_Tau】") 
        return nodeNew
      } ],
      ["Mirostat_Eta",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Mirostat_Eta("【Mirostat_Eta】") 
        return nodeNew
      } ],
      ["Mirostat",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Mirostat("【Mirostat】") 
        return nodeNew
      } ],
      ["Medium",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Medium("【Medium】") 
        return nodeNew
      } ],
      ["推理程度",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Reasoning("【推理程度】") 
        return nodeNew
      } ],
      ["温度",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Temp("【温度】") 
        return nodeNew
      } ],
      ["停止序列",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Sequence("【停止序列】") 
        return nodeNew
      } ],
      ["种子值",() =>{
        let nodeNew = new Node_Parameter_Component_Analysis_Seed("【种子值】") 
        return nodeNew
      } ],
    ]],

      ["数源", [
        // @ts-ignore - 忽略下一行的类型检查
        ["开源数据",  () =>  {let NodeNew = new Node_DataSource_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_DataSource_Rumor);return NodeNew}],
        ["数据方向", [
            ["政治", () => new Node_Model("模型【政治】")],
            ["特侦", () => new Node_Model("模型【特侦】")],
            ["暴恐", () => new Node_Model("模型【暴恐】")],
            ["反邪", () => new Node_Model("模型【反邪】")],
            ["食药环", () => new Node_Model("模型【食药环】")],
          ]],
        ["自定义", []
        ],
        
      ]],

        ["模型", [
          ["突谣", () => new Node_Model("模型【突谣】")],
          ["情报方向", [
            ["政治", () => new Node_Model("模型【政治】")],
            ["特侦", () => new Node_Model("模型【特侦】")],
            ["暴恐", () => new Node_Model("模型【暴恐】")],
            ["反邪", () => new Node_Model("模型【反邪】")],
            ["食药环", () => new Node_Model("模型【食药环】")],
            ["过滤器", () => new Node_Model("模型【过滤器】")],
            ["筛选器", () => new Node_Model("模型【筛选器】")],
            ]
          ],
          ["境内社群",() =>{
              let nodeNew = new Node_Model_Component_Rumor_Community("【境内社群】") 
              return nodeNew
          } ],
          ["境内社群-实时评分",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Score("【境内社群-实时评分】") 
            return nodeNew
          } ],
          ["模型-社群巡查",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Inspection("【模型-社群巡查】") 
            return nodeNew
          } ],
          ["模型-关键词",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Keywords("【模型-关键词】") 
            return nodeNew
          } ],
          ["模型-数源模型",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Source("【模型-数源模型】") 
            return nodeNew
          } ],
          ["模型-群聊性质",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Nature("【模型-群聊性质】") 
            return nodeNew
          } ],
          ["模型-OCR分析",() =>{
            let nodeNew = new Node_Model_Component_Rumor_OCR("【模型-OCR分析】") 
            return nodeNew
          } ],
          ["模型-ASR分析",() =>{
            let nodeNew = new Node_Model_Component_Rumor_ASR("【模型-ASR分析】") 
            return nodeNew
          } ],
          ["Ai工具",() =>{
            let nodeNew = new Node_Model_Component_Rumor_DeepSeek("【Ai工具】") 
            return nodeNew
          } ],
          ["模型-降噪模型",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Noise("【模型-降噪模型】") 
            return nodeNew
          } ],
          ["输出-突出谣言",() =>{
            let nodeNew = new Node_Model_Component_Rumor_Output("【输出-突出谣言】") 
            return nodeNew
          } ],
     
          // ["汇总", [
          //   ["LDA(潜在狄利克雷分配)", () =>  new Node_Model("系统")],
          //   ["NMF(非负矩阵分解)", () =>  new Node_Model("系统")],
          
          // ]],
          // ["类聚", [
          //   ["DeepSeep", () =>  new Node_Model("系统")],
          //   ["iERM", () =>  new Node_Model("系统")],
          
          // ]],
          // ["预测", [
          //   ["ARIMA【时间序列分析】",  () =>  new Node_Model("系统")],
          //   ["LSTM（长短期记忆网络）", () =>  new Node_Model("系统")],
          
          // ]],
          // ["异常检测", [
          //   ["I孤立森林（Isolation Forest）", () =>  new Node_Template("系统")],
          //   ["One-Class SVM", () =>  new Node_Template("系统")],
          
          // ]],
          // ["关系", [
          //   ["社交网络图谱构建", () =>  new Node_Template("系统")],
          //   ["社区发现", () =>  new Node_Template("系统")],
          
          // ]],
          // ["权重", [
          //   ["分词与停用词过滤", () =>  new Node_Template("系统")],
          //   ["TF-IDF", () =>  new Node_Template("系统")],
          
          // ]],
      
      ]],
      ["算法", [
        ["基础算法", [
          ["LDA(潜在狄利克雷分配)", () =>  new Node_Model("系统")],
          ["NMF(非负矩阵分解)", () =>  new Node_Model("系统")],
        ]],
        ["简易加载器",() =>
        {let NodeNew = new Node_System_Component_Algorithm_Load("【简易加载器】");  CoreConfig.Node_System_Algorithm["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Algorithm);return NodeNew}
      ],
      ["简易K采样器",() =>
        {let NodeNew = new Node_System_Component_Algorithm_Sampling("【简易K采样器】");  CoreConfig.Node_System_Algorithm["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Algorithm);return NodeNew}
      ],
          // @ts-ignore - 忽略下一行的类型检查
          ["AI-DeepSeek", () =>  {let NodeNew = new Node_AI_Component_DeepSeek("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_AI_DeepSeek);return NodeNew}],
          // @ts-ignore - 忽略下一行的类型检查
          ["AI-ChatGLM",  () =>  {let NodeNew = new Node_Model("【AI-ChatGLM】");  CoreConfig.Node_AI_ChatGLM["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_AI_ChatGLM);return NodeNew}],
    
        // ["DeepSeek", () =>  new Node_Template("模型【DeepSeek】")],
        // ["ChatGLM", () =>  new Node_Template("ChatGLM")],
        // ["iERM", () =>  new Node_Template("iERM")],
      
      ]],

      ["预览", [

        // @ts-ignore - 忽略下一行的类型检查
        ["突出谣言", () =>  {let NodeNew = new Node_Preview_Score("【预览-突出谣言】"); CoreConfig.Node_System_Preview["ID"]    = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Preview);return NodeNew}],
        // ["评分", () =>  {
        //   let nodeNew = new Node_Preview_Score("预览【等级】")
        //   // RightMenu_Nade_Add(String(nodeNew.id),"Method","1","评分","OncePreview");
        //   return nodeNew
        // }],
      ["论坛", () =>  new Node_Template("论坛")],
      ["实时数据监控",() =>{
        let nodeNew = new Node_System_Component_Preview_Monitor("【实时数据监控】") 
        return nodeNew
      } ],
      ["数据看板",() =>{
        let nodeNew = new Node_System_Component_Preview_Dashboard("【数据看板-社群巡查】") 
        return nodeNew
      } ],
      ["预览-有效舆情",() =>{
        let nodeNew = new Node_System_Component_Preview_Efficient("【预览-有效舆情】") 
        return nodeNew
      } ],
      ["预览-图像预览",() =>{
        let nodeNew = new Node_System_Component_Preview_Image("【预览-图像预览】") 
        return nodeNew
      } ],


      ]],
      ["输出", [
        
        // @ts-ignore - 忽略下一行的类型检查
        ["突出谣言", () =>  {let NodeNew = new Node_System_Component_Output("【输出-突出谣言】"); CoreConfig.Node_System_Output["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Output);return NodeNew}],
    
      ["汇总", () =>  new Node_Template("汇总")],


      ]],

    ])
  });
  area.use(contextMenu);



// ---------------------------------------------------------------------------------------- 界面系统功能配置

  AreaExtensions.selectableNodes(area, AreaExtensions.selector(), {
    accumulating: AreaExtensions.accumulateOnCtrl()
  });

  render.addPreset(Presets.contextMenu.setup());
  render.addPreset(Presets.classic.setup());

  connection.addPreset(ConnectionPresets.classic.setup());

  arrange.addPreset(ArrangePresets.classic.setup());

  editor.use(engine);
  editor.use(area);
  area.use(connection);
  area.use(render);
  area.use(arrange);
  // area.use(zoom);
  

  AreaExtensions.simpleNodesOrder(area);
  AreaExtensions.showInputControl(area);

  editor.addPipe((context) => {
    if (["connectioncreated", "connectionremoved"].includes(context.type)) {
      process();
    }
    return context;
  });





// 安全节点创建函数

// async function createAndAddNode(editor: NodeEditor<Schemes>, type: string) {
//   const node = new ClassicPreset.Node(type);
//   node.id = `${type}-${Date.now()}`; // 时间戳保证唯一

//   if (!editor.getNode(node.id)) {
//     await editor.addNode(node);
//     return node;
//   }
//   return null;
// }




  // const a = new Node_Echart("系统【开始】");
  // RightMenu_Nade_Add(String(a.id),"System","Start","开始","");
  // // a.setLabel("新标签");
  // // CoreNodeTree["Nodes"][String(a.id)]=  {"Class":"System","Type":"系统","Method":"Start",
  // //   "Explain":"工作流的启动控制",
  // //   "Status":"未启用",
  // //   "Schemes":"情报+AI测试",
  // // }
  

  // // a.addOutput("a", new ClassicPreset.Output(socket));

  // const progressControl = new ProgressControl(0);
  // // const switchControl = new SwitchControl(true,  (checked) => {
  // //     // console.log('回调收到新值:', checked);
  // //     console.log('Switch 状态变更:', checked);

  // //     area.update("control", switchControl.id);
  // //     // this.update(); // 触发节点更新
      
  // // } );



  // const options: SelectOption<string>[] = [
  //   { value: 'once', label: '即时执行' },
  //   { value: 'Loop', label: '循环执行' },
  //   // { value: 'Timed', label: '定时运行' },
  // ];
  // const selectControl = new SelectControl<string>(
  //   options,
  //   'Loop', // 默认选中的值
    
  //   (value) => {
  //     console.log('Selected value:', value);
  //     WorkFolw_Schems["Start"]["Method"] = value;
  //     area.update("control", selectControl.id);
  //   }
  // );




  // const inputControl = new ClassicPreset.InputControl("number", {
  //   initial: 0,
  //   change(value) {
  //     progressControl.percent = value;
  //     console.log("OK",  a.controls);

  //     // handleSubmit({ name: 'John Doe', email: '<EMAIL>' })
  //     area.update("control", progressControl.id);
  //   }
  // });








  
  // a.addControl("input1",inputControl); // 添加下拉菜单的具体选项
  // a.addControl("input", inputControl);
  // a.addControl("control", switchControl);
  // a.addControl("progress", progressControl);
  // a.addControl("control1", selectControl);


  // const numberControl = new NumberInputControl(1, 0, 10, 1,(value) => {
  //   console.log('[回调] 新值:', value); // 确保此处能触发
  //   area.update("control", numberControl.id);
  // });
  // a.addControl("quantity", numberControl)




  // a.addControl(
  //   "button",
  //   new ButtonControl("启动", () => {
  //     const percent = Math.round(Math.random() * 100);

  //     // inputControl.setValue(percent);
  //     // area.update("control", inputControl.id);

  //     // progressControl.percent = percent;
  //     // area.update("control", progressControl.id);

  //     // area.update("control", numberControl.id);

  //     Schemes_Start();


  //   })



    
  // );
  // await editor.addNode(a);


  //   "ID":Node_ID,
  //   "Class":Type_Info.Name,
  //   "Type":Node_Type,
  //   "Method":Node_Method,
  //   "Explain":Type_Info.Explain,
  //   "Status":"未启用",
  //   "Schemes":"情报+AI测试",

// ----------------------------------------------------------------------------------------

  let System_Start = new Node_System_Component_Start("【系统-开始】")

  let Info ={}
  // RightMenu_Nade_Add(String(Node_System_Start.id),"DataSource","Score","评分","OnceSource");
  
  CoreConfig.Node_System_Start["ID"]    = String(System_Start.id)
  RightMenu_Node_Add(CoreConfig.Node_System_Start);

  // System_Start.setButtonAction(() => {Schemes_Start();});
  // 设置回调并接收参数
  (System_Start.controls.Content as ButtonControl_Start).setButtonAction((nodeId, action) => {
    console.log("节点 ID:", nodeId);
    console.log("动作类型:", action);

    Schemes_Start()
    // const buttonElement = document.getElementById('Button_Left_Node');
    //   if (buttonElement) {
    //     buttonElement.click();
    //   }
  });
  await editor.addNode(System_Start);
    // const element = document.getElementById('System_Status');
    // if (element) {
    //   element.style.backgroundColor = 'yellow'; // 修改样式
    // }

    // // 这里可以访问节点数据或其他逻辑
    // console.log("当前节点数据:", Node_System_Start.data());
  // });



  // const b = new Node_Preview("系统【开始】");
  // RightMenu_Nade_Add(String(b.id),"System","Start","开始");

  // await editor.addNode(b);





  // const a1 = new NumberNode(1, process);
  // const b = new NumberNode(1, process);
  // const c = new AddNode(process, (c) => area.update("control", c.id));


  // const d = new Node_Template("情报");



  // const con1 = new Connection(a1, "value", c, "left");
  // const con2 = new Connection(b, "value", c, "right");

  // await editor.addNode(a1);
  // await editor.addNode(b);
  // await editor.addNode(c);
  // await editor.addNode(d);

  // await editor.addConnection(con1);
  // await editor.addConnection(con2);







//  function  RightMenu_Nade_Add(Node_ID:string,Node_Class:string,Node_Type:string,Node_Method:string,Tag:string){



// 

function RemoveNode(Node_ID:string) {
  // await editor.removeNode(Node_ID)
  const node = editor.getNode(Node_ID);
  if (node) {
     editor.removeNode(Node_ID);
  } else {
    console.warn(`节点 ${Node_ID} 不存在，跳过删除`);
  }
  // await 
}

// ---------------------------------------------------------------------------------------- 功能函数
// 右键添加函数
 function  RightMenu_Node_Add(Node_Add_Info:Type_Json){

    console.log("Node_Add_Info",Node_Add_Info)
    CoreNodeTree["Nodes"][Node_Add_Info["ID"]]= Node_Add_Info 

    console.log("CoreNodeTree",CoreNodeTree)

    // WorkFolw_Schems={
    //   "Start":{},
    //   "Object":{},
    //   "DataSource":[],
    //   "Analysis":[],
    //   "Model":[],
    //   "AI":[],
    //   "Template":[],
    //   "End":[],
    //   "OnceSource":{},
    //   "OncePreview":{},
    let nodes = editor.getNodes();
    let Node_Start       = nodes.find((node) => node.id === Node_Add_Info["ID"]);

    if  (Node_Start) {
      console.log(`节点 ${Node_Add_Info["ID"]}`);
    }else{  console.warn(`节点 ${Node_Add_Info["ID"]} 不存在，跳过删除`);}

    if ( ["Functon"].includes(Node_Add_Info["Type"])) {
      console.log("Node_Add_Info",Node_Add_Info)
      // let nodes = editor.getNodes();
      // let Node      = nodes.find((node) => node.id === Node_Add_Info["ID"]);
      // await editor.removeNode(Node_Add_Info["ID"])
      // RemoveNode(Node_Add_Info["ID"])

      setTimeout(() => {
        RemoveNode(Node_Add_Info["ID"]);
      }, 300);

    }

    const NowTime = new Date();
    Node_Add_Info["Update"] = `${NowTime.toLocaleDateString()} ${NowTime.toLocaleTimeString()}`

    if ( ["System"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]] =  Node_Add_Info} catch (error) {}
    }
    if ( ["DataSource",].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]].push(Node_Add_Info)} catch (error) {}
    }
    if ( ["Model"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems["Model"].push(Node_Add_Info)} catch (error) {}
    }
    if ( ["Parameter"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems["Parameter"].push(Node_Add_Info)} catch (error) {}
    }
    if ( ["Preview"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems["Preview"].push(Node_Add_Info)} catch (error) {}
    }
    if ( ["AI"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems["AI"].push(Node_Add_Info)} catch (error) {}
    }
    if ( ["Algorithm"].includes(Node_Add_Info["Class"])) {
      try {WorkFolw_Schems["Algorithm"].push(Node_Add_Info)} catch (error) {}
    }

    if ( ["OncePreview","OnceSource","OnceOutput","Statistic"].includes(Node_Add_Info["Type"])) {
      try {WorkFolw_Schems[Node_Add_Info["Type"]]=  Node_Add_Info} catch (error) {}
    }

    // try {
    //   WorkFolw_Schems[Node_Add_Info["Module"]].push(Node_Add_Info)
    // } catch (error) {
    //   console.log(error)
      
    // }
    
    console.log("WorkFolw_Schems",WorkFolw_Schems)
    

//   // let NewNode  = editor.getNode(Node_ID)
//   // area.translate(a.id, { x: 120, y:700 });
//   let Type_Info =Node_Type_List[Node_Class];
  // CoreNodeTree["Nodes"][Node_ID]={
  //   "ID":Node_ID,
  //   "Class":Type_Info.Name,
  //   "Type":Node_Type,
  //   "Method":Node_Method,
  //   "Explain":Type_Info.Explain,
  //   "Status":"未启用",
  //   "Schemes":"情报+AI测试",
  // }


// // Node_Class =

//     try {
//       WorkFolw_Schems[Tag] =  CoreNodeTree["Nodes"][Node_ID]
//     } catch (error) {
      
//     }


//     switch (Node_Type) {
//       case "Start":
//         WorkFolw_Schems["Start"] = CoreNodeTree["Nodes"][Node_ID]

//         break;
//       case "Object":
//           WorkFolw_Schems["Object"] = CoreNodeTree["Nodes"][Node_ID]
//           break;
//       default:
//         break;
//     }
    

  // NewNode.addControl(
  //   "button",
  //   new ButtonControl("启动", () => {
      


  //   })
  // )

  
 }
// 课题自动生成
 async function  RightMenu_Model(Model:string){

  let NodeNew = null
  for (let Node_Name of ["Object","DataSource","AI","ChatGLM","Output","Statistic","Status","Algorithm_1","Algorithm_2"
    ,"Model_1","Model_2","Model_3","Model_4","Model_5","Model_6","Model_7","Model_8","Model_9","Model_10"
    ,"Preview_1","Preview_2","Preview_3","Preview_4"
    ,"Parameter_1","Parameter_2","Parameter_3","Parameter_4","Parameter_5","Parameter_6","Parameter_7","Parameter_8","Parameter_9"
    ,"Parameter_10","Parameter_11","Parameter_12","Parameter_13","Parameter_14"
  ]) {
    console.log("Node",Node_Name);
    switch (Node_Name) {
  
      case "Object":
         // @ts-ignore - 忽略下一行的类型检查
         NodeNew= new Node_System_Component_Object_Inspection("【课题-本地数据巡查】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Object)
        break
      case "Model_1":
         // @ts-ignore - 忽略下一行的类型检查
         NodeNew = new Node_Model_Component_Rumor_Score("【数源-境内社群实时评分】");  CoreConfig.Node_Model_Rumor["ID_1"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_2":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Source("【模型-数源模型】");  CoreConfig.Node_Model_Rumor["ID_2"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_3":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_OCR("【模型-OCR分析】");  CoreConfig.Node_Model_Rumor["ID_3"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_4":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Noise("【模型-降噪模型】");  CoreConfig.Node_Model_Rumor["ID_4"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_5":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Keywords("【模型-关键词】");  CoreConfig.Node_Model_Rumor["ID_5"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_6":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Nature("【模型-群聊性质】");  CoreConfig.Node_Model_Rumor["ID_6"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_7":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_DeepSeek("【模型-Ai工具】");  CoreConfig.Node_Model_Rumor["ID_7"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_8":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Inspection("【模型-社群巡查】");  CoreConfig.Node_Model_Rumor["ID_8"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_9":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_ASR("【模型-ASR分析】");  CoreConfig.Node_Model_Rumor["ID_9"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Model_10":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model_Component_Rumor_Output("【输出-突出谣言】");  CoreConfig.Node_Model_Rumor["ID_10"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Model_Rumor);
        break;
      case "Preview_1":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Preview_Monitor("【数据实时监控】");  CoreConfig.Node_System_Preview["ID_1"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Preview);
        break;
      case "Preview_2":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Preview_Efficient("【预览-有效舆情】");  CoreConfig.Node_System_Preview["ID_2"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Preview);
        break;
      case "Preview_3":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Preview_Dashboard("【数据看板】");  CoreConfig.Node_System_Preview["ID_3"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Preview);
        break;
      case "Preview_4":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Preview_Image("【预览-图像预览】");  CoreConfig.Node_System_Preview["ID_4"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Preview);
        break;
      case "Parameter_1":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Length("【上下文长度】");  CoreConfig.Node_Parameter_Analysis["ID_1"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_2":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Tfs_Z("【Tfs_Z】");  CoreConfig.Node_Parameter_Analysis["ID_2"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_3":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Punish("【频率惩罚】");  CoreConfig.Node_Parameter_Analysis["ID_3"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_4":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_P_value("【最小P值】");  CoreConfig.Node_Parameter_Analysis["ID_4"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_5":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Mirostat("【Mirostat】");  CoreConfig.Node_Parameter_Analysis["ID_5"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_6":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Mirostat_Eta("【Mirostat_Eta】");  CoreConfig.Node_Parameter_Analysis["ID_6"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_7":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Mirostat_Tau("【Mirostat_Tau】");  CoreConfig.Node_Parameter_Analysis["ID_7"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_8":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Temp("【温度】");  CoreConfig.Node_Parameter_Analysis["ID_8"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_9":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Seed("【种子值】");  CoreConfig.Node_Parameter_Analysis["ID_9"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_10":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Repeat("【重复最后N个】");  CoreConfig.Node_Parameter_Analysis["ID_10"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_11":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Top_K("【Top_K】");  CoreConfig.Node_Parameter_Analysis["ID_11"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_12":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Top_P("【Top_P】");  CoreConfig.Node_Parameter_Analysis["ID_12"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_13":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Reasoning("【推理程度】");  CoreConfig.Node_Parameter_Analysis["ID_13"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "Parameter_14":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Parameter_Component_Analysis_Sequence("【停止序列】");  CoreConfig.Node_Parameter_Analysis["ID_14"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_Parameter_Analysis);
        break;
      case "AI":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_AI_Component_DeepSeek("【AI-DeepSeek】");  CoreConfig.Node_AI_DeepSeek["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_AI_DeepSeek);
        break;
      case "ChatGLM":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_Model("【AI-ChatGLM】");  CoreConfig.Node_AI_ChatGLM["ID"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_AI_ChatGLM);
        break;
      case "Algorithm_1":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Algorithm_Load("【简易加载器】");  CoreConfig.Node_System_Algorithm["ID_1"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Algorithm);
        break;
      case "Algorithm_2":
        // @ts-ignore - 忽略下一行的类型检查
        NodeNew = new Node_System_Component_Algorithm_Sampling("【简易K采样器】");  CoreConfig.Node_System_Algorithm["ID_2"]     = String(NodeNew.id); RightMenu_Node_Add(CoreConfig.Node_System_Algorithm);
        break;

    //      // 设置回调并接收参数
    //      (NodeNew.controls.More as ButtonControl_More).setButtonAction((nodeId, action) => {
    //       console.log("节点 ID:", nodeId);
    //       console.log("动作类型:", action);
    //       const buttonElement = document.getElementById('Button_Left_Node');
    //         if (buttonElement) {
    //           buttonElement.click();
    //         }
    //     });
    //     break;
    
      default:
        break;
    }
    try {
      
        // @ts-ignore - 忽略下一行的类型检查
        await editor.addNode(NodeNew);
      
  //     // area.translate(0, 0); 
  //     // area.zoom(0.8, area.area.mouse);
    } catch (error) {
    }
    setTimeout((RightMenu_ReSet_Arrange), 1000);
  }
 }

function  RightMenu_ReSet_Arrange(){

  console.log("WorkFolw_Schems",WorkFolw_Schems)
  WorkFolw_Schems["Location"]={
    "Start":{x:-120,y:0},
    "Object":{x:320,y:0},
    "OnceSource":{x:320,y:300},
    "OncePreview":{x:620,y:300},
    "OnceOutput":{x:620,y:300},
    "Statistic":{x:620,y:300}, 
    "Model":{x:620,y:300}, 
    // "OncePreview":{x:320,y:0},
  }
  let Location = WorkFolw_Schems["Location"]

  let nodes = editor.getNodes();

  let Start_ID = WorkFolw_Schems["Start"]["ID"]

  area.translate(Start_ID,{ x: Location["Start"]["x"], y:Location["Start"]["y"]})

  let Object_ID = WorkFolw_Schems["Object"]["ID"]
  area.translate(Object_ID,{ x: Location["Object"]["x"]-100, y:Location["Object"]["y"]})

  let Model_1_Id = WorkFolw_Schems["Model"][0]["ID_1"]
  area.translate(Model_1_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+1280})
  let Model_2_Id = WorkFolw_Schems["Model"][1]["ID_2"]
  area.translate(Model_2_Id,{ x: Location["Object"]["x"]+2000, y:Location["Object"]["y"]})
  let Model_3_Id = WorkFolw_Schems["Model"][1]["ID_3"]
  area.translate(Model_3_Id,{ x: Location["Object"]["x"]+2000, y:Location["Object"]["y"]+460})
  let Model_4_Id = WorkFolw_Schems["Model"][1]["ID_4"]
  area.translate(Model_4_Id,{ x: Location["Object"]["x"]+2000, y:Location["Object"]["y"]+920})
  let Model_5_Id = WorkFolw_Schems["Model"][1]["ID_5"]
  area.translate(Model_5_Id,{ x: Location["Object"]["x"]+1500, y:Location["Object"]["y"]})
  let Model_6_Id = WorkFolw_Schems["Model"][1]["ID_6"]
  area.translate(Model_6_Id,{ x: Location["Object"]["x"]+1500, y:Location["Object"]["y"]+460})
  let Model_7_Id = WorkFolw_Schems["Model"][1]["ID_7"]
  area.translate(Model_7_Id,{ x: Location["Object"]["x"]+1500, y:Location["Object"]["y"]+920})
  let Model_8_Id = WorkFolw_Schems["Model"][1]["ID_8"]
  area.translate(Model_8_Id,{ x: Location["Object"]["x"]+1000, y:Location["Object"]["y"]})
  let Model_9_Id = WorkFolw_Schems["Model"][1]["ID_9"]
  area.translate(Model_9_Id,{ x: Location["Object"]["x"]+1000, y:Location["Object"]["y"]+460})
  let Model_10_Id = WorkFolw_Schems["Model"][1]["ID_10"]
  area.translate(Model_10_Id,{ x: Location["Object"]["x"]+1000, y:Location["Object"]["y"]+920})

  let Preview_1_Id = WorkFolw_Schems["Preview"]["ID_1"]
  area.translate(Preview_1_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]})
  let Preview_2_Id = WorkFolw_Schems["Preview"]["ID_2"]
  area.translate(Preview_2_Id,{ x: Location["Object"]["x"]+4300, y:Location["Object"]["y"]})
  let Preview_3_Id = WorkFolw_Schems["Preview"]["ID_3"]
  area.translate(Preview_3_Id,{ x: Location["Object"]["x"]+1500, y:Location["Object"]["y"]-370})
  let Preview_4_Id = WorkFolw_Schems["Preview"]["ID_4"]
  area.translate(Preview_4_Id,{ x: Location["Object"]["x"]+5000, y:Location["Object"]["y"]+200})


  let AI_Id = WorkFolw_Schems["AI"][0]["ID"]
  area.translate(AI_Id,{ x: Location["Object"]["x"]+2630, y:Location["Object"]["y"]})
  let ChatGLM_Id = WorkFolw_Schems["AI"][1]["ID"]
  area.translate(ChatGLM_Id,{ x: Location["Object"]["x"]+2430, y:Location["Object"]["y"]})

  let Algorithm_1_Id = WorkFolw_Schems["Algorithm"][0]["ID_1"]
  area.translate(Algorithm_1_Id,{ x: Location["Object"]["x"]+400, y:Location["Object"]["y"]})
  let Algorithm_2_Id = WorkFolw_Schems["Algorithm"][1]["ID_2"]
  area.translate(Algorithm_2_Id,{ x: Location["Object"]["x"]+2630, y:Location["Object"]["y"]+460})


  let Parameter_1_Id = WorkFolw_Schems["Parameter"][0]["ID_1"]
  area.translate(Parameter_1_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+300})
  let Parameter_2_Id = WorkFolw_Schems["Parameter"][0]["ID_2"]
  area.translate(Parameter_2_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+440})
  let Parameter_3_Id = WorkFolw_Schems["Parameter"][0]["ID_3"]
  area.translate(Parameter_3_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+580})
  let Parameter_4_Id = WorkFolw_Schems["Parameter"][0]["ID_4"]
  area.translate(Parameter_4_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+720})
  let Parameter_5_Id = WorkFolw_Schems["Parameter"][0]["ID_5"]
  area.translate(Parameter_5_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+860})
  let Parameter_6_Id = WorkFolw_Schems["Parameter"][0]["ID_6"]
  area.translate(Parameter_6_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+1000})
  let Parameter_7_Id = WorkFolw_Schems["Parameter"][0]["ID_7"]
  area.translate(Parameter_7_Id,{ x: Location["Object"]["x"]+3200, y:Location["Object"]["y"]+1140})
  let Parameter_8_Id = WorkFolw_Schems["Parameter"][0]["ID_8"]
  area.translate(Parameter_8_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+300})
  let Parameter_9_Id = WorkFolw_Schems["Parameter"][0]["ID_9"]
  area.translate(Parameter_9_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+440})
  let Parameter_10_Id = WorkFolw_Schems["Parameter"][0]["ID_10"]
  area.translate(Parameter_10_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+580})
  let Parameter_11_Id = WorkFolw_Schems["Parameter"][0]["ID_11"]
  area.translate(Parameter_11_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+720})
  let Parameter_12_Id = WorkFolw_Schems["Parameter"][0]["ID_12"]
  area.translate(Parameter_12_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+860})
  let Parameter_13_Id = WorkFolw_Schems["Parameter"][0]["ID_13"]
  area.translate(Parameter_13_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+1000})
  let Parameter_14_Id = WorkFolw_Schems["Parameter"][0]["ID_14"]
  area.translate(Parameter_14_Id,{ x: Location["Object"]["x"]+3600, y:Location["Object"]["y"]+1140})


  let Node_Start        = nodes.find((node) => node.id === Start_ID);
  let Node_Object       = nodes.find((node) => node.id === Object_ID);
  let Node_Model_1      = nodes.find((node) => node.id === Model_1_Id);
  let Node_Model_2      = nodes.find((node) => node.id === Model_2_Id);
  let Node_Model_3      = nodes.find((node) => node.id === Model_3_Id);
  let Node_Model_4      = nodes.find((node) => node.id === Model_4_Id);
  let Node_Model_5      = nodes.find((node) => node.id === Model_5_Id);
  let Node_Model_6      = nodes.find((node) => node.id === Model_6_Id);
  let Node_Model_7      = nodes.find((node) => node.id === Model_7_Id);
  let Node_Model_8      = nodes.find((node) => node.id === Model_8_Id);
  let Node_Model_9      = nodes.find((node) => node.id === Model_9_Id);
  let Node_Model_10     = nodes.find((node) => node.id === Model_10_Id);
  let Node_Preview_1    = nodes.find((node) => node.id === Preview_1_Id);
  let Node_Preview_2    = nodes.find((node) => node.id === Preview_2_Id);
  let Node_Preview_4    = nodes.find((node) => node.id === Preview_4_Id);
  let Node_Parameter_1  = nodes.find((node) => node.id === Parameter_1_Id);
  let Node_Parameter_2  = nodes.find((node) => node.id === Parameter_2_Id);
  let Node_Parameter_3  = nodes.find((node) => node.id === Parameter_3_Id);
  let Node_Parameter_4  = nodes.find((node) => node.id === Parameter_4_Id);
  let Node_Parameter_5  = nodes.find((node) => node.id === Parameter_5_Id);
  let Node_Parameter_6  = nodes.find((node) => node.id === Parameter_6_Id);
  let Node_Parameter_7  = nodes.find((node) => node.id === Parameter_7_Id);
  let Node_Parameter_8  = nodes.find((node) => node.id === Parameter_8_Id);
  let Node_Parameter_9  = nodes.find((node) => node.id === Parameter_9_Id);
  let Node_Parameter_10 = nodes.find((node) => node.id === Parameter_10_Id);
  let Node_Parameter_11 = nodes.find((node) => node.id === Parameter_11_Id);
  let Node_Parameter_12 = nodes.find((node) => node.id === Parameter_12_Id);
  let Node_Parameter_13 = nodes.find((node) => node.id === Parameter_13_Id);
  let Node_Parameter_14 = nodes.find((node) => node.id === Parameter_14_Id);
  let Node_AI           = nodes.find((node) => node.id === AI_Id);
  let Node_ChatGLM      = nodes.find((node) => node.id === ChatGLM_Id);
  let Node_Algorithm_1  = nodes.find((node) => node.id === Algorithm_1_Id);
  let Node_Algorithm_2  = nodes.find((node) => node.id === Algorithm_2_Id);


  // let Node_OnceModel   = nodes.find((node) => node.id === OnceModel_Id);
  // let Node_AI          = nodes.find((node) => node.id === AI_Id);
  // let Node_OncePreview = nodes.find((node) => node.id === OncePreview_Id);
  // let Node_OnceOutput  = nodes.find((node) => node.id === OnceOutput_Id);


   // @ts-ignore - 忽略下一行的类型检查
  const connection_1 = new Connection(Node_Start, "Start", Node_Object, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_2 = new Connection(Node_Object, "Output", Node_Algorithm_1, "optional_lora_stack");
   // @ts-ignore - 忽略下一行的类型检查
  const connection_3 = new Connection(Node_Model_2, "Output", Node_Algorithm_2, "model");
   // @ts-ignore - 忽略下一行的类型检查
  const connection_4 = new Connection(Node_Model_3, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_5 = new Connection(Node_Model_4, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_6 = new Connection(Node_Model_5, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_7 = new Connection(Node_Model_6, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_8 = new Connection(Node_Model_7, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_9 = new Connection(Node_Model_8, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_10 = new Connection(Node_Model_9, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_11 = new Connection(Node_Model_10, "Output", Node_Algorithm_2, "model");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_12 = new Connection(Node_Model_1, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_13 = new Connection(Node_Preview_1, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_14 = new Connection(Node_Parameter_1, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_15 = new Connection(Node_Parameter_2, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_16 = new Connection(Node_Parameter_3, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_17 = new Connection(Node_Parameter_4, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_18 = new Connection(Node_Parameter_5, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_19 = new Connection(Node_Parameter_6, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_20 = new Connection(Node_Parameter_7, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_21 = new Connection(Node_Parameter_8, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_22 = new Connection(Node_Parameter_9, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_23 = new Connection(Node_Parameter_10, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_24 = new Connection(Node_Parameter_11, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_25 = new Connection(Node_Parameter_12, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_26 = new Connection(Node_Parameter_13, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_27 = new Connection(Node_Parameter_14, "Output", Node_Preview_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_28 = new Connection(Node_AI, "Output", Node_Preview_1, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_29 = new Connection(Node_ChatGLM, "date", Node_AI, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_30 = new Connection(Node_Algorithm_2, "image", Node_Model_1, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_31 = new Connection(Node_Algorithm_1, "model", Node_Model_2, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_32 = new Connection(Node_Algorithm_1, "model", Node_Model_3, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_33 = new Connection(Node_Algorithm_1, "model", Node_Model_4, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_34 = new Connection(Node_Algorithm_1, "model", Node_Model_5, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_35 = new Connection(Node_Algorithm_1, "model", Node_Model_6, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_36 = new Connection(Node_Algorithm_1, "model", Node_Model_7, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_37 = new Connection(Node_Algorithm_1, "model", Node_Model_8, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_38 = new Connection(Node_Algorithm_1, "model", Node_Model_9, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_39 = new Connection(Node_Algorithm_1, "model", Node_Model_10, "Input");
  // @ts-ignore - 忽略下一行的类型检查
  const connection_40 = new Connection(Node_Preview_2, "Output", Node_Preview_4, "Input");

   // @ts-ignore - 忽略下一行的类型检查
  //  const con3 = new Connection(Node_OnceModel, "Output", Node_OnceSource, "Input");

  //  // @ts-ignore - 忽略下一行的类型检查
  //  const con4 = new Connection(Node_OnceSource, "Output", Node_OncePreview, "Input");




  //     // @ts-ignore - 忽略下一行的类型检查
  //  const con5 = new Connection(Node_AI, "Output", Node_OncePreview, "Input");


  //  // @ts-ignore - 忽略下一行的类型检查
  //  const con6 = new Connection(Node_OncePreview, "Output", Node_OnceOutput, "Input");




  editor.addConnection(connection_1);
  editor.addConnection(connection_2);
  editor.addConnection(connection_3);
  editor.addConnection(connection_4);
  editor.addConnection(connection_5);
  editor.addConnection(connection_6);
  editor.addConnection(connection_7);
  editor.addConnection(connection_8);
  editor.addConnection(connection_9);
  editor.addConnection(connection_10);
  editor.addConnection(connection_11);
  editor.addConnection(connection_12);
  editor.addConnection(connection_13);
  editor.addConnection(connection_14);
  editor.addConnection(connection_15);
  editor.addConnection(connection_16);
  editor.addConnection(connection_17);
  editor.addConnection(connection_18);
  editor.addConnection(connection_19);
  editor.addConnection(connection_20);
  editor.addConnection(connection_21);
  editor.addConnection(connection_22);
  editor.addConnection(connection_23);
  editor.addConnection(connection_24);
  editor.addConnection(connection_25);
  editor.addConnection(connection_26);
  editor.addConnection(connection_27);
  editor.addConnection(connection_28);
  editor.addConnection(connection_29);
  editor.addConnection(connection_30);
  editor.addConnection(connection_31);
  editor.addConnection(connection_32);
  editor.addConnection(connection_33);
  editor.addConnection(connection_34);
  editor.addConnection(connection_35);
  editor.addConnection(connection_36);
  editor.addConnection(connection_37);
  editor.addConnection(connection_38);
  editor.addConnection(connection_39);
  editor.addConnection(connection_40);


  area.area.zoom(0.3, 0, 0)

  //  arrange.layout();

 }




 

 function  RightMenu_Schemes_Info(){
  // console.log("toggleFeature",editor.getNodes())
  // console.log("Node_Type_List",Node_Type_List)
  // console.log("CoreNodeTree",CoreNodeTree)
 }



 function  Schemes_Start(){


  const intervalId = setInterval(() => {













    // 每秒执行的任务
    // setCounter(prev => prev + 1); // 示例：每秒计数器+1
    // console.log('定时任务执行', new Date().toLocaleTimeString());
    let Object_ID = WorkFolw_Schems["Object"]["ID"]
    // console.log("Object_ID",Object_ID)
  //  let nodes = editor.getNodes();
    // let Node_Object = nodes.find((node) => node.id === Object_ID);
    let nodes = editor.getNodes();


    let AI_Id = WorkFolw_Schems["AI"][0]["ID"]
    let Node_AI          = nodes.find((node) => node.id === AI_Id);

    console.log("Node_AI",Node_AI)
    










    let Node_Object = nodes.find((node) => node.id === Object_ID);
    if (Node_Object){
      try {
                   
  
                  if (Node_Object){
                    
                    if (Count<=100){
                      //     Count+=10;
                      // }else{
                      //   Count=0;
                      // }
                      
                      Count+=20
                  
                    // } catch (error) {
                          
                    // }

  
                    // // @ts-ignore - 忽略下一行的类型检查
                    // let Progress = Node_Object.controls.Progress as ProgressControl_Object;
                    // // @ts-ignore - 忽略下一行的类型检查
                    // if (Progress) {
                    //   // @ts-ignore - 忽略下一行的类型检查
                    //   Progress.setPercent(Count); // 需要自定义更新方法
                      
                    //   // 4. 触发控件更新（核心）
                    //   area.update('control', Progress.id);
                    // }
                    // if (Count<=100){
                    //     Count+=10;
                    // }else{
                    //   Count=0;
                    // }
                    // console.log("Progress",Progress);
                //   // 3. 更新进度值（假设 ProgressControl 有 update 方法）
  
                    }else{

                      Count=0
                      // 当 Count 超过 100 时，清理定时器
                      clearInterval(intervalId);
                      console.log("定时器已停止，Count 超过 100");
                    
                    }


                    let Object_Info={
                      "Title":"",
                      "Percent":Count,
                    }

                  // try {
                    // @ts-ignore - 忽略下一行的类型检查
                    // Cnode?.updateContentValue('新的内容值11111');
                    // // @ts-ignore - 忽略下一行的类型检查
                    Node_Object?.updateContent(Object_Info);
                
                    // console.log('Label',Node_OnceSource?.label);
                    area.update("node", Object_ID);


  
                  };
          } catch (error) {
            
          }
  
    }
  
  }, 2000);






  console.log("CoreNodeTree",CoreNodeTree)
  let Object_ID = WorkFolw_Schems["Object"]["ID"]
  console.log("Object_ID",Object_ID)





  let nodes = editor.getNodes();
  let Node_Object = nodes.find((node) => node.id === Object_ID);

  // if (Node_Object){
    // const progressControl = Node_Object.controls['Content'] as ProgressControl_Object | undefined;
  // console.log("Node_Object",Node_Object);


  // Node_Object.label = "New Label";

  // area.update('node', Object_ID);


  // // @ts-ignore - 忽略下一行的类型检查
  // let Progress = Node_Object.controls.Progress as ProgressControl_Object;
  //  // @ts-ignore - 忽略下一行的类型检查
  // if (Progress) {
  //   // @ts-ignore - 忽略下一行的类型检查
  //   Progress.setPercent(30); // 需要自定义更新方法
    
  //   // 4. 触发控件更新（核心）
  //   area.update('control', Progress.id);
  // }
  // console.log("Progress",Progress);
  //   // 3. 更新进度值（假设 ProgressControl 有 update 方法）

    

  // };




  // progressControl.percent = value;
  // console.log("OK");









 
  console.log("Object",CoreNodeTree["Nodes"][Object_ID])


  console.log("WorkFolw_Schems",WorkFolw_Schems)
  let Connections = editor.getConnections();
  // console.log('执行启动功能获取到的当前连接：', Connections)
  WorkFolw_Schems['Connections'] = Connections


  let CoreNodeTree_Str = JSON.stringify(CoreNodeTree, null, 2);
  let WorkFolw_Schems_Str = JSON.stringify(WorkFolw_Schems, null, 2);
  const __Service_Requests = new Service_Requests()
  let RequestsData={
    'user_id': '',
    'user_token': '',
    'data_class': 'Analysis',
    'data_type': 'Service_WorFlow',
    'data_methods': 'start_scheme_mission',
    'data_argument': "{}",
    'data_kwargs': WorkFolw_Schems_Str,
  };
  
  // let Response_Data = __CoreControl.Update_Tree(RequestsData);
  (async () => {
    let Response_Data =  await __Service_Requests.Async(RequestsData);
    console.log("Response Data:", Response_Data);
    // 拿到结果 打开一个抽屉展示
    if (Response_Data.Status == "Success") {
      eventBus.emit('open-modal', { type: 'update_node_think', think_info: Response_Data.Content_Info });
    } else {
      console.log("思考出错 不打开弹窗")
    }
    let Score_Info_Status = document.getElementById('Score_Info_Status');
    if (Score_Info_Status) {
      Score_Info_Status.textContent=JSON.stringify(Response_Data);
    }

    let OnceSource_Id = WorkFolw_Schems["OnceSource"]["ID"]
    console.log('OnceSource_Id',OnceSource_Id);
   

    let Node_OnceSource = nodes.find((node) => node.id === OnceSource_Id);
  
    console.log('clicked:Node_OnceSource',Node_OnceSource);
    try {
      // @ts-ignore - 忽略下一行的类型检查
      // Cnode?.updateContentValue('新的内容值11111');
      // // @ts-ignore - 忽略下一行的类型检查
      Node_OnceSource?.updateContent(Response_Data["Intelligence"]);
  
      console.log('Label',Node_OnceSource?.label);
      area.update("node", OnceSource_Id);
  
    } catch (error) {
          
    }




    // -----结果展示
    let OncePreview_Id = WorkFolw_Schems["OncePreview"]["ID"]
    let Node_OncePreview = nodes.find((node) => node.id === OncePreview_Id);
    console.log('clicked:Node_OncePreview',Node_OncePreview);
    try {
 
      Response_Data["Intelligence"]["Analysis_Status"]["Title"]   = Response_Data["Intelligence"]["INTELLIGENCE_TITLE_CN"];
      Response_Data["Intelligence"]["Analysis_Status"]["Content"] = Response_Data["Intelligence"]["INTELLIGENCE_CONTENT_CN"];
        // @ts-ignore - 忽略下一行的类型检查
      Node_OncePreview?.updateContent(Response_Data["Intelligence"]["Analysis_Status"]);
  
      console.log('Label',Node_OncePreview?.label);
      area.update("node", OncePreview_Id);
  
    } catch (error) {
          
    }

  

    // if (scoreInfoRef.current) {
    //   scoreInfoRef.current.textContent = 'Hello, world!';
    // }

  })();


  

  // console.log("Response_Data",Response_Data)


 };




//  function findNodeById() {
//   // 获取所有节点
//   const nodes = editor.getNodes();
//   // 查找匹配的节点



//   // return nodes.find(node => node.id === nodeId);
// }



//  通过节点ID 获取节点的value值
function findNodeById(nodeId: string) {
  const nodes = editor.getNodes();
  const Cnode = nodes.find((node) => node.id === nodeId);
  console.log('clicked:updateContentValue',Cnode);
  try {
    // @ts-ignore - 忽略下一行的类型检查
    // Cnode?.updateContentValue('新的内容值11111');
    // // @ts-ignore - 忽略下一行的类型检查
    // Cnode?.updateLabel('新的内容值11111');
    console.log('Label',Cnode?.label);
  } catch (error) {
    console.log('获取节点label出错');
  };
};







// 监听节点点击事件
area.addPipe((context) => {

  
  // console.log('[editor.ts]监听节点点击事件:',context);
  if (context.type === 'nodepicked') {
    const node = context.data;
    let Node_Info = CoreNodeTree["Nodes"][node.id];
    try {
      findNodeById(node.id)
      // @ts-ignore - 忽略下一行的类型检查
      node.updateContentValue('新的内容值');
    } catch (error) {
      
    };
      
    // let CoreNodeTree_Str = JSON.stringify(CoreNodeTree, null, 2);
    // let CoreC = new CoreControl()
    // let RequestsData={
    //   'user_id': '',
    //   'user_token': '',
    //   'data_class': 'Config',
    //   'data_type': 'Service_Check',
    //   'data_methods': 'return_sentinel_config_custom',
    //   'data_argument': "{}",
    //   'data_kwargs': CoreNodeTree_Str,
    // }
    
    // CoreC.Update_Tree(RequestsData);


    try {
      console.log('Node clicked:', node.id,Node_Info);
      console.log('Node clicked:', node);
      // node.
      let Node_Info_ID = document.getElementById('Node_Info_ID');
      if (Node_Info_ID) {
        Node_Info_ID.textContent=`ID: ${node.id}`;
      }
  
      let Node_Info_Schemes = document.getElementById('Node_Info_Schemes');
      if (Node_Info_Schemes) {
        Node_Info_Schemes.textContent=`课题: ${Node_Info.Schemes}`;
      }
  
  
  
      let Node_Info_Class = document.getElementById('Node_Info_Class');
      if (Node_Info_Class) {
        Node_Info_Class.textContent=`模块: ${Node_Info.Class}`;
      }
  
  
      let Node_Info_Type = document.getElementById('Node_Info_Type');
      if (Node_Info_Type) {
  
  
        if (Node_Info.Class ==="数源"){
          Node_Info_Type.textContent=`方向: ${Node_Info.Type}`;
        }else{
          Node_Info_Type.textContent=`类型: ${Node_Info.Type}`;
        }
          
        
      }
  
      let Node_Info_Method = document.getElementById('Node_Info_Method');
      if (Node_Info_Method) {
        
      if (Node_Info.Class ==="数源"){
        Node_Info_Method.textContent=`类型: ${Node_Info.Method}`;
      }else{
        Node_Info_Method.textContent=`方法: ${Node_Info.Method}`;
      }
        
      }
      let Node_Info_Explain = document.getElementById('Node_Info_Explain');
      if (Node_Info_Explain) {
        Node_Info_Explain.textContent=`说明: ${Node_Info.Explain}`;
      }
      let Node_Info_Status = document.getElementById('Node_Info_Status');
      if (Node_Info_Status) {
        Node_Info_Status.textContent=` ${Node_Info.Status}`;
      }
  
      
    } catch (error) {
      
    };

   




  }



  

  // }else{
  //   if (context.type === 'connectioncreate'){
  //     const Connection = context.data;
  //     console.log('Connection clicked:', context.data);
  //   }
  // }


  // if (["connectioncreated", "connectionremoved"].includes(context.type)) {
  //   process();
  // }

  //   // } if (canCreateConnection(context.data)) return false

  // }



  return context;
});



// ---------------------------------------------------------------------------------------- 添加按钮事件监听
const Button_Node_Update = document.getElementById('Button_Node_Update');
if (Button_Node_Update) {
  Button_Node_Update.addEventListener('click', async () => {
    
    let Object_ID = WorkFolw_Schems["Object"]["ID"]
    console.log("Object_ID",Object_ID)
    let Input_Node_Label  = document.getElementById('Input_Node_Label') as HTMLInputElement;
  
    // let InText = ;
    // console.log("Click",InText)
  
    let nodes = editor.getNodes();
    let Node_Object = nodes.find((node) => node.id === Object_ID);
  
    if (Node_Object){
        //   const progressControl = Node_Object.controls['progress'] as ProgressControl_Object | undefined;
        console.log("Node_Object",Node_Object);
      
      
        Node_Object.label = `课题【${Input_Node_Label?.value}】`;
      
        area.update('node', Object_ID);
      
  
     }

  });
}

var Count =1



// WorkFolw_Schems={
//   "Start":{},
//   "Object":{},
//   "DataSource":[],
//   "Analysis":[],
//   "Model":[],
//   "AI":[],
//   "Template":[],
//   "End":[],
//   "OnceSource":{},
//   "OncePreview":{},

// }


// ["AI-DeepSeek", () =>  {let NodeNew = new Node_Model("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek);return NodeNew}],
// ["实时突谣评分",  () =>  {let NodeNew = new Node_DataSource_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);return NodeNew}],

function Initial_Schemes() {
// const Node_List =["Object","OncePreview"]
let NodeNew = null
for (let Node_Name of ["Object","DataSource","AI","Preview"]) {
  console.log("Node",Node_Name);
  switch (Node_Name) {
    case "Object":
       // @ts-ignore - 忽略下一行的类型检查
       NodeNew= new Node_System_Component_Object("【课题-未知】"); CoreConfig.Node_System_Object["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_System_Object)

      break;
    case "DataSource":
       // @ts-ignore - 忽略下一行的类型检查
       NodeNew = new Node_DataSource_Component_Rumor("【数源-实时突谣评分】");  CoreConfig.Node_DataSource_Rumor["ID"]     = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_DataSource_Rumor);

      break;
    case "AI":
       // @ts-ignore - 忽略下一行的类型检查
       NodeNew = new Node_Model("【AI-DeepSeek】"); CoreConfig.Node_AI_DeepSeek["ID"]    = String(NodeNew.id); RightMenu_Nade_Add(CoreConfig.Node_AI_DeepSeek)

      break;
    case "Preview":
       // @ts-ignore - 忽略下一行的类型检查
       NodeNew = new Node_Preview_Score("【预览-突出谣言】");CoreConfig[`Node_System_Preview`]["ID"]    = String(NodeNew.id);RightMenu_Nade_Add(CoreConfig.Node_System_Preview);
     


      break;
  
    default:
      break;
  }



  
  try {
    
    // @ts-ignore - 忽略下一行的类型检查
    editor.addNode(NodeNew);
    
    // area.translate(0, 0); 
    // area.zoom(0.8, area.area.mouse);
  } catch (error) {
    
  }
  setTimeout((RightMenu_ReSet_Arrange), 1000);
  
}



  
}




// Initial_Schemes()

// setTimeout(Initial_Schemes, 1000);
// function arrangeNodes(nodes: Node[], spacing = 100) {
//   let yPos = 0;
//   nodes.forEach(node => {
//     node.position.set(0, yPos);
//     yPos += node.height + spacing;
//   });
// }

// // 使用示例
// const allNodes = editor.getNodes();
// arrangeNodes(allNodes);


  // 2. 定义服务方法并绑定必要上下文
  // const Function_Service = (config: string) => {
  //   console.log("执行服务函数",config);

  //   console.log("服务配置:", { config });
  //   // 可以访问编辑器内部状态
  //   console.log("当前节点数量:", editor.getNodes().length);
  // };

  const FunctionService = (maxi:Type_Json) => {
    console.log("执行服务函数",maxi);
    // 可以访问编辑器内部状态
    console.log("当前节点数量:", editor.getNodes().length);
    Initial_Schemes();
  };


  await arrange.layout();
  AreaExtensions.zoomAt(area, editor.getNodes());

  return {
    // destroy: () => area.destroy(),
    destroy: () => {
      area.destroy();
      editor.clear();
    },
    FunctionService
  };
}
