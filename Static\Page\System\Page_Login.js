
// 监听登录按钮
$('#CSC_Login, #CSC_Login_Icon').on('click', (event) => {
    // console.log('登录按钮点击触发')
    doLogin();
});

// 监听是否有回车事件
$('#username, #password').on('keydown', function(event) {
    if (event.which === 13) { // 检查键码是否为13（Enter键）
        event.preventDefault(); // 阻止默认行为
        doLogin();
    };
});

function doLogin() {
    var UserName = $('#username').val();
    var PassWord = $('#password').val();
    // 执行登录请求
    // console.log('执行登录请求，用户名：', username, '密码：', password);
    if (UserName.length !== 0 && PassWord.length !== 0) {
        let Requests_Data = {
            "user_id": "CSC",
            "user_token":"login" ,
            "data_class": "Sentinel",
            "data_type": 'System',
            "data_methods": "user_login_authentication_verify",
            "data_argument": `{}`,
            "data_kwargs":{"User_Name":UserName,"User_Password":PassWord},
        };
        /****************************************同步请求***************************************************/
        // __Service_Requests = new Service_Requests("Sync",Requests_Data);
        // Result  = __Service_Requests.callMethod();
        // console.log('Result:',Result);
        /****************************************异步请求***************************************************/
        __Service_Requests = new Service_Requests("Async",Requests_Data);
        __Service_Requests.callMethod()
            .then((Result) => {
                // console.log('callMethod Result:',Result);
                if (Result['Status'] === 'Success') {
                    localStorage.setItem('User_Token', Result['User_Token'])
                    // var User_Token = localStorage.getItem('User_Token')
                    // console.log('User_Token:',User_Token)
                    window.location.href="Service_Page?Page=Page_Home"
                } else {
                    Lobibox.notify('warning', {
                        pauseDelayOnHover: true,
                        size: 'mini',
                        rounded: true,
                        delayIndicator: false,
                        continueDelayOnInactiveTab: false,
                        position: 'top right',
                        msg: '账号密码错误或者授权未通过！'
                    });
                };
            }).catch((err) => {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            });
    } else {
        // console.log('账号密码为空 不执行');
        Lobibox.notify('warning', {
            pauseDelayOnHover: true,
            size: 'mini',
            rounded: true,
            delayIndicator: false,
            continueDelayOnInactiveTab: false,
            position: 'top right',
            msg: '账号信息为空，请补全！'
        });
    };
};