import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ButtonControl,Node_Socket,DataSourceControl_Statistic} from "./Node_Controls";
















  export class Node_DataSource_Component_Statistic extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    button_1:ButtonControl,
    Conent:DataSourceControl_Statistic,
    

  }> 
  {
    width  = 480;
    height = 298;
    
      constructor(Label: string,) {
        super(Label);

      const textAreaControl = new DataSourceControl_Statistic(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }
// 自定义按钮位置
  // 自定义按钮位置

      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    