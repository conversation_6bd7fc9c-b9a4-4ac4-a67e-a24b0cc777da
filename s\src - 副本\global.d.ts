// global.d.ts
declare module '*.module.less' {
    const classes: { [key: string]: string }
    export default classes
}

declare module '*.less' {
    const content: Record<string, string>
    export default content
}

declare global {
    interface Window {
      BMapGL: {
        Map: new (container: HTMLElement, options?: BMapOptions) => BMapInstance;
        Point: new (lng: number, lat: number) => PointInstance;
      };
      initBMapCallback?: () => void;
    }
  
    interface BMapOptions {
      enableMapClick?: boolean;
      disableDoubleClickZoom?: boolean;
      enableDragging?: boolean;
    }
  
    interface BMapInstance {
      centerAndZoom(point: PointInstance, zoom: number): void;
      enableScrollWheelZoom(): void;
      destroy(): void;
      addEventListener(type: string, handler: (event: any) => void): void;
      removeEventListener(type: string, handler: (event: any) => void): void;
    }
  
    interface PointInstance {
      lng: number;
      lat: number;
    }
}