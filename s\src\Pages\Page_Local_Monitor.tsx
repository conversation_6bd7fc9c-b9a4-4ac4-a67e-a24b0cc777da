import React, { useState, useEffect } from 'react';
import { Tabs, Input, List, Table, Button, Typography, Spin } from 'antd';
import { ReloadOutlined, MenuOutlined, MessageOutlined } from '@ant-design/icons';
import { useServiceRequests } from '@/Core/Core_Control';

const { TabPane } = Tabs;
const { Title } = Typography;

interface GroupInfo {
  id: string;
  name: string;
  memberCount?: number;
  lastTime?: string;
  lastMessage?: string;
}

interface MessageInfo {
  ID: string;
  SOURCE_WEB_DATE: string;
  SOURCE_AUTHOR: string;
  SOURCE_TITLE: string;
}

const Page_Local_Monitor: React.FC = () => {
  const { AsyncTokenRequests } = useServiceRequests();
  const [activeTab, setActiveTab] = useState<'wechat' | 'qq'>('wechat');
  const [groupList, setGroupList] = useState<GroupInfo[]>([]);
  const [messageList, setMessageList] = useState<MessageInfo[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<GroupInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 加载群组列表
  useEffect(() => {
    const fetchGroups = async () => {
      setLoading(true);
      setSelectedGroup(null);
      setMessageList([]);
      try {
        const data_methods = activeTab === 'wechat' ? 'return_wechat_info' : 'return_qq_info';
        const requestData = {
          user_id: '',
          user_token: '',
          data_class: 'Sentiment',
          data_type: 'Service',
          data_methods,
          data_argument: {},
          data_kwargs: {},
        };
        const result = await AsyncTokenRequests(requestData);
        let groupListRaw: any[] = [];
        if (result.Status === 'Success') {
          groupListRaw = activeTab === 'wechat' ? result.Wechat_Group_List : result.QQ_Group_List;
        }
        setGroupList((groupListRaw || []).map((item: any) => ({
          id: item.GROUP_NUMBER || item.id || item.ID,
          name: item.GROUP_NAME || item.name,
          memberCount: item.GROUP_MEMBER || item.memberCount,
          lastTime: item.GROUP_SHOW_LASTTIME || item.lastTime,
          lastMessage: item.lastMessage || '',
        })));
      } catch (error) {
        setGroupList([]);
      } finally {
        setLoading(false);
      }
    };
    fetchGroups();
    // eslint-disable-next-line
  }, [activeTab]);

  // 加载消息列表
  const handleGroupSelect = async (group: GroupInfo) => {
    setSelectedGroup(group);
    setLoading(true);
    try {
      const data_methods = activeTab === 'wechat' ? 'return_wechat_message' : 'return_qq_message';
      const requestData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods,
        data_argument: {},
        data_kwargs: { GROUP_NUMBER: group.id },
      };
      const result = await AsyncTokenRequests(requestData);
      let messageListRaw: any[] = [];
      if (result.Status === 'Success') {
        messageListRaw = result.Group_Content_List;
      }
      setMessageList((messageListRaw || []).map((item: any) => ({
        ID: item.ID || item.id,
        SOURCE_WEB_DATE: item.SOURCE_WEB_DATE || item.time,
        SOURCE_AUTHOR: item.SOURCE_AUTHOR || item.author,
        SOURCE_TITLE: item.SOURCE_TITLE || item.title,
      })));
    } catch (error) {
      setMessageList([]);
    } finally {
      setLoading(false);
    }
  };


  const columns = [
    { title: '序号', dataIndex: 'ID', key: 'ID', width: 80, align: 'center' as const },
    { title: '创建时间', dataIndex: 'SOURCE_WEB_DATE', key: 'SOURCE_WEB_DATE', width: 180, align: 'center' as const },
    { title: '消息作者', dataIndex: 'SOURCE_AUTHOR', key: 'SOURCE_AUTHOR', width: 120, align: 'center' as const },
    { title: '消息内容', dataIndex: 'SOURCE_TITLE', key: 'SOURCE_TITLE', align: 'left' as const, ellipsis: true },
  ];

  return (
    <div style={{ minHeight: '100vh', padding: 16, color: '#fff' }}>
      <Tabs activeKey={activeTab} onChange={key => setActiveTab(key as 'wechat' | 'qq')}>
        <TabPane tab="微信" key="wechat" />
        <TabPane tab="QQ" key="qq" />
      </Tabs>
      <div style={{ display: 'flex', gap: 24, marginTop: 16, alignItems: 'flex-start' }}>
        {/* 群组列表 */}
        <div style={{ width: 320, borderRadius: 10, padding: '12px 8px', minHeight: 600, background: 'rgba(24, 24, 24, 0.15)', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>
          <Input.Search
            placeholder="群组名"
            allowClear
            value={searchKeyword}
            onChange={e => setSearchKeyword(e.target.value)}
            style={{ marginBottom: 12, background: 'rgba(0,0,0,0.1)', border: 'none', color: '#fff' }}
          />
          <Spin spinning={loading} style={{ color: '#fff' }}>
            <List
              dataSource={groupList.filter(g => g.name.includes(searchKeyword))}
              renderItem={group => (
                <div
                  key={group.id}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: '7px 10px',
                    borderRadius: 8,
                    marginBottom: 4,
                    background: selectedGroup?.id === group.id ? 'rgba(24,144,255,0.28)' : 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                  }}
                  onClick={() => handleGroupSelect(group)}
                >
                  <img
                    src={`https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(group.id)}`}
                    alt="avatar"
                    style={{ width: 40, height: 40, borderRadius: '50%', background: '#222', marginRight: 12, flexShrink: 0 }}
                  />
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center', minWidth: 0 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontWeight: 600, fontSize: 16, color: '#fff', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: 160 }}>{group.name}</span>
                      <span style={{ fontSize: 13, color: '#bbb', marginLeft: 10, whiteSpace: 'nowrap' }}>{group.lastTime ?? '-'}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', marginTop: 2 }}>
                      <span style={{ fontSize: 13, color: '#b6e1ff', marginRight: 8 }}>●</span>
                      <span style={{ fontSize: 13, color: '#bbb' }}>{group.memberCount ?? '-'} </span>
                    </div>
                  </div>
                </div>
              )}
            />
          </Spin>
        </div>
        {/* 消息内容 */}
        <div style={{ flex: 1, borderRadius: 10, padding: 18, minHeight: 600, marginLeft: 0 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <MenuOutlined />
              <Title level={5} style={{ marginBottom: 12, color: '#fff' }}>{selectedGroup?.name || '请选择群组'}</Title>
            </div>
            <Button icon={<ReloadOutlined />} onClick={() => setSelectedGroup(null)} disabled={loading}>刷新</Button>
          </div>
          <Table
            columns={columns}
            dataSource={messageList}
            rowKey="ID"
            loading={loading}
            pagination={{
              pageSize: 10, // 默认每页10条
              showSizeChanger: true, // 允许切换每页条数
              pageSizeOptions: [5, 10, 20, 50, 100], // 可选项
              showQuickJumper: true,
              showTotal: total => `共 ${total} 条消息`
            }}
            scroll={{ y: 400 }}
            size="small"
          />
        </div>
      </div>
    </div>
  );
};

export default Page_Local_Monitor;
