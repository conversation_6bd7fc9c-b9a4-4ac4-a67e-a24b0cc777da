# -*- coding: utf-8 -*-
import time, os, sys, cv2
from PySide6 import QtCore, QtGui, QtWidgets
import Component_Common
import qtawesome

sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils\Toolkit")
# import Service_Table
import Service_Print, Service_Table

PP = Service_Print.Service_Print()
PW = Service_Print.Service_Print('Warning')
PI = Service_Print.Service_Print('Info')
PS = Service_Print.Service_Print('Success')
PE = Service_Print.Service_Print('Error')
PC = Service_Print.Service_Print('Core')
PK = Service_Print.Service_Print('Key')
PT = Service_Print.Service_Print('TryError')
PJ = Service_Print.Service_Print('Json')
PL = Service_Print.Service_Print('Logging')
PPP = Service_Print.Service_Print('Json_Time')
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils")
from UtilsCenter import *

Page_Info = {}


class Component_Devicetree_List(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.QWidget_Parent = parent
        self.machine_list = self.load_machine_data()  # 加载真实数据
        self.tree_data = self.build_tree_data()  # 构建树形数据
        self.expanded_nodes = set()  # 记录展开的节点
        self.initUI()

    def load_machine_data(self):
        """从数据库加载设备数据"""
        try:
            Data = {
                "Table_Name": "Machine_Info_list",
                "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Machine\Machine_Info_list.db",
            }
            __Service_Table = Service_Table.Service_Table("sqlite_backall")
            machine_list = __Service_Table(Data)
            return machine_list
        except Exception as e:
            PE(f"加载设备数据失败: {e}")
            return []

    def build_tree_data(self):
        """根据真实数据构建树形结构"""
        tree_data = {}

        if not self.machine_list:
            return tree_data

        # 按设备类型分组
        device_groups = {}
        for machine in self.machine_list:
            machine_type = machine.get('MACHINE_TYPE', 'Unknown')
            machine_name = machine.get('MACHINE_NAME', 'Unknown Device')

            if machine_type not in device_groups:
                device_groups[machine_type] = []
            device_groups[machine_type].append(machine_name)

        # 构建树形数据结构
        for device_type, device_names in device_groups.items():
            children = {}
            active_count = 0

            for device_name in device_names:
                # 这里可以根据设备状态设置不同的图标
                # 暂时都设置为在线状态
                children[device_name] = {
                    "type": "leaf",
                    "icon": "🟢"  # 绿色表示在线
                }
                active_count += 1

            tree_data[device_type] = {
                "count": f"[{active_count}/{len(device_names)}]",
                "children": children
            }

        return tree_data

    def refresh_data(self):
        """刷新数据并重建树形结构"""
        # 保存当前展开状态
        expanded_backup = self.expanded_nodes.copy()

        self.machine_list = self.load_machine_data()
        self.tree_data = self.build_tree_data()

        # 恢复展开状态
        self.expanded_nodes = expanded_backup

        self.refresh_tree()

        # 更新统计信息
        self.update_stats_display()

        # 重置滚动条位置到顶部
        self.QScrollArea_Tree.verticalScrollBar().setValue(0)


    def initUI(self):
        Page_Info["Page_Element_List"] = {}
        Page_Info["Page_Def_List"] = {}
        Page_Info["StyleSheet"] = {}

        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        # 搜索框
        self.create_search_box(__QVBoxLayout)

        # 标签页
        self.create_tabs(__QVBoxLayout)

        # 树形结构容器
        self.QWidget_TreeContainer = QtWidgets.QWidget()
        self.QWidget_TreeContainer.setStyleSheet("QWidget{background:transparent;border: none;}")
        self.QVBoxLayout_Tree = QtWidgets.QVBoxLayout(self.QWidget_TreeContainer)
        self.QVBoxLayout_Tree.setSpacing(0)
        self.QVBoxLayout_Tree.setContentsMargins(0, 0, 0, 0)

        # 创建树形结构
        self.create_tree_structure()

        # 创建滚动区域
        self.QScrollArea_Tree = QtWidgets.QScrollArea()
        self.QScrollArea_Tree.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(25,25,50,1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(25,25,50,1);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(25,25,50,1);
            }
        """)
        self.QScrollArea_Tree.setWidget(self.QWidget_TreeContainer)
        self.QScrollArea_Tree.setWidgetResizable(True)

        __QVBoxLayout.addWidget(self.QScrollArea_Tree, 1)
        self.Set_Popup()
        self.PopupParameter = {}

    def Set_Popup(self):
        self.QLabel_Popup = QtWidgets.QLabel(self)
        self.QLabel_Popup.resize(300, 280)  # 适中的尺寸
        self.QLabel_Popup.setStyleSheet("background-color: rgba(25,25,50,1); border-radius: 5px; padding: 3px;")
        self.QLabel_Popup.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup.setVisible(False)

        __QVBoxLayout = QtWidgets.QVBoxLayout(self.QLabel_Popup)
        __QVBoxLayout.setSpacing(8)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        # 标题区域
        self.QLabel_Popup_Title = QtWidgets.QLabel("设备视频源配置")
        self.QLabel_Popup_Title.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_Title.setFixedHeight(30)
        self.QLabel_Popup_Title.setStyleSheet("background: transparent;color:rgba(0, 255, 136, 255); ")
        self.QLabel_Popup_Title.setFont(QtGui.QFont("Microsoft YaHei", 8))

        # 内容区域
        self.QLabel_Popup_Content = QtWidgets.QLabel()
        self.QLabel_Popup_Content.setStyleSheet("background:transparent;border: none;")
        self.QVBoxLayout_Popup_Content = QtWidgets.QVBoxLayout(self.QLabel_Popup_Content)
        self.QVBoxLayout_Popup_Content.setSpacing(8)
        self.QVBoxLayout_Popup_Content.setAlignment(QtCore.Qt.AlignCenter)
        self.QVBoxLayout_Popup_Content.setContentsMargins(0, 0, 0, 0)

        # 按钮区域 -
        QLabel_Bottom = QtWidgets.QLabel()
        QLabel_Bottom.setFixedHeight(30)
        QLabel_Bottom.setStyleSheet("background:transparent;border: none;")

        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout(QLabel_Bottom)
        QHBoxLayout_Bottom.setSpacing(8)
        QHBoxLayout_Bottom.setAlignment(QtCore.Qt.AlignCenter)
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)

        StyleSheet_QLabel = """QLabel {
                                                      background-color: rgba(255,255,255,0.8);
                                                      border-radius: 6px;
                                                      border: none;
                                                      color:#1E90FF
                                                  }
                                                  QLabel:hover {
                                                      background-color: rgba(255,255,255,0.3);
                                                      border: none;
                                                  }
                                              """

        # 取消按钮
        self.QLabel_Popup_No = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_No.setFixedSize(50, 25)
        self.QLabel_Popup_No.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_No.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_No.setText("取消")
        self.QLabel_Popup_No.clicked.connect(lambda: self.Popup_Close())
        self.QLabel_Popup_No.setStyleSheet("""
            QLabel {
                background-color: transparent;
                color: white;
                border: none;
            }
            QLabel:hover {
                text-decoration: underline; 
            }
        """)

        # 确定按钮
        self.QLabel_Popup_Yes = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_Yes.setFixedSize(50, 25)
        self.QLabel_Popup_Yes.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_Yes.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_Yes.setText("确定")
        self.QLabel_Popup_Yes.clicked.connect(lambda: self.Popup_Emit())
        self.QLabel_Popup_Yes.setStyleSheet("""
            QLabel {
                background-color: transparent;
                color: white;
                border: none;
            }
            QLabel:hover {
                text-decoration: underline; 
            }
        """)

        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_No)
        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_Yes)

        # 添加到主布局
        __QVBoxLayout.addWidget(self.QLabel_Popup_Title)
        __QVBoxLayout.addWidget(self.QLabel_Popup_Content, 1)
        __QVBoxLayout.addWidget(QLabel_Bottom)

    def Set_Popup_Channel_Select(self, PopupParameter):
        # 清空现有内容
        while self.QVBoxLayout_Popup_Content.count():
            item = self.QVBoxLayout_Popup_Content.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # 设备名称显示
        device_label = QtWidgets.QLabel(f"设备: {PopupParameter['device_name']}")
        device_label.setStyleSheet("""
            QLabel {
                background: transparent;
                color: rgba(0, 255, 136, 255);
                font-size: 10px;
                font-weight: bold;
                padding: 5px;
            }
        """)
        device_label.setAlignment(QtCore.Qt.AlignCenter)
        self.QVBoxLayout_Popup_Content.addWidget(device_label)

        # 视频源显示区域
        self.video_source_display = QtWidgets.QTextEdit()
        self.video_source_display.setPlaceholderText("视频源地址")
        self.video_source_display.setPlainText(PopupParameter['video_source'])
        self.video_source_display.setReadOnly(True)
        self.video_source_display.setFixedHeight(80)  # 固定高度
        self.video_source_display.setStyleSheet("""
            QTextEdit {
                background-color: rgba(0, 0, 0, 0.8);
                border: 1px solid rgba(76, 201, 240, 0.4);
                border-radius: 3px;
                padding: 6px 10px;
                color: #e0e0e0;
                font-family: "Microsoft YaHei";
                font-size: 10px;
                selection-background-color: #000000;
                selection-color: #ffffff;
            }
            QTextEdit:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
            }
        """)
        self.QVBoxLayout_Popup_Content.addWidget(self.video_source_display)

        # 通道选择
        channel_label = QtWidgets.QLabel("选择通道:")
        channel_label.setStyleSheet("""
            QLabel {
                background: transparent;
                color: rgba(255, 255, 255, 0.8);
                font-size: 10px;
                padding: 5px 0px;
            }
        """)
        self.QVBoxLayout_Popup_Content.addWidget(channel_label)

        self.channel_combo = QtWidgets.QComboBox()
        self.channel_combo.setFixedHeight(30)
        self.channel_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(0, 0, 0, 0.8);
                border: 1px solid rgba(76, 201, 240, 0.4);
                border-radius: 3px;
                padding: 6px 10px;
                color: #e0e0e0;
                font-family: "Microsoft YaHei";
                font-size: 11px;
            }
            QComboBox:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
            }
            QComboBox:focus {
                border: 1px solid #4cc9f0;
                background-color: rgba(255, 255, 255, 0.15);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #e0e0e0;
                width: 0;
                height: 0;
            }
            QComboBox QAbstractItemView {
                background-color: rgba(0, 0, 0, 0.9);
                border: 1px solid rgba(76, 201, 240, 0.4);
                border-radius: 3px;
                color: #e0e0e0;
                font-size: 11px;
                selection-background-color: rgba(76, 201, 240, 0.3);
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 5px 10px;
                border: none;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(76, 201, 240, 0.2);
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: rgba(76, 201, 240, 0.4);
            }
        """)

        # 添加通道选项
        self.channel_combo.addItem("请选择通道...", None)
        for i in range(1, 10):
            self.channel_combo.addItem(f"通道 {i}", i)

        self.QVBoxLayout_Popup_Content.addWidget(self.channel_combo)

        # 添加一些间距
        spacer = QtWidgets.QLabel()
        spacer.setFixedHeight(10)
        spacer.setStyleSheet("background: transparent;")
        self.QVBoxLayout_Popup_Content.addWidget(spacer)


    def select_channel(self, channel_id):
        """选择通道"""
        self.selected_channel = channel_id

        # 更新按钮状态
        for i, btn in enumerate(self.channel_buttons):
            if i + 1 == channel_id:
                btn.setChecked(True)
            else:
                btn.setChecked(False)

        PP(f"选择了通道: {channel_id}")

    def Open_Popup(self, PopupParameter):
        """打开弹窗"""
        self.PopupParameter = PopupParameter

        # 设置标题
        self.QLabel_Popup_Title.setText("设备视频源配置")

        # 设置内容
        self.Set_Popup_Channel_Select(PopupParameter)

        # 显示弹窗并居中 - 与设置组件一致的居中方式
        self.QLabel_Popup.setVisible(True)
        self.QLabel_Popup.move(
            self.width() // 2 - self.QLabel_Popup.width() // 2,
            self.height() // 2 - self.QLabel_Popup.height() // 2
        )

    def Popup_Emit(self):
        """确定按钮点击事件"""
        selected_channel = self.channel_combo.currentData()

        if selected_channel is None:
            QtWidgets.QMessageBox.warning(self, "提示", "请选择一个通道！")
            return

        # 发送信号
        result = {
            "Command": "SetVideoSource",
            "Device_Name": self.PopupParameter['device_name'],
            "Channel_ID": selected_channel,
            "Video_Source": self.PopupParameter['video_source']
        }
        self.Signal_Result.emit(result)

        PS(f"已将设备 '{self.PopupParameter['device_name']}' 的视频源发送到通道 {selected_channel}")

        # 关闭弹窗
        self.Popup_Close()

    def Popup_Close(self):
        """取消按钮点击事件"""
        self.QLabel_Popup.setVisible(False)

        # 重置下拉框
        if hasattr(self, 'channel_combo'):
            self.channel_combo.setCurrentIndex(0)


    def create_search_box(self, layout):
        """创建搜索框"""
        search_container = QtWidgets.QWidget()
        search_container.setFixedHeight(40)
        search_container.setStyleSheet("background: transparent;")

        search_layout = QtWidgets.QHBoxLayout(search_container)
        search_layout.setContentsMargins(10, 5, 10, 5)

        self.search_input = QtWidgets.QLineEdit()
        self.search_input.setPlaceholderText("搜索设备...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 8px 15px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 1px solid rgba(0, 255, 136, 255);
                background-color: rgba(255, 255, 255, 0.15);
            }
        """)
        self.search_input.textChanged.connect(self.simple_search)
        search_layout.addWidget(self.search_input)

        # 添加刷新按钮
        refresh_btn = QtWidgets.QPushButton("🔄")
        refresh_btn.setFixedSize(30, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 255, 136, 0.2);
                border: 1px solid rgba(0, 255, 136, 255);
            }
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setToolTip("刷新设备列表")
        search_layout.addWidget(refresh_btn)

        layout.addWidget(search_container)

    def create_tabs(self, layout):
        """创建标签页"""
        tab_container = QtWidgets.QWidget()
        tab_container.setFixedHeight(40)
        tab_container.setStyleSheet("background: transparent;")

        tab_layout = QtWidgets.QHBoxLayout(tab_container)
        tab_layout.setContentsMargins(10, 0, 10, 0)
        tab_layout.setSpacing(20)

        # 设备树标签（激活状态）
        device_tab = QtWidgets.QLabel("设备树")
        device_tab.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-bottom: 2px solid rgba(0, 255, 136, 255);
                padding-bottom: 8px;
            }
        """)

        # 显示设备统计信息
        total_devices = len(self.machine_list)
        device_types = len(self.tree_data)
        stats_label = QtWidgets.QLabel(f"({device_types} 类型, {total_devices} 设备)")
        stats_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 12px;
                padding-bottom: 8px;
            }
        """)

        tab_layout.addWidget(device_tab)
        tab_layout.addWidget(stats_label)
        tab_layout.addStretch()

        layout.addWidget(tab_container)

    def simple_search(self, text):
        """简单搜索功能"""
        # 遍历所有树节点，隐藏/显示匹配的项目
        for i in range(self.QVBoxLayout_Tree.count()):
            item = self.QVBoxLayout_Tree.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                # 检查节点名称是否包含搜索文本
                should_show = self.check_node_match(widget, text.lower())
                widget.setVisible(should_show)

    def check_node_match(self, widget, search_text):
        """检查节点是否匹配搜索文本"""
        if not search_text:  # 如果搜索为空，显示所有
            return True

        # 查找节点中的文本标签
        for child in widget.findChildren(QtWidgets.QLabel):
            if hasattr(child, 'text') and search_text in child.text().lower():
                return True

        return False

    def create_tree_structure(self):
        """创建树形结构"""
        if not self.tree_data:
            # 如果没有数据，显示提示信息
            no_data_label = QtWidgets.QLabel("暂无设备数据")
            no_data_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 14px;
                    padding: 20px;
                    background: transparent;
                }
            """)
            no_data_label.setAlignment(QtCore.Qt.AlignCenter)
            self.QVBoxLayout_Tree.addWidget(no_data_label)
            return

        for node_name, node_data in self.tree_data.items():
            tree_item = self.create_tree_item(node_name, node_data, 0)
            self.QVBoxLayout_Tree.addWidget(tree_item)

        # 添加弹性空间
        self.QVBoxLayout_Tree.addStretch()

    def create_tree_item(self, name, data, level):
        """创建树形节点"""
        container = QtWidgets.QWidget()
        container.setStyleSheet("background: transparent;")

        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建节点主体
        node_widget = self.create_node_widget(name, data, level)
        main_layout.addWidget(node_widget)

        # 创建子节点容器
        if "children" in data and data["children"]:
            children_container = QtWidgets.QWidget()
            children_container.setStyleSheet("background: transparent;")
            children_layout = QtWidgets.QVBoxLayout(children_container)
            children_layout.setContentsMargins(0, 0, 0, 0)
            children_layout.setSpacing(0)

            # 添加子节点
            for child_name, child_data in data["children"].items():
                child_item = self.create_tree_item(child_name, child_data, level + 1)
                children_layout.addWidget(child_item)

            # 设置子节点容器的可见性
            node_id = f"{name}_{level}"
            children_container.setVisible(node_id in self.expanded_nodes)

            # 存储容器引用以便后续控制
            setattr(container, 'children_container', children_container)
            main_layout.addWidget(children_container)

        return container

    def create_node_widget(self, name, data, level):
        """创建单个节点控件"""
        node_widget = QtWidgets.QWidget()
        node_widget.setFixedHeight(32)
        node_widget.setStyleSheet("""
            QWidget {
                background: transparent;
            }
            QWidget:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)

        # 存储节点信息，用于右键菜单
        node_widget.node_name = name
        node_widget.node_data = data
        node_widget.node_level = level

        # 设置右键菜单
        node_widget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        node_widget.customContextMenuRequested.connect(
            lambda pos: self.show_context_menu(pos, node_widget)
        )

        # 设置双击事件
        node_widget.mouseDoubleClickEvent = lambda event: self.on_node_double_click(node_widget)

        layout = QtWidgets.QHBoxLayout(node_widget)
        layout.setContentsMargins(10 + level * 20, 0, 10, 0)
        layout.setSpacing(8)

        # 展开/收起按钮
        expand_btn = QtWidgets.QPushButton()
        expand_btn.setFixedSize(16, 16)
        expand_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.6);
                font-size: 10px;
            }
            QPushButton:hover {
                color: white;
            }
        """)

        # 设置展开/收起图标和功能
        node_id = f"{name}_{level}"
        has_children = "children" in data and data["children"]

        if has_children:
            if node_id in self.expanded_nodes:
                expand_btn.setText("▼")
            else:
                expand_btn.setText("▶")
            expand_btn.clicked.connect(lambda: self.toggle_node(node_id, node_widget.parent()))
        else:
            expand_btn.setText("  ")
            expand_btn.setEnabled(False)

        layout.addWidget(expand_btn)

        # 节点图标
        icon_label = QtWidgets.QLabel()
        icon_label.setFixedSize(16, 16)
        icon_label.setStyleSheet("color: rgba(255, 255, 255, 0.8);")

        if data.get("type") == "leaf":
            icon_text = data.get("icon", "🔧")  # 设备图标
        else:
            icon_text = "📁"  # 文件夹图标

        icon_label.setText(icon_text)
        layout.addWidget(icon_label)

        # 节点名称
        name_label = QtWidgets.QLabel(name)
        name_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                background: transparent;
            }
        """)
        layout.addWidget(name_label)

        # 计数信息
        if "count" in data:
            count_label = QtWidgets.QLabel(data["count"])
            count_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 11px;
                    background: transparent;
                }
            """)
            layout.addWidget(count_label)

        layout.addStretch()

        return node_widget

    def on_node_double_click(self, node_widget):
        """节点双击事件"""
        """节点双击事件"""
        if node_widget.node_data.get("type") == "leaf":
            # 如果是设备节点，获取设备的视频源并传递给视频播放页面
            self.send_device_video_source(node_widget)
        else:
            # 如果是组节点，切换展开/收起状态
            node_id = f"{node_widget.node_name}_{node_widget.node_level}"
            self.toggle_node(node_id, node_widget.parent())

    def show_context_menu(self, pos, node_widget):
        """显示右键菜单"""
        # 如果是设备类型节点，不显示右键菜单
        if node_widget.node_data.get("type") != "leaf":
            return

        # 只为设备节点创建菜单
        menu = QtWidgets.QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: rgba(45, 45, 45, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 5px;
            }
            QMenu::item {
                background-color: transparent;
                color: white;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QMenu::item:selected {
                background-color: rgba(0, 255, 136, 0.2);
                color: rgba(0, 255, 136, 255);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
                margin: 5px 0px;
            }
        """)

        # 设备节点菜单 - 三个功能
        info_action = menu.addAction("ℹ️ 设备信息")
        info_action.triggered.connect(
            lambda: self.show_device_info(node_widget)
        )

        video_action = menu.addAction("📹 发送视频源")
        video_action.triggered.connect(
            lambda: self.send_device_video_source(node_widget)
        )

        change_group_action = menu.addAction("🔄 更换分组")
        change_group_action.triggered.connect(
            lambda: self.change_device_group(node_widget)
        )

        # 显示菜单
        global_pos = node_widget.mapToGlobal(pos)
        menu.exec_(global_pos)

    def send_device_video_source(self, node_widget):
        """发送设备视频源到视频播放页面"""
        device_name = node_widget.node_name

        # 从原始数据中查找设备信息
        device_info = None
        for machine in self.machine_list:
            if machine.get('MACHINE_NAME') == device_name:
                device_info = machine
                break

        if not device_info:
            QtWidgets.QMessageBox.warning(
                self, "错误", "找不到设备信息！"
            )
            return

        # 尝试从设备配置中提取视频源URL
        video_source = self.extract_video_source(device_info)

        if not video_source:
            QtWidgets.QMessageBox.warning(
                self, "错误", "该设备没有可用的视频源！"
            )
            return

        # 使用弹窗方式选择通道
        popup_params = {
            'device_name': device_name,
            'video_source': video_source,
            'device_info': device_info
        }
        self.Open_Popup(popup_params)


    def extract_video_source(self, device_info):
        """从设备信息中提取视频源URL"""
        try:
            # 尝试解析MACHINE_CHANNEL_CONFIG字段
            channel_config = device_info.get('MACHINE_CHANNEL_CONFIG')
            if not channel_config:
                return None

            # 如果是字符串，尝试解析
            if isinstance(channel_config, str):
                import ast
                try:
                    channel_config = ast.literal_eval(channel_config)
                except:
                    # 如果解析失败，尝试从字符串中提取URL
                    return self.extract_url_from_string(channel_config)

            # 如果是列表，取第一个通道
            if isinstance(channel_config, list) and len(channel_config) > 0:
                first_channel = channel_config[0]
                # 注意这里是 Stream_URL 而不是 Stream URL
                stream_url = first_channel.get('Stream_URL', {})

                # 优先选择HTTPS FLV格式，支持多种键名格式
                for key in ['HTTPS_FLV', 'https_flv', 'HTTPS FLV']:
                    if key in stream_url:
                        return stream_url[key]

                # 其他格式的优先级
                for key in ['FLV', 'flv']:
                    if key in stream_url:
                        return stream_url[key]

                for key in ['RTSP', 'rtsp']:
                    if key in stream_url:
                        return stream_url[key]

                for key in ['RTMP', 'rtmp']:
                    if key in stream_url:
                        return stream_url[key]

                # 返回第一个可用的URL
                for url_type, url in stream_url.items():
                    if url and isinstance(url, str) and url.startswith(('http', 'rtsp', 'rtmp')):
                        return url

            return None

        except Exception as e:
            PE(f"提取视频源时发生错误: {e}")
            # 尝试从字符串中提取URL
            channel_config_str = str(device_info.get('MACHINE_CHANNEL_CONFIG', ''))
            return self.extract_url_from_string(channel_config_str)

    def extract_url_from_string(self, config_str):
        """从配置字符串中提取视频URL"""
        try:
            import re

            # 查找HTTPS FLV URL - 支持多种格式
            patterns = [
                r"'HTTPS_FLV':\s*'([^']+)'",
                r"'https_flv':\s*'([^']+)'",
                r"'HTTPS FLV':\s*'([^']+)'"
            ]

            for pattern in patterns:
                match = re.search(pattern, config_str)
                if match:
                    return match.group(1)

            # 查找FLV URL
            flv_patterns = [
                r"'FLV':\s*'([^']+)'",
                r"'flv':\s*'([^']+)'"
            ]

            for pattern in flv_patterns:
                match = re.search(pattern, config_str)
                if match:
                    return match.group(1)

            # 查找RTSP URL
            rtsp_patterns = [
                r"'RTSP':\s*'([^']+)'",
                r"'rtsp':\s*'([^']+)'"
            ]

            for pattern in rtsp_patterns:
                match = re.search(pattern, config_str)
                if match:
                    return match.group(1)

            # 查找RTMP URL
            rtmp_patterns = [
                r"'RTMP':\s*'([^']+)'",
                r"'rtmp':\s*'([^']+)'"
            ]

            for pattern in rtmp_patterns:
                match = re.search(pattern, config_str)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            PE(f"从字符串提取URL时发生错误: {e}")
            return None



    def change_device_group(self, node_widget):
        """更换设备分组"""
        device_name = node_widget.node_name

        # 从原始数据中查找设备信息
        device_info = None
        for machine in self.machine_list:
            if machine.get('MACHINE_NAME') == device_name:
                device_info = machine
                break

        if not device_info:
            QtWidgets.QMessageBox.warning(
                self, "错误", "找不到设备信息！"
            )
            return

        # 获取当前分组
        current_group = device_info.get('MACHINE_TYPE', 'Unknown')
        device_id = device_info.get('ID')

        if device_id is None:
            QtWidgets.QMessageBox.warning(
                self, "错误", "找不到设备ID！"
            )
            return

        # 获取所有现有的分组类型
        existing_groups = set()
        for machine in self.machine_list:
            machine_type = machine.get('MACHINE_TYPE')
            if machine_type:
                existing_groups.add(machine_type)

        existing_groups = sorted(list(existing_groups))

        # 创建分组选择对话框
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("更换设备分组")
        dialog.setFixedSize(400, 300)
        dialog.setStyleSheet("""
            QDialog {
                background-color: rgba(45, 45, 45, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 5px;
                padding: 5px;
                color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
                width: 0;
                height: 0;
            }
            QLineEdit {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 5px;
                padding: 5px;
                color: white;
                font-size: 12px;
            }
            QPushButton {
                background-color: rgba(0, 255, 136, 0.2);
                border: 1px solid rgba(0, 255, 136, 255);
                border-radius: 5px;
                padding: 8px 15px;
                color: white;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(0, 255, 136, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(0, 255, 136, 0.4);
            }
        """)

        layout = QtWidgets.QVBoxLayout(dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 设备信息显示
        info_label = QtWidgets.QLabel(f"设备名称: {device_name}\n当前分组: {current_group}")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 分组选择
        group_label = QtWidgets.QLabel("选择新分组:")
        layout.addWidget(group_label)

        group_combo = QtWidgets.QComboBox()
        group_combo.setEditable(True)  # 允许输入新分组名
        group_combo.addItems(existing_groups)

        # 设置当前分组为默认选择
        if current_group in existing_groups:
            group_combo.setCurrentText(current_group)

        layout.addWidget(group_combo)

        # 提示信息
        tip_label = QtWidgets.QLabel("提示: 可以选择现有分组或输入新分组名称")
        tip_label.setStyleSheet("color: rgba(255, 255, 255, 0.6); font-size: 10px;")
        layout.addWidget(tip_label)

        # 按钮
        button_layout = QtWidgets.QHBoxLayout()

        confirm_btn = QtWidgets.QPushButton("确认更换")
        cancel_btn = QtWidgets.QPushButton("取消")

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        layout.addLayout(button_layout)

        # 按钮事件
        def on_confirm():
            new_group = group_combo.currentText().strip()
            if not new_group:
                QtWidgets.QMessageBox.warning(dialog, "警告", "请输入分组名称！")
                return

            if new_group == current_group:
                QtWidgets.QMessageBox.information(dialog, "提示", "分组未发生变化！")
                dialog.accept()
                return

            # 执行数据库更新操作
            try:
                Replace_Data = {
                    "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Machine\Machine_Info_list.db",
                    "Table_Name": "Machine_Info_list",
                    "Replace_Data": {
                        "ID": str(device_id),
                        "MACHINE_TYPE": new_group
                    }
                }

                Service_Test_Table = Service_Table.Service_Table("sqlite_replace")
                result = Service_Test_Table(Replace_Data)

                if result:
                    # 更新内存中的数据
                    device_info['MACHINE_TYPE'] = new_group

                    PS(f"设备分组更换成功: {device_name} ({current_group} -> {new_group})")

                    # 重新构建树形数据并刷新
                    self.tree_data = self.build_tree_data()
                    self.refresh_tree()
                    self.update_stats_display()

                    # 重置滚动条位置到顶部
                    self.QScrollArea_Tree.verticalScrollBar().setValue(0)

                    QtWidgets.QMessageBox.information(
                        dialog, "成功",
                        f"设备 '{device_name}' 已成功从 '{current_group}' 移动到 '{new_group}'"
                    )
                    dialog.accept()
                else:
                    PE(f"设备分组更换失败: {device_name}")
                    QtWidgets.QMessageBox.critical(dialog, "错误", "数据库更新失败！")

            except Exception as e:
                PE(f"更换设备分组时发生错误: {e}")
                QtWidgets.QMessageBox.critical(dialog, "错误", f"操作失败: {str(e)}")

        def on_cancel():
            dialog.reject()

        confirm_btn.clicked.connect(on_confirm)
        cancel_btn.clicked.connect(on_cancel)

        # 显示对话框
        dialog.exec_()



    def update_stats_display(self):
        """更新统计信息显示"""
        try:
            # 查找统计标签并更新
            total_devices = len(self.machine_list)
            device_types = len(self.tree_data)

            # 这里可以添加更新UI中统计信息的逻辑
            # 由于原代码中统计信息是在初始化时创建的，这里只是一个占位方法
            # 如果需要动态更新统计信息，需要保存对统计标签的引用
            pass
        except Exception as e:
            PE(f"更新统计信息时发生错误: {e}")

    def show_device_info(self, node_widget):
        """显示设备信息"""
        device_name = node_widget.node_name

        # 从原始数据中查找设备信息
        device_info = None
        for machine in self.machine_list:
            if machine.get('MACHINE_NAME') == device_name:
                device_info = machine
                break

        if device_info:
            info_text = f"设备名称: {device_info.get('MACHINE_NAME', 'Unknown')}\n"
            info_text += f"设备类型: {device_info.get('MACHINE_TYPE', 'Unknown')}\n"
            # 添加其他字段信息
            for key, value in device_info.items():
                if key not in ['MACHINE_NAME', 'MACHINE_TYPE']:
                    info_text += f"{key}: {value}\n"

            QtWidgets.QMessageBox.information(
                self, "设备信息", info_text
            )
        else:
            QtWidgets.QMessageBox.warning(
                self, "错误", "找不到设备信息！"
            )

    def refresh_tree(self):
        """刷新树形结构"""
        # 清空现有树形结构
        for i in reversed(range(self.QVBoxLayout_Tree.count())):
            child = self.QVBoxLayout_Tree.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 重新创建树形结构
        self.create_tree_structure()

        # 强制更新布局
        self.QWidget_TreeContainer.updateGeometry()
        self.QScrollArea_Tree.updateGeometry()

        # 强制处理所有待处理的事件
        QtWidgets.QApplication.processEvents()

        # 重置滚动条位置到顶部
        self.QScrollArea_Tree.verticalScrollBar().setValue(0)



    def toggle_node(self, node_id, container):
        """切换节点的展开/收起状态"""
        children_container = getattr(container, 'children_container', None)
        if not children_container:
            return

        if node_id in self.expanded_nodes:
            # 收起节点
            self.expanded_nodes.remove(node_id)
            children_container.setVisible(False)
        else:
            # 展开节点
            self.expanded_nodes.add(node_id)
            children_container.setVisible(True)

        # 更新展开/收起按钮的图标
        self.update_expand_button(container, node_id)

    def update_expand_button(self, container, node_id):
        """更新展开/收起按钮的图标"""

        # 查找按钮控件并更新图标
        def find_expand_button(widget):
            if isinstance(widget, QtWidgets.QPushButton):
                return widget
            for child in widget.children():
                if isinstance(child, QtWidgets.QWidget):
                    result = find_expand_button(child)
                    if result:
                        return result
            return None

        expand_btn = find_expand_button(container)
        if expand_btn:
            if node_id in self.expanded_nodes:
                expand_btn.setText("▼")
            else:
                expand_btn.setText("▶")

    def Popup_Emit(self):
        """弹窗确认事件"""
        PI("设备树项目已选择")

        # 检查是否选择了通道
        if not hasattr(self, 'channel_combo') or not self.channel_combo:
            QtWidgets.QMessageBox.warning(self, "提示", "请选择一个通道！")
            return

        selected_channel = self.channel_combo.currentData()

        if selected_channel is None:
            QtWidgets.QMessageBox.warning(self, "提示", "请选择一个通道！")
            return

        # 检查是否有弹窗参数
        if not hasattr(self, 'PopupParameter') or not self.PopupParameter:
            PE("没有设备信息")
            return

        # 发送信号给视频播放页面
        result = {
            "Command": "SetVideoSource",
            "Device_Name": self.PopupParameter['device_name'],
            "Channel_ID": selected_channel,
            "Video_Source": self.PopupParameter['video_source']
        }

        PP(f"=== 设备树准备发送信号 ===")
        PP(f"信号内容: {result}")
        # 移除这行：PP(f"信号接收者数量: {self.Signal_Result.receivers()}")

        # 发送信号
        self.Signal_Result.emit(result)

        PP(f"信号已发送")
        PS(f"已将设备 '{self.PopupParameter['device_name']}' 的视频源发送到通道 {selected_channel}")

        # 关闭弹窗
        self.Popup_Close()

        # 显示成功消息
        QtWidgets.QMessageBox.information(
            self, "设置成功",
            f"已将设备 '{self.PopupParameter['device_name']}' 的视频源设置到通道 {selected_channel}\n\n"
            f"视频源: {self.PopupParameter['video_source'][:100]}{'...' if len(self.PopupParameter['video_source']) > 100 else ''}"
        )


    def Popup_Close(self):
        """弹窗关闭事件"""
        PI("设备树选择已取消")

        # 隐藏弹窗
        if hasattr(self, 'QLabel_Popup'):
            self.QLabel_Popup.setVisible(False)

        # 重置下拉框选择
        if hasattr(self, 'channel_combo') and self.channel_combo:
            self.channel_combo.setCurrentIndex(0)

        # 清空弹窗参数
        if hasattr(self, 'PopupParameter'):
            self.PopupParameter = {}


if __name__ == "__main__":
    Data = {
        "Table_Name": "Machine_Info_list",
        "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Machine\Machine_Info_list.db",
    }
    __Service_Table = Service_Table.Service_Table("sqlite_backall")
    Machine_List = __Service_Table(Data)
    PJ(Machine_List)
    # print(Machine_List['MACHINE_NAME'])
    # Machine_Type=[item['MACHINE_TYPE'] for item in Machine_List]

    # Machine_Name=[item['MACHINE_NAME'] for item in Machine_List]
    # ID = int([item['ID'] for item in Machine_List][0])
    #
    # # print(Machine_Type)
    #
    # # print(Machine_Name)
    # # print(ID)
    # #
    # # #
    # # #
    # #
    # # Delete_Data = {
    # #
    # #     'Table_Path_SQLite': r"D:\Sentinel Foundation\Data\Machine\Machine_Info_list.db",
    # #     "Delect_Data": {'ID': ID},
    # #     "Table_Name": 'Machine_Info_list',
    # #
    # # }
    # # __Service_Table = Service_Table.Service_Table('sqlite_delete_data')
    # # PP(__Service_Table(Delete_Data))

    # Replace_Data = {
    #     "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Machine\Machine_Info_list.db",
    #     "Table_Name": "Machine_Info_list",
    #     "Replace_Data": {
    #         "ID": "2",
    #         "MACHINE_TYPE": "National_Device"}}
    # Service_Test_Table = Service_Table.Service_Table("sqlite_replace")
    # PP(Service_Test_Table(Replace_Data))

