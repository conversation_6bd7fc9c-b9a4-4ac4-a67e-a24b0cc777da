import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,SystemControl_Preview_Image} from "./Node_System_Control_Preview_Image";

export class Node_System_Component_Preview_Image extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| SystemControl_Preview_Image}> 
  {
    width =480;
    height = 480;
    constructor(Label: string,) {
          super(Label);
    
          this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
          this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));
    
          const ConentControl = new SystemControl_Preview_Image(
            '【标题】:未知', // Label for the text area
            0, // Initial value
            (title) => {
              console.log('TextArea value changed:', title);
            }
          );
          
          this.addControl("Conent",  ConentControl);
    
        };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    