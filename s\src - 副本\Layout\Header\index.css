  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #0a0f1c;
    padding: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  }

  .header-container .ant-btn {
    color: #ffffff;
    transition: color 0.3s ease;
  }
  
  .header-container .ant-btn:hover {
    /* color: #00f0ff; */
    color: #00f0ff !important;
    background-color: transparent !important;
  }
  
  .header-dropdown {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid #2a3147;
    border-radius: 8px;
    color: #ffffff;
  }
  
  .header-dropdown .ant-badge {
    color: #ffffff;
  }
  
  .header-dropdown .ant-btn {
    color: #ffffff;
  }
  
  .header-dropdown .ant-btn:hover {
    color: #00f0ff;
  }
  
  .custom-badge {
    margin-top: 20px !important;
  }

  .header-dropdown-menu {
    background-color: #131a2b !important; /* 背景色 */
    border: 1px solid #2d354d; /* 边框 */
    border-radius: 8px; /* 圆角 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); /* 阴影 */
  }
  
  .header-dropdown-menu .ant-dropdown-menu-item {
    color: #ffffff; /* 文字颜色 */
    font-size: 14px; /* 字体大小 */
    line-height: 1.5; /* 行高 */
    padding: 8px 16px; /* 内间距 */
  }
  
  .header-dropdown-menu .ant-dropdown-menu-item svg {
    color: #ffffff; /* 图标颜色 */
  }
  
  .header-dropdown-menu .ant-dropdown-menu-item:hover,
  .header-dropdown-menu .ant-dropdown-menu-item-active {
    background-color: #1e263d; /* 悬停/激活背景色 */
    color: #00f0ff; /* 悬停/激活文字颜色 */
  }

  :where(.css-dev-only-do-not-override-36gkoj).ant-dropdown .ant-dropdown-menu, 
  :where(.css-dev-only-do-not-override-36gkoj).ant-dropdown-menu-submenu .ant-dropdown-menu {
    background-color: #131a2b !important;
  }

  .header-dropdown-menu,
  :where(.css-dev-only-do-not-override-36gkoj) .header-dropdown-menu {
    background-color: #131a2b !important;
    border: 1px solid #2d354d !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  .header-dropdown-menu .ant-dropdown-menu-item,
  :where(.css-dev-only-do-not-override-36gkoj) .header-dropdown-menu .ant-dropdown-menu-item {
    color: #ffffff !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    padding: 8px 16px !important;
  }

  .header-dropdown-menu .ant-dropdown-menu-item svg,
  :where(.css-dev-only-do-not-override-36gkoj) .header-dropdown-menu .ant-dropdown-menu-item svg {
    color: #ffffff !important;
  }

  .header-dropdown-menu .ant-dropdown-menu-item:hover,
  .header-dropdown-menu .ant-dropdown-menu-item-active,
  :where(.css-dev-only-do-not-override-36gkoj) .header-dropdown-menu .ant-dropdown-menu-item:hover,
  :where(.css-dev-only-do-not-override-36gkoj) .header-dropdown-menu .ant-dropdown-menu-item-active {
    background-color: #1e263d !important;
    color: #00f0ff !important;
  }

  .dark-dropdown :where(.css-dev-only-do-not-override-36gkoj) .ant-dropdown .ant-dropdown-menu,
  .dark-dropdown :where(.css-dev-only-do-not-override-36gkoj) .ant-dropdown-menu-submenu .ant-dropdown-menu {
    background-color: #131a2b !important;
    border: 1px solid #2d354d !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }