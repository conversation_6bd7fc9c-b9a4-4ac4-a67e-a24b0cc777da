# -*- coding: utf-8 -*-
import time, os, sys
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtCore import (Slot, Property)




class Component_Chat(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.setAcceptDrops(True)
        # self.setStyleSheet("QLabel{background:rgba(55, 55, 55, 0);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")
        self.setStyleSheet("QLabel{background:rgba(255, 255, 255, 0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边


        # self.QLabel_Content = QtWidgets.QLabel()
        # # self.QLabel_Content.setFixedHeight(150)
        # self.QLabel_Content.setStyleSheet("QLabel{background:transparent;border: none;}")

        __QVBoxLayout.addWidget(self.Set_Title(), 0)
        __QVBoxLayout.addWidget(self.Set_Content(), 1)
        __QVBoxLayout.addWidget(self.Set_Bottom(), 0)
        # 发送消息示例
        self.Send_Question("视频分析完成了吗？", "user_avatar.png", "小明")
        self.Send_Answer("是的，您的视频已经分析完毕。", "bot_avatar.png", "AI助手")


    def Set_Title(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("""
            QLabel {
                border-radius: 0px;
                background: rgba(0, 0, 0, 155);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        __QLabel.setFixedHeight(48)

        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)  # 内边界
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setMinimumSize(30, 30)
        QLabel_Icon.setMaximumSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        # QLabel_Icon.setPixmap(Image_Logo)
        # QLabel_Icon.setText("提示222")

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        # QLabel_Title.setMinimumSize(230, 30)
        QLabel_Title.setAlignment(QtCore.Qt.AlignVCenter)
        # QLabel_Title.setMaximumSize(230, 30)
        # QLabel_Title.setText(self.Dialog_Info["Dialog_Title"])
        QLabel_Title.setText(R"目标视频：D:\45.MP4 【本地视频】智能体分析结果")

        Icon = qta.icon('mdi.menu', scale_factor=1, color='white', color_active='blue')
        QPushButton_Menu = QtWidgets.QPushButton(Icon, '')
        QPushButton_Menu.setStyleSheet(
            '''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;}''')
        # QPushButton_Exit.setSizePolicy(Button_Adaptive)
        # QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())
        QPushButton_Menu.clicked.connect(lambda: self.Send_Question_Now(
            "这是一段非常长的文本，它应该会自动换行。当文本超过最大宽度时，会自动换到下一行显示。这个功能对于聊天应用非常重要，可以确保消息内容正确显示。"


        ))

        __QHBoxLayout.addWidget(QLabel_Icon, 1, alignment=QtCore.Qt.AlignLeft, )
        __QHBoxLayout.addWidget(QLabel_Title, 9, alignment=QtCore.Qt.AlignLeft)
        __QHBoxLayout.contentsRect()
        __QHBoxLayout.addWidget(QPushButton_Menu, 1)

        return __QLabel

    def Set_Content(self):
        # 创建主容器（设置半透明白色背景）
        chat_container = QtWidgets.QWidget()
        chat_container.setAttribute(QtCore.Qt.WA_TranslucentBackground)  # 允许透明
        chat_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 1);
                border-radius: 8px;
                border: none;
            }
        """)

        # 创建滚动内容容器（也需要透明）
        scroll_content = QtWidgets.QWidget()
        scroll_content.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        scroll_content.setStyleSheet("background: transparent; border: none;")

        # 设置滚动内容布局
        scroll_layout = QtWidgets.QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.addWidget(chat_container)
        scroll_layout.addStretch(1)  # 添加弹性空间

        # 创建滚动区域（完全透明）
        self.scroll_area = QtWidgets.QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setWidget(scroll_content)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                width: 0px;
                background: transparent;
            }
        """)

        # 设置聊天内容布局
        self.chat_layout = QtWidgets.QVBoxLayout(chat_container)
        self.chat_layout.setSpacing(12)
        self.chat_layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距使内容不贴边

        return self.scroll_area


    def Set_Content1(self):
        __QLabel = QtWidgets.QLabel()
        # 关键修改1：确保背景可显示且透明
        __QLabel.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        __QLabel.setStyleSheet("""
                QLabel {
                    background-color: rgba(255, 255, 255, 0.3);
                    border: none;
                }
            """)

        # 关键修改2：滚动区域设置透明
        self.QScrollArea_Centent = QtWidgets.QScrollArea(__QLabel)
        self.QScrollArea_Centent.setWidgetResizable(True)
        self.QScrollArea_Centent.setStyleSheet("""
                QScrollArea {
                    background: transparent;
                    border: none;
                }
                QScrollBar:vertical {
                    width: 8px;
                    background: rgba(200, 200, 200, 50);
                }
                QScrollBar::handle:vertical {
                    background: rgba(150, 150, 150, 100);
                    border-radius: 4px;
                }
            """)

        # 关键修改3：根部件设置透明背景
        self.QWidget_Root = QtWidgets.QWidget()
        self.QWidget_Root.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.QWidget_Root.setStyleSheet("""
                QWidget {
                    background-color: rgba(255, 255, 255, 0.3);
                    border: none;
                }
            """)

        self.QVBoxLayout_Centent = QtWidgets.QVBoxLayout(self.QWidget_Root)
        self.QVBoxLayout_Centent.setSpacing(0)
        self.QVBoxLayout_Centent.setContentsMargins(8, 8, 8, 8)

        # 关键修改4：顶部填充部件
        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.QWidget_TopFiller.setStyleSheet("""
                QWidget {
                    background-color: rgba(255, 255, 255, 0.3);
                    border: none;
                }
            """)

        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(6)
        self.QVBoxLayout_Line.setContentsMargins(0, 8, 0, 0)

        # 添加测试标签（保持原有样式）
        Line_StyleSheet = """
                QLabel {
                    background-color: rgba(0,0,0,0.8);
                    border-radius: 3px;
                    border: none;
                    color: #1E90FF;
                }
            """
        # for i in range(20):
        #     label = QtWidgets.QLabel(f"Line {i+1}")
        #     label.setFixedHeight(30)
        #     label.setStyleSheet(Line_StyleSheet)
        #     self.QVBoxLayout_Line.addWidget(label)

        self.QVBoxLayout_Centent.addWidget(self.QWidget_TopFiller)
        self.QScrollArea_Centent.setWidget(self.QWidget_Root)

        if __QLabel.layout() is None:
            __QLabel.setLayout(QtWidgets.QVBoxLayout())
        __QLabel.layout().addWidget(self.QScrollArea_Centent)

        return __QLabel

    def create_message_widget(self, is_me, avatar, nickname, content, is_image=False):
        """创建消息控件（确保自动换行）"""
        message_widget = QtWidgets.QWidget()
        message_widget.setStyleSheet("background:transparent;")

        # 水平布局
        layout = QtWidgets.QHBoxLayout(message_widget)
        layout.setContentsMargins(8, 4, 8, 4)  # 增加左右边距
        layout.setSpacing(8)

        # 头像
        avatar_label = QtWidgets.QLabel()
        avatar_label.setObjectName("avatar_label")
        avatar_label.setFixedSize(40, 40)
        avatar_label.setStyleSheet("border-radius:20px; border:1px solid #eee;")

        # 消息内容区域
        content_widget = QtWidgets.QWidget()
        content_widget.setStyleSheet("background:transparent;")
        content_layout = QtWidgets.QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(2)

        # 昵称
        nickname_label = QtWidgets.QLabel(nickname)
        nickname_label.setObjectName("nickname_label")
        nickname_label.setStyleSheet("color:#666; font-size:12px;")

        # 消息内容（关键修改部分）
        content_label = QtWidgets.QLabel()
        content_label.setObjectName("content_label")
        content_label.setWordWrap(True)  # 必须启用自动换行
        content_label.setTextFormat(QtCore.Qt.RichText)
        content_label.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Preferred
        )

        # 设置样式表（包含最大宽度）
        bubble_style = """
            QLabel {
                background: rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                padding: 8px 12px;
                color: #fff;
                font-size: 14px;
                max-width: 400px;
            }
        """
        content_label.setStyleSheet(bubble_style)

        # 处理内容
        if is_image:
            pixmap = QtGui.QPixmap(content) if isinstance(content, str) else content
            content_label.setPixmap(pixmap.scaled(300, 300, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
        else:
            # 替换换行符并设置文本
            formatted_text = content.replace('\n', '<br>')
            content_label.setText(formatted_text)

        content_layout.addWidget(nickname_label)
        content_layout.addWidget(content_label)

        # 布局方向
        if is_me:
            layout.addStretch(1)
            layout.addWidget(content_widget)
            layout.addWidget(avatar_label)
            content_label.setStyleSheet(bubble_style + """
                background: rgba(100, 180, 255, 0.9);
                color: white;
            """)
        else:
            layout.addWidget(avatar_label)
            layout.addWidget(content_widget)
            layout.addStretch(1)

        return message_widget

    def Send_Question(self, text, avatar="default_avatar.png", nickname="用户"):
        """发送问题"""
        widget = self.create_message_widget(True, avatar, nickname, text)
        self.chat_layout.addWidget(widget)
        self.scroll_to_bottom()
    def Send_Question_History(self, text, avatar="default_avatar.png", nickname="用户"):
        widget = self.create_message_widget(True, avatar, nickname, text)
        self.chat_layout.addWidget(widget)
        self.scroll_to_bottom()
        # QTime
        # 这个视频的主要内容是？
        Answer ="这个视频里的5个人在进行谈话，分别在多是分钟到00:03:45秒的时候，提及了一个关于“市政府游行的话题”的话题，其中年纪较大的男性估计是领导者，多次安排和指导部署具体实施方式和时间，大概是明天上午9点过开始行动"


        QtCore.QTimer.singleShot(3000, lambda :self.Send_Answer_Now(Answer))






    def Send_Answer(self, text, avatar="bot_avatar.png", nickname="AI助手"):
        """发送回答"""
        widget = self.create_message_widget(False, avatar, nickname, text)
        self.chat_layout.addWidget(widget)
        self.scroll_to_bottom()

    def Send_Answer_Now(self, text, avatar="bot_avatar.png", nickname="AI助手", typing_effect=True, delay=100):
        """发送回答（支持打字机效果）
        Args:
            text (str): 要显示的文本
            avatar (str): 头像路径
            nickname (str): 昵称
            typing_effect (bool): 是否启用打字机效果
            delay (int): 每个字符显示的延迟（毫秒）
        """
        # 先创建一个空白的消息控件
        self.answer_widget = self.create_message_widget(False, avatar, nickname, "")
        self.chat_layout.addWidget(self.answer_widget)

        # 更精确地找到消息内容的 QLabel
        # 方法1：通过对象名称查找（推荐）
        content_label = self.answer_widget.findChild(QtWidgets.QLabel, "content_label")

        # 方法2：通过布局结构查找（如果方法1不可行）
        if content_label is None:
            content_widget = self.answer_widget.layout().itemAt(1).widget()  # 获取内容widget
            content_label = content_widget.layout().itemAt(1).widget()  # 获取内容label

        if typing_effect:
            # 启用打字机效果
            self.typing_text = text
            self.typing_index = 0
            self.typing_timer = QtCore.QTimer()
            self.typing_timer.timeout.connect(lambda: self._update_typing_text(content_label))
            self.typing_timer.start(delay)  # 每 delay 毫秒显示一个字
        else:
            # 直接显示完整文本
            content_label.setText(text)
            self.scroll_to_bottom()
    def Send_Image(self, image_path, is_me=True, avatar="default_avatar.png", nickname="用户"):
        """发送图片"""
        widget = self.create_message_widget(is_me, avatar, nickname, image_path, is_image=True)
        self.chat_layout.addWidget(widget)
        self.scroll_to_bottom()

    def scroll_to_bottom(self):
        """滚动到底部"""
        QtCore.QTimer.singleShot(100, lambda: self.scroll_area.verticalScrollBar().setValue(
            self.scroll_area.verticalScrollBar().maximum()
        ))

    def _update_typing_text(self, label):
        """打字机效果（支持换行）"""
        if self.typing_index < len(self.typing_text):
            partial_text = self.typing_text[:self.typing_index + 1]
            # 替换换行符并保留HTML标签
            formatted_text = partial_text.replace('\n', '<br>')
            label.setText(formatted_text)
            self.typing_index += 1
            self.scroll_to_bottom()
        else:
            self.typing_timer.stop()

    # def Send_Question(self):
    #
    #     pass
    #
    #
    #
    #
    #
    # def Send_Answer(self):
    #     pass
    #
    #
    #

    def Set_Bottom(self):
        # 主容器
        bottom_widget = QtWidgets.QWidget()
        bottom_widget.setFixedHeight(80)
        bottom_widget.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 155);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)

        # 主布局（水平排列）
        main_layout = QtWidgets.QHBoxLayout(bottom_widget)
        main_layout.setContentsMargins(8, 8, 12, 8)
        main_layout.setSpacing(8)

        # 附件按钮
        self.attach_button = QtWidgets.QPushButton()
        self.attach_button.setIcon(qta.icon('mdi.paperclip', color='white'))
        self.attach_button.setFixedSize(40, 40)
        self.attach_button.setStyleSheet("""
            QPushButton {
                background: rgba(22, 175, 252, 0.8);
                border: none;
                border-radius: 20px;
            }
            QPushButton:hover {
                background: rgba(22, 175, 252, 1.0);
            }
            QPushButton:pressed {
                background: rgba(15, 150, 220, 1.0);
            }
        """)
        self.attach_button.setToolTip("添加附件")

        # 文本输入框
        self.message_input = QtWidgets.QTextEdit()
        self.message_input.setPlaceholderText("请提问...")
        self.message_input.setMaximumHeight(60)
        self.message_input.setStyleSheet("""
            QTextEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 3px;
                padding: 8px;
                font-size: 14px;
                color: #333;
            }
        """)

        # 安装事件过滤器来检测回车键
        self.message_input.installEventFilter(self)

        # 发送按钮
        self.send_button = QtWidgets.QPushButton()
        self.send_button.setIcon(qta.icon('mdi.send', color='white'))
        self.send_button.setFixedSize(40, 40)
        self.send_button.setStyleSheet("""
            QPushButton {
                background: rgba(22, 175, 252, 0.8);
                border: none;
                border-radius: 20px;
            }
            QPushButton:hover {
                background: rgba(22, 175, 252, 1.0);
            }
            QPushButton:pressed {
                background: rgba(15, 150, 220, 1.0);
            }
        """)
        self.send_button.setToolTip("发送消息")

        # 将控件添加到布局
        main_layout.addWidget(self.message_input, stretch=1)
        main_layout.addWidget(self.attach_button)
        main_layout.addWidget(self.send_button)

        # 连接信号槽
        self.send_button.clicked.connect(self._send_message)

        return bottom_widget

    def eventFilter(self, source, event):
        """事件过滤器，用于检测QTextEdit中的回车键"""
        if (source is self.message_input and
                event.type() == QtCore.QEvent.KeyPress and
                event.key() == QtCore.Qt.Key_Return and
                not event.modifiers() & QtCore.Qt.ShiftModifier):
            self._send_message()
            return True  # 表示已处理该事件

        return super().eventFilter(source, event)

    def _send_message(self):
        """发送消息处理函数"""
        message = self.message_input.toPlainText().strip()
        if message:
            self.Send_Question_History(message)
            self.message_input.clear()
