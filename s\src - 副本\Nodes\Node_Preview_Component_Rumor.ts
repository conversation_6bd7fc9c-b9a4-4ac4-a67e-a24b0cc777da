import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ButtonControl,Node_Socket,PreviewControl_Rumor} from "./Node_Controls";
















  export class Node_Preview_Component_Rumor extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    button_1:ButtonControl,
    Conent:PreviewControl_Rumor,
    

  }> 
  {
    width =480;
    height = 688;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new PreviewControl_Rumor(
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 
        '未知', 

       
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }
        updateContent(Intelligence:Record<string, any>){
          const contentControl = this.controls.Conent;
  
          contentControl.setContent(Intelligence)
          console.log('Intelligence:', Intelligence);
          
  
  
        }

      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    