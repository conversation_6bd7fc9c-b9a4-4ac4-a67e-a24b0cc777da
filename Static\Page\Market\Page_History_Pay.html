<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>哨兵导控</title>
    <!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
    <link rel="stylesheet" href="/static/CSS/bootstrap-fileinput/fileinput.min.css">
    <link rel="stylesheet" href="/static/CSS/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/CSS/animate/animate.min.css">
    <link rel="stylesheet" href="/static/CSS/jquery/fullcalendar/fullcalendar.min.css">
    
    <link rel="stylesheet" href="/static/CSS/daterangepicker/daterangepicker.min.css">

    <!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />

    <link href="/static/CSS/font-awesome/all.min.css" rel="stylesheet" />
    <!-- 加载 Select2 -->
    <link href="/static/CSS/select2/select2.min.css" rel="stylesheet" />
    
    <link rel="stylesheet" href="/static/CSS/jquery/jquery.scrollbar.css">
    <!--     dialog   -->
    <link rel="stylesheet" href="/static/CSS/trumbowyg/trumbowyg.min.css">
    <link rel="stylesheet" href="/static/CSS/sweetalert/sweetalert2.min.css">

    <link rel="stylesheet" href="/static/CSS/server_style.css">

    <!-- data-table CSS ============================================ -->
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-table.css">
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-editable.css">

    <!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
    <!-- App styles -->
    <link rel="stylesheet" href="/static/CSS/app/app.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />

    <style type="text/css">
        .table {
            table-layout: fixed;
            word-break: break-all;
        }
        .table-responsive select option {
            background-color: rgb(255, 255, 255) !important;
            /* 设置下拉框的背景颜色为绿色 */
            color: #333;
            /* 设置下拉框的文字颜色为白色 */
        }
        .nav-link {
            display: block;
            padding: 0rem 1.5rem;
        }
    </style>
    
</head>

<body class="bg-theme bg-theme1">

    <div class="wrapper">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>

        <div class="page-wrapper">
            <div class="page-content-wrapper">
                <div class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <div class="form-row">
                                <label for="diy_time_list" class="font-weight-bold d-flex align-items-center">查询日期范围：</label>
                                <div class="col-lg-3 col-md-12 my-md-3">
                                    <input type="text" id="diy_time_list" class="form-control" placeholder="点击选择日期范围">
                                </div>
                                <div class="col-lg-3 col-md-12 my-md-3">
                                    <button class="btn btn-primary mr-2" id="query_table">查询</button>
                                    <button class="btn btn-success" id="export_table">导出</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="table" class="table table-hover">

                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/daterangepicker/jquery.daterangepicker.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/fileinput.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/zh.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/theme.min.js"></script>
    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>
    <script src="/static/JavaScript/select2/select2.full.min.js"></script>
    <script src="/static/JavaScript/select2/zh-CN.js"></script>
    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>
    <!-- data-table  ============================================ -->
    <script src="/static/JavaScript/data-table/bootstrap-table.js"></script>
    <script src="/static/JavaScript/data-table/bootstrap-table-zh-CN.js"></script>

    <!-- App functions and dialog -->
    <script src="/static/JavaScript/App/clamp.js"></script>
    <script src="/static/JavaScript/App/trumbowyg.min.js"></script>

    <!-- App functions and notify -->
    <script src="/static/JavaScript/App/bootstrap-notify.min.js"></script>

    <!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>

    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <!-- <script src="/static/JavaScript/Config/Server_Tools/Server_Print.js"></script>
    <script src="/static/JavaScript/Config/Server_Tools/Server_Href.js"></script>
    <script src="/static/JavaScript/Config/Server_Function/Server_Data.js"></script>
    <script src="/static/JavaScript/Config/System_BaseConfig.js"></script> -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <script src="/static/JavaScript/clipboard/cilpboard.min.js"></script>
    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/App/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/App/dataTables.buttons.min.js"></script>
    <script src="/static/JavaScript/App/buttons.print.min.js"></script>
    <script src="/static/JavaScript/App/jszip.min.js"></script>
    <script src="/static/JavaScript/App/buttons.html5.min.js"></script>
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <script src="/static/Page/Market/Page_History_Pay.js"></script>
    <script src="/static/JavaScript/xlxs/xlxs.min.js"></script>
       
    <script>
		var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>
    <script>
        function Page_Init() {
            $('#diy_time_list').val(`${startOfMonth} 至 ${today}`)
            query_table();
        };

        window.onload = function () {
            Element_Sidebar_Header();
            Page_Init();
        };
    </script>

</body>

</html>