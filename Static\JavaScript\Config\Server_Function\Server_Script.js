console.log('Loading_Script_List')







function Loading_Script_List(){

    //加载AI控制
    const Script_BaseConfig = document.createElement('script');
    Script_BaseConfig.onload = Script_BaseConfig.onreadystateschange = function () {
    if (!this.readyState || this.readyState == 'loaded' || this.readyState == 'complete' // IE onreadystateschange
    ) {
            // 脚本加载完成后执行某些逻辑
            // IE支持onreadystateschange事件
            // FF支持onload事件
        }
    };
    Script_BaseConfig.src = '/static/JavaScript/Config/System_BaseConfig.js';
    document.body.appendChild(Script_BaseConfig);

    //加载图片控制
    const Script_Server_Data = document.createElement('script');
    Script_Server_Data.onload = Script_Server_Data.onreadystateschange = function () {
    if (!this.readyState || this.readyState == 'loaded' || this.readyState == 'complete' // IE onreadystateschange
    ) {
            // 脚本加载完成后执行某些逻辑
            // IE支持onreadystateschange事件
            // FF支持onload事件
        }
    };
    Script_Server_Data.src = '/static/JavaScript/Config/Server_Function/Server_Data.js';
    document.body.appendChild(Script_Server_Data);

    //加载情报控制函数
    const Script_Server_Intelligence = document.createElement('script');
    Script_Server_Intelligence.onload = Script_Server_Intelligence.onreadystateschange = function () {
    if (!this.readyState || this.readyState == 'loaded' || this.readyState == 'complete' // IE onreadystateschange
    ) {
            // 脚本加载完成后执行某些逻辑
            // IE支持onreadystateschange事件
            // FF支持onload事件
        }
    };
    Script_Server_Intelligence.src = '/static/JavaScript/Config/Server_Function/Server_Intelligence.js';
    document.body.appendChild(Script_Server_Intelligence);




}
function  Loading_Script_Source(){

    var loadingTime=5
    showLoading = function (loadText) {
        if(!loadText){
            $("#loadText").html(loadText)
        }
        $('#loadingModal').modal({backdrop: 'static', keyboard: false});
    }
    
    hideLoading = function () {
        setTimeout("$('#loadingModal').modal('hide')",500);
    }



}
  
function Service_Alert (Alert_Info){
    swal({
        title: Alert_Info['Title'],
        text: Alert_Info['Content'],
        type: Alert_Info['Type'],
        buttonsStyling: false,
        confirmButtonClass: 'btn btn-sm btn-light',
        background: 'rgba(0, 0, 0, 0.96)'
    })


}


Loading_Script_List()


Loading_Script_Source()







