# -*- mode: python ; coding: utf-8 -*-
import sys
import os
import site
import pkg_resources

block_cipher = None

# 项目根目录
project_root = r'D:\\Sentinel_Foundation'

# 获取编码路径
encoding_paths = []
possible_paths = [
    os.path.join(sys.prefix, 'Lib', 'encodings'),
    os.path.join(sys.base_prefix, 'Lib', 'encodings'),
    os.path.join(sys.executable.replace('python.exe', ''), 'Lib', 'encodings'),
]

for path in site.getsitepackages():
    possible_paths.append(os.path.join(path, 'encodings'))

for path in possible_paths:
    if os.path.exists(path):
        encoding_paths.append((path, 'encodings'))
        print(f"找到编码路径: {path}")
        break

# 构建数据文件列表
datas_list = encoding_paths.copy()

# 添加你的Bin模块和Utils模块
bin_path = os.path.join(project_root, 'Bin')
if os.path.exists(bin_path):
    for root, dirs, files in os.walk(bin_path):
        for file in files:
            if file.endswith(('.py', '.pyd', '.dll', '.so')):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_root)
                target_dir = os.path.dirname(rel_path)
                datas_list.append((file_path, target_dir))
                print(f"添加文件: {file_path} -> {target_dir}")
    pathex=[

print(f"总共添加了 {len(datas_list)} 个数据文件")

a = Analysis(
    ['Windows_Anlysis.py'],
        '.',
        project_root,
        os.path.join(project_root, 'Bin'),
        os.path.join(project_root, 'Bin', 'Utils'),
    ],
    binaries=[],
    datas=datas_list,
    hiddenimports=[
        # 编码相关
        'encodings',
        'encodings.aliases',
        'encodings.utf_8',
        'encodings.ascii',
        'encodings.latin_1',
        'encodings.cp1252',
        'encodings.mbcs',

        # 发现的模块
        'Bin',
        'Bin.Config',
        'Bin.Resource',
        'Bin.Resource.CSS',
        'Bin.Resource.CSS.UI_Icons',
        'Bin.Resource.Icons',
        'Bin.Resource.Image',
        'Bin.System',
        'Bin.System.OS',
        'Bin.System.OS.Component',
        'Bin.System.OS.Component.Component_AudioChat',
        'Bin.System.OS.Component.Component_AudioEQ',
        'Bin.System.OS.Component.Component_AudioNoise',
        'Bin.System.OS.Component.Component_AudioPlay',
        'Bin.System.OS.Component.Component_AudioTranslate',
        'Bin.System.OS.Component.Component_AudioWave',
        'Bin.System.OS.Component.Component_Common',
        'Bin.System.OS.Component.Component_Config',
        'Bin.System.OS.Component.Component_Login',
        'Bin.System.OS.Component.Component_Media_PopupDialog',
        'Bin.System.OS.Component.Component_PopupDialog',
        'Bin.System.OS.Component.Component_VideoPlay',
        'Bin.System.OS.Page',
        'Bin.System.OS.Page.Media',
        'Bin.System.OS.Page.Media.Page_Widget_Media_Center',
        'Bin.System.OS.Page.Media.Page_Widget_Media_Video',
        'Bin.System.OS.Page.Media.Page_Widget_Media_Voiceprint',
        'Bin.System.OS.Page.Media.Page_Widget_Voiceprint_Analysis',
        'Bin.System.OS.Page.Page_Utils.Utils_PopupDialog_Video',
        'Bin.System.OS.Page.System',
        'Bin.System.OS.Page.System.Page_Widget',
        'Bin.System.OS.Page.System.Page_Widget_Home',
        'Bin.System.OS.Page.System.Page_Widget_SentinelServer',
        'Bin.System.OS.Resource.CSS.UI_Icons',
        'Bin.System.OS.Utils',
        'Bin.System.OS.Utils.Utils_PopupDialog',
        'Bin.System.OS.Utils.Utils_PopupDialog_Media',
        'Bin.System.OS.Windows',
        'Bin.System.OS.Windows.Test',
        'Bin.System.OS.Windows.Test_倒谱图',
        'Bin.System.OS.Windows.Test_功率图',
        'Bin.System.OS.Windows.Windows_Anlysis',
        'Bin.System.OS.Windows.Windows_Anlysis_bak',
        'Bin.System.OS.Windows.Windows_Media_Center',
        'Bin.System.OS.Windows.Windows_Media_View',
        'Bin.System.OS.Windows.Windows_VoiceprintAnlysis',
        'Bin.System.OS.Windows.build',
        'Bin.System.OS.Windows.build.Windows_Anlysis',
        'Bin.System.OS.Windows.build.Windows_Anlysis.localpycs',
        'Bin.System.OS.Windows.dist',
        'Bin.System.Server',
        'Bin.System.Server.Server_Media',
        'Bin.System.Server.Server_Media.Server',
        'Bin.System.Server.Server_Media.Server.OS',
        'Bin.System.Server.Server_Media.Server.OS.Page',
        'Bin.System.Server.Server_Media.Server.OS.Page.Page_Widget_Voiceprint_Analysis',
        'Bin.System.Server.Server_Media.Server.OS.Test',
        'Bin.System.Server.Server_Media.Server.OS.Test_凸型波',
        'Bin.System.Server.Server_Media.Server.OS.Voiceprint',
        'Bin.System.Server.Server_Media.Server.OS.VoiceprintAnlysis',
        'Bin.System.Server.Server_Media.Server.OS.Windows',
        'Bin.System.Server.Server_Media.Server.OS.Windows.Windows_Media_View',
        'Bin.System.Web',
        'Bin.System.Web.nginx',
        'Bin.System.Web.nginx.conf',
        'Bin.System.Web.nginx.contrib',
        'Bin.System.Web.nginx.contrib.unicode2nginx',
        'Bin.System.Web.nginx.contrib.vim',
        'Bin.System.Web.nginx.contrib.vim.ftdetect',
        'Bin.System.Web.nginx.contrib.vim.ftplugin',
        'Bin.System.Web.nginx.contrib.vim.indent',
        'Bin.System.Web.nginx.contrib.vim.syntax',
        'Bin.System.Web.nginx.docs',
        'Bin.System.Web.nginx.html',
        'Bin.System.Web.nginx.html.build',
        'Bin.System.Web.nginx.html.build.Persion',
        'Bin.System.Web.nginx.html.build.static',
        'Bin.System.Web.nginx.html.build.static.css',
        'Bin.System.Web.nginx.html.build.static.js',
        'Bin.System.Web.nginx.html.build.static.media',
        'Bin.System.Web.nginx.logs',
        'Bin.System.Web.nginx.temp',
        'Bin.System.Web.nginx.temp.client_body_temp',
        'Bin.System.Web.nginx.temp.fastcgi_temp',
        'Bin.System.Web.nginx.temp.proxy_temp',
        'Bin.System.Web.nginx.temp.scgi_temp',
        'Bin.System.Web.nginx.temp.uwsgi_temp',
        'Bin.Utils',
        'Bin.Utils.Function',
        'Bin.Utils.Function.Function_Core_Config',
        'Bin.Utils.Function.Function_Core_Router',
        'Bin.Utils.Sentinel',
        'Bin.Utils.Sentinel.Core_Sentinel',
        'Bin.Utils.Sentinel.Utils',
        'Bin.Utils.Sentinel.Utils.Element',
        'Bin.Utils.Sentinel.Utils.Server_Sentinel',
        'Bin.Utils.Sentinel.Utils.Service_Html',
        'Bin.Utils.Sentinel.Utils.Service_Html.Service_Html_Element',
        'Bin.Utils.Service_CacheStack',
        'Bin.Utils.Service_Cipher',
        'Bin.Utils.Service_Excel',
        'Bin.Utils.Service_File',
        'Bin.Utils.Service_Html_Element',
        'Bin.Utils.Service_Logging',
        'Bin.Utils.Service_Message',
        'Bin.Utils.Service_Port',
        'Bin.Utils.Service_Print',
        'Bin.Utils.Service_Progress',
        'Bin.Utils.Service_Redis',
        'Bin.Utils.Service_Sqlite',
        'Bin.Utils.Service_Status',
        'Bin.Utils.Service_System',
        'Bin.Utils.Service_Table',
        'Bin.Utils.Toolkit',
        'Bin.Utils.Toolkit.Module',
        'Bin.Utils.Toolkit.Module.Module_Print',
        'Bin.Utils.Toolkit.Module.Module_Python',
        'Bin.Utils.Toolkit.Module.Module_Python.python',
        'Bin.Utils.Toolkit.Module.Module_Table',
        'Bin.Utils.Toolkit.Module.Module_Table.Table_SQLite',
        'Bin.Utils.Toolkit.Service_CacheStack',
        'Bin.Utils.Toolkit.Service_ChatAI',
        'Bin.Utils.Toolkit.Service_Cipher',
        'Bin.Utils.Toolkit.Service_Config',
        'Bin.Utils.Toolkit.Service_Excel',
        'Bin.Utils.Toolkit.Service_File',
        'Bin.Utils.Toolkit.Service_Image',
        'Bin.Utils.Toolkit.Service_Logging',
        'Bin.Utils.Toolkit.Service_Message',
        'Bin.Utils.Toolkit.Service_Port',
        'Bin.Utils.Toolkit.Service_Print',
        'Bin.Utils.Toolkit.Service_Progress',
        'Bin.Utils.Toolkit.Service_Redis',
        'Bin.Utils.Toolkit.Service_Script_Install',
        'Bin.Utils.Toolkit.Service_Sqlite',
        'Bin.Utils.Toolkit.Service_Status',
        'Bin.Utils.Toolkit.Service_Synchronization',
        'Bin.Utils.Toolkit.Service_System',
        'Bin.Utils.Toolkit.Service_Table',
        'Bin.Utils.Toolkit.Service_Xml',
        'Bin.Utils.UtilsCenter',
        'Component_Common',
        'Component_VideoPlay',
        'Core_Sentinel',
        'Function_Core_Config',
        'Function_Core_Router',
        'Sentinel',
        'Server',
        'Service_CacheStack',
        'Service_Cipher',
        'Service_Excel',
        'Service_File',
        'Service_Html_Element',
        'Service_Logging',
        'Service_Message',
        'Service_Port',
        'Service_Print',
        'Service_Progress',
        'Service_Redis',
        'Service_Sqlite',
        'Service_Status',
        'Service_System',
        'Service_Table',
        
        # 加密相关模块
        'gmssl',
        'gmssl.sm2',
        'gmssl.sm3',
        'gmssl.sm4',
        'gmssl.func',
        'gmssl.sm9',
        'cryptography',
        'cryptography.hazmat',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.backends',
        'cryptography.hazmat.backends.openssl',

        # PySide6相关
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'PySide6.QtXml',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
    cipher=block_cipher,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Windows_Anlysis',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Logo.ico',
)
