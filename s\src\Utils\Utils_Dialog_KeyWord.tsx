import React, { useEffect,useState,useRef } from 'react';
import styles from '../Styles/Utils_Common.module.css';
import { Button, Progress ,Switch,Input,Image,Table,Tabs, Divider,Tag, Transfer,Flex,FlexProps,Segmented ,Tooltip,theme} from "antd";
import { ArrowUpOutlined,LoadingOutlined,CloudUploadOutlined} from '@ant-design/icons';
// import {CoreControl} from "../core/CoreControl"
import { FloatButton } from 'antd';
import type { 
    ColumnsType,
    TablePaginationConfig,
    // TableProps 
  } from 'antd/es/table';
// import type { GetProp, TableColumnsType, TableProps, TransferProps } from 'antd';

import type { GetProp, TableColumnsType, TableProps, TransferProps,InputRef } from 'antd';

import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import { useAuth } from '@/Core/Core_AuthContent';

// import {CoreControl} from "../Core/CoreControl"
import { AlignLeftOutlined ,BarsOutlined ,PlusOutlined,MinusOutlined,FullscreenOutlined,EyeOutlined ,ExpandOutlined  ,AimOutlined,CloseOutlined} from '@ant-design/icons';
interface Props_Dialog {
  isOpen: boolean; // 接收 isOpen 状态
  Toggle_Dialog_KeyWord: () => void; // 接收 toggleDialog 函数
}
interface DataType {
    key: React.Key;
    name: string;
    age: number;
    status: string;
  }

  type TransferItem = GetProp<TransferProps, 'dataSource'>[number];
  type TableRowSelection<T extends object> = TableProps<T>['rowSelection'];

  





































  interface TableTransferProps extends TransferProps<TransferItem> {
      dataSource: DataType[];
      leftColumns: TableColumnsType<DataType>;
      rightColumns: TableColumnsType<DataType>;
    }
    // 模拟数据生成
const mockData = Array.from({ length: 20 }).map<DataType>((_, i) => ({
    key: i.toString(),
    name: ` ${i + 1}`,
    age: Math.floor(Math.random() * 30 + 20),
    status: ['111', '222', '333'][i % 3]
  }));
  
    const columns: TableColumnsType<DataType> = [
        {
          title: 'KEY',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '热度',
          dataIndex: 'age',
          key: 'age',
          sorter: (a, b) => a.age - b.age,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          render: (status: string) => (
            <Tag 
            style={{width:50,height:20}}
            color={status === '111' ? 'green' : status === '222' ? 'orange' : 'red'}>
              {status.toUpperCase()}
            </Tag>
          ),
        },
      ];
      








      









      const TableTransfer: React.FC<TableTransferProps> = ({
        leftColumns,
        rightColumns,
        ...restProps
      }) => {
        return (
          <Transfer {...restProps}>
            {({ 
              direction,
              filteredItems,
              onItemSelect,
              onItemSelectAll,
              selectedKeys: listSelectedKeys,
              disabled: listDisabled
            }) => {
              const currentColumns = direction === 'left' ? leftColumns : rightColumns;
              
              const rowSelection: TableRowSelection<TransferItem> = {
                getCheckboxProps: () => ({ disabled: listDisabled }),
                onSelectAll: (selected, selectedRows) => {
                  const keys = selectedRows.map((row) => row.key);
                  onItemSelectAll(keys, selected);
                },
                selectedRowKeys: listSelectedKeys,
                selections: [
                  Table.SELECTION_ALL,
                  Table.SELECTION_INVERT,
                  Table.SELECTION_NONE,
                ],
              };
      
              return (
                <Table
                  rowSelection={rowSelection}
                  columns={currentColumns}
                  dataSource={filteredItems}
                  size="small"
                  style={{ pointerEvents: listDisabled ? 'none' : undefined }}
                  onRow={({ key, disabled: itemDisabled }) => ({
                    onClick: () => {
                      if (itemDisabled || listDisabled) return;
                      onItemSelect(key, !listSelectedKeys.includes(key));
                    },
                  })}
                />
              );
            }}
          </Transfer>
        );
      };



      





//     return (

//     )

    
  



//   }




      const boxStyle: React.CSSProperties = {
        margin:"60px -0px",
        width: '98%',
        height: "550px",
        borderRadius: 6,
        border: '1px solid #40a9ff',
      };




      const alignOptions = ['flex-start', 'center', 'flex-end'];

      const justifyOptions = [
        'flex-start',
        'center',
        'flex-end',
        'space-between',
        'space-around',
        'space-evenly',
      ];



const Utils_Dialog_KeyWord: React.FC<Props_Dialog> = ({ 
        isOpen, 
        Toggle_Dialog_KeyWord 
      }) => {
        // const { user } = SetContext(); // 获取用户数据
        const { user  } = useAuth();
        const [targetKeys, setTargetKeys] = useState<TransferProps['targetKeys']>([]);
        const [disabled, setDisabled] = useState(false);
      
        const handleChange: TableTransferProps['onChange'] = (
          nextTargetKeys,
          direction,
          moveKeys
        ) => {
          setTargetKeys(nextTargetKeys);
          console.log('移动方向:', direction);
          console.log('移动的键:', moveKeys);
        };
      
        const toggleDisabled = (checked: boolean) => {
          setDisabled(checked);
        };



        var DynamicTabContent =  <Flex vertical gap="middle" className={styles.transferContainer}>
          <div>
              <h1>Welcome, {user?.username}!</h1>
              <p>Age: {user?.username}</p>
          </div>
        <TableTransfer
          style={{width:"100%",height:"500px",padding:"10px"}}
          dataSource={mockData}
          targetKeys={targetKeys}
          disabled={disabled}
          onChange={handleChange}
          leftColumns={columns}
          rightColumns={columns}

          
     

          filterOption={(inputValue, item) =>
            item.name.includes(inputValue) || item.status.includes(inputValue) 
            
          }




          
        />






  
        <Flex gap="small" align="center">
          <Switch
        //   style={{ height: "33px", width: "133px",bottom: 18,left: 10,position: 'absolute',}}
            checkedChildren="禁用操作"
            unCheckedChildren="启用操作"
            checked={disabled}
            onChange={toggleDisabled}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => console.log('添加新关键词')}
          >
            添加关键词
          </Button>
        </Flex>
      </Flex>
  
  



   const [justify, setJustify] = React.useState<FlexProps['justify']>(justifyOptions[0]);
   const [alignItems, setAlignItems] = React.useState<FlexProps['align']>(alignOptions[0]);


    const operations = <Button>Extra Action</Button>;
    // const items = Array.from({ length: 3 }).map((_, i) => {
    //     const id = String(i + 1);
    //     return {
    //       label: `Tab ${id}`,
    //       key: id,
    //       children: `Content of tab ${id}`,
    //     };
    //   });





      const Items_Tabs =[

        {"key":"1","label":"今日热词","children":(<Flex justify="center" align="start"><Component_Keys /></Flex>),},
        {"key":"2","label":"近期热词","children":"近期热词",},
        {"key":"3","label":"关键词","children":DynamicTabContent,},
        {"key":"4","label":"停用词","children":"停用词","cardBg":"rgba(0,255,0,1)",        "itemColor":"rgba(0,255,0,1)"},
        {"key":"5","label":"热词","children":"当前热词4","cardBg":"rgba(0,255,0,1)",        "itemColor":"rgba(0,255,0,1)"},
      ]






      
        return (
          <div className={`${styles.Dialog_Common_Langer} ${isOpen ? styles.Open : ''}`} style={{ display: isOpen ? 'block' : 'none' }}>

            <div style={{ height: "33px", width: "33px",top: 8,right: 10,position: 'absolute',zIndex:99999,backgroundColor:"transparent",}} ><CloseOutlined  onClick={Toggle_Dialog_KeyWord}  style={{margin: "4px", color: 'white', fontSize:18 }} /> </div> 
            <Flex gap="middle" align="center" vertical>
                        {/* <p>Select justify :</p>
                        <Segmented options={justifyOptions} onChange={setJustify} /> */}
                        {/* <p>Select align :</p>
                        <Segmented options={alignOptions} onChange={setAlignItems} /> */}
                            <Flex style={boxStyle} justify={"center"} align={"start"}>
                               
                            <Tabs 
                                // cardBg={"white"}
                         
                                size="large"
                                type="card"
                                tabBarStyle={{        // 文字颜色
                                    color: "red",
                                    fontSize: 36,
                                    background:"red",
                                    ['--active-color' as any]: 'white', // 激活颜色
                                    ['--hover-color' as any]: '#e6f7ff' // 悬停颜色
                                                        
                    
                                  
                                
                                }}
                               
                                // style={{width:"90%",color:"white"}}
                                style={{
                                    width:"90%",
                                    color:"white",
                                    ['--active-color' as any]: '#1890ff' // CSS 变量方式
                                  }}
                                // tabBarExtraContent={operations} 
                                items={Items_Tabs} 
                                
                                
                                />
                                
                            
                            </Flex>
                        </Flex>

        


          </div>
        );
      };
      
      export default Utils_Dialog_KeyWord;

// const Utils_Dialog_KeyWord: React.FC<Props_Dialog> = ({ isOpen, Toggle_Dialog_KeyWord, }) => {



//     const [targetKeys, setTargetKeys] = useState<TransferProps['targetKeys']>([]);
//     const [disabled, setDisabled] = useState(false);
//     // 类型明确的回调函数
//     const onChange: TableTransferProps['onChange'] = (nextTargetKeys, direction, moveKeys) => {
//         setTargetKeys(nextTargetKeys);
//         console.log('Moved keys:', moveKeys);
//         console.log('Direction:', direction);
//     };
    
//     const toggleDisabled = (checked: boolean) => {
//       setDisabled(checked);
//     };













//     const __CoreControl = new CoreControl()
//     const operations = <Button>Extra Action</Button>;
//     // const items = Array.from({ length: 3 }).map((_, i) => {
//     //     const id = String(i + 1);
//     //     return {
//     //       label: `Tab ${id}`,
//     //       key: id,
//     //       children: `Content of tab ${id}`,
//     //     };
//     //   });


//         // 独立组件
//         const DynamicTabContent =
//             <div className="dynamic-tab">
//             <h3 style={{color:"white"}}>动态内容 - 标签页 {}</h3>
//             <ul>
//                 {Array.from({ length: 3 }).map((_, i) => (
//                 <li key={i}>项目 {i + 1}</li>
//                 ))}
//             </ul>
//             </div>
        
      





















//       const items =[

//         {"key":"1","label":"今日热词","children":"今日热词",},
//         {"key":"2","label":"近期热词","children":"近期热词",},
//         {"key":"3","label":"关键词","children":DynamicTabContent,},
//         {"key":"4","label":"停用词","children":"停用词","cardBg":"rgba(0,255,0,1)",        "itemColor":"rgba(0,255,0,1)"},
//         {"key":"5","label":"热词","children":"当前热词4","cardBg":"rgba(0,255,0,1)",        "itemColor":"rgba(0,255,0,1)"},
//       ]



//       const dataSource: DataType[] = [
//         { key: '1', name: 'John Brown', age: 32, status: 'Active' },
//         { key: '2', name: 'Jim Green', age: 42, status: 'Inactive' },
//         { key: '3', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '4', name: 'John Brown', age: 32, status: 'Active' },
//         { key: '5', name: 'Jim Green', age: 42, status: 'Inactive' },
//         { key: '6', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '7', name: 'Jim Green', age: 42, status: 'Inactive' },
//         { key: '8', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '9', name: 'Jim Green', age: 42, status: 'Inactive' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//         { key: '10', name: 'Joe Black', age: 32, status: 'Active' },
//       ];
    
//       const columns: ColumnsType<DataType> = [
//         {
//           title: 'ID',
//           dataIndex: 'name',
//           key: 'name',
//         },
//         {
//           title: '热词',
//           dataIndex: 'age',
//           key: 'age',
//         },
//         {
//           title: 'Status',
//           dataIndex: 'status',
//           key: 'status',
//         },
//         {
//             title: '操作',
//             key: 'action',
//             render: () => (
//               <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
//                 <Button type="primary" size="small" style={{ backgroundColor: 'rgba(123, 142, 220, 0.8)', borderColor: 'rgba(0,0,0,0.0)' }}>
//                   删除
//                 </Button>
//               </div>
//             ),
//           },
//       ];
//       const rowSelection: TableProps<DataType>['rowSelection'] = {
//         onChange: (selectedKeys) => {
//         //   setSelectedRowKeys(selectedKeys);
//         },
//       };
      




//       const Init_Table_Data = () => {
//         console.log("Init_Table_Data");
//       let RequestsData={
//         'user_id': '',
//         'user_token': '',
//         'data_class': 'Score',
//         'data_type': 'Service_Config',
//         'data_methods': 'return_hotopic_keyword_list',
//         'data_argument': "{}",
//         'data_kwargs': "{}",
//       };

      
//     //     
//       (async () => {
//         let Response_Data = await __CoreControl.Update_Tree(RequestsData);
        
//         console.log("Response Data:", Response_Data);
//         try {
//           // @ts-ignore - 忽略下一行的类型检查

        
//         } catch (error) {
              
//         }


       
      
    

//             })();
//         }


//     // 使用 useEffect 钩子在组件挂载时执行
//     useEffect(() => {
//         // Init_Table_Data();
//     }, []); // 空依赖数组确保只在组件挂载时执行一次
        

    



//   return (
//     <div className={`${styles.Dialog_Common_Langer} ${isOpen ? styles.Open : ''}`}>
//       <div className={styles.Dialog_Content}>
//         <p>这是一个对话框</p>
//         {/* <button onClick={toggleDialog}>关闭</button> */}


//         <div style={{ height: "33px", width: "33px",top: 8,right: 10,position: 'absolute',zIndex:99999,backgroundColor:"transparent",}} >
//             <CloseOutlined  onClick={Toggle_Dialog_KeyWord}  style={{margin: "4px", color: 'white', fontSize:18 }} />

//         </div>

//         <Tabs 
//         tabBarStyle={{        // 文字颜色
//             color: 'white',
//             // size:"large",
//         }}
      
//         tabBarExtraContent={operations} items={items} />
      





//       </div>
//     </div>
//   );
// };

// export default Utils_Dialog_KeyWord;

{/* <Table
rowSelection={rowSelection}
columns={columns}
dataSource={dataSource}
bordered
style={{ background:"red",width: '100%',height: '100%'  }}
scroll={{ y:400 }} // 设置表格的滚动高度
pagination={{
    pageSize: 10, // 每页显示 10 行
  }} */}
// rowClassName={rowClassName}
// />



{/* {Array.from({ length: 10 }).map((_, index) => (
//  {Array_Data_Setting.map((item, index) => (
<div
// key={item.ID}
key={index}
style={{
  height: 30,
  width: '96%',
  backgroundColor: `hsl(${index * 12}, 70%, 60%)`, // 使用 HSL 颜色模式生成不同颜色
//   backgroundColor: `rgba(255,255,255,0.3)`, // 使用 HSL 颜色模式生成不同颜色
  margin: '5px auto',
  borderRadius: '4px',
  // border: '1px solid #ddd',
}}
>
  {/* <Button  onClick={Toggle_Dialog_KeyWord}   type="primary"  size="small"  style={{ marginLeft:5,marginTop:2,width: 50 ,height: 25 ,backgroundColor: 'rgba(123, 142, 220, 0.8)',borderColor: 'rgba(0,0,0,0.0)', }} >{item.NAME}</Button> */}
  {/* <Button                           type="primary"  size="small"  style={{ marginLeft:5,marginTop:2,width: "75%" ,height: 25 ,backgroundColor: 'rgba(123, 142, 220, 0.8)',borderColor: 'rgba(0,0,0,0.0)', }} >{item.CONTENT}</Button> */}
{/* //</div> */}
{/* ))} */}
{/* */}

