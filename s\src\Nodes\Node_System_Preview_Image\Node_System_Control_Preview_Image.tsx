import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "../Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "../Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");

export class SystemControl_Preview_Image extends ClassicPreset.Control {
    constructor(
      public Title: string, 

      public Percent: number,
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Object: Record<string, any>) {
      const safeGet = (key: string) => Object?.[key] || "未知";

      const safeParseInt = (value: any): number => {
        if (value === null || value === undefined) {
          return 0;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? 0 : parsed;
      };



      this.Title         = safeGet("Title");
      this.Percent       = safeParseInt(safeGet("Percent"));
   
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
}

export  function Node_System_Preview_Image(props: { data:SystemControl_Preview_Image }) {
  

  return (

      <Flex  justify={"center"} align={"center"} style={{width:"100%"}}>
         <Input style={{width: "480px",height: "390px",background: "transparent",}}/>
      </Flex>


    );

};
