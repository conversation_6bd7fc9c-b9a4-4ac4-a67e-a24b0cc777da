

import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import {Node_Socket,ModelControl_Rumor_Community} from "./Node_Model_Control_Rumor_Community";


export class Node_Model_Component_Rumor_Community extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    Conent:ModelControl_Rumor_Community,
    

  }> 
  {
    width =480;
    height = 428;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new ModelControl_Rumor_Community(
        '', // Label for the text area
        '抖音', // Initial value
        '微信', // Initial value
        '微博', // Initial value
        '\n', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }

      updateContent(Intelligence:Record<string, any>){
        const contentControl = this.controls.Conent;

        contentControl.setContent(Intelligence)
        console.log('Intelligence:', Intelligence);
        


      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    