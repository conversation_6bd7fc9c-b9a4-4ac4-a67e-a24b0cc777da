
import sys
import numpy as np
import librosa
import pyaudio
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QSlider, QLabel, QPushButton
from PySide6.QtCore import Qt, QThread, Signal
from PySide6 import Qt<PERSON><PERSON>, QtGui, QtWidgets

class Component_AudioEQ(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, *args, parent=None):
        super().__init__()
        self.initUI()

    def initUI(self):
        __QVBoxLayout = QtWidgets.QHBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(0)
        __Slider_EQ_1=Slider_EQ()
        __Slider_EQ_2=Slider_EQ()
        __Slider_EQ_3=Slider_EQ()
        __Slider_EQ_4=Slider_EQ()
        __Slider_EQ_5=Slider_EQ()
        __Slider_EQ_6=Slider_EQ()
        __Slider_EQ_7=Slider_EQ()
        __Slider_EQ_8=Slider_EQ()

        __QVBoxLayout.addWidget(__Slider_EQ_1)
        __QVBoxLayout.addWidget(__Slider_EQ_2)
        __QVBoxLayout.addWidget(__Slider_EQ_3)
        __QVBoxLayout.addWidget(__Slider_EQ_4)
        __QVBoxLayout.addWidget(__Slider_EQ_5)
        __QVBoxLayout.addWidget(__Slider_EQ_6)
        __QVBoxLayout.addWidget(__Slider_EQ_7)
        __QVBoxLayout.addWidget(__Slider_EQ_8)









class Slider_EQ(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        # 创建 QVBoxLayout
        # self.showMinimized(30,200)
        # self.showMinimized(30,200)
        layout = QtWidgets.QVBoxLayout(self)

        # 创建 QSlider
        self.slider = QtWidgets.QSlider(Qt.Vertical)  # 设置为垂直方向
        self.slider.setMinimum(0)  # 最小值
        self.slider.setMaximum(10)  # 最大值
        self.slider.setValue(5)  # 初始值
        self.slider.setTickPosition(QtWidgets.QSlider.TicksBothSides)  # 显示刻度
        self.slider.setTickInterval(1)  # 刻度间隔
        self.slider.valueChanged.connect(self.on_value_changed)  # 连接信号槽

        # 创建 QLabel 用于显示当前值
        # self.label = QtWidgets.QLabel("50", self)
        # self.label.setAlignment(Qt.AlignCenter)

        # 将 QSlider 和 QLabel 添加到布局
        layout.addWidget(self.slider)
        # layout.addWidget(self.label)

        # 设置窗口属性
        # self.setWindowTitle("Frequency Control")
        # self.resize(100, 400)

    def on_value_changed(self, value):
        # 根据 QSlider 的值更新 QLabel 的文本
        # self.label.setText(str(value))
        self.update()  # 触发重绘

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # # 绘制刻度和数字
        # painter.setPen(QtGui.QPen(Qt.white, 1))
        # font = QtGui.QFont()
        # font.setPointSize(8)
        # painter.setFont(font)
        #
        # slider_rect = self.slider.geometry()
        # slider_height = slider_rect.height()
        # slider_width = slider_rect.width()
        # tick_interval = self.slider.tickInterval()
        # tick_count = (self.slider.maximum() - self.slider.minimum()) // tick_interval + 1
        #
        # for i in range(tick_count):
        #     value = self.slider.minimum() + i * tick_interval
        #     y = slider_height - (value - self.slider.minimum()) * (slider_height / (self.slider.maximum() - self.slider.minimum()))
        #     painter.drawLine(slider_width + 0.5, y, slider_width + 1, y)  # 绘制刻度线
        #     painter.drawText(slider_width + 1.5, y + 0.5, str(value))  # 绘制数字







if __name__ == "__main__":
   pass