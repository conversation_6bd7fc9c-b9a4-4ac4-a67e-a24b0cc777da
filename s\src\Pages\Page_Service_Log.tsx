import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Select, 
  Space, 
  message,
  Tag,
  Typography
} from 'antd';
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Service_Log.css';

const { Option } = Select;
const { Text } = Typography;

// 数据类型定义
interface ServiceLogData {
  id: string;
  XID: string;                    // 日志时间
  MARKET_TYPE: string;            // 日志类型
  MARKET_SERVER: string;          // 服务内容
  MARKET_DEAL_INFO: string;       // 交易信息
}

interface DealInfo {
  last: number;      // 服务前
  consume: number;   // 当次消费
  update: number;    // 结余
}

interface ServerInfo {
  NAME: string;      // 服务名称
}

const Page_Service_Log: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [logData, setLogData] = useState<ServiceLogData[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('all');

  // 初始化数据
  useEffect(() => {
    loadLogData();
  }, []);

  // 加载日志数据
  const loadLogData = async (timeRange: string = 'all') => {
    setLoading(true);
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'User',
      //   data_type: 'Service',
      //   data_methods: 'personal_history_service',
      //   data_argument: '{}',
      //   data_kwargs: { User_History_Date: timeRange }
      // };
      // const response = await serviceRequests.Async(requestData);
      // setLogData(response || []);

      // 模拟数据
      const mockData: ServiceLogData[] = [
        {
          id: '1',
          XID: '2024-01-15 10:30:00',
          MARKET_TYPE: '账号采购',
          MARKET_SERVER: '[{"NAME":"Facebook账号"}]',
          MARKET_DEAL_INFO: '{"last":1000,"consume":50,"update":950}'
        },
        {
          id: '2',
          XID: '2024-01-14 14:20:00',
          MARKET_TYPE: '网络反制',
          MARKET_SERVER: '[{"NAME":"Twitter点赞"},{"NAME":"YouTube观看"}]',
          MARKET_DEAL_INFO: '{"last":950,"consume":100,"update":850}'
        },
        {
          id: '3',
          XID: '2024-01-13 09:15:00',
          MARKET_TYPE: '流量卡采购',
          MARKET_SERVER: '[{"NAME":"移动流量卡"}]',
          MARKET_DEAL_INFO: '{"last":850,"consume":30,"update":820}'
        }
      ];
      setLogData(mockData);
    } catch (error) {
      message.error('加载日志数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setSelectedTimeRange(value);
  };

  // 查询按钮点击
  const handleQuery = () => {
    loadLogData(selectedTimeRange);
  };

  // 导出CSV
  const handleExport = () => {
    try {
      // 准备导出数据
      const headers = ['序号', '日志时间', '日志类型', '服务内容', '服务前', '当次消费', '结余'];
      const exportData = logData.map((item, index) => {
        const serverInfo = parseServerInfo(item.MARKET_SERVER);
        const dealInfo = parseDealInfo(item.MARKET_DEAL_INFO);

        return [
          index + 1,
          item.XID,
          item.MARKET_TYPE,
          serverInfo,
          dealInfo.last,
          dealInfo.consume,
          dealInfo.update
        ];
      });

      // 创建CSV内容
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => row.join(','))
      ].join('\n');

      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const csvWithBOM = BOM + csvContent;

      // 创建下载链接
      const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // 生成文件名
      const date = new Date();
      const filename = `服务日志_${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}.csv`;

      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 解析服务信息
  const parseServerInfo = (serverStr: string): string => {
    try {
      const servers: ServerInfo[] = JSON.parse(serverStr);
      return servers.map(s => `【${s.NAME}】`).join('');
    } catch {
      return serverStr;
    }
  };

  // 解析交易信息
  const parseDealInfo = (dealStr: string): DealInfo => {
    try {
      return JSON.parse(dealStr);
    } catch {
      return { last: 0, consume: 0, update: 0 };
    }
  };

  // 获取日志类型标签颜色
  const getLogTypeColor = (type: string): string => {
    switch (type) {
      case '账号采购': return 'blue';
      case '网络反制': return 'green';
      case '流量卡采购': return 'orange';
      case '充值': return 'purple';
      case '退款': return 'red';
      default: return 'default';
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '日志时间',
      dataIndex: 'XID',
      key: 'XID',
      width: 160,
    },
    {
      title: '日志类型',
      dataIndex: 'MARKET_TYPE',
      key: 'MARKET_TYPE',
      width: 120,
      render: (type: string) => (
        <Tag color={getLogTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '服务内容',
      dataIndex: 'MARKET_SERVER',
      key: 'MARKET_SERVER',
      render: (serverStr: string) => (
        <Text>{parseServerInfo(serverStr)}</Text>
      ),
    },
    {
      title: '服务前',
      dataIndex: 'MARKET_DEAL_INFO',
      key: 'last',
      width: 100,
      render: (dealStr: string) => {
        const dealInfo = parseDealInfo(dealStr);
        return <Text>{dealInfo.last}</Text>;
      },
    },
    {
      title: '当次消费',
      dataIndex: 'MARKET_DEAL_INFO',
      key: 'consume',
      width: 100,
      render: (dealStr: string) => {
        const dealInfo = parseDealInfo(dealStr);
        return <Text style={{ color: '#ff4d4f' }}>-{dealInfo.consume}</Text>;
      },
    },
    {
      title: '结余',
      dataIndex: 'MARKET_DEAL_INFO',
      key: 'update',
      width: 100,
      render: (dealStr: string) => {
        const dealInfo = parseDealInfo(dealStr);
        return <Text style={{ color: '#52c41a' }}>{dealInfo.update}</Text>;
      },
    },
  ];

  return (
    <div className="service-log-page">
      {/* 查询条件 */}
      <Card style={{ marginBottom: 16 }}>
        <h6 style={{ marginBottom: 16 }}>查询条件：</h6>
        <Space wrap>
          <Select
            value={selectedTimeRange}
            onChange={handleTimeRangeChange}
            style={{ width: 120 }}
          >
            <Option value="week">一周</Option>
            <Option value="month">本月</Option>
            <Option value="year">本年</Option>
            <Option value="all">所有</Option>
          </Select>
          
          <Button 
            type="primary" 
            icon={<SearchOutlined />}
            onClick={handleQuery}
          >
            查询
          </Button>
          
          <Button 
            type="default" 
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出
          </Button>
        </Space>
      </Card>

      {/* 日志列表 */}
      <div className="table-responsive">
        <Table
          columns={columns}
          dataSource={logData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1000 }}
        />
      </div>
    </div>
  );
};

export default Page_Service_Log;
