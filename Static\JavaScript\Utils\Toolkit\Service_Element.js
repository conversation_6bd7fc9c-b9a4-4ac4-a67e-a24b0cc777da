
class Service_Element {
    constructor(Type,Data){
        this.Type = Type
        this.Data = Data

    }
    callMethod(){
        this.CONFIG()
        try {
            return eval(`this.${this.Type}()`);
        } catch (Error) {
            return {'Error': Error, "Type": this.Type, "Data": this.Data, "Status": "Failed"}
        }
    }
    

    Element_Sidebar_Header(){
        let Requests_Data = {
            "user_id": "CSC",
            "user_token":"this.Data.User_Token" ,
            "data_class": "Sentinel",
            "data_type": 'Service_Html',
            "data_methods": "return_html_config_custom",
            "data_argument": `{}`,
            "data_kwargs":`{"Source_UUID":"11111"}`,
        }
        // import { Service_Requests } from './Service_Requests.js';
        __Service_Requests = new Service_Requests("Asyn",Requests_Data)
        this.Result  = __Service_Requests.callMethod()
        console.log("Result:",this.Result);
        return this.Result


    }

    CONFIG(){
        this.Result = {'Status': 'Failed'}

    }
}