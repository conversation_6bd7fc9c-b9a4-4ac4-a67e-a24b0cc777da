/* 搜索展示页面样式 */
.search-display-page {
  padding: 24px;
  min-height: 100vh;
}

/* 搜索卡片 */
.search-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 搜索头部 */
.search-header {
  text-align: center;
  padding: 40px 0 20px;
}

.search-title {
  font-size: 48px !important;
  font-weight: 300;
  color: #262626;
  margin: 0 !important;
  letter-spacing: 2px;
}

/* 搜索输入区域 */
.search-input-container {
  padding: 0 40px 40px;
  margin-bottom: 20px;
}

.search-input {
  border-radius: 50px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input .ant-input {
  border-radius: 50px 0 0 50px !important;
  border: 2px solid #e8e8e8;
  padding: 12px 24px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.search-input .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-input .ant-input-search-button {
  border-radius: 0 50px 50px 0 !important;
  border: 2px solid #1890ff;
  background: #1890ff;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 500;
  height: 52px;
  transition: all 0.3s ease;
}

.search-input .ant-input-search-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 搜索结果区域 */
.search-results-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
  margin-top: 24px;
}

/* 结果工具栏 */
.results-toolbar {
  margin-bottom: 24px;
  padding: 16px 24px;
  border-radius: 8px;
  align-items: center;
}

.results-toolbar .ant-checkbox-wrapper {
  margin-right: 16px;
  font-weight: 500;
}

.results-actions {
  text-align: right;
}

.results-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 搜索结果列表 */
.search-results-list {
  margin-bottom: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.search-result-item {
  padding: 20px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
  transition: all 0.3s ease;
}

.search-result-item:hover {
  background: #fafafa;
}

.search-result-item:last-child {
  border-bottom: none !important;
}

.result-content {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  text-decoration: none;
  flex: 1;
  margin-right: 16px;
  line-height: 1.4;
}

.result-title:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.result-meta {
  flex-shrink: 0;
  align-items: center;
}

.emotion-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.emotion-tag.positive {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.emotion-tag.negative {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.emotion-tag.neutral {
  background: #f0f0f0;
  color: #666;
  border: 1px solid #d9d9d9;
}

.result-body {
  line-height: 1.6;
  color: #666;
  font-size: 14px;
}

/* 搜索结果美化 */
.search-results-list {
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.search-result-item {
  background: transparent !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 12px rgba(24, 144, 255, 0.08);
  padding: 28px 30px !important;
  border: none !important;
  transition: box-shadow 0.25s, background 0.2s;
  position: relative;
}

.search-result-item:hover {
  background: #f0faff;
  box-shadow: 0 4px 18px rgba(24, 144, 255, 0.18);
}

.result-card {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.result-title {
  font-size: 20px;
  font-weight: bold;
  color: #1677ff;
  flex: 1;
  margin-right: 18px;
  line-height: 1.4;
  text-decoration: none;
}

.emotion-tag {
  padding: 4px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
  margin-left: 10px;
  box-shadow: 0 1px 6px rgba(0,0,0,0.04);
  border: none;
}

.emotion-tag.positive {
  background: linear-gradient(90deg, #eafff1 60%, #b7eb8f 100%);
  color: #389e0d;
}
.emotion-tag.negative {
  background: linear-gradient(90deg, #fff1f0 60%, #ffa39e 100%);
  color: #cf1322;
}
.emotion-tag.neutral {
  background: linear-gradient(90deg, #f9f9f9 60%, #e4e4e4 100%);
  color: #666;
}

.result-keywords {
  margin-bottom: 8px;
}
.keyword-tag {
  display: inline-block;
  background: #f0f5ff;
  color: #2f54eb;
  border-radius: 5px;
  padding: 2px 10px;
  margin-right: 8px;
  font-size: 13px;
  margin-bottom: 2px;
}

.result-meta {
  display: flex;
  gap: 18px;
  font-size: 13px;
  color: #888;
  margin-bottom: 10px;
}

.result-body {
  line-height: 1.8;
  color: #444;
  font-size: 15px;
  word-break: break-all;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-link {
  margin-left: 16px;
  color: #1677ff;
  font-size: 13px;
  text-decoration: underline;
}
.detail-link:hover {
  color: #40a9ff;
}

/* 分页 */
.search-pagination {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #f0f0f0;
}

/* 热点动态区域 */
.hot-notices-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 32px;
  margin-top: 32px;
}

.hot-notices-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.hot-notices-header h5 {
  margin: 0 !important;
  font-size: 20px;
  font-weight: 600;
}

.hot-notices-header .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.hot-notices-list {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.hot-notice-item {
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background: transparent !important;
  width: 100%;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.hot-notice-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.hot-notice-item.notices-level1 {
  border-left: 3px solid #ff4d4f;
}

.hot-notice-item.notices-level1:hover {
  background: rgba(255, 77, 79, 0.1);
}

.hot-notice-item.notices-level2 {
  border-left: 3px solid #faad14;
}

.hot-notice-item.notices-level2:hover {
  background: rgba(250, 173, 20, 0.1);
}

.hot-notice-item.notices-level3 {
  border-left: 3px solid #52c41a;
}

.hot-notice-item.notices-level3:hover {
  background: rgba(82, 196, 26, 0.1);
}

.hot-notice-title {
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  color: #ffffff;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.hot-notice-content {
  display: none; /* 隐藏内容，只显示标题 */
}

.hot-notice-meta {
  display: none; /* 隐藏元信息 */
}

.hot-notice-time {
  display: none;
}

.hot-notice-level {
  display: none; /* 隐藏等级标签，用左边框颜色表示 */
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.notice-icon {
  color: #ffffff;
  font-size: 14px;
  flex-shrink: 0;
  opacity: 0.8;
}

.notice-content .ant-typography {
  flex: 1;
  line-height: 1.4;
  color: #ffffff;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-display-page {
    padding: 16px;
  }
  
  .search-title {
    font-size: 32px !important;
  }
  
  .search-input-container {
    padding: 0 20px 30px;
  }
  
  .search-input .ant-input {
    font-size: 14px;
    padding: 10px 20px;
  }
  
  .search-input .ant-input-search-button {
    font-size: 14px;
    padding: 0 24px;
    height: 44px;
  }
  
  .results-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start !important;
  }
  
  .results-actions {
    text-align: left;
    width: 100%;
  }
  
  .result-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .result-meta {
    align-self: flex-start;
  }
  
  .hot-notices-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .search-display-page {
    padding: 12px;
  }
  
  .search-title {
    font-size: 24px !important;
  }
  
  .search-header {
    padding: 20px 0 15px;
  }
  
  .search-input-container {
    padding: 0 12px 20px;
  }
  
  .search-input .ant-input {
    font-size: 13px;
    padding: 8px 16px;
  }
  
  .search-input .ant-input-search-button {
    font-size: 13px;
    padding: 0 20px;
    height: 40px;
  }
  
  .results-toolbar {
    padding: 12px 16px;
  }
  
  .search-result-item {
    padding: 16px 0 !important;
  }
  
  .result-title {
    font-size: 16px;
  }
  
  .emotion-tag {
    font-size: 11px;
    padding: 2px 8px;
  }
  
  .hot-notice-item {
    padding: 12px;
  }
  
  .notice-content .ant-typography {
    font-size: 13px;
  }
}

/* 滚动条样式 */
.search-results-list::-webkit-scrollbar,
.hot-notices-list::-webkit-scrollbar {
  width: 6px;
}

.search-results-list::-webkit-scrollbar-track,
.hot-notices-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.search-results-list::-webkit-scrollbar-thumb,
.hot-notices-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.search-results-list::-webkit-scrollbar-thumb:hover,
.hot-notices-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-results-section {
  animation: fadeIn 0.5s ease-out;
}

.hot-notice-item {
  animation: fadeIn 0.3s ease-out;
}

.hot-notice-item:nth-child(1) { animation-delay: 0.1s; }
.hot-notice-item:nth-child(2) { animation-delay: 0.2s; }
.hot-notice-item:nth-child(3) { animation-delay: 0.3s; }
.hot-notice-item:nth-child(4) { animation-delay: 0.4s; }
.hot-notice-item:nth-child(5) { animation-delay: 0.5s; }
.hot-notice-item:nth-child(6) { animation-delay: 0.6s; }
.hot-notice-item:nth-child(7) { animation-delay: 0.7s; }
.hot-notice-item:nth-child(8) { animation-delay: 0.8s; }
