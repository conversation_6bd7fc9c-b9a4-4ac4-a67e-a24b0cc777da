import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/Core/Core_AuthContent';
import { notification } from 'antd';

interface RequestsData {
  user_id: string;
  user_token: string;
  data_class: string;
  data_type: string;
  data_methods: string;
  data_argument: string;
  data_kwargs: string;

}


export class Service_Requests {
  public async Async(data: RequestsData): Promise<any> {
    let Response_Data;
    try {
        // 开发模式的统一代理
        // const response = await axios.post(`http://${window.location.hostname}:9311/Service_Interface`,data,{ headers: { 'Content-Type': 'application/json' } });
        // nginx代理 打包的方式使用这种访问
        // const response = await axios.post(`/api/Service_Interface`,data,{ headers: { 'Content-Type': 'application/json' } });
        const apiURL = process.env.REACT_APP_API_URL;
        if (!apiURL) {
          throw new Error('API URL is not defined in environment variables');
        }
        const response = await axios.post(apiURL,data,{ headers: { 'Content-Type': 'application/json' } });
        console.log(response.data)
        if (response.data.Status ==="Success"){
          Response_Data = response.data
        }
      } catch (err) {
        // setError(err.message || 'An error occurred');
      } finally {
        // setLoading(false);
      }
      return Response_Data
    };
};


/**
 * 重构请求实现在此处判断token过期返回的状态码
 * @returns Send_Async_Requests
 */
export function useServiceRequests() {
  const { user,logout  } = useAuth();
  const navigate = useNavigate();

  // 安全检查Token过期 Msg这个Key值存不存在 
  const getSafeValue = (data: any, key: string, defaultValue: string = '值不存在') => {
    return data[key] !== undefined ? data[key] : defaultValue;
  };
  const AsyncTokenRequests = async (data: RequestsData): Promise<any> => {
    let Response_Data;
    try {
      // 统一给user_id和user_token添加token
      data.user_id = user?.usertoken || '';
      data.user_token = user?.usertoken || '';

      // 开发模式的统一代理
      // const response = await axios.post(`http://${window.location.hostname}:9311/Service_Interface`,data,{ headers: { 'Content-Type': 'application/json' } });
      // nginx代理 打包的方式使用这种访问
      // const response = await axios.post(`/api/Service_Interface`,data,{ headers: { 'Content-Type': 'application/json' } });
      const apiURL = process.env.REACT_APP_API_URL;
      if (!apiURL) {
        throw new Error('API URL is not defined in environment variables');
      }
      const response = await axios.post(apiURL,data,{ headers: { 'Content-Type': 'application/json' } });
      if (response.data.Status === "Success") {
        return response.data;
      } else {
        if (getSafeValue(response.data, 'Msg') === 'Token 校验失败或无效' || getSafeValue(response.data, 'Msg') === 'Token 已过期,请重新登录') {
          notification.warning({
            message: '登录状态失效',
            description: '您的登录已过期，请重新登录',
            placement: 'topRight',
            duration: 4,
          });
          logout();
          navigate('/Page_Login');
        }
      }
    } catch (err) {
      console.error(err);
    }
    return Response_Data;
  };

  return { AsyncTokenRequests };
}
