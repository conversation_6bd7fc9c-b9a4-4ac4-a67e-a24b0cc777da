
import sys
import numpy as np
import librosa
import os
from PySide6.QtWidgets import <PERSON>App<PERSON>, QWidget
from PySide6.QtGui import <PERSON>Painter, QColor, QPen, QLinearGradient
from PySide6.QtCore import Qt, QTimer


class Component_AudioChat(QWidget):
    def __init__(self, audio_file):
        super().__init__()
        # self.setWindowTitle("Balanced Audio Visualizer")
        # self.resize(1000, 600)

        # Audio loading
        self.audio_file = audio_file
        try:
            self.y, self.sr = librosa.load(audio_file, sr=44100)
            self.y = librosa.util.normalize(self.y)
            self.harmonic, self.percussive = librosa.effects.hpss(self.y)
            self.duration = librosa.get_duration(y=self.y, sr=self.sr)
        except Exception as e:
            print(f"Error loading audio: {e}")
            sys.exit(1)

        # Parameters
        self.n_fft = 2048
        self.hop_length = 256  # Reduced hop length for better time resolution
        self.bands = 128
        self.history_length = 3  # Reduced history for more real-time feel

        # State variables
        self.current_frame = 0
        self.spectrum = np.zeros(self.bands, dtype=np.float32)
        self.vocal_activity = 0.0




        # Timer
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(30)  # ~33 fps

    def update_frame(self):
        frame_idx = int(self.current_frame * self.sr / self.hop_length)

        if frame_idx * self.hop_length + self.n_fft < len(self.y):
            # Process current frame with overlap for smoother transition
            start_idx = max(0, frame_idx * self.hop_length - self.n_fft // 4)
            frame = self.y[start_idx: start_idx + self.n_fft]

            if len(frame) < self.n_fft:
                frame = np.pad(frame, (0, self.n_fft - len(frame)), 'constant')

            # Calculate spectrum with balanced frequency response
            stft = np.abs(librosa.stft(frame, n_fft=self.n_fft, hop_length=self.hop_length))
            mel = librosa.feature.melspectrogram(S=stft, sr=self.sr, n_mels=self.bands, fmax=8000)

            # Apply perceptual weighting
            freq_weights = np.linspace(1.0, 0.8, self.bands)  # Gentle high-frequency roll-off
            mel = mel * freq_weights[:, np.newaxis]

            db = librosa.power_to_db(mel, ref=np.max)
            db = np.clip(db, -40, 0)
            self.spectrum = ((db + 40) / 40)[:, 0].astype(np.float32)

            # Vocal detection
            vocal_frame = self.harmonic[start_idx: start_idx + self.n_fft]
            if len(vocal_frame) < self.n_fft:
                vocal_frame = np.pad(vocal_frame, (0, self.n_fft - len(vocal_frame)), 'constant')

            vocal_spec = np.abs(librosa.stft(vocal_frame, n_fft=512))
            vocal_energy = np.mean(vocal_spec[50:150])  # 300-3000Hz range
            self.vocal_activity = np.clip(0.7 * self.vocal_activity + 0.3 * vocal_energy * 0.5, 0, 1)

            self.current_frame += 0.03
        else:
            self.current_frame = 0

        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        w, h = self.width(), self.height()
        center_y = h // 2

        # Background with balanced gradient
        gradient = QLinearGradient(0, 0, 0, h)
        gradient.setColorAt(0, QColor(15, 20, 30))
        gradient.setColorAt(1, QColor(8, 12, 18))
        painter.fillRect(0, 0, w, h, gradient)

        # Balanced spectrum visualization
        if len(self.spectrum) > 0:
            spacing = w / self.bands  # Equal spacing for all bands
            max_height = h * 0.45

            # Apply frequency balance correction
            balanced_spectrum = self.spectrum * np.linspace(0.9, 1.1, self.bands)
            balanced_spectrum = np.clip(balanced_spectrum, 0, 1)

            for i in range(self.bands):
                value = balanced_spectrum[i]

                # Dynamic height scaling for better balance
                height = value * max_height * (1.0 + 0.2 * np.sin(i / 10))  # Small variation

                # Color coding with balanced brightness
                hue = 200 + (i / self.bands) * 120  # Blue to green spectrum
                sat = 180 + value * 50
                light = 120 + value * 120

                color = QColor.fromHsl(
                    int(np.clip(hue, 0, 359)),
                    int(np.clip(sat, 0, 255)),
                    int(np.clip(light, 0, 255))
                )

                # Line width with balanced variation
                line_width = 2 + value * 4

                painter.setPen(QPen(color, line_width))
                x_pos = (i + 0.5) * spacing
                painter.drawLine(x_pos, center_y - height / 2, x_pos, center_y + height / 2)

        # Progress and info display
        progress = min(1, max(0, self.current_frame / max(0.1, self.duration)))

        # Balanced progress bar
        # painter.fillRect(20, h - 50, w - 40, 8, QColor(60, 60, 80))
        # painter.fillRect(20, h - 50, int((w - 40) * progress), 8, QColor(100, 180, 255))

        # Text info with balanced layout
        # painter.setPen(Qt.white)
        # painter.drawText(20, 30, os.path.basename(self.audio_file))
        # painter.drawText(20, 60, f"Time: {self.current_frame:.1f}s / {self.duration:.1f}s")

        # Vocal activity indicator
        if self.vocal_activity > 0.3:
            painter.setPen(QColor(255, 120, 120))
            # painter.drawText(w - 150, 30, "Vocal Active")


if __name__ == "__main__":
    app = QApplication(sys.argv)

    audio_file = r"D:\M800001ziKgJ3o5Ipp.mp3"  # Replace with your file path
    audio_file = r"D:\videoplayback.mp3"  # Replace with your file path
    if not os.path.exists(audio_file):
        print(f"File not found: {audio_file}")
        sys.exit(1)

    visualizer = BalancedAudioVisualizer(audio_file)
    visualizer.show()
    sys.exit(app.exec())