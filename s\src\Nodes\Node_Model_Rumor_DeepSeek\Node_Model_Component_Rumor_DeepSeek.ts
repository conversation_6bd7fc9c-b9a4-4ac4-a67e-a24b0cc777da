import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,ModelControl_Rumor_DeepSeek} from "./Node_Model_Control_Rumor_DeepSeek";

export class Node_Model_Component_Rumor_DeepSeek extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| ModelControl_Rumor_DeepSeek}> 
  {
    width  = 480;
    height = 428;
    constructor(Label: string,) {
      super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const ConentControl = new ModelControl_Rumor_DeepSeek(
        '【标题】:未知', // Label for the text area
        '0',
        '0',
        '1',

        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("<PERSON><PERSON>",  <PERSON>entControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    