
import {ClassicPreset } from "rete";
import { Input,Layout, Space, Cascader,Checkbox,CheckboxProps,} from "antd";
import "../Nodes.module.css";
import type { ThemeConfig,} from 'antd';



const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");


export class ModelControl_Rumor_Community extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public douyin: string, 
      public weixing: string, 
      public weibo: string, 
      public author: string,  
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = (safeGet("INTELLIGENCE_TITLE") || "").slice(0, 30)
      this.douyin        = safeGet("INTELLIGENCE_WEB_TIME");
      this.weixing      = safeGet("INTELLIGENCE_NAME_CN");
      this.weibo    = safeGet("INTELLIGENCE_SOURCE_TYPE");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }



export  function Node_Model_Rumor_Community(props: { data:ModelControl_Rumor_Community }) {
  

  
    // 主题配置
const theme: ThemeConfig = {
  components: {
    Carousel: {
      // 核心配置
      motionDurationSlow: "0.1s",      // 文字颜色
      
    },
  },
};
const onChange: CheckboxProps['onChange'] = (e) => {
  console.log(`checked = ${e.target.checked}`);
};

// this.title       = safeGet("INTELLIGENCE_TITLE");
// this.date        = safeGet("INTELLIGENCE_WEB_TIME");
// this.source      = safeGet("INTELLIGENCE_NAME_CN");
// this.author      = safeGet("INTELLIGENCE_AUTHOR");
// this.image       = safeGet("INTELLIGENCE_TYPE");
// this.url         = safeGet("INTELLIGENCE_URL");
// this.content     = safeGet("INTELLIGENCE_CONTENT");
  return (
    
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>

      <Space direction="horizontal" size="middle" style={{ width: '100%' }} > 
        <Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"flex-start",justifyItems:"conter"}}  > 
          <span style={{marginTop:4,marginLeft:12,color:"white",fontSize: 19}} >{props.data.weixing}<Checkbox style={{marginTop:4,marginLeft:140,color:"white"}}></Checkbox></span></Layout>
        <Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems: "flex-start",justifyContent: "conter"}}  > 
          <span style={{marginTop:4,marginLeft:12,color:"white",fontSize: 20}} >QQ <Checkbox style={{marginTop:4,marginLeft:140,color:"white"}}></Checkbox></span>
        </Layout>
      
     </Space>

     <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"flex-start",justifyItems:"conter"}}  > 
          <span style={{marginTop:4,marginLeft:12,color:"white",fontSize: 19}} >{props.data.douyin}<Checkbox style={{marginTop:4,marginLeft:140,color:"white"}}></Checkbox></span></Layout>
        <Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"flex-start",justifyItems:"conter"}}  > 
          <span style={{marginTop:4,marginLeft:12,color:"white",fontSize: 19}} >{props.data.weibo}<Checkbox style={{marginTop:4,marginLeft:138,color:"white"}}></Checkbox></span></Layout>
      
     </Space>
     {/* <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="图片" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.image}</span></Layout></Badge.Ribbon>
        <Badge.Ribbon text="作者" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:220,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.author}</span></Layout></Badge.Ribbon>
      
     </Space> */}

     <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Layout   style={{height:40,width:450,   borderRadius: 4,background:"#666",flex:"1", alignItems:"flex-start",justifyItems:"conter"}}  > 
          {/* <Flex vertical gap="middle"> */}
            <Cascader placeholder={<span style={{fontSize: 19 ,color:"white",marginLeft:190}}>更多</span>} variant="filled"  style={{ height:40,width:450, alignItems: "center", justifyContent: "center", color:"white" }} />
          {/* </Flex> */}
        </Layout>
     </Space>
     <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
        <TextArea
              value= {props.data.title} 
              onChange={(e) => {
                const newValue = e.target.value;
              }}
              rows={7}
              style={{ background:"rgba(248, 240, 240, 0.3)",width: 450,color:"white" }}
            />
        </Space>
      
    
    {/* <Badge.Ribbon text="Hippies">
      <Card title="Pushes open the window" size="small">
       
      </Card>
    </Badge.Ribbon> */}
    
    </Space>
  );
}




