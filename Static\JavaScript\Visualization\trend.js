var symptomName = last_year_month();


$(function(){


  init();

})

 function init(){

   var myColor = ['#1089E7', '#F57474', '#56D0E3', '#F8B448', '#8B78F6'];

   //审核基础信息————今日审核来源分布
   var TodayAnalysisSourceType = echarts.init(document.getElementById('TodayAnalysisSourceType'));
   TodayAnalysisSourceType.setOption({

     color:['#5bc0de'],
     grid:{
         left: '5%',
         right: '5%',
         bottom: '5%',
         containLabel: true
     },
     tooltip : {
        trigger: 'item',
        formatter: "{a}<br/>{b}<br/>{c}个"
    },
     calculable : true,
     xAxis : [
         {
             type : 'category',
             data : ['社交媒体','中文','全球','电报'],
             axisLine:{
                  lineStyle:{
                      color: '#5bc0de'
                  },
              },
              axisLabel : {
                interval:0,
                rotate:40,
                  textStyle: {
                      color: '#fff'
                  }
              }
         }
     ],
     yAxis : [
         {
             type : 'value',
             axisLine:{
                 lineStyle:{
                     color: '#5bc0de'
                 },
             },
             splitLine: {
                 "show": false
             },
             axisLabel: {
                 textStyle: {
                     color: '#fff'
                 },
                 formatter: function (value) {
                     return value + ""
                 },
             },
         }
     ],
     series : [
         {
             name:'数据来源',
             type:'bar',
             barWidth : 20,
             data:[2210,1085,926,669],
         },
     ]
   })

   //主要症状
//    var histogramChart2 = echarts.init(document.getElementById('histogramChart2'));
//    histogramChart2.setOption({

//      color:['#FD6C88'],
//      grid:{
//          left: '5%',
//          right: '5%',
//          bottom: '10%',
//          containLabel: true
//      },
//      tooltip : {
//         trigger: 'item',
//         formatter: "{a}<br/>{b}<br/>{c}人"
//     },
//      calculable : true,
//      yAxis : [
//          {
//              type : 'category',
//              data : ['腹痛、腹胀、腹泻','恶心、呕吐、食欲不振','肌肉酸痛、乏力','持续高烧','头痛、眼眶痛、肌肉疼','皮疹、水泡','呼吸浅促','发热、咳嗽、流口水'],
//              axisLine:{
//                   lineStyle:{
//                       color: '#FD6C88'
//                   },
//               },
//               axisLabel : {
//                   textStyle: {
//                       color: '#fff'
//                   }
//               }
//          }
//      ],
//      xAxis : [
//          {
//              type : 'value',
//              axisLine:{
//                  lineStyle:{
//                      color: '#FD6C88'
//                  },
//              },
//              splitLine: {
//                  "show": false
//              },
//              axisLabel: {
//                  textStyle: {
//                      color: '#fff'
//                  },
//                  formatter: function (value) {
//                      return value + ""
//                  },
//              },
//          }
//      ],
//      series : [
//          {
//              name:'主要症状',
//              type:'bar',
//              barWidth : 20,
//              data:[1750,1416,1136,819,704,413,251,175],
//          },
//      ]
//    })

   //传染病发病趋势
//    var lineChart1 = echarts.init(document.getElementById('lineChart1'));
//    lineChart1.setOption({
//      title: {
//         text: '业务数源',
//         textStyle:{
//            fontSize:16,
//            color:'#ff7f50'
//        },
//     },
//      color:["#ff7f50"],
//      grid:{
//          left: '15%',
//          right: '5%',
//          bottom: '15%',

//      },
//      tooltip : {
//           trigger: 'item',
//           formatter: "{a}<br/>{b}<br/>{c}人"
//       },

//      calculable : true,
//          yAxis: [
//              {
//                  type: 'value',
//                  axisLine:{
//                      lineStyle:{
//                          color: '#ff7f50'
//                      },
//                  },
//                  splitLine: {
//                      "show": false
//                  },
//                  axisLabel: {
//                      textStyle: {
//                          color: '#fff'
//                      },
//                      formatter: function (value) {
//                          return value + ""
//                      },
//                  },
//              }
//          ],
//          xAxis: [
//              {
//                  type: 'category',
//                  data : symptomName,
//                  boundaryGap : false,
//                  axisLine:{
//                      lineStyle:{
//                          color: '#ff7f50'
//                      },
//                  },
//                  splitLine: {
//                      "show": false
//                  },
//                  axisLabel: {
//                    // interval:0,
//                    // rotate:40,
//                      textStyle: {
//                          color: '#fff'
//                      },
//                      formatter: function (value) {
//                          return value + ""
//                      },
//                  },
//              }
//          ],
//      series : [
//          {
//              name:'在线数源数量',
//              type:'line',
//              smooth:true,
//              itemStyle: {normal: {areaStyle: {type: 'default'}}},
//              data:[120, 132, 101, 134, 90, 230, 210,120, 132, 101, 134, 90]
//          },
//      ]

//    })

   //系统基础信息--数源详情
   var TodaySourceInfo = echarts.init(document.getElementById('TodaySourceInfo'));
   TodaySourceInfo.setOption({

     grid: {
         top: '12%',
         left: '30%'
     },
      xAxis: {
          show: false
      },
      yAxis: [{
          show: true,
          data:  ['中文数源','智库数源','涉疆数源','涉藏数源','全球数源','高校数源','门户数源','国内新闻','国内论坛','国内社交','热点数源'],
          inverse: true,
          axisLine: {
              show: false
          },
          splitLine: {
              show: false
          },
          axisTick: {
              show: false
          },
          axisLabel: {
              color: '#fff',
              formatter: (value, index) => {
                  return [

                      `{lg|${index+1}}  ` + '{title|' + value + '} '
                  ].join('\n')
              },
              rich: {
                  lg: {
                      backgroundColor: '#339911',
                      color: '#fff',
                      borderRadius: 15,
                      // padding: 5,
                      align: 'center',
                      width: 15,
                      height: 15
                  },
              }
          },


      }, {
          show: true,
          inverse: true,
          data: [2000, 1800, 1200, 1100,900,900,800,700,122,11,111],
          axisLabel: {
              textStyle: {
                  fontSize: 12,
                  color: '#fff',
              },
          },
          axisLine: {
              show: false
          },
          splitLine: {
              show: false
          },
          axisTick: {
              show: false
          },

      }],
      series: [{
          name: '条',
          type: 'bar',
          yAxisIndex: 0,
          data: [20,18,12,11,9,9,8,7,11,44,11],
          barWidth: 10,
          itemStyle: {
              normal: {
                  barBorderRadius: 20,
                  color: function(params) {
                      var num = myColor.length;
                      return myColor[params.dataIndex % num]
                  },
              }
          },
          label: {
              normal: {
                  show: true,
                  position: 'inside',
                  formatter: '{c}%'
              }
          },
      }, {
          name: '框',
          type: 'bar',
          yAxisIndex: 1,
          barGap: '-100%',
          data: [100, 100, 100, 100,100, 100, 100, 100,100, 100,100],
          barWidth: 15,
          itemStyle: {
              normal: {
                  color: 'none',
                  borderColor: '#00c1de',
                  borderWidth: 1,
                  barBorderRadius: 15,
              }
          }
      }, ]
   })

//    //疾病发病趋势
//    var lineChart2 = echarts.init(document.getElementById('lineChart2'));
//    lineChart2.setOption({
//      title: {
//         text: '疾病发病趋势',
//         textStyle:{
//            fontSize:16,
//            color:'#32cd32'
//        },
//        x:"center"
//     },
//      color:["#32cd32"],
//      grid:{
//          left: '15%',
//          right: '5%',
//          bottom: '25%',

//      },
//      tooltip : {
//           trigger: 'item',
//           formatter: "{a}<br/>{b}<br/>{c}人"
//       },

//      calculable : true,
//          yAxis: [
//              {
//                  type: 'value',
//                  axisLine:{
//                      lineStyle:{
//                          color: '#32cd32'
//                      },
//                  },
//                  splitLine: {
//                      "show": false
//                  },
//                  axisLabel: {
//                      textStyle: {
//                          color: '#fff'
//                      },
//                      formatter: function (value) {
//                          return value + ""
//                      },
//                  },
//              }
//          ],
//          xAxis: [
//              {
//                  type: 'category',
//                  data : symptomName,
//                  boundaryGap : false,
//                  axisLine:{
//                      lineStyle:{
//                          color: '#32cd32'
//                      },
//                  },
//                  splitLine: {
//                      "show": false
//                  },
//                  axisLabel: {
//                    // interval:0,
//                    // rotate:40,
//                      textStyle: {
//                          color: '#fff'
//                      },
//                      formatter: function (value) {
//                          return value + ""
//                      },
//                  },
//              }
//          ],
//      series : [
//          {
//              name:'疾病发病人数',
//              type:'line',
//              smooth:true,
//              itemStyle: {normal: {areaStyle: {type: 'default'}}},
//              data:[520, 232,701, 434, 190, 230, 210,120, 132, 101, 134, 890]
//          },
//      ]

//    })

   //审核基础信息————今日审核来源分布
   var TodayAnalysisType = echarts.init(document.getElementById('TodayAnalysisType'));
   TodayAnalysisType.setOption({
     color:["#32cd32","#ff7f50","#87cefa","#FD6C88","#4b5cc4","#faff72"],
     tooltip : {
      trigger: 'item',
      formatter: "{a}<br/>{b}<br/>{c}个"
     },
     calculable : true,
     series : [
         {
             name:'数据类型',
             type:'pie',
             radius : [30, 110],
             center : ['50%', '50%'],
             roseType : 'area',
             x: '50%',
             sort : 'ascending',
             data:[
                 {value:10, name:'人像'},
                 {value:5, name:'文章'},
             ]
         }
     ]
   })

//    //性别分布
//    var labelFromatter = {
//        normal : {
//            label : {
//               position : 'center',
//                formatter : function (params){
//                  console.log(params)
//                  if(params.name == "女性"){
//                    return "女性"+":"+(params.percent + '%')
//                  }else{
//                    return "男性"+":"+(params.percent + '%')
//                  }
//                },
//            },
//            labelLine : {
//                show : false
//            }
//        },
//    };

//    var pieChart2 = echarts.init(document.getElementById('pieChart2'));
//    pieChart2.setOption({

//         color: ['#87cefa','#FD6C88'],
//         tooltip : {
//             trigger: 'item',
//             formatter: "{b}({c})<br/>{d}%"
//         },

//         series : [
//             {
//                 type : 'pie',
//                 center : ['50%', '50%'],
//                 radius : [55, 95],
//                 x: '0%', // for funnel
//                 itemStyle : labelFromatter,
//                 data : [
//                     {name:'男性', value:158},
//                     {name:'女性', value:142},
//                 ]
//             },
//         ],
//    })

 }
