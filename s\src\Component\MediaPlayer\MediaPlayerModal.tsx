// MediaPlayerModal.tsx
import React, { useRef, useEffect, useState } from 'react';
import styles from './MediaPlayerModal.module.css'; // 可自定义样式
import { Button } from 'antd';

interface MediaPlayerState {
  blobUrl: string | null;
  isLoading: boolean;
  error: string | null;
}

interface MediaPlayerModalProps {
  visible: boolean;
  url: string; // 远程视频或音频地址
  mediaType?: 'video' | 'audio';
  onClose: () => void;
}

const MediaPlayerModal: React.FC<MediaPlayerModalProps> = ({ visible, url, mediaType = 'video', onClose }) => {
    const videoRef = useRef<HTMLVideoElement | HTMLAudioElement>(null);
    const waveformCanvasRef = useRef<HTMLCanvasElement>(null);
    const spectrogramCanvasRef = useRef<HTMLCanvasElement>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const analyserRef = useRef<AnalyserNode | null>(null);
    const sourceRef = useRef<MediaElementAudioSourceNode | null>(null);

    const [state, setState] = useState<MediaPlayerState>({
        blobUrl: null,
        isLoading: false,
        error: null,
    });

    const fetchBlobUrl = async (url: string) => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error(`请求失败: ${response.statusText}`);
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            setState({ blobUrl, isLoading: false, error: null });
        } catch (err) {
            setState({ blobUrl: null, isLoading: false, error: '加载媒体失败，请重试' });
        }
    };

    const initAudioAnalysis = () => {
        const mediaEl = videoRef.current;
        const waveformCanvas = waveformCanvasRef.current;
        const spectrogramCanvas = spectrogramCanvasRef.current;

        if (!mediaEl || !waveformCanvas || !spectrogramCanvas) return;

        const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
        const audioCtx = new AudioContext();
        const analyser = audioCtx.createAnalyser();
        analyser.fftSize = 2048;

        const source = audioCtx.createMediaElementSource(mediaEl);
        source.connect(analyser);
        analyser.connect(audioCtx.destination);

        audioContextRef.current = audioCtx;
        analyserRef.current = analyser;
        sourceRef.current = source;

        const dataArrayWaveform = new Uint8Array(analyser.frequencyBinCount);
        const dataArraySpectrogram = new Uint8Array(analyser.frequencyBinCount);

        const drawWaveform = () => {
        const ctx = waveformCanvas.getContext('2d');
        if (!ctx) return;
        const width = waveformCanvas.width;
        const height = waveformCanvas.height;
        ctx.clearRect(0, 0, width, height);
        analyser.getByteTimeDomainData(dataArrayWaveform);

        ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
        ctx.beginPath();

        const sliceWidth = width / dataArrayWaveform.length;
        let x = 0;
        for (let i = 0; i < dataArrayWaveform.length; i++) {
            const v = dataArrayWaveform[i] / 128.0;
            const y = v * height / 2;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
            x += sliceWidth;
        }
        ctx.stroke();
        };

        const drawSpectrogram = () => {
            const ctx = spectrogramCanvas.getContext('2d');
            if (!ctx) return;
            const width = spectrogramCanvas.width;
            const height = spectrogramCanvas.height;
            ctx.clearRect(0, 0, width, height);
            analyser.getByteFrequencyData(dataArraySpectrogram);

            const barWidth = (width / dataArraySpectrogram.length) * 2.5;
            let x = 0;
            for (let i = 0; i < dataArraySpectrogram.length; i++) {
                const barHeight = dataArraySpectrogram[i];
                const hue = barHeight + 100;
                ctx.fillStyle = `hsl(${hue}, 100%, 40%)`;
                ctx.fillRect(x, height - barHeight / 2, barWidth, barHeight / 2);
                x += barWidth + 1;
            }
        };

        const animate = () => {
            drawWaveform();
            drawSpectrogram();
            requestAnimationFrame(animate);
        };
        animate();
    };

    const handleCanPlay = () => {
        const mediaEl = videoRef.current;
        if (mediaEl) {
            setTimeout(async () => {
                try {
                    await mediaEl.play();
                } catch (e) {
                    console.log('自动播放失败，请手动点击');
                }
            }, 100);
        }
    };

    const handleError = () => {
        setState(prev => ({
        ...prev,
        error: '媒体加载失败，请检查网络连接或文件格式',
        }));
    };

    const handlePlay = () => {
        if (!sourceRef.current) {
            initAudioAnalysis();
        }
    };

    // 点击页面恢复 AudioContext（浏览器策略）
    useEffect(() => {
        console.log('当前媒体类型:',mediaType)
        const resumeAudioContext = () => {
        if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
            audioContextRef.current.resume();
        }
        };
        window.addEventListener('click', resumeAudioContext, { once: true });
        return () => {
        window.removeEventListener('click', resumeAudioContext);
        };
    }, []);

    // 加载媒体
    useEffect(() => {
        if (visible && url) {
        fetchBlobUrl(url);
        }
    }, [visible, url]);

    // 监听 play 事件以初始化音频分析
    useEffect(() => {
        const mediaEl = videoRef.current;
        if (!mediaEl) return;
        mediaEl.addEventListener('play', handlePlay);
        return () => {
        mediaEl.removeEventListener('play', handlePlay);
        };
    }, [videoRef.current]);

    // 组件卸载时清理资源
    useEffect(() => {
        return () => {
        if (sourceRef.current) {
            sourceRef.current.disconnect();
            sourceRef.current = null;
        }
        if (analyserRef.current) {
            analyserRef.current.disconnect();
            analyserRef.current = null;
        }
        if (audioContextRef.current) {
            audioContextRef.current.close().then(() => {
            audioContextRef.current = null;
            });
        }
        if (state.blobUrl) {
            URL.revokeObjectURL(state.blobUrl);
        }
        };
    }, []);

    if (!visible) return null;

    return (
        <div className={styles.modalOverlay} onClick={onClose}>
            <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
                <div className={styles.mediaContainer}>
                    {mediaType === 'video' ? (
                        <video
                        ref={videoRef as any}
                        src={state.blobUrl ?? undefined}
                        controls
                        onCanPlay={handleCanPlay}
                        onError={handleError}
                        className={styles.mediaElement}
                        />
                    ) : (
                        <audio
                        ref={videoRef as any}
                        src={state.blobUrl ?? undefined}
                        controls
                        onCanPlay={handleCanPlay}
                        onError={handleError}
                        style={{ display: 'block', width: '100%', height: '100px' }}
                        />
                    )}
                </div>

                {/* 音频可视化 */}
                <div className={styles.visualization}>
                    <div className={styles.canvasWrapper} >
                        <canvas 
                            ref={waveformCanvasRef} 
                            width={300} 
                            height={100} 
                            className={`${styles.canvas} ${styles.waveformCanvas}`}
                        />
                        <div className={styles.graphicLabel}>波形图</div>
                    </div>
                    <div className={styles.canvasWrapper} >
                        <canvas 
                            ref={spectrogramCanvasRef} 
                            width={300} 
                            height={100}
                            className={`${styles.canvas} ${styles.spectrogramCanvas}`}
                        />
                        <div className={styles.graphicLabel}>语谱图</div>
                    </div>
                </div>

                {state.isLoading && <div>加载中...</div>}
                {state.error && <div className={styles.errorMessage}>{state.error}</div>}

                <div className={styles.actions}>
                    <Button onClick={onClose}>
                        弹窗关闭
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default MediaPlayerModal;