# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
# from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog
import qtawesome as qta
from qtawesome import icon
from setuptools.command.easy_install import CommandSpec

# ---------------------------------------------------------------------------------------- 获取系统初始化数据
# from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.Utils.UtilsCenter import *

from Bin.System.OS.Utils import Utils_PopupDialog
Page_Info={}
PP(Page_Info)
# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_Home(QtWidgets.QWidget):
    Signal_Result= QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info

        self.Page_Info = args[0]
        Page_Info  = self.Page_Info
        Page_Info["QLabel_Mission_List"]={}
        Page_Info["QLabel_Status_List"]={}

        PC(Page_Info)
        System_Status = {
            'BaseConfig': {
                'SYSTEM': {
                    'network': "Lan"
                }
            },

        }
        Page_Info["System_Status"] = System_Status

        Page_Info["User_Info"]={}
        Page_Info["User_Info"]["User_Name"]           = "哨兵测试用户"
        Page_Info["User_Info"]["User_Status"]          = "已登录"
        Page_Info["User_Info"]["Work_Method"]         = "本地部署"
        Page_Info["User_Info"]["Work_Computing"]      = "本地大模型"

        # __Page_Worker_Info = Page_Update_Status(self, {"Command": "Worker_Info"})
        # __Page_Worker_Info.Signal_Result.connect(self.PAGE_EXECUTE_HANDLER)
        # __Page_Worker_Info.start()


        Page_Info["Sentinel_Service"] = "Failed"
        # PI(self.Page_Info)
        # PP(('Page_Widget_Home',self.Page_Info))

        # self.Worker_Info   =  self.Page_Info["BaseConfig"]["Task_Config"]["Machine"]["Machine_Info"]

        # __Page_Update_Status = Page_Update_Status(self, {"Command": "Update_Status"})
        # __Page_Update_Status.Signal_Result.connect(self.PAGE_EXECUTE_HANDLER)
        # __Page_Update_Status.start()


        self.initUI()

    # try:
    #     __Loading_Core_Server = Loading_Core_Server()
    #     __Loading_Core_Server()
    # except:
    #     PT()
    def initUI(self):

        QLabel_Name = QtWidgets.QLabel(self)
        QLabel_Name.setStyleSheet("background:transparent")
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 18))
        QLabel_Name.setGeometry(QtCore.QRect(16, 16, 380, 80))
        QLabel_Name.setText(f'{Page_Info["User_Info"]["User_Name"]}')


        #
        # Icon = qta.icon('msc.run', scale_factor=0.5, color=('blue'), color_active='blue')
        # QPushButton_Line_Click = QtWidgets.QPushButton(self)
        # QPushButton_Line_Click.setIconSize(QtCore.QSize(35, 35))
        # QPushButton_Line_Click.setStyleSheet("background:transparent")
        # QPushButton_Line_Click.setIcon(Icon)
        # QPushButton_Line_Click.setGeometry(QtCore.QRect(0, 48, 80, 80))
        # QPushButton_Line_Click.clicked.connect(lambda: self.COMMAND_SERVICE({"Command": "Page_Change", "Page_Name": "Task_WGC_Mission_Execute"}))





        # self.QLabel_Location = QtWidgets.QLabel(self)
        # self.QLabel_Location.setStyleSheet("background:transparent")
        # self.QLabel_Location.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # self.QLabel_Location.setGeometry(QtCore.QRect(186, 0, 380, 80))
        # self.QLabel_Location.setText(
        #     f'<span style="color: black;">终端状态: </span>'
        #     f'<span style="color: blue;">{Page_Info["User_Info"]["User_Status"]}</span>'
        # )


        # self.QLabel_Location = QtWidgets.QLabel(self)
        # self.QLabel_Location.setStyleSheet("background:transparent")
        # self.QLabel_Location.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # self.QLabel_Location.setGeometry(QtCore.QRect(186, 18, 380, 80))
        # self.QLabel_Location.setText(
        #     f'<span style="color: black;">工作模式: </span>'
        #     f'<span style="color: blue;">{Page_Info["User_Info"]["Work_Method"]}</span>'
        #     )
        #
        # self.QLabel_Lan = QtWidgets.QLabel(self)
        # self.QLabel_Lan.setStyleSheet("background:transparent")
        # self.QLabel_Lan.setFont(QtGui.QFont("Microsoft YaHei", 8))
        # self.QLabel_Lan.setGeometry(QtCore.QRect(186, 36, 380, 80))
        # self.QLabel_Lan.setText(
        #     f'<span style="color: black;">算力支持: </span>'
        #     f'<span style="color: blue;">{Page_Info["User_Info"]["Work_Computing"]}</span>'
        #     )



        self.Button_Adaptive = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        # ---------------------------------------------------------------------------------------- Set_ProgressBar
        self.Set_System_Status_List()

        self.Set_Mission_Menu_List()




        # # 创建定时器
        self.QTimer_Update = QTimer(self)
        self.QTimer_Update.timeout.connect(self.Page_Heart)
        self.QTimer_Update.start(20000)


    def Set_System_Status_List(self):

        QLabel_System_Status = QtWidgets.QLabel(self)
        QLabel_System_Status.setStyleSheet('background:')
        QLabel_System_Status.setGeometry(QtCore.QRect(20, 118, 348,58))
        QHBoxLayout = QtWidgets.QHBoxLayout()
        QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        QHBoxLayout.setSpacing(0)
        QLabel_System_Status.setLayout(QHBoxLayout)

        self.Status_List=[
            {"Status_Name":"内存占用", "Status_Sign":"Memory","Status_Value":0},
            {"Status_Name":"CPU利用率","Status_Sign":"CPU","Status_Value":0},
            {"Status_Name":"GPU使用率","Status_Sign":"GPU","Status_Value":0},
            {"Status_Name":"网络流量","Status_Sign":"Flow","Status_Value":0},
                          ]

        for Status in self.Status_List:
            Page_Info["QLabel_Status_List"][Status["Status_Sign"]]={}
            QLabel_Status = QtWidgets.QLabel()
            ProgressBar_Status = QtWidgets.QProgressBar(QLabel_Status)
            ProgressBar_Status.setOrientation(QtCore.Qt.Vertical)
            ProgressBar_Status.setTextVisible(False)
            ProgressBar_Status.setStyleSheet("QProgressBar { border: 0px solid grey; border-radius: 0px; background-color: gray; text-align: center;}QProgressBar::chunk {background:QLinearGradient(x1:0,y1:0,x2:2,y2:0,stop:0 #1F45FC,stop:1  #1F45FC); }")
            ProgressBar_Status.setGeometry(QtCore.QRect(3, 3, 5, 30))
            ProgressBar_Status.setMinimum(0)
            ProgressBar_Status.setMaximum(100)
            ProgressBar_Status.setValue(0)
            Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Progress"] = ProgressBar_Status



            QLabel_Status_Name = QtWidgets.QLabel(QLabel_Status)
            QLabel_Status_Name.setAlignment(QtCore.Qt.AlignLeft)
            QLabel_Status_Name.setStyleSheet("background:transparent")
            QLabel_Status_Name.setFont(QtGui.QFont("Microsoft YaHei", 10))
            QLabel_Status_Name.setText(Status["Status_Name"])
            QLabel_Status_Name.setGeometry(QtCore.QRect(10, -2, 80, 50))

            QLabel_Status_Value = QtWidgets.QLabel(QLabel_Status)
            QLabel_Status_Value.setAlignment(QtCore.Qt.AlignLeft)
            QLabel_Status_Value.setStyleSheet("background:transparent")
            QLabel_Status_Value.setFont(QtGui.QFont("Microsoft YaHei", 13))
            QLabel_Status_Value.setText(str(Status["Status_Value"]) + '%')
            QLabel_Status_Value.setGeometry(QtCore.QRect(10, 15, 80, 50))
            Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Value"] = QLabel_Status_Value

            QHBoxLayout.addWidget(QLabel_Status, 1)




    def Set_Mission_Menu_List(self):

        QLabel_Line_List = QtWidgets.QLabel(self)
        QLabel_Line_List.setStyleSheet("background:rgba(255,255,255,0);border-radius:6px;")
        QLabel_Line_List.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Line_List.setGeometry(QtCore.QRect(2, 188, 378, 388))

        QVBoxLayout_Line = QtWidgets.QVBoxLayout()
        QVBoxLayout_Line.setSpacing(0)  # 内边界
        QVBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line_List.setLayout(QVBoxLayout_Line)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setMinimumSize(390, 2000)  #######设置滚动条的尺寸

        self.Set_Line_Menu({"Line_ID":1})
        self.Mission_List = [
            {"Line_ID":2, "Line_Type":"System", "Line_Name":"哨兵核心",   "Line_Sign":"Sentinel_Server", "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"mdi.server-security","Size":0.8} ,            "Line_Icon_Command":{"Command": "Open_Browser", "Browser_Url": "http://192.168.123.242:8080/Page_Home"},          "Line_Command":{"Command": "Page_Change", "Page_Name": "SentinelServer"}},
            {"Line_ID":3, "Line_Type":"System", "Line_Name":"Web服务",   "Line_Sign":"NodeJs_Web",      "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"mdi.google-chrome","Size":0.8} ,              "Line_Icon_Command":{"Command":  "Page_Change", "Page_Name": "SentinelServer"},           "Line_Command":{"Command": "Page_Change", "Page_Name": "NodeJsWeb"}},
            {"Line_ID":4, "Line_Type":"System", "Line_Name":"AI模型",    "Line_Sign":"AI_Model",        "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"fa5b.react","Size":0.8} ,                     "Line_Icon_Command":{"Command":  "Page_Change", "Page_Name": "SentinelServer"},    "Line_Command":{"Command": "Page_Change", "Page_Name": "AIModel"}},
        ]
        for Mission_Info in self.Mission_List:
            Page_Info["QLabel_Mission_List"][Mission_Info["Line_Sign"]] = {}
            self.Set_Line_Element(Mission_Info)
        ##创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet("QScrollBar {height:0px;}")
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        QVBoxLayout_Line.addWidget(self.QScrollArea_Line, 1)

    def Set_Line_Menu(self, Line_Info):

        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)
        QLabel_Line.setStyleSheet("""QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}""")
        QLabel_Line.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Line.setMinimumSize(348, 70)
        QLabel_Line.setMaximumSize(348, 70)
        QHBoxLayout_Line = QtWidgets.QHBoxLayout()
        QHBoxLayout_Line.setSpacing(0)  # 内边界
        QHBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line.setLayout(QHBoxLayout_Line)

        Menu_List=[
            {"Menu_Name":"任务中心","Menu_Icon":{"Icon":"mdi.google-chrome","Size":1.2},              "Menu_Command":{"Command": "Open_Browser","Browser_Url": "http://192.168.123.242:8080/Page_Home"}},
            {"Menu_Name":"声纹分析","Menu_Icon":{"Icon":"mdi6.soundcloud","Size":1.3},                "Menu_Command":{"Command": "Open_Windows","Dialog_Info": {"Dialog_Title":"哨兵提示",}}},
            {"Menu_Name":"素材整理","Menu_Icon":{"Icon":"mdi6.qrcode-edit","Size":1},                 "Menu_Command":{"Command": "Self_Open_Dialog","Dialog_Info": {"Dialog_Title":"哨兵提示"}}},
            {"Menu_Name":"算法模型","Menu_Icon":{"Icon":"mdi.animation-outline","Size":1},            "Menu_Command":{"Command": "Self_Open_Dialog","Dialog_Info": {"Dialog_Title":"提示"}}},
            {"Menu_Name":"更多","Menu_Icon":{"Icon":"ri.more-line","Size":1.2},                       "Menu_Command":{"Command":  "Self_Open_Dialog","Dialog_Info": {"Dialog_Title":"提示"}}},
        ]
        def Set_Menu(Menu_Info):
            QLabel_Menu = QLabel_Click_Icon()
            QLabel_Menu.setStyleSheet("""QLabel{background:rgba(0,0,0,0.0);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(255, 255,255,0.5);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")

            QVBoxLayout_Menu = QtWidgets.QVBoxLayout()
            QVBoxLayout_Menu.setSpacing(0)  # 内边界
            QVBoxLayout_Menu.setContentsMargins(0, 0, 0, 0)  # 外边
            QLabel_Menu.setLayout(QVBoxLayout_Menu)
            QLabel_Menu.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Menu_Info["Menu_Command"]))

            Icon = qta.icon(f'{Menu_Info["Menu_Icon"]["Icon"]}', scale_factor=Menu_Info["Menu_Icon"]["Size"],color=('#1E90FF'), color_active='skyblue')

            QPushButton_Icon = QtWidgets.QPushButton()
            QPushButton_Icon.setIconSize(QtCore.QSize(25, 25))
            QPushButton_Icon.setStyleSheet("background:transparent")
            QPushButton_Icon.setIcon(Icon)
            QPushButton_Icon.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Menu_Info["Menu_Command"]))

            QLabel_Name = QtWidgets.QLabel(QLabel_Line)
            QLabel_Name.setAlignment(QtCore.Qt.AlignCenter | QtCore.Qt.AlignVCenter)
            QLabel_Name.setStyleSheet("background:transparent;color:#1E90FF")
            QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
            QLabel_Name.setText(Menu_Info["Menu_Name"])

            QVBoxLayout_Menu.addWidget(QPushButton_Icon)
            QVBoxLayout_Menu.addWidget(QLabel_Name)
            return QLabel_Menu
        for Menu_Info in Menu_List:
            QHBoxLayout_Line.addWidget(Set_Menu(Menu_Info))








        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1) * 80)


















    def Set_Mission_List(self):
        QLabel_Line_List = QtWidgets.QLabel(self)
        QLabel_Line_List.setStyleSheet("background:rgba(255,255,255,0);border-width:2px;border-radius:6px;padding:2px 4px;color:black")
        QLabel_Line_List.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Line_List.setGeometry(QtCore.QRect(2, 156, 378, 388))

        QVBoxLayout_Line_List = QtWidgets.QVBoxLayout()
        QVBoxLayout_Line_List.setSpacing(0)  # 内边界
        QVBoxLayout_Line_List.setContentsMargins(0, 0, 0, 0)  # 外边
        QLabel_Line_List.setLayout(QVBoxLayout_Line_List)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setMinimumSize(380, 2000)  #######设置滚动条的尺寸

        QVBoxLayout_QTopFiller = QtWidgets.QVBoxLayout()
        QVBoxLayout_QTopFiller.setSpacing(0)  # 内边界
        QVBoxLayout_QTopFiller.setContentsMargins(0, 0, 0, 0)  # 外边
        self.QWidget_TopFiller.setLayout(QVBoxLayout_QTopFiller)


        self.Mission_List = [
            {"Line_ID":1, "Line_Type":"System", "Line_Name":"哨兵核心",   "Line_Sign":"Sentinel_Server", "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"mdi.server-security","Size":0.8} ,            "Line_Icon_Command":{"Command": "Open_Browser", "Browser_Url": "http://192.168.123.242:8080/Page_Home"},          "Line_Command":{"Command": "Page_Change", "Page_Name": "SentinelServer"}},
            {"Line_ID":2, "Line_Type":"System", "Line_Name":"Web服务",   "Line_Sign":"NodeJs_Web",      "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"mdi.google-chrome","Size":0.8} ,              "Line_Icon_Command":{"Command":  "Page_Change", "Page_Name": "SentinelServer"},           "Line_Command":{"Command": "Page_Change", "Page_Name": "NodeJsWeb"}},
            {"Line_ID":3, "Line_Type":"System", "Line_Name":"AI模型",    "Line_Sign":"AI_Model",        "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"fa5b.react","Size":0.8} ,                     "Line_Icon_Command":{"Command":  "Page_Change", "Page_Name": "SentinelServer"},    "Line_Command":{"Command": "Page_Change", "Page_Name": "AIModel"}},
            # {"Line_ID":4, "Line_Type":"System", "Line_Name":"数据引擎",   "Line_Sign":"Data_Engine",     "Line_Explain":"当前:哨兵系统的业务支持模块管理",                         "Line_Status":"未启动",                    "Line_Icon":{"Icon":"mdi.database-refresh-outline","Size":0.8} ,   "Line_Icon_Command":{"Command": "Page_Change", "Page_Name": "SentinelServer"},                      "Line_Command":{"Command": "Page_Change", "Page_Name": "DataEngine"}},
            # {"Line_ID":2, "Line_Type":"Account", "Line_Name":"工作资源管理",      "Line_Explain":"影子任务账号资源状态",                 "Line_Status":"FK[10]X[0]YT[9]SF[9]",                    "Line_Icon":{"Icon":"mdi6.store-cog-outline","Size":1.2} ,      "Line_Command":{"Command": "Page_Change", "Page_Name": "Task_Live"}},
            # {"Line_ID":3, "Line_Type":"Account", "Line_Name":"平台任务脚本",      "Line_Explain":"管理个平台脚本的维护",                 "Line_Status":"FK[Ok]X[Ok]YT[Ok]SF[Ok]",                 "Line_Icon":{"Icon":"ph.code-bold","Size":1.1} ,                "Line_Command":{"Command": "Page_Change", "Page_Name": "Task_Live"}},
            # {"Line_ID":4, "Line_Type":"Account", "Line_Name":"影子整备工具",      "Line_Explain":"各类统计、汇总工具",                   "Line_Status":"未知",                                    "Line_Icon":{"Icon":"msc.tools","Size":1} ,                     "Line_Command":{"Command": "Page_Change", "Page_Name": "Task_Live"}},
            # {"Line_ID":5, "Line_Type":"Account", "Line_Name":"终端设置管理",      "Line_Explain":"系统信息",                             "Line_Status":"工作机器的网络、系统、任务等信息",        "Line_Icon":{"Icon":"fa.cogs","Size":1} ,                       "Line_Command":{"Command": "Page_Change", "Page_Name": "Worker"}},
            # {"Line_ID":4, "Line_Name":"***********","Mission_IP":"***********", "Mission_Type":"1111","Line_Status":"gay"},
            # {"Line_ID":5, "Line_Name":"***********","Mission_IP":"***********", "Mission_Type":"1111","Line_Status":"gay"},
            # {"Line_ID":6, "Line_Name":"***********","Mission_IP":"***********", "Mission_Type":"1111", "Line_Status":"gay"},
        ]

        # self.Set_Line_Menu( {"Line_ID":1})


        for Mission_Info in self.Mission_List:
            Page_Info["QLabel_Mission_List"][Mission_Info["Line_Sign"]]={}
            self.Set_Line_Element(Mission_Info)
        # ##创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet("QScrollBar {height:0px;}")
        #
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        QVBoxLayout_QTopFiller.addWidget(self.QScrollArea_Line, 1)




    def Set_Line_Element(self,Line_Info):
        PP(Line_Info)
        QLabel_Line = QtWidgets.QLabel(self.QWidget_TopFiller)

        QLabel_Line.setStyleSheet("""QLabel{background:rgba(255,255,255,0.3);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
        QLabel_Line.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Line.setMinimumSize(348, 70)
        QLabel_Line.setMaximumSize(348, 70)

        Icon = qta.icon(f'{Line_Info["Line_Icon"]["Icon"]}', scale_factor=Line_Info["Line_Icon"]["Size"], color=('#1E90FF'), color_active='skyblue')

        QPushButton_Line_Icon = QtWidgets.QPushButton(QLabel_Line)
        QPushButton_Line_Icon.setIconSize(QtCore.QSize(25, 25))
        QPushButton_Line_Icon.setStyleSheet("background:transparent")
        QPushButton_Line_Icon.setIcon(Icon)
        QPushButton_Line_Icon.setGeometry(QtCore.QRect(13, 11, 50, 50))
        QPushButton_Line_Icon.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Line_Info["Line_Icon_Command"]))

        QLabel_Line_Name = QtWidgets.QLabel(QLabel_Line)
        QLabel_Line_Name.setAlignment(QtCore.Qt.AlignLeft)
        QLabel_Line_Name.setStyleSheet("background:transparent")
        QLabel_Line_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Line_Name.setText(Line_Info['Line_Name'])
        QLabel_Line_Name.setGeometry(QtCore.QRect(70, 22, 350, 50))


        QLabel_Line_Status = QtWidgets.QLabel(QLabel_Line)
        QLabel_Line_Status.setAlignment(QtCore.Qt.AlignLeft)
        if Line_Info['Line_Status'] =="未启动":
            QLabel_Line_Status.setStyleSheet("background:transparent;color:red")
        else:
            QLabel_Line_Status.setStyleSheet("background:transparent;color:green")
        QLabel_Line_Status.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Line_Status.setText((Line_Info['Line_Status']))
        QLabel_Line_Status.setGeometry(QtCore.QRect(220, 26, 350, 50))
        Page_Info["QLabel_Mission_List"][Line_Info["Line_Sign"]]["Status"] = QLabel_Line_Status

        # Icon_VPN = qta.icon('ph.first-aid-kit-thin', scale_factor=0.5, color=('gray', 720), color_active='skyblue')
        Icon = qta.icon('fa6s.angle-right', scale_factor=0.5, color=('gray'), color_active='blue')
        QPushButton_Line_Click = QtWidgets.QPushButton(QLabel_Line)
        QPushButton_Line_Click.setIconSize(QtCore.QSize(25, 25))
        QPushButton_Line_Click.setStyleSheet("background:transparent")
        QPushButton_Line_Click.setIcon(Icon)
        QPushButton_Line_Click.setGeometry(QtCore.QRect(300, 11, 50, 50))
        QPushButton_Line_Click.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Line_Info["Line_Command"]))
        # Button_Status_VPN_Image.clicked.connect(
        #     lambda: self.Control_Command({'Command_Type': 'Change_Page', 'Command_Content': 'Mission_Shadow_Client'}))

        # return Label_Status_Mission
        QLabel_Line.move(10, (int(Line_Info['Line_ID']) - 1) * 80)








    # def Update_Status_Heart(self):
    #     if Page_Info["Task_Execute_Switch"]:
    #         PP("Task_Execute_Heart")
    #         __Page_Update_Status = Page_Update_Status(self, {"Command":"Update_Status"})
    #         __Page_Update_Status.Signal_Result.connect(self.PAGE_EXECUTE_HANDLER)
    #         __Page_Update_Status.start()



    def Page_Heart(self):
        __Page_Mission_Execute =Page_Mission_Execute(self,"Mission_Update_Status")
        __Page_Mission_Execute.Signal_Result.connect(self.PAGE_HANDLE_RESULT)
        __Page_Mission_Execute.start()

    def PAGE_HANDLER_EXECUTE(self,*args):

        if  "Self_" in  args[0]["Command"]:
            PP(args[0])
            "Self_Dialog"
            Command = args[0]["Command"].replace("Self_","")
            eval(f"self.{Command}(args[0])")



        else:
            self.Signal_Result.emit(args[0])






    def PAGE_HANDLE_RESULT(self,Result):
        try:
                PP(Result)
                eval(f'self.{Result["Command"]}(Result)')
                # self.Server_Status_Info = Result["Server_Status_Info"]
                # self.Result["Server_Status_Info"] = CServer()
                # self.Result["Status"] = "Success"
                # self.Result["Command"]= "Server_Status"
        except:
            pass

    def Page_Update(self,Result):

        try:
            PP(("Page_Update", Result))
            Server_Status_Info = Result["Server_Status_Info"]
        except:
            Server_Status_Info ={}




        PI("Page_Update")

        # self.QButton_Status = QtWidgets.QPushButton(self)
        # self.QButton_Status.setIconSize(QtCore.QSize(25, 25))
        # self.QButton_Status.setStyleSheet("background:transparent")
        # self.QButton_Status.setIcon(Icon)
        # self.QButton_Status.setGeometry(QtCore.QRect(300, 101, 80, 50))
        # self.QButton_Status.hide()
        # self.QButton_Status_Update.hide()
        # self.QButton_Status_Update.rotation_timer.stop()
        # if self.Page_Info["Sentinel_Service"] == "Success":
        #     Icon = qta.icon('mdi.cloud-check-outline', scale_factor=1, color=('#0041C2', 255), color_active='skyblue')
        #     self.QButton_Status.setIcon(Icon)
        #     self.QButton_Status.show()
        # else:
        #     Icon = qta.icon('mdi6.cloud-alert', scale_factor=1, color=('red',110), color_active='skyblue')
        #     self.QButton_Status.setIcon(Icon)
        #     self.QButton_Status.show()

        #
        #

        #


        for Mission in self.Mission_List:
            try:
                Page_Info["QLabel_Mission_List"][Mission["Line_Sign"]]["Status"].setStyleSheet("background:transparent;color:blue")
                Page_Info["QLabel_Mission_List"][Mission["Line_Sign"]]["Status"].setText(Server_Status_Info[Mission["Line_Sign"]]["Message"])
            except:
                Page_Info["QLabel_Mission_List"][Mission["Line_Sign"]]["Status"].setStyleSheet("background:transparent;color:red")
                Page_Info["QLabel_Mission_List"][Mission["Line_Sign"]]["Status"].setText("未启动")

        for Status in self.Status_List:
            Status_Value = random.randint(0,100)
            if Status_Value <= 60:
                Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Progress"].setStyleSheet("QProgressBar { border: 0px solid grey; border-radius: 0px; background-color: gray; text-align: center;}QProgressBar::chunk {background:QLinearGradient(x1:0,y1:0,x2:2,y2:0,stop:0  #1F45FC,stop:1 #1F45FC); }")
            elif Status_Value > 60 and Status_Value <= 80:
                Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Progress"].setStyleSheet("QProgressBar { border: 0px solid grey; border-radius: 0px; background-color: gray; text-align: center;}QProgressBar::chunk {background:QLinearGradient(x1:0,y1:0,x2:2,y2:0,stop:0 #FFD801,stop:1  #FFD801); }")
            elif Status_Value > 80:
                Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Progress"].setStyleSheet("QProgressBar { border: 0px solid grey; border-radius: 0px; background-color: gray; text-align: center;}QProgressBar::chunk {background:QLinearGradient(x1:0,y1:0,x2:2,y2:0,stop:0 red,stop:1  red); }")




            Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Progress"].setValue(Status_Value)
            Page_Info["QLabel_Status_List"][Status["Status_Sign"]]["Value"].setText(str(Status_Value) + '%')


        # PP(("我是全局的",self.Page_Info["Sentinel_Service"]))
        PP(Page_Info)







    def Open_Dialog(self,Dialog_Info):
        PJ(Dialog_Info)
        screen_pos = self.mapToGlobal(self.rect().center())

        # popup = Utils_PopupDialog.PopupDialog_Normal(self,Dialog_Info["Dialog_Info"])
        # popup.resize(100,30)
        # # 设置弹出窗口的位置
        # popup.move(screen_pos - QtCore.QPoint(popup.width()-48, popup.height() // 4 + 68))
        # popup.show()

        # QLabel_Dialog =Utils_PopupDialog.Utils_Dialog_Normal(self)
        QLabel_Dialog =Utils_PopupDialog.Utils_Dialog_Input(self,{"Dialog_Title":"输入所有数据"})
        # QLabel_Dialog.setStyleSheet(
        #     """""")
        QLabel_Dialog.setFont(QtGui.QFont("Microsoft YaHei", 20))
        QLabel_Dialog.resize(348,170)
        QLabel_Dialog.move(20,170)
        QLabel_Dialog.Signal_Result.connect(self.PAGE_HANDLE_RESULT)
        # __QLabel_Dialog.setMinimumSize(348,170)
        # __QLabel_Dialog.setMaximumSize(348, 170)
        # QLabel_Dialog.move(screen_pos - QtCore.QPoint(QLabel_Dialog.width()-48, QLabel_Dialog.height() // 4 + 68))
        QLabel_Dialog.show()

    # def PAGE_HANDLE_RESULT(self, Result):


            # self.QButton_Status_Update.show()





    # ---------------------------------------------------------------------------------------- 重写绘制事件
    # def paintEvent(self, event):
    #     pass
    #     # self.painter = QtGui.QPainter(self.QLabel_Content)
    #     # self.pen = QtGui.QPen(Qt.green, 2, Qt.SolidLine)  # 设置画笔颜色、宽度和样式
    #     # self.painter.setPen(self.pen)
    #     #
    #     # # 设置直线的起点和终点
    #     # start_point = QtCore.QPoint(150, 50)
    #     # end_point = QtCore.QPoint(350, 150)
    #     #
    #     # # 绘制直线
    #     # self.painter.drawLine(start_point, end_point)


    # ---------------------------------------------------------------------------------------- 重写绘制事件+背景颜色设置
    def paintEvent(self, event):
        pass
        # painter = QtGui.QPainter(self)
        # # gradient = QLinearGradient(self.rect().topRight(), self.rect().bottomLeft())
        # gradient = QtGui.QLinearGradient(self.rect().bottomLeft(),self.rect().topLeft())
        # # gradient.setColorAt(0, QtGui.QColor("#87cefa"))
        # gradient.setColorAt(0, QtGui.QColor("#000000"))
        # gradient.setColorAt(1, QtGui.QColor("#FFFFFF"))
        # painter.fillRect(self.rect(), gradient)
        # painter.end()
    # ---------------------------------------------------------------------------------------- 窗口变化事件
    def resizeEvent(self, event: QtCore.QEvent) -> None:
        super().resizeEvent(event)
        # 每次窗口大小改变时更新按钮的位置
        self.Update_Resize()

    def Update_Resize(self):
        pass














class QLabel_Click_Icon(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        # self.QLabel_Info = args[0]
        self.setMouseTracking(True)  # 启用鼠标追踪
        self._is_hovered = False
        # , "Size": 98
        # self.setAlignment(QtCore.Qt.AlignCenter)
        # # 创建两个 QLabel
        # self.QLabel_Icon = QtWidgets.QLabel( self)
        # self.QLabel_Icon.setAlignment(QtCore.Qt.AlignBottom |QtCore.Qt.AlignHCenter)
        # font_awesome_icon = icon(self.QLabel_Info["Icon"], color='#ffffff', size=self.QLabel_Info["Size"])
        # self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"], self.QLabel_Info["Size"]))
        # self.QLabel_Icon .setStyleSheet('''QLabel{background:transparent;border:0px solid #002040;transform: rotate(90deg)}''')
        # self.QLabel_Name = QtWidgets.QLabel(self.QLabel_Info["Name"], self)
        # self.QLabel_Name.setAlignment(QtCore.Qt.AlignTop | QtCore.Qt.AlignHCenter)
        # self.QLabel_Name.setStyleSheet('background:transparent;border:0px solid #002040;color:#ffffff')
        #
        # # 创建一个垂直布局
        # QVBoxLayout = QtWidgets.QVBoxLayout(self)
        # QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        # QVBoxLayout.setSpacing(0)
        #
        # # 将两个 QLabel 添加到垂直布局中
        # QVBoxLayout.addWidget(self.QLabel_Icon,3)
        # QVBoxLayout.addWidget(self.QLabel_Name,1)

    def enterEvent(self, event: QtCore.QEvent) -> None:
        self._is_hovered = True
        # self.setPalette(self.hover_palette)  # 设置悬停调色板
        # font_awesome_icon = icon(self.QLabel_Info["Icon"], color='#6495ed', size=self.QLabel_Info["Size"])
        #
        # # QLabel_Service_AI = QLabel_Click_Icon({"Icon": 'fa.viacoin', 'Name': 'AI模块',"Size":78}, self.QLabel_Status)
        # # 创建一个变换对象
        #
        #
        # self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"]+10, self.QLabel_Info["Size"]+10))
        # self.QLabel_Name.setStyleSheet('background:transparent;color:#ffffff;border:0px solid #002040;color:#6495ed')



        self.update()  # 更新显示

    def leaveEvent(self, event: QtCore.QEvent) -> None:
        self._is_hovered = False
        # font_awesome_icon = icon(self.QLabel_Info["Icon"], color='#ffffff', size=self.QLabel_Info["Size"])
        # self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"], self.QLabel_Info["Size"]))
        # self.QLabel_Name.setStyleSheet('background:transparent;border:0px solid #002040;color:#ffffff')
        # self.update()  # 更新显示

    def mousePressEvent(self, event):
        # font_awesome_icon = icon(self.QLabel_Info["Icon"], color='red', size=108)
        # self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(132, 132))
        # self.QLabel_Name.setStyleSheet('background:transparent;color:red;border:0px solid #002040;')

        if event.button() == QtCore.Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)




class QPushButton_RotatingIcon(QtWidgets.QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置图标
        # self.icon_name = 'fa.spinner'
        self.icon_name = 'ri.refresh-line'
        self.icon_color = 'blue'
        self.original_pixmap = self.create_icon_pixmap()
        self.setIcon(QtGui.QIcon(self.original_pixmap))
        self.setIconSize(self.original_pixmap.size())

        # 旋转定时器
        self.rotation_timer = QTimer(self)
        self.rotation_timer.timeout.connect(self.update_icon_rotation)
        self.rotation_angle = 0
        self.rotation_timer.start(100)  # 每100毫秒更新一次

    def create_icon_pixmap(self):
        # 使用 qtawesome 创建图标
        icon = qta.icon(self.icon_name, color=self.icon_color)
        pixmap = icon.pixmap(QtCore.QSize(32, 32))  # 根据需要调整大小
        return pixmap

    def update_icon_rotation(self):
        # 更新图标的旋转
        # PP("11")
        self.rotation_angle += 10  # 每次旋转10度
        if self.rotation_angle >= 360:
            self.rotation_angle = 0

            # 旋转图标
        pixmap = self.original_pixmap.transformed(QtGui.QTransform().rotate(self.rotation_angle))
        self.setIcon(QtGui.QIcon(pixmap))




class Page_Mission_Execute(QtCore.QThread):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.CONFIG()
        self.Type = args[0]

    def run(self):
        # PP("Page_Update_Status",9)
        try:
            Method = getattr(self, f'{self.Type}', None)
            if Method and callable(Method):
                return Method()
            else:
                return f"{self.__class__.__name__} No Such Method: {self.Type}"
        except Exception as e:
            self.Signal_Result.emit({"Error": e})

    def Worker_Info(self):
        try:
            self.CacheStack_Data = {}
            self.CacheStack_Data['Method'] = "Key_Get"
            self.CacheStack_Data['Data'] = ["Worker_Info"]
            Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
            Worker_Info = json.loads(Response.text)
            # PJ(Worker_Info)
            self.Signal_Result.emit({"Worker_Info": Worker_Info})
        except:
            PT()
            self.Signal_Result.emit({"Worker_Info": "Failed"})




        pass

    def Mission_Update_Status(self):
        PP("Mission_Update_Status", 3)
        # PJ(CServer())

        # Server_Status_Info  =
        self.Result["Server_Status_Info"]    = CServer()
        self.Result["Status"]                = "Success"
        self.Result["Command"]               = "Page_Update"
        self.Signal_Result.emit(self.Result)
        # return self.Result




    def CONFIG(self):
        self.Result = {'Status': 'Failed'}








            # Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            # # PI(eval(Response.text))
            # Sentinel_Service = eval(Response.text)
            # if Sentinel_Service["Status"] == "Active":
            #     Page_Info["Sentinel_Service"] = "Success"

        #
        # try:
        #     self.CacheStack_Data = {}
        #     self.CacheStack_Data['Method'] = "Key_Get"
        #     self.CacheStack_Data['Data'] = ["System_Status"]
        #     Response = requests.post('http://127.0.0.1:9333/Worker_CacheStack_Service', json=self.CacheStack_Data,proxies={"http": None, "https": None, })
        #     System_Status = json.loads(Response.text)
        #     PJ(System_Status)
        #     self.Signal_Result.emit({"System_Status": System_Status})
        # except:
        #     PT()
        #     self.Signal_Result.emit({"System_Status": "Failed"})



            # Page_Info["Sentinel_Service"] = "Failed"






        # global Page_Info
        # while True:
        #     time.sleep(10)
        #     PP("Core Update")

            # self.Proxies =
            # Response
            # try:
            #     Response  = requests.get("http://127.0.0.1:9333/System_Status", proxies={"http": None,"https": None,})
            #     # PI(eval(Response.text))
            #     Sentinel_Service = eval(Response.text)
            #     if Sentinel_Service["Status"] == "Active":
            #         Page_Info["Sentinel_Service"] = "Success"
            # except:
            #     Page_Info["Sentinel_Service"] = "Failed"

            # System_Status =  SCKeYGet({"Key":"System_Status"})
            # PJ(System_Status)
            # try:
            #     pass
            #     # Page_Info["System_Status"] = SCKey_Get("System_Status")
            # except:
            #     PT()

            # System_Status









if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Home()
    Page_Widget.show()
    sys.exit(app.exec())
