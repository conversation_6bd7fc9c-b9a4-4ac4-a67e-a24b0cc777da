# -*- coding: utf-8 -*-
import time,os,sys,cv2
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget,QPushButton,QSlider,QHBoxLayout,QFileDialog
from PySide6.QtGui import QImage, QPixmap,QIcon,QMouseEvent
from PySide6.QtCore import QTimer, Qt,QUrl,QPropertyAnimation, QEasingCurve
from PySide6.QtCore import QThread, Signal, Slot
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from PySide6.QtMultimediaWidgets import QGraphicsVideoItem
from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, <PERSON><PERSON>ush<PERSON>utton,
                               QGraphicsView, QGraphicsS<PERSON>, QGraphicsProxyWidget,
                               QLabel, QSlider, QHBoxLayout)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QPoint, QUrl,Signal
from PySide6.QtGui import QColor, QPalette, QIcon
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

import qtawesome
sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Component")
import Component_Common



class VideoWidget11(QGraphicsVideoItem):
    Signal_Result = Signal(dict)
    def __init__(self, parent=None):
        super().__init__(parent)

    def mousePressEvent(self, event):
        # 调用父类的处理以保持正常功能
        super().mousePressEvent(event)
        # 执行自定义点击处理
        if event.button() == Qt.LeftButton:
            print('')
            # self.Parent.handle_video_click()  # 调用主窗口的处理函数
            self.Signal_Result.emit({})

class Component_Player_Multimedia(QtWidgets.QWidget):
    _first_show = True

    def __init__(self, parent=None,*args):
        super().__init__(parent)
        self.QWidget_parent = parent

        self.setWindowTitle("Video Player")
        # self.resize(800, 600)

        self.is_Main_Title = False
        self.is_Progress_Seeking = False
        self.is_Control = False
        # self.media_player.play()
        self.initUI()
    def initUI(self):

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # self.top_label = QtWidgets.QLabel(r"D:\45M.mp4")
        # self.top_label = QtWidgets.QWidget()
        # self.top_label.setFixedHeight(28)
        # # self.top_label.setAutoFillBackground(True)
        # # self.top_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        # self.top_label.setStyleSheet("""
        #     color: white;
        #     background-color: rgba(0, 0, 0, 0.3);
        #     padding: 4px 8px;
        #     font-size: 14px;
        # """)

        # main_layout.addWidget(self.top_label)

        # GraphicsView
        self.view = QtWidgets.QGraphicsView()
        self.view.setFrameShape(QtWidgets.QGraphicsView.NoFrame)
        self.view.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.view.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scene = QtWidgets.QGraphicsScene(self)


        self.view.setScene(self.scene)
        main_layout.addWidget(self.view)

        # 绿色背景
        self.QWidget_Background = QWidget()
        self.QWidget_Background.setAutoFillBackground(True)
        self.QWidget_Background.setStyleSheet("background-color: rgba(0, 0, 0, 1);")

        # 播放器
        self.media_player = QMediaPlayer(self)
        self.audio_output = QAudioOutput(self)
        self.media_player.setAudioOutput(self.audio_output)

        # 视频项
        self.video_item = VideoWidget11()
        # self.video_item.setStyleSheet("border:none")

        self.video_item.Signal_Result.connect(self.Toggle_Colrol_Tools)

        self.video_item.setSize(self.size())
        self.video_item.setAspectRatioMode(Qt.IgnoreAspectRatio)  # 铺满，不保持比例
        self.video_item.setPos(0, 0)  # 左上角对齐


        self.media_player.setVideoOutput(self.video_item)

        # 红色控制面板
        self.QWidget_Control = QWidget()
        self.QWidget_Control.setFixedHeight(158)
        self.QWidget_Control.setStyleSheet("background-color: rgba(0, 0, 0, 0.3);")
        self.setup_controls()

        # 加入场景
        # self.top_proxy = self.scene.addWidget(self.top_label)
        green_proxy = self.scene.addWidget(self.QWidget_Background)
        self.scene.addItem(self.video_item)
        red_proxy = self.scene.addWidget(self.QWidget_Control)

        # Z 顺序：背景 < 视频 < 控制面板
        # self.top_proxy.setZValue(0)
        green_proxy.setZValue(1)
        self.video_item.setZValue(2)
        red_proxy.setZValue(3)

        # 初始位置
        self.red_proxy = red_proxy
        self.red_proxy.setPos(0, self.height())

        # self.top_proxy.setPos(0, 0)

        # 动画
        self.animation = QPropertyAnimation(self.red_proxy, b"pos")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutQuad)

        # 示例视频：请换成自己的文件
        self.media_player.setSource(QUrl.fromLocalFile(r"http://42.193.47.16:9311/rtp/34020000001320000001_34020000001320000001/hls.m3u8?originTypeStr=rtp_push"))
        # self.media_player.setSource(QUrl.fromLocalFile(r"https://vjs.zencdn.net/v/oceans.mp4"))




    def setup_controls(self):
        __QVBoxLayout = QVBoxLayout(self.QWidget_Control)
        QLabel_Config = QtWidgets.QLabel(r"https://vjs.zencdn.net/v/oceans.mp4")
        QLabel_Config.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        QLabel_Config.setStyleSheet("color:white;background:transparent;margin-left:20px;margin-right:10px;font-size:18px;font-weight:bold;font-family:Arial")
        QLabel_Control = QtWidgets.QLabel()
        QHBoxLayout_Control = QHBoxLayout(QLabel_Control)





        self.Slider_Progress_Player = ClickableSlider(Qt.Horizontal)
        self.Slider_Progress_Player.setStyleSheet("""
                    QSlider::groove:horizontal {
                        height: 4px;
                        border-radius: 2px;
                        background:rgba(255, 255, 255,0.3);
                    }

                    QSlider::sub-page:horizontal {
                        height: 4px;
                        border-radius: 2px;
                        background: rgba(81, 196, 211,0.8);
                    }

                    QSlider::add-page:horizontal {
                        background: transparent;
                    }

                    QSlider::handle:horizontal {
                        width: 16px;
                        height: 16px;
                        margin: -6px 0;
                        border-radius: 8px;
                        background: rgba(81, 196, 211,0.8);
                    }

                    QSlider::handle:horizontal:pressed {
                        background:  background: transparent;
                    }
                """)
        self.Slider_Progress_Player.sliderMoved.connect(self.set_position)
       


        self.QLabel_Time = QLabel("00:00 / 00:00")
        self.QLabel_Time.setStyleSheet("color:white;background:transparent")

        btn_bar = QWidget()
        hbox = QHBoxLayout(btn_bar)

        self.Button_Play = Component_Common.Component_Common_QLabel_Click()
        self.Button_Play.setFixedSize(48,30)
        self.Button_Play.setPixmap(qtawesome.icon("fa6.circle-play", color='white', size=28).pixmap(28, 28))
        self.Button_Play.clicked.connect(self.toggle_playback)

        self.Button_Stop = Component_Common.Component_Common_QLabel_Click()
        self.Button_Stop.setFixedSize(48, 30)
        self.Button_Stop.setPixmap(qtawesome.icon("fa5.stop-circle", color='white', size=28).pixmap(28, 28))
        self.Button_Stop.clicked.connect(self.media_player.stop)

        # self.stop_btn = QPushButton()
        # self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop"))
        # self.stop_btn.clicked.connect(self.media_player.stop)
        #
        # self.vol = QSlider(Qt.Horizontal)
        # self.vol.setRange(0, 100)
        # self.vol.setValue(50)
        # self.vol.valueChanged.connect(lambda v: self.audio_output.setVolume(v / 100))
        #
        # self.toggle_btn = QPushButton("Show Controls")
        # self.toggle_btn.clicked.connect(self.Toggle_Colrol_Tools)

        # hbox.addWidget(self.Button_Play)
        # hbox.addWidget(self.Button_Stop)
        # hbox.addWidget(QLabel("Volume:"))
        # hbox.addWidget(self.vol)
        # hbox.addWidget(self.toggle_btn)

        QHBoxLayout_Control.addWidget( self.Button_Play)
        QHBoxLayout_Control.addWidget( self.Slider_Progress_Player)
        QHBoxLayout_Control.addWidget( self.Button_Stop)
        QHBoxLayout_Control.addWidget(self.QLabel_Time)





        # __QHBoxLayout.addWidget(btn_bar)

        __QVBoxLayout.addWidget(QLabel_Config,1)
        __QVBoxLayout.addWidget(QLabel_Control, 1)
        # 播放器信号
        self.media_player.positionChanged.connect(self.update_pos)
        self.media_player.durationChanged.connect(self.update_dur)
        self.media_player.playbackStateChanged.connect(self.update_btn)


    def showEvent(self, event):
        super().showEvent(event)
        if self._first_show:
            self._first_show = False
            # self.toggle_red_widget()   # 第一次真正显示时再弹出
            self.Toggle_Colrol_Tools()

    def resizeEvent(self, event):
        # 让绿色背景、视频、控制面板全部铺满
        self.QWidget_Background.resize(self.size())
        self.video_item.setSize(self.size())
        if self.is_Control:
            self.red_proxy.setPos(0, self.height() - self.QWidget_Control.height())
        else:
            self.red_proxy.setPos(0, self.height())
        self.QWidget_Control.resize(self.width(), self.QWidget_Control.height())
        super().resizeEvent(event)

        # ✅ 关键：设置
        # top_label
        # 的宽度为窗口宽度，高度固定
        # self.top_label.setFixedWidth(self.width())
        # self.top_proxy.widget().setFixedWidth(self.width())
        #
        # # ✅ 初始位置（隐藏状态）
        # if not self.is_Control:
        #     self.top_proxy.setPos(0, -self.top_label.height())
        #     self.red_proxy.setPos(0, self.height())
        #
        # else:
        #     self.top_proxy.setPos(0, 0)
        #     self.red_proxy.setPos(0, self.height() - self.QWidget_Control.height())

    def Toggle_Colrol_Tools(self):
        # if self.is_Control:
        #     self.top_label.setVisible(False)
        # else:
        #     self.top_label.setVisible(True)


        target_y = self.height() - self.QWidget_Control.height() if not self.is_Control else self.height()
        self.animation.setStartValue(self.red_proxy.pos())
        self.animation.setEndValue(QPoint(0, target_y))
        self.animation.start()




        self.is_Control = not self.is_Control
        # self.toggle_btn.setText("Hide Controls" if self.is_Control else "Show Controls")
        if self.is_Main_Title:pass
            # self.QWidget_parent.Set_Hide_Main_Title()
        else:pass

        self.is_Main_Title = not self.is_Main_Title
    # ---------- 播放器辅助 ----------
    def toggle_playback(self):
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()


    def set_position(self, position):
        # 根据滑块位置(0-100)设置媒体位置
        if self.media_player.duration() > 0:
            media_position = position * self.media_player.duration() // 100
            self.media_player.setPosition(media_position)
            self.is_Progress_Seeking = True

    def update_pos(self, pos):
        if self.media_player.duration():
            self.Slider_Progress_Player.setValue(int(pos * 100 / self.media_player.duration()))
            self.QLabel_Time.setText(f"{self.fmt(pos)} / {self.fmt(self.media_player.duration())}")

    def update_dur(self, dur):
        self.QLabel_Time.setText(f"{self.fmt(self.media_player.position())} / {self.fmt(dur)}")

    def update_btn(self, state):
        if state == QMediaPlayer.PlayingState :
            self.Button_Play.setPixmap(qtawesome.icon("fa6.circle-play", color='white', size=28).pixmap(28, 28))
        else:
            self.Button_Play.setPixmap(qtawesome.icon("fa6.circle-pause", color='white', size=28).pixmap(28, 28))

        # self.Button_Play.setIcon(QIcon.fromTheme(
        #     "media-playback-pause" if state == QMediaPlayer.PlayingState else "media-playback-start"))

    @staticmethod
    def fmt(ms):
        s = ms // 1000
        return f"{s // 60:02d}:{s % 60:02d}"
class Component_Player_Multimedia1(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, *args, parent=None):
        super().__init__()
        self.Video_Info = args[0]
        print(self.Video_Info)
        self.initUI()
    def initUI(self):
        # 创建主控件
        # self.central_widget = QWidget()
        # self.setCentralWidget(self.central_widget)

        # 主布局
        # 主布局
        self.main_layout = QVBoxLayout(self)

        # 视频显示部件
        self.video_widget = QVideoWidget()
        self.main_layout.addWidget(self.video_widget, 1)

        # 媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        self.media_player.setVideoOutput(self.video_widget)

        # 控制区域
        self.control_widget = QWidget()
        self.control_widget.setFixedHeight(188)  # 设置固定高度188px
        self.control_widget.setStyleSheet("background-color: #333;")
        self.control_layout = QVBoxLayout(self.control_widget)

        # 使用自定义的可点击进度条
        self.Slider_Progress_Player_slider = ClickableSlider(Qt.Horizontal)
        self.Slider_Progress_Player_slider.setStyleSheet("""
                    QSlider::groove:horizontal {
                        height: 4px;
                        border-radius: 2px;
                        background: rgba(255, 255, 255, 100);
                    }

                    QSlider::sub-page:horizontal {
                        height: 4px;
                        border-radius: 2px;
                        background: rgba(81, 196, 211,0.8);
                    }

                    QSlider::add-page:horizontal {
                        background: transparent;
                    }

                    QSlider::handle:horizontal {
                        width: 16px;
                        height: 16px;
                        margin: -6px 0;
                        border-radius: 8px;
                        background: rgba(81, 196, 211,0.8);
                    }

                    QSlider::handle:horizontal:pressed {
                        background: rgba(81, 196, 211,0.8);
                    }
                """)

        self.Slider_Progress_Player_slider.setRange(0, 100)
        self.Slider_Progress_Player_slider.sliderMoved.connect(self.set_position)
        self.is_Progress_Seeking = False

        # 控制按钮
        self.button_widget = QWidget()
        self.button_layout = QHBoxLayout(self.button_widget)

        # 播放/暂停按钮
        self.play_button = QPushButton()
        self.play_button.setIcon(QIcon.fromTheme("media-playback-start"))
        self.play_button.clicked.connect(self.play_pause)

        # 停止按钮
        self.stop_button = QPushButton()
        self.stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_button.clicked.connect(self.stop)

        # 音量控制
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.set_volume)

        # 文件打开按钮
        self.open_button = QPushButton("打开文件")
        self.open_button.clicked.connect(self.open_file)

        # 当前时间/总时间标签
        self.time_label = QLabel("00:00 / 00:00")
        # 隐藏/显示按钮
        self.toggle_button = QPushButton("隐藏控制面板", self)
        self.toggle_button.setFixedWidth(100)
        self.toggle_button.setFixedHeight(25)
        self.toggle_button.clicked.connect(self.toggle_control_panel)
        # 添加按钮到布局
        self.button_layout.addWidget(self.toggle_button)
        self.button_layout.addWidget(self.play_button)
        self.button_layout.addWidget(self.stop_button)
        self.button_layout.addWidget(QLabel("音量:"))
        self.button_layout.addWidget(self.volume_slider)
        self.button_layout.addWidget(self.open_button)
        # self.button_layout.addWidget(self.time_label)

        # 添加控件到控制区域

        self.control_layout.addWidget( self.Slider_Progress_Player_slider)
        self.control_layout.addWidget(self.time_label)
        self.control_layout.addWidget(self.button_widget)

        # 添加控制区域到主布局
        self.main_layout.addWidget(self.control_widget)

        # 连接信号
        self.media_player.positionChanged.connect(self.update_position)
        self.media_player.durationChanged.connect(self.update_duration)
        self.media_player.playbackStateChanged.connect(self.update_buttons)
        # 初始化动画
        self.animation = QPropertyAnimation(self.control_widget, b"pos")
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.setDuration(300)  # 动画持续时间300ms
        # 初始化音量
        self.audio_output.setVolume(0.5)

        # 控制面板状态
        self.control_visible = True

    def open_file(self):
        file_dialog = QFileDialog(self)
        file_dialog.setNameFilter("视频文件 (*.mp4 *.avi *.mkv *.mov *.wmv)")
        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            self.media_player.setSource(QUrl.fromLocalFile(file_path))
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(True)

    def play_pause(self):
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()

    def stop(self):
        self.media_player.stop()

    def set_position(self, position):
        # 根据滑块位置(0-100)设置媒体位置
        if self.media_player.duration() > 0:
            media_position = position * self.media_player.duration() // 100
            self.media_player.setPosition(media_position)
            self.is_Progress_Seeking = True

    def set_volume(self, volume):
        self.audio_output.setVolume(volume / 100)

    def update_position(self, position):
        # 更新进度条位置(如果不是用户正在拖动)
        if not self.is_Progress_Seeking and self.media_player.duration() > 0:
             self.Slider_Progress_Player_slider.setValue(position * 100 // self.media_player.duration())

        # 更新当前时间显示
        current_time = self.format_time(position)
        duration = self.format_time(self.media_player.duration())
        self.time_label.setText(f"{current_time} / {duration}")

        self.is_Progress_Seeking = False

    def update_duration(self, duration):
        # 更新总时间显示
        if duration > 0:
            current_time = self.format_time(self.media_player.position())
            total_time = self.format_time(duration)
            self.time_label.setText(f"{current_time} / {total_time}")

    def update_buttons(self, state):
        # 根据播放状态更新按钮图标
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(QIcon.fromTheme("media-playback-pause"))
        else:
            self.play_button.setIcon(QIcon.fromTheme("media-playback-start"))

    def format_time(self, milliseconds):
        # 将毫秒转换为 MM:SS 格式
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"


    def toggle_control_panel(self):
        """切换控制面板的显示/隐藏状态"""
        if self.control_visible:
            # 隐藏动画 - 向下移动控制面板的高度
            self.animation.setStartValue(self.control_widget.pos())
            self.animation.setEndValue(self.control_widget.pos() +
                                       self.control_widget.rect().bottomRight() -
                                       self.control_widget.rect().topLeft())
            self.animation.start()
            self.toggle_button.setText("显示控制面板")
            self.toggle_button.move(self.width() - 110, self.height() - 25)
        else:
            # 显示动画 - 向上移动控制面板的高度
            self.animation.setStartValue(self.control_widget.pos())
            self.animation.setEndValue(self.control_widget.pos() -
                                       (self.control_widget.rect().bottomRight() -
                                        self.control_widget.rect().topLeft()))
            self.animation.start()
            self.toggle_button.setText("隐藏控制面板")
            self.toggle_button.move(self.width() - 110, self.height() - 213)

        self.control_visible = not self.control_visible








class ClickableSlider(QSlider):
    def __init__(self, orientation):
        super().__init__(orientation)

        self.setMouseTracking(True)

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            # 计算点击位置对应的值
            val = self.minimum() + ((self.maximum() - self.minimum()) * event.position().x()) / self.width()
            self.setValue(int(val))
            # 发送滑块移动信号
            self.sliderMoved.emit(int(val))
        super().mousePressEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    Window = Component_Player_Multimedia({"Video_Source":"rtsp://admin:csc888888!@192.168.123.57:554/Streaming/Channels/101"})
    Window.show()
    sys.exit(app.exec())