import React, { useEffect, useRef, useState } from 'react';
import { throttle } from 'lodash-es';
import { ReactElement } from 'react';

interface CustomBackTopProps {
  threshold?: number;
}

const CustomBackTop: React.FC<CustomBackTopProps> = ({ threshold = 300 }): ReactElement | null => {
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const container = document.querySelector('.ant-layout-content') as HTMLDivElement;
    if (!container) return;

    contentRef.current = container;

    const handleScroll = () => {
      const scrollTop = container.scrollTop;
      setShow(scrollTop > threshold);
    };

    const throttledScroll = throttle(handleScroll, 100);
    container.addEventListener('scroll', throttledScroll);

    // 初始检查一次
    handleScroll();

    return () => {
      container.removeEventListener('scroll', throttledScroll);
      throttledScroll.cancel();
    };
  }, [threshold]);

  const scrollToTop = () => {
    const container = contentRef.current;
    if (container) {
      container.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return show ? (
    <div
      onClick={scrollToTop}
      style={{
        position: 'fixed',
        bottom: 48,
        right: 48,
        zIndex: 999,
        width: 40,
        height: 40,
        lineHeight: '40px',
        textAlign: 'center',
        color: '#fff',
        // backgroundColor: '#1890ff',
        borderRadius: '50%',
        cursor: 'pointer',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        // backgroundColor: 'rgba(24, 144, 255, 0.7)', // 调整透明度
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        opacity: 0.8,
      }}
    >
      ↑
    </div>
  ) : null;
};

export default CustomBackTop;