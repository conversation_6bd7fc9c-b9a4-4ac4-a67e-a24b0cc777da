import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,Cascader,Checkbox,CheckboxProps,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "../Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "../Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps, } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");

export class SystemControl_Preview_Efficient extends ClassicPreset.Control {
    
    constructor(
      public UUID_Source: string, 
      public Method: string, 
      public Type: string, 
      public Score: string, 
      public Direction: string, 
      public Theme: string, 
      public Object: string, 
      public Image: string, 
      public Bidding: string, 
      public Keyword: string, 
      public Title: string, 
      public Content: string, 

      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {

      const Score_Info = (key: string) => Rumor?.[key] || "未知";
      this.UUID_Source              = `【UUID】:${Score_Info("UUID")}  【数源】:${Score_Info("Source")}`;
      this.Method                   = Score_Info("Method")
      this.Type                     = Score_Info("Type")
      this.Score                    = Score_Info("Score")
      this.Direction                = Score_Info("Direction")
      this.Theme                    = Score_Info("Theme")
      this.Object                   = Score_Info("Object");
      this.Image                    = `【图片】:${Score_Info("Image")}`;
      this.Bidding                  = `【中标】:${Score_Info("Bidding")}`;
      this.Keyword                  = `【关键词】:\n${Score_Info("Keyword")}`;
      this.Title                    = (Score_Info("Title") || "").slice(0, 30)
      this.Content                  = `\n${Score_Info("Content")}`;
      // this.Content                  = `\n${Score_Info("Content")}`;
      
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

export  function Node_System_Preview_Efficient(props: { data:SystemControl_Preview_Efficient}) {
  const [value, setValue] = useState(3);

  const [tags, setTags] = useState(['Custom Tag 1', 'Custom Tag 2']);
  // this.Tags.push(props.data.Keyword)
  // 主题配置
  const desc = ['', '', '', '', ''];
const theme: ThemeConfig = {
components: {
  Carousel: {
    // 核心配置
    motionDurationSlow: "0.1s",      // 文字颜色
    
  },

  Rate:{
    starBg:"#000",
    starSize:15,
  }
},
};


return (
  
  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
       <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
       <Badge.Ribbon text="Token" placement="start" color="blue">
       <Layout   style={{height:150,width:450,  borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"center"}}  > 
              <Layout style={{marginTop:40,marginLeft:8,background:"rgba(117, 101, 207, 0.0)"}}>
                  {/* <Component_Keys 
                  
                  initialTags={tags} 
                  onTagsChange={(newTags) => {
                    console.log('Tags changed:', newTags);
                    setTags(newTags);
                  }} 
                  /> */}


                  {props.data.Keyword} 


              </Layout>
       </Layout>
             
        </Badge.Ribbon>





                                                                                                                                                                                               
    </Space>

    <Space direction="horizontal" size="middle" style={{ width: '100%' }} > 
      <Badge.Ribbon text="方向" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Direction}</span></Layout></Badge.Ribbon>
      <Badge.Ribbon text="主题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Theme}</span></Layout></Badge.Ribbon>
    
   </Space>

   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
      <Badge.Ribbon text="分类" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Type}</span></Layout></Badge.Ribbon>
      <Badge.Ribbon text="课题" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Object}</span></Layout></Badge.Ribbon>
    
   </Space>
   {/* <span style={{marginTop:8,marginRight:8,color:"white"}} >{props.data.Score}</span> */}

   {/* const [value, setValue] = useState(3);
  return (
    <Flex gap="middle" vertical>
      <Rate tooltips={desc} onChange={setValue} value={value} />{value ? <span>{desc[value - 1]}</span> : null}
      
    </Flex> */}
 <ConfigProvider theme={theme}>

   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
      <Badge.Ribbon text="评分" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", justifyItems:"conter"}}  >  <Rate disabled  count={5} defaultValue={3}  style={{marginTop:10,marginLeft:102,color:"white"}}/></Layout></Badge.Ribbon>
      <Badge.Ribbon text="结果" placement="start" color="#3e6ae1"><Layout   style={{height:40,width:217,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > <span style={{marginTop:8,marginRight:8,color:"white"}} >无效</span></Layout></Badge.Ribbon>
    
   </Space>
  </ConfigProvider>
   <Space direction="horizontal" size="middle" style={{ marginTop:-20, width: '100%' }} > 
        <Badge.Ribbon text="预览" placement="start" color="#3e6ae1">
          <Layout   style={{height:40,width:450,   borderRadius: 4,background:"#666",flex:"1", alignItems:"end",justifyItems:"conter"}}  > 
            <Cascader placeholder={<span style={{fontSize: 19 ,color:"white",marginLeft:140}}>结合上下文预览</span>} variant="filled"  style={{ height:40,width:450, alignItems: "center", justifyContent: "center", color:"white" }} />
          </Layout></Badge.Ribbon>
     </Space>
   <Space direction="horizontal" size="middle" style={{ marginTop:10, width: '100%' }} >
        <Badge.Ribbon text="内容" placement="start" color="#3e6ae1">
          <TextArea
                value= {props.data.Content} 
                onChange={(e) => {
                  const newValue = e.target.value;
 
                }}
                rows={7}
                style={{ background:"rgba(248, 240, 240, 0.3)",width: 450,color:"white" }}
              />
        </Badge.Ribbon>
        </Space>
      

    
  
  {/* <Badge.Ribbon text="Hippies">
    <Card title="Pushes open the window" size="small">
     
    </Card>
  </Badge.Ribbon> */}
  
  </Space>
);
}

