import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { SystemControl_Output,ButtonControl_Release,Node_Socket,ImageControl_Face,ButtonControl_More} from "./Node_Controls";
import { release } from "os";



export class Node_System_Component_Output extends ClassicPreset.Node<
  { [key in string]: ClassicPreset.Socket },
  { [key in string]: ClassicPreset.Socket },
  {
    [key in string]:
      | ButtonControl_Release
      | ButtonControl_More
      | SystemControl_Output
      | ImageControl_Face
      | ClassicPreset.Control
  }
> {

    private _label: string;
    width = 388;
    height = 188;
  
    constructor(Label: string,) {
      super(Label);
      this._label = Label;

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""));
      this.addControl("button_1", new ButtonControl_Release("下载", () => {}));
      // this.addControl("button_2", new ButtonControl_More("详情", () => {}));


       const textAreaControl = new SystemControl_Output(
              '【标题】:未知', // Label for the text area
              0, // Initial value


              (title) => {
                console.log('TextArea value changed:', title);
              }
            );
      this.addControl("Content", textAreaControl);


    }

   
    updateLabel(newLabel: string): void {
        this._label = newLabel;
        // this.updateUI(); // 假设有一个 updateUI 方法来触发 UI 更新
      }

}

