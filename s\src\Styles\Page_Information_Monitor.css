/* 信息监控页面样式 */

/* 视图切换按钮样式 - 去掉白色描边 */
.view-switch-btn {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.view-switch-btn:hover {
  border: none !important;
  box-shadow: none !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.view-switch-btn:focus {
  border: none !important;
  box-shadow: none !important;
}

/* 通用按钮无边框样式 */
.no-border-btn {
  border: none !important;
  box-shadow: none !important;
}

.no-border-btn:hover {
  border: none !important;
  box-shadow: none !important;
}

.no-border-btn:focus {
  border: none !important;
  box-shadow: none !important;
}

.no-border-btn:active {
  border: none !important;
  box-shadow: none !important;
}

/* 信息列表卡片样式 */
.info-card {
  margin-bottom: 16px;
  padding: 16px 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  position: relative;
}

/* 复选框和序号容器 */
.checkbox-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 序号样式 */
.item-number {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 标题链接样式 */
.article-title {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
  line-height: 24px;
}

/* 内容段落样式 */
.article-content {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 20px;
  padding-right: 120px;
}

/* 底部信息样式 */
.article-meta {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 操作图标容器 - 右下角 */
.action-icons {
  position: absolute;
  bottom: 16px;
  right: 20px;
  display: flex;
  gap: 12px;
}

/* 操作图标样式 */
.action-icon {
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.3s;
}

.action-icon:hover {
  color: #1890ff;
}

/* 标签样式调整 */
.emotion-tag {
  margin: 0;
}

.media-tag {
  margin: 0;
}
