
import sys
import numpy as np
import librosa
import pyaudio
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QSlider, QLabel, QPushButton
from PySide6.QtCore import Qt, QThread, Signal
from PySide6 import Qt<PERSON>ore, QtGui, QtWidgets

class Component_AudioNoise(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self, *args, parent=None):
        super().__init__()
        self.initUI()

    def initUI(self):
        __QVBoxLayout = QtWidgets.QHBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(0)

        Switch_Rotary_Noise_1 =Switch_Rotary_Noise()
        Switch_Rotary_Noise_2 =Switch_Rotary_Noise()
        Switch_Rotary_Noise_3 =Switch_Rotary_Noise()
        Switch_Rotary_Noise_4 =Switch_Rotary_Noise()





        __QVBoxLayout.addWidget(Switch_Rotary_Noise_1)
        __QVBoxLayout.addWidget(Switch_Rotary_Noise_2)
        __QVBoxLayout.addWidget(Switch_Rotary_Noise_3)
        __QVBoxLayout.addWidget(Switch_Rotary_Noise_4)





class Switch_Rotary_Noise(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # 创建 QVBoxLayout
        layout = QtWidgets.QVBoxLayout(self)

        # 创建 QDial
        self.dial = QtWidgets.QDial()

        # self.dial.setMaximumHeight(58)
        self.dial.setMinimumSize(58,58)
        self.dial.setMaximumSize(58,58)
        # self.dial.setMaximumWidth(58)
        self.dial.setWrapping(True)  # 设置为循环旋转
        self.dial.setMinimum(0)  # 最小值
        self.dial.setMaximum(100)  # 最大值
        self.dial.setValue(0)  # 初始值
        self.dial.valueChanged.connect(self.on_value_changed)  # 连接信号槽

        # 隐藏刻度线
        self.dial.setStyleSheet("""
            QDial {
                background-color: transparent;
                border: none;
            }
            QDial::groove {
                background-color: transparent;
                border: none;
            }
            QDial::handle {
                background-color: blue;
                border: none;
                width: 20px;
                height: 20px;
            }
        """)

        # 创建 QLabel 用于显示当前值
        self.label = QtWidgets.QLabel("0", self)
        self.label.setAlignment(Qt.AlignCenter)

        # 将 QDial 和 QLabel 添加到布局
        layout.addWidget(self.dial)
        layout.addWidget(self.label)

        # 设置窗口属性
        self.setWindowTitle("Rotary Switch")
        self.resize(300, 300)

    def on_value_changed(self, value):
        # 根据 QDial 的值更新 QLabel 的文本
        self.label.setText(str(value))


if __name__ == "__main__":
   pass