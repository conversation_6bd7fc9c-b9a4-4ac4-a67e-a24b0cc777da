body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/**
  新增整体样式
*/
html,body,div,span,applet,object,iframe,
h1,h2,h3,h4,h5,h6,p,blockquote,pre,
a,abbr,acronym,address,big,cite,
code,del,dfn,em,img,ins,kbd,
q,s,samp,small,strike,strong,
sub,sup,tt,var,b,u,i,center,
dl,dt,dd,ol,ul,li,
fieldset,form,label,legend,
table,caption,tbody,tfoot,thead,tr,th,td,
article,aside,canvas,details,embed,
figure,figcaption,footer,header,hgroup,
menu,nav,output,ruby,section,summary,
time,mark,audio,video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  box-sizing: border-box;
}

article,aside,details,figcaption,figure,
footer,header,hgroup,menu,nav,section {
  display: block;
}
body {
  line-height: 1;
}
ol,ul {
  list-style: none;
}
blockquote,q{
  quotes: none;
}

a,a:hover{
  color: inherit;
  text-decoration: none;
}
table{
  border-collapse: collapse;
  border-spacing: 0;
}
html,body{
  width: 100%;
  background-color: #f5f5f5;
}
.fl{
  float: left;
}
.fr{
  float:right;
}

.main-container{
  /**
   * min-height减64px 会有16px的距离出现整个界面滚动条
   */
  min-height: calc(100vh - 64px);
  background: linear-gradient(to right, #0a0f1c, #131a2b);
  /* min-height: calc(100vh - 80px); */
}

.main-container .ant-layout-content {
  /* margin: 0px !important; */
  /* 此两行设置固定content高度，使得侧边栏/顶部栏不会随着滚动条滚动 */
  overflow: auto;
  max-height: calc(100vh - 64px); 
}

.main-container .app-name{
  /* background-color: #001520; */
  text-align: center;
  color: #fff;
  line-height: 64px;
  font-weight: bold;
  font-size: 16px;
}

.main-container .ant-layout-content {
  /* background-color: #f5f5f5 !important; */
  background-color: #131a2b !important;
}

/* .main-container .ant-menu {
  height: calc(100vh - 64px) !important;
} */

/*  滚动条样式 */
/* &::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0);
  border-left: 1px solid rgba(0, 0, 0, 0);
}

&::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

&::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  background-clip: padding-box;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  min-height: 28px;
}

&::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
} */
/* 滚动条整体样式 */
&::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 滚动条轨道 */
&::-webkit-scrollbar-track {
  background-color: rgba(42, 49, 71, 0.6);
  border-radius: 4px;
}

/* 滚动条滑块 */
&::-webkit-scrollbar-thumb {
  background-color: #00F0FF;
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.4);
}

/* 滑块悬停效果 */
&::-webkit-scrollbar-thumb:hover {
  background-color: #00C8FF;
}


/* Content根路径样式*/
.content-container {
  background-color: #131a2b;
  min-height: calc(100vh - 64px);
  padding: 24px;
  overflow: auto;
}

.content-card {
  background-color: #1e263d;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #ffffff;
  /* margin-bottom: 20px; */
  min-height: calc(100vh - 112px); /* 设置最小高度 使得不出现滚动条的情况下 占满屏幕 */
}

.content-card h3 {
  color: #00f0ff;
  font-size: 18px;
  margin-bottom: 16px;
}

.content-card .ant-table {
  background-color: #1e263d;
  color: #ffffff;
}

.content-card .ant-btn-primary {
  background-color: #00f0ff;
  border-color: #00f0ff;
  color: #000000;
}

.content-card .ant-btn-primary:hover {
  background-color: #00c8ff;
  border-color: #00c8ff;
}


/* 定义通用滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #00C8FF rgba(42, 49, 71, 0.6);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: rgba(42, 49, 71, 0.6);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #00F0FF;
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.4);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #00C8FF;
}
