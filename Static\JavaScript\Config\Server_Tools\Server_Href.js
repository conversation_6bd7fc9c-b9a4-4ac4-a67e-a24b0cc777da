




class Server_Href {
    constructor(type,data){
        this.type = type
        try {
            this.data = data
        } catch (error) {
            this.data = {}
        }
        
    }
    run(data){
        // this.data['Href_URL'] = ''
        try {
            this.data = data
        } catch (error) {
            this.data = {}
        }
        console.log('key',  this.data )

        try {
           
            eval(`this.href_${this.type}()`);
        } catch (error) {
            return {'Error': error, "Type": this.type, "Data": this.data, "Status": "Failed"}
            
        }
    }
    // "top","right","fa fa-comments","success","animated fadeInRight","animated fadeOutRight",'','试用客户未开放该功能'
    href_Change (Href_URL=this.data['Href_URL']){
        console.log("run",this.type,Href_URL);
        try {
            window.location.href = Href_URL
        } catch (error) {
            type = ''
        }
        }


    }
