# Page_Information_Monitor API集成说明

## 概述

本文档说明了如何将`Page_Information_Monitor.tsx`组件与后端API集成，实现舆情监控数据的获取和筛选功能。

## 主要修改

1. **导入API请求Hook**
   ```typescript
   import { useServiceRequests } from '@/Core/Core_Control';
   ```

2. **添加API请求Hook**
   ```typescript
   const { AsyncTokenRequests } = useServiceRequests();
   ```

3. **添加筛选条件状态**
   ```typescript
   const [filterParams, setFilterParams] = useState({
     Public_Time: 'Today',
     Public_Time_List: [],
     Source_Type: ['All'],
     Emotion_Type: ['All'],
     Media_Type: [],
     Monitor_Type: [],
     Public_Area: '全国',
     Public_IP: '全国',
     Unit_Type: '全部',
     Article_Type: '全部',
     Show_Count: 'All'
   });
   ```

4. **实现API请求函数**
   ```typescript
   const loadArticleList = async () => {
     setLoading(true);
     try {
       const requestsData = {
         user_id: '',
         user_token: '',
         data_class: 'Sentiment',
         data_type: 'Service',
         data_methods: 'get_domestic_today_basic_list',
         data_argument: '{}',
         data_kwargs: {
           Table_condiction_param: filterParams,
           HanderDetailsEmotion: handlerDetailsEmotion,
           Case_UUID: caseUUID,
           HanderDetailsKeyword: handlerDetailsKeyword,
         }
       };

       const result = await AsyncTokenRequests(requestsData);
       
       if (result && result.Status === 'Success' && result.result?.items) {
         // 处理API返回的数据
         // ...
       }
     } catch (error) {
       // 错误处理
     } finally {
       setLoading(false);
     }
   };
   ```

5. **添加筛选条件处理函数**
   ```typescript
   // 处理时间范围变化
   const handleTimeRangeChange = (range: string) => {
     setFilterParams(prev => ({
       ...prev,
       Public_Time: range
     }));
   };

   // 处理媒体类型变化
   const handleMediaTypesChange = (types: string[]) => {
     setFilterParams(prev => ({
       ...prev,
       Source_Type: types
     }));
   };

   // 处理情感类型变化
   const handleEmotionTypesChange = (types: string[]) => {
     setFilterParams(prev => ({
       ...prev,
       Emotion_Type: types
     }));
   };
   
   // 其他处理函数...
   ```

6. **更新UI组件的事件处理**
   ```typescript
   // 时间范围按钮
   <Button onClick={() => handleTimeRangeClick('Today')}>今天</Button>

   // 媒体类型复选框
   <Checkbox.Group onChange={(values) => {
     setMediaTypes(values);
     handleMediaTypesChange(values);
   }}>
     // 复选框选项...
   </Checkbox.Group>

   // 情感类型复选框
   <Checkbox.Group onChange={(values) => {
     setEmotionTypes(values);
     handleEmotionTypesChange(values);
   }}>
     // 复选框选项...
   </Checkbox.Group>

   // 发布地区选择框
   <Select onChange={(value) => {
     setPublishArea(value);
     handlePublishAreaChange(value);
   }}>
     // 选项...
   </Select>
   ```

7. **实现保存参数功能**
   ```typescript
   const handleSave = async () => {
     try {
       const requestsData = {
         user_id: '',
         user_token: '',
         data_class: 'Sentiment',
         data_type: 'Service',
         data_methods: 'editor_detail_table_param',
         data_argument: '{}',
         data_kwargs: {
           Table_condiction_param: filterParams,
           Table_User_Alert_Case_Dict: {
             CASE_UUID: caseUUID,
             CASE_NAME: '',
             CASE_EXPLAIN: { KEYWORD_INFO: [] }
           }
         }
       };

       const result = await AsyncTokenRequests(requestsData);
       
       if (result && result.Status === 'Success') {
         message.success('筛选方案保存成功！');
       } else {
         message.error('筛选方案保存失败，请重试！');
       }
     } catch (error) {
       message.error('网络出错，保存失败！');
     }
   };
   ```

## API请求说明

### 1. 获取舆情数据

- **API方法**: `get_domestic_today_basic_list`
- **请求参数**:
  - `Table_condiction_param`: 筛选条件
  - `HanderDetailsEmotion`: 情感详情
  - `Case_UUID`: 方案UUID
  - `HanderDetailsKeyword`: 关键词详情

### 2. 保存筛选参数

- **API方法**: `editor_detail_table_param`
- **请求参数**:
  - `Table_condiction_param`: 筛选条件
  - `Table_User_Alert_Case_Dict`: 用户方案信息

## 数据处理流程

1. **初始化**: 组件加载时调用`loadArticleList`获取初始数据
2. **筛选条件变化**: 用户修改筛选条件时，更新`filterParams`状态
3. **执行筛选**: 点击筛选按钮时，调用`loadArticleList`重新获取数据
4. **保存参数**: 点击保存按钮时，调用`handleSave`保存筛选条件

## 注意事项

1. **Token处理**: 当前实现中，`user_id`和`user_token`为空字符串，实际使用时应从认证系统获取
2. **错误处理**: 已添加基本的错误处理，但可能需要根据实际情况进一步完善
3. **数据转换**: API返回的数据结构可能需要根据实际情况调整转换逻辑
4. **状态同步**: 确保UI状态与`filterParams`保持同步，避免不一致问题

## 后续优化建议

1. **分页处理**: 当前实现使用前端分页，可考虑改为后端分页以提高性能
2. **缓存机制**: 可添加数据缓存，减少重复请求
3. **加载状态**: 可为各个筛选条件添加独立的加载状态
4. **错误重试**: 添加请求失败自动重试机制
5. **数据预加载**: 可考虑预加载部分数据，提升用户体验
