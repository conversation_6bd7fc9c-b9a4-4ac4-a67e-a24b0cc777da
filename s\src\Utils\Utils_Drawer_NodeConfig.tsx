import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { PlusOutlined,SettingOutlined ,EditOutlined,CheckOutlined,CloseOutlined} from '@ant-design/icons';
import type { InputRef ,CollapseProps} from 'antd';
import { Layout,Flex, Input, Tag, theme, Tooltip,Tabs,Collapse,Space,Switch ,InputNumber} from 'antd';
import { Line } from '@ant-design/charts';
import { Column } from '@ant-design/plots';
import { useAuth } from '@/Core/Core_AuthContent';

const { Search } = Input;





























const DemoLine = () => {
    const data = [
      { year: '1991', value: 3 },
      { year: '1992', value: 4 },
      { year: '1993', value: 3.5 },
      { year: '1994', value: 5 },
      { year: '1995', value: 4.9 },
      { year: '1996', value: 6 },
      { year: '1997', value: 7 },
      { year: '1998', value: 9 },
      { year: '1999', value: 13 },
    ];
    const config = {
      data,
      xField: 'year',
      yField: 'value',
      point: {
        shapeField: 'square',
        sizeField: 4,
      },
      interaction: {
        tooltip: {
          marker: false,
        },
      },
      style: {
        lineWidth: 2,
      },
    };
    return <Line {...config} />;
  };
  

  const DATA = [1, 2, 3, 4, 5, 6, 7, 8,9,10,11,12];

  const DemoColumn = () => {
    const [data, setData] = React.useState(DATA);
  
    React.useEffect(() => {
      const time = setInterval(() => {
        setData([
          ...DATA.sort(() => {
            return Math.random() - 0.5;
          }),
        ]);
      }, 2000);
      return () => clearInterval(time);
    }, []);
  
    const config = {
      data: data.map((value) => ({
        index: value.toString(),
        value,
      })),
      xField: 'index',
      yField: 'value',
    };
    return <Column {...config} />;
  };


const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const itemsNest: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel nest panel',
    children: <p>{text}</p>,
  },
];
{/* <div style={{width:"100%",height:"100px",background:"red"}} ></div> */}

// var Label_Info = <Space.Compact>
//       <Search addonBefore="ID" placeholder="未知" allowClear />
//     </Space.Compact>defaultValue="mysite"

var Label_Info = <Flex gap="4px 0" wrap>
    <Input  addonBefore="ID" addonAfter={<SettingOutlined />}   placeholder="未知"   /> 
    <Input  addonBefore="ID" addonAfter={<EditOutlined />}   placeholder="未知"   /> 
    
    </Flex> 


const items: CollapseProps['items'] = [
  {
    key: '1',
    label: '详情',

    style:{
        pending:"0px",
    },
    children: Label_Info,
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: Label_Info,
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children:"------",
  },
];




const Utils_Drawer_NodeConfig: React.FC = () => {



  // const { user, updateUser } = SetContext(); // 获取用户数据和更新方法
  const { user  } = useAuth();
  const [name, setName] = useState(user?.username);
  const [age, setAge] = useState(user?.username.toString());
  
  
    


  


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // updateUser({ name, age: parseInt(age, 10) }); // 更新用户数据
  };


  const text_1 = (<Flex style={{ paddingInlineStart: 24 }}>




    <Space >
    <div>
        <h2>Edit Profile</h2>
        <form onSubmit={handleSubmit}>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Name"
          />
          <input
            type="number"
            value={age}
            onChange={(e) => setAge(e.target.value)}
            placeholder="Age"
          />
          <button type="submit">Update</button>
        </form>
      </div>
  
  
    </Space>
  <Space 
    align="baseline" 
    direction="horizontal" 
    style={{ 
      width: '100%',        // 关键点1：占满父容器宽度
      justifyContent: 'space-between'  // 关键点2：两端对齐
    }}
  >
    <span style={{ 
      flex: "none",
      margin: "8px 4px",
      padding: "4px",
      border: "0px solid #40a9ff"
    }}>
      设置
    </span>
    
    <div style={{ marginLeft: 'auto' }}>
      <Switch 
        checkedChildren="开启" 
        unCheckedChildren="关闭" 
        defaultChecked 
      />
    </div>
  </Space>
  </Flex>
  );
  const text_2 = (<Layout   >
      
      
          <Flex style={{ paddingInlineStart: 24 }}>
              <Space 
              align="baseline" 
              direction="horizontal" 
              style={{ 
                  width: '100%',        // 关键点1：占满父容器宽度
                  justifyContent: 'space-between'  // 关键点2：两端对齐
              }}
              >
              <span style={{ 
                  flex: "none",
                  margin: "8px 4px",
                  padding: "4px",
                  border: "0px solid #40a9ff"
              }}>
                  设置
              </span>
              
              <div style={{ marginLeft: 'auto' }}>
                  <Switch 
                  checkedChildren="开启" 
                  unCheckedChildren="关闭" 
                  defaultChecked 
                  />
              </div>
              </Space>
              </Flex>
  
      
      <Flex style={{ paddingInlineStart: 24,marginTop:8 }}>
              <Space 
              align="baseline" 
              direction="horizontal" 
              style={{ 
                  width: '100%',        // 关键点1：占满父容器宽度
                  justifyContent: 'space-between'  // 关键点2：两端对齐
              }}
              >
              <span style={{ 
                  flex: "none",
                  margin: "8px 4px",
                  padding: "4px",
                  border: "0px solid #40a9ff"
              }}>
                  设置
              </span>
              
     
                  <InputNumber min={1} max={10} defaultValue={3}  size='small' style={{width:58}}/>
          
              </Space>
              </Flex>
  
  
      </Layout>
      );
      
  
  const Items_Config: CollapseProps['items'] = [
      {
        key: '1',
        label: 'This is panel header 1',
        children: text_1,
      },
      {
        key: '2',
        label: 'This is panel header 2',
        children: text_2,
      },
      {
        key: '3',
        label: 'This is panel header 3',
        children: text_1,
      },
    ];
    
    const Config_List: React.FC = () => <Collapse items={Items_Config} bordered={false} defaultActiveKey={['1']} />;

    const onChange = (key: string | string[]) => {
        console.log(key);
      };


















 

    //   let Flex_Acharts =  <Flex> <DemoLine /><Flex/>
      let Flex_Acharts =   <Flex>  <DemoLine /></Flex>
      let Flex_Acharts2 =   <Flex>  <DemoColumn /></Flex>







  
    const Items_Tabs =[

        // {"key":"1","label":"属性","children":(<Collapse defaultActiveKey={['1']} onChange={onChange} items={items} />),},
        {"key":"1","label":"属性","children":<Config_List />,},
        {"key":"2","label":"设置","children": Flex_Acharts,},
        {"key":"3","label":"其他","children":Flex_Acharts2,},

      ]






  return (
    <Flex gap="4px 0" wrap>

                <Tabs 
                                // cardBg={"white"}
                         
                                size="small"
                                type="card"
                                tabBarStyle={{        // 文字颜色
                                    color: "red",
                                    fontSize: 36,
                                    // background:"red",
                                    ['--active-color' as any]: 'white', // 激活颜色
                                    ['--hover-color' as any]: '#e6f7ff' // 悬停颜色
                                                        
                    
                                  
                                
                                }}
                               
                                // style={{width:"90%",color:"white"}}
                                style={{
                                    width:"90%",
                                    color:"white",
                                    ['--active-color' as any]: '#1890ff' // CSS 变量方式
                                  }}
                                // tabBarExtraContent={operations} 
                                items={Items_Tabs} 
                                
                                
                                />



        {/* <div style={{width:"100%",height:"900px",background:"red"}} */}
        
        {/* ></div> */}
    </Flex>
  );
};

export default Utils_Drawer_NodeConfig;