*{margin: 0;padding: 0;}
.one{
    width: 100px;
    height: 100px;
   /* position: absolute;*/
    margin: 20px;
    background:transparent url(/static/images/App/Logo.ico) no-repeat;
}
.boxList{
    border: 1px solid #e1e1ec;
    height: 550px;
    margin: 30px;
    position: relative;
    width: 500px;
}
.div1{width: 200px;
    height: 200px;
    position: absolute;
    margin: 20px;
    float: right;
    background:transparent url(/static/images/App/Logo.ico) no-repeat;
    top: 50px;
    right: 100px;
}
.div2{width: 200px;
    height: 200px;
    position: absolute;
    margin: 20px;
    float: right;
    background:transparent url(/static/images/App/Logo.ico)no-repeat;
    right: 350px;
    top: 400px;
}

.ddd{width: 1000px;height: 600px;border: 1px solid #ff0033;position: relative;margin-left: 100px;margin-top: 100px;top: 100px;left: 100px}
.title{
    background: #399;
    height: 20px;
    width: 100%;
    color: #eeeeee;
    text-align: center;
    cursor: move;
}
img{width: 100%;height: 80%}
.tezml{
    border: 3px #ff0033 dashed;
}
.box{
    margin: 50px;
    padding: 30px;
    background: #eeeeee;
}

.head h1{
    font-size: 60px;
    font-weight: bolder;
}
.head h3{
    line-height: 50px;
}
hr{margin-top: 20px}
.child_hr{
    color: #006CBF;
}
.example_one{
    height: 600px;
}
.example_one .code{
    float: left;
    width: 45%;
}
.example_one .example{
    float: right;
    border-left: 1px solid #cccccc;
    width: 45%;
    height: 620px;
}

h2{
    color: #006CBF;
    margin: 50px 0 0 20px;
}
.onlyX{
    top: 40%;
}
.onlyY{
    left: 45%;
}
.div7{
    bottom: 0;
}
.div8{
    float: left;
}
.div9{
    float: left;
}
.div10{
    float: left;
}
.div11{
    float: left;
}
.div12{
    float: left;
}
.div13{
    float: left;
}

.abc{
    border: 2px solid blue;
}
input{
    margin-left: 50px;
}
.h800{
    height: 800px;
}

