# -*- coding: utf-8 -*-
import time,os,sys,cv2
from PySide6 import Qt<PERSON>ore, QtGui, QtWidgets
import Component_Common
import qtawesome

Page_Info = {}
class Component_Setting_Channel(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        try:self.Channel_Info =args[0]
        except:self.Channel_Info={}
        self.initUI()

    def initUI(self):

        Page_Info["Page_Element_List"] = {}
        Page_Info["Page_Def_List"] = {}
        Page_Info["StyleSheet"] = {}

        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(3)  # 内边界
        self.QVBoxLayout_Line.setContentsMargins(3, 0, 0, 0)  # 外边

        StyleSheet_Line = """QLabel {
                                                       background-color: rgba(0,0,0,0.8);
                                                       border-radius: 3px;
                                                       border: none;
                                                       color: #1E90FF;
                                                   }
                                                   QLabel:hover {
                                                       background-color: rgba(255,255,255,0.3);
                                                       border: none;
                                                   }
                                               """


        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Line",         "Line_Name":"通道备注名",        "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"视频一",               "Line_Parameter":{},},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"哨兵前端",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"哨兵1",                "Line_Parameter":["哨兵1", "哨兵2", "哨兵3", "哨兵4"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Text",         "Line_Name":"视频地址",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":r"D:\45M.mp4",          "Line_Parameter":{},},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"分辨率",            "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"高清(720*576)",        "Line_Parameter":["超高清(1920*1080)", "高清(720*576)","标清(640*480)","省流(480*320)"],},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"音频播放",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"关闭",                 "Line_Parameter":["打开", "关闭"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"自动录像",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"关闭",                 "Line_Parameter":["打开", "关闭"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"重连次数",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"重连5次",                 "Line_Parameter":["重连5次", "重连10次","总是重连"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"传输方式",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"国标28181",                 "Line_Parameter":["国标28181", "哨兵加密","国密通讯"]},))





        ##创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet(
            "QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        # __QVBoxLayout.addWidget(self.QLabel_Line_List["Set_Line_Source_Select"])
        __QVBoxLayout.addWidget(self.QScrollArea_Line, 1)

        self.Set_Popup()

        self.PopupParameter={}


    def Set_Line(self, Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        # __QLabel.setStyleSheet("""QLabel {background-color: rgba(255,255,255,0.8);border-radius: 3px;border: none;}QLabel:hover {background-color: rgba(255,255,255,0.3);border: none;}""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 0, 0)
        __QHBoxLayout.setSpacing(3)

        # 名称
        QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(60)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:rgba(0, 255, 136, 255); ")
        # 名称
        QLabel_Content = QtWidgets.QLabel(Line_Info["Line_Content"])
        Page_Info["Page_Element_List"][f"QLabel_Content_{Line_Info['Line_Type']}"] = QLabel_Content
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content.setStyleSheet("background: transparent;color: #1E90FF; ")



        QPushButton_Line_Click = QtWidgets.QPushButton()
        QPushButton_Line_Click.setIconSize(QtCore.QSize(25, 25))
        QPushButton_Line_Click.setStyleSheet("background:transparent")
        QPushButton_Line_Click.setIcon(qtawesome.icon('fa6s.angle-right', scale_factor=0.5, color=('gray'), color_active='blue'))
        QPushButton_Line_Click.clicked.connect(lambda :self.Open_Popup(Line_Info))
        # QPushButton_Line_Click.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Line_Info["Line_Command"]))

        __QHBoxLayout.addWidget(QLabel_Name, 1)
        __QHBoxLayout.addWidget(QLabel_Content)
        __QHBoxLayout.addWidget(QPushButton_Line_Click,alignment=QtCore.Qt.AlignRight)

        return __QLabel



    def Set_Popup_Input_Text(self,PopupParameter):

        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass




        self.QTextEdit_Popup_Input = QtWidgets.QTextEdit()
        self.QTextEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        self.QTextEdit_Popup_Input.setStyleSheet("""
            QTextEdit {
                background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
                border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
                border-radius:3px;                            /* 圆角 */
                padding: 6px 10px;                             /* 内边距 */
                color: #e0e0e0;                                /* 文字颜色 */
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #000000;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
            }

            /* 鼠标悬停状态 */
            QTextEdit:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
                   selection-background-color: #000000;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
                color: #000000; 
            }

            /* 聚焦状态（键盘输入时） */
            QTextEdit:focus {
                border: 1px solid #4cc9f0;
                background-color: rgba(255, 255, 255, 0.15);
            }

            /* 占位提示文字 */
            QTextEdit[placeholderText] {
                color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
            }
        """)
        self.QTextEdit_Popup_Input.setSizePolicy(QtWidgets.QSizePolicy.Expanding,  QtWidgets.QSizePolicy.Fixed  )

        self.QHBoxLayout_Popup_Content.addWidget(self.QTextEdit_Popup_Input)



    def Set_Popup_Input_Line(self,PopupParameter):


        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass

        self.QLineEdit_Popup_Input = QtWidgets.QLineEdit()
        self.QLineEdit_Popup_Input = QtWidgets.QLineEdit()
        # self.QLineEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")

        StyleSheet_QLabel = """
                                                                     QLabel {
                                                                         background-color:rgba(40, 52, 80, 0.3);
                                                                         border: 0px solid rgba(0, 180, 255, 60);
                                                                         border-radius: 3px;
                                                                         padding: 0px;
                                                                         font-size:13px;
                                                                         font-weight: bold;
                                                                         color:white;
                                                                     }
                                                                     QLabel:hover {
                                                                         background-color: rgba(0, 100, 150, 150);
                                                                     }
                                                                 """


        self.QLineEdit_Popup_Input.setStyleSheet("""
                    QLineEdit {
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 3px;
                        color: white;
                        padding: 5px;
                    }
                  
                """)
        self.QLineEdit_Popup_Input.setSizePolicy(QtWidgets.QSizePolicy.Expanding,  QtWidgets.QSizePolicy.Fixed  )










        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)
        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)
        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)


    def Set_Popup_Input_Select(self, PopupParameter):

        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass

        # 创建一个 QComboBox
        self.QComboBox_Select = QtWidgets.QComboBox()
        self.QComboBox_Select.setStyleSheet("""
                    QComboBox {
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 3px;
                        color: white;
                        padding: 5px;
                    }
                    QComboBox::drop-down {
                        border: none;
                    }
                    QComboBox::down-arrow {
                        image: none;
                    }
                    QComboBox::item {
                        background-color: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 5px;
                    }
                    QComboBox::item:selected {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                """)
        self.QComboBox_Select.addItems(PopupParameter["Line_Parameter"])
        self.QComboBox_Select.setFixedSize(218,25)

        self.QHBoxLayout_Popup_Content.addWidget( self.QComboBox_Select)





    def Set_Popup(self):
        self.QLabel_Popup = QtWidgets.QLabel( self)
        self.QLabel_Popup.resize(268, 188)
        self.QLabel_Popup.setStyleSheet("background-color: rgba(25,25,50,1); border-radius: 5px; padding: 3px;")
        self.QLabel_Popup.setAlignment(QtCore.Qt.AlignCenter)

        __QVBoxLayout = QtWidgets.QVBoxLayout(self.QLabel_Popup)
        __QVBoxLayout.setSpacing(8)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Popup_Title = QtWidgets.QLabel("视频地址")
        self.QLabel_Popup_Title.setAlignment(QtCore.Qt.AlignLeft)
        self.QLabel_Popup_Title.setFixedHeight(30)
        self.QLabel_Popup_Title.setStyleSheet("background: transparent;color:rgba(0, 255, 136, 255); ")
        self.QLabel_Popup_Title.setFont(QtGui.QFont("Microsoft YaHei", 8))


        self.QLabel_Popup_Content = QtWidgets.QLabel()
        self.QLabel_Popup_Content.setStyleSheet("background:transparent;border: none;")
        self.QHBoxLayout_Popup_Content = QtWidgets.QVBoxLayout(self.QLabel_Popup_Content)
        self.QHBoxLayout_Popup_Content.setSpacing(8)  # 内边界
        self.QHBoxLayout_Popup_Content.setAlignment(QtCore.Qt.AlignCenter)
        self.QHBoxLayout_Popup_Content.setContentsMargins(0, 0, 0, 0)  # 外边


        QLabel_Bottom = QtWidgets.QLabel()
        QLabel_Bottom.setFixedHeight(30)
        QLabel_Bottom.setStyleSheet("background:transparent;border: none;")

        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout(QLabel_Bottom)
        QHBoxLayout_Bottom.setSpacing(8)  # 内边界
        QHBoxLayout_Bottom.setAlignment(QtCore.Qt.AlignCenter)
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边



        StyleSheet_QLabel = """QLabel {
                                                      background-color: rgba(255,255,255,0.8);
                                                      border-radius: 6px;
                                                      border: none;
                                                      color:#1E90FF
                                                  }
                                                  QLabel:hover {
                                                      background-color: rgba(255,255,255,0.3);
                                                      border: none;
                                                  }
                                              """

        self.QLabel_Popup_Yes = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_Yes.setFixedSize(50, 25)
        self.QLabel_Popup_Yes.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_Yes.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_Yes.setText("确定")
        self.QLabel_Popup_Yes.clicked.connect(lambda: self.Popup_Emit())
        self.QLabel_Popup_Yes.setStyleSheet("""
                   QLabel {
                       background-color: transparent;
                       color: white;
                       border: none;
                   }
                   QLabel:hover {
                       text-decoration: underline; 
                   }
               """)

        self.QLabel_Popup_No = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_No.setFixedSize(50, 25)
        self.QLabel_Popup_No.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_No.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_No.setText("取消")
        self.QLabel_Popup_No.clicked.connect(lambda: self.Popup_Close())
        self.QLabel_Popup_No.setStyleSheet("""
                   QLabel {
                       background-color: transparent;
                       color: white;
                       border: none;
                   }
                   QLabel:hover {
                       text-decoration: underline; 
                   }
               """)


        QHBoxLayout_Bottom.contentsRect()
        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_Yes, )
        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_No, )

        __QVBoxLayout.addWidget(self.QLabel_Popup_Title,0)
        __QVBoxLayout.addWidget(self.QLabel_Popup_Content)
        __QVBoxLayout.addWidget(QLabel_Bottom,0)

        self.QLabel_Popup.setVisible(False)

    def Popup_Emit_bak(self,):
        print("Popup_Emit")
        # print( self.PopupParameter)
        # {"Input_Select":""}

        Emit_Content = ""
        match self.PopupParameter["Line_Type"]:
            case "Input_Select":

                Emit_Content =self.QComboBox_Select.currentText()
            case "Input_Line":

                print( "Input_Line",self.QLineEdit_Popup_Input.text())
                Emit_Content = self.QLineEdit_Popup_Input.text()
            case "Input_Text":


                print( "Input_Line",self.QTextEdit_Popup_Input.toPlainText())
                Emit_Content = self.QTextEdit_Popup_Input.toPlainText()


            case _:print("unknown")
    def Popup_Emit(self):
        print("Popup_Emit")

        Emit_Content = ""
        match self.PopupParameter["Line_Type"]:
            case "Input_Select":
                Emit_Content = self.QComboBox_Select.currentText()
                print("Input_Select选择的内容:", Emit_Content)

            case "Input_Line":
                print("Input_Line", self.QLineEdit_Popup_Input.text())
                Emit_Content = self.QLineEdit_Popup_Input.text()

            case "Input_Text":
                print("Input_Text", self.QTextEdit_Popup_Input.toPlainText())
                Emit_Content = self.QTextEdit_Popup_Input.toPlainText()

            case _:
                print("unknown")

        self.QLabel_Popup.setVisible(False)




        # Page_Info["Page_Element_List"][f"QLabel_Content_{self.PopupParameter['Line_Name']}"].setText(Emit_Content)
        # 根据 Line_Type 而不是 Line_Name 来选择元素
        line_type = self.PopupParameter.get('Line_Type', '')
        element_key = f"QLabel_Content_{line_type}"

        if element_key in Page_Info["Page_Element_List"]:
            Page_Info["Page_Element_List"][element_key].setText(Emit_Content)
            print(f"成功更新元素: {element_key} 内容: {Emit_Content}")
        else:
            print(f"找不到页面元素: {element_key}")
            print(f"Line_Type: {line_type}")
            print(f"可用的页面元素: {list(Page_Info['Page_Element_List'].keys())}")



        self.QLabel_Popup.setVisible(False)

    def Popup_Close(self):
        self.QLabel_Popup.setVisible(False)

    def Open_Popup(self,PopupParameter):
        # 弹出的 QLabel

        # self.popup_label.setVisible(False)  # 初始隐藏
        # self.popup_label.move(self.width() // 2 - self.popup_label.width() // 2,self.height() // 2 - self.popup_label.height() // 2)
        # self.Set_Popup_Input(PopupParameter)
        # self.Set_Popup_Select(PopupParameter)

        # if PopupParameter['Line_Sign'] == ""
        self.PopupParameter= PopupParameter

        self.QLabel_Popup_Title.setText(PopupParameter["Line_Name"])


        exec(f"self.Set_Popup_{PopupParameter['Line_Type']}(PopupParameter)")




        # 切换弹出 QLabel 的显示状态
        self.QLabel_Popup.setVisible(not self.QLabel_Popup.isVisible())
        if self.QLabel_Popup.isVisible():
            # 如果显示，调整位置使其居中
            self.QLabel_Popup.move(self.width() // 2 - self.QLabel_Popup.width() // 2,self.height() // 2 - self.QLabel_Popup.height() // 2)


class Component_Setting_Function(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self,  parent=None):
        super().__init__(parent)
        # self.Channel_Info = args[0] or {}
        self.QWidget_Parent = parent  # 现在你可以用 self.Pe 访问父控件

        self.initUI()


    def initUI(self):

        Page_Info["Page_Element_List"] = {}
        Page_Info["Page_Def_List"] = {}
        Page_Info["StyleSheet"] = {}

        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(3)  # 内边界
        self.QVBoxLayout_Line.setContentsMargins(3, 0, 0, 0)  # 外边

        StyleSheet_Line = """QLabel {
                                                       background-color: rgba(0,0,0,0.8);
                                                       border-radius: 3px;
                                                       border: none;
                                                       color: #1E90FF;
                                                   }
                                                   QLabel:hover {
                                                       background-color: rgba(255,255,255,0.3);
                                                       border: none;
                                                   }
                                               """


        # self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Line",         "Line_Name":"通道备注名",        "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"视频一",               "Line_Parameter":{},},))
        # self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Select",       "Line_Name":"哨兵功能",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        # self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_Type":"Input_Text",         "Line_Name":"视频地址",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":r"D:\45M.mp4",          "Line_Parameter":{},},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"1", "Line_Type":"Input_Select",       "Line_Name":"功能部件【一】",            "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,     "Line_Content":"人脸预警",                "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"2", "Line_Type":"Input_Select",       "Line_Name":"功能部件【二】",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,   "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"3", "Line_Type":"Input_Select",       "Line_Name":"功能部件【三】",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"4", "Line_Type":"Input_Select",       "Line_Name":"功能部件【四】",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,     "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"5", "Line_Type":"Input_Select",       "Line_Name":"功能部件【五】",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))
        self.QVBoxLayout_Line.addWidget(self.Set_Line({"Line_ID":"6", "Line_Type":"Input_Select",       "Line_Name":"功能部件【六】",          "Line_Size":[28,288],"Line_StyleSheet":StyleSheet_Line,    "Line_Content":"人脸预警",               "Line_Parameter":["人脸预警", "人脸布控详情", "人体雷达", "人体雷达监控结果","开门震动预警", "开门震动预警"]},))




        ##创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet(
            "QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        # __QVBoxLayout.addWidget(self.QLabel_Line_List["Set_Line_Source_Select"])
        __QVBoxLayout.addWidget(self.QScrollArea_Line, 1)

        self.Set_Popup()

        self.PopupParameter={}


    def Set_Line(self, Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        # __QLabel.setStyleSheet("""QLabel {background-color: rgba(255,255,255,0.8);border-radius: 3px;border: none;}QLabel:hover {background-color: rgba(255,255,255,0.3);border: none;}""")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 0, 0)
        __QHBoxLayout.setSpacing(3)

        # 名称
        QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(90)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Name.setStyleSheet("background: transparent;color:rgba(0, 255, 136, 255); ")
        # 名称
        QLabel_Content = QtWidgets.QLabel(Line_Info["Line_Content"])
        # Page_Info["Page_Element_List"][f"QLabel_Content_{Line_Info['Line_Type']}"] = QLabel_Content
        Page_Info["Page_Element_List"][f"QLabel_Content_{Line_Info['Line_Name']}"] = QLabel_Content
        QLabel_Content.setFont(QtGui.QFont("Microsoft YaHei", 8))
        QLabel_Content.setStyleSheet("background: transparent;color: #1E90FF; ")



        QPushButton_Line_Click = QtWidgets.QPushButton()
        QPushButton_Line_Click.setIconSize(QtCore.QSize(25, 25))
        QPushButton_Line_Click.setStyleSheet("background:transparent")
        QPushButton_Line_Click.setIcon(qtawesome.icon('fa6s.angle-right', scale_factor=0.5, color=('gray'), color_active='blue'))
        QPushButton_Line_Click.clicked.connect(lambda :self.Open_Popup(Line_Info))
        # QPushButton_Line_Click.clicked.connect(lambda: self.PAGE_HANDLER_EXECUTE(Line_Info["Line_Command"]))

        __QHBoxLayout.addWidget(QLabel_Name, 1)
        __QHBoxLayout.addWidget(QLabel_Content)
        __QHBoxLayout.addWidget(QPushButton_Line_Click,alignment=QtCore.Qt.AlignRight)

        return __QLabel



    def Set_Popup_Input_Text(self,PopupParameter):

        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass




        self.QTextEdit_Popup_Input = QtWidgets.QTextEdit()
        self.QTextEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        self.QTextEdit_Popup_Input.setStyleSheet("""
            QTextEdit {
                background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
                border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
                border-radius:3px;                            /* 圆角 */
                padding: 6px 10px;                             /* 内边距 */
                color: #e0e0e0;                                /* 文字颜色 */
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #000000;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
            }

            /* 鼠标悬停状态 */
            QTextEdit:hover {
                border: 1px solid rgba(76, 201, 240, 0.7);
                background-color: rgba(255, 255, 255, 0.12);
                   selection-background-color: #000000;          /* 选中文本背景 */
                selection-color: #ffffff;                     /* 选中文本颜色 */
                color: #000000; 
            }

            /* 聚焦状态（键盘输入时） */
            QTextEdit:focus {
                border: 1px solid #4cc9f0;
                background-color: rgba(255, 255, 255, 0.15);
            }

            /* 占位提示文字 */
            QTextEdit[placeholderText] {
                color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
            }
        """)
        self.QTextEdit_Popup_Input.setSizePolicy(QtWidgets.QSizePolicy.Expanding,  QtWidgets.QSizePolicy.Fixed  )

        self.QHBoxLayout_Popup_Content.addWidget(self.QTextEdit_Popup_Input)



    def Set_Popup_Input_Line(self,PopupParameter):


        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass

        self.QLineEdit_Popup_Input = QtWidgets.QLineEdit()
        self.QLineEdit_Popup_Input = QtWidgets.QLineEdit()
        # self.QLineEdit_Popup_Input.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")

        StyleSheet_QLabel = """
                                                                     QLabel {
                                                                         background-color:rgba(40, 52, 80, 0.3);
                                                                         border: 0px solid rgba(0, 180, 255, 60);
                                                                         border-radius: 3px;
                                                                         padding: 0px;
                                                                         font-size:13px;
                                                                         font-weight: bold;
                                                                         color:white;
                                                                     }
                                                                     QLabel:hover {
                                                                         background-color: rgba(0, 100, 150, 150);
                                                                     }
                                                                 """


        self.QLineEdit_Popup_Input.setStyleSheet("""
                    QLineEdit {
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 3px;
                        color: white;
                        padding: 5px;
                    }
                  
                """)
        self.QLineEdit_Popup_Input.setSizePolicy(QtWidgets.QSizePolicy.Expanding,  QtWidgets.QSizePolicy.Fixed  )










        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)
        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)
        self.QHBoxLayout_Popup_Content.addWidget(self.QLineEdit_Popup_Input)


    def Set_Popup_Input_Select(self, PopupParameter):

        try:
            while self.QHBoxLayout_Popup_Content.count():
                item = self.QHBoxLayout_Popup_Content.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
        except:
            pass

        # 创建一个 QComboBox
        self.QComboBox_Select = QtWidgets.QComboBox()
        self.QComboBox_Select.setStyleSheet("""
                    QComboBox {
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        border-radius: 3px;
                        color: white;
                        padding: 5px;
                    }
                    QComboBox::drop-down {
                        border: none;
                    }
                    QComboBox::down-arrow {
                        image: none;
                    }
                    QComboBox::item {
                        background-color: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 5px;
                    }
                    QComboBox::item:selected {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                """)
        self.QComboBox_Select.addItems(PopupParameter["Line_Parameter"])
        self.QComboBox_Select.setFixedSize(218,25)

        self.QHBoxLayout_Popup_Content.addWidget( self.QComboBox_Select)





    def Set_Popup(self):
        self.QLabel_Popup = QtWidgets.QLabel( self)
        self.QLabel_Popup.resize(268, 188)
        self.QLabel_Popup.setStyleSheet("background-color: rgba(25,25,50,1); border-radius: 5px; padding: 3px;")
        self.QLabel_Popup.setAlignment(QtCore.Qt.AlignCenter)

        __QVBoxLayout = QtWidgets.QVBoxLayout(self.QLabel_Popup)
        __QVBoxLayout.setSpacing(8)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Popup_Title = QtWidgets.QLabel("视频地址")
        self.QLabel_Popup_Title.setAlignment(QtCore.Qt.AlignLeft)
        self.QLabel_Popup_Title.setFixedHeight(30)
        self.QLabel_Popup_Title.setStyleSheet("background: transparent;color:rgba(0, 255, 136, 255); ")
        self.QLabel_Popup_Title.setFont(QtGui.QFont("Microsoft YaHei", 8))


        self.QLabel_Popup_Content = QtWidgets.QLabel()
        self.QLabel_Popup_Content.setStyleSheet("background:transparent;border: none;")
        self.QHBoxLayout_Popup_Content = QtWidgets.QVBoxLayout(self.QLabel_Popup_Content)
        self.QHBoxLayout_Popup_Content.setSpacing(8)  # 内边界
        self.QHBoxLayout_Popup_Content.setAlignment(QtCore.Qt.AlignCenter)
        self.QHBoxLayout_Popup_Content.setContentsMargins(0, 0, 0, 0)  # 外边


        QLabel_Bottom = QtWidgets.QLabel()
        QLabel_Bottom.setFixedHeight(30)
        QLabel_Bottom.setStyleSheet("background:transparent;border: none;")

        QHBoxLayout_Bottom = QtWidgets.QHBoxLayout(QLabel_Bottom)
        QHBoxLayout_Bottom.setSpacing(8)  # 内边界
        QHBoxLayout_Bottom.setAlignment(QtCore.Qt.AlignCenter)
        QHBoxLayout_Bottom.setContentsMargins(0, 0, 0, 0)  # 外边



        StyleSheet_QLabel = """QLabel {
                                                      background-color: rgba(255,255,255,0.8);
                                                      border-radius: 6px;
                                                      border: none;
                                                      color:#1E90FF
                                                  }
                                                  QLabel:hover {
                                                      background-color: rgba(255,255,255,0.3);
                                                      border: none;
                                                  }
                                              """

        self.QLabel_Popup_Yes = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_Yes.setFixedSize(50, 25)
        self.QLabel_Popup_Yes.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_Yes.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_Yes.setText("确定")
        self.QLabel_Popup_Yes.clicked.connect(lambda: self.Popup_Emit())
        self.QLabel_Popup_Yes.setStyleSheet("""
                          QLabel {
                              background-color: transparent;
                              color: white;
                              border: none;
                          }
                          QLabel:hover {
                              text-decoration: underline; 
                          }
                      """)

        self.QLabel_Popup_No = Component_Common.Component_Common_QLabel_Click()
        self.QLabel_Popup_No.setFixedSize(50, 25)
        self.QLabel_Popup_No.setStyleSheet(StyleSheet_QLabel)
        self.QLabel_Popup_No.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Popup_No.setText("取消")
        self.QLabel_Popup_No.clicked.connect(lambda: self.Popup_Close())
        self.QLabel_Popup_No.setStyleSheet("""
                                  QLabel {
                                      background-color: transparent;
                                      color: white;
                                      border: none;
                                  }
                                  QLabel:hover {
                                      text-decoration: underline; 
                                  }
                              """)


        QHBoxLayout_Bottom.contentsRect()
        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_Yes, )
        QHBoxLayout_Bottom.addWidget(self.QLabel_Popup_No, )

        __QVBoxLayout.addWidget(self.QLabel_Popup_Title,0)
        __QVBoxLayout.addWidget(self.QLabel_Popup_Content)
        __QVBoxLayout.addWidget(QLabel_Bottom,0)

        self.QLabel_Popup.setVisible(False)

    def Popup_Emit_bak(self,):
        print("Popup_Emit")
        # print( self.PopupParameter)
        # {"Input_Select":""}

        Emit_Content = ""
        match self.PopupParameter["Line_Type"]:
            case "Input_Select":

                Emit_Content =self.QComboBox_Select.currentText()
            case "Input_Line":

                print( "Input_Line",self.QLineEdit_Popup_Input.text())
                Emit_Content = self.QLineEdit_Popup_Input.text()
            case "Input_Text":


                print( "Input_Line",self.QTextEdit_Popup_Input.toPlainText())
                Emit_Content = self.QTextEdit_Popup_Input.toPlainText()


            case _:print("unknown")





        Page_Info["Page_Element_List"][f"QLabel_Content_{self.PopupParameter['Line_Type']}"].setText(Emit_Content)



        self.QLabel_Popup.setVisible(False)
    def Popup_Emit(self):
        print("Popup_Emit")

        Emit_Content = ""
        match self.PopupParameter["Line_Type"]:
            case "Input_Select":
                Emit_Content = self.QComboBox_Select.currentText()
                print("Input_Select选择的内容:", Emit_Content)

            case "Input_Line":
                print("Input_Line", self.QLineEdit_Popup_Input.text())
                Emit_Content = self.QLineEdit_Popup_Input.text()

            case "Input_Text":
                print("Input_Text", self.QTextEdit_Popup_Input.toPlainText())
                Emit_Content = self.QTextEdit_Popup_Input.toPlainText()

            case _:
                print("unknown")

        # Page_Info["Page_Element_List"][f"QLabel_Content_{self.PopupParameter['Line_Type']}"].setText(Emit_Content)
        Page_Info["Page_Element_List"][f"QLabel_Content_{self.PopupParameter['Line_Name']}"].setText(Emit_Content)
        self.QLabel_Popup.setVisible(False)

    def Popup_Close(self):
        print(self.PopupParameter)
        self.QWidget_Parent.Function_QLabel_Setcel_Reback(self.PopupParameter["Line_ID"])
        self.QLabel_Popup.setVisible(False)

    def Open_Popup(self,PopupParameter):
        # 弹出的 QLabel
        self.QWidget_Parent.Function_QLabel_Setcel_Change(PopupParameter["Line_ID"])

        # self.popup_label.setVisible(False)  # 初始隐藏
        # self.popup_label.move(self.width() // 2 - self.popup_label.width() // 2,self.height() // 2 - self.popup_label.height() // 2)
        # self.Set_Popup_Input(PopupParameter)
        # self.Set_Popup_Select(PopupParameter)

        # if PopupParameter['Line_Sign'] == ""
        self.PopupParameter= PopupParameter

        self.QLabel_Popup_Title.setText(PopupParameter["Line_Name"])


        exec(f"self.Set_Popup_{PopupParameter['Line_Type']}(PopupParameter)")




        # 切换弹出 QLabel 的显示状态
        self.QLabel_Popup.setVisible(not self.QLabel_Popup.isVisible())
        if self.QLabel_Popup.isVisible():
            # 如果显示，调整位置使其居中
            self.QLabel_Popup.move(self.width() // 2 - self.QLabel_Popup.width() // 2,self.height() // 2 - self.QLabel_Popup.height() // 2)


class Component_Setting_Control(QtWidgets.QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        # 使用与其他组件相同的样式设置方式
        self.setStyleSheet("""
            QLabel {
                background-color: rgba(0,0,0,0.8);
                border-radius: 3px;
                border: none;
                color: #1E90FF;
            }
            QLabel:hover {
                background-color: rgba(255,255,255,0.3);
                border: none;
            }
        """)

        # 主布局
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 创建方向控制区域
        direction_widget = self.Create_Direction_Control()
        main_layout.addWidget(direction_widget, alignment=QtCore.Qt.AlignCenter)

        # 创建缩放控制区域
        zoom_widget = self.Create_Zoom_Control()
        main_layout.addWidget(zoom_widget, alignment=QtCore.Qt.AlignCenter)

        # 创建功能选项区域
        options_widget = self.Create_Options_Control()
        main_layout.addWidget(options_widget, alignment=QtCore.Qt.AlignLeft)

        # 添加弹性空间
        main_layout.addStretch()

    def Create_Direction_Control(self):
        """创建方向控制区域"""
        widget = QtWidgets.QWidget()
        widget.setFixedSize(200, 200)

        # 使用绝对定位
        widget.setStyleSheet("background: transparent;")

        # 中心按钮（停止/确认）
        self.btn_center = self.Create_Control_Button("●", 50, 50)
        self.btn_center.setParent(widget)
        self.btn_center.move(75, 75)  # 居中位置
        self.btn_center.clicked.connect(lambda: self.handle_direction_click("center"))

        # 上按钮
        self.btn_up = self.Create_Control_Button("▲", 40, 40)
        self.btn_up.setParent(widget)
        self.btn_up.move(80, 25)
        self.btn_up.clicked.connect(lambda: self.handle_direction_click("up"))

        # 下按钮
        self.btn_down = self.Create_Control_Button("▼", 40, 40)
        self.btn_down.setParent(widget)
        self.btn_down.move(80, 135)
        self.btn_down.clicked.connect(lambda: self.handle_direction_click("down"))

        # 左按钮
        self.btn_left = self.Create_Control_Button("◀", 40, 40)
        self.btn_left.setParent(widget)
        self.btn_left.move(25, 80)
        self.btn_left.clicked.connect(lambda: self.handle_direction_click("left"))

        # 右按钮
        self.btn_right = self.Create_Control_Button("▶", 40, 40)
        self.btn_right.setParent(widget)
        self.btn_right.move(135, 80)
        self.btn_right.clicked.connect(lambda: self.handle_direction_click("right"))

        return widget

    def Create_Zoom_Control(self):
        """创建缩放控制区域"""
        widget = QtWidgets.QWidget()
        # 设置缩放控制区域背景透明
        widget.setStyleSheet("background: transparent;")

        layout = QtWidgets.QHBoxLayout(widget)
        layout.setSpacing(30)
        layout.setContentsMargins(0, 0, 0, 0)

        # 放大按钮
        self.btn_zoom_in = self.Create_Control_Button("+", 50, 50)
        self.btn_zoom_in.clicked.connect(lambda: self.Handle_Zoom_Click("in"))
        layout.addWidget(self.btn_zoom_in)

        # 缩小按钮
        self.btn_zoom_out = self.Create_Control_Button("−", 50, 50)
        self.btn_zoom_out.clicked.connect(lambda: self.Handle_Zoom_Click("out"))
        layout.addWidget(self.btn_zoom_out)

        return widget

    def Create_Options_Control(self):
        """创建功能选项区域"""
        widget = QtWidgets.QWidget()
        widget.setStyleSheet("background: transparent;")  # 确保选项区域背景透明
        layout = QtWidgets.QVBoxLayout(widget)
        layout.setSpacing(10)
        layout.setContentsMargins(0, 0, 0, 0)

        # 显示名称选项
        self.checkbox_show_name = QtWidgets.QCheckBox("显示名称")
        self.checkbox_show_name.setChecked(True)
        self.checkbox_show_name.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 14px;
                background: transparent;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid rgba(255, 255, 255, 0.5);
                border-radius: 3px;
                background: rgba(0, 0, 0, 0.3);
            }
            QCheckBox::indicator:checked {
                background: rgba(76, 201, 240, 0.8);
                border: 1px solid #4cc9f0;
            }
        """)
        layout.addWidget(self.checkbox_show_name)

        # 智能码流选项
        self.checkbox_smart_stream = QtWidgets.QCheckBox("智能码流")
        self.checkbox_smart_stream.setStyleSheet(self.checkbox_show_name.styleSheet())
        layout.addWidget(self.checkbox_smart_stream)

        return widget

    def Create_Control_Button(self, text, width, height):
        """创建控制按钮"""
        button = QtWidgets.QPushButton(text)
        button.setFixedSize(width, height)
        button.setStyleSheet("""
            QPushButton {
                background-color: rgba(100, 100, 100, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: %dpx;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(150, 150, 150, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background-color: rgba(76, 201, 240, 0.8);
                border: 1px solid #4cc9f0;
            }
        """ % (min(width, height) // 2))

        return button

    def handle_direction_click(self, direction):
        """处理方向控制点击事件"""
        print(f"方向控制: {direction}")
        # 这里可以添加具体的云台控制逻辑

    def Handle_Zoom_Click(self, zoom_type):
        """处理缩放控制点击事件"""
        print(f"缩放控制: {zoom_type}")
        # 这里可以添加具体的缩放控制逻辑