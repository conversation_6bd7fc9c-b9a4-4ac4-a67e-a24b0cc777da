import React, { useState } from 'react';
import { Button, Select, Dropdown, MenuProps } from 'antd';
import { DownOutlined,CaretRightOutlined ,ReconciliationOutlined } from '@ant-design/icons';


interface OptionType {
  label: string;
  value: string;
}

interface Type_Json {
  [key: string]: any;
}

interface Props_Select {
  Editor_Command: (commandInfo: Type_Json) => void;
}
const { Option } = Select;



const options: OptionType[] = [
  { label: '执行(一次)', value: '1' },
  { label: '执行(实时)', value: '2' },
  { label: '执行(循环)', value: '3' },
];

const Utils_Select_Schems: React.FC<Props_Select> = ({ Editor_Command }) => {
  const [selectedValue, setSelectedValue] = useState<OptionType | null>(null);

  // 处理选择框的值变化
  const handleSelectChange = (value: string) => {
    const selectedOption = options.find((opt) => opt.value === value);
    setSelectedValue(selectedOption || null);
  };

  // 按钮的显示文字
  const buttonText = selectedValue ? selectedValue.label : '方案';
  const commandInfo: Type_Json = { "111": "111" }; // 示例参数
  // 下拉菜单内容
  // 使用新的 items 格式定义菜单
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <span onClick={() =>  Editor_Command(commandInfo)}>
          情报【谣言】
        </span>
      ),
    },
    {
      key: '2',
      label: (
        <span onClick={() =>  Editor_Command(commandInfo)}>
         情报【预警】
        </span>
      ),
    },
    {
      key: '3',
      label: (
        <span onClick={() =>  Editor_Command({"333": "333"})}>
         本地【巡查】
        </span>
      ),
    },
  ];
  return (
    
   
    <Dropdown   
    
    menu={{ items }}  
    
    trigger={['click']} placement="topCenter">
      
      <Button type="primary" style={{ width: 80, height: 23,margin: "8px",textAlign: "center" , lineHeight: '23px', padding: '0', borderRadius: 4 ,backgroundColor: 'rgba(0,0,0,0.8)',borderColor: 'rgba(0,0,0,0.8)',}}>
             <ReconciliationOutlined   style={{ color: 'white',}} />{buttonText} 
      </Button>
    </Dropdown>
    
  );
};

export default Utils_Select_Schems;