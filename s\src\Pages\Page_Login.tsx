import React, { useEffect, useState } from 'react';
import Particles from "@tsparticles/react";
import { initParticlesEngine } from "@tsparticles/react";
import { loadFull } from 'tsparticles';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import type { MoveDirection } from "tsparticles-engine";
import { useNavigate } from 'react-router-dom';
import { Service_Requests } from '@/Core/Core_Control';
import { useAuth } from '@/Core/Core_AuthContent';
import { Form, Input, Checkbox, Button, message, notification  } from 'antd';
// import { LoginData } from '@/Mock/PageLogin_Data' // 引入mock数据
import '@/Styles/Page_Login.css'

const Page_Login: React.FC = () => {
  const [isEngineReady, setIsEngineReady] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false)
  const { user, login, refreshMenus  } = useAuth();
  const navigate = useNavigate();

  /** 
   * 如果用户已经登录，直接跳转到首页
  */
  useEffect(() => {
    if (user) {
      console.log('如果 user.home 存在，则跳转到 user.home，否则跳转到默认的首页')
      // 如果 user.home 存在，则跳转到 user.home，否则跳转到默认的首页
      const HomePath = user.redirect || '/Page_Translate';
      navigate(HomePath, { replace: true });
    }
  }, [user, navigate]);

  useEffect(() => {
    let isMounted = true;
    let tsParticlesInstance: any = null;
  
    const initializeParticles = async () => {
      if (!isMounted) return;
  
      await initParticlesEngine(async (engine) => {
        await loadFull(engine);
      });
  
      setIsEngineReady(true);
    };
  
    initializeParticles();
  
    return () => {
      isMounted = false;
    };
  }, []);

  // 初始化填入账号名和密码
  useEffect(() => {
    setUsername('演示账号');
    setPassword('csc18010587617');
  }, []);

  const particlesOptions = {
    background: {
      color: '#01010f',
    },
    fpsLimit: 60,
    interactivity: {
      events: {
        onClick: { enable: true, mode: 'push' },
        onHover: { enable: true, mode: 'repulse' },
      },
      modes: {
        push: { particles_nb: 4 },
        repulse: { distance: 200, duration: 0.4 },
      },
    },
    particles: {
      color: { value: '#ffffff' },
      links: {
        color: '#ffffff',
        distance: 150,
        enable: true,
        opacity: 0.3,
      },
      move: {
        direction: "right" as MoveDirection,
        enable: true,
        outMode: "out" as const,
        random: true,
        speed: 10,
        straight: true,
      },
      number: {
        density: { enable: true, area: 800 },
        value: 90,
      },
      opacity: {
        value: 0.5,
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: { min: 1, max: 3 },
      },
    },
    detectRetina: true,
  };

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('执行登录，用户名:', username, '密码:', password);

    // 验证用户名和密码不为空
    if (username.length === 0 || password.length === 0) {
      notification.warning({
        message: '登录失败',
        description: '账号信息为空，请补全！',
        placement: 'topRight',
        duration: 4,
      });
      return;
    }

    setLoading(true);

    // 按照原始JavaScript代码的格式构建请求数据
    let Requests_Data = {
      "user_id": "CSC",
      "user_token": "login",
      "data_class": "Sentinel",
      "data_type": 'System',
      "data_methods": "user_login_authentication_verify",
      "data_argument": `{}`,
      "data_kwargs": JSON.stringify({"User_Name": username, "User_Password": password}),
    };

    console.log('发送登录请求，参数:', Requests_Data);

    // 使用Service_Requests类进行异步请求
    const serviceRequests = new Service_Requests();
    serviceRequests.Async(Requests_Data)
      .then((Result: any) => {
        console.log('登录请求结果:', Result);
        if (Result && Result['Status'] === 'Success') {
          notification.success({
            message: '登录成功',
            description: '欢迎回来，' + Result['User_Name'],
            placement: 'topRight',
            duration: 4,
          });

          // 构建用户信息对象
          const userInfo = {
            username: Result['User_Name'],
            roles: Result['User_Role'],
            usertoken: Result['User_Token'],
            redirect: Result['Link_Home'],
            userface: Result['User_Face'],
          };

          // 保存用户token到localStorage（与原始代码一致）
          localStorage.setItem('User_Token', Result['User_Token']);

          // 登录用户并刷新菜单
          login(userInfo);
          refreshMenus();

          // 跳转到首页
          const HomePath = userInfo.redirect || '/Page_Home';
          navigate(HomePath, { replace: true });
        } else {
          console.log('登录失败，返回状态:', Result);
          notification.warning({
            message: '登录失败',
            description: '账号密码错误或者授权未通过！',
            placement: 'topRight',
            duration: 4,
          });
        }
        setLoading(false);
      })
      .catch((err: any) => {
        console.error('登录请求异常:', err);
        notification.error({
          message: '网络错误',
          description: '网络出错！',
          placement: 'topRight',
          duration: 4,
        });
        setLoading(false);
      });
  };

  return (
    <div className="login-page">
      {!isEngineReady && <div className="loading">引擎加载中...</div>}
      <Particles
        id="tsparticles"
        options={particlesOptions}
        key={isEngineReady ? 'particles-ready' : 'particles-not-ready'}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: -1,
          pointerEvents: 'none',
        }}
      />
      <div className="login-form-container">
        <h2>欢迎回来</h2>
        <p>请输入你的账户信息</p>
        <form onSubmit={handleLogin}  className='login-form-wapper'>
          <input 
            type="text" 
            placeholder="用户名或邮箱" 
            value={username} 
            onChange={handleUsernameChange} 
            required
          />
          <div className="password-input-container">
            <input 
              type={showPassword ? 'text' : 'password'} 
              placeholder="密码" 
              value={password} 
              onChange={handlePasswordChange} 
              required
            />
            {password.length > 0 && (
              <button 
                type="button" 
                className="toggle-password-btn" 
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </button>
            )}
          </div>
          <Button type="primary" htmlType="submit" loading={loading} className="custom-login-button">
            登录
          </Button>
        </form>
        <div className="login-footer">
          <a 
            href="#"
            onClick={(e) => {
              e.preventDefault(); // 阻止默认跳转
              message.success('找回密码请联系我们: 028-87469311'); // 显示提示信息
            }}
          >
            忘记密码？
          </a>

          <a 
            href="#"
            onClick={(e) => {
              e.preventDefault(); // 阻止默认跳转
              message.success('暂时未开放账号注册，试用请联系我们: 028-87469311'); // 显示提示信息
            }}
          >
            注册账号
          </a>
        </div>
      </div>
    </div>
  );
};

export default Page_Login;