/**
  *  动态修改webpack配置
  *  1.确保 TypeScript 和 Webpack 都能正确解析路径别名
  *  2.入没生效 安装下 "npm install react-app-rewired@latest customize-cra@latest"
  *  3.安装npm install --save-dev terser-webpack-plugin 在打包时将console.log去除（开发文件有打印，打包后的文件无打印）
*/
const { override, addWebpackAlias } = require('customize-cra')
const path = require('path')
const TerserPlugin = require('terser-webpack-plugin')

// 添加 .less 支持的配置函数
const addLessLoader = () => (config) => {
  config.module.rules.push({
    test: /\.less$/,
    use: ['style-loader', 'css-loader', 'less-loader'],
    include: path.resolve(__dirname, 'src'),
    exclude: /node_modules/,
  });
  return config;
};

// 配置 TerserPlugin 删除 console
const disableConsoleLog = () => (config) => {
  if (process.env.NODE_ENV === 'production') {
    config.optimization.minimizer = config.optimization.minimizer.map(mini => {
      if (mini.constructor.name === 'TerserPlugin') {
        return new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true, // 删除所有 console.*
              drop_debugger: true, // 删除 debugger
            },
          },
        });
      }
      return mini;
    });
  }
  return config;
};

module.exports = override(
  addWebpackAlias({
    // 指定@符指向src目录
    '@': path.resolve(__dirname, 'src'),
  }),
  addLessLoader(),
  disableConsoleLog()
)


