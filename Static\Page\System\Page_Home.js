/****************************************图表初始化注册***************************************************/ 
// --------------------------------------- 今日数据汇总
var options = {
    series: [{
        name: '舆情走势',
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    }],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        type: 'area',
        height: 340,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        },
        dropShadow: {
            enabled: false,
            top: 3,
            left: 14,
            blur: 4,
            opacity: 0.10,
        }
    },
    legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: -25
    },
    dataLabels: {
        enabled: false
    },
    stroke: {
        show: true,
        width: 3,
        curve: 'smooth'
    },
    tooltip: {
        theme: 'dark',
        y: {
            formatter: function (val) {
                return val + " 条 "
            }
        }
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            gradientToColors: ['#fff', 'rgba(255, 255, 255, 0.65)'],
            shadeIntensity: 1,
            type: 'vertical',
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0.1,
            //stops: [0, 50, 65, 91]
        },
    },
    grid: {
        show: true,
        borderColor: 'rgba(255, 255, 255, 0.12)',
        strokeDashArray: 5,
    },
    colors: ["#fff", "rgba(255, 255, 255, 0.65)"],
    yaxis: {
        labels: {
            formatter: function (value) {
                return value + "条";
            }
        },
    },
    xaxis: {
        categories: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'],
        labels: {
            formatter: function (value) {
                return value + "点";
            }
        },
    }
};
var Today_Sentiment_Time_Crawl = new ApexCharts(document.querySelector("#chart1"), options);
Today_Sentiment_Time_Crawl.render();
// ---------------------------------------媒体类型 饼状图
var options = {
    series: [],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        height: 380,
        type: 'pie',
    },
    // colors: ["#673ab7", "#32ab13", "#f02769", "#ffc107", "#198fed"],
    colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"],
    labels: [],
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                height: 360
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};
var Today_Sentiment_Platform_Type_Bar = new ApexCharts(document.querySelector("#Today_Sentiment_Platform_Type_Bar"), options);
Today_Sentiment_Platform_Type_Bar.render();
// ---------------------------------------情感类型 饼状图
var Today_Sentiment_Emotion_Bar_Options = {
    series: [],
    chart: {
        foreColor: 'rgba(255, 255, 255, 0.65)',
        height: 380,
        type: 'donut',
    },
    // colors: ["#673ab7", "#32ab13", "#f02769", "#ffc107", "#198fed"],
    colors: ["#30b817", "#176eb8", "#dc3545"],
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                height: 320
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};
var Today_Sentiment_Emotion_Bar = new ApexCharts(document.querySelector("#Today_Sentiment_Emotion_Bar"), Today_Sentiment_Emotion_Bar_Options);
Today_Sentiment_Emotion_Bar.render();

// ---------------------------------------关键词 词云
var Keyword_WordCloud_Option = {
    tooltip: {
        show: true,
        formatter: function(item) {
            return item[0] + ': 触发' + item[1] + '次'
        }
    },
    list: [],
    fontFamily: 'SimHei, sans-serif',
    color: 'random-light',
    rotateRatio: 0,
    rotationSteps: 2,
    backgroundColor: 'transparent',
    drawOutOfBound: false,
    shrinkToFit: true,
    // color: '#15a4fa',
    // backgroundColor:'rgba(255, 255, 255, 0)',
    drawBoundingBox: true, // 是否绘制边界框
    // shape: 'diamond',
    shape: 'rectangular',
    ellipticity: 0.8,
    noDataLoadingOption: {                                  // 无数据提示。
        backgroundColor: 'rgba(255, 255, 255, 0)',
        text: '暂无数据',
        textStyle: {
            color: '#15a4fa',
            fontSize: 14
        }
    },
    click: function(item, dimension, event) {
        console.log(`选中的文本内容: ${item[0]}`);
        HanderKeywordToDetails(item[0]);
    },
};
var Today_Sentiment_Keyword_WordCloud = new Js2WordCloud(document.getElementById('Today_Sentiment_Keyword_WordCloud'));
Today_Sentiment_Keyword_WordCloud.setOption(Keyword_WordCloud_Option);

/****************************************数据请求***************************************************/ 
function Requests_Sentiment_Info() {
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": "return_domestic_visualization",
        "data_argument": `{}`,
        "data_kwargs":{},
    };
    __Service_Requests = new Service_Requests("Sync",Requests_Data);
    Result  = __Service_Requests.callMethod();
    console.log('Result222:',Result);
    var Today_Sentiment_Emotion_Bar_Label = [];
    var Today_Sentiment_Emotion_Bar_Value = [];
    // var TodaySentimentCount = 0;
    Result.Today_Sentiment_Emotion_Info.map(item => {
        // console.log('item',item);
        // TodaySentimentCount += item[1];
        if (item[0] === '正面') {
            document.getElementById('Today_Sentiment_Emotion_Positive').innerText = item[1];
        } else if(item[0] === '中性') {
            document.getElementById('Today_Sentiment_Emotion_Neutral').innerText = item[1];
        } else {
            document.getElementById('Today_Sentiment_Emotion_Negative').innerText = item[1];
        };
        Today_Sentiment_Emotion_Bar_Label.push(item[0]);
        Today_Sentiment_Emotion_Bar_Value.push(item[1]);
    });
    document.getElementById('Today_Sentiment_Count').innerText = Result.Today_Sentiment_Count;
    // ------------------------------总览率的划分
    Object.keys(Result.Today_Sentiment_Time_Percent).forEach(key => {
        document.getElementById('Today_Percent_' + key).innerText = Result.Today_Sentiment_Time_Percent[key];
    });
    // ------------------------------汇总曲线图数据渲染
    Today_Sentiment_Time_Crawl.updateSeries([{
        name: '舆情走势',
        data: Result.Today_Sentiment_Time_Crawl
    }]);
    // -------------------------------媒体类型 图表渲染
    var Today_Sentiment_Platform_Type_Bar_Label = [];
    var Today_Sentiment_Platform_Type_Bar_value = [];
    Result.Today_Sentiment_Platform_Type.map(item => {
        Today_Sentiment_Platform_Type_Bar_Label.push(item[0]);
        Today_Sentiment_Platform_Type_Bar_value.push(item[1]);
    });
    // 如果需要更新 labels，可以使用以下方法
    Today_Sentiment_Platform_Type_Bar.updateOptions({
        labels: Today_Sentiment_Platform_Type_Bar_Label
    }, false, false);
    // 重新渲染图表
    Today_Sentiment_Platform_Type_Bar.updateSeries(Today_Sentiment_Platform_Type_Bar_value);
    // -------------------------------情感类型 图表渲染
    Today_Sentiment_Emotion_Bar.updateOptions({
        labels: Today_Sentiment_Emotion_Bar_Label
    }, false, false);
    Today_Sentiment_Emotion_Bar.updateSeries(Today_Sentiment_Emotion_Bar_Value);
    // -------------------------------关键词分析
    Keyword_WordCloud_Option.list = Result.Today_Sentiment_Key;
    Today_Sentiment_Keyword_WordCloud.setOption(Keyword_WordCloud_Option);
    
    // WordCloud.minFontSize = "15px"
    // // 计算最大权重
    // const maxWeight = Math.max(...Result['Today_Sentiment_Key'].map(item => item[1]));
    // // console.log('maxWeight:',maxWeight)
    // // 归一化权重，并将其缩放到0到40的范围
    // const normalizedList = Result['Today_Sentiment_Key'].map(item => {
    //     const normalizedWeight = Math.round((item[1] / maxWeight) * 400);
    //     return [item[0], normalizedWeight]; // 返回包含单词和归一化权重的数组
    // });
    // // 配置词云参数
    // const options = {
    //     list: normalizedList,
    //     gridSize: Math.round(10 * $('#Today_Sentiment_Keyword_WordCloud').width() / 1024),
    //     weightFactor: function (size) {
    //         return size * $('#Today_Sentiment_Keyword_WordCloud').width() / 1024;
    //     },
    //     fontFamily: 'SimHei, sans-serif',
    //     color: 'random-light',
    //     rotateRatio: 0,
    //     rotationSteps: 2,
    //     backgroundColor: 'transparent',
    //     drawOutOfBound: false,
    //     // minSize: 12,
    //     // shrinkToFit: true,
    //     // // shape: 'circle'
    //     // shape: 'diamond'
    //     shrinkToFit: true, // 允许单词缩小以适应空间
    //     sizeRange: [12, 40], // 调整字体大小范围
    //     layoutAnimation: false, // 关闭布局动画
    //     shape: 'rectangular', // 使用正方形形状
    // };
    // // 渲染词云
    // WordCloud(document.getElementById('Today_Sentiment_Keyword_WordCloud'), options);
    // --------------------------------最热文章 内容渲染
    var mission_active_totle =''
    for (var i = Result.Today_Sentiment_Hot_Article.length-1;i>=0;i--) {
        var mission_info =  Result.Today_Sentiment_Hot_Article[i]
        var mission_info_one = `
            <div class="item" onclick="cheack_url('${mission_info['Url']}')">
                <span style="color:#ffc107;">${mission_info['Platform']} - </span>${mission_info['Title']}
            </div>`
        mission_active_totle += mission_info_one
    }
    document.getElementById("scroll-content").innerHTML = mission_active_totle;
};

/****************************************最热文章***************************************************/ 
// 开始滚动
function startScroll() {
    // 滚动速度（毫秒）
    let scrollSpeed = 1000;
    var scrollContainer = $('#scroll-container');
    var scrollContent = $('#scroll-content');
    var firstItemHeight = scrollContent.children('.item').first().outerHeight(true); // 获取第一行内容的高度
    function scroll() {
        scrollContent.animate({top: '-=' + firstItemHeight + 'px'}, scrollSpeed, 'linear', function(){
            var firstItem = $(this).children('.item').first(); // 获取第一行内容
            $(this).append(firstItem); // 将第一行内容追加到最后
            $(this).css('top', 0); // 重置滚动内容的位置
            setTimeout(scroll, 0); // 递归调用滚动函数，实现无缝滚动
        });
    };
    scroll(); // 开始滚动
};

// 停止滚动
function stopScroll() {
    $('#scroll-content').stop(); // 停止滚动动画
};

// 当鼠标悬停在区域时停止滚动，移开时继续滚动
$('#scroll-container').hover(stopScroll, startScroll);

/****************************************链接跳转***************************************************/ 
function cheack_url(url) {
    window.open(url);
};


/****************************************总览数据审核***************************************************/ 
function HanderToDetails(val) {
    console.log('总览数据审核:',val);
    localStorage.setItem('HanderDetailsEmotion',val);
    window.location.href="Service_Page?Page=Page_Details";
};

/****************************************总览数据审核***************************************************/ 
function HanderKeywordToDetails(val) {
    console.log('总览数据审核:',val);
    localStorage.setItem('HanderDetailsKeyword',val);
    window.location.href="Service_Page?Page=Page_Details";
};

