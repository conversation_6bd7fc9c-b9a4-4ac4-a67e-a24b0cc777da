import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Checkbox,
  Radio,
  Select,
  DatePicker,
  Row,
  Col,
  Space,
  Typography,
  Pagination,
  message,
  Tooltip,
  Modal,
  Form,
  Tag,
  Spin,
  Divider
} from 'antd';
import {
  SearchOutlined,
  DownOutlined,
  UpOutlined,
  StarOutlined,
  FolderOutlined,
  BellOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EditOutlined,
  UnorderedListOutlined,
  MenuOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import '../Styles/Page_Information_Monitor.css';


const { Option } = Select;
const { RangePicker } = DatePicker;

// 数据类型定义
interface ArticleInfo {
  id: string;
  title: string;
  content: string;
  source: string;
  author: string;
  publishTime: string;
  emotion: string;
  mediaType: string;
  link: string;
  selected?: boolean;
}

const Page_Information_Monitor: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [articleList, setArticleList] = useState<ArticleInfo[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchType, setSearchType] = useState('All');
  const [timeRange, setTimeRange] = useState('Today');
  const [customTimeVisible, setCustomTimeVisible] = useState(false);
  const [mediaTypes, setMediaTypes] = useState<string[]>(['All']);
  const [emotionTypes, setEmotionTypes] = useState<string[]>(['All']);
  const [mediaAttributes, setMediaAttributes] = useState<string[]>(['All']);
  const [monitorTypes, setMonitorTypes] = useState<string[]>(['All']);
  const [showCount, setShowCount] = useState('All');
  const [publishArea, setPublishArea] = useState('全部');
  const [ipLocation, setIpLocation] = useState('全部');
  const [sentimentCategory, setSentimentCategory] = useState('全部');
  const [articleCategory, setArticleCategory] = useState('全部');
  const [viewType, setViewType] = useState('normal');
  const [collapsed, setCollapsed] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);

  // 媒体类型统计数据
  const mediaTypeStats = {
    'All': 0,
    '网页': 0,
    '微博': 0,
    'APP': 0,
    '论坛': 0,
    '报刊': 0,
    '视频': 0,
    '头条': 0,
    '搜狐': 0,
    '问答': 0,
    '评论': 0,
    '公众号': 0,
    '其他类型': 0
  };

  // 情感类型统计数据
  const emotionTypeStats = {
    'All': 0,
    '正面': 0,
    '中性': 0,
    '负面': 0
  };

  // 初始化数据
  useEffect(() => {
    loadArticleList();
  }, []);

  // 加载文章列表
  const loadArticleList = async () => {
    setLoading(true);
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟数据
      const mockArticles: ArticleInfo[] = [
        {
          id: '1',
          title: '科技创新推动经济高质量发展取得新突破',
          content: '随着科技创新的不断深入，我国经济发展质量持续提升，新兴产业蓬勃发展，为经济增长注入新动力。',
          source: '人民日报',
          author: '张记者',
          publishTime: '2025-01-25 14:30:25',
          emotion: '正面',
          mediaType: '网页',
          link: 'https://example.com/article1'
        },
        {
          id: '2',
          title: '教育改革深入推进，素质教育成效显著',
          content: '在教育改革的推动下，素质教育理念深入人心，学生综合素质得到全面提升，教育质量不断改善。',
          source: '光明日报',
          author: '李记者',
          publishTime: '2025-01-25 13:45:12',
          emotion: '正面',
          mediaType: '微博',
          link: 'https://example.com/article2'
        },
        {
          id: '3',
          title: '环保政策效果明显，生态环境持续改善',
          content: '在严格的环保政策推动下，各地生态环境质量显著改善，绿色发展理念深入人心，可持续发展取得新进展。',
          source: '环球时报',
          author: '王记者',
          publishTime: '2025-01-25 12:20:08',
          emotion: '正面',
          mediaType: 'APP',
          link: 'https://example.com/article3'
        },
        {
          id: '4',
          title: '数字化转型加速，传统产业焕发新活力',
          content: '数字化转型浪潮下，传统产业积极拥抱新技术，实现转型升级，焕发出新的发展活力。',
          source: '经济日报',
          author: '赵记者',
          publishTime: '2025-01-25 11:15:33',
          emotion: '中性',
          mediaType: '论坛',
          link: 'https://example.com/article4'
        },
        {
          id: '5',
          title: '文化产业蓬勃发展，文化自信不断增强',
          content: '文化产业快速发展，优秀传统文化与现代科技深度融合，文化自信不断增强，文化软实力显著提升。',
          source: '中国文化报',
          author: '钱记者',
          publishTime: '2025-01-25 10:30:45',
          emotion: '正面',
          mediaType: '公众号',
          link: 'https://example.com/article5'
        }
      ];

      setArticleList(mockArticles);
      setTotalCount(mockArticles.length);
    } catch (error) {
      message.error('加载文章列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    loadArticleList();
    message.success('搜索完成');
  };

  // 处理筛选
  const handleFilter = () => {
    loadArticleList();
    message.success('筛选完成');
  };

  // 重置筛选条件
  const handleReset = () => {
    setTimeRange('Today');
    setMediaTypes(['All']);
    setEmotionTypes(['All']);
    setMediaAttributes(['All']);
    setMonitorTypes(['All']);
    setShowCount('All');
    setPublishArea('全部');
    setIpLocation('全部');
    setSentimentCategory('全部');
    setArticleCategory('全部');
    setSearchKeyword('');
    setSearchType('All');
    setCustomTimeVisible(false);
    message.success('重置完成');
  };

  // 刷新数据
  const handleRefresh = () => {
    loadArticleList();
  };

  // 保存参数
  const handleSave = () => {
    message.success('参数保存成功');
  };

  // 切换折叠状态
  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedArticles(articleList.map(article => article.id));
    } else {
      setSelectedArticles([]);
    }
  };

  // 处理时间范围选择
  const handleTimeRangeClick = (range: string) => {
    setTimeRange(range);
    if (range === 'Other') {
      setCustomTimeVisible(true);
    } else {
      setCustomTimeVisible(false);
    }
  };

  // 处理视图类型切换
  const handleViewTypeChange = (type: string) => {
    setViewType(type);
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 第一个卡片：筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <div
          style={{
            maxHeight: collapsed ? '10rem' : 'none',
            overflow: 'hidden',
            transition: 'max-height 0.3s ease'
          }}
        >
          <div>
            {/* 方案名称和视图切换 */}
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Typography.Title level={4} style={{ margin: 0, display: 'inline-block' }}>
                  方案_默认
                  <Tooltip title="方案编辑">
                    <EditOutlined style={{ marginLeft: 8, cursor: 'pointer', fontSize: 16 }} />
                  </Tooltip>
                  <Tooltip title="方案列表">
                    <UnorderedListOutlined style={{ marginLeft: 8, cursor: 'pointer', fontSize: 16 }} />
                  </Tooltip>
                </Typography.Title>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Space>
                  <Button
                    size="small"
                    type={viewType === 'simple' ? 'primary' : 'default'}
                    onClick={() => handleViewTypeChange('simple')}
                    icon={<UnorderedListOutlined />}
                    className="view-switch-btn"
                  >
                    精简视图
                  </Button>
                  <Button
                    size="small"
                    type={viewType === 'normal' ? 'primary' : 'default'}
                    onClick={() => handleViewTypeChange('normal')}
                    icon={<MenuOutlined />}
                    className="view-switch-btn"
                  >
                    普通视图
                  </Button>
                  <Button
                    size="small"
                    type={viewType === 'ocr' ? 'primary' : 'default'}
                    onClick={() => handleViewTypeChange('ocr')}
                    icon={<AppstoreOutlined />}
                    className="view-switch-btn"
                  >
                    OCR视图
                  </Button>
                </Space>
              </Col>
            </Row>

            {/* 时间范围 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>时间范围:</Typography.Text>
              </Col>
              <Col span={22}>
                <Space wrap style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                  <Button
                    size="small"
                    type={timeRange === 'TwoDay' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('TwoDay')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    24小时
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'Today' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('Today')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    今天
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'Yesterday' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('Yesterday')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    昨天
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'ThreeDay' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('ThreeDay')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    近三天
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'WeekDay' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('WeekDay')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    近七天
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'MouthDay' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('MouthDay')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    近30天
                  </Button>
                  <Button
                    size="small"
                    type={timeRange === 'Other' ? 'primary' : 'default'}
                    onClick={() => handleTimeRangeClick('Other')}
                    style={{ margin: '4px', padding: '0 16px' }}
                    className="no-border-btn"
                  >
                    自定义
                  </Button>
                  {customTimeVisible && (
                    <div style={{ padding: '0 8px' }}>
                      <Input
                        placeholder="点击选择日期范围"
                        style={{ fontSize: '0.875rem', width: '240px' }}
                      />
                    </div>
                  )}
                </Space>
              </Col>
            </Row>

            {/* 媒体类型 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>媒体类型:</Typography.Text>
              </Col>
              <Col span={22}>
                <Checkbox.Group value={mediaTypes} onChange={setMediaTypes}>
                  <Space wrap>
                    <Checkbox value="All">全部({mediaTypeStats['All']})</Checkbox>
                    <Checkbox value="网页">网页({mediaTypeStats['网页']})</Checkbox>
                    <Checkbox value="微博">微博({mediaTypeStats['微博']})</Checkbox>
                    <Checkbox value="APP">APP({mediaTypeStats['APP']})</Checkbox>
                    <Checkbox value="论坛">论坛({mediaTypeStats['论坛']})</Checkbox>
                    <Checkbox value="报刊">报刊({mediaTypeStats['报刊']})</Checkbox>
                    <Checkbox value="视频">视频({mediaTypeStats['视频']})</Checkbox>
                    <Checkbox value="头条">头条({mediaTypeStats['头条']})</Checkbox>
                    <Checkbox value="搜狐">搜狐({mediaTypeStats['搜狐']})</Checkbox>
                    <Checkbox value="问答">问答({mediaTypeStats['问答']})</Checkbox>
                    <Checkbox value="评论">评论({mediaTypeStats['评论']})</Checkbox>
                    <Checkbox value="公众号">公众号({mediaTypeStats['公众号']})</Checkbox>
                    <Checkbox value="其他类型">其他类型({mediaTypeStats['其他类型']})</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Col>
            </Row>

            {/* 情感属性 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>情感属性:</Typography.Text>
              </Col>
              <Col span={22}>
                <Checkbox.Group value={emotionTypes} onChange={setEmotionTypes}>
                  <Space wrap>
                    <Checkbox value="All">全部({emotionTypeStats['All']})</Checkbox>
                    <Checkbox value="正面">正面({emotionTypeStats['正面']})</Checkbox>
                    <Checkbox value="中性">中性({emotionTypeStats['中性']})</Checkbox>
                    <Checkbox value="负面">负面({emotionTypeStats['负面']})</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Col>
            </Row>

            {/* 多媒属性 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>多媒属性:</Typography.Text>
              </Col>
              <Col span={22}>
                <Checkbox.Group value={mediaAttributes} onChange={setMediaAttributes}>
                  <Space wrap>
                    <Checkbox value="All">全部</Checkbox>
                    <Checkbox value="音频">音频</Checkbox>
                    <Checkbox value="视频">视频</Checkbox>
                    <Checkbox value="广播">广播</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Col>
            </Row>

            {/* 重点数源 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>重点数源:</Typography.Text>
              </Col>
              <Col span={22}>
                <Checkbox.Group value={monitorTypes} onChange={setMonitorTypes}>
                  <Space wrap>
                    <Checkbox value="All">全部</Checkbox>
                    <Checkbox value="重点关注">重点关注</Checkbox>
                    <Checkbox value="不看屏蔽数源">不看屏蔽数源</Checkbox>
                  </Space>
                </Checkbox.Group>
              </Col>
            </Row>

            {/* 推送条数 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>推送条数:</Typography.Text>
              </Col>
              <Col span={22}>
                <Radio.Group value={showCount} onChange={(e) => setShowCount(e.target.value)}>
                  <Space wrap>
                    <Radio value="All">全部</Radio>
                    <Radio value="100">100条</Radio>
                    <Radio value="200">200条</Radio>
                    <Radio value="500">500条</Radio>
                    <Radio value="1000">1000条</Radio>
                    <Radio value="2000">2000条</Radio>
                    <Radio value="5000">5000条</Radio>
                  </Space>
                </Radio.Group>
              </Col>
            </Row>

            {/* 地区和类别选择 */}
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>发布地区:</Typography.Text>
              </Col>
              <Col span={3}>
                <Select value={publishArea} onChange={setPublishArea} style={{ width: '100%' }}>
                  <Option value="全部">全部</Option>
                  <Option value="北京">北京</Option>
                  <Option value="上海">上海</Option>
                  <Option value="广东">广东</Option>
                </Select>
              </Col>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>IP属地:</Typography.Text>
              </Col>
              <Col span={3}>
                <Select value={ipLocation} onChange={setIpLocation} style={{ width: '100%' }}>
                  <Option value="全部">全部</Option>
                  <Option value="北京">北京</Option>
                  <Option value="上海">上海</Option>
                  <Option value="广东">广东</Option>
                </Select>
              </Col>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>舆情类别:</Typography.Text>
              </Col>
              <Col span={3}>
                <Select value={sentimentCategory} onChange={setSentimentCategory} style={{ width: '100%' }}>
                  <Option value="全部">全部</Option>
                  <Option value="新闻媒体">新闻媒体</Option>
                  <Option value="资讯媒体">资讯媒体</Option>
                  <Option value="企业">企业</Option>
                  <Option value="政务">政务</Option>
                  <Option value="组织">组织</Option>
                  <Option value="个人">个人</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Col>
              <Col span={2}>
                <Typography.Text style={{ fontSize: '1rem' }}>文章类别:</Typography.Text>
              </Col>
              <Col span={3}>
                <Select value={articleCategory} onChange={setArticleCategory} style={{ width: '100%' }}>
                  <Option value="全部">全部</Option>
                  <Option value="社会">社会</Option>
                  <Option value="经济">经济</Option>
                  <Option value="财经">财经</Option>
                  <Option value="体育">体育</Option>
                  <Option value="教育">教育</Option>
                  <Option value="科技">科技</Option>
                  <Option value="娱乐">娱乐</Option>
                  <Option value="公益">公益</Option>
                  <Option value="时政">时政</Option>
                  <Option value="军事">军事</Option>
                  <Option value="文化">文化</Option>
                  <Option value="汽车">汽车</Option>
                  <Option value="能源">能源</Option>
                  <Option value="旅行">旅行</Option>
                  <Option value="女性">女性</Option>
                  <Option value="动漫">动漫</Option>
                  <Option value="农业">农业</Option>
                  <Option value="银行">银行</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Col>
            </Row>
          </div>
        </div>

        {/* 操作按钮 */}
        <div style={{ marginTop: 16 }}>
          <Space>
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
            <Button type="default" onClick={handleFilter} style={{ float: 'right' }} className="no-border-btn">
              筛选
            </Button>
            <Button onClick={handleReset} style={{ float: 'right' }} className="no-border-btn">
              重置
            </Button>
            <Button
              onClick={toggleCollapse}
              style={{ float: 'right' }}
              icon={collapsed ? <DownOutlined /> : <UpOutlined />}
              className="no-border-btn"
            >
              {collapsed ? '展开' : '收起'}
            </Button>
          </Space>
        </div>
      </Card>

      {/* 第二个卡片：文章列表 */}
      <Card>
        {/* 操作栏 */}
        <Row align="middle" style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Space>
              <Checkbox
                checked={selectAll}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选
              </Checkbox>
              <Tooltip title="批量收藏">
                <StarOutlined style={{ fontSize: 20, cursor: 'pointer', marginLeft: 8 }} />
              </Tooltip>
              <Tooltip title="批量加入素材库">
                <FolderOutlined style={{ fontSize: 20, cursor: 'pointer', marginLeft: 8 }} />
              </Tooltip>
              <Tooltip title="批量报警">
                <BellOutlined style={{ fontSize: 20, cursor: 'pointer', marginLeft: 8 }} />
              </Tooltip>
              <Tooltip title="批量下载">
                <DownloadOutlined style={{ fontSize: 20, cursor: 'pointer', marginLeft: 8 }} />
              </Tooltip>
            </Space>
          </Col>
          <Col span={16}>
            <Row justify="end" align="middle">
              <Col>
                <Space>
                  <Typography.Text>共{totalCount}条消息</Typography.Text>
                  <Button size="small" onClick={handleRefresh} icon={<ReloadOutlined />} className="no-border-btn">
                    本地刷新
                  </Button>
                  <Button size="small" icon={<DownloadOutlined />} className="no-border-btn">
                    全部下载
                  </Button>
                  <Input.Group compact>
                    <Select
                      value={searchType}
                      onChange={setSearchType}
                      style={{ width: 100 }}
                    >
                      <Option value="All">全部</Option>
                      <Option value="Keyword">关键词</Option>
                      <Option value="Title">标题</Option>
                      <Option value="Content">内容</Option>
                      <Option value="Source">来源</Option>
                      <Option value="Link">链接</Option>
                    </Select>
                    <Input
                      style={{ width: 200 }}
                      placeholder="信息标题/内容/来源查询"
                      value={searchKeyword}
                      onChange={(e) => setSearchKeyword(e.target.value)}
                      onPressEnter={handleSearch}
                    />
                    <Button
                      type="default"
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                      className="no-border-btn"
                    />
                  </Input.Group>
                </Space>
              </Col>
            </Row>
          </Col>
        </Row>

        <Divider style={{ margin: '16px 0' }} />

        {/* 文章内容区域 */}
        <Spin spinning={loading}>
          <div style={{ minHeight: 400 }}>
            {articleList.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '50px 0', color: '#999' }}>
                暂无数据
              </div>
            ) : (
              articleList.map((article, index) => (
                <div key={article.id} className="info-card">
                  <Row align="top" gutter={16}>
                    <Col flex="none" style={{ width: 40 }}>
                      <div className="checkbox-container">
                        <Checkbox
                          checked={selectedArticles.includes(article.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedArticles([...selectedArticles, article.id]);
                            } else {
                              setSelectedArticles(selectedArticles.filter(id => id !== article.id));
                              setSelectAll(false);
                            }
                          }}
                        />
                        <Typography.Text className="item-number">
                          {(currentPage - 1) * pageSize + index + 1}
                        </Typography.Text>
                      </div>
                    </Col>
                    <Col flex="auto">
                      <Space direction="vertical" style={{ width: '100%' }} size="small">
                        {/* 标题行 */}
                        <Row justify="space-between" align="middle">
                          <Col span={20}>
                            <Typography.Link
                              href={article.link}
                              target="_blank"
                              className="article-title"
                            >
                              {article.title}
                            </Typography.Link>
                          </Col>
                          <Col span={4} style={{ textAlign: 'right' }}>
                            <Space size="small">
                              <Tag
                                color={article.emotion === '正面' ? 'green' : article.emotion === '负面' ? 'red' : 'default'}
                                className="emotion-tag"
                              >
                                {article.emotion}
                              </Tag>
                              <Tag className="media-tag">{article.mediaType}</Tag>
                            </Space>
                          </Col>
                        </Row>

                        {/* 内容行 */}
                        <Typography.Paragraph
                          ellipsis={{ rows: 2, expandable: true, symbol: '更多' }}
                          className="article-content"
                        >
                          {article.content}
                        </Typography.Paragraph>

                        {/* 底部信息行 */}
                        <Row justify="space-between" align="middle" style={{ marginTop: 8 }}>
                          <Col span={24}>
                            <Space size="large">
                              <Typography.Text className="article-meta">
                                来源: {article.source}
                              </Typography.Text>
                              <Typography.Text className="article-meta">
                                作者: {article.author}
                              </Typography.Text>
                              <Typography.Text className="article-meta">
                                {article.publishTime}
                              </Typography.Text>
                            </Space>
                          </Col>
                        </Row>
                      </Space>
                    </Col>
                  </Row>

                  {/* 操作图标 - 右下角 */}
                  <div className="action-icons">
                    <Tooltip title="收藏">
                      <StarOutlined className="action-icon" />
                    </Tooltip>
                    <Tooltip title="加入素材库">
                      <FolderOutlined className="action-icon" />
                    </Tooltip>
                    <Tooltip title="报警">
                      <BellOutlined className="action-icon" />
                    </Tooltip>
                    <Tooltip title="下载">
                      <DownloadOutlined className="action-icon" />
                    </Tooltip>
                  </div>
                </div>
              ))
            )}
          </div>
        </Spin>

        {/* 分页 */}
        <Row justify="end" style={{ marginTop: 16 }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            }}
          />
        </Row>
      </Card>
    </div>
  );
};

export default Page_Information_Monitor;
