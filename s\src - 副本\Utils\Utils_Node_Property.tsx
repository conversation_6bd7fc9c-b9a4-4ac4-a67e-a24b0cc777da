import React, { useState } from 'react';
import { Button, message } from "antd";
interface Section {
    id: number;
    label: string;
    content: React.ReactNode; // 改为接受React节点
    isExpanded: boolean;
    height: number;
    contentType?: 'id'  |'info'|'form' | 'text' | 'buttons'; // 新增内容类型标识
    metadata?: any; // 扩展元数据
  }
  





  const renderContent = (section: Section) => {
    switch(section.contentType) {
      case 'id':
        return (
          <div style={{ height: '0px' }}>
    
          </div>
        );

    case 'info':
        return (
            <div style={{color: 'white',padding: '5px', }}>
                    <p id="Node_Info_Schemes" style={{  fontSize: '11px', textAlign: 'left', backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px -5px', height:"20px",width: '218px', flex: '1', padding: '2px 15px' }}>课题: 未选择节点</p>
                    <p id="Node_Info_Class"   style={{  fontSize: '11px', textAlign: 'left', backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px -5px', height:"20px",width: '218px', flex: '1', padding: '2px 15px' }}>模块: 未选择节点</p>
                    <p id="Node_Info_Type"    style={{  fontSize: '11px',                    backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px  -5px', height:"20px",width: '218px', flex: '1' ,padding: '2px 15px'}}>类型: 未选择节点</p>
                    <p id="Node_Info_Method"  style={{  fontSize: '11px',                    backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px  -5px', height:"20px",width: '218px', flex: '1' ,padding: '2px 15px'}}>方法: 未选择节点</p>
                    <p id="Node_Info_Explain" style={{  fontSize: '11px',                    backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px -5px', height:"20px",width: '218px', flex: '1' ,padding: '2px 15px'}}>说明: 未选择节点</p>
                    <p                        style={{  fontSize: '11px',                    backgroundColor: 'black',    border: '1px solid #ddd', borderRadius: '4px',  margin: '0px -5px', height:"20px",width: '218px', flex: '1' ,padding: '2px 15px'}}>状态:<span id="Node_Info_Status"  style={{color: 'red',}} ></span></p>
                </div>
        );
    case 'form':
        return (
          <div className="content-form">
            <input 
              id ="Input_Node_Label"
              type="text" 
              placeholder="课题【Unkown】"
              style={{ width: '96%', marginBottom: 8 }} 
            />

            <div style={{ display: 'flex', gap: 8 }}>
              <button id ="Button_Node_Update" style={{ flex: 1 }}>确认</button>
              <button style={{ flex: 1 }}>取消</button>
            </div>
          </div>
        );
      case 'buttons':
        return (
          <div className="button-group" style={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: '8px'
          }}>
            {['选项A', '选项B', '选项C'].map((text) => (
              <button 
                key={text}
                style={{ 
                  padding: '6px 12px',
                  fontSize: 12,
                  borderRadius: 4 
                }}
              >
                {text}
              </button>
            ))}
          </div>
        );
  
      default: // 默认文本内容
        return (
          <>
            <p style={{ margin: 0 }}>{section.content}</p>
            <p style={{ margin: '10px 0 0' }}>Additional content...</p>
          </>
        );
    }
  }
























const Utils_Node_Property: React.FC = () => {
    // 初始状态：3个可折叠区块
    const [sections, setSections] = useState<Section[]>([
      { id: 0, label: 'ID:',   content: <span>自定义内容结构</span>,    isExpanded: false, height: 0,  contentType: 'id' },
      { id: 1, label: '信息',  content: <span>自定义内容结构</span>,    isExpanded: true, height: 125,  contentType: 'info' },
      { id: 2, label: '基础',  content: <span>自定义内容结构</span>,    isExpanded: false, height: 200 , contentType: 'form' },
      { id: 3, label: '功能',  content: <span>自定义内容结构</span>,    isExpanded: false, height: 100 ,  contentType: 'buttons' },
      { id: 4, label: '连接',  content: <span>自定义内容结构</span>,    isExpanded: false, height: 200 , contentType: 'form' },
      { id: 5, label: '输出',  content: <span>自定义内容结构</span>,    isExpanded: false, height: 200 , contentType: 'text' },
    ]);
  
    // 展开/折叠处理
    const toggleSection = (id: number) => {
      setSections(sections.map(section => {




        if (section.id === id) {
            const shouldExpand = !section.isExpanded;
          return {
            ...section,
            isExpanded: !section.isExpanded,
            // height: section.isExpanded ? 10 : section.height // 展开高度自定义
            height: shouldExpand ? 
            section.height :  // 使用预设的展开高度
            section.height 
          };
        }
        return section;
      }));
    };
  
    return (
      <div style={{
        width: '220px',
        height: 'calc(100vh - 20px)', // 根据实际情况调整
        margin: '0px auto',
        fontFamily: 'Arial, sans-serif',
        overflowY: 'auto', // 启用垂直滚动
  
        /* 隐藏所有滚动条 */
        scrollbarWidth: 'none',          // Firefox
        msOverflowStyle: 'none',         // IE/Edge
        /* WebKit 浏览器伪类（需要全局样式或CSS模块）*/
        // '&::-webkit-scrollbar': {
        //     display: 'none'
        // }

      }}>
        {sections.map((section, index) => (
          <div
            key={section.id}
            style={{
              height: section.isExpanded ? 'auto' : '30px',
              marginBottom: '0px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              overflow: 'hidden',
              transition: 'all 0.3s ease-in-out'
            }}>
            {/* 可点击的标题区域 */}
            <div
              style={{
                padding: '5px',
                height:'30px',
                // margin: "10px 20px",
                backgroundColor: '#f8f9fa',
                borderBottom: '1px solid #ddd',
                cursor: 'pointer',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
              onClick={() => toggleSection(section.id)}>
              <h3 id="Node_Info_ID" style={{ margin: "10px -10px",padding: "10px 15px", display: "flex",height: "30px",fontSize:"12px"}}>{section.label}</h3>

              
              {section.id !== 0 && (  // 条件判断
                    <span style={{
                        width: "24px",  // 更合理的尺寸
                        height: "24px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        margin: "0px 5px 0px 0",  // 简化定位
                        padding:"0px 0px ",
                        transform: section.isExpanded ? 'rotate(270deg)' : 'rotate(0deg)',
                        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                      }}>
                    ▼
                    </span>
                )}



                
                            {/* <span style={{
                  width: "35px",        // 固定宽度
                  height: "35px",// 固定高度
                margin: "-10px -20px ",
                
                padding: "5px 5px", 
                display: "flex",
                transform: section.isExpanded ? 'rotate(270deg)' : 'rotate(0deg)',
                transformOrigin: "center center", // 关键：设置旋转中心
    
                // transition: 'transform 0.3s',
                // willChange: "transform" // 优化动画性能
              }}>
                ▼
              </span> */}
            </div>
  
            {/* 可展开的内容区域 */}
            <div
              style={{
                height: `${section.height}px`,
                // height: section.isExpanded ? 'auto' : '10px',
                maxHeight: section.isExpanded ? '1000px' : '10px',
                // padding: section.isExpanded ? '20px' : '0 20px',
                opacity: section.isExpanded ? 1 : 0,
                transition: 'all 0.3s ease-in-out',
                overflow: 'hidden'
              }}>
                  {/* <button id="button_test" > </button>
              <p style={{ margin: 0 }}>{section.content}</p>
              <p style={{ margin: '10px 0 0' }}>Additional content...</p> */}


                {renderContent(section)}


            </div>
          </div>
        ))}
      </div>
    );
  };

  
export default Utils_Node_Property;