 import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
 import { Node_Socket,SystemControl_Object_Inspection} from "./Node_System_Control_Object_Inspection";
 
 export class Node_System_Component_Object_Inspection extends ClassicPreset.Node<
    { [key in string]: ClassicPreset.Socket },
    { [key in string]: ClassicPreset.Socket },
    { [key in string]:| SystemControl_Object_Inspection}>  
  {
      width  = 388;
      height = 188;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new SystemControl_Object_Inspection(
        '【标题】:未知',

        0,
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }

      updateContent(Config:Record<string, any>){
        let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
        


      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    