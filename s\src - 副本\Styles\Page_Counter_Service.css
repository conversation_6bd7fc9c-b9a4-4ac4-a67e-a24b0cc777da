/* 反制服务页面样式 */
.counter-service-page {
  padding: 24px;
  min-height: 100vh;
  padding-bottom: 120px; /* 为底部购物车留出空间 */
}

/* 全局样式重置和修复 */
.counter-service-page * {
  box-sizing: border-box;
}

/* 确保所有按钮文字居中 */
.counter-service-page .ant-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  line-height: 1.5 !important;
  vertical-align: middle !important;
}

.counter-service-page .ant-btn > span {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  text-align: center !important;
}

/* 自定义圆形勾选框样式 */
.counter-service-page .ant-checkbox {
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

.counter-service-page .ant-radio {
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

/* 将复选框改为圆形 */
.counter-service-page .ant-checkbox-inner {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  border: 2px solid #d9d9d9 !important;
  background-color: #fff !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  transition: all 0.3s ease !important;
}

/* 复选框选中状态 - 整个圆形填满 */
.counter-service-page .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

/* 隐藏默认的勾选标记 */
.counter-service-page .ant-checkbox-checked .ant-checkbox-inner::after {
  display: none !important;
}

/* 单选框也改为圆形填满样式 */
.counter-service-page .ant-radio-inner {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  border: 2px solid #d9d9d9 !important;
  background-color: #fff !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  transition: all 0.3s ease !important;
}

/* 单选框选中状态 - 整个圆形填满 */
.counter-service-page .ant-radio-checked .ant-radio-inner {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

/* 隐藏默认的单选框中心点 */
.counter-service-page .ant-radio-checked .ant-radio-inner::after {
  display: none !important;
}

/* 单选框悬停效果 */
.counter-service-page .ant-radio:hover .ant-radio-inner {
  border-color: #40a9ff !important;
}

.counter-service-page .ant-radio-checked:hover .ant-radio-inner {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 单选框禁用状态 */
.counter-service-page .ant-radio-disabled .ant-radio-inner {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.counter-service-page .ant-radio-disabled.ant-radio-checked .ant-radio-inner {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

/* 单选框焦点状态 */
.counter-service-page .ant-radio-input:focus + .ant-radio-inner {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 复选框悬停效果 */
.counter-service-page .ant-checkbox:hover .ant-checkbox-inner {
  border-color: #40a9ff !important;
}

.counter-service-page .ant-checkbox-checked:hover .ant-checkbox-inner {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 确保复选框在不同状态下都是圆形 */
.counter-service-page .ant-checkbox-indeterminate .ant-checkbox-inner {
  border-radius: 50% !important;
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.counter-service-page .ant-checkbox-indeterminate .ant-checkbox-inner::after {
  display: none !important;
}

/* 禁用状态的圆形复选框 */
.counter-service-page .ant-checkbox-disabled .ant-checkbox-inner {
  border-radius: 50% !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.counter-service-page .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

/* 焦点状态 */
.counter-service-page .ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 确保圆形复选框在组中的间距 */
.counter-service-page .ant-checkbox-group .ant-checkbox-wrapper {
  margin-right: 16px !important;
  margin-bottom: 8px !important;
}

/* 确保圆形单选框在组中的间距 */
.counter-service-page .ant-radio-group .ant-radio-wrapper {
  margin-right: 16px !important;
  margin-bottom: 8px !important;
}

/* 确保复选框和单选框包装器对齐 */
.counter-service-page .ant-checkbox-wrapper {
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1.5 !important;
  margin-right: 16px !important;
  margin-bottom: 0 !important;
}

.counter-service-page .ant-radio-wrapper {
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1.5 !important;
  margin-right: 16px !important;
  margin-bottom: 0 !important;
}

/* 确保文字与复选框/单选框对齐 */
.counter-service-page .ant-checkbox + span,
.counter-service-page .ant-radio + span {
  padding-left: 8px !important;
  line-height: 1.5 !important;
  display: flex !important;
  align-items: center !important;
}

/* 操作类型选择器 */
.operation-selector {
  margin-bottom: 16px;
  text-align: center;
}

.operation-selector .ant-radio-group {
  display: inline-flex;
}

.operation-selector .ant-radio-button-wrapper {
  padding: 8px 24px !important;
  font-weight: 500;
  border-radius: 6px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1.5 !important;
  text-align: center !important;
  min-height: 32px !important;
}

.operation-selector .ant-radio-button-wrapper-checked {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.operation-selector .ant-radio-button-wrapper > span {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 筛选卡片 */
.filter-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
}

/* 目标链接容器 */
.target-url-container {
  margin-bottom: 16px;
}

.target-url-container label {
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 16px;
}

.filter-section label {
  font-weight: 600;
  margin-bottom: 12px;
  display: block;
  line-height: 1.5;
}

.filter-section .ant-checkbox-group,
.filter-section .ant-radio-group {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-actions {
  margin-top: 12px;
}

/* 联系信息区域 */
.contact-info-section {
  margin-top: 16px;
}

.contact-info-section label {
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

.contact-info-section .notice {
  margin-top: 16px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  color: #52c41a;
}

/* 通用提示信息 */
.general-notice {

  color: #fa8c16;
  font-size: 14px;
  text-align: center;
}

/* 服务列表 */
.service-list {
  margin-bottom: 16px;
}

.service-panel .ant-collapse-header {
  font-weight: 600;
  font-size: 16px;
  text-transform: uppercase;
}

.service-items {
  padding: 0;
}

.service-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-remark {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-control .ant-btn {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  min-width: 32px !important;
  border-radius: 4px;
}

.quantity-control .ant-btn .anticon {
  font-size: 12px;
  line-height: 1;
}

.quantity-control .ant-input-number {
  text-align: center;
}

.quantity-control .ant-input-number-input {
  text-align: center;
}

/* 底部购物车 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fa8c16;
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.cart-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cart-user .ant-avatar {
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-user .ant-avatar:hover {
  transform: scale(1.1);
}

.cart-user .credit-btn {
  background: #52c41a;
  border-color: #52c41a;
  font-weight: bold;
}

.cart-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 回到顶部按钮 */
.back-to-top-btn {
  position: fixed;
  bottom: 140px;
  right: 24px;
  z-index: 1001;
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.back-to-top-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 按钮通用样式 */
.counter-service-page .ant-btn {
  border-radius: 6px !important;
  font-weight: 500;
  transition: all 0.3s ease;
  height: auto !important;
  min-height: 32px !important;
}

.counter-service-page .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.counter-service-page .ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.counter-service-page .ant-btn-danger {
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.counter-service-page .ant-btn-danger:hover {
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.3);
  transform: translateY(-1px);
}

.counter-service-page .ant-btn-lg {
  min-height: 40px !important;
}

/* 复选框和单选框组样式 */
.counter-service-page .ant-checkbox-group,
.counter-service-page .ant-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.counter-service-page .ant-checkbox-wrapper,
.counter-service-page .ant-radio-wrapper {
  font-weight: 500;
  text-transform: uppercase;
}

/* 模态框样式 */
.counter-service-page .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.counter-service-page .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 8px 8px;
}

.counter-service-page .ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

/* 信用点模态框 */
.credit-modal .ant-modal-content {
  background: #fff;
}

/* 结算模态框 */
.checkout-modal .ant-modal-content {
  background: #fff;
}

.checkout-items {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.checkout-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

/* 编辑模态框 */
.edit-modal .ant-modal-content {
  background: #fff;
}

.edit-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.edit-label {
  width: 80px;
  flex-shrink: 0;
  margin-right: 16px;
  font-weight: bold;
  color: #262626;
  font-size: 14px;
  text-align: right;
}

.edit-content {
  flex: 1;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px 12px;
  color: #262626;
  text-align: center;
  line-height: 26px;
}

.edit-content .ant-input {
  background: #fff;
  color: #000;
}

/* 输入框样式 */
.counter-service-page .ant-input,
.counter-service-page .ant-input-number {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.counter-service-page .ant-input:focus,
.counter-service-page .ant-input-number:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 确保图标在按钮中居中 */
.counter-service-page .ant-btn .anticon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 修复输入框对齐 */
.counter-service-page .ant-input {
  display: flex;
  align-items: center;
}

.counter-service-page .ant-input-number {
  display: flex;
  align-items: center;
}

.counter-service-page .ant-input-number-input {
  height: 100%;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .counter-service-page {
    padding: 16px;
    padding-bottom: 140px;
  }

  .operation-selector .ant-radio-button-wrapper {
    padding: 6px 16px !important;
    font-size: 14px;
  }

  .cart-footer {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .cart-actions {
    width: 100%;
    justify-content: space-between;
  }

  .back-to-top-btn {
    bottom: 160px;
    right: 16px;
  }

  .service-item .ant-row {
    flex-direction: column;
    gap: 8px;
  }

  .service-item .ant-col {
    width: 100%;
  }

  .quantity-control {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .counter-service-page {
    padding: 12px;
    padding-bottom: 160px;
  }

  .cart-footer {
    padding: 8px 12px;
  }

  .cart-actions {
    flex-direction: column;
    gap: 8px;
  }

  .counter-service-page .ant-btn {
    font-size: 12px;
    height: 32px;
    padding: 0 12px;
  }

  .service-remark {
    max-width: 150px;
  }
}
