import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,AIControl_DeepSeek} from "./Node_AI_Control_DeepSeek";

export class Node_AI_Component_DeepSeek extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| AIControl_DeepSeek}> 
  {
    width  = 480;
    height = 428;
    constructor(Label: string,) {
      super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const ConentControl = new AIControl_DeepSeek(
        '【标题】:未知', // Label for the text area
        '0',
        '0',
        '1',

        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    