.main-content {
  display: flex;
  /* height: 100vh; */
  height: calc(100vh - 120px);
}

.sidebar {
  /* width: 250px;
  background: white;
  overflow-y: auto; */
  flex: 0 0 300px; /* 固定宽度 */
  border-right: 1px solid #e8e8e8; /* 右侧分割线 */
  padding-left: 20px;
  padding-right: 20px;
  overflow-y: auto;
  background-color: #fff; /* 可选背景色 */
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: white;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.message {
  max-width: 70%;
}

.message.user {
  margin-left: auto;
}

.message.bot {
  background-color: #f7f7f7;
  border-radius: 18px 18px 0 18px;
  padding: 10px 15px;
  align-self: flex-start;
}

.message.user .message-content {
  background-color: #e6f7ff;
  border-radius: 18px 0 18px 18px;
  padding: 10px 15px;
  align-self: flex-end;
}

.message-time {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 5px;
}

.input-container {
  display: flex;
  flex-direction: column;
}

.active-conversation {
  background-color: #e6f7ff;
}

/* .edit-icon-wrapper {
  position: absolute;
  top: 12px;
  right: 16px;
  cursor: pointer;
  color: #8c8c8c;
  font-size: 16px;
  display: flex;
  align-items: center;
} */
.edit-icon-wrapper {
  display: inline-block;
  margin-left: 8px;
  cursor: pointer;
}

.custom-list-item .ant-list-item-meta-title,
.custom-list-item .ant-list-item-meta-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}