/* 首页样式 */
.data-overview {
  padding: 1rem;
  background-color: #343a40;
  min-height: calc(100vh - 70px);
  font-family: 'Open Sans', sans-serif;
}

/* 概览卡片样式 */
.radius-15 {
  border-radius: 15px !important;
}

.card {
  box-shadow: 0 0.1rem 0.7rem rgb(0 0 0 / 18%);
  border: 1px solid rgba(0, 0, 0, 0);
  background-color: rgb(0 0 0 / 0.24);
  border-radius: 15px;
  transition: all 0.3s ease;
  color: #e5e9ec;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1.5rem rgb(0 0 0 / 25%);
}

.card-body {
  color: #e5e9ec;
  padding: 1.5rem;
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.14);
  color: #e5e9ec;
  font-size: 16px;
  font-weight: 600;
  padding: 1.25rem 1.5rem;
}

/* 数据卡片点击效果 */
.card h2 {
  cursor: pointer;
  transition: all 0.3s ease;
}

.card h2:hover {
  color: #0075ff;
  transform: scale(1.05);
}

.text-white {
  color: rgba(255, 255, 255, 0.87) !important;
}

.font-35 {
  font-size: 35px;
}

.font-14 {
  font-size: 14px;
}

.ml-auto {
  margin-left: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.d-flex {
  display: flex !important;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  height: 340px;
  width: 100%;
}

.chart-container canvas {
  max-height: 100%;
  max-width: 100%;
}

/* 热门文章列表样式 */
.hot-articles {
  max-height: 340px;
  overflow-y: auto;
  padding-right: 10px;
}

.hot-article-item {
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.hot-article-item:last-child {
  border-bottom: none;
}

.hot-article-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding-left: 10px;
  padding-right: 10px;
}

.article-title {
  color: rgba(255, 255, 255, 0.87);
  font-weight: 600;
  margin-bottom: 8px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.article-title:hover {
  color: #0075ff;
}

.text-muted {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-info {
  color: #17a2b8 !important;
}

/* 卡片组样式 */
.card-deck {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-top: 20px;
}

.card-deck .card {
  flex: 1;
  margin-bottom: 0;
}

/* 地区分布图样式 */
.region-chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

/* 数据统计动画 */
.data-overview .card {
  animation: slideInUp 0.6s ease-out;
}

.data-overview .card:nth-child(1) { animation-delay: 0.1s; }
.data-overview .card:nth-child(2) { animation-delay: 0.2s; }
.data-overview .card:nth-child(3) { animation-delay: 0.3s; }
.data-overview .card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 数字滚动动画 */
.counter-animation {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 图标样式 */
.data-icon {
  width: 35px;
  height: 35px;
  fill: currentColor;
  transition: all 0.3s ease;
}

.card:hover .data-icon {
  transform: rotate(10deg) scale(1.1);
}

/* 趋势箭头样式 */
.trend-arrow {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  margin-left: 5px;
}

.trend-up {
  color: #28a745;
}

.trend-down {
  color: #dc3545;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .card-deck {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .data-overview {
    padding: 15px;
  }

  .card-body {
    padding: 1rem;
  }

  .font-35 {
    font-size: 28px;
  }

  .chart-container,
  .hot-articles {
    height: 280px;
  }

  .region-chart-container {
    height: 300px;
  }

  .card-deck {
    gap: 10px;
  }

  .hot-article-item {
    padding: 10px 0;
  }

  .article-title {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .data-overview {
    padding: 10px;
  }

  .card {
    margin-bottom: 15px;
  }

  .card-body {
    padding: 0.75rem;
  }

  .font-35 {
    font-size: 24px;
  }

  .font-14 {
    font-size: 12px;
  }

  .chart-container,
  .hot-articles {
    height: 250px;
  }

  .region-chart-container {
    height: 250px;
  }

  .d-lg-flex {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .hot-article-item {
    padding: 8px 0;
  }

  .article-title {
    font-size: 0.85rem;
    margin-bottom: 5px;
  }
}