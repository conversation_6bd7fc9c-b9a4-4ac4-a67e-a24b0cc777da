
class Function_Core_Config {
   
    constructor(Type,Data){
        this.Type = Type
        this.Data = Data

    }
    callMethod(){
        this.CONFIG()
        try {
            return eval(`this.${this.Type}()`);
        } catch (Error) {
            return {'Error': Error, "Type": this.Type, "Data": this.Data, "Status": "Failed"}
            
        }
    }


    CONFIG(){
        this.Result = {'Status': 'Failed'}
    }
    ServerConfig (){
        var ServerConfig_Info
        // let User_Token = this.data['User_Token']
        var Page_InfoStr = JSON.stringify(this.Data)
        console.log('Requests_Data:',Page_InfoStr)
        let Requests_Data = {
            "user_id": "CSC",
            "user_token":"this.Data.User_Token" ,
            "data_class": "Sentinel",
            "data_type": 'Service_Web',
            "data_methods": "return_core_web_servercnfig",
            "data_argument": `{}`,
            "data_kwargs":Page_InfoStr,
        }

        console.log('Requests_Data:',Requests_Data,)

        $.ajax({
            type: "POST",
            url: "http://192.168.122.110:9311/Service_Interface",
            // url: "http://192.168.122.9:9311/Service_Interface",
            // 发送同步请求
            async: false,
            // data: Requests_Data,
            contentType: 'application/json',
            processData: false,
            data: JSON.stringify(Requests_Data),
            success: function (Respones) {
                console.log('Respones:',Respones)
                if ('Status' in Respones && Respones['Status'] == "Failed") {
                    // window.location = '/Service_Page?Page=Page_Not_Found'
                }
                return ServerConfig_Info = Respones
                // console.log('Sentinel_BaseConfig1:',this.Sentinel_BaseConfig)
                // this.Resoytr(this.Sentinel_BaseConfig)

            }
        });


        return ServerConfig_Info
        // console.log('Sentinel_BaseConfig2:',this.Sentinel_BaseConfig)



    }



}


