/* 主容器 */
/* .file-upload-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-width: none;
  margin: 0;
  padding: 24px;
  background:  #2A3147;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(114, 136, 160, 0.3),
    0 2px 8px rgba(255, 255, 255, 0.1) inset;
  color: #ffffff;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
  overflow-y: auto;
  
}
.file-upload-container h3 {
  color: #ffffff;
} */

.file-container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}


.file-header-title-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 4px solid #1890ff;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.file-header-title-wrapper:hover {
  background: rgba(255, 255, 255, 0.08);
}

.file-header-title-wrapper h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f0f0f0;
  letter-spacing: 0.3px;
}

.file-header-divider {
  border: 0;
  border-top: 1px solid #e8e8e8;
  margin: 20px 0;
}

.file-ff_fileupload_wrap {
  padding: 0 20px;
}



/* 容器头部布局 */
.container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 拖拽区域 */
.drop-zone-video {
  height: 200px;
  border: 2px dashed #c9c9c9;
  border-radius: 8px;
  /* background-color: #2A3147; */
  background-color: transparent !important;
  display: flex;
  flex-direction: column;
  align-items: center;     /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.drop-zone-video:hover {
  border-color: #007bff;
  background-color: #2A3147;
}

.drop-zone-video p {
  margin: 4px 0;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  width: 100%;
  
}

.drop-zone-video input[type='file'] {
  display: none;
}

/* 错误信息 */
.error-message {
  color: #ff6b6b;
  font-size: 14px;
  margin-top: 12px;
  display: block;
}

/* 文件列表和文件项 */
.file-list {
  margin-top: 24px;
  list-style: none;
  padding: 0;
  /* margin: 0; */
  /* background-color: #2A3147; 主要背景颜色 */
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  /* background-color: #2A3147; */
  background-color: transparent;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.file-item:hover {
  border-color: #1890ff;
  /* background-color: #2A3147; */
}

.file-icon,
.file-preview {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
  font-size: 24px;
  color: #ffffff;
}

.file-info {
  flex: 1;
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #ffffff;
  word-break: break-all;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.file-size {
  color: #ffffff;
  font-size: 12px;
}

/* 文件操作按钮容器 */
.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-actions-icons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-icon {
  font-size: 16px;
  cursor: pointer;
  transition: color 0.2s;
  color: #ffffff;
}

.download-icon:hover {
  color: #1890ff;
}

.reprocess-icon:hover {
  color: #52c41a;
}

/* 批量提交按钮 */
.batch-submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.batch-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.batch-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.batch-submit-btn:hover::before {
  left: 100%;
}

.batch-submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* 删除按钮 */
.remove-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.remove-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
}

.remove-btn:hover::before {
  left: 100%;
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* 上传按钮 */
.upload-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-btn:hover {
  background-color: #0056b3;
}
/* .content-card {
  background-color: #2A3147;
} */

/* 提交图标 */
.submit-icon {
  cursor: pointer;
  font-size: 16px;
  color: #52c41a;
  padding: 8px;
  border-radius: 50%;
  background: rgba(82, 196, 26, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  position: relative;
  overflow: hidden;
}

.submit-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(82, 196, 26, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.submit-icon:hover {
  color: #389e0d;
  background: rgba(82, 196, 26, 0.15);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.submit-icon:hover::before {
  width: 100%;
  height: 100%;
}

.submit-icon:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
}

/* 处理类型选择器 */
.process-type-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.process-type-selector label {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.process-type-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #2A3147 url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23ffffff' d='M4.427 6.427L8 10l3.573-3.573A1 1 0 0010.427 5H5.573a1 1 0 00-.854 1.573z'/%3E%3C/svg%3E") no-repeat right 8px center;
  background-size: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  width: 75px;
}

.process-type-dropdown:hover {
  border-color: #9ca3af;
  background-color: #2A3147;
}

.process-type-dropdown:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #2A3147;
}

.process-type-dropdown option {
  padding: 8px 12px;
  font-size: 14px;
  color: #ffffff;
  background: #2A3147;
}

/* 批量处理表格 */
.audio-process-table-container {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #2A3147;
  min-height: 260px;
}

.audio-process-table-container h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #2A3147; /* 改为深色背景 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); 
}

.table-header h3 {
  margin: 0;
  color: #ffffff; /* 改为白色文字 */
  font-size: 16px;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.reset-btn,
.process-btn {
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 16px;
}

.reset-btn {
  background-color: #2A3147;
  color: #ffffff;
  border: 1px solid #d9d9d9;
}

.reset-btn:hover {
  background-color: #2A3147;
  color: #ffffff;
  border-color: #bfbfbf;
}

.process-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.process-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.audio-process-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: #2A3147; /* 表格背景 */
}

.audio-process-table thead {
  background-color: #2A3147;
}

.audio-process-table thead th {
  text-align: center;
}

.audio-process-table th,
.audio-process-table td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 改为半透明白色边框 */
}

.audio-process-table th {
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.audio-process-table td {
  color: #ffffff; /* 表格数据文字改为白色 */
  background: #2A3147; /* 表格单元格背景 */
}

.audio-process-table tbody tr:hover {
  background-color: #f9f9f9;
}

.audio-process-table tbody tr:last-child td {
  border-bottom: none;
}

/* 进度条 */
.progress-container {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill.with-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 12px;
  color: #ffffff;
  white-space: nowrap;
  min-width: 35px;
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

.status-等待上传 {
  background-color: #f0f0f0;
  color: #ffffff;
}

.status-上传中 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-服务器处理中 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-已上传 {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-上传失败 {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 删除图标 */
.delete-icon {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.delete-icon:hover {
  background-color: #fff2f0;
  color: #ff1f1f;
}

/* 动画 */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.processing-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .process-type-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .process-type-dropdown {
    width: 120px;
  }
}


.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-selector label {
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}

.model-dropdown {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  background-color: #2A3147;
  color: white;
  cursor: pointer;
  min-width: 80px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.model-dropdown option {
  background-color: #2A3147;
  color: white;
  padding: 4px 8px;
}

.model-dropdown:hover {
  border-color: #bfbfbf;
}

.model-dropdown:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Modal弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: #2A3147; /* 修改背景色 */
  color: white;         /* 全局字体颜色设为白 */
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 70vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #444c66; /* 深色边线 */
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white; /* 标题文字 */
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: #3a415c;
  color: white;
}

.modal-body {
  padding: 24px;
}

.keywords-list {
  margin-bottom: 16px;
}

.keyword-item {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  align-items: center;
}

.keyword-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #555d77;
  border-radius: 4px;
  font-size: 14px;
  background-color: #1e2333;
  color: white;
  transition: border-color 0.2s;
}

.keyword-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.remove-keyword-btn {
  padding: 6px 12px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.remove-keyword-btn:hover {
  background-color: #ff7875;
}

.add-keyword-btn {
  width: 100%;
  padding: 10px;
  background-color: #3c445c;
  color: white;
  border: 1px dashed #999;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.add-keyword-btn:hover {
  background-color: #4a5373;
  border-color: #ccc;
  color: #91d5ff;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #444c66;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.confirm-btn:hover {
  background-color: #40a9ff;
}


/* 关键词配置按钮样式 */
.keyword-config-btn {
  padding: 6px 12px;
  background-color: #2A3147;
  color: white;
  border: none;
  border: 1px solid white; 
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.keyword-config-btn:hover {
  background-color: #2A3147;
}

/* 确保弹窗在最顶层 */
.modal-overlay {
  z-index: 9999 !important;
}


/* 媒体播放器模态框样式 */
.media-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.media-player-container {
  background: #2A3147;
  color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  min-width: 400px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.media-player-title-bar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #495166;
}

.media-player-title {
  margin: 0;
  color: white;
  font-size: 16px;
  font-weight: 600;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-player-close-x {
  background: none;
  border: none;
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.media-player-close-x:hover {
  background: #495166;
  color: white;
}

.media-player-content {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 100px;
}

.media-player-video {
  width: 100%;
  max-width: 600px;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-player-audio {
  width: 100%;
  max-width: 500px;
  height: 54px;
}

.media-player-loading {
  text-align: center;
  padding: 20px;
  color: #1890ff;
  font-size: 14px;
}

.media-player-loading-content {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.media-player-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #1890ff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.media-player-error {
  color: #ff7875;
  text-align: center;
  padding: 20px;
  background: #3a2f31;
  border: 1px solid #5a3a3a;
  border-radius: 6px;
  margin: 10px 0;
}

.media-player-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #495166;
}

.media-player-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.media-player-btn-primary {
  background: #1890ff;
  color: white;
}

.media-player-btn-primary:hover {
  background: #40a9ff;
}

.media-player-btn-success {
  background: #52c41a;
  color: white;
}

.media-player-btn-success:hover {
  background: #73d13d;
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


@media (max-width: 768px) {
  .eq-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .eq-dropdown {
    width: 120px;
  }
}

.eq-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.eq-selector label {
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}

.eq-dropdown {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  background-color: #2A3147;
  color: white;
  cursor: pointer;
  min-width: 80px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.eq-dropdown option {
  background-color: #2A3147;
  color: white;
  padding: 4px 8px;
}

.eq-dropdown:hover {
  border-color: #bfbfbf;
}

.eq-dropdown:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Modal弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: #2A3147; /* 修改背景色 */
  color: white;         /* 全局字体颜色设为白 */
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 70vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #444c66; /* 深色边线 */
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white; /* 标题文字 */
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: #3a415c;
  color: white;
}

.modal-body {
  padding: 24px;
}

.file-container-translate-table {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  margin-top: 40px;
  height: 39px;
}

.file-table-title-wrapper {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-left: 4px solid #1890ff;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.file-table-title-wrapper:hover {
  background: rgba(255, 255, 255, 0.08);
}

.file-table-title-wrapper h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f0f0f0;
  letter-spacing: 0.3px;
}

.play_loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9998;
}

.play_loadingContainer {
  text-align: center;
}

.play_spinner {
  width: 48px;
  height: 48px;
  border: 5px solid #f0f0f0;
  border-top: 5px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin: 0 auto;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}