import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
// import { TextAreaControl }  from "./TextAreaControl";
import { TextareaControl,Textarea_Rumor_Control,Control_Preview_Score }  from "./MyButtonComponent";


// ---------------------------------------------------------------------------------------- 自定义节点
const socket = new ClassicPreset.Socket("socket");

export class ButtonControl extends ClassicPreset.Control {
  constructor(public label: string, public onClick: () => void) {
    super();
  }
}

export class ProgressControl extends ClassicPreset.Control {
  constructor(public percent: number) {
    super();
  }
}


export class SwitchControl extends ClassicPreset.Control {
  constructor(
    public checked: boolean,
    public onChange: (checked: boolean) => void
  ) {
    super();
  }
}



export type SelectOption<T extends string | number> = {
  value: T;
  label: string;
};



export class InputControl extends ClassicPreset.Control {
  // 必须使用public修饰符
  constructor(
    public value: string,
    public onChange: (value: string) => void
  ) {
    super();
  }

  

}

// export class TextAreaControl extends ClassicPreset.Control {
//   constructor(
//     public value: string,
//     public onChange: (value: string) => void
//   ) {
//     super();
//   }
// }

// export class TextInputControl extends ClassicPreset.Control {
//   constructor(public label: string, public value: string, public onChange: (value: string) => void) {
//     super();
//   }

//   render() {
//     return (
//       <div>
//         <label>{this.label}</label>
//         <Input
//           value={this.value}
//           onChange={(e) => {
//             const newValue = e.target.value;
//             this.value = newValue;
//             this.onChange(newValue);
//           }}
//         />
//       </div>
//     );
//   }
// }















export class Node_System extends ClassicPreset.Node<
{},
{}, 
{}> 
 {
  width = 0;
  height = 0;

  constructor(Label: string,) {
    super("");
   
  }
  

  data() {
    return {};
  }
    // 添加点击事件处理方法
 }





 export class Node_Process extends ClassicPreset.Node<
 {},
 { exec: ClassicPreset.Socket }, 
 { Label: ClassicPreset.InputControl<"text">}> 
  {
   width = 180;
   height = 90;
 
   constructor(Label: string,) {
     super(Label);
     this.addOutput("exec", new ClassicPreset.Output(socket, "Exec"));
   }
   
 
   execute(_: never, forward: (output: "exec") => void) {
     forward("exec");
   }
 
   data() {
     return {};
   }
     // 添加点击事件处理方法
  }
 
 
 
 



  export class Node_Object extends ClassicPreset.Node<
  { input_1: ClassicPreset.Socket},
  { exec: ClassicPreset.Socket }, 
  { Label: ClassicPreset.InputControl<"text">}> 
   {
    width = 380;
    height = 190;
  
    constructor(Label: string,) {
      super(Label);
      const input_1 = new ClassicPreset.Input(socket, "工作流入口");
      this.addInput("input_1", input_1);

      
      this.addOutput("exec", new ClassicPreset.Output(socket, "Exec"));
    }
    
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    }
  
    data() {
      return {};
    }
      // 添加点击事件处理方法
   }
  
  







   export class Node_Start extends ClassicPreset.Node<
   { input_1: ClassicPreset.Socket,},
   { output_1: ClassicPreset.Socket, }, 
   { Label: ClassicPreset.InputControl<"text">,
    button:ButtonControl,
   }
   > 
    {
     width = 280;
     height = 290;
   
     constructor(Label: string,) {
       super(Label);
      //  const input_1 = new ClassicPreset.Input(socket, "工作流入口");
      //  this.addInput("input_1", input_1);
      
      this.addControl("button",new ButtonControl("启动", () => {}));
       this.addOutput("output_1", new ClassicPreset.Output(socket, "链接"));
     }
     
   
     execute(_: never, forward: (output: "exec") => void) {
       forward("exec");
     }
   
     data() {
       return {};
     }
       // 添加点击事件处理方法
    }
   
   
 
 










  export class Node_End extends ClassicPreset.Node<{
    left: ClassicPreset.Socket;},
     {  }, 
  
  { Label: ClassicPreset.InputControl<"text">}
  > {
    width = 180;
    height = 90;
  
    constructor(Label: string,) {
      super("结束");
      const left = new ClassicPreset.Input(socket, Label);
      this.addInput("left", left);
      // this.addOutput("exec", new ClassicPreset.Output(socket, "Exec"));
    }
  
    execute(_: never, forward: (output: "left") => void) {
      forward("left");
    }
  
    data() {
      return {};
    }
  }














  

  export class Node_DataSource_Rumor extends ClassicPreset.Node<
  
  {

    left: ClassicPreset.Socket;},
  
  
    { Output_1: ClassicPreset.Socket; }, 
  
  { 
    Label: ClassicPreset.InputControl<"text">,
    content:Textarea_Rumor_Control,
  }
  > {
    width = 580;
    height = 428;
  
    constructor(Label: string,) {
      super(Label);





      const left = new ClassicPreset.Input(socket, Label);
      this.addInput("left", left);
      // const content =  new ClassicPreset.InputControl("text", { 
      //   initial: "a12313123",
      //   change(value) {
      //     console.log('Input value changed:', value);
      //   }
      // })
      // this.addControl("content", content);

      // content.setValue("11111")



      this.addOutput("Output_1", new ClassicPreset.Output(socket, "输出"));


      const textAreaControl = new Textarea_Rumor_Control(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title: any) => {
          console.log('TextArea value changed:', title);
        }
      );
      this.addControl('content', textAreaControl);
      


      // this.inputControl = new InputControl(
      //       "", // 初始值
      //       (value) => {
      //         console.log('输入值更新:', value);
      //         this.update(); // 触发节点更新
      //       }
      //     );

      
      // this.addOutput("exec", new ClassicPreset.Output(socket, "Exec"));
    }

    updateLabel(newLabel: string) {
      this.label = newLabel; // 更新节点的名称
      
    }

    updateContent(Intelligence:Record<string, any>){
      const contentControl = this.controls.content;

      contentControl.setContent(Intelligence)
      console.log('Intelligence:', Intelligence);
      // const safeGet = (key: string) => Intelligence?.[key] || "未知";
      // console.log('Intelligence:',safeGet('INTELLIGENCE_TITLE'));
      // if (contentControl) {
      //   try {
      //     // @ts-ignore - 忽略下一行的类型检查
      //     contentControl.setContent(safeGet('INTELLIGENCE_TITLE'))
          
      //   } catch (error) {
          
      //   }
       
      // }

      // const titleControl = this.controls.textAreaControl;


    }
    // 动态更新内容的方法
  // updateContentValue(newValue: string) {
  //   console.log('Input value changed:', newValue);
    
  //   const contentControl = this.controls.content;
    
  //   if (contentControl) {
  //     // 正确设置值的方式
  //     contentControl.setValue(newValue);
      
  //     // 触发控件更新
  //     // content.setValue("11111")
      
  //     // 触发节点更新
  //     // this.update();
  //   }
  //   }
    // updateContentValue(newValue: string) {
    //   const contentControl = this.controls['content'] as ClassicPreset.InputControl;
    //   if (contentControl) {
    //     contentControl.value = newValue;
    //     contentControl.update(); // 触发控件更新
    //   }
    // }
  
    execute(_: never, forward: (output: "left") => void) {
      forward("left");
    
    }
  
    data() {
      return {};
    }
  }
  











  

  export class Node_Preview extends ClassicPreset.Node<
  
  {

    left: ClassicPreset.Socket;},
  
  
    {  }, 
  
  { 
    Label: ClassicPreset.InputControl<"text">,
    content: ClassicPreset.InputControl<"text">,
    description:TextareaControl,
  }
  > {
    width = 580;
    height = 388;
  
    constructor(Label: string,) {
      super(Label);





      const left = new ClassicPreset.Input(socket, Label);
      this.addInput("left", left);
      const content =  new ClassicPreset.InputControl("text", { 
        initial: "a12313123",
        change(value) {
          console.log('Input value changed:', value);
        }
      })
      this.addControl("content", content);

      content.setValue("11111")


      const textAreaControl = new TextareaControl(
        'Description', // Label for the text area
        '1111', // Initial value
        (value: any) => {
          console.log('TextArea value changed:', value);
        }
      );
      this.addControl('description', textAreaControl);
      


      // this.inputControl = new InputControl(
      //       "", // 初始值
      //       (value) => {
      //         console.log('输入值更新:', value);
      //         this.update(); // 触发节点更新
      //       }
      //     );

      
      // this.addOutput("exec", new ClassicPreset.Output(socket, "Exec"));
    }

    updateLabel(newLabel: string) {
      this.label = newLabel; // 更新节点的名称
      
    }
    // 动态更新内容的方法
  updateContentValue(newValue: string) {
    console.log('Input value changed:', newValue);
    
    const contentControl = this.controls.content;
    
    if (contentControl) {
      // 正确设置值的方式
      contentControl.setValue(newValue);
      
      // 触发控件更新
      // content.setValue("11111")
      
      // 触发节点更新
      // this.update();
    }
    }
    // updateContentValue(newValue: string) {
    //   const contentControl = this.controls['content'] as ClassicPreset.InputControl;
    //   if (contentControl) {
    //     contentControl.value = newValue;
    //     contentControl.update(); // 触发控件更新
    //   }
    // }
  
    execute(_: never, forward: (output: "left") => void) {
      forward("left");
    
    }
  
    data() {
      return {};
    }
  }
  



  export class Node_DataSource_Score extends ClassicPreset.Node<
  
  {

    left: ClassicPreset.Socket;},
  
  
    { Output_1: ClassicPreset.Socket; }, 
  
  { 
    Label: ClassicPreset.InputControl<"text">,
    content:Textarea_Rumor_Control,
  }
  > {
    width = 580;
    height = 428;
  
    constructor(Label: string,) {
      super(Label);





      const left = new ClassicPreset.Input(socket, Label);
      this.addInput("left", left);



      this.addOutput("Output_1", new ClassicPreset.Output(socket, "输出"));


      const textAreaControl = new Textarea_Rumor_Control(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title: any) => {
          console.log('TextArea value changed:', title);
        }
      );
      this.addControl('content', textAreaControl);

    }

    updateLabel(newLabel: string) {
      this.label = newLabel; // 更新节点的名称
      
    }

    updateContent(Intelligence:Record<string, any>){
      const contentControl = this.controls.content;

      contentControl.setContent(Intelligence)
      console.log('Intelligence:', Intelligence);
      


    }
  
    execute(_: never, forward: (output: "left") => void) {
      forward("left");
    
    }
  
    data() {
      return {};
    }
  }
  








export class Node_DataSource extends ClassicPreset.Node<
{ exec1: ClassicPreset.Socket, exec2: ClassicPreset.Socket,},
{ text: ClassicPreset.Socket, 
  image: ClassicPreset.Socket,
  media: ClassicPreset.Socket,

},
{ Label: ClassicPreset.InputControl<"text"> }> 
{
    width = 180;
    height = 390;
  
    constructor(Label: string,) {
      super(Label);
    //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
    this.addInput("exec1", new ClassicPreset.Input(socket, "预处理模型1", true));
    this.addInput("exec2", new ClassicPreset.Input(socket, "预处理模型2", true));
    this.addOutput("text", new ClassicPreset.Output(socket, "文本"));
    this.addOutput("image", new ClassicPreset.Output(socket, "图片"));
    this.addOutput("media", new ClassicPreset.Output(socket, "媒体"));


    }
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    }
  }
  












// export class Node_DataSource_Score extends ClassicPreset.Node<
// { input_1:ClassicPreset.Socket,
//   exec1: ClassicPreset.Socket, 
//   exec2: ClassicPreset.Socket,},
// { text: ClassicPreset.Socket, 
//   image: ClassicPreset.Socket,
//   media: ClassicPreset.Socket,

// },
// { Label: ClassicPreset.InputControl<"text"> }> 
// {
//     width = 180;
//     height = 390;
  
//     constructor(Label: string,) {
//       super(Label);
//     //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
//     this.addInput("input_1", new ClassicPreset.Input(socket, "方案入口", true));
//     this.addInput("exec1", new ClassicPreset.Input(socket, "预处理模型1", true));
//     this.addInput("exec2", new ClassicPreset.Input(socket, "预处理模型2", true));
//     this.addOutput("text", new ClassicPreset.Output(socket, "输出"));



//     }
  
//     execute(_: never, forward: (output: "exec") => void) {
//       forward("exec");
//     }
//   }
  








// export class Node_DataSource extends ClassicPreset.Node<
// { exec1: ClassicPreset.Socket, exec2: ClassicPreset.Socket,},
// { date: ClassicPreset.Socket, 
//   title: ClassicPreset.Socket,
//   content: ClassicPreset.Socket,
//   author: ClassicPreset.Socket,
//   image: ClassicPreset.Socket,
//   sound: ClassicPreset.Socket,
//   video: ClassicPreset.Socket,
// },
// { Label: ClassicPreset.InputControl<"text"> }> 
// {
//     width = 180;
//     height = 390;
  
//     constructor(Label: string,) {
//       super(Label);
//     //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
//     this.addInput("exec1", new ClassicPreset.Input(socket, "预处理模型1", true));
//     this.addInput("exec2", new ClassicPreset.Input(socket, "预处理模型2", true));
//     this.addOutput("date", new ClassicPreset.Output(socket, ""));
//     this.addOutput("title", new ClassicPreset.Output(socket, "标题"));
//     this.addOutput("content", new ClassicPreset.Output(socket, "内容"));
//     this.addOutput("author", new ClassicPreset.Output(socket, "作者"));
//     this.addOutput("image", new ClassicPreset.Output(socket, "图片"));
//     this.addOutput("sound", new ClassicPreset.Output(socket, "音频"));
//     this.addOutput("video", new ClassicPreset.Output(socket, "视频"));

//     // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));

      
//     //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
//     }
  
//     execute(_: never, forward: (output: "exec") => void) {
//       forward("exec");
//     }
//   }
  


  



export class Node_Analysis extends ClassicPreset.Node<
{ exec1: ClassicPreset.Socket, exec2: ClassicPreset.Socket,},
{ date: ClassicPreset.Socket, 
  title: ClassicPreset.Socket,
  content: ClassicPreset.Socket,
  author: ClassicPreset.Socket,
  image: ClassicPreset.Socket,
  sound: ClassicPreset.Socket,
  video: ClassicPreset.Socket,
},
{ Label: ClassicPreset.InputControl<"text"> }> 
{
    width = 180;
    height = 290;
  
    constructor(Label: string,) {
      super(Label);
    //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
    this.addInput("exec1", new ClassicPreset.Input(socket, "云算力", true));
    this.addInput("exec2", new ClassicPreset.Input(socket, "本地算力", true));
    this.addOutput("date", new ClassicPreset.Output(socket, "舆情"));
    this.addOutput("title", new ClassicPreset.Output(socket, "情报"));
    this.addOutput("content", new ClassicPreset.Output(socket, "翻译"));

    // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));

      
    //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
    }
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    }
  }
  

  



  export class Node_Model extends ClassicPreset.Node<
  {  exec1: ClassicPreset.Socket,
     exec2: ClassicPreset.Socket, 
     exec3: ClassicPreset.Socket,
     exec4: ClassicPreset.Socket,
    },
  { date: ClassicPreset.Socket, 
    title: ClassicPreset.Socket,
    content: ClassicPreset.Socket,
    author: ClassicPreset.Socket,
    image: ClassicPreset.Socket,
    sound: ClassicPreset.Socket,
    video: ClassicPreset.Socket,
  },
  { Label: ClassicPreset.InputControl<"text"> }> 
  {
      width = 180;
      height = 290;
    
      constructor(Label: string,) {
        super(Label);


        
      //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
      this.addInput("exec1", new ClassicPreset.Input(socket, "数源1", true));
      this.addInput("exec2", new ClassicPreset.Input(socket, "数源2", true));
      this.addInput("exec3", new ClassicPreset.Input(socket, "数源3", true));
      this.addInput("exec4", new ClassicPreset.Input(socket, "增强模型", true));
      this.addOutput("date", new ClassicPreset.Output(socket, "自定义"));
      this.addOutput("title", new ClassicPreset.Output(socket, "基础"));

  
      // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));
  
        
      //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    
  

    



  export class Node_Template extends ClassicPreset.Node<
  {  exec1: ClassicPreset.Socket,
     exec2: ClassicPreset.Socket, 
     exec3: ClassicPreset.Socket,
     exec4: ClassicPreset.Socket,
    },
  { date: ClassicPreset.Socket, 
    title: ClassicPreset.Socket,
    content: ClassicPreset.Socket,
    author: ClassicPreset.Socket,
    image: ClassicPreset.Socket,
    sound: ClassicPreset.Socket,
    video: ClassicPreset.Socket,
  },
  { 
    Label: ClassicPreset.InputControl<"text"> }> 

  {
      width = 180;
      height = 290;
    
      constructor(Label: string,) {
        super(Label);


        
      //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
      this.addInput("exec1", new ClassicPreset.Input(socket, "模型1", true));
      this.addInput("exec2", new ClassicPreset.Input(socket, "模型2", true));
      this.addInput("exec3", new ClassicPreset.Input(socket, "模型3", true));
      this.addInput("exec4", new ClassicPreset.Input(socket, "自定义模型", true));
      this.addOutput("date", new ClassicPreset.Output(socket, "思路"));
      this.addOutput("title", new ClassicPreset.Output(socket, "输出"));

  
      // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));
  
        
      //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
 
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }





      
      export class SelectControl<T extends string | number = string | number> 
        extends ClassicPreset.Control {
        constructor(
          public options: SelectOption<T>[],
          public selected: T,
          public onChange: (value: T) => void
        ) {
          super();
        }
      }
      
      
      



      export class Node_Echart extends ClassicPreset.Node<
      
        { [key in string]: ClassicPreset.Socket },
        { [key in string]: ClassicPreset.Socket },
        {
          [key in string]:
            | ButtonControl
            | ProgressControl
            | SwitchControl
            | SelectControl
            | ClassicPreset.Control
            | ClassicPreset.InputControl<"number">
            | ClassicPreset.InputControl<"text">;
        }
      > {
      width = 180;
      height =130;
    
      constructor(Label: string,) {
        super(Label);


        
      //   this.addOutput("exec", new ClassicPreset.Output(socket, "Exec1"));
    //   this.addInput("exec1", new ClassicPreset.Input(socket, "模型1", true));
    //   this.addInput("exec2", new ClassicPreset.Input(socket, "模型2", true));
    //   this.addInput("exec3", new ClassicPreset.Input(socket, "模型3", true));
    //   this.addInput("exec4", new ClassicPreset.Input(socket, "自定义模型", true));
    //   this.addOutput("date", new ClassicPreset.Output(socket, "思路"));
    //   this.addOutput("title", new ClassicPreset.Output(socket, "输出"));

  
      // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));
  
        
      //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
      }
      
      data() {
        return {}; // 根据实际情况返回数据结构
      }
 
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }


    // { content: TextAreaControl }
    

    // -------------------------------------------------------------------------------------- Preview

    


    export class Node_Preview_Rumor1 extends ClassicPreset.Node<
    
    {
  
      Input: ClassicPreset.Socket;},
    
    
      { Output: ClassicPreset.Socket; }, 
    
    { 
      Label: ClassicPreset.InputControl<"text">,
      content:Control_Preview_Score,
    }
    > {
      width = 580;
      height = 428;
    
      constructor(Label: string,) {
        super(Label);
  
  
  
  
  
        const left = new ClassicPreset.Input(socket, "");
        this.addInput("Input", left);
  

  
        this.addOutput("Output", new ClassicPreset.Output(socket, "输出"));
  
  
        const ControlPreviewScore = new Control_Preview_Score(
          '【UUID】:未知', // Label for the text area
          '【规则】:未知', // Initial value
          '【方向】:未知', // Initial value
          '【课题】:未知', // Initial value
          '【图片】:未知', // Initial value
          '【中标】:未知', // Initial value
          '【关键词】:未知', // Initial value

          (UUID_Source: any) => {
            console.log('TextArea value changed:', UUID_Source);
          }
        );
        this.addControl('content', ControlPreviewScore);
  
      }
  
      updateLabel(newLabel: string) {
        this.label = newLabel; // 更新节点的名称
        
      }
  
      updateContent(Intelligence:Record<string, any>){
        const contentControl = this.controls.content;
  
        contentControl.setContent(Intelligence)
        console.log('Intelligence:', Intelligence);
        
  
  
      }
    
      execute(_: never, forward: (output: "left") => void) {
        forward("left");
      
      }
    
      data() {
        return {};
      }
    }
    
  