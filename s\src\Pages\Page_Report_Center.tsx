import React, { useState, useEffect } from 'react';
import { Card, Table, Select, Button, Space, Tag, message, Spin, Modal } from 'antd';
import { DownloadOutlined, DeleteOutlined, FileTextOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import '../Styles/Page_Report_Center.css';
import { useServiceRequests } from '@/Core/Core_Control';

const { Option } = Select;
const { confirm } = Modal;

interface ReportRecord {
  id: string;
  reportTime: string;
  reportUuid: string;
  reportName: string;
  reportStatus: 'Active' | 'Finish' | 'Download';
  reportFileType?: string;
  reportSaveName?: string;
}

const Page_Report_Center: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportRecord[]>([]);

  // 筛选条件
  const [dateFilter, setDateFilter] = useState('HalfYear');

  // 引入API请求钩子
  const { AsyncTokenRequests } = useServiceRequests();

  // 日期筛选选项
  const dateOptions = [
    { value: 'Day', label: '今日' },
    { value: 'TwoDay', label: '近两日' },
    { value: 'Mouth', label: '本月' },
    { value: 'HalfYear', label: '近半年' },
    { value: 'Year', label: '近一年' },
    { value: 'All', label: '所有' }
  ];

  // 获取报告数据
  const fetchReportData = async () => {
    setLoading(true);
    try {
      const requestData = {
        user_id: '', // useServiceRequests会自动填充
        user_token: '', // useServiceRequests会自动填充
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_report_info',
        data_argument: '{}',
        data_kwargs: {
          Choice_Date_Content: dateFilter,
          Choice_Date_List: [],
        },
      };
      const result = await AsyncTokenRequests(requestData);
      if (result && result.Status === 'Success') {
        // 兼容老接口字段
        const reportList = (result.Report_List || []).map((item: any, idx: number) => ({
          id: item.ID || String(idx + 1),
          reportTime: item.REPORT_TIME,
          reportUuid: item.REPORT_UUID,
          reportName: item.REPORT_NAME,
          reportStatus: item.REPORT_STATUS,
          reportFileType: item.REPORT_FILE_TYPE,
          reportSaveName: item.REPORT_SAVE_NAME,
        }));
        setReportData(reportList);
      } else {
        message.warning(result?.Msg || '网络出错！');
      }
    } catch (err) {
      message.warning('网络出错！');
    } finally {
      setLoading(false);
    }
  };

  // 筛选数据
  const handleFilter = () => {
    fetchReportData();
  };

  // 下载报告
  const handleDownload = (report: ReportRecord) => {
    console.log('下载报告:', report.reportUuid);

    // 创建表单进行下载
    const form = document.createElement('form');
    form.action = `https://${window.location.host}/CSC_file_download`;
    form.method = 'POST';

    // 添加参数
    const params = [
      { name: 'file_type', value: report.reportFileType || 'pdf' },
      { name: 'file_time', value: report.reportTime },
      { name: 'save_name', value: report.reportSaveName || report.reportName }
    ];

    params.forEach(param => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = param.name;
      input.value = param.value;
      form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    // 更新状态为已下载
    setReportData(prev =>
      prev.map(item =>
        item.reportUuid === report.reportUuid
          ? { ...item, reportStatus: 'Download' as const }
          : item
      )
    );

    message.success('报告下载已开始');
  };

  // 删除报告
  const handleDelete = async (reportUuid: string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个报告吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 模拟删除API调用
          await new Promise(resolve => setTimeout(resolve, 500));

          setReportData(prev => prev.filter(item => item.reportUuid !== reportUuid));
          message.success('报告删除成功');
          console.log('删除报告成功:', reportUuid);
        } catch (error) {
          console.error('删除报告失败:', error);
          message.error('删除报告失败');
        }
      }
    });
  };

  // 表格列定义
  const columns: ColumnsType<ReportRecord> = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 160,
      align: 'center',
    },
    {
      title: '报告标识',
      dataIndex: 'reportUuid',
      key: 'reportUuid',
      width: 150,
      align: 'center',
    },
    {
      title: '报告名称',
      dataIndex: 'reportName',
      key: 'reportName',
      ellipsis: true,
      render: (text: string) => (
        <span title={text}>
          <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          {text}
        </span>
      ),
    },
    {
      title: '处置状态',
      dataIndex: 'reportStatus',
      key: 'reportStatus',
      width: 120,
      align: 'center',
      render: (status: string) => {
        const statusConfig = {
          Active: { color: 'processing', text: '执行中' },
          Finish: { color: 'success', text: '已完成' },
          Download: { color: 'default', text: '已下载' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          {(record.reportStatus === 'Finish' || record.reportStatus === 'Download') && (
            <Button
              type="primary"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            >
              下载
            </Button>
          )}
          {record.reportStatus === 'Active' && (
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.reportUuid)}
            >
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchReportData();
  }, []);

  return (
    <div className="report-center-page">
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <span>日期筛选:</span>
          <Select
            value={dateFilter}
            onChange={setDateFilter}
            style={{ width: 120 }}
          >
            {dateOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Button type="primary" onClick={handleFilter}>
            筛选
          </Button>
        </Space>
      </Card>

      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={reportData}
            rowKey="reportUuid"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 800 }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default Page_Report_Center;
