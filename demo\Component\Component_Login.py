# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import (Qt, QTimer, QPropertyAnimation, QRect, QEasingCurve, Signal)
from PySide6.QtGui import (QColor, QPainter, QFont, QPen, QIcon)
from PySide6.QtWidgets import (QMainWindow, QGraphicsDropShadowEffect, QWidget, QApplication)
from PySide6.QtGui import QColor
from PySide6.QtWidgets import (QGraphicsDropShadowEffect, QWidget)
from PySide6.QtCore import (QSize, QRect, QMetaObject)
from PySide6.QtWidgets import (QWidget, QVB<PERSON>Layout, Q<PERSON>rame, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QSizePolicy)
# from PyQt5.QtGui import QScreen
from PySide6.QtWebEngineWidgets import QWebEngineView
import qtawesome as qta
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.Toolkit import Service_Print
PP    = Service_Print.Service_Print()
PW    = Service_Print.Service_Print('Warning')
PJ    = Service_Print.Service_Print('Json')
PPP   = Service_Print.Service_Print('Json_Time')
from Bin.Resource.CSS import UI_Icons
def apply_shadow_effect(widget, blur_radius=10, x_offset=0, y_offset=0, color=(0, 0, 0, 150)):
    """为指定的小部件应用阴影效果。

    Args:
        widget(QWidget): 要应用阴影效果的小部件.
        blur_radius(int): 阴影的模糊半径.
        x_offset(int): 阴影的X轴偏移量.
        y_offset(int): 阴影的Y轴偏移量.
        color: 表示阴影RGBA颜色的元组.
    """
    shadow = QGraphicsDropShadowEffect(widget)
    shadow.setBlurRadius(blur_radius)
    shadow.setXOffset(x_offset)
    shadow.setYOffset(y_offset)
    shadow.setColor(QColor(*color))
    widget.setGraphicsEffect(shadow)

# ---------------------------------------------------------------------------------------- 获取系统初始化数据
class Component_Login(QtWidgets.QMainWindow):
    Signal_Result = QtCore.Signal(dict)

    def __init__(self):
        super().__init__()

        self.setWindowFlags(QtCore.Qt.FramelessWindowHint)  # 隐藏边框
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)  # 设置窗口背景透明
        # self.setWindowIcon(QtGui.QIcon("./Resource/Image/System/logo.ico"))
        # self.setWindowIcon(QtGui.QIcon("%s/Resource/Image/System/logo.ico"%Real_Path))
        # self.setWindowTitle("哨兵GIS地图处置")
        # self.setAcceptDrops(True)
        # screen = QDesktopWidget().screenGeometry()

        # screens = QGuiApplication.screens()
        # PW((screens))
        self.resize(400,700)
        # self.setFixedSize(self.width(), self.height())
        # self.OS_UI_CSS()

        self.FilePath = ''


        # # Def ---------------- Widget_Central
        # self.Widget_Central = QtWidgets.QWidget(self)
        # self.setW

        self.HLayout = QtWidgets.QHBoxLayout(self)
        self.HLayout.setContentsMargins(0, 0, 0, 0)
        self.HLayout.setSpacing(0)

        # self.QLabel_Menu = QtWidgets.QLabel()
        # self.QLabel_Menu.setStyleSheet('background:#002040;border:1px solid #002040;')

        self.QLabel_Content  = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet('background:#002040;border:1px solid #002040;')
        # self.QLabel_Content.setStyleSheet('background:transparent;border:0px solid #002040;')


        # self.QLabel_Info  = QtWidgets.QLabel()
        # self.QLabel_Info.setStyleSheet('background:#002040;border:1px solid #002040;')


        # self.HLayout.addWidget(self.QLabel_Menu,1)
        self.HLayout.addWidget(self.QLabel_Content, 1)
        # self.HLayout.addWidget(self.QLabel_Info, 2)

        self.Set_Content()
    def Set_Content(self):


        self.ui = Ui_Login()
        self.ui.setupUi(self)
        HLayout = QtWidgets.QHBoxLayout(self)
        HLayout.setContentsMargins(0, 0, 0, 0)
        HLayout.setSpacing(0)
        # IMPORT CIRCULAR PROGRESS
        self.progress = CircularProgress()
        self.progress.width = 140
        self.progress.height = 140
        self.progress.value = 0
        self.progress.setFixedSize(self.progress.width, self.progress.height)
        self.progress.font_size = 20
        self.progress.add_shadow()
        self.progress.progress_width = 4
        self.progress.progress_color = QColor("#bdff00")
        self.progress.text_color = QColor("#E6E6E6")
        self.progress.bg_color = QColor("#222222")
        self.progress.setParent(self.ui.preloader)
        self.progress.show()

        # ADD DROP SHADOW
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(15)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 80))
        self.ui.bg.setGraphicsEffect(self.shadow)

        # QTIMER
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)
        self.timer.start(10)

        self.counter = int()
    def update(self):
        # global counter

        # SET VALUE TO PROGRESS BAR
        self.progress.set_value(self.counter)

        # CLOSE SPLASH SCREEN AND OPEN MAIN APP
        if self.counter >= 100:
            # STOP TIMER



            self.timer.stop()



            self.animation_login()
        # INCREASE COUNTER
        self.counter += 1

        # START ANIMATION TO LOGIN

    def animation_login(self):
        # ANIMATION
        self.animation = QPropertyAnimation(self.ui.frame_widgets, b"geometry")
        self.animation.setDuration(3000)
        self.animation.setStartValue(QRect(0, 70, self.ui.frame_widgets.width(), self.ui.frame_widgets.height()))
        self.animation.setEndValue(QRect(0, -380, self.ui.frame_widgets.width(), self.ui.frame_widgets.height()))
        self.animation.setEasingCurve(QEasingCurve.InOutQuart)
        self.animation.start()
        # 登录成功，发射信号
        # self.animation.finished.connect(lambda: QTimer.singleShot(800, self.Signal_Command.emit))
        self.animation.finished.connect(self.PAGE_HANDLER_EXECUTE)

    def PAGE_HANDLER_EXECUTE(self):
        # PP(COMMAND
        self.Signal_Result.emit("{}")  # 发送信号并传递数据

        # 抽屉内容部件

        # # 初始化动画
        # self.animation_1 = QPropertyAnimation(self.drawer_content, b"pos")
        # self.animation_1.setDuration(500)  # 动画持续时间
        # self.animation_1.setEasingCurve(QEasingCurve.InOutQuad)  # 缓动函数
        # # self.drawer_open = False
        # self.animation_1.setStartValue(QtCore.QPoint(0, self.height()))
        # self.animation_1.setEndValue(QtCore.QPoint(0, 100))
        #
        # self.animation_1.start()







class CircularProgress(QWidget):
    shadow: QGraphicsDropShadowEffect

    def __init__(self):
        QWidget.__init__(self)

        # CUSTOM PROPERTIES
        self.value = 0
        self.width = 100
        self.height = 100
        self.progress_width = 10
        self.progress_rounded_cap = True
        self.max_value = 100
        self.progress_color = 0xff79c6
        # Text
        self.enable_text = True
        self.font_family = "Segoe UI"
        self.font_size = 12
        self.suffix = "%"
        self.text_color = 0xff79c6
        # BG
        self.enable_bg = True
        self.bg_color = 0x44475a

        # SET DEFAULT SIZE WITHOUT LAYOUT
        self.resize(self.width, self.height)

    # ADD DROP_SHADOW
    def add_shadow(self):
        apply_shadow_effect(self, 15, 0, 0, (0, 0, 0, 80))

    # SET VALUE
    def set_value(self, value):
        self.value = value
        self.repaint()  # Render progress bar after change value

    # PAINT EVENT (DESIGN YOUR CIRCULAR PROGRESS HERE)
    def paintEvent(self, e):
        # SET PROGRESS PARAMETERS
        width = self.width - self.progress_width
        height = self.height - self.progress_width
        margin = self.progress_width / 2
        value = self.value * 360 / self.max_value

        # PAINTER
        paint = QPainter()
        paint.begin(self)
        paint.setRenderHint(QPainter.Antialiasing)  # remove pixelated edges
        paint.setFont(QFont(self.font_family, self.font_size))

        # CREATE RECTANGLE
        rect = QRect(0, 0, self.width, self.height)
        paint.setPen(Qt.NoPen)
        paint.drawRect(rect)

        # PEN
        pen = QPen()
        pen.setWidth(self.progress_width)
        # Set Round Cap
        if self.progress_rounded_cap:
            pen.setCapStyle(Qt.RoundCap)

        # ENABLE BG
        if self.enable_bg:
            pen.setColor(QColor(self.bg_color))
            paint.setPen(pen)
            paint.drawArc(margin, margin, width, height, 0, 360 * 16)

        # CREATE ARC / CIRCULAR PROGRESS
        pen.setColor(QColor(self.progress_color))
        paint.setPen(pen)
        paint.drawArc(margin, margin, width, height, -90 * 16, -value * 16)

        # CREATE TEXT
        if self.enable_text:
            pen.setColor(QColor(self.text_color))
            paint.setPen(pen)
            paint.drawText(rect, Qt.AlignCenter, f"{self.value}{self.suffix}")

        # END
        paint.end()

class Ui_Login(object):
    def setupUi(self, Login):
        if not Login.objectName():
            Login.setObjectName(u"Login")
        Login.resize(200, 320)
        Login.setStyleSheet('background:transparent;border:0px solid #002040;')
        # Login.setStyleSheet('background:rgba(0,0,0,1);border:0px solid #002040;')
        Login.setMinimumSize(QSize(200, 320))
        Login.setMaximumSize(QSize(200, 320))
        # Login.setStyleSheet(u"#bg {\n"
        #                     "	background-color: rgb(0, 0, 0);\n"
        #                     "	border-radius: 10px;\n"
        #                     "}\n"
        #                     "QLabel {\n"
        #                     "	color:  rgb(121, 121, 121);\n"
        #                     "	padding-left: 10px;\n"
        #                     "	padding-top: 20px;\n"
        #                     "}\n"
        #                     ".QLineEdit {\n"
        #                     "	border: 3px solid rgb(47, 48, 50);\n"
        #                     "	border-radius: 15px;\n"
        #                     "	background-color: rgb(47, 48, 50);\n"
        #                     "	color: rgb(121, 121, 121);\n"
        #                     "	padding-left: 10px;\n"
        #                     "	padding-right: 10px;\n"
        #                     "	background-repeat: none;\n"
        #                     "	background-position: left center;\n"
        #                     "}\n"
        #                     ".QLineEdit:hover {\n"
        #                     "	color: rgb(230, 230, 230);\n"
        #                     "	border: 3px solid rgb(62, 63, 66);\n"
        #                     "}\n"
        #                     ".QLineEdit:focus {\n"
        #                     "	color: rgb(230, 230, 230);\n"
        #                     "	border: 3px solid rgb(189, 255, 0);\n"
        #                     "	background-color: rgb(14, 14, 15);\n"
        #                     "}")
        self.centralwidget = QWidget(Login)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(10, 10, 10, 10)
        self.bg = QFrame(self.centralwidget)
        self.bg.setObjectName(u"bg")
        self.bg.setFrameShape(QFrame.NoFrame)
        self.bg.setFrameShadow(QFrame.Raised)
        self.frame_widgets = QFrame(self.bg)
        self.frame_widgets.setObjectName(u"frame_widgets")
        self.frame_widgets.setGeometry(QRect(0, 70, 180, 620))
        self.frame_widgets.setMinimumSize(QSize(180, 620))
        self.frame_widgets.setFrameShape(QFrame.NoFrame)
        self.frame_widgets.setFrameShadow(QFrame.Raised)
        self.verticalLayout_2 = QVBoxLayout(self.frame_widgets)
        self.verticalLayout_2.setSpacing(5)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(20, 10, 20, 10)
        self.preloader = QFrame(self.frame_widgets)
        self.preloader.setObjectName(u"preloader")
        self.preloader.setMinimumSize(QSize(140, 140))
        self.preloader.setMaximumSize(QSize(160, 160))
        self.preloader.setFrameShape(QFrame.NoFrame)
        self.preloader.setFrameShadow(QFrame.Raised)

        self.verticalLayout_2.addWidget(self.preloader)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_2.addItem(self.verticalSpacer)

        self.logo = QFrame(self.frame_widgets)
        self.logo.setObjectName(u"logo")

        self.logo.setMinimumSize(QSize(0, 160))
        self.logo.setStyleSheet(u"#logo {\n"
                                "	border-radius: 8px;\n"
                                "	background-image: url(.png);\n"
                                "	width:100px;\n"
                                "	height:100px;\n"
                                "	background-position: center;\n"
                                "	background-repeat: no-repeat;\n"
                                "}")
        self.logo.setFrameShape(QFrame.NoFrame)
        self.logo.setFrameShadow(QFrame.Raised)
        Pixmap_Logo = QtGui.QPixmap(u":/rs/System/Logo.png")
        # Pixmap_Logo = QtGui.QPixmap(u":/rs/System/Shield.png")
        # Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\bin\System\OS\Resource\Image\System\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(380, 380)
        self.QLabel_Login = QtWidgets.QLabel(self.logo)
        self.QLabel_Login.setGeometry(QRect(5, 10, 120, 120))
        self.QLabel_Login.setPixmap(Image_Logo)
        self.QLabel_Login.setScaledContents(True)
        #
        # self.QLabel_Title = QtWidgets.QLabel(self.logo)
        # self.QLabel_Title.setFont(QtGui.QFont("Microsoft YaHei", 12))
        # self.QLabel_Title.setStyleSheet("""QLabel{color:#ffffff}""")
        # self.QLabel_Title.setText("加载完成")
        # self.QLabel_Title.setAlignment(QtCore.Qt.AlignCenter)
        # self.QLabel_Title.setGeometry(QRect(22, 50, 180, 180))






        self.verticalLayout_2.addWidget(self.logo)


        # self.drawer_content =  QFrame(self.frame_widgets)
        # self.drawer_content.setGeometry(10, 10, 200, 200)
        # self.drawer_content.setStyleSheet("QFrame { background: red; }")
        # # self.drawer_content.show()
        #
        # self.verticalLayout_2.addWidget(self.drawer_content)




        # self.user_description = QLabel(self.frame_widgets)
        # self.user_description.setObjectName(u"user_description")
        # self.user_description.setStyleSheet(u"background: transparent;")
        #
        # self.verticalLayout_2.addWidget(self.user_description)

        # self.username = QLineEdit(self.frame_widgets)
        # self.username.setObjectName(u"username")
        # self.username.setMinimumSize(QSize(0, 30))
        # self.username.setMaximumSize(QSize(16777215, 40))
        #
        # self.verticalLayout_2.addWidget(self.username)
        #
        # self.password = QLineEdit(self.frame_widgets)
        # self.password.setObjectName(u"password")
        # self.password.setMinimumSize(QSize(0, 30))
        # self.password.setMaximumSize(QSize(16777215, 40))
        # self.password.setEchoMode(QLineEdit.Password)
        #
        # self.verticalLayout_2.addWidget(self.password)

        self.verticalLayout.addWidget(self.bg)

        Login.setCentralWidget(self.centralwidget)

        self.retranslateUi(Login)

        QMetaObject.connectSlotsByName(Login)

    # setupUi

    def retranslateUi(self, Login):

        ...
        # Login.setWindowTitle(QCoreApplication.translate("Login", u"Login. PyBlackBOX", None))
        # self.user_description.setText(QCoreApplication.translate("Login", u"Login (pass: 123456):", None))
        # self.username.setPlaceholderText(QCoreApplication.translate("Login", u"Username", None))
        # self.password.setPlaceholderText(QCoreApplication.translate("Login", u"Password", None))
    # retranslateUi

if __name__ == '__main__':

    app = QApplication(sys.argv)
    Page_Widget = Component_Login()
    Page_Widget.show()
    sys.exit(app.exec())
