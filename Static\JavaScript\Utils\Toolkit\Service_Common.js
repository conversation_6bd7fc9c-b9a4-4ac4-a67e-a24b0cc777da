
/****************************************侧边栏顶部栏***************************************************/
Element_Sidebar_Header = function(){
    Page  = window.location.href.split("Page=")[1];
    var User_Token_Info = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
    console.log('User_Token_Info',User_Token_Info);
    let Requests_Data = {
        "user_id": "CSC",
        "user_token":User_Token_Info,
        "data_class": "Sentinel",
        "data_type": 'Service_Html',
        "data_methods": "return_html_config_custom",
        "data_argument": `{}`,
        "data_kwargs":`{"Page":"${Page}"}`,
    };
    __Service_Requests = new Service_Requests("Sync",Requests_Data);
    Result  = __Service_Requests.callMethod();
    // console.log("Result2:",Result);
    if ('Code' in Result) {
        localStorage.setItem('Login_Out', 'Cookie Expire');
        window.location.href="Service_Page?Page=Page_Login";
    } else {
        localStorage.setItem('Login_Out', '')
        $("#Element_Header").html(Result.Element_Header);
        $("#Element_Sidebar").html(Result.Element_Sidebar);
        let __params = getQueryParams()
        localStorage.setItem('cache_route', `${location.pathname}?Page=${__params.Page}`)
    };
    // __Service_Requests = new Service_Requests("Async",Requests_Data);
    // __Service_Requests.callMethod()
    //     .then((Result) => {
    //         console.log("Result2:",Result);
    //         $("#Element_Header").html(Result.Element_Header);
    //         $("#Element_Sidebar").html(Result.Element_Sidebar);
    //     }).catch((err) => { 
    //         console.log("侧边栏请求，网络出错:",err);
    //     });
};

function getQueryParams() {
    let params = {};
    let queryString = window.location.search.slice(1);
    let pairs = queryString.split("&");

    pairs.forEach(function (pair) {
        let keyValue = pair.split("=");
        let key = decodeURIComponent(keyValue[0]);
        let value = decodeURIComponent(keyValue[1]);
        params[key] = value;
    });

    return params;
}

/****************************************Loading 注册调用***************************************************/
Loading_Show = function (loadText) {
    if(!loadText){
        $("#loadText").html(loadText);
    };
    $('#loadingModal').modal({backdrop: 'static', keyboard: false});
};

Loading_Hide = function () {
    setTimeout("$('#loadingModal').modal('hide')",500);
};