/* 服务日志页面样式 */
.service-log-page {
  padding: 24px;
  min-height: 100vh;
}

/* 查询条件卡片 */
.service-log-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.service-log-page h6 {
  font-size: 16px;
  font-weight: 600;

  margin: 0;
}

/* 表格容器 */
.table-responsive {
  overflow-x: auto;
}

.table-responsive .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.table-responsive .ant-table-thead > tr > th {

  font-weight: 600;

  text-align: center;
}

.table-responsive .ant-table-tbody > tr > td {
  text-align: center;
  padding: 12px 16px;
}

.table-responsive .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.ant-btn-default {
  border-color: #d9d9d9;
}

.ant-btn-default:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 选择器样式 */
.ant-select {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 空间组件样式 */
.ant-space {
  width: 100%;
}

.ant-space-item {
  display: flex;
  align-items: center;
}

/* 数字样式 */
.service-log-page .ant-typography {
  margin-bottom: 0;
}

/* 消费金额样式 */
.consume-amount {
  color: #ff4d4f;
  font-weight: 500;
}

/* 结余金额样式 */
.balance-amount {
  color: #52c41a;
  font-weight: 500;
}

/* 服务内容样式 */
.service-content {
  max-width: 200px;
  word-break: break-all;
  white-space: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-log-page {
    padding: 16px;
  }
  
  .service-log-page h6 {
    font-size: 14px;
  }
  
  .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .table-responsive .ant-table {
    font-size: 12px;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .service-content {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .service-log-page {
    padding: 12px;
  }
  
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }
  
  .table-responsive .ant-table-thead > tr > th,
  .table-responsive .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
  
  .ant-select {
    font-size: 12px;
  }
  
  .service-content {
    max-width: 100px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

.ant-empty-description {
  color: #999;
}

/* 成功状态样式 */
.ant-message-success .anticon {
  color: #52c41a;
}

/* 错误状态样式 */
.ant-message-error .anticon {
  color: #ff4d4f;
}

/* 警告状态样式 */
.ant-message-warning .anticon {
  color: #faad14;
}

/* 信息状态样式 */
.ant-message-info .anticon {
  color: #1890ff;
}

/* 表格滚动条样式 */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* 查询条件标签样式 */
.service-log-page .ant-space > .ant-space-item > span {
  font-weight: 500;
  color: #595959;
  white-space: nowrap;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 日志类型标签特殊样式 */
.ant-tag-blue {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.ant-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-orange {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag-purple {
  background: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

.ant-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

/* 金额数字样式 */
.amount-text {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* 服务内容标签样式 */
.service-tag {
  margin: 2px;
  border-radius: 4px;
  font-size: 12px;
}
