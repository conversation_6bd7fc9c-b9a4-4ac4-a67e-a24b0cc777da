import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ButtonControl_Start,Node_Socket} from "./Node_Controls";














  export class Node_System_Component_Start extends ClassicPreset.Node<
  {   
    left_1: ClassicPreset.Socket,
    left_2: ClassicPreset.Socket, 
    left_3: ClassicPreset.Socket,
    left_4: ClassicPreset.Socket,
    left_5: ClassicPreset.Socket,
    left_6: ClassicPreset.Socket,
    left_7: ClassicPreset.Socket,
    left_8: ClassicPreset.Socket,
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    Content:ButtonControl_Start,

  }> 
  {
      width = 180;
      height = 128;
    
      constructor(Label: string,) {
        super(Label);


      this.addOutput("Start", new ClassicPreset.Output(Node_Socket, ""));



      
      // this.addControl("Content", new ButtonControl_Start("启动", () => {}));

      this.addControl("Content", new ButtonControl_Start("启动"));

      
    //   this.addControl("button_2", new ButtonControl("停止", () => {}));

 // 正确绑定点击事件
        // const buttonControl = this.controls.Content as ButtonControl_Start;
        // buttonControl.onClick = () => {
        // console.log("按钮点击");
        // };
        
            // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));
  
      // 如果需要在类外部操作，可以在类中添加公共方法
 
      //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
      }
                // 提供公共方法供外部修改回调
            public setButtonAction(callback: () => void) {
                const btn = this.controls.Content as ButtonControl_Start;
                btn.onClick = callback;
            }
          

        //     // 提供公共方法供外部修改回调
        // public setButtonAction(callback: () => void) {
        //     const btn = this.controls.Content as ButtonControl_Start;
        //     btn.onClick = callback;
        // }
// 自定义按钮位置
  // 自定义按钮位置

      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    