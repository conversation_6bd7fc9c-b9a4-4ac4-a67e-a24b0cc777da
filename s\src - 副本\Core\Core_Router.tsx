import React from 'react';
import {  Navigate  } from 'react-router-dom'
import cloneDeep from 'lodash.clonedeep';
import MyLayout from './Core_Layout'
import Page_Home from '../Pages/Page_Home'
import Page_Login from '../Pages/Page_Login'
import Page_Manage_User from '../Pages/Page_Manage_User'
import Page_Manage_Keyword from '../Pages/Page_Manage_Keyword'
import Page_Cryto from '../Pages/Page_Cryto'
import Page404 from '../Pages/Page_404';
// 新增页面导入
import Page_Information_Monitor from '../Pages/Page_Information_Monitor'
import Page_Search_Display from '../Pages/Page_Search_Display'
import Page_Local_Monitor from '../Pages/Page_Local_Monitor'
import Page_Information_Insight from '../Pages/Page_Information_Insight'

import Page_Alarm_Center from '../Pages/Page_Alarm_Center'
import Page_Report_Center from '../Pages/Page_Report_Center'
import Page_Data_Overview from '../Pages/Page_Data_Overview'
import Page_Platform_Config from '../Pages/Page_Platform_Config'
import Page_Counter_Service from '../Pages/Page_Counter_Service'
import Page_Purchased_Service from '../Pages/Page_Purchased_Service'
import Page_Service_Log from '../Pages/Page_Service_Log'
import Page_Recharge_Record from '../Pages/Page_Recharge_Record'
import Page_Other_Service from '../Pages/Page_Other_Service'
import { useAuth } from '@/Core/Core_AuthContent';


/**
 * 路由配置 + 菜单配置 + 权限校验
 * 一级菜单path最好添加'/。。' 绝对路径定位
 * 如果不需要展示在侧边栏的数据  直接用path,element这样的结构就行
 * 二级菜单的path不要用'/'这样的绝对定位方式
*/

// 定义原始路由配置
const rawRoutes = [
    {
      path: '/',
      element: <Navigate to='/Page_Home' replace />,
    },
    {
      path: '/Page_Login',
      element: <Page_Login />,
    },
    {
      path: '/Page_Home',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Home />,
          menu: {
            label: '首页',
            icon: 'HomeOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Information_Monitor',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Information_Monitor />,
          menu: {
            label: '信息监测',
            icon: 'MonitorOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Search_Display',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Search_Display />,
          menu: {
            label: '搜索展示',
            icon: 'SearchOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Local_Monitor',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Local_Monitor />,
          menu: {
            label: '本地监控',
            icon: 'DesktopOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Information_Insight',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Information_Insight />,
          menu: {
            label: '信息洞察',
            icon: 'EyeOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Alarm_Center',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Alarm_Center />,
          menu: {
            label: '预警中心',
            icon: 'AlertOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Report_Center',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Report_Center />,
          menu: {
            label: '报告中心',
            icon: 'FileTextOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Data_Overview',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Data_Overview />,
          menu: {
            label: '数据总览',
            icon: 'DashboardOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Service_Support',
      Component: MyLayout,
      menu: {
        label: '服务支持',
        icon: 'CustomerServiceOutlined'
      },
      children: [
        {
          path: 'Counter',
          element: <Page_Counter_Service />,
          menu: {
            label: '反制服务',
            icon: 'SafetyOutlined'
          }
        },
        {
          path: 'Purchased',
          element: <Page_Purchased_Service />,
          menu: {
            label: '已购服务',
            icon: 'ShoppingCartOutlined'
          }
        },
        {
          path: 'Other',
          element: <Page_Other_Service />,
          menu: {
            label: '其它服务查询',
            icon: 'SearchOutlined'
          }
        },
        {
          path: 'Log',
          element: <Page_Service_Log />,
          menu: {
            label: '服务日志',
            icon: 'FileTextOutlined'
          }
        },
        {
          path: 'Recharge',
          element: <Page_Recharge_Record />,
          menu: {
            label: '充值记录',
            icon: 'CreditCardOutlined'
          }
        }
      ]
    },
    {
      path: '/Page_Platform_Config',
      Component: MyLayout,
      children: [
        {
          index: true,
          element: <Page_Platform_Config />,
          menu: {
            label: '平台配置',
            icon: 'SettingOutlined'
          },
          // permission: ['Admin', 'Manage'], // 临时注释掉权限限制
        }
      ]
    },

    // {
    //   path: '/Page_Cryto',
    //   Component: MyLayout,
    //   children: [
    //     {
    //       index: true,
    //       element: <Page_Cryto />,
    //       menu: {
    //         label: '加解密',
    //         icon: 'ExperimentOutlined'
    //       },
    //       permission: ['Admin', 'Manage'],
    //     }
    //   ]
    // },
    // {
    //   path: '/Page_Manage',
    //   Component: MyLayout,
    //   menu: {
    //     label: '系统设置',
    //     icon: 'ToolOutlined'
    //   },
    //   permission: ['Admin', 'Editor', 'Manage'],
    //   children: [
    //     {
    //       path: 'User',
    //       element: <Page_Manage_User />,
    //       menu: {
    //         label: '用户管理',
    //         icon: 'UserOutlined'
    //       },
    //       permission: ['Admin', 'Editor'],
    //     },
    //     {
    //       path: 'Keyword',
    //       element: <Page_Manage_Keyword />,
    //       menu: {
    //         label: '关键词管理',
    //         icon: 'KeyOutlined'
    //       },
    //       // permission: ['Admin', 'Editor'],
    //     }
    //   ]
    // },
    {
        path: '*',
        element: <Page404 />,
    }
];


// 导出克隆后的原始路由供外部使用
export const getRawRoutes = () => cloneDeep(rawRoutes);

type MenuItem = {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: MenuItem[];
};


/**
 * 提取菜单项的函数  权限匹配的路由才加入到左侧菜单栏
 * @param routes 
 * @param userPermissions 
 * @returns 
 */
export const generateMenuItems = (routes: any[], userPermissions: string): MenuItem[] => {
    const filterRouteToMenuItem = (route: any, parentPath: string = ''): MenuItem | null => {
      // console.log('路由权限校验ing:',route)
      // 构建完整路径
      const fullPath = route.path.startsWith('/')
        ? route.path
        : `${parentPath}/${route.path}`;
  
      // 如果 route 自身有 menu，则优先使用
      if (route.menu) {
        // 如果此路由未设置权限 或者权限是个列表 此用户的权限在列表中 则判断通过
        const hasAccess =
          !route.permission ||
          (Array.isArray(route.permission) && route.permission.includes(userPermissions));
  
        if (!hasAccess) return null;
  
        const menuItem: MenuItem = {
          key: fullPath,
          label: route.menu.label,
          icon: route.menu.icon
        };
  
        // 处理子路由
        if (route.children && route.children.length > 0) {
          const childMenus = route.children
            .map((child: any) => filterRouteToMenuItem(child, fullPath))
            .filter(Boolean) as MenuItem[];
  
          if (childMenus.length > 0) {
            menuItem.children = childMenus;
          }
        }
        // console.log('一级menu:',route,menuItem)
        return menuItem;
      }
  
      // 如果 route 没有 menu，但有 children 并且 children[0] 有 menu，也提取出来
      if (route.children && route.children.length > 0 && route.children[0].menu) {
        const child = route.children[0];
        const hasAccess =
          !child.permission ||
          (Array.isArray(child.permission) && child.permission.includes(userPermissions));
  
        if (!hasAccess) return null;
        
        // console.log('一级无menu:',route,{
        //   key: fullPath,
        //   label: child.menu.label,
        //   icon: child.menu.icon
        // })

        return {
          key: fullPath,
          label: child.menu.label,
          icon: child.menu.icon
        };
      }
  
      return null;
    };
  
    return routes.map(route => filterRouteToMenuItem(route)).filter(item => item !== null) as MenuItem[];
};

/**
 * 获取菜单配置的自定义 Hook
 */
export const useMenuList = () => {
    const { user, menuRefreshKey } = useAuth();
    const userPermissions = user?.roles || '';
    const routes = getRawRoutes();
    // console.log('----------------------------useMenuList------------------------------')
    // console.log('权限刷新菜单:',routes, userPermissions)

    return React.useMemo(() => generateMenuItems(routes, userPermissions), [userPermissions, menuRefreshKey]);
};


/**
 * 路由权限验证
*/
function filterRoutesByPermission(routes: any[], userRoles: string) {
    return routes.filter(route => {
      console.log('权限检查 - 路由:', route.path, '需要权限:', route.permission, '用户角色:', userRoles)
      const hasAccess =
        !route.permission ||
        (Array.isArray(route.permission) && route.permission.includes(userRoles));
      console.log('权限检查结果:', hasAccess)
      if (route.children) {
        route.children = filterRoutesByPermission(route.children, userRoles);
      }

      return hasAccess || (route.children && route.children.length > 0);
    });
};

export function useFilteredRoutes() {
    const { user, menuRefreshKey  } = useAuth();
    const userPermissions = user?.roles || '';
  
    return React.useMemo(() => {
      const routes = getRawRoutes();
      // console.log('清洗路由清单：',routes,userPermissions)
      const Routes_Permission_List =  filterRoutesByPermission(routes, userPermissions);
      // console.log('Routes_Permission_List:',Routes_Permission_List)
      return Routes_Permission_List
    }, [userPermissions, menuRefreshKey ]);
};

// export default createBrowserRouter(routes);
