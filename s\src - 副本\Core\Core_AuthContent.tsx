import React, { createContext, useContext, useReducer, useState, useCallback  } from 'react';

// 定义用户状态的类型
interface User {
    username: string;
    roles: string;
    usertoken: string;
    userface: string;
    redirect: string;
}

// 定义 AuthContext 的类型
interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    login: (user: User) => void;
    logout: () => void;
    refreshMenus: () => void;
    menuRefreshKey: number;

}

// 初始化 AuthContext，提供一个默认值
const AuthContext = createContext<AuthContextType | undefined>(undefined);


// 定义初始状态
const initialState: { user: User | null; isAuthenticated: boolean } = {
    user: null,
    isAuthenticated: false
};

// 定义 reducer 函数来处理状态更新
const authReducer = (
    state: { user: User | null; isAuthenticated: boolean },
    action: { type: string; payload?: User }
) => {
    switch (action.type) {
        case 'LOGIN':
            if (!action.payload) {
                throw new Error('Invalid login payload');
            }
            return {
                ...state,
                user: action.payload, // 确保 action.payload 是 User 类型
                isAuthenticated: true
            };
        case 'LOGOUT':
            return {
                ...state,
                user: null, // user 是 null，符合 initialState 的类型
                isAuthenticated: false
            };
        default:
            return state;
    }
};

// 创建 AuthProvider 组件
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [state, dispatch] = useReducer(authReducer, initialState, initializeState);

    // 初始化定义menuRefreshKey， 用于手动控制菜单刷新
    const [menuRefreshKey, setMenuRefreshKey] = useState(0);

    const refreshMenus = useCallback(() => {
        // console.log('refreshMenus调用')
        setMenuRefreshKey(prev => prev + 1);
    }, []);


    // 初始化状态
    function initializeState() {
        const user = localStorage.getItem('user');
        if (user) {
            return {
                user: JSON.parse(user),
                isAuthenticated: true
            };
        }
        return initialState;
    }

    // 登录方法
    const login = (user: User) => {
        dispatch({ type: 'LOGIN', payload: user });
        localStorage.setItem('user', JSON.stringify(user));
    };

    // 登出方法
    const logout = () => {
        dispatch({ type: 'LOGOUT' });
        localStorage.removeItem('user'); 
    };

    // 提供上下文值
    const authContextValue: AuthContextType = {
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        login,
        logout,
        refreshMenus,
        menuRefreshKey,
    };

    return (
        <AuthContext.Provider value={authContextValue}>
            {children}
        </AuthContext.Provider>
    );
};

// 自定义钩子来使用 AuthContext
const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export { AuthProvider, useAuth };