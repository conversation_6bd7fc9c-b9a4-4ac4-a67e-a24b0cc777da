// import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
// import { SystemControl_Object,ButtonControl,Node_Socket,ButtonControl_More} from "./Node_Controls";









// export class Node_System_Component_Object extends ClassicPreset.Node<
//   { [key in string]: ClassicPreset.Socket },
//   { [key in string]: ClassicPreset.Socket },
//   {
//     [key in string]:
//       | ButtonControl_More
//       | SystemControl_Object
//       | ClassicPreset.Control
//   }
// > {

//     private _label: string;
//     width = 388;
//     height = 188;
  
//     constructor(Label: string,) {
//       super(Label);
//       this._label = Label;

//       this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""));
//       this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));
     

//     const textAreaControl = new SystemControl_Object(
//         '【标题】:未知',
//         0, 

//         (title) => {
//           console.log('TextArea value changed:', title);
//         }
//       );
      
//       this.addControl("Conent",  textAreaControl);
//       // this.addControl("More", new ButtonControl_More("详情", () => {}));
   
//     }
//     updateContent(Intelligence:Record<string, any>){
//       const contentControl = this.controls.Conent;

//       contentControl.setContent(Intelligence)
//       console.log('Intelligence:', Intelligence);
      


//     }
   
//     updateLabel(newLabel: string): void {
//         this._label = newLabel;
//         // this.updateUI(); // 假设有一个 updateUI 方法来触发 UI 更新
//       }

// }


import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ButtonControl,Node_Socket,SystemControl_Object} from "./Node_Controls";
















  export class Node_System_Component_Object extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  { Label: ClassicPreset.InputControl<"text"> ,
    button_1:ButtonControl,
    Conent:SystemControl_Object,
    

  }> 
  {
      width  = 388;
      height = 188;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new SystemControl_Object(
        '【标题】:未知',

        0,
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);




        }

      updateContent(Object:Record<string, any>){
        const contentControl = this.controls.Conent;

        contentControl.setContent(Object)
        console.log('Object:', Object);
        


      }
      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    