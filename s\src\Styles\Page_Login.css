
  #tsparticles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #01010f;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    z-index: 9999;
  }

  .login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #ffffff;
    overflow: hidden;
  }
  
  .login-form-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    padding: 40px;
    max-width: 400px;
    width: 100%;
    animation: fadeInUp 1s ease-out forwards;
  }
  
  .login-form-container h2 {
    margin-bottom: 10px;
    font-size: 28px;
    color: #00ffff;
    text-align: center;
  }
  
  .login-form-container p {
    margin-bottom: 20px;
    color: #cccccc;
    text-align: center;
  }
  
  .login-form-wapper input[type="text"],
  .login-form-wapper input[type="password"] {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: none;
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 14px;
    transition: background-color 0.3s ease;
    box-sizing: border-box;
  }
  
  .login-form-wapper input[type="text"]:focus,
  .login-form-wapper input[type="password"]:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .login-form-wapper button[type="submit"] {
    width: 100%;
    padding: 12px;
    background: linear-gradient(to right, #00eaff, #007eff);
    border: none;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .login-form-wapper button[type="submit"]:hover {
    transform: scale(1.03);
    box-shadow: 0 0 15px rgba(0, 127, 255, 0.5);
  }
  
  .login-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    font-size: 13px;
  }
  
  .login-footer a {
    color: #00ffff;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .login-footer a:hover {
    color: #00cfff;
  }

  .password-input-container {
    position: relative;
    width: 100%;
  }
  
  .toggle-password-btn {
    position: absolute;
    right: 10px;
    top: 40%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    font-size: 16px;
    cursor: pointer;
    outline: none;
  }

  .custom-login-button {
    height: 36px;
    font-size: 14px;
    padding: 0 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .custom-login-button.ant-btn-primary:focus-visible {
    outline: none;
    box-shadow: none;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }