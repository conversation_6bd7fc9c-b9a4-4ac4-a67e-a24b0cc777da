import sys,time,os
from PySide6 import QtWidgets, QtGui, QtCore
import qtawesome as qta
from Bin.System.OS.Component import Component_Common
from Bin.Utils.UtilsCenter import *
# -*- coding: utf-8 -*-
import time, os, sys, cv2
from PySide6 import Qt<PERSON><PERSON>, QtGui, QtWidgets
import Component_Common
import qtawesome
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils\Toolkit")
# import Service_Table
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils\Toolkit")
import Service_Print, Service_Table

PP = Service_Print.Service_Print()
PW = Service_Print.Service_Print('Warning')
PI = Service_Print.Service_Print('Info')
PS = Service_Print.Service_Print('Success')
PE = Service_Print.Service_Print('Error')
PC = Service_Print.Service_Print('Core')
PK = Service_Print.Service_Print('Key')
PT = Service_Print.Service_Print('TryError')
PJ = Service_Print.Service_Print('Json')
PL = Service_Print.Service_Print('Logging')
PPP = Service_Print.Service_Print('Json_Time')
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils")
from UtilsCenter import *

class Component_Processing_Radar(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.resize(1280, 720)  # 与EQ处理弹窗相同的尺寸
        self.setAcceptDrops(True)
        self.setStyleSheet("QLabel{background:rgba(25,25,50,1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")

        # 初始化数据
        self.radar_data = []
        self.load_radar_data()

        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("QLabel{background:transparent;border: none;}")
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("QLabel{background:transparent;}")

        __QVBoxLayout.addWidget(self.Set_Title(), 0)
        __QVBoxLayout.addWidget(self.Set_Content(),1)

    def load_radar_data(self):
        """加载雷达设备数据"""
        try:
            Data = {
                "Table_Name": "Radar_Info_List",
                "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Radar\Radar_Info_List.db",
            }
            __Service_Table = Service_Table.Service_Table("sqlite_backall")
            self.radar_data = __Service_Table(Data)
            PP(f"成功从数据库加载 {len(self.radar_data)} 条雷达设备数据")
            # PJ(self.radar_data)  # 打印加载的数据用于调试
        except Exception as e:
            PE(f"从数据库加载雷达设备数据失败: {e}")
            self.radar_data = []

    def Set_Title(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(40)
        __QLabel.setStyleSheet("QLabel{background:transparent;}")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)  # 内边界
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setFixedWidth(80)
        QLabel_Title.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        QLabel_Title.setAlignment(QtCore.Qt.AlignVCenter |QtCore.Qt.AlignLeft)
        QLabel_Title.setText("雷达设备管理")

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='white', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setFixedWidth(58)
        QPushButton_Close.setStyleSheet(
            '''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;font-weight: bold;}''')
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())

        __QHBoxLayout.addWidget(QLabel_Icon,  0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter )
        __QHBoxLayout.addWidget(QLabel_Title, 9, alignment=QtCore.Qt.AlignLeft)
        __QHBoxLayout.addWidget(QPushButton_Close,  1,alignment=QtCore.Qt.AlignRight)

        return __QLabel

    def Set_Content(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("background: rgba(25,25,50,1);")

        # 使用布局确保占据所有空间
        __QVBoxLayout = QtWidgets.QVBoxLayout(__QLabel)
        __QVBoxLayout.setContentsMargins(15, 15, 15, 15)
        __QVBoxLayout.setSpacing(10)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)

        # 添加标题和统计信息
        self.create_header(__QVBoxLayout)

        # 添加雷达设备数据表格
        self.create_radar_table(__QVBoxLayout)

        return __QLabel

    def create_header(self, layout):
        """创建标题和统计信息"""
        # 主标题
        title_label = QtWidgets.QLabel("雷达设备监控信息")
        title_label.setStyleSheet("""
            QLabel {
                color: rgba(22, 175, 252, 255);
                font-size: 18px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title_label)

        # 统计信息
        stats_widget = QtWidgets.QWidget()
        stats_widget.setStyleSheet("background: transparent;")
        stats_layout = QtWidgets.QHBoxLayout(stats_widget)
        stats_layout.setSpacing(30)
        stats_layout.setAlignment(QtCore.Qt.AlignCenter)

        # 统计数据
        total_count = len(self.radar_data)
        active_count = len([r for r in self.radar_data if r.get('RADER_STATUS') == 'Active'])
        location_count = len(set([r.get('RADER_LOCATION', '') for r in self.radar_data if r.get('RADER_LOCATION')]))

        # 计算平均电量
        power_values = []
        for r in self.radar_data:
            power = r.get('RADER_POWER', '0')
            try:
                power_values.append(int(power))
            except:
                power_values.append(0)
        avg_power = sum(power_values) // len(power_values) if power_values else 0

        stats_data = [
            {"label": "设备总数", "value": f"{total_count}台", "color": "#FF6B6B"},
            {"label": "在线设备", "value": f"{active_count}台", "color": "#4ECDC4"},
            {"label": "平均电量", "value": f"{avg_power}%", "color": "#96CEB4"},
            {"label": "部署位置", "value": f"{location_count}处", "color": "#45B7D1"},
        ]

        for stat in stats_data:
            stat_label = QtWidgets.QLabel()
            stat_label.setStyleSheet(f"""
                QLabel {{
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid {stat["color"]};
                    border-radius: 8px;
                    padding: 8px 15px;
                    color: white;
                    font-size: 12px;
                }}
            """)
            stat_label.setText(f'<div style="text-align:center;">'
                              f'<span style="color:{stat["color"]};font-size:16px;font-weight:bold">{stat["value"]}</span><br>'
                              f'<span style="font-size:10px;">{stat["label"]}</span>'
                              f'</div>')
            stat_label.setAlignment(QtCore.Qt.AlignCenter)
            stats_layout.addWidget(stat_label)

        layout.addWidget(stats_widget)

    def create_radar_table(self, layout):
        """创建雷达设备数据表格"""
        # 表格容器
        table_widget = QtWidgets.QWidget()
        table_widget.setStyleSheet("background: transparent;")
        table_layout = QtWidgets.QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.setSpacing(5)

        # 表格标题
        table_title = QtWidgets.QLabel("雷达设备详细信息")
        table_title.setStyleSheet("""
            QLabel {
                color: rgba(22, 175, 252, 255);
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        table_layout.addWidget(table_title)

        # 创建表格
        self.table = QtWidgets.QTableWidget()
        self.setup_table()
        table_layout.addWidget(self.table)

        layout.addWidget(table_widget)

    def setup_table(self):
        """设置表格"""
        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(25,25,50,1);
                border: 1px solid rgba(22, 175, 252, 0.5);
                border-radius: 5px;
                gridline-color: rgba(22, 175, 252, 0.3);
                color: white;
                font-size: 11px;
                alternate-background-color: rgba(60, 60, 60, 0.8);
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(22, 175, 252, 0.2);
                color: white;
                background-color: transparent;
            }
            QTableWidget::item:alternate {
                background-color: rgba(25,25,50,1);
                color: white;
            }
            QTableWidget::item:selected {
                background-color: rgba(22, 175, 252, 0.4);
                color: white;
            }
            QTableWidget::item:hover {
                background-color: rgba(22, 175, 252, 0.2);
                color: white;
            }
            QHeaderView::section {
                background-color: rgba(22, 175, 252, 0.8);
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
            QTableWidget QTableCornerButton::section {
                background-color: rgba(22, 175, 252, 0.8);
                border: none;
            }
        """)

        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)

        # 设置列
        headers = ["设备名称", "设备型号", "部署位置", "经纬度", "电量", "状态", "更新时间"]
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)

        # 填充数据
        self.populate_table()

        # 调整列宽
        self.table.resizeColumnsToContents()
        self.table.horizontalHeader().setStretchLastSection(True)

    def populate_table(self):
        """填充表格数据"""
        self.table.setRowCount(len(self.radar_data))
        PP(f"开始填充雷达设备表格，共 {len(self.radar_data)} 条数据")

        for row, radar in enumerate(self.radar_data):
            # 解析设备信息
            info_str = radar.get('RADER_INFO', '')
            code, longitude, latitude = self.parse_radar_info(info_str)

            # 填充每一列
            items = [
                radar.get('RADER_NAME', ''),
                code,
                radar.get('RADER_LOCATION', ''),
                f"{longitude}, {latitude}",
                f"{radar.get('RADER_POWER', '0')}%",
                radar.get('RADER_STATUS', ''),
                radar.get('RADER_UPDATE', '')
            ]

            PP(f"第{row+1}行数据: {items}")

            for col, item_text in enumerate(items):
                item = QtWidgets.QTableWidgetItem(str(item_text))
                item.setTextAlignment(QtCore.Qt.AlignCenter)

                # 设置默认文字颜色为白色
                item.setForeground(QtGui.QColor("white"))

                # 根据列设置特殊颜色
                if col == 4:  # 电量列
                    try:
                        power_value = int(radar.get('RADER_POWER', '0'))
                        if power_value >= 70:
                            item.setForeground(QtGui.QColor("#4ECDC4"))  # 绿色
                        elif power_value >= 30:
                            item.setForeground(QtGui.QColor("#FFEAA7"))  # 黄色
                        else:
                            item.setForeground(QtGui.QColor("#FF6B6B"))  # 红色
                    except:
                        item.setForeground(QtGui.QColor("#FF6B6B"))
                    item.setBackground(QtGui.QColor(0, 0, 0, 0))  # 透明背景
                elif col == 5:  # 状态列
                    if item_text == 'Active':
                        item.setForeground(QtGui.QColor("#4ECDC4"))
                        item.setText("在线")
                    else:
                        item.setForeground(QtGui.QColor("#FF6B6B"))
                        item.setText("离线")
                    item.setBackground(QtGui.QColor(0, 0, 0, 0))  # 透明背景
                else:
                    # 其他列设置透明背景，确保继承表格的交替颜色
                    item.setBackground(QtGui.QColor(0, 0, 0, 0))

                self.table.setItem(row, col, item)

        PP("雷达设备表格填充完成")



    def parse_radar_info(self, info_str):
        """解析 RADER_INFO 字符串"""
        try:
            # 简单的字符串解析，提取 CODE, LONGITUDE, LATITUDE
            code = "未知"
            longitude = "未知"
            latitude = "未知"

            if "CODE" in info_str:
                start = info_str.find("'CODE':'") + 8
                end = info_str.find("'", start)
                if end > start:
                    code = info_str[start:end]

            if "LONGITUDE" in info_str:
                start = info_str.find("'LONGITUDE':'") + 13
                end = info_str.find("'", start)
                if end > start:
                    longitude = info_str[start:end]

            if "LATITUDE" in info_str:
                start = info_str.find("'LATITUDE':'") + 12
                end = info_str.find("'", start)
                if end > start:
                    latitude = info_str[start:end]

            return code, longitude, latitude
        except:
            return "未知", "未知", "未知"



    def Dialog_Close(self):
        """关闭对话框"""
        self.close()
