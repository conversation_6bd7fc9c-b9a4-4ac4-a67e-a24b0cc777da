!function(t,i,e){function s(t){var i={};return void 0!=t.touches?(i.x=t.touches[0].pageX,i.y=t.touches[0].pageY):void 0!==t.pageX?(i.x=t.pageX,i.y=t.pageY):(i.x=t.clientX,i.y=t.clientY),i}function o(e,s){this.element=e,this.width=null,this.volume=null,this.current=null,this.clonesContainer=[],this.positionsContainer=[],this.itemsContainer=[],this.maskWrapper=null,this.speed=null,this.verticalSlide=!1,this.activeProp="width",this.clonnedPropVolume=null,this.autoPlayLoop=null,this.touch={},this.isTouch=!1,this.processes={},this.Objs=[],this.resizeHandle=null,t.each(o.Obj,t.proxy(function(i,e){this.Objs.push({type:e.type,update:t.proxy(e.update,this)})},this)),this.initiate(s),this.options.resize&&t(i).on("resize",t.proxy(function(){clearTimeout(this.resizeHandle),this.resizeHandle=setTimeout(t.proxy(this.reset,this),this.options.resizeRefresh)},this))}var n=null;o.Obj=[{type:["items","width","all"],update:function(){this.element.find(".cloned").remove(),this.clonesContainer=[],this.positionsContainer=[]}},{type:["clones"],update:function(){var i,e=this.clonesContainer.length-Math.max(2*this.options.items,4);e=Math.abs(e/2);for(var s=this.getItemCss(),o=0,n=0,r=e;n<r;n++)this.clonesContainer.push(this.clonesContainer.length/2),i=t(this.itemsContainer[this.clonesContainer[this.clonesContainer.length-1]]).clone(),this.maskWrapper.append(i.css(s).addClass("cloned")),o+=this.verticalSlide?i.outerHeight(!0):i.outerWidth(!0),this.clonesContainer.push(this.itemsContainer.length-1-(this.clonesContainer.length-1)/2),i=t(this.itemsContainer[this.clonesContainer[this.clonesContainer.length-1]]).clone(),this.maskWrapper.prepend(i.css(s).addClass("cloned")),o+=this.verticalSlide?i.outerHeight(!0):i.outerWidth(!0);this.clonnedPropVolume=o}},{type:["items","width"],update:function(){var i,e,s,o,n=0;for(o="rtl"==this.options.direction?1:-1,"rtl"==this.options.direction&&this.options.vertical&&(o=-1),s=(this.verticalSlide?this.getHeight()/this.options.items:this.getWidth())/this.options.items,i=0,e=this.clonesContainer.length+this.itemsContainer.length;i<e;i++)this.verticalSlide?n+=(t(this.itemsContainer[this.relativePos(i)]).height()+this.options.margin)*o:n+=this.options.fixWidth?s*o:(t(this.itemsContainer[this.relativePos(i)]).width()+this.options.margin)*o,this.positionsContainer.push(n)}},{type:["items","width","height"],update:function(){var t={};this.volume=this.getVolume(this.activeProp,0,this.itemsContainer.length),t[this.activeProp]=(this.options.circular,Math.abs(this.getPosition(this.positionsContainer.length))),t.overflow="hidden",this.maskWrapper.css(t)}},{type:["items","width","position"],update:function(){var t=this.getPosition(this.normalizePos(this.current))||0;this.animate(t)}},{type:["items","width","position"],update:function(){for(var t="rtl"==this.options.direction?1:-1,i=[],e=this.getPosition(this.current),s=e+this.getWidth("inner")*t,o=0,n=this.positionsContainer.length;o<n;o++){var r=Math.abs(this.positionsContainer[o])*t,h=this.positionsContainer[o-1]||0;(this.compare(h,e,"<=")&&this.compare(h,s,">")||this.compare(r,e,"<")&&this.compare(r,s,">"))&&i.push(o)}this.maskWrapper.children("."+this.options.activeClass).removeClass(this.options.activeClass);var a="("+i.join("), :eq(")+")";if(this.maskWrapper.children(":eq"+a).addClass(this.options.activeClass),!this.options.fixHeight){var p=this.maskWrapper.children("."+this.options.activeClass).height();this.maskWrapper.css("height",p+"px")}}}],o.prototype.getViewPort=function(){return t(i).width()},o.prototype.update=function(){for(var i=0,e=this.Objs.length,s=t.proxy(function(t){return this[t]},this.processes);i<e;)(this.processes.updateAll||t.grep(this.Objs[i].type,s).length)&&this.Objs[i].update(),i++;this.processes={}},o.prototype.queueProcess=function(t){this.processes[t]=!0},o.prototype.initiate=function(i){var e;this.options=t.extend({},o.defaultOptions,i),this.mainOptions=this.options,this.resize=this.options.resize;var s=this.getViewPort(),n=0;this.resize&&(t.each(this.resize,function(t){t<=s&&t>-1&&(n=t)}),this.options=n?t.extend({},this.options,this.resize[n]):this.options),this.options.init(),this.element.addClass(this.options.elemClass),this.maskWrapper=t('<div class="TurboMask '+this.options.maskClass+'"></div>'),this.sliderWrapper=t('<div class="TurboWrapper"></div>'),new o.videos(this),this.element.css({overflow:"hidden",visibility:"hidden",position:"static"==this.element.css("position")?"relative":this.element.css("position"),direction:"rtl"==this.options.direction?"rtl":"ltr"}),e=this.element.children(),this.maskWrapper.append(e),this.element.append(this.maskWrapper),this.element.after(this.sliderWrapper),this.sliderWrapper.append(this.element),this.storeItems(e),this.width=this.element.width(),this.height=this.element.height(),this.cacheFirst(),this.mode(),this.currentSlide(this.options.startSlide),this.activeProp=this.verticalSlide?"height":"width",this.options.circular&&this.queueProcess("clones"),this.options.startSlide=t.isNumeric(this.options.startSlide)?this.options.startSlide:0,this.setItemsCss(),this.element.css("visibility","visible"),this.attach(),this.options.autoPlay&&this.autoPlay(),this.options.wheel&&this.element.on("mousewheel DOMMouseScroll MozMousePixelScroll",t.proxy(this.mouseWheel,this)),this.options.prevTrigger&&t(this.options.prevTrigger).length&&t(this.options.prevTrigger).on("click",t.proxy(this.prev,this)),this.options.nextTrigger&&t(this.options.nextTrigger).length&&t(this.options.nextTrigger).on("click",t.proxy(this.next,this)),this.queueProcess("items"),this.update(),this.resetSlide(this.maskWrapper.children().index(this.firstSlide)),this.navigation()},o.prototype.reset=function(){this.sliderWrapper.before(this.element),this.sliderWrapper.remove(),this.element.append(this.element.find(".TurboMask").children()),this.element.find(".TurboMask").remove(),this.element.find(".cloned").remove(),this.initiate(this.mainOptions)},o.prototype.unbind=function(){this.options.wheel&&this.element.off("DOMMouseScroll",t.proxy(this.mouseWheel,this)),this.options.prevTrigger&&t(this.options.prevTrigger).length&&t(this.options.prevTrigger).off("click",t.proxy(this.prev,this)),this.options.nextTrigger&&t(this.options.nextTrigger).length&&t(this.options.nextTrigger).off("click",t.proxy(this.next,this))},o.prototype.eventTarget=function(t){switch(t.type){case"mousedown":case"touchstart":this.dragStart(t);break;case"mousemove":case"touchmove":this.dragMove(t);break;case"mouseup":case"touchend":this.dragEnd(t)}},o.prototype.attach=function(){this.maskWrapper.on("transitionend",t.proxy(this.transitionEnd,this)),this.options.drag&&(this.maskWrapper.on(o.events.drag.start,t.proxy(this.eventTarget,this)),this.maskWrapper.on("dragstart",function(){return!1}))},o.prototype.currentSlide=function(i){if(void 0===i)return this.current;var e,s,o;e=this.current,s=this.getMin(),o=this.getMax(),i=this.normalizePos(i),t.proxy(this.options.before,this)(),this.options.circular||(i>o?i=e>o?s:o:i<s&&(i=o)),this.current=i,this.trigger("changed_position",{value:{position:this.current,previous:e}})},o.prototype.trigger=function(t,i){return i.time=new Date,i.type="Turbo-evnt-"+t,this.element.trigger("Turbo-evnt-"+t,i)},o.prototype.cacheFirst=function(){this.firstSlide=this.itemsContainer[this.relativePos(this.current)]},o.prototype.slideTo=function(t){var i=this.relativePos(this.current);return t?i+=this.options.slideCount:i-=this.options.slideCount,i},o.prototype.next=function(){this.slide(this.slideTo(!0),this.options.slideSpeed)},o.prototype.prev=function(){this.slide(this.slideTo(!1),this.options.slideSpeed)},o.prototype.destroy=function(){this.sliderWrapper.before(this.element),this.sliderWrapper.remove(),this.element.append(this.maskWrapper.children()),this.maskWrapper.remove(),this.element.removeData("Turbo"),this.element.find(".cloned").remove(),this.element.find("div").removeClass("active"),this.element.css({marginTop:"",display:"",overflow:"",visibility:"",position:"",direction:""}),this.element.removeClass("active")},o.prototype.autoPlay=function(){var e=this.next;this.vertical||"rtl"!=this.options.direction||(e=this.next),this.vertical||"ltr"!=this.options.direction||(e=this.next),i.clearInterval(this.autoPlayLoop),this.autoPlayLoop=i.setInterval(t.proxy(e,this),this.options.timeout),this.options.pauseOnHover&&this.element.hover(t.proxy(this.stop,this),t.proxy(this.autoPlay,this))},o.prototype.stop=function(){i.clearInterval(this.autoPlayLoop),i.clearTimeout(this.anim)},o.prototype.pause=function(){i.clearInterval(this.autoPlayLoop)},o.prototype.transitionEnd=function(){this.options.autoPlay&&this.autoPlay()},o.prototype.getVolume=function(i,e,s){for(var o,n=0,r=e;r<s;r++)o=t(this.itemsContainer[r]),n+="width"==i?o.outerWidth(!0):o.outerHeight(!0);return n},o.prototype.getItemCss=function(){var t,i,e;return this.verticalSlide?(t={display:"block",float:"none",width:"100%"},e=(this.getHeight()/this.options.items).toFixed(3),t["margin-bottom"]=this.options.margin+"px",t.height=e-this.options.margin+"px"):((t={display:"block",float:"ltr"==this.options.direction?"left":"right"})["margin-"+("rtl"==this.options.direction?"left":"right")]=this.options.margin+"px",i=(this.getWidth()/this.options.items).toFixed(3),this.options.fixWidth&&(t.width=Math.abs(i)-this.options.margin+"px")),t},o.prototype.setItemsCss=function(){var i,e,s;for(s=this.getItemCss(),i=0;e=this.itemsContainer.length,i<e;i++)t(this.itemsContainer[i]).css(s)},o.prototype.mode=function(){o.modes;this.options.vertical&&(this.verticalSlide=!0)},o.prototype.slide=function(i,e){var s,o,r,h;n=this.current,h=this.itemsContainer.length+this.clonesContainer.length,r=i-this.relativePos(this.current),this.stop(),this.options.circular?(s=r+this.current,o=this.current,s<this.options.items?(n=o+this.itemsContainer.length,this.resetSlide(n)):s>=h-this.options.items&&(n=o-this.itemsContainer.length,this.resetSlide(n)),this.anim=setTimeout(t.proxy(function(){this.speed=this.normalizeSpeed(this.current,n+r,e)||0,this.currentSlide(n+r),this.queueProcess("position"),this.update()},this),30)):(this.speed=this.options.slideSpeed||0,this.currentSlide(i),this.queueProcess("position"),this.update())},o.prototype.animate=function(i){var e,s={};this.verticalSlide?s.transform="translate3d(0px,"+i+"px,0px)":s.transform="translate3d("+i+"px,0px,0px)",e=this.speed/1e3,s.transition=e+"s",this.maskWrapper.css(s),this.trigger("slideEnd",{index:this.current}),t.proxy(this.options.after,this)()},o.prototype.resetSlide=function(t){t=this.normalizePos(t),this.speed=0,this.current=t,this.queueProcess("position"),this.update()},o.prototype.getPosition=function(i){return void 0===i?t.map(this.positionsContainer,t.proxy(function(t,i){return this.getPosition(i)},this)):this.positionsContainer[i-1]||0},o.prototype.getWidth=function(t){return"inner"==t?this.width:"outer"==t?this.width:this.width+this.options.margin},o.prototype.getHeight=function(){return this.height},o.prototype.getMin=function(t){return t?0:this.clonesContainer.length/2},o.prototype.getMax=function(t){var i;return t?this.itemsContainer.length-1:(this.options.circular?this.options.circular&&(i=this.itemsContainer.length+this.options.items):i=this.itemsContainer.length-this.options.items,i)},o.prototype.normalizePos=function(t,i){if(this.options.circular){var e=i?this.itemsContainer.length:this.itemsContainer.length+this.clonesContainer.length;t=(t%e+e)%e}else t=Math.max(this.getMin(i),Math.min(this.getMax(i),t));return t},o.prototype.relativePos=function(t){return t=this.normalizePos(t),t-=this.clonesContainer.length/2,this.normalizePos(t,!0)},o.prototype.storeItems=function(i){this.itemsContainer=[],i.each(t.proxy(function(t,i){this.itemsContainer.push(i),this.trigger("item-ready",{value:{item:i,index:t}})},this))},o.prototype.normalizeSpeed=function(t,i,e){return Math.min(Math.max(Math.abs(t-i),1),6)*Math.abs(e)},o.prototype.navigation=function(){var i=!1,e=t('<div class="slider-navigation"><div class="dotsClass '+this.options.dotsClass+'" style="list-style:none;direction: '+this.options.direction+'"></div></div>');if(this.options.hasDots){this.list=[],this.createList();this.current;for(var s=0;s<this.list.length;s++)e.find(".dotsClass").append('<span class="dotClass '+this.options.dotClass+'"></span>');this.navgationContainer=e,this.navgationDots=e.find(".dotsClass"),this.navgationDots.children().eq(t.inArray(this.getDotNavigationPos(),this.list)).addClass("active"),this.sliderWrapper.on("Turbo-evnt-changed_position",t.proxy(function(i,e){this.navgationDots.find(".active").removeClass("active"),this.navgationDots.children().eq(t.inArray(this.getDotNavigationPos(),this.list)).addClass("active")},this)),e.on("click",".dotsClass span",t.proxy(function(i){t(this);var e=t(i.target).index();this.slideToDotPage(e)},this)),i=!0}this.options.hasNav&&(e.append('<div class="turbo-nav"><div class="turbo-prev">'+this.options.navPrevLabel+'</div><div class="turbo-next">'+this.options.navNextLabel+"</div></div>"),e.on("click",".turbo-nav .turbo-prev",t.proxy(function(t){this.prev()},this)),e.on("click",".turbo-nav .turbo-next",t.proxy(function(t){this.next()},this)),i=!0),i&&this.sliderWrapper.append(e)},o.prototype.dragStart=function(n){var r,h,a,p,l={};a=n.originalEvent||i.event||n,this.isTouch=!0,this.isTouch&&this.stop(),this.speed=0,r=(p=s(a)).x,h=p.y,l.x=this.maskWrapper.position().left,l.y=this.maskWrapper.position().top,this.touch.target=a.target||a.srcElement,"rtl"==this.options.direction&&(l.x=l.x+this.maskWrapper.width()-this.getWidth()+this.options.margin),this.touch.start={x:r-l.x,y:h-l.y},this.touch.offsetX=l.x,this.touch.offsetY=l.y,this.touch.start.start=this.verticalSlide?h-this.touch.start.y:r-this.touch.start.x;var d=["img","a"];-1!==t.inArray(this.touch.target.tagName.toLowerCase(),d)&&(this.touch.target.draggable=!1),this.maskWrapper.addClass("turbo-drag"),t(e).on(o.events.drag.move+" "+o.events.drag.end,t.proxy(this.eventTarget,this))},o.prototype.dragMove=function(t){var e,o,n,r,h,a,p;e=(n=s(t.originalEvent||i.event||t)).x,o=n.y,this.touch.destX=e-this.touch.start.x,this.touch.destY=o-this.touch.start.y,p=this.verticalSlide?this.touch.destY:this.touch.destX,r=this.verticalSlide?this.touch.destY-this.touch.offsetY:this.touch.destX-this.touch.offsetX,a=this.options.direction,h=r>0?"rtl"==a?"left":"right":"rtl"==a?"right":"left",this.touch.directionType=h,this.options.circular&&(this.compare(p,this.getPosition(this.getMin()),">")&&"right"==h?p-=this.getPosition(0)-this.getPosition(this.itemsContainer.length):this.compare(p,this.getPosition(this.getMax()),"<")&&"left"==h&&(p+=this.getPosition(0)-this.getPosition(this.itemsContainer.length))),this.animate(p),this.verticalSlide?this.touch.destY=p:this.touch.destX=p},o.prototype.dragEnd=function(){var i,s,n;this.maskWrapper.removeClass("turbo-drag"),i=this.verticalSlide?this.touch.destY:this.touch.destX,s=this.getPosition(),this.touch.target.removeAttribute("draggable"),t.each(s,t.proxy(function(t,e){i>e-30&&i<e+30?n=t:this.compare(i,e,"<")&&this.compare(i,s[t+1],">")&&(n="left"==this.touch.directionType?t+1:t)},this)),this.speed=this.options.dragSpeed,this.currentSlide(n),this.queueProcess("position"),this.update(),t(e).off(o.events.drag.move),t(e).off(o.events.drag.end)},o.prototype.mouseWheel=function(t){var e,s=0;if(t.preventDefault(),t||i.event,"wheelDeltaX"in(e=t.originalEvent))e.wheelDeltaX,s=e.wheelDeltaY;else if("wheelDelta"in e)s=e.wheelDelta;else{if(!("detail"in e))return;s=-e.detail}this.verticalSlide?s>0?this.next():this.prev():s>0?this.prev():this.next()},o.prototype.compare=function(t,i,e){var s,o="rtl"==this.options.direction;switch(e){case"<":s=o?t>i:t<i;break;case">":s=o?t<i:t>i;break;case">=":s=o?t<=i:t>=i;break;case"<=":s=o?t>=i:t<=i}return s},o.prototype.getDotNavigationPos=function(){var i=this.relativePos(this.current);return t.grep(this.list,function(t){return t.first<=i&&t.last>=i}).pop()},o.prototype.slideToDotPage=function(i){var e,s;e=this.list.length,s=this.list[(i%e+e)%e],t.proxy(this.slide,this)(s.first,this.options.dotSpeed)},o.prototype.createList=function(){var t,i,e=this.options,s=this.options.circular?e.items:1,o=this.clonesContainer.length/2||0,n=o+this.itemsContainer.length;for(t=o,i=0;t<n;t++)(i>=s||0==i)&&(this.list.push({first:t-o,last:t-o+s-1}),i=0),i+=1},o.videos=function(t){this.turbo=t,this.videos={},this.register()},o.videos.prototype.register=function(){this.turbo.sliderWrapper.on("Turbo-evnt-item-ready",t.proxy(function(i,e){var s=(e=e.value).item;t(s).hasClass("_video")&&this.parse(t(s),t(s).find(".item_video").attr("href"))},this)),this.turbo.sliderWrapper.on("click",".turbo-video-play",t.proxy(function(i){var e=t(i.target).parent(".turbo-video-wrapper");return this.videos[e.attr("data-video")]&&(this.stop(),this.play(e.parent(),this.videos[e.attr("data-video")])),!1},this)),this.turbo.sliderWrapper.on("Turbo-evnt-changed_position",t.proxy(function(t,i){(i=i.value).previous!=i.position&&this.stop()},this))},o.videos.prototype.stop=function(){var i,e=this;this.turbo.element.find(".item.playing").each(function(){i=t(this).find(".turbo-video-wrapper").attr("data-video"),console.log(i),t(this).find("iframe").remove(),e.getThumb(t(this),e.videos[i])})},o.videos.prototype.parse=function(t,i){var e,s,o=/(http:|https:)\/\/(www.)?(youtube\.com|vimeo\.com)\/(watch\?v=|embed)?([A-Za-z0-9._%-]*)(\&\S+)?/;(e=i.match(o))&&(e[3].indexOf("youtube")>-1?s="youtube":e[3].indexOf("vimeo")>-1&&(s="vimeo"),this.videos[i]={type:s,id:e[5],width:t.attr("data-width")||this.turbo.videoWidth,height:t.attr("data-height")||this.turbo.videoHeight,url:i},this.getThumb(t,this.videos[i]))},o.videos.prototype.createThumb=function(i,e,s){var o,n=t('<div class="turbo-video-wrapper"></div>');n.append('<div class="turbo-video-play"></div>'),o='<div class="turbo-video-thumb" style="background-image:url('+e+')"></div>',n.append(o),n.attr("data-video",s.url),i.append(n)},o.videos.prototype.getThumb=function(i,e){var s;switch(e.type){case"youtube":s="http://img.youtube.com/vi/"+e.id+"/hqdefault.jpg",this.createThumb(i,s,e);break;case"vimeo":t.ajax({type:"GET",url:"http://vimeo.com/api/v2/video/"+e.id+".json",jsonp:"callback",dataType:"jsonp",success:t.proxy(function(t){s=t[0].thumbnail_large,this.createThumb(i,s,e)},this)})}},o.videos.prototype.play=function(t,i){i.width||(i.width="100%"),i.height||(i.height="100%"),t.find(".turbo-video-wrapper").hide(),"youtube"==i.type?t.append('<iframe width="'+i.width+'" height="'+i.height+'" src="http://www.youtube.com/embed/'+i.id+'?autoplay=1" frameborder="0" allowfullscreen></iframe>'):"vimeo"==i.type&&t.append('<iframe src="http://player.vimeo.com/video/'+i.id+'?autoplay=1" width="'+i.width+'" height="'+i.height+'" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>'),t.addClass("playing")},o.videos.prototype.output=function(){},o.modes={vertical:"vertical",horizontal:"horizontal"},o.events={drag:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}},o.defaultOptions={items:4,autoPlay:!1,pauseOnHover:!1,startSlide:0,circular:!0,direction:"ltr",vertical:!1,elemClass:"sliderClass",maskClass:"maskClass",activeClass:"active",hasDots:!0,hasNav:!1,dotsClass:"",dotClass:"",navNextLabel:"Next",navPrevLabel:"Prev",nextTrigger:!1,prevTrigger:!1,dotSpeed:250,slideSpeed:500,dragSpeed:250,slideCount:1,timeout:500,margin:0,fixWidth:!0,fixHeight:!0,drag:!0,wheel:!1,resize:!1,resizeRefresh:200,hasVideo:!1,videoWidth:!1,videoHeight:!1,before:function(){},after:function(){},init:function(){}},t.fn.Turbo=function(i){return this.each(function(){t(this).data("Turbo")||t(this).data("Turbo",new o(t(this),i))})}}(jQuery,window,document);