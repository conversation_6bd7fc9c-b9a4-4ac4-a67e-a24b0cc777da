# -*- coding: utf-8 -*-
import time,os,sys,cv2
from PySide6 import Qt<PERSON>ore, QtGui, QtWidgets


class Component_Config(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()


        self.initUI()

    def initUI(self):
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QWidget_TopFiller = QtWidgets.QWidget()
        self.QWidget_TopFiller.setStyleSheet("QWidget{background:transparent;border: none;border-width:0px;}")
        self.QVBoxLayout_Line = QtWidgets.QVBoxLayout(self.QWidget_TopFiller)
        self.QVBoxLayout_Line.setSpacing(6)  # 内边界
        self.QVBoxLayout_Line.setContentsMargins(0, 0, 0, 0)  # 外边

        StyleSheet_Line = """QLabel {
                                                       background-color: rgba(255,255,255,0.8);
                                                       border-radius: 0px;
                                                       border: none;
                                                   }
                                                   QLabel:hover {
                                                       background-color: rgba(255,255,255,0.3);
                                                       border: none;
                                                   }
                                               """

        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Address"])
        # self.QVBoxLayout_Line.addWidget(self.QLabel_Line_List["Set_Line_Source_Token"])
        Line_List=[
            {"Line_Name":"设置一","Line_Size":[48,348],"Line_StyleSheet":StyleSheet_Line},
            {"Line_Name":"设置二","Line_Size":[48,348],"Line_StyleSheet":StyleSheet_Line},
            {"Line_Name":"设置三","Line_Size":[48,348],"Line_StyleSheet":StyleSheet_Line},
            {"Line_Name":"设置四","Line_Size":[48,348],"Line_StyleSheet":StyleSheet_Line},
            {"Line_Name":"设置五","Line_Size":[48,348],"Line_StyleSheet":StyleSheet_Line},



        ]
        for Line_Info in Line_List:

            self.QVBoxLayout_Line.addWidget(self.Set_Line(Line_Info))






        ##创建一个滚动条
        self.QScrollArea_Line = QtWidgets.QScrollArea()
        self.QScrollArea_Line.setStyleSheet(
            "QScrollBar {height:0px;border: none;}QScrollArea {border: none;background: transparent;}")
        self.QScrollArea_Line.setWidget(self.QWidget_TopFiller)
        # __QVBoxLayout.addWidget(self.QLabel_Line_List["Set_Line_Source_Select"])
        __QVBoxLayout.addWidget(self.QScrollArea_Line, 1)


    def Set_Line(self,Line_Info):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(Line_Info["Line_Size"][0])
        __QLabel.setFixedWidth(Line_Info["Line_Size"][1])
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        __QLabel.setStyleSheet(Line_Info["Line_StyleSheet"])
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setContentsMargins(10, 0, 10, 0)
        __QHBoxLayout.setSpacing(12)

        # 名称
        QLabel_Name = QtWidgets.QLabel(Line_Info["Line_Name"])
        QLabel_Name.setFixedWidth(120)
        QLabel_Name.setFont(QtGui.QFont("Microsoft YaHei", 12))
        QLabel_Name.setStyleSheet("background: transparent;")
        __QHBoxLayout.addWidget(QLabel_Name, 1)

        # self.QLineEdit_SourceAddress = QtWidgets.QLineEdit()
        # # __QLineEdit.setText("rtsp://admin:csc888888!@**************:554/Streaming/Channels/101")
        # self.QLineEdit_SourceAddress.setPlaceholderText("请输入 RTSP / HTTP / 本地文件路径")
        # self.QLineEdit_SourceAddress.setStyleSheet("""
        #     QLineEdit {
        #         background-color: rgba(0, 0, 0, 0.8);   /* 半透明背景 */
        #         border: 1px solid rgba(76, 201, 240, 0.4);     /* 科技蓝边框 */
        #         border-radius:3px;                            /* 圆角 */
        #         padding: 6px 10px;                             /* 内边距 */
        #         color: #e0e0e0;                                /* 文字颜色 */
        #         font-family: "Microsoft YaHei";
        #         font-size: 13px;
        #         selection-background-color: #000000;          /* 选中文本背景 */
        #         selection-color: #ffffff;                     /* 选中文本颜色 */
        #     }
        #
        #     /* 鼠标悬停状态 */
        #     QLineEdit:hover {
        #         border: 1px solid rgba(76, 201, 240, 0.7);
        #         background-color: rgba(255, 255, 255, 0.12);
        #            selection-background-color: #000000;          /* 选中文本背景 */
        #         selection-color: #ffffff;                     /* 选中文本颜色 */
        #         color: #000000;
        #     }
        #
        #     /* 聚焦状态（键盘输入时） */
        #     QLineEdit:focus {
        #         border: 1px solid #4cc9f0;
        #         background-color: rgba(255, 255, 255, 0.15);
        #     }
        #
        #     /* 占位提示文字 */
        #     QLineEdit[placeholderText] {
        #         color: rgba(255, 255, 255, 0.8);              /* 半透明提示文字 */
        #     }
        # """)
        # self.QLineEdit_SourceAddress.setSizePolicy(QtWidgets.QSizePolicy.Expanding,QtWidgets.QSizePolicy.Fixed)
        # __QHBoxLayout.addWidget(self.QLineEdit_SourceAddress)

        return __QLabel










