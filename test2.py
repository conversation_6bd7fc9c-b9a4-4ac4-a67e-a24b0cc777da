import sys,time,os
from PySide6 import QtWidgets, QtGui, QtCore
import qtawesome as qta
from Bin.System.OS.Component import Component_Common
from Bin.Utils.UtilsCenter import *
# -*- coding: utf-8 -*-
import time, os, sys, cv2
from PySide6 import Qt<PERSON><PERSON>, QtGui, QtWidgets
import Component_Common
import qtawesome
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils\Toolkit")
# import Service_Table
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils\Toolkit")
import Service_Print, Service_Table

PP = Service_Print.Service_Print()
PW = Service_Print.Service_Print('Warning')
PI = Service_Print.Service_Print('Info')
PS = Service_Print.Service_Print('Success')
PE = Service_Print.Service_Print('Error')
PC = Service_Print.Service_Print('Core')
PK = Service_Print.Service_Print('Key')
PT = Service_Print.Service_Print('TryError')
PJ = Service_Print.Service_Print('Json')
PL = Service_Print.Service_Print('Logging')
PPP = Service_Print.Service_Print('Json_Time')
sys.path.append(rf"D:\Sentinel Foundation\Bin\Utils")
from UtilsCenter import *

class Component_Processing_Portrait(QtWidgets.QLabel):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.resize(1280, 720)  # 与EQ处理弹窗相同的尺寸
        self.setAcceptDrops(True)
        self.setStyleSheet("QLabel{background:rgba(25,25,50,1);border-width:0px;border-radius:6px;padding:2px 4px;color:black;border-style: solid;border: none;}")

        # 初始化数据
        self.portrait_data = []
        self.load_portrait_data()

        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)
        __QVBoxLayout.setSpacing(0)  # 内边界
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("QLabel{background:transparent;border: none;}")
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("QLabel{background:transparent;}")

        __QVBoxLayout.addWidget(self.Set_Title(), 0)
        __QVBoxLayout.addWidget(self.Set_Content(),1)

    def load_portrait_data(self):
        """加载人像数据"""
        try:
            Data = {
                "Table_Name": "Portrait_Info_List",
                "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Portrait\Portrait_Info_List.db",
            }
            __Service_Table = Service_Table.Service_Table("sqlite_backall")
            self.portrait_data = __Service_Table(Data)
            PP(f"成功从数据库加载 {len(self.portrait_data)} 条人像数据")
            # PJ(self.portrait_data)
        except Exception as e:
            PE(f"从数据库加载人像数据失败: {e}")
            self.portrait_data = []

    def Set_Title(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setFixedHeight(40)
        __QLabel.setStyleSheet("QLabel{background:transparent;}")
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)  # 内边界
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)  # 外边

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(30, 30)
        QLabel_Icon.setStyleSheet("""QLabel {padding: 3px;background:rgba(0, 0, 0,0);border-radius:6px;}""")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(28, 28, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        QLabel_Icon.resize(28, 28)
        QLabel_Icon.setScaledContents(True)
        QLabel_Icon.setPixmap(Image_Logo)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setFixedWidth(80)
        QLabel_Title.setStyleSheet(
            "QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        QLabel_Title.setAlignment(QtCore.Qt.AlignVCenter |QtCore.Qt.AlignLeft)
        QLabel_Title.setText("人脸部署人数")

        Icon = qta.icon('ph.x-thin', scale_factor=1, color='white', color_active='blue')
        QPushButton_Close = QtWidgets.QPushButton(Icon, '')
        QPushButton_Close.setFixedWidth(58)
        QPushButton_Close.setStyleSheet(
            '''QPushButton {background:transparent;} QPushButton:hover{background-color: rgba(0,0,0,0.6);border: 0px;border-radius:3px;font-weight: bold;}''')
        QPushButton_Close.clicked.connect(lambda: self.Dialog_Close())

        __QHBoxLayout.addWidget(QLabel_Icon,  0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter )
        __QHBoxLayout.addWidget(QLabel_Title, 9, alignment=QtCore.Qt.AlignLeft)
        __QHBoxLayout.addWidget(QPushButton_Close,  1,alignment=QtCore.Qt.AlignRight)

        return __QLabel

    def Set_Content(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("background: rgba(25,25,50,1);")

        # 使用布局确保占据所有空间
        __QVBoxLayout = QtWidgets.QVBoxLayout(__QLabel)
        __QVBoxLayout.setContentsMargins(15, 15, 15, 15)
        __QVBoxLayout.setSpacing(10)
        __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)

        # 添加标题和统计信息
        self.create_header(__QVBoxLayout)

        # 添加人像数据表格
        self.create_portrait_table(__QVBoxLayout)

        return __QLabel

    def create_header(self, layout):
        """创建标题和统计信息"""
        # 主标题
        title_label = QtWidgets.QLabel("部署人数预警信息")
        title_label.setStyleSheet("""
            QLabel {
                color: rgba(22, 175, 252, 255);
                font-size: 18px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        title_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title_label)

        # 统计信息
        stats_widget = QtWidgets.QWidget()
        stats_widget.setStyleSheet("background: transparent;")
        stats_layout = QtWidgets.QHBoxLayout(stats_widget)
        stats_layout.setSpacing(30)
        stats_layout.setAlignment(QtCore.Qt.AlignCenter)

        # 统计数据
        total_count = len(self.portrait_data)
        active_count = len([p for p in self.portrait_data if p.get('PORTRAIT_STATUS') == 'Active'])

        stats_data = [
            {"label": "总人数", "value": f"{total_count}人", "color": "#4ECDC4"},
            {"label": "中标人数", "value": f"{active_count}人", "color": "#96CEB4"},
            {"label": "今日更新", "value": f"{total_count}条", "color": "#FFEAA7"},
        ]

        for stat in stats_data:
            stat_label = QtWidgets.QLabel()
            stat_label.setStyleSheet(f"""
                QLabel {{
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid {stat["color"]};
                    border-radius: 8px;
                    padding: 8px 15px;
                    color: white;
                    font-size: 12px;
                }}
            """)
            stat_label.setText(f'<div style="text-align:center;">'
                              f'<span style="color:{stat["color"]};font-size:16px;font-weight:bold">{stat["value"]}</span><br>'
                              f'<span style="font-size:10px;">{stat["label"]}</span>'
                              f'</div>')
            stat_label.setAlignment(QtCore.Qt.AlignCenter)
            stats_layout.addWidget(stat_label)

        layout.addWidget(stats_widget)

    def create_portrait_table(self, layout):
        """创建人像数据表格"""
        # 表格容器
        table_widget = QtWidgets.QWidget()
        table_widget.setStyleSheet("background: rgba(25,25,50,1);")
        table_layout = QtWidgets.QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.setSpacing(5)

        # 表格标题
        table_title = QtWidgets.QLabel("人像详细信息")
        table_title.setStyleSheet("""
            QLabel {
                color: rgba(22, 175, 252, 255);
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        table_layout.addWidget(table_title)

        # 创建表格
        self.table = QtWidgets.QTableWidget()
        self.setup_table()
        table_layout.addWidget(self.table)

        layout.addWidget(table_widget)

    def setup_table(self):
        """设置表格"""
        # 设置表格样式 - 修复后的版本
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(25,25,50,1);
                border: 1px solid rgba(22, 175, 252, 0.5);
                border-radius: 5px;
                gridline-color: rgba(22, 175, 252, 0.3);
                color: white;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(22, 175, 252, 0.2);
                color: white;
                background-color: rgba(30, 30, 60, 1);
            }
            QTableWidget::item:alternate {
                background-color: rgba(25,25,50,1;
                color: white;
            }
            QTableWidget::item:selected {
                background-color: rgba(22, 175, 252, 0.4);
                color: white;
            }
            QTableWidget::item:hover {
                background-color: rgba(22, 175, 252, 0.2);
                color: white;
            }
            QHeaderView::section {
                background-color: rgba(22, 175, 252, 0.8);
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
            
            QScrollBar:vertical {
                background-color: rgba(30, 30, 60, 1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: rgba(22, 175, 252, 0.6);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: rgba(22, 175, 252, 0.8);
            }
        """)

        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)

        # 设置列
        headers = ["ID", "姓名", "案件", "性别", "年龄", "地址", "状态", "更新时间"]
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)

        # 填充数据
        self.populate_table()

        # 调整列宽
        self.table.resizeColumnsToContents()
        self.table.horizontalHeader().setStretchLastSection(True)

    def populate_table(self):
        """填充表格数据 - 修复后的版本"""
        self.table.setRowCount(len(self.portrait_data))
        PP(f"开始填充表格，共 {len(self.portrait_data)} 条数据")

        for row, person in enumerate(self.portrait_data):
            # 解析PORTRAIT_INFO中的数据
            try:
                info_str = person.get('PORTRAIT_INFO', '{}')
                info_str = info_str.replace("'", '"')

                import re
                info_str = re.sub(r'("[\w_]+"),("[\w\u4e00-\u9fff]+")(?=,|}))', r'\1:\2', info_str)

                import json
                info = json.loads(info_str)
            except Exception as e:
                info = self.manual_parse_info(person.get('PORTRAIT_INFO', '{}'))

            # 填充每一列
            items = [
                str(person.get('ID', '')),
                person.get('PORTRAIT_NAME', ''),
                person.get('PORTRAIT_CASE', ''),
                info.get('GENDER', '未知'),
                info.get('AGE', '未知'),
                info.get('ADDRESS', '未知'),
                person.get('PORTRAIT_STATUS', ''),
                person.get('PORTRAIT_UPDATE', '')
            ]

            PP(f"第{row+1}行数据: {items}")

            for col, item_text in enumerate(items):
                item = QtWidgets.QTableWidgetItem(str(item_text))
                item.setTextAlignment(QtCore.Qt.AlignCenter)

                # 根据行数设置背景色（手动实现交替行颜色）
                if row % 2 == 0:  # 偶数行
                    item.setBackground(QtGui.QColor(30, 30, 60, 255))  # 深色背景
                else:  # 奇数行
                    item.setBackground(QtGui.QColor(45, 45, 75, 255))  # 稍浅背景

                # 设置文字颜色
                if col == 6:  # 状态列
                    if item_text == 'Active':
                        item.setForeground(QtGui.QColor("#4ECDC4"))
                    else:
                        item.setForeground(QtGui.QColor("#FF6B6B"))
                else:
                    item.setForeground(QtGui.QColor("white"))

                self.table.setItem(row, col, item)

        PP("表格填充完成")

    def manual_parse_info(self, info_str):
        """手动解析PORTRAIT_INFO字符串"""
        try:
            info = {}
            # 移除外层的大括号和引号
            info_str = info_str.strip().strip("'\"").strip('{}')
            # PP(f"清理后的字符串: {info_str}")

            # 尝试简单的键值对匹配
            import re
            # 匹配 'KEY':'VALUE' 或 'KEY','VALUE' 模式
            pattern = r"'(\w+)'[,:]\s*'([^']*?)'"
            matches = re.findall(pattern, info_str)

            for key, value in matches:
                info[key] = value

            # PP(f"手动解析结果: {info}")
            return info
        except Exception as e:
            # PP(f"手动解析也失败: {e}")
            return {'GENDER': '未知', 'AGE': '未知', 'ADDRESS': '未知'}

    def Dialog_Close(self):
        """关闭弹窗"""
        self.close()
if __name__ == '__main__':
    # Data = {
    #     "Table_Name": "Portrait_Today_Alarm",
    #     "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Portrait\Portrait_Today_Alarm.db",
    # }
    # __Service_Table = Service_Table.Service_Table("sqlite_backall")
    # PJ(__Service_Table(Data))

    Data = {
        "Table_Name": "Dooralarm_Info_List",
        "Table_Path_SQLite": r"D:\Sentinel Foundation\Data\Dooralarm\Dooralarm_Info_List.db",
    }
    __Service_Table = Service_Table.Service_Table("sqlite_backall")
    PJ(__Service_Table(Data))