# -*- coding: utf-8 -*-
import time,os,sys,cv2
from PySide6 import QtCore, QtGui, QtWidgets
import qtawesome as qta
from subprocess import Popen
from PySide6.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget,QPushButton,QSlider,QHBoxLayout,QFileDialog
from PySide6.QtGui import QImage, QPixmap,QIcon,QMouseEvent
from PySide6.QtCore import QTimer, Qt,QUrl,QPropertyAnimation, QEasingCurve
from PySide6.QtCore import QThread, Signal, Slot
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from PySide6.QtMultimediaWidgets import QGraphicsVideoItem
from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, <PERSON><PERSON>ush<PERSON>utton,
                               QGraphicsView, QGraphicsS<PERSON>, QGraphicsProxyWidget,
                               QLabel, QSlider, QHBoxLayout)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QPoint, QUrl,Signal
from PySide6.QtGui import QColor, QPalette, QIcon,QBrush
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

import qtawesome
sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Component")
import Component_Common



class Component_LyricsPlayer(QtWidgets.QWidget):
    Signal_Result = Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)

    # 示例歌词
        self.lyrics = [
            "—— 开始 ——",
            "00：00   一行行文字在这里",
            "00：03   点击任意一行",
            "00：06   控制台会打印行号",
            "00：09   对话字幕的例子",
            "00：12   开始三人对话",
            "00：15   甲：大家好！",
            "00：18   乙：你好，甲！",
            "00：21   丙：今天聊点什么？",
            "00：24   甲：先自我介绍吧",
            "00：27   乙：我是乙，喜欢编程",
            "00：30   丙：我叫丙，爱做设计",
            "00：33   甲：我负责产品",
            "00：36   乙：那我们互补",
            "00：39   丙：一起做个小项目？",
            "00：42   甲：好主意！",
            "00：45   乙：用什么技术栈？",
            "00：48   丙：Python + PySide6",
            "00：51   甲：界面我来画原型",
            "00：54   乙：后端逻辑交给我",
            "00：57   丙：UI 设计我包了",
            "01：00   甲：需求先定一下",
            "01：03   乙：做个歌词播放器？",
            "01：06   丙：酷！带卡拉OK效果",
            "01：09   甲：再加点击行号高亮",
            "01：12   乙：行，我处理事件",
            "01：15   丙：我来整样式表",
            "01：18   甲：时间轴同步谁做？",
            "01：21   乙：定时器我搞定",
            "01：24   丙：颜色用半透明青色",
            "01：27   甲：用户能关闭滚动？",
            "01：30   乙：加个开关就行",
            "01：33   丙：图标我来画",
            "01：36   甲：今晚出第一版",
            "01：39   乙：冲！",
            "01：42   丙：咖啡已备好",
            "01：45   甲：先跑通列表展示",
            "01：48   乙：事件监听写完",
            "01：51   丙：样式表搞定",
            "01：54   甲：颜色透明度OK",
            "01：57   乙：滚动条已隐藏",
            "02：00   丙：字体用思源黑体",
            "02：03   甲：行高再调大点",
            "02：06   乙：Item高度已设36",
            "02：09   丙：选中背景透明",
            "02：12   甲：测试点击行号",
            "02：15   乙：输出正确！",
            "02：18   丙：换首歌试试",
            "02：21   甲：LRC解析放二期",
            "02：24   乙：先硬编码100行",
            "02：27   丙：行数已够",
            "02：30   甲：再加滚动到行中",
            "02：33   乙：scrollToItem搞定",
            "02：36   丙：进度条要不要？",
            "02：39   甲：二期一起上",
            "02：42   乙：先打包可执行",
            "02：45   丙：PyInstaller走起",
            "02：48   甲：图标也打包进去",
            "02：51   乙：--icon参数已加",
            "02：54   丙：文件瘦身用UPX",
            "02：57   甲：README写好了",
            "03：00   乙：GIF演示也录了",
            "03：03   丙：GitHub仓库公开",
            "03：06   甲：Issue模板配好",
            "03：09   乙：Actions自动构建",
            "03：12   丙：Linux/Mac也测了",
            "03：15   甲：用户反馈来啦",
            "03：18   乙：希望可调速",
            "03：21   丙：滑条控件安排",
            "03：24   甲：下一版给记忆",
            "03：27   乙：配置文件用JSON",
            "03：30   丙：主题日夜切换",
            "03：33   甲：API文档补全",
            "03：36   乙：类型提示已加",
            "03：39   丙：代码格式化black",
            "03：42   甲：覆盖率上90%",
            "03：45   乙：pytest-cov跑过",
            "03：48   丙：徽章贴在README",
            "03：51   甲：准备发v1.0",
            "03：54   乙：Tag已打",
            "03：57   丙：Release包上传",
            "04：00   甲：推文也发了",
            "04：03   乙：Star破百！",
            "04：06   丙：感谢开源社区",
            "04：09   甲：继续迭代",
            "04：12   乙：功能永远不嫌多",
            "04：15   丙：保持简洁",
            "04：18   甲：今天就到这",
            "04：21   乙：收工！",
            "04：24   丙：明晚见",
            "04：27   甲：88~",
            "04：30   乙：ZZZ",
            "04：33   丙：晚安",
            "04：36   （屏幕渐暗）",
            "04：39   ……",
            "04：42   ……",
            "04：45   ……",
            "04：48   ……",
            "04：51   ……",
            "04：54   ……",
            "—— 结束 ——"
        ]

        self.list_widget = QtWidgets.QListWidget()
        self.list_widget.setStyleSheet("""
                                        QListWidget {
                                            border: none;
                                            background: transparent;
                                        }
                                        QListWidget::item:selected {
                                            background: transparent;
                                        }
                                        QListWidget QScrollBar:vertical,
                                        QListWidget QScrollBar:horizontal {
                                            width: 0px;
                                            height: 0px;
                                        }
                                    """)
        self.list_widget.addItems(self.lyrics)
        self.list_widget.itemClicked.connect(self.on_item_clicked)

        # 布局
        # central = QWidget()
        layout = QVBoxLayout(self)
        layout.addWidget(self.list_widget)
        # self.setCentralWidget(central)

        # 模拟歌词高亮滚动（可选）
        self.current_line = 0
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.scroll_lyrics)
        self.timer.start(1000)  # 每 1 秒滚动一次


    def on_item_clicked(self, item: QtWidgets.QListWidgetItem):
        row = self.list_widget.row(item)
        print(f"点击了第 {row} 行：{item.text()}")


    def scroll_lyrics(self):
        """简单地把当前行设为黄色背景，上一行恢复白色"""

        for i in range(self.list_widget.count()):
            self.list_widget.item(i).setForeground(QBrush(QColor(255,255, 255, 255)))
            self.list_widget.item(i).setBackground(QColor("transparent"))

        if self.current_line < self.list_widget.count():
            self.list_widget.item(self.current_line).setBackground(QBrush(QColor(0, 255, 255, 76)))
            self.list_widget.item(self.current_line).setForeground(QBrush(QColor(255, 255, 0)))
            self.current_line += 1
        else:
            self.current_line = 0  # 循环播放







if __name__ == "__main__":
    app = QApplication(sys.argv)
    Window = Component_Player_Multimedia({"Video_Source":"rtsp://admin:csc888888!@192.168.123.57:554/Streaming/Channels/101"})
    Window.show()
    sys.exit(app.exec())