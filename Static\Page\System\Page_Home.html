<!DOCTYPE html>
<html lang="en">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!-- Vector CSS -->
	<link href="/static/CSS/vectormap/jquery-jvectormap-2.0.2.css" rel="stylesheet" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
	<style>
		#scroll-container {
			height: 400px;
			overflow: hidden;
			position: relative;
		}

		#scroll-content {
			position: absolute;
			top: 0;
		}

		.item {
			width:100%;
			white-space:nowrap;
			overflow:hidden;
			text-overflow:ellipsis;
			box-sizing:border-box;
			padding-right:10px;
			margin-bottom: 20px;
		}

	</style>
</head>
<body class="bg-theme bg-theme1">
	<!-- wrapper -->
	<div class="wrapper">
        <!--sidebar-wrapper-->
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<!--page-wrapper-->
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
                    <!-- 信息总览 -->
					<div class="row">
						<div class="col-12 col-lg-3">
							<div class="card radius-15">
								<div class="card-body">
									<div class="d-flex align-items-center">
										<div>
											<h2 class="mb-0 text-white" id="Today_Sentiment_Count">0 <i class='bx bxs-up-arrow-alt font-14 text-white'></i> </h2>
										</div>
										<div class="ml-auto font-35 text-white">
                                            <svg t="1735463538684" class="icon" onclick="HanderToDetails('All')" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19166" width="35" height="35"><path d="M239.296 533.312h545.408a32.384 32.384 0 0 0 32.768-32c0-17.664-14.72-32-32.768-32H239.36a32.384 32.384 0 0 0-32.768 32c0 17.728 14.72 32 32.768 32z m0-234.624h545.408a32.384 32.384 0 0 0 32.768-32c0-17.664-14.72-32-32.768-32H239.36a32.384 32.384 0 0 0-32.768 32c0 17.664 14.72 32 32.768 32zM479.296 704h-240a32.384 32.384 0 0 0-32.768 32c0 17.664 14.72 32 32.768 32h240a32.384 32.384 0 0 0 32.704-32c0-17.664-14.656-32-32.704-32z m240-64c-66.304 0-120 52.48-120 117.312s53.76 117.376 120 117.376c66.24 0 120-52.544 120-117.376 0-64.768-53.76-117.312-120-117.312z m0 170.688a53.952 53.952 0 0 1-54.592-53.376c0-29.44 24.448-53.312 54.592-53.312 30.08 0 54.528 23.872 54.528 53.312 0 29.44-24.448 53.376-54.528 53.376zM882.88 0H141.12C80.832 0 32 47.744 32 106.688v810.624C32 976.192 80.832 1024 141.12 1024h741.76c60.288 0 109.12-47.744 109.12-106.688V106.688C992 47.808 943.168 0 882.88 0z m43.648 917.312a43.136 43.136 0 0 1-43.648 42.688H141.12a43.136 43.136 0 0 1-43.648-42.688V106.688c0-23.552 19.52-42.688 43.648-42.688h741.76c24.128 0 43.648 19.136 43.648 42.688v810.624z" fill="#1296db" p-id="19167"></path></svg>
										</div>
									</div>
									<div class="d-flex align-items-center">
										<div>
											<p class="mb-0 text-white">信息总量</p>
										</div>
										<div class="ml-auto font-14 text-white" id="Today_Percent_All">+0%</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-12 col-lg-3">
							<div class="card radius-15">
								<div class="card-body">
									<div class="d-flex align-items-center">
										<div>
											<h2 class="mb-0 text-white" id="Today_Sentiment_Emotion_Positive">0 <i class='bx bxs-down-arrow-alt font-14 text-white'></i> </h2>
										</div>
										<div class="ml-auto font-35 text-white">
                                            <svg t="1735463331936" class="icon" onclick="HanderToDetails('正面')" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12658" width="42" height="42"><path d="M0 512A512.1536 512.1536 0 0 1 512 0a512.1536 512.1536 0 0 1 512 512 512.1536 512.1536 0 0 1-512 512A512.1536 512.1536 0 0 1 0 512z m972.8 0c0-60.928-11.776-119.808-35.072-176.384-23.552-56.32-56.832-106.24-99.84-149.504a464.1792 464.1792 0 0 0-149.504-99.84C631.808 62.976 572.928 51.2 512 51.2s-119.808 11.776-176.384 35.072c-56.32 23.552-106.24 56.832-149.504 99.84a464.1792 464.1792 0 0 0-99.84 149.504C62.976 392.192 51.2 451.072 51.2 512s11.776 119.808 35.072 176.384c23.552 56.32 56.832 106.24 99.84 149.504 43.264 43.008 93.184 76.288 149.504 99.84C392.192 961.024 451.072 972.8 512 972.8s119.808-11.776 176.384-35.072c56.32-23.552 106.24-56.832 149.504-99.84 43.008-43.264 76.288-93.184 99.84-149.504C961.024 631.808 972.8 572.928 972.8 512z m-650.752-214.528c0.512-1.536 1.28-3.072 2.304-4.608l3.072-3.84 3.84-3.072c1.536-1.024 3.072-1.792 4.608-2.304 1.536-0.768 3.072-1.28 4.608-1.536 1.792-0.256 3.328-0.512 5.12-0.512h358.4c1.792 0 3.328 0.256 5.12 0.512 1.536 0.256 3.072 0.768 4.608 1.536 1.536 0.512 3.072 1.28 4.608 2.304l3.84 3.072 3.072 3.84c1.024 1.536 1.792 3.072 2.304 4.608 0.768 1.536 1.28 3.072 1.536 4.608 0.256 1.792 0.512 3.328 0.512 5.12s-0.256 3.328-0.512 5.12c-0.256 1.536-0.768 3.072-1.536 4.608-0.512 1.536-1.28 3.072-2.304 4.608l-3.072 3.84-3.84 3.072c-1.536 1.024-3.072 1.792-4.608 2.304-1.536 0.768-3.072 1.28-4.608 1.536-1.792 0.256-3.328 0.512-5.12 0.512h-153.6v128h126.208c3.328 0 6.656 0.768 9.728 2.048 3.328 1.28 5.888 3.072 8.448 5.376 2.304 2.56 4.096 5.12 5.376 8.448 1.28 3.072 2.048 6.4 2.048 9.728 0 1.792-0.256 3.328-0.512 4.864-0.256 1.792-0.768 3.328-1.536 4.864-0.512 1.536-1.28 3.072-2.304 4.608-0.768 1.28-2.048 2.56-3.072 3.84l-3.84 3.072c-1.536 1.024-3.072 1.792-4.608 2.304-1.536 0.768-3.072 1.28-4.608 1.536-1.792 0.256-3.328 0.512-5.12 0.512H550.4v179.2h153.6c1.792 0 3.328 0.256 5.12 0.512 1.536 0.256 3.072 0.768 4.608 1.536 1.536 0.512 3.072 1.28 4.608 2.304 1.28 0.768 2.56 2.048 3.84 3.072l3.072 3.84c1.024 1.536 1.792 3.072 2.304 4.608 0.768 1.536 1.28 3.072 1.536 4.608 0.256 1.792 0.512 3.328 0.512 5.12 0 3.328-0.768 6.656-2.048 9.728-1.28 3.328-3.072 5.888-5.376 8.448-2.56 2.304-5.12 4.096-8.448 5.376-3.072 1.28-6.4 2.048-9.728 2.048h-384c-1.792 0-3.328-0.256-5.12-0.512a16.2816 16.2816 0 0 1-4.608-1.536 19.3792 19.3792 0 0 1-4.608-2.304c-1.28-0.768-2.56-2.048-3.84-3.072l-3.072-3.84a19.3792 19.3792 0 0 1-2.304-4.608 16.2816 16.2816 0 0 1-1.536-4.608c-0.256-1.792-0.512-3.328-0.512-5.12 0-3.328 0.768-6.656 2.048-9.728s3.072-5.888 5.376-8.448c2.56-2.304 5.12-4.096 8.448-5.376 3.072-1.28 6.4-2.048 9.728-2.048h51.2V435.2c0-3.328 0.768-6.656 2.048-9.728 1.28-3.328 3.072-5.888 5.376-8.448 2.56-2.304 5.12-4.096 8.448-5.376 3.072-1.28 6.4-2.048 9.728-2.048s6.656 0.768 9.728 2.048c3.328 1.28 5.888 3.072 8.448 5.376 2.304 2.56 4.096 5.12 5.376 8.448 1.28 3.072 2.048 6.4 2.048 9.728v256h76.8v-195.584c-1.024-3.072-1.792-6.144-1.792-9.216 0-1.536 0.256-3.328 0.512-4.864s0.512-3.072 1.28-4.352V332.8h-153.6c-1.792 0-3.328-0.256-5.12-0.512a16.2816 16.2816 0 0 1-4.608-1.536 19.3792 19.3792 0 0 1-4.608-2.304l-3.84-3.072-3.072-3.84a19.3792 19.3792 0 0 1-2.304-4.608 16.2816 16.2816 0 0 1-1.536-4.608c-0.256-1.792-0.512-3.328-0.512-5.12s0.256-3.328 0.512-5.12c0.256-1.536 0.768-3.072 1.536-4.608z" fill="#1296db" p-id="12659"></path></svg>
										</div>
									</div>
									<div class="d-flex align-items-center">
										<div>
											<p class="mb-0 text-white">正面信息</p>
										</div>
										<div class="ml-auto font-14 text-white" id="Today_Percent_Positive">+0%</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-12 col-lg-3">
							<div class="card radius-15">
								<div class="card-body">
									<div class="d-flex align-items-center">
										<div>
											<h2 class="mb-0 text-white" id="Today_Sentiment_Emotion_Neutral">0 <i class='bx bxs-up-arrow-alt font-14 text-white'></i> </h2>
										</div>
										<div class="ml-auto font-35 text-white">
                                            <svg t="1735462848469" class="icon" onclick="HanderToDetails('中性')" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11371" width="42" height="42"><path d="M691.2 384h-153.6v-102.4c0-7.68-2.56-12.8-7.68-17.92-5.12-5.12-10.24-7.68-17.92-7.68s-12.8 2.56-17.92 7.68c-5.12 5.12-7.68 10.24-7.68 17.92v102.4h-153.6c-12.8 0-25.6 12.8-25.6 25.6v179.2c0 12.8 12.8 25.6 25.6 25.6h153.6v128c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-128h153.6c12.8 0 25.6-12.8 25.6-25.6v-179.2c0-12.8-12.8-25.6-25.6-25.6z m-204.8 179.2h-102.4c-12.8 0-25.6-12.8-25.6-25.6v-76.8c0-12.8 12.8-25.6 25.6-25.6h102.4v128z m179.2-25.6c0 12.8-12.8 25.6-25.6 25.6h-102.4v-128h102.4c12.8 0 25.6 12.8 25.6 25.6v76.8z" fill="#FFA245" p-id="11372"></path><path d="M872.96 151.04C778.24 53.76 647.68 0 512 0S245.76 53.76 151.04 151.04 0 376.32 0 512s53.76 266.24 151.04 360.96S376.32 1024 512 1024s266.24-53.76 360.96-151.04S1024 647.68 1024 512 970.24 245.76 872.96 151.04z m-35.84 686.08c-43.52 43.52-92.16 76.8-148.48 99.84-56.32 23.04-115.2 35.84-176.64 35.84s-120.32-12.8-176.64-35.84c-56.32-23.04-104.96-56.32-148.48-99.84-43.52-43.52-76.8-92.16-99.84-148.48C64 632.32 51.2 573.44 51.2 512s12.8-120.32 35.84-176.64c23.04-56.32 56.32-104.96 99.84-148.48 43.52-43.52 92.16-76.8 148.48-99.84C391.68 64 450.56 51.2 512 51.2s120.32 12.8 176.64 35.84c56.32 23.04 104.96 56.32 148.48 99.84 43.52 43.52 76.8 92.16 99.84 148.48 23.04 56.32 35.84 115.2 35.84 176.64s-12.8 120.32-35.84 176.64c-23.04 56.32-56.32 104.96-99.84 148.48z" fill="#FFA245" p-id="11373"></path></svg>
										</div>
									</div>
									<div class="d-flex align-items-center">
										<div>
											<p class="mb-0 text-white">中性信息</p>
										</div>
										<div class="ml-auto font-14 text-white" id="Today_Percent_Neutral">-0%</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-12 col-lg-3">
							<div class="card radius-15">
								<div class="card-body">
									<div class="d-flex align-items-center">
										<div>
											<h2 class="mb-0 text-white" id="Today_Sentiment_Emotion_Negative">0 <i class='bx bxs-up-arrow-alt font-14 text-white'></i> </h2>
										</div>
										<div class="ml-auto font-35 text-white">
                                            <svg t="1735463380301" class="icon" onclick="HanderToDetails('负面')" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14284" width="42" height="42"><path d="M872.96 151.04C778.24 53.76 647.68 0 512 0S245.76 53.76 151.04 151.04 0 376.32 0 512s53.76 266.24 151.04 360.96S376.32 1024 512 1024s266.24-53.76 360.96-151.04S1024 647.68 1024 512 970.24 245.76 872.96 151.04z m-35.84 686.08c-43.52 43.52-92.16 76.8-148.48 99.84-56.32 23.04-115.2 35.84-176.64 35.84s-120.32-12.8-176.64-35.84c-56.32-23.04-104.96-56.32-148.48-99.84-43.52-43.52-76.8-92.16-99.84-148.48C64 632.32 51.2 573.44 51.2 512s12.8-120.32 35.84-176.64c23.04-56.32 56.32-104.96 99.84-148.48 43.52-43.52 92.16-76.8 148.48-99.84C391.68 64 450.56 51.2 512 51.2s120.32 12.8 176.64 35.84c56.32 23.04 104.96 56.32 148.48 99.84 43.52 43.52 76.8 92.16 99.84 148.48 23.04 56.32 35.84 115.2 35.84 176.64s-12.8 120.32-35.84 176.64c-23.04 56.32-56.32 104.96-99.84 148.48z" fill="#d81e06" p-id="14285"></path><path d="M706.56 716.8H663.04c-23.04-2.56-40.96-7.68-56.32-17.92-43.52-25.6-64-84.48-66.56-168.96 0-7.68-7.68-15.36-17.92-15.36h-12.8c-12.8 0-23.04 10.24-23.04 23.04-2.56 81.92-25.6 138.24-66.56 163.84-15.36 10.24-33.28 15.36-56.32 17.92h-53.76L307.2 768h28.16c12.8 0 23.04 0 33.28-2.56 30.72-2.56 56.32-12.8 79.36-25.6 28.16-17.92 51.2-46.08 66.56-79.36 15.36 35.84 38.4 61.44 66.56 79.36 23.04 15.36 48.64 23.04 79.36 25.6 10.24 0 23.04 2.56 33.28 2.56h17.92c2.56 0 5.12 0 7.68-2.56 2.56-2.56 2.56-5.12 2.56-7.68l-5.12-33.28c0-5.12-5.12-7.68-10.24-7.68z" fill="#d81e06" p-id="14286"></path><path d="M340.48 458.24c7.68-2.56 12.8-2.56 20.48-7.68 0 2.56-2.56 5.12-2.56 7.68v153.6c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-102.4c0-12.8 12.8-25.6 25.6-25.6h153.6c12.8 0 25.6 12.8 25.6 25.6v102.4c0 7.68 2.56 12.8 7.68 17.92 5.12 5.12 10.24 7.68 17.92 7.68s12.8-2.56 17.92-7.68c5.12-5.12 7.68-10.24 7.68-17.92v-153.6c0-12.8-12.8-25.6-25.6-25.6h-33.28c10.24-12.8 17.92-28.16 23.04-43.52 5.12-12.8 7.68-25.6 10.24-40.96 0-7.68 2.56-12.8 2.56-17.92v-12.8c0-2.56 0-7.68-5.12-7.68-2.56-2.56-7.68-2.56-10.24-2.56h-148.48l7.68-30.72c0-5.12-2.56-10.24-5.12-12.8-5.12-7.68-7.68-7.68-12.8-7.68H460.8c-12.8 0-23.04 10.24-25.6 23.04 2.56-7.68 0 10.24-7.68 28.16-7.68 17.92-17.92 35.84-30.72 51.2-12.8 15.36-25.6 28.16-38.4 38.4-7.68 5.12-12.8 7.68-17.92 10.24-7.68 2.56-15.36 10.24-15.36 20.48v15.36c0 5.12 2.56 10.24 5.12 12.8 0 2.56 5.12 2.56 10.24 2.56z m94.72-69.12c7.68-10.24 15.36-20.48 20.48-33.28h122.88c2.56 0 2.56 0 5.12 2.56v5.12c0 2.56-2.56 5.12-2.56 7.68-7.68 25.6-23.04 43.52-40.96 56.32-2.56 2.56-5.12 2.56-5.12 5.12h-145.92c15.36-10.24 30.72-25.6 46.08-43.52z" fill="#d81e06" p-id="14287"></path></svg>
										</div>
									</div>
									<div class="d-flex align-items-center">
										<div>
											<p class="mb-0 text-white">负面信息</p>
										</div>
										<div class="ml-auto font-14 text-white" id="Today_Percent_Negative">+0%</div>
									</div>
								</div>
							</div>
						</div>
					</div>
                    <!-- 数据汇总曲线图 -->
					<div class="row">
						<div class="col-12 col-lg-8 d-flex align-items-stretch">
							<div class="card radius-15 w-100">
								<div class="card-header border-bottom-0">
									<div class="d-lg-flex align-items-center">
										<div>
											<h5 class="mb-2 mb-lg-0">今日舆情走势</h5>
										</div>
										<!-- <div class="ml-lg-auto mb-2 mb-lg-0">
											<div class="btn-group-round">
												<div class="btn-group">
													<button type="button" class="btn btn-light">Daiiy</button>
													<button type="button" class="btn btn-light">Weekly</button>
													<button type="button" class="btn btn-light">Monthly</button>
												</div>
											</div>
										</div> -->
									</div>
								</div>
								<div class="card-body">
									<div id="chart1"></div>
								</div>
							</div>
						</div>
						<div class="col-12 col-lg-4 d-flex align-items-stretch">
							<div class="card radius-15 w-100">
								<div class="card-header border-bottom-0">
									<div class="d-lg-flex align-items-center">
										<div>
											<h5 class="mb-2 mb-lg-0">最热文章</h5>
										</div>
									</div>
								</div>
								<div class="card-body">
									<div id="scroll-container">
										<div id="scroll-content">
											
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					
                    <!-- 媒体类型  情感分类 -->
                    <div class="card-deck">
						<div class="card radius-15">
							<div class="card-body">
								<div class="d-flex align-items-center">
									<div>
										<h5 class="mb-0">媒体类型</h5>
									</div>
								</div>
								<div id="Today_Sentiment_Platform_Type_Bar"></div>
							</div>
						</div>
						<div class="card radius-15">
							<div class="card-body">
								<div class="d-flex align-items-center">
									<div>
										<h5 class="mb-0">情感分类</h5>
									</div>
								</div>
								<div id="Today_Sentiment_Emotion_Bar"></div>
							</div>
						</div>
						<div class="card radius-15">
							<div class="card-body">
								<div class="d-flex align-items-center">
									<div>
										<h5 class="mb-0">中标词分析</h5>
									</div>
								</div>
								<div id="Today_Sentiment_Keyword_WordCloud" style="height: 400px;"></div>
								<!-- <div id="Today_Sentiment_Keyword_WordCloud" style="height: 100%;"></div> -->
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--end page-wrapper-->
		<!--start overlay-->
		<div class="overlay toggle-btn-mobile"></div>
		<!--end overlay-->
		<!--Start Back To Top Button-->
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<!--End Back To Top Button-->
		<!--footer -->
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
		<!-- end footer -->
	</div>
	<!--start switcher-->
	<!-- <div class="switcher-wrapper">
		<div class="switcher-btn"> <i class='bx bx-cog bx-spin'></i>
		</div>
		<div class="switcher-body">
			<h5 class="mb-0 text-uppercase">Theme Customizer</h5>
			<hr/>
			<p class="mb-0">Gaussion Texture</p>
			  <hr>
			  
			  <ul class="switcher">
				<li id="theme1"></li>
				<li id="theme2"></li>
				<li id="theme3"></li>
				<li id="theme4"></li>
				<li id="theme5"></li>
				<li id="theme6"></li>
			  </ul>
               <hr>
			  <p class="mb-0">Gradient Background</p>
			  <hr>
			  
			  <ul class="switcher">
				<li id="theme7"></li>
				<li id="theme8"></li>
				<li id="theme9"></li>
				<li id="theme10"></li>
				<li id="theme11"></li>
				<li id="theme12"></li>
			  </ul>
		</div>
	</div> -->
	<!--end switcher-->
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
	<!-- Vector map JavaScript -->
    <script src="/static/JavaScript/vectormap/jquery-jvectormap-2.0.2.min.js"></script>
	<script src="/static/JavaScript/vectormap/jquery-jvectormap-world-mill-en.js"></script>
	<script src="/static/JavaScript/vectormap/jquery-jvectormap-in-mill.js"></script>
	<script src="/static/JavaScript/vectormap/jquery-jvectormap-us-aea-en.js"></script>
	<script src="/static/JavaScript/vectormap/jquery-jvectormap-uk-mill-en.js"></script>
	<script src="/static/JavaScript/vectormap/jquery-jvectormap-au-mill.js"></script>
	<script src="/static/JavaScript/apexcharts-bundle/js/apexcharts.min.js"></script>
	<script src="/static/JavaScript/js2wordcloud.js"></script>
	<!-- word cloud -->
	<!-- <script type="text/javascript" src="/static/JavaScript/App/wordcloud2.js"></script> -->
	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <script src="/static/Page/System/Page_Home.js"></script>
	<!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
	<script>
        // var User_Token = localStorage.getItem('User_Token')
		var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Sentiment_Info();
			startScroll(); // 开始滚动
		};

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>