
import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Node_Socket,ModelControl_Rumor_Keywords} from "./Node_Model_Control_Rumor_Keywords";

export class Node_Model_Component_Rumor_Keywords extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| ModelControl_Rumor_Keywords}> 
  {
    width  = 388;
    height = 428;
    constructor(Label: string,) {
      super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const ConentControl = new ModelControl_Rumor_Keywords(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
       