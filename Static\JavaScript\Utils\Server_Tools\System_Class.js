
// alert("function System_Class");
// D:\csc_server\service\System\APP_Server\Static\JavaScript\App\.js
// ----------------- 数据类型
function ERM_type(para) {    return Object.prototype.toString.call(para)}


class test_play{
    constructor(type,data){
        this.type = type
        this.data = data
    }
    run(){
        console.log("run",this.type,this.data);
        eval(`this.play_${this.type}()`);
    }


    

    play_sex(){
        console.log("我会玩sex",this.data);
    }

    play_ball(){
        console.log("我会玩ball",this.data);
    }

  






}

// // ----------------- 数组去重
// function unique1(arr) {    return [...new Set(arr)]}
// function unique2(arr) {    var obj = {};    return arr.filter(ele => {        if (!obj[ele]) {            obj[ele] = true;            return true;        }    })}
// // function unique3(arr) {    var result = [];    arr.forEach(ele => {        if (result.indexOf(ele) == -1) {            result.push(ele)        }    })    return result;}}}}



// // ----------------- 去除连续的字符串
// String.prototype.unique = function () {    var obj = {},        str = '',        len = this.length;    for (var i = 0; i < len; i++) {        if (!obj[this[i]]) {            str += this[i];            obj[this[i]] = true;        }    }    return str;}
// // ###### //去除连续的字符串 function uniq(str) {    return str.replace(/(\w)\1+/g, '$1')}

// // ----------------- 深克隆
// //深克隆（深克隆不考虑函数）function deepClone(obj, result) {    var result = result || {};    for (var prop in obj) {        if (obj.hasOwnProperty(prop)) {            if (typeof obj[prop] == 'object' && obj[prop] !== null) {                // 引用值(obj/array)且不为null                if (Object.prototype.toString.call(obj[prop]) == '[object Object]') {                    // 对象                    result[prop] = {};                } else {                    // 数组                    result[prop] = [];                }                deepClone(obj[prop], result[prop])    } else {        // 原始值或func        result[prop] = obj[prop]    }}}return result;
// // }
// // 深浅克隆是针对引用值function deepClone(target) {    if (typeof (target) !== 'object') {        return target;    }    var result;    if (Object.prototype.toString.call(target) == '[object Array]') {        // 数组        result = []    } else {        // 对象        result = {};    }    for (var prop in target) {        if (target.hasOwnProperty(prop)) {            result[prop] = deepClone(target[prop])        }    }    return result;}// 无法复制函数var o1 = jsON.parse(jsON.stringify(obj1));

// // ----------------- 改变原数组Array
// // 改变原数组Array.prototype.myReverse = function () {    var len = this.length;    for (var i = 0; i < len; i++) {        var temp = this[i];        this[i] = this[len - 1 - i];        this[len - 1 - i] = temp;    }    return this;}




// // ----------------- 圣杯模式的继承
// function inherit(Target, Origin) {    function F() {};    F.prototype = Origin.prototype;    Target.prototype = new F();    Target.prototype.constructor = Target;}    
// // 最终的原型指向    Target.prop.uber = Origin.prototype;}


// // ----------------- 找出字符串中第一次只出现一次的字母
// String.prototype.firstAppear = function () {    var obj = {},        len = this.length;    for (var i = 0; i < len; i++) {        if (obj[this[i]]) {            obj[this[i]]++;        } else {            obj[this[i]] = 1;        }    }    for (var prop in obj) {       if (obj[prop] == 1) {         return prop;       }    }}


// // ----------------- 找元素的第n级父元素

// function parents(ele, n) {    while (ele && n) {        ele = ele.parentElement ? ele.parentElement : ele.parentNode;        n--;    }    return ele;}

// // ----------------- 返回元素的第n个兄弟节点


// function retSibling(e, n) {    while (e && n) {        if (n > 0) {            if (e.nextElementSibling) {                e = e.nextElementSibling;            } else {                for (e = e.nextSibling; e && e.nodeType !== 1; e = e.nextSibling);            }            n--;        } else {            if (e.previousElementSibling) {                e = e.previousElementSibling;            } else {                for (e = e.previousElementSibling; e && e.nodeType !== 1; e = e.previousElementSibling);            }            n++;        }    }    return e;}






















