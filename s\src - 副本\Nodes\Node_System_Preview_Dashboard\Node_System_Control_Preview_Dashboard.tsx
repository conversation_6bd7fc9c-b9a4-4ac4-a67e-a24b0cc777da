
import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Input,Layout, Col, Row,Carousel,Card, Statistic,} from "antd";
import React from 'react';
import "../Nodes.module.css";
import {CloudServerOutlined,CheckOutlined,AuditOutlined,ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,} from '@ant-design/icons';


export const Node_Socket = new ClassicPreset.Socket("socket");


export class SystemControl_Preview_Dashboard extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();
  
      
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
      this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
      this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
      this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
      this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

  export function Node_System_Preview_Dashboard(props: { data: SystemControl_Preview_Dashboard }) {
    // return <Progress type="circle" percencolort={props.data.percent} />;

    const Title_1=<span style={{color:"white"}} >今日数据</span> 
    const Title_2=<span style={{color:"white"}} >待处理</span>
    const Title_3=<span style={{color:"white"}} >已分析</span>
    const Title_4=<span style={{color:"white"}} >突出谣言</span>
 

    const Title_5=<span style={{color:"white"}} >错误</span>
    const Title_6=<span style={{color:"white"}} >成功</span>
    const Title_7=<span style={{color:"white"}} >Token</span>
    const Title_8=<span style={{color:"white"}} >大模型</span>




    const Layout_1 = <Layout style={{background:"transparent"}}>
    <Row gutter={16}>
        <Col span={12}>
          <Card variant="borderless" style={{background:"rgba(0,0,0,0.3)"}}>
        
            <Statistic
                 title={Title_1}
              value={1128}
              // precision={2}
              valueStyle={{ color: 'rgba(237, 236, 243, 0.8)' }}
              prefix={<CloudServerOutlined />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
            
            title={Title_2}
              value={45}
              // precision={2}
              valueStyle={{ color: "blue" }}
              prefix={<CheckOutlined />}
              suffix="条"
             
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={16}  style={{marginTop:8}} >

        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
              title={Title_3}
              value={789}
              precision={0}
              valueStyle={{ color: "green" }}
              prefix={<CheckOutlined />}
              suffix="条"
            
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
            <Statistic
              title={Title_4}
              value={5}
              precision={0}
              valueStyle={{ color: "red" }}
              prefix={<AuditOutlined />}
              suffix="条"
              
            />
          </Card>
        </Col>
      </Row>

  
    </Layout>
    const Layout_2 = <Layout style={{background:"transparent"}}>
    <Row gutter={16}>
       <Col span={12}>
         <Card variant="borderless" style={{background:"rgba(0,0,0,0.3)"}}>
       
           <Statistic
                title={Title_5}
             value={91128}
             // precision={2}
             valueStyle={{ color: 'red' }}
             prefix={<ExclamationCircleOutlined />}
             suffix="条"
           />
         </Card>
       </Col>
       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
           
           title={Title_6}
             value={1232345}
             // precision={2}
             valueStyle={{ color: "purple" }}
             prefix={<CheckCircleOutlined />}
             suffix="条"
            
           />
         </Card>
       </Col>
     </Row>
     <Row gutter={16}  style={{marginTop:8}} >

       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
             title={Title_7}
             value={22119}
             precision={0}
             valueStyle={{ color: "yellow" }}
             prefix={<FieldStringOutlined />}
             suffix="个"
           
           />
         </Card>
       </Col>
       <Col span={12}>
         <Card variant="borderless"  style={{background:"rgba(0,0,0,0.3)"}}>
           <Statistic
             title={Title_8}
             value={2}
             precision={0}
             valueStyle={{ color: "cyan" }}
             prefix={<ApartmentOutlined />}
             suffix="个"
             
           />
         </Card>
       </Col>
     </Row>

 
 </Layout>



    const contentStyle: React.CSSProperties = {
      height: '100%',
      color: '#fff',
      // lineHeight: '160px',
      marginTop:-10,
      textAlign: 'center',
      background: 'tranparent',
    };


    return (
     <Carousel 
        autoplay
        autoplaySpeed={5000}
        speed={500}
        >
            <div>
             
              <h3 style={contentStyle}> {Layout_1}</h3>
            </div>
            <div>
              <h3 style={contentStyle}>{Layout_2}</h3>
            </div> 
      
          </Carousel>
    
  )
  }

  
