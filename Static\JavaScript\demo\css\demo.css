.demo-inline-wrapper {
    display: inline-block;
    margin-bottom: 30px;
    vertical-align: top;
}

.demo-inline-wrapper:not(:last-child) {
    margin-right: 30px;
}

.demo-inline-wrapper .form-group {
    margin: 0;
}

.btn-demo > .btn,
.btn-demo > .btn-group {
    margin: 0 5px 10px 0;
}

.color-demo {
    color: #fff;
    text-align: center;
    padding: 1.6rem;
    border-radius: 2px;
    margin-bottom: 2.2rem;
    box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.2);
}

.color-demo--light {
    color: #5E5E5E;
}

.color-demo__color {
    text-transform: uppercase;
    font-weight: 500;
}

.color-demo__hex {
    margin: 0.4rem 0;
}

.animation-demo .row {
    margin: 1.9rem -0.76rem 0;
}

.animation-demo .col-sm-6 {
    padding: 0 0.76rem;
    margin-bottom: 0.9rem;
}

.animation-demo .card-body {
    padding-bottom: 1.15rem;
}

.animation-demo .card-body > img {
    position: relative;
    width: 100%;
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.2);
}

.animation-demo .card-body .btn {
    width: 100%;
}

.icons-demo .card-body .zmdi {
    font-size: 1.7rem;
    vertical-align: bottom;
    margin-right: 1rem;
}

.icons-demo .card-body .col-sm-4 {
    padding: 0.8rem 1.1rem;
}

.icons-demo .card-body .col-sm-4:hover {
    background: rgba(0, 0, 0, 0.05);
}

.card-demo {
    vertical-align: top;
    max-width: 450px;
    width: 100%;
    margin: 0 30px 0 0;
    display: inline-block;
}

.dropdown-demo {
    margin: 10px 10px 0 0;
    display: inline-block;
}

.modal-demo {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-demo .modal {
    z-index: 0;
    position: relative;
    display: block;
}

.rating-demo .rating {
    margin: 10px 20px 10px 0;
}