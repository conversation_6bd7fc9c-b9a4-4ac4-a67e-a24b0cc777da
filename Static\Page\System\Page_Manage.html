<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- select2 -->
    <link href="/static/CSS/select2/css/select2.min.css" rel="stylesheet" />
    <link href="/static/CSS/select2/css/select2-bootstrap4.css" rel="stylesheet" />
    <!-- fileupload -->
    <link href="/static/CSS/fancy-file-uploader/fancy_fileupload.css" rel="stylesheet" />
    <!-- table -->
    <link rel="stylesheet" href="/static/CSS/datatable/css/buttons.bootstrap4.min.css" />
    <link rel="stylesheet" href="/static/CSS/datatable/css/dataTables.bootstrap4.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
                    <div class="row">
                        <div class="col-12 col-lg-12">
                            <div class="card shadow-none">
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link active" id="keyword-tab"  data-toggle="tab" href="#keyword" role="tab" aria-controls="keyword" aria-selected="true">
                                                方案设置
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="alarm-tab"  data-toggle="tab" href="#alarm" role="tab" aria-controls="alarm" aria-selected="false">
                                                预警设置
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="account-tab"  data-toggle="tab" href="#account" role="tab" aria-controls="account" aria-selected="false">
                                                账号管理
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="source-tab"  data-toggle="tab" href="#source" role="tab" aria-controls="source" aria-selected="false">
                                                数源管理
                                            </a>
                                        </li>
                                        <li class="nav-item" role="presentation"> 
                                            <a class="nav-link" id="user-tab"  data-toggle="tab" href="#user" role="tab" aria-controls="user" aria-selected="false">
                                                用户管理
                                            </a>
                                        </li>
                                        
                                    </ul>
                                    <div class="tab-content p-3" id="myTabContent">
                                        <div class="tab-pane fade show active" id="keyword" role="tabpanel" aria-labelledby="keyword-tab">
                                            <div class="form-row">
                                                <div class="col-md text-right">
                                                    <button type="button" onclick="Requests_Manage_Keyword_Info()" class="btn btn-light">方案刷新</button>
                                                    <button type="button" class="btn btn-light btn-addkeyword" data-toggle="modal" data-target="#Detail_Keyword_Modal">新增方案</button>
                                                    <button type="button" class="btn btn-primary" id="Submit_Translate_Manage_Keyword">提交设置</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <table
                                                id="Manage_Keyword_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                        <div class="tab-pane fade show" id="alarm" role="tabpanel" aria-labelledby="alarm-tab">
                                            <div class="form-row">
                                                <div class="col-md text-right">
                                                    <button type="button" onclick="Requests_Manage_Alarm_Info()" class="btn btn-light">预警刷新</button>
                                                    <button type="button" class="btn btn-light btn-addalarm" data-toggle="modal" data-target="#Detail_Alarm_Modal">新增预警</button>
                                                    <button type="button" class="btn btn-primary" id="Submit_Translate_Manage_Alarm">提交设置</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <table
                                                id="Manage_Alarm_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                        <div class="tab-pane fade show" id="account" role="tabpanel" aria-labelledby="account-tab">
                                            <div class="form-row">
                                                <div class="col-md text-right">
                                                    <button type="button" onclick="Requests_Manage_Account_Info()" class="btn btn-light">账号刷新</button>
                                                    <button type="button" class="btn btn-light btn-addaccount" data-toggle="modal" data-target="#Detail_Account_Modal">新增账号</button>
                                                    <button type="button" class="btn btn-primary" id="Submit_Translate_Manage_Account">提交设置</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <table
                                                id="Manage_Account_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                        <div class="tab-pane fade show" id="source" role="tabpanel" aria-labelledby="source-tab">
                                            <div class="form-row">
                                                <div class="col-md text-right">
                                                    <button type="button" onclick="Requests_Manage_Source_Info()" class="btn btn-light">数源刷新</button>
                                                    <button type="button" class="btn btn-light btn-addsource" data-toggle="modal" data-target="#Detail_Source_Modal">新增数源</button>
                                                    <button type="button" class="btn btn-primary" id="Submit_Translate_Manage_Source">提交设置</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <table
                                                id="Manage_Source_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                        <div class="tab-pane fade show" id="user" role="tabpanel" aria-labelledby="user-tab">
                                            <div class="form-row">
                                                <div class="col-md text-right">
                                                    <button type="button" onclick="Requests_Manage_User_Info()" class="btn btn-light">用户刷新</button>
                                                    <!-- <button type="button" class="btn btn-light btn-adduser">新增用户</button> -->
                                                    <button type="button" class="btn btn-primary" id="Submit_Translate_Manage_User">提交设置</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <table
                                                id="Manage_User_Table"
                                                class="table table-striped table-bordered"
                                            ></table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
				</div>
			</div>
		</div>
		<div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
	</div>
    <div class="modal fade" id="Detail_Keyword_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticKeywordBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticKeywordBackdropLabel">方案编辑&新增</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            方案名称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="请输入方案名称" id="Keyword_USER_NAME">
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            所属分类
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <select class="single-select" id="Keyword_USER_TYPE">
                                <option value="时事新闻">时事新闻</option>
                                <option value="聚焦热点">聚焦热点</option>
                                <option value="涉政信息">涉政信息</option>
                                <option value="涉邪消息">涉邪消息</option>
                                <option value="反动言论">反动言论</option>
                                <option value="重点人物">重点人物</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            关键词
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <textarea class="form-control" placeholder="请输入关键词，多个关键词使用中英文逗号','隔开。" rows="3" cols="3" id="Keyword_KEYWORD_INFO"></textarea>
                    </div>
                    <div class="form-group">
                        <label>特征词</label>
                        <textarea class="form-control" placeholder="请输入特征词，多个特征词使用中英文逗号','隔开，特征词与关键词是与关系，特征词之间是或关系。" rows="3" cols="3" id="Keyword_FEATUREWORD_INFO"></textarea>
                    </div>
                    <div class="form-group">
                        <label>停用词</label>
                        <textarea class="form-control" placeholder="请输入停用词，多个停用词使用中英文逗号','隔开，停用词与关键词是与关系，停用词之间是或关系。" rows="3" cols="3" id="Keyword_STOPWORD_INFO"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_Keyword_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>

    <div class="modal fade" id="Detail_Alarm_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticAlarmBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticAlarmBackdropLabel">预警编辑&新增</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            预警名称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="" id="Editor_ALARM_NAME">
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            预警方式
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <!-- <select class="single-select" id="Editor_ALARM_TYPE"> -->
                            <select class="multiple-select" data-placeholder="Choose anything" multiple="multiple" id="Editor_ALARM_TYPE">
                                <option value="平台预警">平台预警</option>
                                <option value="APP预警">APP预警</option>
                                <option value="微信公众号" disabled>微信公众号</option>
                                <option value="钉钉预警" disabled>钉钉预警</option>
                                <option value="短信预警" disabled>短信预警</option>
                                <option value="邮箱预警" disabled>邮箱预警</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            预警内容
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <textarea class="form-control" placeholder="请参照下方的公式 设置预警内容" rows="3" cols="3" id="Editor_ALARM_INFO"></textarea>
                    </div>
                    <div class="form-group">
                        <label>公式详解</label>
                        <textarea 
                            class="form-control" 
                            placeholder="" rows="5" cols="3"  id="Editor_ALARM_SAMPLE">
                        </textarea>
                    </div>
                    
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_Alarm_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>

    <div class="modal fade" id="Detail_Account_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticAccountBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticAccountBackdropLabel">账号编辑&新增</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            账号类型
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <select class="single-select" id="Editor_ACCOUNT_TYPE">
                                <option value="敌对账号">敌对账号</option>
                                <option value="仇视言论">仇视言论</option>
                                <option value="谣言转发">谣言转发</option>
                                <option value="意见领袖">意见领袖</option>
                                <option value="政治宣传">政治宣传</option>
                                <option value="网络水军">网络水军</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            账号平台
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <select class="single-select" id="Editor_ACCOUNT_PLATFORM">
                                <option value="抖音">抖音</option>
                                <option value="快手">快手</option>
                                <option value="哔哩哔哩">哔哩哔哩</option>
                                <option value="知乎">知乎</option>
                                <option value="微博">微博</option>
                                <option value="小红书">小红书</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            账号名称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="" id="Editor_ACCOUNT_NAME">
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            账号主页
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="" id="Editor_ACCOUNT_URL">
                        </div>
                    </div>
                    
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_Account_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>

    <div class="modal fade" id="Detail_Source_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticSourceBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticSourceBackdropLabel">数源编辑&新增</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            数源类型
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <select class="single-select" id="Editor_SOURCE_TYPE">
                                <option value="敌对网站">敌对网站</option>
                                <option value="仇视言论">仇视言论</option>
                                <option value="谣言转发">谣言转发</option>
                                <option value="意见领袖">意见领袖</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="position: relative;">
                            数源名称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="" id="Editor_SOURCE_NAME">
                        </div>
                    </div>
                    <div class="form-group">
                        <label style="position: relative;">
                            数源主页
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control border-left-0" placeholder="" id="Editor_SOURCE_URL">
                        </div>
                    </div>
                    
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_Source_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>

    <div class="modal fade" id="Detail_User_Modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticUserBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0">
              <h3 class="modal-title" id="staticUserBackdropLabel">用户编辑</h3>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <div class="form-group">
                        <label style="position: relative;">
                            用户头像自定义上传
                        </label>
                        <div class="input-group">
                            <form style="width:100%">
                                <input id="Editor_User_Upload_Face" type="file" name="file" accept=".jpg, .png, .jpeg,image/jpeg, image/png" multiple>
                            </form>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label" style="position: relative;">
                            登录账号
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Editor_USER_LOGIN_ACCOUNT" disabled>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label" style="position: relative;">
                            登录密码
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Editor_USER_LOGIN_PASSWORD">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label" style="position: relative;">
                            用户昵称
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Editor_USER_NICKNAME">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label" style="position: relative;">
                            用户单位
                            <span style="color: red;position: absolute;font-size: 20px;">
                                *
                            </span>
                        </label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="Editor_USER_UNIT">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
              <button type="button" class="btn btn-light" id="Editor_Add_User_Save_Button">保存</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
          </div>
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
    <!-- file-uploader -->
    <script src="/static/JavaScript/fancy-file-uploader/jquery.ui.widget.js"></script>
    <script src="/static/JavaScript/fancy-file-uploader/jquery.fileupload.js"></script>
    <script src="/static/JavaScript/fancy-file-uploader/jquery.iframe-transport.js"></script>
    <script src="/static/JavaScript/fancy-file-uploader/jquery.fancy-fileupload.js"></script>

    <script src="/static/JavaScript/select2/js/select2.min.js"></script>
    <script src="/static/JavaScript/datatable/js/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/daterangepicker/moment.js"></script>
    <script src="/static/JavaScript/input-tags/js/tagsinput.js"></script>

	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Manage.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Manage_Keyword_Info();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>