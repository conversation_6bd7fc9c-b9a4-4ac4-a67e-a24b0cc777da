// utils/crypto.ts
import CryptoJS from 'crypto-js';

const AES_KEY = 'YourSecretKey123'; // 16/24/32字节的密钥
const AES_IV = '1234567890123456';  // 16字节的IV（初始化向量）

export const aesEncrypt = (data: string): string => {
  const key = CryptoJS.enc.Utf8.parse(AES_KEY);
  const iv = CryptoJS.enc.Utf8.parse(AES_IV);
  
  const encrypted = CryptoJS.AES.encrypt(
    CryptoJS.enc.Utf8.parse(data),
    key,
    {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  );
  
  return encrypted.toString();
};

export const aesDecrypt = (encryptedData: string): string => {
  const key = CryptoJS.enc.Utf8.parse(AES_KEY);
  const iv = CryptoJS.enc.Utf8.parse(AES_IV);
  
  const decrypted = CryptoJS.AES.decrypt(
    encryptedData,
    key,
    {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  );
  
  return decrypted.toString(CryptoJS.enc.Utf8);
};