import React from 'react';

// 简单的CSS样式图表组件，不依赖外部库

// 平台分布饼图（使用CSS实现）
export const SimplePlatformChart: React.FC = () => {
  const data = [
    { name: '网页', value: 6737, color: '#5B8BFF' },
    { name: '微信', value: 5275, color: '#65DB79' },
    { name: '微博', value: 7687, color: '#FF6E5E' },
    { name: 'App', value: 4219, color: '#5EAAF7' },
    { name: '论坛', value: 827, color: '#6A30F5' },
    { name: '报刊', value: 574, color: '#2192FD' },
    { name: '视频', value: 963, color: '#7C8EEE' },
    { name: '头条', value: 790, color: '#D64541' },
    { name: '搜狐号', value: 435, color: '#F2C81C' },
    { name: '问答', value: 193, color: '#14CEAA' },
    { name: '评论', value: 2, color: '#1DC9B7' },
    { name: '其他类型', value: 859, color: '#8573DD' }
  ];

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div style={{ height: '300px', padding: '20px', color: '#e5e9ec' }}>
      <h6 style={{ marginBottom: '15px', color: '#e5e9ec' }}>平台分布统计</h6>

      {/* 简单的条形图表示 */}
      <div style={{ marginTop: '20px' }}>
        {data.slice(0, 8).map((item, index) => {
          const percentage = (item.value / Math.max(...data.map(d => d.value))) * 100;
          const valuePercentage = ((item.value / total) * 100).toFixed(1);
          return (
            <div key={index} style={{ marginBottom: '12px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '12px',
                color: '#e5e9ec',
                marginBottom: '4px'
              }}>
                <span>{item.name}</span>
                <span>{item.value} ({valuePercentage}%)</span>
              </div>
              <div style={{
                width: '100%',
                height: '8px',
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${percentage}%`,
                  height: '100%',
                  backgroundColor: item.color,
                  borderRadius: '4px',
                  transition: 'width 0.3s ease'
                }}></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 情感属性环形图（使用CSS实现）
export const SimpleSentimentChart: React.FC = () => {
  const data = [
    { name: '正面', value: 85, color: '#1f77b4' },
    { name: '负面', value: 4, color: '#d62728' },
    { name: '中性', value: 11, color: '#2ca02c' }
  ];

  return (
    <div style={{ height: '300px', padding: '20px', textAlign: 'center' }}>
      {/* 环形图的简单表示 */}
      <div style={{ 
        width: '150px', 
        height: '150px', 
        margin: '0 auto 20px',
        position: 'relative',
        borderRadius: '50%',
        background: `conic-gradient(
          ${data[0].color} 0deg ${data[0].value * 3.6}deg,
          ${data[1].color} ${data[0].value * 3.6}deg ${(data[0].value + data[1].value) * 3.6}deg,
          ${data[2].color} ${(data[0].value + data[1].value) * 3.6}deg 360deg
        )`
      }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '80px',
          height: '80px',
          backgroundColor: '#2c3e50',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px',
          color: '#e5e9ec'
        }}>
          情感分析
        </div>
      </div>
      
      {/* 图例 */}
      <div style={{ display: 'flex', justifyContent: 'center', gap: '20px', flexWrap: 'wrap' }}>
        {data.map((item, index) => (
          <div key={index} style={{ 
            display: 'flex', 
            alignItems: 'center',
            fontSize: '14px',
            color: '#e5e9ec'
          }}>
            <div style={{ 
              width: '12px', 
              height: '12px', 
              backgroundColor: item.color, 
              marginRight: '6px',
              borderRadius: '2px'
            }}></div>
            <span>{item.name}: {item.value}%</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// 舆情趋势图（平滑曲线图）
export const SimpleTrendChart: React.FC = () => {
  // 生成时间数据（24小时）
  const generateData = () => {
    const data = [];
    const hours = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'];
    const values = [120, 80, 200, 350, 280, 180]; // 模拟数据，形成一个平滑的曲线

    for (let i = 0; i < hours.length; i++) {
      data.push({
        time: hours[i],
        value: values[i]
      });
    }

    return data;
  };

  const data = generateData();
  const maxValue = 400; // 固定最大值
  const minValue = 0;   // 固定最小值

  // 创建SVG路径
  const createSmoothPath = () => {
    const width = 800;
    const height = 240;
    const padding = 60;

    const points = data.map((item, index) => {
      const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
      const y = height - padding - (item.value / maxValue) * (height - 2 * padding);
      return { x, y, value: item.value };
    });

    // 创建平滑曲线路径
    let path = `M ${points[0].x} ${points[0].y}`;

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];

      // 使用贝塞尔曲线创建平滑效果
      const cp1x = prev.x + (curr.x - prev.x) * 0.5;
      const cp1y = prev.y;
      const cp2x = curr.x - (curr.x - prev.x) * 0.5;
      const cp2y = curr.y;

      path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
    }

    return { path, points, width, height, padding };
  };

  const { path, points, width, height, padding } = createSmoothPath();

  return (
    <div style={{ height: '300px', padding: '20px', backgroundColor: 'transparent' }}>
      <svg width="100%" height="240" viewBox={`0 0 ${width} ${height}`} style={{ overflow: 'visible' }}>
        {/* 网格线 */}
        {[0, 200, 400].map((value, index) => {
          const y = height - padding - (value / maxValue) * (height - 2 * padding);
          return (
            <g key={value}>
              <line
                x1={padding}
                y1={y}
                x2={width - padding}
                y2={y}
                stroke="rgba(255,255,255,0.1)"
                strokeWidth="1"
              />
              <text
                x={padding - 10}
                y={y + 4}
                fill="#adb5bd"
                fontSize="12"
                textAnchor="end"
              >
                {value}
              </text>
            </g>
          );
        })}

        {/* 平滑曲线 */}
        <path
          d={path}
          fill="none"
          stroke="#5B8BFF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* 数据点 */}
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="4"
            fill="#5B8BFF"
            stroke="#ffffff"
            strokeWidth="2"
          />
        ))}

        {/* X轴标签 */}
        {data.map((item, index) => {
          const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
          return (
            <text
              key={index}
              x={x}
              y={height - 10}
              fill="#adb5bd"
              fontSize="12"
              textAnchor="middle"
            >
              {item.time}
            </text>
          );
        })}
      </svg>
    </div>
  );
};

// 地区分布图（使用CSS实现）
export const SimpleMapChart: React.FC = () => {
  const data = [
    { name: '北京', value: 15234 },
    { name: '上海', value: 12456 },
    { name: '广东', value: 23567 },
    { name: '浙江', value: 8934 },
    { name: '江苏', value: 11234 },
    { name: '山东', value: 9876 },
    { name: '河南', value: 7654 },
    { name: '四川', value: 6543 },
    { name: '湖北', value: 5432 },
    { name: '湖南', value: 4321 }
  ];

  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <div style={{ height: '350px', padding: '20px' }}>
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '10px'
      }}>
        {data.map((item, index) => {
          const percentage = (item.value / maxValue) * 100;
          return (
            <div key={index} style={{ marginBottom: '8px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                fontSize: '12px', 
                color: '#e5e9ec',
                marginBottom: '4px'
              }}>
                <span>{item.name}</span>
                <span>{item.value.toLocaleString()}</span>
              </div>
              <div style={{ 
                width: '100%', 
                height: '8px', 
                backgroundColor: 'rgba(255,255,255,0.1)', 
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{ 
                  width: `${percentage}%`, 
                  height: '100%', 
                  backgroundColor: '#1f77b4',
                  borderRadius: '4px',
                  transition: 'width 0.3s ease'
                }}></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
