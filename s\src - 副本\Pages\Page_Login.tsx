import React, { useEffect, useState } from 'react';
import Particles from "@tsparticles/react";
import { initParticlesEngine } from "@tsparticles/react";
import { loadFull } from 'tsparticles';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import type { MoveDirection } from "tsparticles-engine";
import { useNavigate } from 'react-router-dom';
import { useServiceRequests } from '@/Core/Core_Control';
import { useAuth } from '@/Core/Core_AuthContent';
import { Form, Input, Checkbox, Button, message, notification  } from 'antd';
// import { LoginData } from '@/Mock/PageLogin_Data' // 引入mock数据
import '@/Styles/Page_Login.css'

const Page_Login: React.FC = () => {
  const [isEngineReady, setIsEngineReady] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false)
  const { user, login, refreshMenus  } = useAuth();
  const navigate = useNavigate();
  const { AsyncTokenRequests } = useServiceRequests();

  /** 
   * 如果用户已经登录，直接跳转到首页
  */
  useEffect(() => {
    if (user) {
      console.log('如果 user.home 存在，则跳转到 user.home，否则跳转到默认的首页')
      // 如果 user.home 存在，则跳转到 user.home，否则跳转到默认的首页
      const HomePath = user.redirect || '/Page_Translate';
      navigate(HomePath, { replace: true });
    }
  }, [user, navigate]);

  useEffect(() => {
    let isMounted = true;
    let tsParticlesInstance: any = null;
  
    const initializeParticles = async () => {
      if (!isMounted) return;
  
      await initParticlesEngine(async (engine) => {
        await loadFull(engine);
      });
  
      setIsEngineReady(true);
    };
  
    initializeParticles();
  
    return () => {
      isMounted = false;
    };
  }, []);

  // 初始化填入账号名和密码
  useEffect(() => {
    setUsername('演示账号');
    setPassword('csc18010587617');
  }, []);

  const particlesOptions = {
    background: {
      color: '#01010f',
    },
    fpsLimit: 60,
    interactivity: {
      events: {
        onClick: { enable: true, mode: 'push' },
        onHover: { enable: true, mode: 'repulse' },
      },
      modes: {
        push: { particles_nb: 4 },
        repulse: { distance: 200, duration: 0.4 },
      },
    },
    particles: {
      color: { value: '#ffffff' },
      links: {
        color: '#ffffff',
        distance: 150,
        enable: true,
        opacity: 0.3,
      },
      move: {
        direction: "right" as MoveDirection,
        enable: true,
        outMode: "out" as const,
        random: true,
        speed: 10,
        straight: true,
      },
      number: {
        density: { enable: true, area: 800 },
        value: 90,
      },
      opacity: {
        value: 0.5,
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: { min: 1, max: 3 },
      },
    },
    detectRetina: true,
  };

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleLogin= (e: React.FormEvent) => {
    e.preventDefault();
    console.log('执行登录，用户名:', username, '密码:', password);
    setLoading(true)
    let Requests_Data={
      "user_id": "",
      "user_token":"" ,
      "data_class": "Sentinel",
      "data_type": 'System',
      "data_methods": "user_login_authentication_verify",
      "data_argument": `{}`,
      "data_kwargs":JSON.stringify({"User_Name":username,"User_Password":password}),
    };
    (async () => {
      AsyncTokenRequests(Requests_Data)
        .then((Response_Data) => {
          console.log("fetchData_Init 请求成功 Response Data:", Response_Data);
          if (Response_Data.Status === 'Success') {
            // message.success('登陆成功！');
            notification.success({
              message: '登录成功',
              description: '欢迎回来,' + Response_Data.User_Name,
              placement: 'topRight',
              duration: 4,
            });
            // setIsLoggedIn(true);
            const user = {
                username: Response_Data.User_Name,
                roles: Response_Data.User_Role,
                usertoken:Response_Data.User_Token,
                redirect:Response_Data.Link_Home,
                userface:Response_Data.User_Face,
            };
            login(user);
            refreshMenus(); // 手动触发菜单/路由刷新
            const HomePath = user.redirect || '/Page_Translate';
            navigate(HomePath, { replace: true }); // 登录成功后跳转到首页
          } else {
            console.log('登陆失败')
            message.error(Response_Data.Status);
          }
          setLoading(false)
        }).catch ((err) => {
          message.error('网络出错，请稍后重试！');
          console.log('请求失败',err)

          setLoading(false)
        })
    })();
  };

  return (
    <div className="login-page">
      {!isEngineReady && <div className="loading">引擎加载中...</div>}
      <Particles
        id="tsparticles"
        options={particlesOptions}
        key={isEngineReady ? 'particles-ready' : 'particles-not-ready'}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: -1,
          pointerEvents: 'none',
        }}
      />
      <div className="login-form-container">
        <h2>欢迎回来</h2>
        <p>请输入你的账户信息</p>
        <form onSubmit={handleLogin}  className='login-form-wapper'>
          <input 
            type="text" 
            placeholder="用户名或邮箱" 
            value={username} 
            onChange={handleUsernameChange} 
            required
          />
          <div className="password-input-container">
            <input 
              type={showPassword ? 'text' : 'password'} 
              placeholder="密码" 
              value={password} 
              onChange={handlePasswordChange} 
              required
            />
            {password.length > 0 && (
              <button 
                type="button" 
                className="toggle-password-btn" 
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </button>
            )}
          </div>
          <Button type="primary" htmlType="submit" loading={loading} className="custom-login-button">
            登录
          </Button>
        </form>
        <div className="login-footer">
          <a 
            href="#"
            onClick={(e) => {
              e.preventDefault(); // 阻止默认跳转
              message.success('找回密码请联系我们: 028-87469311'); // 显示提示信息
            }}
          >
            忘记密码？
          </a>

          <a 
            href="#"
            onClick={(e) => {
              e.preventDefault(); // 阻止默认跳转
              message.success('暂时未开放账号注册，试用请联系我们: 028-87469311'); // 显示提示信息
            }}
          >
            注册账号
          </a>
        </div>
      </div>
    </div>
  );
};

export default Page_Login;