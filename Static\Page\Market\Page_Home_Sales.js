
/****************************************初始化服务项目列表***************************************************/ 
//缓存一下用户操作的数据
var Mission_List = {}
// 拷贝的数据
var Copy_Mission_List = {}
// var server_list = ['facebook', 'twitter', 'youtube', 'vkontakte', 'discuss', 'odnoklassniki','Telegram']
var server_list = []

/****************************************服务项目列表请求***************************************************/ 
function Page_Init() {
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Service_List',
    //     'User_Token': User_Token,
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        "data_class": "Network",
        "data_type": 'Price',
        "data_methods": "get_redis_mission",
        "data_argument": `{}`,
        "data_kwargs":`{}`,
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if (res != null) {
                let list = Init_Mission_List(res)
                Mission_List = list
                Copy_Mission_List = JSON.parse(JSON.stringify(list))
                let type = 'account'
                let platform_list = Reflect.ownKeys(Mission_List[type])
                Set_Mission_List(platform_list, type, Mission_List[type])
                server_list = platform_list
                $('#type_account label:first').after(setAccountCheckbox(platform_list))
                $('#type_counter label:first').after(setCounterRadio(Reflect.ownKeys(Mission_List['counter'])))
                Select_Multi()
                Select_Single()
            }
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        });
};
/****************************************动态生成账号类型筛选***************************************************/
function setAccountCheckbox(list){
    let html = ''
    list.forEach(e => {
        html += `<label class="custom-control custom-checkbox">
        <input type="checkbox" value="${e}" class="custom-control-input" checked>
        <span class="custom-control-indicator"></span>
        <span class="custom-control-description text-uppercase">${e}</span>
    </label>`
    })
    return html
};
/****************************************动态生成营销服务筛选***************************************************/
function setCounterRadio(list){
    let html = ''
    list.forEach((e,i) => {
        html += `<label class="custom-control custom-radio">
                                <input type="radio" name="radio-inline" value="${e}"
                                    class="custom-control-input" ${i==0?'checked':''}>
                                <span class="custom-control-indicator"></span>
                                <span class="custom-control-description text-uppercase">${e}</span>
                            </label>`
    })
    return html
}
var user_info = {}
var point_step = 100
/****************************************获取用户信息***************************************************/
function Get_User_CreditPoints() {
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'User_Info',
    //     'User_Token': User_Token,
    // });
    loaderShow()
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        "data_class": "Sentinel", 
        "data_type": 'Service_User',
        "data_methods": "return_user_credit_points",
        "data_argument": `{}`,
        "data_kwargs":`{}`,
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ('Status' in res) {
                return notify({
                    message: '获取用户数据失败！',
                    type: 'danger',
                })
            }
            user_info = res
            Set_User_Info(user_info)
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        }).finally(() => loaderHide());

};
/****************************************监听用户刷新***************************************************/
$('#reflush_points').on('click', function () {
    Get_User_CreditPoints()
})
//转换服务项目列表数据
function Init_Mission_List(list) {
    let all_list = Object.keys(list).map(e => {
        let item = list[e]
        item['argument']['url'] = ''
        item['argument']['comment_list'] = []
        return { id: e, ...item, number: 0 }
    })
    // let platform_list = all_list.map(e => e.platform)
    let obj = {}
    // platform_list.forEach(e => obj[e] = all_list.filter(i => i.platform === e && i.status === 'Active') || [])
    /****************************************新版本**************************************************/
    // 获取服务类型并去重
    let server_dict = all_list.map(e => e.type)
    server_dict = [...new Set(server_dict)]
    // 把服务类型和数据关联
    server_dict.forEach(e => {
        // 获取type下的服务列表
        let temp_list = all_list.filter(i => i.type == e)
        let platform_list = [...new Set(temp_list.map(e => e.platform))]
        let temp_obj = {}
        platform_list.forEach(e => {
            temp_obj[e] = temp_list.filter(i => i.platform == e && i.status === 'Active')
        })
        obj[e] = temp_obj
    })
    return obj
}
// 渲染服务项目列表
function Set_Mission_List(server_list, type, list) {
    $('#accordion').empty()
    const fragment = document.createDocumentFragment();
    // 拼接模板
    server_list.forEach(item => {
        let c_temp_list = list[item].map(c_item => {
            // 设置模板操作
            let comment_temp = `
                                    <div class="col-sm">
                                          <div class="input-group mb-3">
                                              <button class="btn btn-success btn-block" type="button" onclick="handleEdit('${item}','${c_item['id']}')">编辑</button>
                                          </div>
                                    </div>
                                `
            let common_temp = `
                    <div class="col-sm">
                              <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-outline-secondary" type="button" id="button-addon1" onclick="decrementValue('${item}','${c_item['id']}')">
                                            <i class="zmdi zmdi-minus"></i>
                                        </button>
                                    </div>
                                    <input type="number" class="form-control text-center" onblur="inputNumberBlur('${item}','${c_item['id']}')" oninput="inputNumberChange('${item}','${c_item['id']}')"  value="${c_item['number']}" placeholder="0" id="${item}_${c_item['id']}" aria-label="Example text with button addon" aria-describedby="button-addon1 button-addon2">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" id="button-addon2" onclick="incrementValue('${item}','${c_item['id']}')">
                                            <i class="zmdi zmdi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                    `
            let action_temp = {
                'comment': comment_temp,
                'common': common_temp,
            }
            let c_temp = `<li class="my-4">
                        <div class="row">
                            <div class="col-sm">
                              服务编号：${c_item['id']}
                            </div>
                            <div class="col-sm">
                              项目名称：${c_item['name']}
                            </div>
                            <div class="col-sm text-truncate" data-toggle="tooltip" data-placement="top" title="${c_item['argument']['remark']}">
                              说明：${c_item['argument']['remark']}
                            </div>
                            <div class="col-sm">
                             Min/Max：${c_item['argument']['condition'].filter(e => e != '').length == 0 ? '无限制' : c_item['argument']['condition'].join('/')}
                            </div>
                            <div class="col-sm">
                              单价（元）：${c_item['argument']['price']}
                            </div>
                            ${action_temp[c_item['argument']['sign'] === 'comment' ? 'comment' : 'common']}
                        </div>
                    </li>`;
            return c_temp;
        })
        let temp = `
            <div class="card">
                <div class="card-header" role="tab" id="t_${item}">
                    <a class="text-uppercase" data-toggle="collapse" href="#${item}" aria-expanded="true" aria-controls="${item}">
                        ${item}
                    </a>
                </div>
                <div id="${item}" class="collapse show" role="tabpanel" aria-labelledby="headingOne" data-parent="#accordion">
                <div class="card-body">
                        <ul>
                            ${c_temp_list.join('')}
                        </ul>
                    </div>
                </div>
            </div>
            `
        let container = document.createElement('div');
        container.innerHTML = temp;
        fragment.appendChild(container);
    });
    $('#accordion').append(fragment)

    $('[data-toggle="tooltip"]').tooltip();
}
// 设置用户信息
function Set_User_Info(info) {
    $('#user_charger').text(info.user_charger)
    $('#username').val(info.user_name)
    $('#usertype').val(info.user_type)
    $('#phone').val(info.user_phone)
    $('#credit_points').val(info.user_charger)
    $('#email').val(info.user_email || "未设置")
}
/*********************************************/
// 筛选多选
function Select_Multi() {
    $('.custom-checkbox input[type=checkbox]').click(function () {
        let __server_list = []
        let type = $('input[type="radio"][name="operation"]:checked').val();
        $('.custom-checkbox input[type=checkbox]').each(function () {
            if ($(this).is(':checked')) {
                let value = $(this).val()
                __server_list.push(value)
            }
        })
        Set_Mission_List(__server_list, type, Mission_List[type])
    })
}
// 重置筛选 则查询所有
$('#restSelect').click(function () {
    let checklist = $('.custom-checkbox input[type=checkbox]')
    for (let i = 0; i < checklist.length; i++) {
        checklist[i].checked = false
    }
})
// 单选
function Select_Single(type) {
    $('.custom-radio input[type=radio]').click(function () {
        let selectedValue = $('input[name="radio-inline"]:checked').val();
        let type = $('input[type="radio"][name="operation"]:checked').val();
        let __server_list = [selectedValue]
    
        Set_Mission_List(__server_list, type, Mission_List[type])
        server_list = Reflect.ownKeys(Mission_List[type])
    
    })
}   

/****************************************************/
// 更新数量
function updateCountValue(platform, service_id, direction) {
    let type = $('input[type="radio"][name="operation"]:checked').val();
    let value = parseInt($(`#${platform}_${service_id}`).val());
    value = isNaN(value) ? 0 : value;
    // 获取购买限制条件
    let condition = Mission_List[type][platform].find(item => item.id === service_id)['argument']['condition'];
    let max = condition.length === 2 ? condition[1] : undefined;
    let min = condition.length > 0 ? condition[0] : undefined;
    max = max == '' ? undefined : max
    min = min == '' ? undefined : min
    if (direction === 'increment') {
        value += 1;
        //判断购物车当前最大购买数量
        if (max !== undefined && value > max) {
            value = max;
        }
        if (max !== undefined && value < min) {
            value = min;
        }
    } else if (direction === 'decrement') {
        //判断购物车当前最小购买数量
        value -= 1;
        if (min !== undefined && value < min) {
            value = 0;
        } else {
            value = min === undefined ? Math.max(value, 0) : Math.max(value, min);
        }

    }
    $(`#${platform}_${service_id}`).val(value);
    return value
}
//处理输入情况的显示BUG
function inputNumberChange(platform, service_id) {
    if (!check_add_mission(service_id,platform)) {
        $(`#${platform}_${service_id}`).val(0);
        return
    }
    let value = parseInt($(`#${platform}_${service_id}`).val());
    $(`#${platform}_${service_id}`).val(value);
    // update_mission_list(platform, service_id, 'number', value)
}
// 失去焦点触发,处理购买限制
function inputNumberBlur(platform, service_id) {
    if (!check_add_mission(service_id,platform)) {
        return
    }
    let type = $('input[type="radio"][name="operation"]:checked').val();
    let value = parseInt($(`#${platform}_${service_id}`).val());
    let newVal = isNaN(value) ? 0 : value;
    // 获取购买限制条件
    let condition = Mission_List[type][platform].find(item => item.id === service_id)['argument']['condition'];
    let max = condition.length === 2 ? condition[1] : undefined;
    let min = condition.length > 0 ? condition[0] : undefined;
    max = max == '' ? undefined : max
    min = min == '' ? undefined : min
    value = Math.max(min, Math.min(newVal, max));
    $(`#${platform}_${service_id}`).val(value || newVal);
    update_mission_list(platform, service_id, 'number', value || newVal)
}

// 增加数量
function incrementValue(platform, service_id) {
    if (!check_add_mission(service_id, platform)) {
        return
    }

    // 更新视图
    let value = updateCountValue(platform, service_id, 'increment');
    // 更新数据
    update_mission_list(platform, service_id, 'number', value)
}

// 减少数量
function decrementValue(platform, service_id) {
    // 更新数量视图
    let value = updateCountValue(platform, service_id, 'decrement');
    // 更新数据
    update_mission_list(platform, service_id, 'number', value)
}
//更新list
function update_mission_list(platform, service_id, key, value) {
    let type = $('input[type="radio"][name="operation"]:checked').val();
    let platform_list = Mission_List[type][platform]
    let index = platform_list.findIndex(item => item['id'] === service_id)
    eval(`Mission_List[type][platform][${index}].${key} = ${value};`)

}

// 检测链接是否有效
function url_check(url, platform) {
    let falg_null = true
    let falg_platform = true
    let falg_RegExp = true
    // ['facebook','twitter','youtube','vkontakte','discuss','odnoklassniki']
    let platform_list = { 'facebook': 'facebook', 'twitter': 'x', 'youtube': 'youtube', 'vkontakte': 'vk.com', 'odnoklassniki': 'ok.ru', 'discuss': 'discuss.com' }
    // console.log(falg_null,falg_platform,falg_RegExp,platform_list)
    let urlRegExp = /^((https|http|ftp|rtsp|mms)?:\/\/)+[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/
    // console.log('urlfalg', url.indexOf(platform_list[platform]) != -1 )
    if (url === "") {
        falg_null = false
    }

    if (url.indexOf(platform_list[platform]) != -1) {

    } else {
        falg_platform = false
    }
    if (urlRegExp.test(url)) {

    } else {
        falg_RegExp = false
    }

    if (falg_null && falg_platform && falg_RegExp) {
        return true
        // console.log('true')
    } else {
        //  console.log('false')
        return false
    }


}

//检查平台与输入链接是否匹配
function check_input(platform) {
    let url = $('#target_url').val()
    let falg = url_check(url, platform)
    if (!falg && url) {
        notify({
            message: '目标链接格式错误！',
            type: 'danger'
        })
        return false
    } else if (!falg && !url) {
        notify({
            message: '请输入目标链接！',
            type: 'warning'
        })
        return false
    }
    return true
}
// 失去焦点时提示处理
$('#target_url').on('blur', function () {
    let platform = $('input[name="radio-inline"]:checked').val();
    check_input(platform)
})
//检查添加数量时的链接是否正确
function check_add_mission(service_id, __platform) {
    let type = $('input[type="radio"][name="operation"]:checked').val();
    let platform = $('input[name="radio-inline"]:checked').val(); //针对营销服务

    if (type === "counter") {
        let bool = check_input(platform)
        let url = $("#target_url").val()
        if (!bool) {
            return false
        }
        Mission_List[type][platform].find(item => item.id === service_id)['argument']['url'] = url
        return true
    }
    if (type === "phone") {
        let contact = $("#contact").val()
        let address = $("#address").val()
        let contact_name = $("#contact_name").val()
        if (!contact) {
            notify({
                message: '请输入联系方式！',
                type: 'warning'
            })
            return false
        }
        if (!contact_name) {
            notify({
                message: '请输入收货人姓名！',
                type: 'warning'
            })
            return false
        }
        if (!address) {
            notify({
                message: '请输入收货地址！',
                type: 'warning'
            })
            return false
        }

        Mission_List[type][__platform].find(item => item.id === service_id)['argument']['address'] = address
        Mission_List[type][__platform].find(item => item.id === service_id)['argument']['contact'] = contact
        Mission_List[type][__platform].find(item => item.id === service_id)['argument']['contact_name'] = contact_name
        return true
    }
    return true
}


// 节流

var throttleTimer = false;

var throttle = (callback, time) => {
    if (throttleTimer) return;

    throttleTimer = true;
    setTimeout(() => {
        callback();
        throttleTimer = false;
    }, time);
};
// 防抖
function debounce(func, delay, ...params) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, params.concat(args)), delay);
    };
}
function __debounce(func, wait, immediate) {
    let timeout;

    return function() {
        const context = this;
        const args = arguments;

        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };

        const callNow = immediate && !timeout;

        clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
    };
}

/**********************************处理返回顶部滚动**********************************************/
var scrollFunction = () => {
    if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        $('#backBtn').show(500)
    } else {
        $('#backBtn').hide(600)
    }
};

window.addEventListener('scroll', debounce(onScroll,1000))
function onScroll() {
    throttle(scrollFunction, 100);
    // fixBoxOnScroll();
}

let ticking = false;
let originalOffsetTop = null;
let isFixed = false;

function fixBoxOnScroll() {
    if (!ticking) {
        window.requestAnimationFrame(() => {
            var box = document.getElementById("opera_box");
            if (originalOffsetTop === null) {
                originalOffsetTop = box.offsetTop;
            }
            let pageYOffset = window.pageYOffset;
            let offsetHeight = box.offsetHeight
            let triggerPoint = originalOffsetTop;
            if (pageYOffset > triggerPoint && !isFixed) {
                box.style.position = "fixed";
                box.style.top = (originalOffsetTop - 20) + 'px';
                box.style.left = "0px";
                box.style.background = "#000"
                box.style.padding = "0px 30px"
                isFixed = true
                $('#block').css('height',`${offsetHeight + 20}px`,'width','100%')
            } else if (pageYOffset <= triggerPoint && isFixed){
                box.style.position = "static"; // 使用 absolute 而不是 static 来保持元素在文档流中的位置
                // box.style.top = ''; // 确保元素返回原来的位置
                box.style.top = originalOffsetTop + 'px'
                box.style.background = "none"
                box.style.padding = "0"
                isFixed = false;
                $('#block').css('height',`0px`,'width','100%')
            }

            ticking = false;
        });

        ticking = true;
    }
}

function topFunction() {
    $('html, body').animate({ scrollTop: 0 }, 600); // For Chrome, Firefox, IE and Opera
}
$("#backBtn").click(topFunction)
/************************************************************************************/

// 处理账号支持与营销服务

// 当页面加载完成时获取选中的值
if ($("#marketing_server").is(":checked")) {
    $("#target-url-container").show();
}
// 监听服务类型的变化事件
$('#operation input[name="operation"]').on('change', function () {
    let type = $(this).val();
    if (type === 'counter') {
        $("#target-url-container").slideToggle(500);
        $("#type_account").hide()
        $("#type_counter").show()
        $('#type_phone').slideUp()
        let selectedValue = $('input[name="radio-inline"]:checked').val();
        Set_Mission_List([selectedValue], type, Mission_List[type])

    } else if (type === 'account') {
        $("#target-url-container").slideUp(500);
        $("#type_account").show()
        $("#type_counter").hide()
        $('#type_phone').slideUp()
        let selectedValues = $('input[type="checkbox"]:checked', '#type_account').map(function () {
            return this.value;
        }).get();
        Set_Mission_List(selectedValues, type, Mission_List[type])
    } else {
        $("#target-url-container").hide();
        $("#type_account").hide()
        $("#type_counter").hide()
        $('#type_phone').slideToggle(500)
        $('#contact').val(user_info.user_phone)
        let platform_list = Reflect.ownKeys(Mission_List[type] || {})
        Set_Mission_List(platform_list, type, Mission_List[type])
    }

});
/*******************************************************/
//构造评论表格操作栏
function operateFormatter(value, row, index) {
    return `<button class="btn btn-warning btn-sm btn-del">删除</button>`
}
let comment_text = $('#FormControlTextarea')

let $comment_table = $('#comment_table').bootstrapTable({
    formatNoMatches: function () {
        return '暂无评论信息';
    },
    height: 300,
    columns: [
        {
            title: '序号',
            width: 50,
            formatter: function (value, row, index) {
                return index + 1;
            }
        },
        {
            field: 'text',
            title: '评论内容',
            halign: 'center'
        },

        {
            field: 'operate',
            title: '操作',
            class: 'text-right',
            width: 50,
            formatter: operateFormatter,
            events: {
                'click .btn-del': remove_comment
            }
        }
    ]
})
//添加评论类型的服务项目
function handleEdit(platform, service_id) {
    // $('#target_url').val('https://www.facebook.com/login/?next=https%3A%2F%2Fwww.facebook.com%2F')
    if (!check_add_mission(service_id)) {
        return
    }
    let type = $('input[type="radio"][name="operation"]:checked').val();
    let __service = Mission_List[type][platform].find(item => item.id === service_id)
    let condition = __service['argument']['condition']
    let url = $('#target_url').val()
    $('#current_url').text(url)
    $('#comment_condition').text(condition.join('/'))
    //载入数据
    let data = __service['argument']['comment_list']
    $comment_table.bootstrapTable('load', data)
    $('#comment_count').text(data.length)
    $('#FormControlTextarea').val(data.map(item => item.text).join('\n'))
    //
    $('#modal-editor').modal('show')
    comment_text.off('input').on('input', debounce(comment_input, 1000, __service))
    let composing = false;
    comment_text.off('compositionstart').on('compositionstart', () => {
        composing = true;
    });
    comment_text.off('compositionend').on('compositionend', () => {
        composing = false;
        debounce(comment_input, 1000, __service)
    });
    $('#submit_comment').off('click').on('click', debounce(submit_comment, 1000, __service))
}
//删除评论内容
function remove_comment(e, value, row, index) {
    let comment_list = $comment_table.bootstrapTable('getData')
    comment_list.splice(index, 1)
    $('#comment_table').bootstrapTable('load', comment_list)
    if (comment_list.length === 0) {
        comment_text.val('')
    } else {
        let text = comment_list.map(item => item.text).join('\n')
        comment_text.val(text)
    }
    $('#comment_count').text(comment_list.length)
}
//处理input输入评论条数
function comment_input(service) {
    let condition = service['argument']['condition']
    let max = condition[1]
    let text = comment_text.val()
    let comment_list = text
        .split('\n')
        .filter(item => item !== '')
        .map((e, i) => ({ text: DOMPurify.sanitize(e), id: i + 1 }))
    $comment_table.bootstrapTable('load', comment_list.slice(0, 50))
    if (comment_list.length > max) {
        notify({
            message: `最多支持${max}条评论`,
            type: 'warning'
        })
    }
    $('#comment_count').text(comment_list.length)

}
//提交按钮
function submit_comment(service) {
    let data = $comment_table.bootstrapTable('getData')
    let min = service['argument']['condition'][0]
    if (data.length === 0) {
        notify({
            message: '评论内容不能为空',
            type: 'warning'
        })
        return
    }
    if (data.length < min) {
        notify({
            message: `至少输入${min}条评论`,
            type: 'warning'
        })
        return
    }
    // Mission_List[service.platform].find(item => item.id == service.id).argument.comment_list = data
    // Mission_List[service.platform].find(item => item.id == service.id).number = 1
    let it = Mission_List[service.type][service.platform].find(item => item.id == service.id)
    let _it = JSON.parse(JSON.stringify(it))
    _it.argument.comment_list = data
    _it.number = data.length
    shop_list.push({ ..._it })
    $shop_table.bootstrapTable('load', shop_list)
    computed_point()
    $('[data-toggle="tooltip"]').tooltip()
    $('#modal-editor').modal('hide')
    it.argument.comment_list = []
    it.number = 0
    it.argument.url = ''
}


/**************************加入购物车*******************************/
var shop_list = []

/********************************弹窗表格***********************************/
//购物表
$shop_table = $('#shop_list').bootstrapTable({
    formatNoMatches: function () {
        return '暂购物信息';
    },
    height: 500,
    columns: [
        {
            field: 'id',
            title: '序号',
            visible: false
        },
        {
            title: '序号',
            formatter: function (value, row, index) {
                return `${index + 1}`
            },
            width: 50
        },
        {
            field: 'name',
            title: '项目',
            halign: 'center',
            align: 'center',
        },
        {
            field: 'argument',
            title: '目标链接',
            halign: 'center',
            align: 'center',
            formatter: function (value, row, index) {
                let url = value.url;
                // 去掉域名
                url = url.replace(/^https?:\/\/[^\/]+/, '');
                return `<p class="text-truncate" data-toggle="tooltip" data-placement="top" title="${url || '-'}">${url || '-'}</p>`;
            }
        },

        {
            field: 'number',
            title: '数量',
            halign: 'center',
            align: 'center',
            width: 50
        },

        {
            field: 'price',
            title: '价格',
            halign: 'center',
            align: 'center',
            formatter: function (value, row, index) {
                return row.argument.price
            },
            width: 50
        },

        {
            field: 'operate',
            title: '操作',
            class: 'text-right',
            width: 60,
            formatter: operateFormatter,
            events: {
                'click .btn-del': remove_shop
            }
        }
    ]
})
// 移除商品
function remove_shop(e, value, row, index) {

    shop_list.splice(index, 1)
    $shop_table.bootstrapTable('load', shop_list)
    computed_point()
}
// 计算总点数和总数量
function computed_point() {
    let total_point = new Decimal(0);
    let total_number = 0;
    let total_service = 0;
    shop_list.forEach(item => {
        const number = new Decimal(parseInt(item['number']));
        const price = new Decimal(parseFloat(item['argument']['price']));
        total_point = total_point.add(number.mul(price))
        total_number += number
        total_service += 1
    })
    let price = total_point.mul(point_step)
    $('#buy_point').text(price)
    $('#order_price').text(price)
    $('#buy_number').text(`${total_service}项`)
}
//加入购物车事件
function add_mission() {
    /**
     * 遍历服务类型----> 遍历服务列表----> 遍历服务对象——> 遍历服务参数------>如果number大于0就添加进购物车
    */
    ['account', 'counter', 'phone'].forEach(type_info => {
        if(Mission_List[type_info]){
            let platform_list = Reflect.ownKeys(Mission_List[type_info])
            platform_list.forEach(platform => {
                Mission_List[type_info][platform].forEach(service => {
                    if (service.number > 0) {
                        shop_list.push(service)
                    }
                })
            })
        }
        
    })
    // server_list.forEach(platform => {
    //     Mission_List[platform].forEach(service => {
    //         if (service.number > 0) {
    //             shop_list.push(service)
    //         }
    //     })
    // })

    if (shop_list.length > 0) {
        $shop_table.bootstrapTable('load', shop_list)
        computed_point()
        Mission_List = JSON.parse(JSON.stringify(Copy_Mission_List))
        let type = $('input[type="radio"][name="operation"]:checked').val();
        if (type === 'account') {
            Set_Mission_List(server_list, type, Mission_List[type])
        } else if (type === 'counter') {
            let selectedValue = $('input[name="radio-inline"]:checked').val();
            Set_Mission_List([selectedValue], type, Mission_List[type])
        } else {
            let platform_list = Reflect.ownKeys(Mission_List[type])
            Set_Mission_List(platform_list, type, Mission_List[type])

        }
    }
}
// 创建订单
$('#post_order').on('click', function () {
    if (shop_list.length === 0) {
        notify({
            message: '购物车为空',
            type: 'warning'
        })
        return
    }
    let price = $('#order_price').text()
    if (price > user_info.user_charger) {
        notify({
            message: '余额不足',
            type: 'warning'
        })
        $('#modal-backdrop-ignore').modal('show')
        return
    }
    let order_dict = shop_list.map(e => {
        return {
            SID: e.id,
            NAME: e.name,
            ORDER_NUMBER: e.number,
            ORDER_URL: e.argument.url,
            ORDER_TYPE: e.type,
            ORDER_PRICE: e.argument.price,
            ORDER_COMMENT_LIST: e.argument.comment_list.map(e => ({ CONTENT: e.text, ID: e.id })),
            ORDER_CONTACT: e.argument.contact || '',
            ORDER_CONTACT_NAME: e.argument.contact_name || '',
            ORDER_ADDRESS: e.argument.address || "",
            ORDER_PLATFORM: e.platform
        }
    })
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Order_Submit',
    //     'User_Token': User_Token,
    //     order_dict
    // });
    loaderShow()
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'Market', 
        'data_type': 'Network', 
        'data_methods': 'submit_order',
        "data_argument": `{}`,
        "data_kwargs":{'order_dict':order_dict},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ('Status' in res && res.Status === 'success') {
                $('#modal-large').modal('hide');
                shop_list = []
                $shop_table.bootstrapTable('load', [])
                computed_point()
                return notify({
                    message: '提交订单成功！',
                    type: 'success',
                })
            }
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        }).finally(() => loaderHide());
    // console.log(order_dict)
    // let order_dict['SID']
    // let order_dict['NAME']
    // let order_dict['ORDER_NUMBER']
    // let order_dict['ORDER_URL']
    // let order_dict['ORDER_TYPE']
    // let order_dict['ORDER_PRICE']
    // let order_dict['ORDER_COMMENT_LIST']
})

/************************Recharge_Points*********************************/
const debouncedRechargePoints = debounce(recharge_points, 1000);
$('#Recharge_Points').on('submit', function(event) {
    event.preventDefault()
    debouncedRechargePoints()
})
function recharge_points() {
    
    let price = $('#rechange_price').val()
    if (price && price <= 0) {
        return notify({
            message: '充值金额不能为空',
            type: 'warning'
        })
    }
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Create_Pay',
    //     'User_Token': User_Token,
    //     "Points": price
    // });
    // __Server_Data.async_run()
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        "data_class": "CreditPoints", 
        "data_type": 'Check', 
        "data_methods": "recharge",
        "data_argument": `{}`,
        "data_kwargs":{"Points": price},
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((res) => {
            if ("status" in res && res.status === 'success') {
                let pay_url = res.pay_url
                $('#qrcode').empty()
                let qrcode = new QRCode('qrcode', {
                    width: 220,
                    height: 220,
                    text: pay_url,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                })
                $('#qrcode').show()
                $('#qr_submit').hide()
                $('#cancel_pay').hide()
                $('#reflush_points').show()
                // 开启轮询支付状态
                query_pay(res['pay_order_id'])

            }
        }).catch((err) => {
            console.log("初始化请求出错:", err)
        });
}
var query_pay_timer = null
// 定时查询支付状态
function query_pay(pay_order_id, delay = 5000) {
    if (!pay_order_id) return
    // const __Query_Pay_Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'Order_Handle',
    //     'User_Token': User_Token,
    //     pay_order_id: pay_order_id
    // });
    query_pay_timer = setInterval(() => {
        let Requests_Data = {
            "user_id": 'Market',
            "user_token":User_Token,
            "data_class": "CreditPoints", 
            "data_type": 'Check', 
            "data_methods": "order_handle",
            "data_argument": `{}`,
            "data_kwargs":{'pay_order_id': pay_order_id},
        };
        __Service_Requests = new Service_Requests("Async",Requests_Data);
        __Service_Requests.callMethod()
        // __Query_Pay_Server_Data.async_run().then(res => {
        .then(res => {
            if ("Status" in res && res["Status"] === 'Success') {
                let Trade_Status = res['Trade_Status']
                // console.log(Trade_Status)
                switch (Trade_Status) {
                    case 'TRADE_CLOSED': //交易关闭
                        clearInterval(query_pay_timer)
                    case "TRADE_SUCCESS": //交易成功
                        clearInterval(query_pay_timer)
                        Get_User_CreditPoints()
                        $('#qr_submit').show()
                        $('#cancel_pay').show()
                        $('#reflush_points').hide()
                        $('#qrcode').hide()
                        break;
                    case "WAIT_BUYER_PAY": //等待支付
                        break;
                    default:
                        clearInterval(query_pay_timer)
                }
                $('#pay_order_status').text(res["Messag"])
            } else {
                $('#pay_order_status').text(res["Messag"])
            }
        })
    }, delay)

}