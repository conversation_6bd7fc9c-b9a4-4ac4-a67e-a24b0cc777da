import React, { useRef,useState, useEffect } from 'react';


interface Props_Status {
  toggleDrawer: () => void; // 接收 toggleDrawer 函数
}

const Utils_System_Status: React.FC<Props_Status> = ({ toggleDrawer }) => {

    let Save_Time = (new Date()).toLocaleTimeString()

    return (
        <div
       
        style={{
            position: 'absolute',
            background:"rgba(182, 172, 172, 0.0)",
            top:8,
            left:3,
            width:"188px",
            height:"20px",
            display: 'flex', // 使用 flex 布局
            justifyContent: 'space-between', // 水平间距分布
            alignItems: 'center', // 垂直居中对齐
            padding: '0 5px', // 添加内边距，避免内容紧贴边界
            
            }}>

        <p   id="System_Status_Save_Time"     style={{ margin: 0 ,color:"rgba(255, 255, 255, 0.5)", fontSize:12,}}>自动保存 {Save_Time}</p>
        <p   id="System_Status_Save_Release"  style={{ margin: 0 ,color:"rgba(255, 255, 255, 0.5)", fontSize:12,}}>未发布</p>



        </div>


  );
};

export default Utils_System_Status;