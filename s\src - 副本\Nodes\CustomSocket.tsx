// import * as React from "react";
// import { ClassicPreset } from "rete";
// import styled from "styled-components";
// import { $socketsize } from "./Consts";
// import { $fontfamily } from "./Consts";
// const Styles = styled.div`
//   display: inline-block;
//   cursor: pointer;
//   border: 1px solid grey;
//   width: ${$socketsize}px;
//   height: ${$socketsize * 2}px;
//   vertical-align: middle;
//   background: #fff;
//   z-index: 2;
//   box-sizing: border-box;
//   &:hover {
//     background: #ddd;
//   }
// `;
// tsx
// 复制
import * as React from "react";
import { ClassicPreset } from "rete";
import styled from "styled-components";
import { $socketsize } from "./Consts";

interface SocketProps {
  $socketsize: number;
  onClick?: () => void;
  className?: string;
}

const Styles = styled.div<{ $socketsize: number }>`
  display: inline-block;
  cursor: pointer;
  width: ${props => props.$socketsize * 0.8}px;
  height: ${props => props.$socketsize * 0.8}px;
  vertical-align: middle;
  z-index: 2;
  position: relative;
  box-sizing: border-box;
  transition: all 0.2s ease;
  left: 0px; /* 整体向左移动10px */

  svg {
    width: 100%;
    height: 100%;
    display: block;
    overflow: visible;
    transform: translateX(-5px)
  }

  path {
    fill: transparent;
    stroke: #666;
    stroke-width: 1px;
    stroke-linejoin: round; /* 确保转角处平滑 */
    vector-effect: non-scaling-stroke; /* 保持线条粗细不随缩放变化 */
    transition: all 0.2s ease;
  }

  &:hover {
    transform: scale(1.1);
    path {
      stroke: #999;
    }
  }
`;

const Socket = ({ $socketsize, onClick, className }: SocketProps) => {
  // 原始路径数据（已向左偏移10px）
  const pathData = "M-10,0 L0,0 L10,10 L0,20 L-10,20 Z";

  return (
    <Styles 
      $socketsize={$socketsize} 
      onClick={onClick}
      className={className}
    >
      <svg viewBox="-12 -2 24 24"> {/* 调整viewBox适应偏移 */}
        <path d={pathData} />
      </svg>
    </Styles>
  );
};

// Rete 专用的 CustomSocket 组件
export function CustomSocket<T extends ClassicPreset.Socket>(props: { data: T }) {
  return <Socket $socketsize={$socketsize} />;
}

export default Socket;