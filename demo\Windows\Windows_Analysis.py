import sys,random
import numpy as np
import librosa
from PySide6 import QtWidgets,QtCore,QtGui
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QToolButton, QLabel, QFrame, QSizePolicy,
                               QDockWidget, QStatusBar, QMenuBar, QMenu, )
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont, QPalette, QColor,QAction
import qtawesome as qta
from Bin.Utils.UtilsCenter import *
# sys.path.append(r"D:\Sentinel Foundation\Media\Server\OS\Page")
from Bin.System.OS.Page.Media import Page_Widget_Media_VoicePrint,Page_Widget_Media_VideoMonitor,Page_Widget_Media_VideoAnalysis,Page_Widget_Media_VideoHome
sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Utils")
import Utils_PopupDialog
sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Component")
import Component_Config,Component_Media_PopupDialog
import Component_Devicetree
import image_rc

Page_Info={}
class Windows_Analysis(QtWidgets.QMainWindow):
    def __init__(self):
        global Page_Info
        super().__init__()
        self.setWindowTitle("哨兵数据分析工作站")
        self.resize(1920, 1080)
        # self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 3px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')

        self.setWindowFlags(QtCore.Qt.FramelessWindowHint | QtCore.Qt.WindowStaysOnTopHint)  # 设置无边框和置顶窗口样式
        self.setWindowIcon(QtGui.QIcon(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.ico"))
        # # 添加阴影效果
        # self.shadow = QtWidgets.QGraphicsDropShadowEffect(self)
        # self.shadow.setBlurRadius(40)  # 阴影模糊半径
        # self.shadow.setColor(QColor(0, 0, 0, 128))  # 阴影颜色和透明度
        # self.shadow.setOffset(10, 0)  # 阴影偏移量
        # self.setGraphicsEffect(self.shadow)  # 将阴影效果应用到窗口
        Page_Info["Page_Def_List"]={}

        self.initUI()



    def initUI(self):
        # self.setStyleSheet(
        #     '''background:rgba(194, 223, 255,1);border-width:5px;border-radius:6px;padding:2px 4px;color:black''')
        self.QWidget_Central = QtWidgets.QWidget(self)
        self.setCentralWidget(self.QWidget_Central)

        __QVBoxLayout = QtWidgets.QHBoxLayout(self.QWidget_Central)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout.setSpacing(0)

        self.QWidget_Left = QtWidgets.QWidget()
        self.QWidget_Left.setStyleSheet('background:red; color:white;')
        self.QWidget_Left.setFixedWidth(60)
        # self.QLabel_Title.setAlignment(Qt.AlignCenter)  # 设置文本居中
        self.QWidget_Left.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)  # 设置大小策略



        self.QWidget_Left_Drawer = QtWidgets.QWidget()
        self.QWidget_Left_Drawer.setStyleSheet('background:transparent')
        self.QWidget_Left_Drawer.setFixedWidth(0)
        # self.QLabel_Title.setAlignment(Qt.AlignCenter)  # 设置文本居中
        self.QWidget_Left_Drawer.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        self.QWidget_Device_Drawer = QtWidgets.QWidget()
        self.QWidget_Device_Drawer.setStyleSheet('background:rgba(40, 52, 80, 0.8)')
        self.QWidget_Device_Drawer.setFixedWidth(0)
        self.QWidget_Device_Drawer.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        __QHBoxLayout_Left = QtWidgets.QHBoxLayout(self.QWidget_Left)
        __QHBoxLayout_Left.setContentsMargins(0, 0, 0, 0)
        __QHBoxLayout_Left.setSpacing(0)











        self.QLabel_PageMenu = QtWidgets.QLabel()
        self.QLabel_PageMenu.setAlignment(Qt.AlignCenter)


        __QHBoxLayout_Left.addWidget(self.QLabel_PageMenu)
        # __QHBoxLayout_Left.addWidget(self.toggle_button)

        # 创建内容区域
        self.QWidget_Right = QtWidgets.QWidget()
        # self.QWidget_Right.setStyleSheet('background:rgba(40, 52, 80, 0.3); color:white;')
        self.QWidget_Right.setStyleSheet('background:rgba(0, 0, 0, 0.85); color:rgba(0, 255, 180, 0.9);')
        # self.QWidget_Content.setAlignment(Qt.AlignCenter)
        self.QWidget_Right.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        __QVBoxLayout_Right = QtWidgets.QVBoxLayout(self.QWidget_Right)
        __QVBoxLayout_Right.setContentsMargins(0, 0, 0, 0)
        __QVBoxLayout_Right.setSpacing(0)

        self.QLabel_Title = QtWidgets.QLabel("QLabel_Title")
        self.QLabel_Title.setStyleSheet('background:rgb(37, 41, 48); color:white;')
        self.QLabel_Title .setMinimumHeight(58)
        self.QLabel_Title .setMaximumHeight(58)
        self.QLabel_Title.setAlignment(Qt.AlignCenter)

        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet('background:; color:white;')
        self.QLabel_Content.setAlignment(Qt.AlignCenter)

        __QVBoxLayout_Right.addWidget(self.QLabel_Title,0)
        __QVBoxLayout_Right.addWidget(self.QLabel_Content,1)



        __QVBoxLayout.addWidget(self.QWidget_Left)
        __QVBoxLayout.addWidget(self.QWidget_Left_Drawer)
        __QVBoxLayout.addWidget(self.QWidget_Device_Drawer)
        __QVBoxLayout.addWidget(self.QWidget_Right)


        self.is_expanded_Screen = False
        self.is_expanded_Menu = False
        self.is_expanded_Drawer_Left = False
        self.is_expanded_Drawer_Right = False
        Page_Info["Page_Def_List"]["Toggle_Menu"]  = self.Toggle_Menu
        Page_Info["Page_Def_List"]["Open_Browser"] = self.Open_Browser
        Page_Info["Page_Def_List"]["Page_Change"]  = self.Page_Change
        Page_Info["Page_Def_List"]["Permission_Dialog"] = self.Permission_Dialog
        #
        #
        self.Set_Title()
        self.Set_PageMenu()
        self.Set_Content()


        self.Set_Left_Drawer()


    def Set_Title(self):
        StyleSheet_QPushButton = """
               QPushButton{background-position: left center;
                   background-repeat: no-repeat;
                   border: none;
                   border-radius: 3px;
                   border-left: 0px solid transparent;
                   background-color: rgb(37, 41, 48);
                   padding: 3px;
                   color: rgb(113, 126, 149);}
                   QPushButton:hover {background-color: rgba(255, 255, 255, 0.3);}
               """
        QHBoxLayout_Title = QtWidgets.QHBoxLayout(self.QLabel_Title)
        QHBoxLayout_Title.setContentsMargins(0, 0,8, 0)
        QHBoxLayout_Title.setSpacing(0)

        self.QLabel_Switch = QtWidgets.QLabel()
        self.QLabel_Switch.setStyleSheet('background:')

        QHBoxLayout_Switch = QtWidgets.QHBoxLayout(self.QLabel_Switch)
        QHBoxLayout_Switch.setContentsMargins(5, 0, 0, 0)
        QHBoxLayout_Switch.setSpacing(0)










        self.QLabel_Setting = QtWidgets.QLabel()
        self.QLabel_Setting.setMaximumHeight(30)
        self.QLabel_Setting.setStyleSheet('background:')

        QHBoxLayout_Setting = QtWidgets.QHBoxLayout(self.QLabel_Setting)
        QHBoxLayout_Setting.setContentsMargins(0, 0, 0, 0)
        QHBoxLayout_Setting.setSpacing(0)
















        QPushButton_Menu = QtWidgets.QPushButton()
        # QPushButton_Menu.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_menu.png"))
        QPushButton_Menu.setIcon(QIcon(":/assets/icon_menu.png"))
        QPushButton_Menu.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Menu.clicked.connect(self.Toggle_Menu)
        QPushButton_Menu.hide()


        # self.Label_Logo.setLayout(HBoxLayout_Logo)

        # QHBoxLayout_Swich.addWidget(QPushButton_Menu, 1,alignment=QtCore.Qt.AlignLeft)

        # QPushButton_NewButton = QtWidgets.QPushButton()
        # QPushButton_NewButton.setIcon(QIcon(":/assets/icon_settings.png"))
        # QPushButton_NewButton.setStyleSheet(StyleSheet_QPushButton)
        # QPushButton_NewButton.clicked.connect(self.Toggle_Drawer_Right)


        QPushButton_Setting = QtWidgets.QPushButton()
        # QPushButton_Setting.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_settings.png"))
        QPushButton_Setting.setIcon(QIcon(":/assets/icon_settings.png"))
        QPushButton_Setting.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Setting.clicked.connect(self.Toggle_Drawer_Right)

        QPushButton_Min = QtWidgets.QPushButton()
        # QPushButton_Min.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_minimize.png"))
        QPushButton_Min.setIcon(QIcon(":/assets/icon_minimize.png"))
        QPushButton_Min.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Min.clicked.connect(self.showMinimized)


        self.QPushButton_Max = QtWidgets.QPushButton()
        # self.QPushButton_Max.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_maximize.png"))
        self.QPushButton_Max.setIcon(QIcon(":/assets/icon_maximize.png"))
        self.QPushButton_Max.setStyleSheet(StyleSheet_QPushButton)
        # self.# QPushButton_Max.clicked.connect( self.showFullScreen)
        self.QPushButton_Max.clicked.connect( self.Page_Screen)



        # QPushButton_Max.clicked.connect( self.showFullScreen)

        QPushButton_Close = QtWidgets.QPushButton()
        # QPushButton_Close.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_close.png"))
        QPushButton_Close.setIcon(QIcon(":/assets/icon_close.png"))
        QPushButton_Close.setStyleSheet(StyleSheet_QPushButton)
        QPushButton_Close.clicked.connect(self.closeEvent)









        # QHBoxLayout_Setting.addWidget(QMenuBar_Menu, 1)
        # QHBoxLayout_Setting.addWidget(QPushButton_Setting, 1)
        QHBoxLayout_Setting.addWidget(QPushButton_Min, 1)
        QHBoxLayout_Setting.addWidget(self.QPushButton_Max, 1)
        QHBoxLayout_Setting.addWidget(QPushButton_Close, 1)










        QHBoxLayout_Title.addWidget(self.QLabel_Switch, 7)
        QHBoxLayout_Title.addWidget(self.QLabel_Setting, 1)

    def Set_PageMenu(self):

        __QVBoxLayout = QtWidgets.QVBoxLayout(self.QLabel_PageMenu)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        # __QVBoxLayout.setAlignment(QtCore.Qt.AlignTop)
        __QVBoxLayout.setSpacing(0)

        QLabel_Log       = QtWidgets.QLabel()
        QLabel_Log.setMinimumHeight(58)
        QLabel_Log.setMaximumHeight(58)
        QLabel_Log.setStyleSheet('background:rgb(37, 41, 48); color:white;')
        QLabel_Menu_List = QtWidgets.QLabel()
        QLabel_Menu_List.setStyleSheet('background:rgb(37, 41, 48); color:white;')
        QLabel_Config    = QtWidgets.QLabel()
        QLabel_Config.setStyleSheet('background:rgb(37, 41, 48); color:white;')


        # QLabel_Title = QtWidgets.QLabel()
        # QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        # QLabel_Title.setMinimumHeight(30)
        # QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Log)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)


        # 系统图标
        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(48, 48)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(42, 42, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        QLabel_Icon.setPixmap(Image_Logo)

        self.QLabel_Title_Name = QtWidgets.QLabel()
        self.QLabel_Title_Name.setAlignment( QtCore.Qt.AlignVCenter)

        self.QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding-right: 10px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 18px; }")
        self.QLabel_Title_Name.setText("哨兵数据分析")
        self.QLabel_Title_Name.hide()

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(self.QLabel_Title_Name, 0)




        QVBoxLayout_Menu_List = QtWidgets.QVBoxLayout(QLabel_Menu_List)
        QVBoxLayout_Menu_List.setSpacing(0)
        QVBoxLayout_Menu_List.setAlignment(QtCore.Qt.AlignTop)
        QVBoxLayout_Menu_List.setContentsMargins(0, 0, 0, 0)
        # ["Media_VideoMonitor", "Media_Voiceprint", "Media_VideoAnalysis"]
        # ["Media_Video", "Media_Voiceprint"]
        Menu_List=[
            {"Menu_ID":1,"Menu_Name":"",        "Menu_Icon":"icon_menu",            "Icon_Command":{"Command":"Toggle_Menu","Page_Name":""},                                                 "Menu_Command":{"Command":"Page_Change","Page_Name":""}},
            {"Menu_ID":2,"Menu_Name":"视频监控","Menu_Icon":"cil-screen-desktop",   "Icon_Command":{"Command":"Page_Change","Page_Name":"Media_VideoMonitor"},                               "Menu_Command":{"Command":"Page_Change","Page_Name":"Media_VideoMonitor"}},
            {"Menu_ID":3,"Menu_Name":"语音声纹","Menu_Icon":"cil-microphone",       "Icon_Command":{"Command":"Page_Change","Page_Name":"Media_VoicePrint"},                                 "Menu_Command":{"Command":"Page_Change","Page_Name":"Media_Voiceprint"}},
            {"Menu_ID":4,"Menu_Name":"视频分析","Menu_Icon":"cil-movie",            "Icon_Command":{"Command":"Page_Change","Page_Name":"Media_VideoAnalysis"},                              "Menu_Command":{"Command":"Page_Change","Page_Name":"Media_VideoAnalysis"}},
            {"Menu_ID":5,"Menu_Name":"社工检索","Menu_Icon":"cil-magnifying-glass", "Icon_Command":{"Command":"Open_Browser","Browser_Url":"http://localhost:8080/Page_Track_Media"},        "Menu_Command":{"Command":"Open_Browser","Browser_Url":"http://localhost:8080/Page_Track_Media"}},
            {"Menu_ID":6,"Menu_Name":"定位服务","Menu_Icon":"cil-location-pin",     "Icon_Command":{"Command":"Open_Browser","Browser_Url":"http://localhost:9300/Page_Track_Media"},        "Menu_Command":{"Command":"Open_Browser","Browser_Url":"http://localhost:8080/Page_Track_Media"}},
            {"Menu_ID":7,"Menu_Name":"指挥调度","Menu_Icon":"cil-task",            "Icon_Command":{"Command": "Permission_Dialog", "Menu_Name": "指挥调度"},                                 "Menu_Command": {"Command": "Permission_Dialog", "Menu_Name": "指挥调度"}},
            {"Menu_ID":8,"Menu_Name":"视频会议","Menu_Icon":"cil-people",          "Icon_Command":{"Command": "Permission_Dialog", "Menu_Name": "视频会议"},                                 "Menu_Command": {"Command": "Permission_Dialog", "Menu_Name": "视频会议"}},
            {"Menu_ID":9,"Menu_Name":"人体检测","Menu_Icon":"cil-user-follow",          "Icon_Command":{"Command": "Permission_Dialog", "Menu_Name": "人体检测"},                                 "Menu_Command": {"Command": "Permission_Dialog", "Menu_Name": "人体检测"}},

        ]
        def  Set_Menu(Menu_Info):
            QLabel_Menu = QLabel_Click_Menu(Menu_Info)
            QLabel_Menu.setStyleSheet("""QLabel{background:rgb(37, 41, 48);border-width:1px;border-radius:0px;padding:0px;color:black;border-style: solid;}QLabel:hover{background-color:rgba(115, 111,110,0.2);border-width: 0px;border-style: solid}QLabel:checked{color:red;}""")
            QLabel_Menu.setMinimumHeight(50)
            QLabel_Menu.setMaximumHeight(50)
            QLabel_Menu.clicked.connect(lambda: self.Page_Change(Menu_Info["Menu_Command"]))
            # QLabel_Menu.clicked.connect(lambda: self.Permission_Dialog(Menu_Info["Menu_Command"]))
            return QLabel_Menu

        for Menu_Info  in Menu_List:
            QVBoxLayout_Menu_List.addWidget(Set_Menu(Menu_Info))

        StyleSheet_QPushButton = """
                          QPushButton{background-position: left center;
                              background-repeat: no-repeat;
                              border: none;
                              border-radius: 3px;
                              border-left: 0px solid transparent;
                              background-color: transparent;
                              padding: 3px;
                              color: rgb(113, 126, 149);}
                          """




        # QPushButton_Config = QtWidgets.QPushButton(QLabel_Config)
        # QPushButton_Config.setGeometry(5,3, 50, 50)
        # QPushButton_Config.setMinimumSize(50,50)
        # QPushButton_Config.setMaximumSize(50,50)
        # # QPushButton_Config.setIcon(QIcon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\cil-settings.png"))
        # QPushButton_Config.setIcon(QIcon(":/assets/cil-settings.png"))
        # QPushButton_Config.setStyleSheet(StyleSheet_QPushButton)
        # QPushButton_Config.clicked.connect(self.Toggle_Drawer_Left)
        #
        #
        # __QLabel_Config= QtWidgets.QLabel(QLabel_Config)
        # __QLabel_Config.setAlignment(QtCore.Qt.AlignVCenter)
        # __QLabel_Config.setGeometry(63,3, 150, 50)
        # __QLabel_Config.setStyleSheet("QLabel {background-color: transparent; padding-right: 10px; margin: 0px; border: 0px;color:white;font-weight: bold;font-size: 13px; }")
        # __QLabel_Config.setText("设置")



        __QVBoxLayout.addWidget(QLabel_Log, 0)
        __QVBoxLayout.addWidget(QLabel_Menu_List, 1)
        # __QVBoxLayout.addWidget(QLabel_Config, 1)


    def Set_Content(self):
        global  Page_Info

        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Content)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)
        # __QHBoxLayout.addWidget(self.Add_Page(""))

        QLabel_Center =  QtWidgets.QLabel()


        self.QStackedLayout_Center = QtWidgets.QStackedLayout(QLabel_Center)
        # Page_Widget_Media_VideoMonitor, Page_Widget_Media_VideoAnalysis

        # Page_List = ['Home', 'Login', 'VPN', "Page_Widget_Media_Voiceprint"]
        Page_List = ["Media_VideoMonitor","Media_VoicePrint","Media_VideoAnalysis"]
        # self.QLabel_Title

        # PP(Page_Info,9)
        self.Page_Change_Index = {}
        for Index, Page in enumerate(Page_List):
            self.Page_Change_Index[Page] = Index
            exec("__Page_%s = Page_Widget_%s.Page_Widget_%s(Page_Info)" % (Page, Page,Page))
            exec("__Page_%s.setMinimumSize(self.QLabel_Content.width(),self.QLabel_Content.height())" % (Page))
            # exec("__Page_%s.Signal_Command.connect(self.EXECUTE_COMMAND)" % (Page))
            exec("self.QStackedLayout_Center.addWidget(__Page_%s)" % (Page))

        # PPP(self.Page_Info["User_Login_Status"])
        # if self.Page_Info["User_Login_Status"] =="Success":
        #     self.MenuBar.show()
        #     self.Stacked_Center.setCurrentIndex(0)
        # else:
        #     self.MenuBar.hide()
        #     self.Stacked_Center.setCurrentIndex(0)

        self.QStackedLayout_Center.setCurrentIndex(0)





        self.QWidget_Right_Drawer = QtWidgets.QWidget()
        self.QWidget_Right_Drawer.setStyleSheet('background:cyan; color:white;')
        self.QWidget_Right_Drawer.setFixedWidth(0)
        # self.QLabel_Title.setAlignment(Qt.AlignCenter)  # 设置文本居中
        self.QWidget_Right_Drawer.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)







        # __QHBoxLayout.addWidget(self.QStackedLayout_Center)
        __QHBoxLayout.addChildLayout(self.QStackedLayout_Center)
        __QHBoxLayout.addWidget(QLabel_Center)
        __QHBoxLayout.addWidget(self.QWidget_Right_Drawer)


    def Set_Left_Drawer(self):
        __QVBoxLayout = QtWidgets.QVBoxLayout(self.QWidget_Left_Drawer)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)


        __QLabel   =  QtWidgets.QLabel("系统设置")
        __QLabel.setStyleSheet('background:rgba(37, 41, 48,1); color:white;font-size:16px')

        __QLabel.setAlignment(QtCore.Qt.AlignCenter)
        __QLabel.setFixedHeight(58)
        __QLabel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        __Component_Config =Component_Config.Component_Config()
        __Component_Config.setStyleSheet('background:rgba(40, 52, 80, 0.6);')


        __QVBoxLayout.addWidget(__QLabel,alignment=QtCore.Qt.AlignTop)
        __QVBoxLayout.addWidget(__Component_Config)

    def Page_Change(self, ChangeParameter):
        PP(('Page_Change', ChangeParameter))
        Page_Index = self.Page_Change_Index[ChangeParameter["Page_Name"]]
        PW(Page_Index)
        try:exec(f'self.__Page_{ChangeParameter["Page_Name"]}.Page_Update(Command["Page_Param"])')
        except:pass
        self.QStackedLayout_Center.setCurrentIndex(Page_Index)

    def Page_Screen(self):
        if self.is_expanded_Screen:
            self.showNormal()
            self.QPushButton_Max.setIcon(QIcon(":/assets/icon_maximize.png"))
            # self.QPushButton_Max.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_maximize.png"))
        else:
            self.showFullScreen()
            self.QPushButton_Max.setIcon(QIcon(":/assets/icon_restore.png"))
            # self.QPushButton_Max.setIcon(QIcon(r"D:\Sentinel Foundation\Bin\Resource\Icons\icon_restore.png"))
        self.is_expanded_Screen = not self.is_expanded_Screen



        # self.is_expanded_Screen

    def Open_Browser(self,BrowserParameter):
        # 指定要打开的网址
        url = QtCore.QUrl(BrowserParameter["Browser_Url"])  # 替换为你想要的网址
        if not QtGui.QDesktopServices.openUrl(url):
            print("无法打开浏览器")

    # def Permission_Dialog(self, DialogParameter):
    #
    #     dialog = Component_Media_PopupDialog.PopupDialog_Test(self)
    #     dialog.exec()

    def Permission_Dialog(self,DialogParameter):
        dialog = Utils_PopupDialog.Utils_Dialog_Normal(self, {
            "Dialog_Title": "哨兵提示",
            "Dialog_Content": "抱歉，该功能为服务器版本才能使用！",
            "Dialog_Execute": ""
        })

        dialog.move(self.geometry().center() - dialog.rect().center())

        dialog.show()
    def Toggle_Menu(self,*args):
        if self.is_expanded_Menu:
            end_width = 0
            self.QLabel_Title_Name.hide()

        else:
            end_width = 200
            self.QLabel_Title_Name.show()
        # end_width = 60 if self.is_expanded_Menu else 200

        self.animation = QtCore.QPropertyAnimation(self.QWidget_Left, b"minimumWidth")
        self.animation.setDuration(200)  # 动画持续时间500ms
        self.animation.setStartValue(self.QWidget_Left.width())
        self.animation.setEndValue(end_width)
        self.animation.start()

        self.is_expanded_Menu = not self.is_expanded_Menu

    def Toggle_Drawer_Left(self):
        end_width = 0 if self.is_expanded_Drawer_Left else 300
        self.animation = QtCore.QPropertyAnimation(self.QWidget_Left_Drawer, b"minimumWidth")
        self.animation.setDuration(200)  # 动画持续时间500ms
        self.animation.setStartValue(self.QWidget_Left_Drawer.width())
        self.animation.setEndValue(end_width)
        self.animation.start()

        self.is_expanded_Drawer_Left = not self.is_expanded_Drawer_Left




    def Toggle_Drawer_Right(self):
        end_width = 0 if self.is_expanded_Drawer_Right else 300
        self.animation = QtCore.QPropertyAnimation(self.QWidget_Right_Drawer, b"minimumWidth")
        self.animation.setDuration(200)  # 动画持续时间500ms
        self.animation.setStartValue(self.QWidget_Right_Drawer.width())
        self.animation.setEndValue(end_width)
        self.animation.start()

        self.is_expanded_Drawer_Right = not self.is_expanded_Drawer_Right





        #  # ---------------------------------------------------------------------------------------- 重写移动事件

    def mouseMoveEvent(self, e: QtGui.QMouseEvent):
        try:
            self._endPos = e.pos() - self._startPos
            self.move(self.pos() + self._endPos)
        except:
            pass

    def mousePressEvent(self, e: QtGui.QMouseEvent):
        try:
            if e.button() == QtCore.Qt.LeftButton:
                self._isTracking = True
                self._startPos = QtCore.QPoint(e.x(), e.y())
        except:
            pass

    def mouseReleaseEvent(self, e: QtGui.QMouseEvent):
        try:
            if e.button() == QtCore.Qt.LeftButton:
                self._isTracking = False
                self._startPos = None
                self._endPos = None
        except:
            pass

    def closeEvent(self, event):
        PP("closeEvent")
        # 在这里你可以做一些清理工作

        # 然后退出应用程序
        # sys.exit()
        QtCore.QCoreApplication.quit()
        # sys.exit()






class QLabel_Click_Menu(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        # Menu_Icon
        self.Menu_Info  = args[0]
        self.setMouseTracking(True)  # 启用鼠标追踪
        self.initUI()

    def initUI(self):
        global Page_Info
        StyleSheet_QPushButton = """
                  QPushButton{background-position: left center;
                      background-repeat: no-repeat;
                      border: none;
                      border-radius: 3px;
                      border-left: 0px solid transparent;
                      background-color: transparent;
                      padding: 3px;
                      color: rgb(113, 126, 149);}
                  """
        __QHBoxLayout = QtWidgets.QHBoxLayout(self)
        __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(13, 3, 0, 0)

        __QPushButton = QtWidgets.QPushButton(self)
        __QPushButton.setGeometry(3,3, 50, 50)
        __QPushButton.setMinimumSize(50,50)
        __QPushButton.setMaximumSize(50,50)
        # __QPushButton.setIcon(QIcon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{ self.Menu_Info['Menu_Icon'] }.png"))
        __QPushButton.setIcon(QIcon(f":/assets/{ self.Menu_Info['Menu_Icon'] }.png"))
        __QPushButton.setStyleSheet(StyleSheet_QPushButton)
        # if  =="":
        __QPushButton.clicked.connect(lambda :Page_Info["Page_Def_List"][self.Menu_Info['Icon_Command']["Command"]](self.Menu_Info['Icon_Command']))

        __QLabel= QtWidgets.QLabel(self)
        __QLabel.setAlignment(QtCore.Qt.AlignVCenter)
        __QLabel.setGeometry(63, 3, 150, 50)
        __QLabel.setStyleSheet("QLabel {background-color: transparent; padding-right: 10px; margin: 0px; border: 0px;color:white;font-weight: bold;font-size: 13px; }")
        __QLabel.setText(self.Menu_Info["Menu_Name"])






    def enterEvent(self, event: QtCore.QEvent) -> None:
        self._is_hovered = True


    def mousePressEvent(self, event):

        if event.button() == QtCore.Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    Window = Windows_Analysis()
    Window.show()
    sys.exit(app.exec())