/****************************************初始化插件***************************************************/ 
$('.single-select').select2({
    theme: 'bootstrap4',
    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
    placeholder: $(this).data('placeholder'),
    allowClear: Boolean($(this).data('allow-clear')),
});

var Choice_Date_Content = 'HalfYear'
var Choice_Date_List = []
var Report_Info_List = []
$('#Sentiment_Alarm_Time_Element').val(Choice_Date_Content).trigger('change');
/**********************************初始化table***************************************************/
var $table = $('#Reports_table').DataTable({
    language: {
      url: '/static/JavaScript/datatable/js/zh-CN.json',
    },
    columnDefs: [
      { className: 'text-center', targets: '_all' },
      {
        title: '操作',
        width: '120px',
        targets: 5,
        render: function (data, type, row) {
            if (row.REPORT_STATUS === 'Finish') {
                return `
                    <button class="btn btn-outline-success btn-edit btn-sm m-1 px-2" onclick=Download_Upload_Info("${row.REPORT_UUID}")>下载报告</button>
                `;
            } else {
                return `
                    <button class="btn btn-outline-info btn-edit btn-sm m-1 px-2" onclick=Upload_Remove_Info("${row.REPORT_UUID}")>删除</button>
                `;
            };
        },
      },
    ],
    columns: [
      { title: '序号', data: 'ID', width: '50px' },
      { title: '创建时间', data: 'REPORT_TIME', width: '150px' },
      { title: '报告标识', data: 'REPORT_UUID', width: '150px'},
      { title: '报告名称', data: 'REPORT_NAME'},
    //   { title: '报告大小', data: 'REPORT_SIZE'},
      { title: '处置状态', data: 'REPORT_STATUS', width: '150px', render: function(data, type, row) {
        if (row.REPORT_STATUS === 'Active') {
            return `<button class="btn btn-warning btn-edit btn-sm m-1 px-2" >执行中</button>`
        } else if (row.REPORT_STATUS === 'Finish') {
            return `<button class="btn btn-warning btn-edit btn-sm m-1 px-2" >已完成</button>`
        } else if (row.REPORT_STATUS === 'Download') {
            return `<button class="btn btn-success btn-edit btn-sm m-1 px-2" >已下载</button>`
        } else {
            return `<button class="btn btn-warning btn-edit btn-sm m-1 px-2" >状态未知</button>`
        };
      }},
    ],
});

/****************************************初始化请求数据***************************************************/ 
function Requests_Sentiment_Report_Info() {
    Loading_Show();
    let Requests_Data = {
        "user_id": this.User_Token,
        "user_token":this.User_Token,
        "data_class": "Sentiment",
        "data_type": 'Service',
        "data_methods": 'return_report_info',
        "data_argument": `{}`,
        "data_kwargs": {
            'Choice_Date_Content':Choice_Date_Content,
            'Choice_Date_List':Choice_Date_List,
        }
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
        .then((Result) => {
            if (Result.Status === 'Success') {
                // Lobibox.notify('success', {
                //     pauseDelayOnHover: true,
                //     size: 'mini',
                //     rounded: true,
                //     delayIndicator: false,
                //     continueDelayOnInactiveTab: false,
                //     position: 'top right',
                //     msg: '删除此项报告成功！'
                // });
                $table.clear();
                $table.rows.add(Result.Report_List);
                $table.draw();
                Report_Info_List = Result.Report_List;
            } else {
                Lobibox.notify('warning', {
                    pauseDelayOnHover: true,
                    size: 'mini',
                    rounded: true,
                    delayIndicator: false,
                    continueDelayOnInactiveTab: false,
                    position: 'top right',
                    msg: '网络出错！'
                });
            };
            Loading_Hide();
        }).catch((err) => {
            Lobibox.notify('warning', {
                pauseDelayOnHover: true,
                size: 'mini',
                rounded: true,
                delayIndicator: false,
                continueDelayOnInactiveTab: false,
                position: 'top right',
                msg: '网络出错！'
            });
            Loading_Hide();
        });
};

/****************************************筛选时间变化***************************************************/ 
$('#Sentiment_Alarm_Time_Element').on('change',function() {
    Choice_Date_Content = this.value;
    console.log('当前筛选的报警请求时间:', Choice_Date_Content);
    if (this.value === 'Other') {
        console.log('打开日期选择框')
    } else {
        console.log('隐藏日期选择框')
    };
});

/****************************************删除此条数据***************************************************/ 
function Upload_Remove_Info(fileindex) {
    console.log('监听删除的上传:',fileindex)
    let indexToRemove = Report_Info_List.findIndex(item => item.REPORT_UUID === fileindex);
    if (indexToRemove !== -1) {
        // 发送请求  修改状态
        let Requests_Data = {
            "user_id": this.User_Token,
            "user_token":this.User_Token,
            "data_class": "Sentiment",
            "data_type": 'Service',
            "data_methods": 'replace_report_info',
            "data_argument": `{}`,
            "data_kwargs": {
                'Report_Info_List':Report_Info_List[indexToRemove],
            }
        };
        __Service_Requests = new Service_Requests("Async",Requests_Data);
        __Service_Requests.callMethod()
            .then((Result) => {
                if (Result.Status === 'Success') {
                    Report_Info_List.splice(indexToRemove, 1);
                    $table.clear();
                    $table.rows.add(Report_Info_List);
                    $table.draw();
                } else {
                   //
                }
            }).catch((err) => {
                //
            });
    };
   
};



// 下载结果
function Download_Upload_Info(fileindex) {
    console.log('监听要下载的:',fileindex)
    let indexTodownload = Report_Info_List.findIndex(item => item.REPORT_UUID === fileindex);
    console.log('indexTodownload:',indexTodownload);
    // 创建一个表单
    const form = document.createElement('form');
    form.action = `https://${window.location.host}/CSC_file_download`; // 设置表单的action
    form.method = 'POST'; // 设置表单的method为POST
    // 创建一个隐藏的输入字段来存储hash_info参数
    const param1  = document.createElement('input');
    param1.type = 'hidden';
    param1.name = 'file_type'; // 参数名
    param1.value = Report_Info_List[indexTodownload].REPORT_FILE_TYPE; // 参数值
    form.appendChild(param1); // 将输入字段添加到表单中
    const param2  = document.createElement('input');
    param2.type = 'hidden';
    param2.name = 'file_time'; // 参数名
    param2.value = Report_Info_List[indexTodownload].REPORT_TIME; // 参数值
    form.appendChild(param2); // 将输入字段添加到表单中
    const param3  = document.createElement('input');
    param3.type = 'hidden';
    param3.name = 'save_name'; // 参数名
    param3.value = Report_Info_List[indexTodownload].REPORT_SAVE_NAME; // 参数值
    form.appendChild(param3); // 将输入字段添加到表单中
    // 将表单添加到文档中（通常是隐藏的）
    document.body.appendChild(form);
    // 提交表单，触发下载
    form.submit();
    // 提交后移除表单
    document.body.removeChild(form);
};