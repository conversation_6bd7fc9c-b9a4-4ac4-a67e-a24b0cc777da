function __init__() {
    //加载Script
    // console.log('Script:',"Function_Core_Config")
    // const Script = document.createElement('script');
    // Script.onload = Script.onreadystateschange = function () {
    //   if (
    //     !this.readyState ||this.readyState == 'loaded' ||this.readyState == 'complete' 
    //   ) {
    //     // 脚本加载完成后执行某些逻辑
    //   }
    // };
    // Script.src = '/static/JavaScript/Utils/Function/Function_Core_Config.js';
    // document.body.appendChild(Script);
    console.log('Script:', "Function_Core_Config");
    const script = document.createElement('script');
    script.onload = function () {
        // 脚本加载完成后执行某些逻辑
        console.log('Function_Core_Config.js loaded successfully');
       
        // return ServerConfig

        // 可以在这里调用 Function_Core_Config.js 中定义的函数或执行其他操作
    };
    script.onerror = function () {
        // 脚本加载失败时的处理逻辑
        console.error('Failed to load Function_Core_Config.js');
    };
    script.src = '/static/JavaScript/Utils/Function/Function_Core_Config.js';
    document.body.appendChild(script);

}