# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,datetime
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
from PySide6.QtWebEngineWidgets import QWebEngineView
import qtawesome as qta
import cv2
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.Toolkit import Service_Print
PP    = Service_Print.Service_Print()
PW    = Service_Print.Service_Print('Warning')
PJ    = Service_Print.Service_Print('Json')
PPP   = Service_Print.Service_Print('Json_Time')
from Bin.System.OS.Resource.CSS import UI_Icons
from Bin.System.OS.Page.Page_Utils import Utils_PopupDialog_Video

sys.path.append(r"D:\Sentinel Foundation\Bin\System\OS\Component")
import Component_Common


from PySide6.QtWidgets import (QApplication, QWidget, QPushButton, QLabel,QGridLayout, QVBoxLayout, QHBoxLayout, QSizePolicy)
from PySide6.QtCore import QPropertyAnimation, QRect, Qt
import qtawesome
class Windows_Media_View(QtWidgets.QWidget):
    Signal_Command = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        try:self.Data = args[0]
        except:pass
        self.InitUI()



    def InitUI(self):
        # self.setWindowFlags(self.windowFlags() & ~QtCore.Qt.WindowMaximizeButtonHint)
        # self.setWindowIcon(QtGui.QIcon(u":/rs/System/Logo.ico"))
        # self.setWindowTitle("哨兵系统")
        self.QVBoxLayout = QtWidgets.QVBoxLayout(self)
        self.QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.QVBoxLayout.setSpacing(0)

        self.QLabel_Menu = QtWidgets.QLabel()
        self.QLabel_Menu.setStyleSheet('background:#112040;border:1px solid #002040;')

        self.QLabel_Content  = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet('background:#112040;border:1px solid #002040;')

        self.QVBoxLayout.addWidget(self.QLabel_Menu, 1)
        self.QVBoxLayout.addWidget(self.QLabel_Content, 11)

        self.Set_Menu()
        self.Set_Content()


    def Set_Menu(self):

        self.Hlayout_Menu = QtWidgets.QHBoxLayout(self.QLabel_Menu)  # 水品布局
        self.Hlayout_Menu.setSpacing(0)  # 内边界
        self.Hlayout_Menu.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Menu_Title   = QtWidgets.QLabel()

        self.QLabel_Menu_Function = QtWidgets.QLabel()
        self.QLabel_Menu_Function.setStyleSheet('background-color:transparent')
        #

        self.QLabel_Menu_System = QtWidgets.QLabel()
        self.QLabel_Menu_System.setStyleSheet('background-color:transparent')
        self.QLabel_Menu_System.setFixedSize(200, 200)

        self.Pixmap_Title = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\System\OS\Resource\Image\System\Media_Title_Min.png")
        self.Pixmap_Title.scaled(80, 180)
        # Pixmap.scaledToWidth(40)
        # Pixmap.scaled(picSize, Qt::KeepAspectRatio)

        # self.Label_Menu_Title.setGeometry(0, 0, 300, 200)
        self.QLabel_Menu_Title.setPixmap(self.Pixmap_Title)
        # self.Label_Menu_Title.setScaledContents(True)

        self.Hlayout_Menu.addWidget(self.QLabel_Menu_Title, 1)
        self.Hlayout_Menu.addWidget(self.QLabel_Menu_Function, 5)
        self.Hlayout_Menu.addWidget(self.QLabel_Menu_System, 1)






        QVBoxLayout_System = QtWidgets.QVBoxLayout(self.QLabel_Menu_System)  # 水品布局
        QVBoxLayout_System.setSpacing(0)  # 内边界
        QVBoxLayout_System.setContentsMargins(0, 0, 0, 0)  # 外边

        self.QLabel_Show_Time_1 = QtWidgets.QLabel()
        self.QLabel_Show_Time_1.setStyleSheet("""QLabel{color:rgb(139,134,78);font-size:12px; background-color: transparent;}""")
        self.QLabel_Show_Time_1.setFixedSize(200, 200)

        self.QPushButton_Test   = QtWidgets.QPushButton(self.QLabel_Show_Time_1)
        self.QPushButton_Test.setGeometry(0, 0, 88, 38)
        self.QPushButton_Test.setStyleSheet("""QPushButton{color:rgb(255,255,255);font-size:12px; background-color: transparent;}""")
        self.QPushButton_Test.setText("弹窗")
        # self.QPush.clicked.connect(self.Set_PopupDialog_Video)
        # self.QPushButton_Test.clicked.connect(self.toggle_sidebar)


        self.QLabel_Show_Time= QtWidgets.QLabel()
        # self.QLabel_Show_Time.resize(168, 28)
        self.QLabel_Show_Time.setStyleSheet("""QLabel{color:rgb(255,255,255);font-size:15px; background-color: transparent;}""")
        self.QLabel_Show_Time.setText(str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))

        QVBoxLayout_System.addWidget(self.QLabel_Show_Time,1)
        QVBoxLayout_System.addWidget(self.QLabel_Show_Time_1,1)

        # 设置计时器



        # self.QTimer_Count = QtCore.QTimer(self)
        # self.QTimer_Count.timeout.connect(self.Update_Timer_Date)
        # self.QTimer_Count.start(1000)  # 每秒更新一次


    def Set_Content(self):
        HLayout = QtWidgets.QHBoxLayout(self)
        HLayout.setContentsMargins(0, 0, 0, 0)
        HLayout.setSpacing(0)
        # Page_Service_Intelligence_Article
        self.QLabel_Content.setLayout(HLayout)

        __DynamicLabelWindow = DynamicLabelWindow()
        # __Shadow_Mssion_SameScreen_View.show()
        HLayout.addWidget(__DynamicLabelWindow)
        # __DynamicLabelWindow=DynamicLabelWindow(self.QLabel_Content)
        # __DynamicLabelWindow.show()




class DynamicLabelWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("动态标签演示")
        self.resize(800, 600)

        # 初始化界面
        self.Init_UI()

        # 初始化动画
        self.init_animation()

        # 初始显示4个标签
        self.update_labels(4)

    def Init_UI(self):
        # 主布局结构
        self.QVLayout = QVBoxLayout(self)
        self.QVLayout.setContentsMargins(0, 0, 0, 0)
        self.QVLayout.setSpacing(0)



        # 主显示区域
        self.main_container = QWidget()
        self.main_container.setStyleSheet("background: #f0f0f0;")
        self.grid_layout = QGridLayout(self.main_container)
        self.grid_layout.setSpacing(2)
        self.QVLayout.addWidget(self.main_container)

        # 右侧抽屉
        self.drawer = QWidget(self)
        self.drawer.setStyleSheet("""
            background: white;
            border-left: 2px solid #0078d4;
        """)
        self.init_drawer()
        # 控制按钮栏
        self.Set_Control_Bar()
        # 抽屉按钮
        # self.drawer_button = QPushButton("Toggle Drawer", self)
        # self.drawer_button.clicked.connect(self.toggle_drawer)
        # self.drawer_button.move(self.width() - 300, 10)  # 初始位置在抽屉内

    def Set_Control_Bar(self):
        """初始化控制按钮栏"""
        control_bar = QWidget()
        control_bar.setFixedHeight(50)
        control_bar.setStyleSheet("background: #e0e0e0;")

        btn_layout = QHBoxLayout(control_bar)
        btn_layout.setContentsMargins(10, 2,2, 2)

        # 创建数值按钮
        self.num_buttons = []
        for num in [2, 4, 8, 16, 32,"",""]:
            btn = QPushButton(str(num))
            btn.setFixedSize(60, 30)
            if "" =="Toggle Drawer":
                btn.clicked.connect(self.toggle_drawer)
            else:

                btn.clicked.connect(lambda _, n=num: self.update_labels(n))
            btn.setStyleSheet("""
                QPushButton {
                    background: #0078d4;
                    color: white;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background: #006cbd;
                }
            """)
            btn_layout.addWidget(btn)
            self.num_buttons.append(btn)





        self.QPushButton_Test = QPushButton("联测")
        self.QPushButton_Test.clicked.connect(self.Test_Function)
        self.QPushButton_Test.setFixedSize(60, 30)
        self.QPushButton_Test.setStyleSheet("""QPushButton {background: #0078d4;color: white; border-radius: 5px;} """)







        self.drawer_button = QPushButton("弹窗")
        self.drawer_button.clicked.connect(self.toggle_drawer)
        self.drawer_button.setFixedSize(60, 30)
        self.drawer_button.setStyleSheet("""QPushButton {background: #0078d4;color: white; border-radius: 5px;} """)



        btn_layout.addWidget(self.QPushButton_Test)
        btn_layout.addWidget(self.drawer_button)
        QPushButton_Placeholder = QPushButton("")
        QPushButton_Placeholder.setStyleSheet("""QPushButton {background: transparent;color: white;border: 0px;} """)
        btn_layout.addWidget(QPushButton_Placeholder)
        self.QVLayout.addWidget(control_bar)

    def init_drawer(self):
        """初始化抽屉内容"""
        drawer_layout = QVBoxLayout(self.drawer)
        self.drawer_label = QLabel("音视频管理设置\n（多种功能页面）", self.drawer)
        self.drawer_label.setAlignment(Qt.AlignCenter)
        self.drawer_label.setStyleSheet("font-size: 16px; color: #333;")


        drawer_layout.addWidget(self.drawer_label)

    def init_animation(self):
        """初始化抽屉动画"""
        self.animation = QPropertyAnimation(self.drawer, b"geometry")
        self.animation.setDuration(300)
        self.animation.finished.connect(self.handle_animation_end)
        self.is_open = False
        self.update_drawer_position()

    def update_labels(self, count):
        """更新标签显示数量"""
        """更新标签显示数量"""
        # 清除现有标签
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 创建新标签
        colors = ['#FFB6C1', '#98FB98', '#87CEEB', '#DDA0DD']
        for i in range(count):
            # label = QLabel(f"No Signal")

            label  = QLabel_Video_Channel({"ID":f"{i}","Icon": 'fa5.play-circle', 'Name': 'No Signal',"Size":68})
            label.Clicked_More.connect(self.Service_Command)
            label.setStyleSheet(f"""
                    background: rgba(0,0,0,0.8);
                    border: 2px solid #2F4F4F;
                    color:white;
                    font-size: 18px;
                    padding: 0px;
                """)
            label.setAlignment(Qt.AlignCenter)
            label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            label.setMinimumSize(80, 60)

            # 动态计算布局位置
            row = i // self.get_column_count(count)
            col = i % self.get_column_count(count)
            self.grid_layout.addWidget(label, row, col)

        # 更新布局参数
        self.adjust_layout_params(count)

    def get_column_count(self, total):
        """根据总数计算合适的列数"""
        if total <= 4:
            return 2
        elif total <= 8:
            return 4
        elif total <= 16:
            return 4
        else:
            return 8

    def adjust_layout_params(self, count):
        """调整布局参数"""
        cols = self.get_column_count(count)
        rows = (count + cols - 1) // cols

        # 清除现有的行和列伸缩设置
        for i in range(self.grid_layout.rowCount()):
            self.grid_layout.setRowStretch(i, 0)
        for i in range(self.grid_layout.columnCount()):
            self.grid_layout.setColumnStretch(i, 0)

        # 设置新的行和列伸缩比例
        for r in range(rows):
            self.grid_layout.setRowStretch(r, 1)
        for c in range(cols):
            self.grid_layout.setColumnStretch(c, 1)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.main_container.setGeometry(0, 50, self.width(), self.height() - 50)
        self.update_drawer_position()
        # self.drawer_button.move(self.width() - 300, 10)  # 更新按钮位置

    def update_drawer_position(self):
        x_pos = self.width() - 300 if self.is_open else self.width()
        self.drawer.setGeometry(x_pos, 50, 300, self.height() - 50)

    def toggle_drawer(self):
        self.is_open = not self.is_open
        new_width = self.width() - 300 if self.is_open else self.width()
        self.main_container.setFixedWidth(new_width)

        # 设置动画参数
        start_value = QRect(
            self.width() if self.is_open else self.width() - 300,
            0,
            300,
            self.height() - 50
        )
        end_value = QRect(
            self.width() - 300 if self.is_open else self.width(),
            0,
            300,
            self.height() - 50
        )

        if self.is_open:
            self.drawer.show()

        self.animation.stop()
        self.animation.setStartValue(start_value)
        self.animation.setEndValue(end_value)
        self.animation.start()

    def handle_animation_end(self):
        if not self.is_open:
            self.drawer.hide()
            self.main_container.setFixedWidth(self.width())


    def Service_Command(self,Command):
        PP(("Command",Command))
        self.toggle_drawer()


    def Test_Function(self):
        PP("Test_Function")
        self.__Joint_Survey_Test = Joint_Survey_Test()
        self.__Joint_Survey_Test.start()






class QLabel_Video_Channel(QtWidgets.QLabel):
    Clicked_More = QtCore.Signal(dict)

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        self.QLabel_Info = args[0]
        self.setMouseTracking(True)  # 启用鼠标追踪
        self._is_hovered = False

        self._is_Recode = False


        # , "Size": 98
        self.setAlignment(QtCore.Qt.AlignCenter  )
        # 创建两个 QLabel
        self.QLabel_Icon = QtWidgets.QLabel( )
        self.QLabel_Icon.setAlignment(QtCore.Qt.AlignBottom |QtCore.Qt.AlignHCenter)
        font_awesome_icon = qtawesome.icon(self.QLabel_Info["Icon"], color='#ffffff', size=self.QLabel_Info["Size"])
        self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"], self.QLabel_Info["Size"]))
        self.QLabel_Icon .setStyleSheet('''QLabel{background:transparent;border:0px solid #002040;transform: rotate(90deg)}''')
        self.QLabel_Name = QtWidgets.QLabel(self.QLabel_Info["Name"])
        self.QLabel_Name.setAlignment( QtCore.Qt.AlignTop |QtCore.Qt.AlignHCenter )
        # self.QLabel_Name.setAlignment(Qt.AlignCenter)
        self.QLabel_Name.setStyleSheet('background:transparent;border:0px solid #002040;color:#ffffff')

        self.QLabel_Control_Bar =  QtWidgets.QLabel( self)
        self.QLabel_Control_Bar.setGeometry(0,0,self.width(),40)
        # self.QLabel_Control_Bar.setStyleSheet('background:rgba(0,0,0,0.3);border:0px solid #002040;color:#ffffff')
        self.QLabel_Control_Bar.setStyleSheet('background:rgba(255,255,255,0.3);border:0px solid #002040;color:#ffffff')
        self.QLabel_Control_Bar.hide()

        self.Qlayout_Control_Bar = QHBoxLayout(self.QLabel_Control_Bar)
        self.Qlayout_Control_Bar.setContentsMargins(2, 2, 2, 2)
        QPushButton_Placeholder = QPushButton("")
        QPushButton_Placeholder.setFixedSize(600, 20)
        QPushButton_Placeholder.setStyleSheet("""QPushButton {background: transparent;color: white;border: 0px;} """)
        self.Qlayout_Control_Bar.addWidget(QPushButton_Placeholder)
        # for num in ["数源", "截图" ,"录像", "对讲","控制", "设置"]:mdi.video-box"
        Control_Buttons=[
            {"Icon":"ri.stop-circle-line","Command":"Open_ChannelMore","Size":23},
        ]

        #  ---------------------------- 播放键
        self.QLabel_Click_Play = Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)
        self.QLabel_Click_Play.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Click_Play.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Start"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_Play)
        #  ---------------------------- 录像按键
        self.QLabel_Click_Recode = Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("mdi.database-arrow-down", color='white', size=28)
        self.QLabel_Click_Recode.setPixmap(Icon.pixmap(28, 28))

        self.QLabel_Click_Recode.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Recode_Start"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_Recode)
        #  ---------------------------- 录像按键
        self.QLabel_Click_ScreenShot= Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("fa5s.camera", color='white', size=23)
        self.QLabel_Click_ScreenShot.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Click_ScreenShot.clicked.connect(lambda: self.Service_Command({"Command": "Play_Start"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_ScreenShot)

        #  ---------------------------- 录像按键
        self.QLabel_Click_Talk= Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("fa6s.walkie-talkie", color='white', size=23)
        self.QLabel_Click_Talk.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Click_Talk.clicked.connect(lambda: self.Service_Command({"Command": "Play_Start"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_Talk)
        #  ---------------------------- 录像按键
        self.QLabel_Click_Talk= Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("ri.volume-mute-fill", color='white', size=23)
        self.QLabel_Click_Talk.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Click_Talk.clicked.connect(lambda: self.Service_Command({"Command": "Play_Start"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_Talk)

        self.QLabel_Click_More= Component_Common.Component_Common_QLabel_Click()
        Icon = qtawesome.icon("ri.more-fill", color='white', size=23)
        self.QLabel_Click_More.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Click_More.clicked.connect(lambda: self.Service_Command({"Command": "Channel_More"}))
        self.Qlayout_Control_Bar.addWidget(self.QLabel_Click_More)

        self.QLabel_Recode_Status = QtWidgets.QLabel(self)
        Icon = qtawesome.icon("mdi.stop-circle-outline", color='red', size=23)
        self.QLabel_Recode_Status.setPixmap(Icon.pixmap(23, 23))
        self.QLabel_Recode_Status.setAlignment(QtCore.Qt.AlignCenter  )
        self.QLabel_Recode_Status.setStyleSheet('''QLabel{background:transparent;border:0px solid #002040;}''')

        self.QLabel_Recode_Status.move(8, self.height() - 40)
        self.QLabel_Recode_Status.hide()

        self.QLabel_Recode_Time = QtWidgets.QLabel(self)
        self.QLabel_Recode_Time.setText("00:00:00")
        self.QLabel_Recode_Time.setStyleSheet('''QLabel{background:transparent;border:0px solid #002040;font-size:13px}''')
        self.QLabel_Recode_Time.move(38, self.height() - 58)
        self.QLabel_Recode_Time.hide()
        # self.QLabel_Recode_Status.hide()
        # self.QLabel_Recode_Status.raise_()

        #
        # for icon_name in ["ri.stop-circle-line", "fa5s.camera","mdi6.content-save-edit-outline","fa6s.walkie-talkie","ri.settings-3-line"]: #"mdi6.content-save-edit-outline","ri.video-download-line","fa6s.walkie-talkie","ri.settings-3-line"]:
        #     btn = Component_Common.Component_Common_QLabel_Click()
        #     btn.setAlignment(QtCore.Qt.AlignBottom |QtCore.Qt.AlignHCenter)
        #     btn.setFixedSize(40, 20)
        #     font_awesome_icon = qtawesome.icon(icon_name, color='white', size=23)
        #     btn.setPixmap(font_awesome_icon.pixmap(23 , 23 ))
        #     # btn.setIcon(qta.icon(icon_name, color="white",size=98))
        #     btn.clicked.connect(lambda : self.Service_Command({"":""}))
        #     btn.setStyleSheet("""
        #         QLabel {
        #             background: transparent;
        #             color: white;
        #             border-radius: 5px;
        #         }
        #         QLabel:hover {
        #             background: #006cbd;
        #         }
        #     """)
        #     btn_layout.addWidget(btn)






        # 创建一个垂直布局
        QVBoxLayout = QtWidgets.QVBoxLayout(self)
        QVBoxLayout.setContentsMargins(0, 0, 0, 0)
        QVBoxLayout.setSpacing(0)



        # QHBoxLayout_Right_2 = QtWidgets.QHBoxLayout(self.QLabel_Right_2)  # 水品布局
        # QHBoxLayout_Right_2.setSpacing(0)  # 内边界
        # QHBoxLayout_Right_2.setContentsMargins(0, 0, 0, 0)  # 外边

        # self.video_label.setGeometry(10, 10, 640, 380)
        # self.video_label.setFixedSize(640, 380)
        #
        # self.stop_button = QtWidgets.QPushButton('Stop', self)
        # self.stop_button.setGeometry(0, 0, 100, 30)
        # self.stop_button.clicked.connect(self.stop_video)
        #

        #
        # QHBoxLayout_Right_2.addWidget(self.video_label)
        # 将两个 QLabel 添加到垂直布局中

        if self.QLabel_Info["ID"] == "0":
            self.video_source = ""
            self.video_label = QLabel()

            # self.video_thread = VideoCaptureThread(self.video_source)
            # self.video_thread.frame_updated.connect(self.update_frame)
            # self.video_thread.start()
            # QVBoxLayout.addWidget(self.QLabel_Control_Bar, 1)
            QVBoxLayout.addWidget(self.video_label,9)
        else:

            QVBoxLayout.addWidget(self.QLabel_Icon, 1)
            QVBoxLayout.addWidget(self.QLabel_Name, 1)



    def Service_Command(self,Command):
        PP(("Command",Command))
        if Command["Command"] =="Channel_Start":
            Icon = qtawesome.icon("fa6.circle-play", color='green', size=23)
            self.QLabel_Click_Play.setPixmap(Icon.pixmap(23, 23))
            self.QLabel_Click_Play.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Stop"}))

        if Command["Command"] =="Channel_Stop":
            # Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)
            Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)
            self.QLabel_Click_Play.setPixmap(Icon.pixmap(23, 23))
            self.QLabel_Click_Play.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Start"}))

        if Command["Command"] == "Channel_Recode_Start":
            # Icon = qtawesome.icon("fa6.circle-play", color='white', size=23)
            # Icon = qtawesome.icon("mdi.stop-circle-outline", color='red', size=23)
            # self.QLabel_Click_Recode.setPixmap(Icon.pixmap(23, 23))
            # 创建一个QTimer定时器
            self.Time_Count=0
            self.color1 = "white"
            self.color2 = "red"
            self.QLabel_Recode_Status.raise_()
            self.QLabel_Recode_Time.raise_()
            self.QLabel_Click_Recode.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Recode_Stop"}))
            self.current_color =  self.color1
            self._is_Recode = True
            self.timer = QtCore.QTimer(self)
            self.timer.timeout.connect(self.Toggle_Recode)
            self.timer.start(1000)  # 每500毫秒切换一次颜色

        if Command["Command"] == "Channel_Recode_Stop":
            self._is_Recode = False
            # Icon = qtawesome.icon("mdi.database-arrow-down", color='white', size=23)
            # self.QLabel_Click_Recode.setPixmap(Icon.pixmap(23, 23))

            self.QLabel_Click_Recode.clicked.connect(lambda: self.Service_Command({"Command": "Channel_Recode_Start"}))
            #

        if Command["Command"] =="Channel_More":
            self.Clicked_More.emit(self.QLabel_Info)

            self.update()



    def Toggle_Recode(self):
        # 切换颜色
        if self._is_Recode==True:
            # self.QLabel_Recode_Status.move(10, self.height() - 40)
            self.QLabel_Recode_Status.show()
            self.QLabel_Recode_Time.show()
            self.current_color = self.color2 if self.current_color == self.color1 else self.color1
            Icon = qta.icon("mdi.stop-circle-outline", color=self.current_color, size=25)
            self.QLabel_Recode_Status.setPixmap(Icon.pixmap(25,25))
            self.QLabel_Recode_Status.move(-18, self.height() - 40)
            self.QLabel_Recode_Status.raise_()

            self.Time_Count += 1
            # 假设每秒累加1次
            seconds = self.Time_Count

            # 计算分钟数
            minutes = seconds // 60

            seconds %= 60

            if len(str(seconds)) < 2:
                Seconds_Str = "0" + str(seconds)
            else:
                Seconds_Str = str(seconds)

            # 计算小时数
            hours = minutes // 60

            if len(str(hours)) < 2:
                Hours_Str = "0" + str(hours)
            else:
                Hours_Str = str(hours)

            minutes %= 60

            if len(str(minutes)) < 2:
                Minutes_Str = "0" + str(minutes)
            else:
                Minutes_Str = str(minutes)

            # print(f"运行了 {Seconds_Str} 秒")
            # print(f"运行了 {Minutes_Str} 分钟")
            # print(f"运行了 {Hours_Str} 小时")

            self.QLabel_Recode_Time.setText("%s:%s:%s" % (Hours_Str, Minutes_Str, Seconds_Str))








            self.QLabel_Recode_Time.move(48, self.height() - 39)
            self.QLabel_Recode_Time.raise_()











        else:
            self.QLabel_Recode_Status.hide()
            self.QLabel_Recode_Time.hide()
            self.timer.stop()

    @Slot()
    def stop_video(self):
        self.video_thread.stop()
        self.video_thread.quit()
        self.video_thread.wait()
        self.close()


    @Slot(QtGui.QImage)
    def update_frame(self, frame):
        self.video_label.setPixmap(QtGui.QPixmap.fromImage(frame))
        self.video_label.setScaledContents(True)
        # pass



    def enterEvent(self, event: QtCore.QEvent) -> None:







        self._is_hovered = True
        # self.setPalette(self.hover_palette)  # 设置悬停调色板
        font_awesome_icon = qtawesome.icon(self.QLabel_Info["Icon"], color='#6495ed', size=self.QLabel_Info["Size"])

        # QLabel_Service_AI = QLabel_Click_Icon({"Icon": 'fa.viacoin', 'Name': 'AI模块',"Size":78}, self.QLabel_Status)
        # 创建一个变换对象


        self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"]+10, self.QLabel_Info["Size"]+10))
        self.QLabel_Name.setStyleSheet('background:transparent;color:#ffffff;border:0px solid #002040;color:#6495ed')
        self.QLabel_Control_Bar.setMinimumSize(self.width(), 40)
        self.QLabel_Control_Bar.raise_()
        self.QLabel_Control_Bar.show()

        # self.video_label.setMinimumSize(self.height(), self.width(), )
        self.update()  # 更新显示

    def leaveEvent(self, event: QtCore.QEvent) -> None:
        self._is_hovered = False
        font_awesome_icon = qtawesome.icon(self.QLabel_Info["Icon"], color='#ffffff', size=self.QLabel_Info["Size"])
        self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(self.QLabel_Info["Size"], self.QLabel_Info["Size"]))
        self.QLabel_Name.setStyleSheet('background:transparent;border:0px solid #002040;color:#ffffff')
        self.QLabel_Control_Bar.setMinimumSize(self.width(), 40)
        self.QLabel_Control_Bar.hide()
        # self.video_label.setMinimumSize(self.height(), self.width(), )
        self.update()  # 更新显示

    def mousePressEvent(self, event):
        # font_awesome_icon = icon(self.QLabel_Info["Icon"], color='red', size=108)
        # self.QLabel_Icon.setPixmap(font_awesome_icon.pixmap(132, 132))
        # self.QLabel_Name.setStyleSheet('background:transparent;color:red;border:0px solid #002040;')
        pass
        # if event.button() == QtCore.Qt.LeftButton:
        #     self.clicked.emit()
        # super().mousePressEvent(event)

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # 保存当前变换
        painter.save()

        # 应用旋转变换
        transform = QtGui.QTransform()
        transform.rotate(270)
        painter.setTransform(transform)

        # 绘制文本
        rect = QtCore.QRect(QtCore.QPoint(0, 0), self.size())
        painter.drawText(rect, QtCore.Qt.AlignCenter, self.text())

        # 恢复原始变换
        painter.restore()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        # 调整QLabel_Control_Bar的尺寸
        self.QLabel_Control_Bar.resize(self.width(), 40)
        # 调整内部按钮的尺寸和位置
        # self.adjust_control_bar_buttons()
        self.QLabel_Recode_Status.move(8, self.height() - 40)
        self.QLabel_Recode_Status.raise_()
        self.QLabel_Recode_Time.move(38, self.height() - 58)
        self.QLabel_Recode_Time.raise_()


    def adjust_control_bar_buttons(self):
        # 获取QLabel_Control_Bar的可用宽度
        available_width = self.QLabel_Control_Bar.width()
        # 获取按钮的数量
        button_count = self.Qlayout_Control_Bar.count()
        # 计算每个按钮的宽度
        button_width = available_width // button_count
        # 遍历所有按钮并调整尺寸
        for i in range(button_count):
            button = self.Qlayout_Control_Bar.itemAt(i).widget()
            if button:
                button.setFixedSize(button_width, 30)

#  ---------------------------------------------------------------------------------------- 视频服务
class VideoCaptureThread(QtCore.QThread):
    frame_updated = QtCore.Signal(QtGui.QImage)

    def __init__(self, video_source):
        super().__init__()
        # self.video_source = "rtsp://admin:csc888888!@192.168.123.119:554/Streaming/Channels/101"
        # self.video_source = "http://132.232.201.7/live/livestream.flv"
        self.video_source = r"D:\Sentinel Foundation\Bin\System\OS\Page\Page_Utils\1.mp4"
        self.video_source = r"http://43.136.176.127:9311/rtp/34020000001320000003_34020000001310000103.live.flv?originTypeStr=rtp_push"
        self.video_source = r"rtsp://admin:csc2024!@192.168.123.199:554/h264/ch1/main/av_stream"
        # self.video_source = "http://43.136.176.127:9311/rtp/34020000002000000001_34020000001320000001/hls.m3u8"
        # self.video_source = "http://132.232.201.7/live/livestream.flv"
        # self.video_source = 0
        self.cap = cv2.VideoCapture( self.video_source)
        self.running = True

    def run(self):
        while self.running and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break
            # Convert the frame from BGR to RGB format
            frame_resized = cv2.resize(frame, (640, 380))
            rgb_frame = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            # Convert the frame to QImage
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            # convert_to_Qt_format = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            convert_to_Qt_format = QtGui.QImage(rgb_frame.data, 640, 380, bytes_per_line, QtGui.QImage.Format_RGB888)
            # Emit the frame to the main thread
            qimg_resized = convert_to_Qt_format.scaled(640, 380, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.frame_updated.emit(qimg_resized)

    def stop(self):
        self.running = False
        self.cap.release()



#  ---------------------------------------------------------------------------------------- 系统联测
class Joint_Survey_Test(QtCore.QThread):
    def __init__(self, *args):
        super().__init__()
        # self.Url    =  'http://192.168.123.242:9311/Service_Interface'
    def run(self):
        pass

if __name__ == "__main__":
    # app = QApplication(sys.argv)
    # window = DynamicLabelWindow()
    # window.show()
    # sys.exit(app.exec())
    App = QApplication(sys.argv)
    __Windows_Media_View = Windows_Media_View()
    __Windows_Media_View.resize(1920,1080)
    __Windows_Media_View.show()
    sys.exit(App.exec())

