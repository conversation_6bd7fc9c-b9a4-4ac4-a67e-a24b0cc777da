import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Checkbox,
  Radio,
  Row,
  Col,
  Collapse,
  Space,
  message,
  Modal,
  Form,
  InputNumber,
  Typography,
  Descriptions,
  Avatar,
  Tooltip,
  Table
} from 'antd';
import {
  ShoppingCartOutlined,
  CreditCardOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  VerticalAlignTopOutlined,
  MinusOutlined,
  PlusOutlined,
  EditOutlined
} from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Counter_Service.css';

const { Panel } = Collapse;
const { Group: CheckboxGroup } = Checkbox;
const { Group: RadioGroup } = Radio;
const { Text, Title } = Typography;
const { TextArea } = Input;

// 数据类型定义
interface ServiceItem {
  id: string;
  name: string;
  platform: string;
  type: string;
  status: string;
  number: number;
  argument: {
    remark: string;
    price: number;
    condition: string[];
    sign: string;
    url?: string;
    comment_list?: any[];
  };
}

interface ServiceData {
  [type: string]: {
    [platform: string]: ServiceItem[];
  };
}

interface UserInfo {
  user_name: string;
  user_type: string;
  user_phone: string;
  user_email: string;
  user_charger: number;
}

interface ContactInfo {
  name: string;
  phone: string;
  address: string;
}

const Page_Counter_Service: React.FC = () => {
  // 状态管理
  const [activeOperation, setActiveOperation] = useState<string>('account');
  const [loading, setLoading] = useState<boolean>(false);
  const [serviceData, setServiceData] = useState<ServiceData>({});
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [selectedCounterPlatform, setSelectedCounterPlatform] = useState<string>('');
  const [targetUrl, setTargetUrl] = useState<string>('');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [creditModalVisible, setCreditModalVisible] = useState<boolean>(false);
  const [checkoutModalVisible, setCheckoutModalVisible] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    name: '',
    phone: '',
    address: ''
  });
  const [showBackToTop, setShowBackToTop] = useState<boolean>(false);
  const [currentEditItem, setCurrentEditItem] = useState<ServiceItem | null>(null);
  const [commentContent, setCommentContent] = useState<string>('');
  const [rechargeAmount, setRechargeAmount] = useState<string>('');

  // 初始化数据
  useEffect(() => {
    loadServiceData();
    loadUserInfo();
    
    // 监听滚动事件，显示/隐藏回到顶部按钮
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 加载服务数据
  const loadServiceData = async () => {
    setLoading(true);
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'Network',
      //   data_type: 'Price',
      //   data_methods: 'get_redis_mission',
      //   data_argument: '{}',
      //   data_kwargs: '{}'
      // };
      // const response = await serviceRequests.Async(requestData);
      // const processedData = initMissionList(response);
      // setServiceData(processedData);

      // 模拟数据 - 基于原始HTML中的完整平台列表
      const mockServiceData: ServiceData = {
        account: {
          facebook: [
            {
              id: '1001',
              name: 'Facebook个人账号',
              platform: 'facebook',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的Facebook个人账号，适用于个人营销',
                price: 50,
                condition: ['1', '10'],
                sign: 'common'
              }
            },
            {
              id: '1002',
              name: 'Facebook企业页面',
              platform: 'facebook',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的Facebook企业页面，适用于品牌推广',
                price: 100,
                condition: ['1', '5'],
                sign: 'common'
              }
            }
          ],
          twitter: [
            {
              id: '1003',
              name: 'Twitter认证账号',
              platform: 'twitter',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的Twitter账号，适用于信息传播',
                price: 60,
                condition: ['1', '10'],
                sign: 'common'
              }
            }
          ],
          youtube: [
            {
              id: '1004',
              name: 'YouTube频道账号',
              platform: 'youtube',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的YouTube频道账号，适用于视频发布',
                price: 80,
                condition: ['1', '5'],
                sign: 'common'
              }
            }
          ],
          odnoklassniki: [
            {
              id: '1005',
              name: 'Odnoklassniki账号',
              platform: 'odnoklassniki',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的Odnoklassniki账号，适用于俄语市场',
                price: 45,
                condition: ['1', '8'],
                sign: 'common'
              }
            }
          ],
          discuss: [
            {
              id: '1006',
              name: 'Discuss论坛账号',
              platform: 'discuss',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的Discuss论坛账号，适用于社区讨论',
                price: 35,
                condition: ['1', '15'],
                sign: 'common'
              }
            }
          ],
          vkontakte: [
            {
              id: '1007',
              name: 'VKontakte账号',
              platform: 'vkontakte',
              type: 'account',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供已认证的VKontakte账号，适用于俄语社交',
                price: 40,
                condition: ['1', '12'],
                sign: 'common'
              }
            }
          ]
        },
        counter: {
          facebook: [
            {
              id: '2001',
              name: 'Facebook点赞服务',
              platform: 'facebook',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Facebook帖子提供真实用户点赞',
                price: 0.01,
                condition: ['100', '10000'],
                sign: 'common'
              }
            },
            {
              id: '2002',
              name: 'Facebook评论服务',
              platform: 'facebook',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Facebook帖子提供真实用户评论',
                price: 0.5,
                condition: ['10', '100'],
                sign: 'comment'
              }
            }
          ],
          twitter: [
            {
              id: '2003',
              name: 'Twitter点赞服务',
              platform: 'twitter',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Twitter推文提供真实用户点赞',
                price: 0.02,
                condition: ['100', '10000'],
                sign: 'common'
              }
            },
            {
              id: '2004',
              name: 'Twitter评论服务',
              platform: 'twitter',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Twitter推文提供真实用户评论',
                price: 0.3,
                condition: ['20', '200'],
                sign: 'comment'
              }
            }
          ],
          youtube: [
            {
              id: '2005',
              name: 'YouTube观看服务',
              platform: 'youtube',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的YouTube视频提供真实观看量',
                price: 0.005,
                condition: ['1000', '100000'],
                sign: 'common'
              }
            },
            {
              id: '2006',
              name: 'YouTube点赞服务',
              platform: 'youtube',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的YouTube视频提供真实用户点赞',
                price: 0.02,
                condition: ['50', '5000'],
                sign: 'common'
              }
            }
          ],
          odnoklassniki: [
            {
              id: '2007',
              name: 'Odnoklassniki点赞服务',
              platform: 'odnoklassniki',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Odnoklassniki帖子提供真实用户点赞',
                price: 0.015,
                condition: ['50', '5000'],
                sign: 'common'
              }
            }
          ],
          discuss: [
            {
              id: '2008',
              name: 'Discuss回复服务',
              platform: 'discuss',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Discuss帖子提供真实用户回复',
                price: 0.8,
                condition: ['5', '50'],
                sign: 'comment'
              }
            }
          ],
          vkontakte: [
            {
              id: '2009',
              name: 'VKontakte点赞服务',
              platform: 'vkontakte',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的VKontakte帖子提供真实用户点赞',
                price: 0.012,
                condition: ['100', '8000'],
                sign: 'common'
              }
            }
          ],
          telegram: [
            {
              id: '2010',
              name: 'Telegram转发服务',
              platform: 'telegram',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Telegram消息提供真实用户转发',
                price: 0.02,
                condition: ['50', '5000'],
                sign: 'common'
              }
            },
            {
              id: '2011',
              name: 'Telegram点赞服务',
              platform: 'telegram',
              type: 'counter',
              status: 'Active',
              number: 0,
              argument: {
                remark: '为您的Telegram帖子提供真实用户点赞',
                price: 0.015,
                condition: ['100', '10000'],
                sign: 'common'
              }
            }
          ]
        },
        phone: {
          mobile: [
            {
              id: '3001',
              name: '移动流量卡',
              platform: 'mobile',
              type: 'phone',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供移动网络流量卡，支持全国使用',
                price: 30,
                condition: ['1', '10'],
                sign: 'common'
              }
            }
          ],
          unicom: [
            {
              id: '3002',
              name: '联通流量卡',
              platform: 'unicom',
              type: 'phone',
              status: 'Active',
              number: 0,
              argument: {
                remark: '提供联通网络流量卡，支持全国使用',
                price: 25,
                condition: ['1', '10'],
                sign: 'common'
              }
            }
          ]
        }
      };
      
      setServiceData(mockServiceData);
      
      // 设置默认选中的平台
      if (mockServiceData.account) {
        const platforms = Object.keys(mockServiceData.account);
        setSelectedPlatforms(platforms);
      }
      
      if (mockServiceData.counter) {
        const counterPlatforms = Object.keys(mockServiceData.counter);
        if (counterPlatforms.length > 0) {
          setSelectedCounterPlatform(counterPlatforms[0]);
        }
      }
    } catch (error) {
      message.error('加载服务数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载用户信息
  const loadUserInfo = async () => {
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Market',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'Sentinel',
      //   data_type: 'Service_User',
      //   data_methods: 'return_user_credit_points',
      //   data_argument: '{}',
      //   data_kwargs: '{}'
      // };
      // const response = await serviceRequests.Async(requestData);
      // setUserInfo(response);

      // 模拟数据
      const mockUserInfo: UserInfo = {
        user_name: '测试用户',
        user_type: '标准用户',
        user_phone: '138****8888',
        user_email: '<EMAIL>',
        user_charger: 1500
      };
      setUserInfo(mockUserInfo);
    } catch (error) {
      message.error('加载用户信息失败');
    }
  };

  // 计算总购买点数
  const calculateTotalPoints = () => {
    let total = 0;
    Object.keys(serviceData).forEach(type => {
      Object.keys(serviceData[type]).forEach(platform => {
        serviceData[type][platform].forEach(item => {
          if (item.number > 0) {
            total += item.number * item.argument.price;
          }
        });
      });
    });
    return total;
  };

  // 计算总购买数量
  const calculateTotalQuantity = () => {
    let total = 0;
    Object.keys(serviceData).forEach(type => {
      Object.keys(serviceData[type]).forEach(platform => {
        serviceData[type][platform].forEach(item => {
          if (item.number > 0) {
            total += item.number;
          }
        });
      });
    });
    return total;
  };

  // 处理操作类型切换
  const handleOperationChange = (e: any) => {
    setActiveOperation(e.target.value);
  };

  // 处理平台选择变化
  const handlePlatformChange = (checkedValues: string[]) => {
    setSelectedPlatforms(checkedValues);
  };

  // 处理反制服务平台选择变化
  const handleCounterPlatformChange = (e: any) => {
    setSelectedCounterPlatform(e.target.value);
  };

  // 重置筛选
  const resetFilter = () => {
    if (activeOperation === 'account' && serviceData.account) {
      const platforms = Object.keys(serviceData.account);
      setSelectedPlatforms(platforms);
    }
  };

  // 增加数量
  const incrementValue = (platform: string, itemId: string) => {
    const newServiceData = { ...serviceData };
    if (newServiceData[activeOperation] && newServiceData[activeOperation][platform]) {
      const item = newServiceData[activeOperation][platform].find(item => item.id === itemId);
      if (item) {
        const maxValue = item.argument.condition.length > 1 ? parseInt(item.argument.condition[1]) : 999999;
        if (item.number < maxValue) {
          item.number += 1;
          setServiceData(newServiceData);
        } else {
          message.warning(`最大数量限制为 ${maxValue}`);
        }
      }
    }
  };

  // 减少数量
  const decrementValue = (platform: string, itemId: string) => {
    const newServiceData = { ...serviceData };
    if (newServiceData[activeOperation] && newServiceData[activeOperation][platform]) {
      const item = newServiceData[activeOperation][platform].find(item => item.id === itemId);
      if (item && item.number > 0) {
        item.number -= 1;
        setServiceData(newServiceData);
      }
    }
  };

  // 输入数量变化
  const handleNumberChange = (platform: string, itemId: string, value: number) => {
    const newServiceData = { ...serviceData };
    if (newServiceData[activeOperation] && newServiceData[activeOperation][platform]) {
      const item = newServiceData[activeOperation][platform].find(item => item.id === itemId);
      if (item) {
        const minValue = item.argument.condition.length > 0 ? parseInt(item.argument.condition[0]) : 0;
        const maxValue = item.argument.condition.length > 1 ? parseInt(item.argument.condition[1]) : 999999;

        if (value >= minValue && value <= maxValue) {
          item.number = value;
          setServiceData(newServiceData);
        } else {
          message.warning(`数量范围：${minValue} - ${maxValue}`);
        }
      }
    }
  };

  // 编辑服务
  const handleEdit = (platform: string, itemId: string) => {
    if (serviceData[activeOperation] && serviceData[activeOperation][platform]) {
      const item = serviceData[activeOperation][platform].find(item => item.id === itemId);
      if (item) {
        setCurrentEditItem(item);
        setEditModalVisible(true);
      }
    }
  };

  // 加入购物车
  const addToMission = () => {
    const selectedItems = [];
    Object.keys(serviceData).forEach(type => {
      Object.keys(serviceData[type]).forEach(platform => {
        serviceData[type][platform].forEach(item => {
          if (item.number > 0) {
            selectedItems.push({
              ...item,
              quantity: item.number,
              totalPrice: item.number * item.argument.price
            });
          }
        });
      });
    });

    if (selectedItems.length === 0) {
      message.warning('请先选择服务项目');
      return;
    }

    message.success('已加入购物车');
  };

  // 结算
  const handleCheckout = () => {
    const selectedItems = [];
    Object.keys(serviceData).forEach(type => {
      Object.keys(serviceData[type]).forEach(platform => {
        serviceData[type][platform].forEach(item => {
          if (item.number > 0) {
            selectedItems.push(item);
          }
        });
      });
    });

    if (selectedItems.length === 0) {
      message.warning('请先选择服务项目');
      return;
    }

    setCheckoutModalVisible(true);
  };

  // 提交订单
  const submitOrder = async () => {
    try {
      message.success('订单提交成功');

      // 重置所有数量
      const newServiceData = { ...serviceData };
      Object.keys(newServiceData).forEach(type => {
        Object.keys(newServiceData[type]).forEach(platform => {
          newServiceData[type][platform].forEach(item => {
            item.number = 0;
          });
        });
      });
      setServiceData(newServiceData);
      setCheckoutModalVisible(false);

      // 刷新用户信用点
      loadUserInfo();
    } catch (error) {
      message.error('订单提交失败');
    }
  };

  // 回到顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="counter-service-page">
      {/* 操作类型选择 */}
      <div className="operation-selector">
        <div className="btn-group" data-toggle="buttons">
          <RadioGroup
            value={activeOperation}
            onChange={handleOperationChange}
            buttonStyle="solid"
          >
            <Radio.Button value="account">账号支持</Radio.Button>
            <Radio.Button value="counter">营销服务</Radio.Button>
            <Radio.Button value="phone">流量卡</Radio.Button>
          </RadioGroup>
        </div>
      </div>

      {/* 筛选和配置区域 */}
      <Card className="filter-card">
        {/* 目标链接输入 - 仅在营销服务时显示 */}
        {activeOperation === 'counter' && (
          <div className="target-url-container">
            <label htmlFor="target_url">目标链接：</label>
            <Input
              id="target_url"
              placeholder="请输入目标链接"
              value={targetUrl}
              onChange={(e) => setTargetUrl(e.target.value)}
            />
          </div>
        )}

        {/* 账号类型筛选 - 账号支持 */}
        {activeOperation === 'account' && (
          <div className="filter-section">
            <label>账号类型筛选：</label>
            <CheckboxGroup
              options={serviceData.account ? Object.keys(serviceData.account).map(platform => ({
                label: platform.toUpperCase(),
                value: platform
              })) : []}
              value={selectedPlatforms}
              onChange={handlePlatformChange}
            />
            <div className="filter-actions">
              <Button onClick={resetFilter}>重置筛选</Button>
            </div>
          </div>
        )}

        {/* 账号类型筛选 - 营销服务 */}
        {activeOperation === 'counter' && (
          <div className="filter-section">
            <label>账号类型筛选：</label>
            <RadioGroup
              value={selectedCounterPlatform}
              onChange={handleCounterPlatformChange}
            >
              {serviceData.counter && Object.keys(serviceData.counter).map(platform => (
                <Radio value={platform} key={platform}>
                  {platform.toUpperCase()}
                </Radio>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* 收货信息 - 仅在流量卡时显示 */}
        {activeOperation === 'phone' && (
          <div className="contact-info-section">
            <Row gutter={16}>
              <Col span={8}>
                <label htmlFor="contact_name">收货人姓名：</label>
                <Input
                  id="contact_name"
                  placeholder="请输入收货人姓名"
                  value={contactInfo.name}
                  onChange={(e) => setContactInfo({...contactInfo, name: e.target.value})}
                />
              </Col>
              <Col span={8}>
                <label htmlFor="contact">联系方式：</label>
                <Input
                  id="contact"
                  placeholder="请输入收货人联系方式"
                  value={contactInfo.phone}
                  onChange={(e) => setContactInfo({...contactInfo, phone: e.target.value})}
                />
              </Col>
              <Col span={8}>
                <label htmlFor="address">收货地址：</label>
                <Input
                  id="address"
                  placeholder="请输入收货地址"
                  value={contactInfo.address}
                  onChange={(e) => setContactInfo({...contactInfo, address: e.target.value})}
                />
              </Col>
            </Row>
          </div>
        )}

        {/* 通用提示信息 - 对所有操作类型都显示 */}
        <div className="general-notice">
          若超过购买限制数量，请加入购物车后再次添加。
        </div>
      </Card>

      {/* 服务列表 */}
      <div className="service-list">
        <Collapse
          defaultActiveKey={serviceData[activeOperation] ? Object.keys(serviceData[activeOperation]) : []}
          ghost
        >
          {serviceData[activeOperation] && Object.keys(serviceData[activeOperation]).map(platform => {
            const platformServices = serviceData[activeOperation][platform];

            // 根据当前操作类型和筛选条件决定是否显示
            let shouldShow = false;
            if (activeOperation === 'account') {
              shouldShow = selectedPlatforms.includes(platform);
            } else if (activeOperation === 'counter') {
              shouldShow = selectedCounterPlatform === platform;
            } else if (activeOperation === 'phone') {
              shouldShow = true;
            }

            if (!shouldShow) return null;

            return (
              <Panel
                header={platform.toUpperCase()}
                key={platform}
                className="service-panel"
              >
                <div className="service-items">
                  {platformServices.map(item => (
                    <div key={item.id} className="service-item">
                      <Row gutter={16} align="middle">
                        <Col span={3}>
                          <Text strong>服务编号：</Text>
                          <div>{item.id}</div>
                        </Col>
                        <Col span={4}>
                          <Text strong>项目名称：</Text>
                          <div>{item.name}</div>
                        </Col>
                        <Col span={5}>
                          <Text strong>说明：</Text>
                          <Tooltip title={item.argument.remark}>
                            <div className="service-remark">{item.argument.remark}</div>
                          </Tooltip>
                        </Col>
                        <Col span={3}>
                          <Text strong>Min/Max：</Text>
                          <div>
                            {item.argument.condition.filter(c => c !== '').length === 0
                              ? '无限制'
                              : item.argument.condition.join('/')
                            }
                          </div>
                        </Col>
                        <Col span={3}>
                          <Text strong>单价（元）：</Text>
                          <Text type="danger" strong>{item.argument.price}</Text>
                        </Col>
                        <Col span={6}>
                          {item.argument.sign === 'comment' ? (
                            <Button
                              type="primary"
                              icon={<EditOutlined />}
                              onClick={() => handleEdit(platform, item.id)}
                              block
                            >
                              编辑
                            </Button>
                          ) : (
                            <div className="quantity-control">
                              <Button
                                icon={<MinusOutlined />}
                                onClick={() => decrementValue(platform, item.id)}
                                disabled={item.number <= 0}
                              />
                              <InputNumber
                                value={item.number}
                                min={0}
                                max={item.argument.condition.length > 1 ? parseInt(item.argument.condition[1]) : 999999}
                                onChange={(value) => handleNumberChange(platform, item.id, value || 0)}
                                style={{ width: 80, textAlign: 'center' }}
                              />
                              <Button
                                icon={<PlusOutlined />}
                                onClick={() => incrementValue(platform, item.id)}
                              />
                            </div>
                          )}
                        </Col>
                      </Row>
                    </div>
                  ))}
                </div>
              </Panel>
            );
          })}
        </Collapse>
      </div>

      {/* 底部购物车 */}
      <div className="cart-footer">
        <div className="cart-user">
          <Avatar
            src="/static/Images/User/Face/Examine.jpg"
            size="large"
            onClick={() => setCreditModalVisible(true)}
          />
          <Button
            type="primary"
            icon={<CreditCardOutlined />}
            onClick={() => setCreditModalVisible(true)}
            className="credit-btn"
          >
            信用点：{userInfo?.user_charger || 0}
          </Button>
        </div>
        <div className="cart-actions">
          <Text type="danger" strong style={{ fontSize: 16 }}>
            已选购总点数：{calculateTotalPoints().toFixed(2)}
          </Text>
          <Button
            type="primary"
            icon={<CheckCircleOutlined />}
            onClick={handleCheckout}
            size="large"
          >
            立即结算（{calculateTotalQuantity()}）
          </Button>
          <Button
            type="primary"
            danger
            onClick={addToMission}
            size="large"
          >
            加入购物车
          </Button>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      {showBackToTop && (
        <Button
          className="back-to-top-btn"
          icon={<VerticalAlignTopOutlined />}
          onClick={scrollToTop}
          shape="circle"
          size="large"
        />
      )}

      {/* 信用点信息模态框 */}
      <Modal
        title={
          <div style={{ color: '#4dd9d5' }}>
            <Avatar src="/static/Images/App/Sball.gif" style={{ marginRight: 8 }} />
            哨兵信用点信息
          </div>
        }
        open={creditModalVisible}
        onCancel={() => setCreditModalVisible(false)}
        footer={[
          <Button key="refresh" type="primary" onClick={loadUserInfo}>刷新</Button>,
          <Button key="close" onClick={() => setCreditModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
        className="credit-modal"
      >
        {userInfo && (
          <div>
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="用户名称">
                    <Input value={userInfo.user_name} disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="用户类型">
                    <Input value={userInfo.user_type} disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="联系电话">
                    <Input value={userInfo.user_phone} disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="联系邮箱">
                    <Input value={userInfo.user_email || '未设置'} disabled />
                  </Form.Item>
                </Col>
              </Row>
            </Form>

            <div style={{ color: '#4dd9d5', marginBottom: 40, textAlign: 'center' }}>
              确定充值金额后等待支付二维码
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                <Text style={{ fontSize: 28, marginRight: 16 }}>信用点数</Text>
                <Input
                  value={userInfo.user_charger}
                  disabled
                  style={{
                    fontSize: 28,
                    color: '#E67E22',
                    fontWeight: 'bold',
                    width: 200,
                    textAlign: 'center'
                  }}
                />
                <Button type="primary" onClick={loadUserInfo} style={{ marginLeft: 16 }}>
                  刷新
                </Button>
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Text style={{ fontSize: 28, marginRight: 16 }}>充值金额</Text>
                <Input
                  placeholder="请输入充值金额"
                  value={rechargeAmount}
                  onChange={(e) => setRechargeAmount(e.target.value)}
                  style={{
                    fontSize: 28,
                    width: 200,
                    textAlign: 'center'
                  }}
                />
                <Button style={{ marginLeft: 16, opacity: 0 }}>占位</Button>
              </div>
            </div>

            {/* 支付二维码区域 */}
            <div style={{
              width: 268,
              height: 268,
              margin: '40px auto',
              border: '2px dashed #d9d9d9',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999'
            }}>
              支付二维码区域
            </div>
          </div>
        )}
      </Modal>

      {/* 结算模态框 */}
      <Modal
        title={
          <div style={{ color: '#4dd9d5' }}>
            <Avatar src="/static/Images/App/Sball.gif" style={{ marginRight: 8 }} />
            已选服务项目
          </div>
        }
        open={checkoutModalVisible}
        onCancel={() => setCheckoutModalVisible(false)}
        footer={[
          <Button key="submit" type="primary" onClick={submitOrder}>
            提交订单
          </Button>,
          <Button key="cancel" onClick={() => setCheckoutModalVisible(false)}>
            取消
          </Button>
        ]}
        width={800}
        className="checkout-modal"
      >
        <div>
          <div className="checkout-items">
            <Table
              dataSource={Object.keys(serviceData).reduce((acc: any[], type) => {
                Object.keys(serviceData[type]).forEach(platform => {
                  serviceData[type][platform]
                    .filter(item => item.number > 0)
                    .forEach(item => {
                      acc.push({
                        key: item.id,
                        name: item.name,
                        remark: item.argument.remark,
                        quantity: item.number,
                        price: item.argument.price,
                        total: (item.number * item.argument.price).toFixed(2)
                      });
                    });
                });
                return acc;
              }, [])}
              columns={[
                {
                  title: '服务名称',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: '说明',
                  dataIndex: 'remark',
                  key: 'remark',
                  ellipsis: true,
                },
                {
                  title: '数量',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  width: 80,
                },
                {
                  title: '单价',
                  dataIndex: 'price',
                  key: 'price',
                  width: 100,
                  render: (price: number) => `¥${price}`,
                },
                {
                  title: '小计',
                  dataIndex: 'total',
                  key: 'total',
                  width: 100,
                  render: (total: string) => (
                    <Text strong>¥{total}</Text>
                  ),
                },
              ]}
              pagination={false}
              size="small"
            />
          </div>

          <div className="checkout-total">
            <Text>订单总额：</Text>
            <Text type="danger" strong style={{ fontSize: 18 }}>
              {calculateTotalPoints().toFixed(2)}点数
            </Text>
          </div>
        </div>
      </Modal>

      {/* 编辑服务模态框 */}
      <Modal
        title={
          <div style={{ color: '#4dd9d5' }}>
            <Avatar src="/static/Images/App/Sball.gif" style={{ marginRight: 8 }} />
            链接评论编辑
          </div>
        }
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={[
          <Button key="submit" type="primary" onClick={() => {
            setEditModalVisible(false);
            message.success('已加入购物车');
          }}>
            加入购物车
          </Button>,
          <Button key="cancel" onClick={() => setEditModalVisible(false)}>
            取消
          </Button>
        ]}
        width={800}
        className="edit-modal"
      >
        {currentEditItem && (
          <div>
            <div className="edit-section">
              <label className="edit-label">当前链接</label>
              <div className="edit-content">
                {targetUrl || '请在上方输入目标链接'}
              </div>
            </div>

            <div className="edit-section">
              <label className="edit-label">服务说明</label>
              <div className="edit-content">
                评论条数限定{currentEditItem.argument.condition.join('/')}（min/max）
              </div>
            </div>

            <div className="edit-section">
              <label className="edit-label">评论编辑区</label>
              <div className="edit-content">
                <TextArea
                  rows={10}
                  value={commentContent}
                  onChange={(e) => setCommentContent(e.target.value)}
                  placeholder="请输入评论内容，每行一条评论"
                  style={{ fontSize: 14 }}
                />
              </div>
            </div>

            <div style={{ textAlign: 'right', marginTop: 16 }}>
              <Text>评论总数：</Text>
              <Text strong>{commentContent.split('\n').filter(line => line.trim()).length}条</Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Page_Counter_Service;
