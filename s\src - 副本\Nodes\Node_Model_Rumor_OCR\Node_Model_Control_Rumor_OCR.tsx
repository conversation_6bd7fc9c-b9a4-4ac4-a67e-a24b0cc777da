import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { Button, Progress ,Switch,Input,Image,Table,InputNumber,Layout, Col, Row, Slider, Space, Flex, Divider,Rate,Badge,Radio,Carousel,
  Dropdown ,ConfigProvider,Tabs,Card, Statistic,Select
} from "antd";
import styles from "../Nodes.module.css";
import React, { useState,useRef,useEffect } from 'react';
import "../Nodes.module.css";
import { ArrowDownOutlined, ArrowUpOutlined ,CloudServerOutlined,CheckOutlined,AuditOutlined,MoreOutlined,EllipsisOutlined,StopOutlined,CaretRightOutlined,PauseOutlined,
  ApartmentOutlined,FieldStringOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SnippetsOutlined
} from '@ant-design/icons';
import type { MenuProps,InputNumberProps,ThemeConfig,TabsProps } from 'antd';
import {  CloseOutlined } from '@ant-design/icons';


import Component_Keys from '@/Component/Component_NodeFlow/Component_Keys';

import type { RadioChangeEvent } from 'antd';
// @ts-ignore - 忽略下一行的类型检查
import Component_Select from '@/Component/Component_NodeFlow/Component_Select';
import Component_Select_Switch from '@/Component/Component_NodeFlow/Component_Select_Switch';
import BrownianWave from '@/Component/Component_NodeFlow/Echarts/BrownianWave';


const { TextArea } = Input;

export const Node_Socket = new ClassicPreset.Socket("socket");

export class ModelControl_Rumor_OCR extends ClassicPreset.Control {
    constructor(
      public title: string, 
      public date: string, 
      public source: string, 
      public author: string, 
      public image: string, 
      public url: string, 
      public content: string, 
      // public onClick: () => void) 
      public onChange: (value: string) => void)
      {
      super();   
    }
    setContent(Rumor: Record<string, any>) {
      const safeGet = (key: string) => Rumor?.[key] || "未知";
      this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
      this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
      this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
      this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
      this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
      this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
      // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
      // this.update(); // 触发控件更新
    }
    
  }

interface StepTestProps {
  label?: string;
  value?: number;
  onChange?: (value: number) => void;
}

const Step_Test: React.FC<StepTestProps> = (props) => {
  const [inputValue, setInputValue] = useState(props.value || 1);

  const onChange: InputNumberProps['onChange'] = (newValue) => {
    const val = newValue as number;
    setInputValue(val);
    props.onChange?.(val);
  };
  const textStyle = {
    color: 'white',
    margin: 0
  };
  // 主题配置
const theme: ThemeConfig = {
  components: {
    InputNumber: {
      // 核心配置
      colorText: "#ffffff",      // 文字颜色
      colorBorder: "#1890ff",     // 默认边框色
      colorPrimaryHover: "#40a9ff", // 悬浮态边框色
      colorPrimary: "#1677ff",    // 聚焦态边框色
      fontSize: 16,               // 字号（覆盖 inputFontSize）
      handleBg:"rgba(255,255,255,0.1)",
      handleBorderColor:"rgba(255,255,255,0.1)",
      // 其他扩展配置
      colorError: "#ff4d4f",      // 错误状态颜色
      colorBgContainer: "#f6ffed", // 背景色
      // paddingBlock: -28,            // 垂直内边距
      // paddingInline: -26           // 水平内边距
    },
  },
};

  return (
    <Row style={{ display: 'flex', alignItems: 'center', justifyContent: 'start', background: "transparent" ,color: 'white' }}>
      <Col span={4} style={{ background: "transparent" }}>
      <p style={textStyle}>{props.label || "数据"}</p>
      </Col>
      <Col style={{ background: "transparent" }} span={13}>
        <Slider
        style={{color:"white"}}
          min={0}
          max={2}
          step={0.1}
          onChange={onChange}
          value={inputValue}
          // trackStyle={{ background: 'white' }}
          handleStyle={{ 
            borderColor: 'white',
            color: 'white'
          }}
          railStyle={{ background: 'rgba(255,255,255,0.3)' }}
        />
      </Col>
      <Col span={3} style={{ background: "transparent",color: 'white'  }}>
      <ConfigProvider theme={theme}>
        <InputNumber
         suffix="W" 
          min={0}
          max={2}
          step={0.1}
          style={{
            // 外层容器样式
            color: "white",
        
            // display: 'flex',
            // alignItems: 'start', justifyContent: 'start', 
            width:90,
            height:30,
            background:"transparent",
            border: 'none',
            padding:"10 -10",
            position: 'relative',  // 为伪元素定位准备
            marginTop:0,
          }}
          value={inputValue}
          onChange={onChange}
          className={styles.customInput}
         
        />
        </ConfigProvider>
      </Col>
    </Row>
  );
};



export  function Node_Model_Rumor_OCR(props: { data:ModelControl_Rumor_OCR }) {

  const [inputValue, setInputValue] = useState(1);
// 若需要同步数值到父组件
  const handleValueChange = (newValue: number) => {
    setInputValue(newValue);
    // 这里可以更新 TestControl_Rumor 的数据
    // props.data.value = newValue;
  };
  const onChange: InputNumberProps['onChange'] = (newValue) => {
    setInputValue(newValue as number);
  };

  return (
  <Layout style={{zIndex:99999, background: "transparent"}}>
    <Space style={{ width: '100%', background: "transparent" }} direction="vertical">
      <Step_Test label="方式1" value={inputValue}  onChange={handleValueChange} />
      <Step_Test label="方式2" value={inputValue}  onChange={handleValueChange} />
      <Step_Test label="方式3" value={inputValue}  onChange={handleValueChange} />
      <Step_Test label="方式4" value={inputValue}  onChange={handleValueChange} />
      <Step_Test label="方式5" value={inputValue}  onChange={handleValueChange} />
    </Space>
  </Layout>
    

  );

}
