import React, { useState } from 'react';
import { notification } from 'antd';

class Service_Notification {
  private static api: any;

  // 初始化通知 API
  public static init() {
    const [api, contextHolder] = notification.useNotification();
    this.api = api;
    return contextHolder;
  }

  // 打开通知
  public static openNotification(pauseOnHover: boolean,Title:string,Message:string) {
    this.api.open({
      message: <div style={{ color: "cyan" }}>{Title}</div>,
      description:<div style={{ color: "white" }}>{Message}</div>,
      showProgress: true,
      pauseOnHover,
      style:{background:"rgba(136, 184, 238, 0.8)",borderRadius: 5,color: "white",},
    });
  }
}

export default Service_Notification;