@charset "UTF-8";

address,
dl,
ol,
p,
ul {
    margin-bottom: 1rem
}

caption,
th {
    text-align: left
}

dd,
h1,
h2,
h3,
h4,
h5,
h6,
label {
    margin-bottom: .5rem
}

address,
legend {
    line-height: inherit
}

progress,
sub,
sup {
    vertical-align: baseline
}

button,
hr,
input {
    overflow: visible
}

pre,
textarea {
    overflow: auto
}

.btn,
.custom-control-indicator {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none
}

.btn-group-vertical,
.custom-controls-stacked,
.form-inline {
    -webkit-box-direction: normal
}

.breadcrumb,
.dropdown-menu,
.icon-list,
.list,
.list-inline,
.list-unstyled,
.nav,
.navbar-nav,
.navigation,
.navigation__sub>ul,
.pagination,
.price-table__info,
.top-nav {
    list-style: none
}

.waves-effect,
html {
    -webkit-tap-highlight-color: transparent
}

@media print {

    blockquote,
    img,
    pre,
    tr {
        page-break-inside: avoid
    }

    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important
    }

    a,
    a:visited {
        text-decoration: underline
    }

    abbr[title]::after {
        content: " (" attr(title) ")"
    }

    pre {
        white-space: pre-wrap !important
    }

    blockquote,
    pre {
        border: 1px solid #999
    }

    thead {
        display: table-header-group
    }

    h2,
    h3,
    p {
        orphans: 3;
        widows: 3
    }

    h2,
    h3 {
        page-break-after: avoid
    }

    .navbar {
        display: none
    }

    .badge {
        border: 1px solid #000
    }

    .table {
        border-collapse: collapse !important
    }

    .table td,
    .table th {
        background-color: #fff !important
    }

    .table-bordered td,
    .table-bordered th {
        border: 1px solid #ddd !important
    }
}

article,
aside,
dialog,
figcaption,
figure,
footer,
header,
hgroup,
legend,
main,
nav,
section {
    display: block
}

label,
output {
    display: inline-block
}

html {
    box-sizing: border-box;
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar
}

*,
::after,
::before {
    box-sizing: inherit
}

@-ms-viewport {
    width: device-width
}

body {
    margin: 0;
    font-family: Nunito, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: rgba(255, 255, 255, .85);
    background-color: #fff
}

[tabindex="-1"]:focus {
    outline: 0 !important
}

dl,
h1,
h2,
h3,
h4,
h5,
h6,
ol,
p,
ul {
    margin-top: 0
}

abbr[data-original-title],
abbr[title] {
    text-decoration: underline;
    text-decoration: underline dotted;
    cursor: help;
    border-bottom: 0
}

address {
    font-style: normal
}

ol ol,
ol ul,
ul ol,
ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 600
}

dd {
    margin-left: 0
}

blockquote,
figure {
    margin: 0 0 1rem
}

dfn {
    font-style: italic
}

b,
strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

sub,
sup {
    position: relative;
    font-size: 75%;
    line-height: 0
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

a {
    color: #a4d0ff;
    text-decoration: none;
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

a:hover {
    color: #58a8ff;
    text-decoration: none
}

a:not([href]):not([tabindex]),
a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus {
    outline: 0
}

code,
kbd,
pre,
samp {
    font-size: 1em
}

img {
    vertical-align: middle;
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

[role=button],
a,
area,
button,
input,
label,
select,
summary,
textarea {
    -ms-touch-action: manipulation;
    touch-action: manipulation
}

table {
    border-collapse: collapse
}

caption {
    padding-top: 1rem 1.5rem;
    padding-bottom: 1rem 1.5rem;
    color: rgba(255, 255, 255, .7);
    caption-side: bottom
}

button:focus {
    outline: dotted 1px;
    outline: -webkit-focus-ring-color auto 5px
}

button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font-size: inherit;
    line-height: inherit
}

button,
select {
    text-transform: none
}

[type=reset],
[type=submit],
button,
html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner,
button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

input[type=radio],
input[type=checkbox] {
    box-sizing: border-box;
    padding: 0
}

input[type=date],
input[type=time],
input[type=datetime-local],
input[type=month] {
    -webkit-appearance: listbox
}

textarea {
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: 1.5rem;
    color: inherit;
    white-space: normal
}

.badge,
.btn,
.dropdown-header,
.dropdown-item,
.input-group-btn,
.navbar-brand {
    white-space: nowrap
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: none
}

[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

.display-1,
.display-2,
.display-3,
.display-4 {
    line-height: 1.1
}

summary {
    display: list-item
}

template {
    display: none
}

[hidden] {
    display: none !important
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 600;
    line-height: 1.1;
    color: #fff
}

.blockquote,
hr {
    margin-bottom: 1rem
}

.display-1,
.display-2,
.display-3,
.display-4,
.lead {
    font-weight: 300
}

.h1,
h1 {
    font-size: 2.5rem
}

.h2,
h2 {
    font-size: 2rem
}

.h3,
h3 {
    font-size: 1.75rem
}

.h4,
h4 {
    font-size: 1.5rem
}

.h5,
h5 {
    font-size: 1.25rem
}

.h6,
h6 {
    font-size: 1rem
}

.lead {
    font-size: 1.25rem
}

.display-1 {
    font-size: 6rem
}

.display-2 {
    font-size: 5.5rem
}

.display-3 {
    font-size: 4.5rem
}

.display-4 {
    font-size: 3.5rem
}

hr {
    box-sizing: content-box;
    height: 0;
    margin-top: 1rem;
    border: 0;
    border-top: 1px solid rgba(255, 255, 255, .125)
}

.img-fluid,
.img-thumbnail {
    max-width: 100%;
    height: auto
}

.small,
small {
    font-size: 80%;
    font-weight: 400
}

.mark,
mark {
    padding: .2em;
    background-color: #fcf8e3
}

.list-inline,
.list-unstyled {
    padding-left: 0
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: 5px
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

.blockquote {
    font-size: 1.25rem
}

.blockquote-footer {
    display: block;
    font-size: 80%;
    color: rgba(255, 255, 255, .7)
}

.blockquote-footer::before {
    content: "\2014 \00A0"
}

.img-thumbnail {
    padding: .25rem;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 2px;
    transition: all .2s ease-in-out
}

code,
kbd {
    font-size: 90%;
    border-radius: 2px
}

.figure {
    display: inline-block
}

.figure-img {
    margin-bottom: .5rem;
    line-height: 1
}

.table,
pre {
    margin-bottom: 1rem
}

.figure-caption {
    font-size: 90%;
    color: #868e96
}

a>code,
pre code {
    padding: 0;
    color: inherit
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

code {
    padding: .2rem .4rem;
    color: #fff;
    background-color: rgba(255, 255, 255, .08)
}

a>code {
    background-color: inherit
}

kbd {
    padding: .2rem .4rem;
    color: #fff;
    background-color: #212529
}

.table,
pre code {
    background-color: transparent
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 600
}

.btn,
.btn-link,
.dropdown-item {
    font-weight: 400
}

pre {
    display: block;
    margin-top: 0;
    font-size: 90%;
    color: #131313
}

.form-check-input,
.form-text,
.invalid-feedback {
    margin-top: .25rem
}

pre code {
    font-size: inherit;
    border-radius: 0
}

.container,
.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-right: 15px;
    padding-left: 15px;
    width: 100%
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

@media (min-width:576px) {
    .container {
        max-width: 540px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 720px
    }
}

@media (min-width:992px) {
    .container {
        max-width: 960px
    }
}

@media (min-width:1200px) {
    .container {
        max-width: 1140px
    }
}

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

.form-control,
.form-control-file,
.form-control-range,
.form-text {
    display: block
}

.no-gutters {
    margin-right: 0;
    margin-left: 0
}

.no-gutters>.col,
.no-gutters>[class*=col-] {
    padding-right: 0;
    padding-left: 0
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px
}

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
}

.col-1,
.col-auto {
    -webkit-box-flex: 0
}

.col-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
}

.col-1 {
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%
}

.col-2,
.col-3 {
    -webkit-box-flex: 0
}

.col-2 {
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%
}

.col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4,
.col-5 {
    -webkit-box-flex: 0
}

.col-4 {
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%
}

.col-5 {
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%
}

.col-6,
.col-7 {
    -webkit-box-flex: 0
}

.col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%
}

.col-8,
.col-9 {
    -webkit-box-flex: 0
}

.col-8 {
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%
}

.col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10,
.col-11 {
    -webkit-box-flex: 0
}

.col-10 {
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%
}

.col-11 {
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%
}

.col-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
}

.order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
}

.order-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
}

.order-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
}

.order-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
}

.order-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
}

.order-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
}

.order-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
}

.order-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
}

.order-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
}

.order-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
}

@media (min-width:576px) {
    .col-sm {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-sm-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none
    }

    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
        flex: 0 0 8.33333%;
        max-width: 8.33333%
    }

    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
        flex: 0 0 16.66667%;
        max-width: 16.66667%
    }

    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }

    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }

    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }

    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
        flex: 0 0 66.66667%;
        max-width: 66.66667%
    }

    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
        flex: 0 0 83.33333%;
        max-width: 83.33333%
    }

    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
        flex: 0 0 91.66667%;
        max-width: 91.66667%
    }

    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-sm-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .order-sm-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .order-sm-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .order-sm-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .order-sm-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .order-sm-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .order-sm-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .order-sm-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .order-sm-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .order-sm-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .order-sm-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .order-sm-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }
}

@media (min-width:768px) {
    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-md-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none
    }

    .col-md-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
        flex: 0 0 8.33333%;
        max-width: 8.33333%
    }

    .col-md-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
        flex: 0 0 16.66667%;
        max-width: 16.66667%
    }

    .col-md-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-md-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }

    .col-md-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }

    .col-md-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-md-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }

    .col-md-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
        flex: 0 0 66.66667%;
        max-width: 66.66667%
    }

    .col-md-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-md-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
        flex: 0 0 83.33333%;
        max-width: 83.33333%
    }

    .col-md-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
        flex: 0 0 91.66667%;
        max-width: 91.66667%
    }

    .col-md-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-md-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .order-md-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .order-md-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .order-md-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .order-md-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .order-md-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .order-md-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .order-md-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .order-md-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .order-md-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .order-md-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .order-md-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }
}

@media (min-width:992px) {
    .col-lg {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-lg-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none
    }

    .col-lg-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
        flex: 0 0 8.33333%;
        max-width: 8.33333%
    }

    .col-lg-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
        flex: 0 0 16.66667%;
        max-width: 16.66667%
    }

    .col-lg-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-lg-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }

    .col-lg-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }

    .col-lg-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-lg-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }

    .col-lg-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
        flex: 0 0 66.66667%;
        max-width: 66.66667%
    }

    .col-lg-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-lg-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
        flex: 0 0 83.33333%;
        max-width: 83.33333%
    }

    .col-lg-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
        flex: 0 0 91.66667%;
        max-width: 91.66667%
    }

    .col-lg-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-lg-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .order-lg-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .order-lg-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .order-lg-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .order-lg-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .order-lg-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .order-lg-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .order-lg-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .order-lg-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .order-lg-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .order-lg-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .order-lg-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }
}

@media (min-width:1200px) {
    .col-xl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-xl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: none
    }

    .col-xl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
        flex: 0 0 8.33333%;
        max-width: 8.33333%
    }

    .col-xl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
        flex: 0 0 16.66667%;
        max-width: 16.66667%
    }

    .col-xl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-xl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }

    .col-xl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }

    .col-xl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-xl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }

    .col-xl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
        flex: 0 0 66.66667%;
        max-width: 66.66667%
    }

    .col-xl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-xl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
        flex: 0 0 83.33333%;
        max-width: 83.33333%
    }

    .col-xl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
        flex: 0 0 91.66667%;
        max-width: 91.66667%
    }

    .col-xl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-xl-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }

    .order-xl-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }

    .order-xl-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }

    .order-xl-4 {
        -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
        order: 4
    }

    .order-xl-5 {
        -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
        order: 5
    }

    .order-xl-6 {
        -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
        order: 6
    }

    .order-xl-7 {
        -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
        order: 7
    }

    .order-xl-8 {
        -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
        order: 8
    }

    .order-xl-9 {
        -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
        order: 9
    }

    .order-xl-10 {
        -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
        order: 10
    }

    .order-xl-11 {
        -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
        order: 11
    }

    .order-xl-12 {
        -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
        order: 12
    }
}

.table {
    width: 100%;
    max-width: 100%
}

.table td,
.table th {
    padding: 1rem 1.5rem;
    vertical-align: top;
    border-top: 1px solid rgba(255, 255, 255, .125)
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(255, 255, 255, .125)
}

.table tbody+tbody {
    border-top: 2px solid rgba(255, 255, 255, .125)
}

.table .table {
    background-color: #fff
}

.table-sm td,
.table-sm th {
    padding: .75rem 1rem
}

.table-bordered,
.table-bordered td,
.table-bordered th {
    border: 1px solid rgba(255, 255, 255, .125)
}

.table-bordered thead td,
.table-bordered thead th {
    border-bottom-width: 2px
}

.table-hover tbody tr:hover,
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, .04)
}

.table-primary,
.table-primary>td,
.table-primary>th {
    background-color: #b8daff
}

.table-hover .table-primary:hover,
.table-hover .table-primary:hover>td,
.table-hover .table-primary:hover>th {
    background-color: #9fcdff
}

.table-secondary,
.table-secondary>td,
.table-secondary>th {
    background-color: #fff
}

.table-hover .table-secondary:hover,
.table-hover .table-secondary:hover>td,
.table-hover .table-secondary:hover>th {
    background-color: #f2f2f2
}

.table-success,
.table-success>td,
.table-success>th {
    background-color: #c3e6cb
}

.table-hover .table-success:hover,
.table-hover .table-success:hover>td,
.table-hover .table-success:hover>th {
    background-color: #b1dfbb
}

.table-info,
.table-info>td,
.table-info>th {
    background-color: #bee5eb
}

.table-hover .table-info:hover,
.table-hover .table-info:hover>td,
.table-hover .table-info:hover>th {
    background-color: #abdde5
}

.table-warning,
.table-warning>td,
.table-warning>th {
    background-color: #ffeeba
}

.table-hover .table-warning:hover,
.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
    background-color: #ffe8a1
}

.table-danger,
.table-danger>td,
.table-danger>th {
    background-color: #f5c6cb
}

.table-hover .table-danger:hover,
.table-hover .table-danger:hover>td,
.table-hover .table-danger:hover>th {
    background-color: #f1b0b7
}

.table-active,
.table-active>td,
.table-active>th {
    background-color: rgba(255, 255, 255, .04)
}

.table-hover .table-active:hover,
.table-hover .table-active:hover>td,
.table-hover .table-active:hover>th {
    background-color: rgba(242, 242, 242, .04)
}

.thead-inverse th {
    color: #fff;
    background-color: rgba(0, 0, 0, .2)
}

.thead-default th {
    color: rgba(255, 255, 255, .85);
    background-color: rgba(255, 255, 255, .125)
}

.table-inverse {
    color: #fff;
    background-color: rgba(0, 0, 0, .2)
}

.table-inverse td,
.table-inverse th,
.table-inverse thead th {
    border-color: rgba(255, 255, 255, .06)
}

.table-inverse.table-bordered {
    border: 0
}

.table-inverse.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, .05)
}

.table-inverse.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, .075)
}

.form-control,
.form-control:disabled,
.form-control:focus,
.form-control[readonly] {
    background-color: transparent
}

@media (max-width:991px) {
    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -ms-overflow-style: -ms-autohiding-scrollbar
    }

    .table-responsive.table-bordered {
        border: 0
    }
}

.collapsing,
.modal,
.modal-open,
.progress {
    overflow: hidden
}

.form-control {
    width: 100%;
    padding: .6rem 1rem;
    font-size: 1rem;
    line-height: 1.25;
    color: rgba(255, 255, 255, .85);
    background-image: none;
    background-clip: padding-box;
    border: 1px solid rgba(255, 255, 255, .2);
    border-radius: 0;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}

.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}

.form-control:focus {
    color: rgba(255, 255, 255, .85);
    border-color: rgba(255, 255, 255, .4);
    outline: 0
}

.form-control::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5);
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5);
    opacity: 1
}

.form-control::placeholder {
    color: rgba(255, 255, 255, .5);
    opacity: 1
}

select.form-control:not([size]):not([multiple]) {
    height: calc(2.45rem + 2px)
}

select.form-control:focus::-ms-value {
    color: rgba(255, 255, 255, .85);
    background-color: transparent
}

.col-form-label {
    padding-top: calc(.6rem - 1px * 2);
    padding-bottom: calc(.6rem - 1px * 2);
    margin-bottom: 0
}

.col-form-label-lg {
    padding-top: calc(.5rem - 1px * 2);
    padding-bottom: calc(.5rem - 1px * 2);
    font-size: 1.25rem
}

.col-form-label-sm {
    padding-top: calc(.25rem - 1px * 2);
    padding-bottom: calc(.25rem - 1px * 2);
    font-size: .875rem
}

.col-form-legend,
.form-control-plaintext {
    padding-top: .6rem;
    padding-bottom: .6rem;
    margin-bottom: 0
}

.col-form-legend {
    font-size: 1rem
}

.form-control-plaintext {
    line-height: 1.25;
    border: solid transparent;
    border-width: 1px 0
}

.custom-file-input.is-valid~.custom-file-control,
.custom-select.is-valid,
.form-control.is-valid,
.was-validated .custom-file-input:valid~.custom-file-control,
.was-validated .custom-select:valid,
.was-validated .form-control:valid {
    border-color: #28a745
}

.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm,
.input-group-lg>.form-control-plaintext.form-control,
.input-group-lg>.form-control-plaintext.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-plaintext.btn,
.input-group-sm>.form-control-plaintext.form-control,
.input-group-sm>.form-control-plaintext.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-plaintext.btn {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
    padding: .25rem .55rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: 0
}

.input-group-sm>.input-group-btn>select.btn:not([size]):not([multiple]),
.input-group-sm>select.form-control:not([size]):not([multiple]),
.input-group-sm>select.input-group-addon:not([size]):not([multiple]),
select.form-control-sm:not([size]):not([multiple]) {
    height: calc(1.8125rem + 2px)
}

.form-control-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0
}

.input-group-lg>.input-group-btn>select.btn:not([size]):not([multiple]),
.input-group-lg>select.form-control:not([size]):not([multiple]),
.input-group-lg>select.input-group-addon:not([size]):not([multiple]),
select.form-control-lg:not([size]):not([multiple]) {
    height: calc(2.3125rem + 2px)
}

.form-group {
    margin-bottom: 2rem
}

.form-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px
}

.form-row>.col,
.form-row>[class*=col-] {
    padding-right: 5px;
    padding-left: 5px
}

.form-check {
    position: relative;
    display: block;
    margin-bottom: .5rem
}

.form-check.disabled .form-check-label {
    color: rgba(255, 255, 255, .7)
}

.form-check-label {
    padding-left: 1.25rem;
    margin-bottom: 0
}

.form-check-input {
    position: absolute;
    margin-left: -1.25rem
}

.form-check-input:only-child {
    position: static
}

.form-check-inline {
    display: inline-block
}

.form-check-inline .form-check-label {
    vertical-align: middle
}

.form-check-inline+.form-check-inline {
    margin-left: .75rem
}

.invalid-feedback {
    display: none;
    font-size: .875rem;
    color: #dc3545
}

.invalid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    width: 250px;
    padding: .5rem;
    margin-top: .1rem;
    font-size: .875rem;
    line-height: 1;
    color: #fff;
    background-color: rgba(220, 53, 69, .8);
    border-radius: .2rem
}

.custom-select.is-invalid~.invalid-feedback,
.custom-select.is-invalid~.invalid-tooltip,
.custom-select.is-valid~.invalid-feedback,
.custom-select.is-valid~.invalid-tooltip,
.form-control.is-invalid~.invalid-feedback,
.form-control.is-invalid~.invalid-tooltip,
.form-control.is-valid~.invalid-feedback,
.form-control.is-valid~.invalid-tooltip,
.was-validated .custom-select:invalid~.invalid-feedback,
.was-validated .custom-select:invalid~.invalid-tooltip,
.was-validated .custom-select:valid~.invalid-feedback,
.was-validated .custom-select:valid~.invalid-tooltip,
.was-validated .form-control:invalid~.invalid-feedback,
.was-validated .form-control:invalid~.invalid-tooltip,
.was-validated .form-control:valid~.invalid-feedback,
.was-validated .form-control:valid~.invalid-tooltip {
    display: block
}

.custom-control-input.is-valid~.custom-control-description,
.form-check-input.is-valid+.form-check-label,
.was-validated .custom-control-input:valid~.custom-control-description,
.was-validated .form-check-input:valid+.form-check-label {
    color: #28a745
}

.custom-control-input.is-valid~.custom-control-indicator,
.was-validated .custom-control-input:valid~.custom-control-indicator {
    background-color: rgba(40, 167, 69, .25)
}

.custom-control-input.is-invalid~.custom-control-description,
.form-check-input.is-invalid+.form-check-label,
.was-validated .custom-control-input:invalid~.custom-control-description,
.was-validated .form-check-input:invalid+.form-check-label {
    color: #dc3545
}

.custom-file-input.is-valid~.custom-file-control::before,
.was-validated .custom-file-input:valid~.custom-file-control::before {
    border-color: inherit
}

.custom-file-input.is-invalid~.custom-file-control,
.custom-select.is-invalid,
.form-control.is-invalid,
.was-validated .custom-file-input:invalid~.custom-file-control,
.was-validated .custom-select:invalid,
.was-validated .form-control:invalid {
    border-color: #dc3545
}

.custom-control-input.is-invalid~.custom-control-indicator,
.was-validated .custom-control-input:invalid~.custom-control-indicator {
    background-color: rgba(220, 53, 69, .25)
}

.custom-file-input.is-invalid~.custom-file-control::before,
.was-validated .custom-file-input:invalid~.custom-file-control::before {
    border-color: inherit
}

.form-inline {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.form-inline .form-check {
    width: 100%
}

@media (min-width:576px) {
    .form-inline label {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
    }

    .form-inline .form-check,
    .form-inline .form-group {
        display: -webkit-box;
        display: -ms-flexbox;
        margin-bottom: 0
    }

    .form-inline .form-group {
        display: flex;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }

    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .form-inline .form-control-plaintext {
        display: inline-block
    }

    .form-inline .input-group {
        width: auto
    }

    .form-inline .form-control-label {
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .form-check {
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        margin-top: 0
    }

    .form-inline .form-check-label {
        padding-left: 0
    }

    .form-inline .form-check-input {
        position: relative;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
    }

    .form-inline .custom-control {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        padding-left: 0
    }

    .form-inline .custom-control-indicator {
        position: static;
        display: inline-block;
        margin-right: .25rem;
        vertical-align: text-bottom
    }

    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.btn-block,
input[type=button].btn-block,
input[type=reset].btn-block,
input[type=submit].btn-block {
    width: 100%
}

.btn {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: .6rem 1rem;
    font-size: 1rem;
    line-height: 1.25;
    border-radius: 2px;
    transition: all .15s ease-in-out
}

.btn:focus,
.btn:hover {
    text-decoration: none
}

.btn.focus,
.btn:focus {
    outline: 0;
    box-shadow: none
}

.btn.disabled,
.btn:disabled {
    opacity: .65
}

.btn.active,
.btn:active {
    background-image: none
}

a.btn.disabled,
fieldset[disabled] a.btn {
    pointer-events: none
}

.btn-primary {
    color: #fff;
    background-color: #007bff
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9
}

.btn-primary.disabled,
.btn-primary:disabled {
    background-color: #007bff
}

.btn-primary.active,
.btn-primary:active,
.show>.btn-primary.dropdown-toggle {
    background-color: #0069d9;
    background-image: none
}

.btn-secondary {
    color: #111;
    background-color: #fff
}

.btn-secondary:hover {
    color: #111;
    background-color: #ececec
}

.btn-secondary.disabled,
.btn-secondary:disabled {
    background-color: #fff
}

.btn-secondary.active,
.btn-secondary:active,
.show>.btn-secondary.dropdown-toggle {
    background-color: #ececec;
    background-image: none
}

.btn-success {
    color: #fff;
    background-color: #28a745
}

.btn-success:hover {
    color: #fff;
    background-color: #218838
}

.btn-success.disabled,
.btn-success:disabled {
    background-color: #28a745
}

.btn-success.active,
.btn-success:active,
.show>.btn-success.dropdown-toggle {
    background-color: #218838;
    background-image: none
}

.btn-info {
    color: #fff;
    background-color: #17a2b8
}

.btn-info:hover {
    color: #fff;
    background-color: #138496
}

.btn-info.disabled,
.btn-info:disabled {
    background-color: #17a2b8
}

.btn-info.active,
.btn-info:active,
.show>.btn-info.dropdown-toggle {
    background-color: #138496;
    background-image: none
}

.btn-warning {
    color: #111;
    background-color: #ffc107
}

.btn-warning:hover {
    color: #111;
    background-color: #e0a800
}

.btn-warning.disabled,
.btn-warning:disabled {
    background-color: #ffc107
}

.btn-warning.active,
.btn-warning:active,
.show>.btn-warning.dropdown-toggle {
    background-color: #e0a800;
    background-image: none
}

.btn-danger {
    color: #fff;
    background-color: #dc3545
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333
}

.btn-danger.disabled,
.btn-danger:disabled {
    background-color: #dc3545
}

.btn-danger.active,
.btn-danger:active,
.show>.btn-danger.dropdown-toggle {
    background-color: #c82333;
    background-image: none
}

.btn-outline-primary {
    color: #007bff;
    background-color: transparent;
    background-image: none;
    border-color: #007bff
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-outline-primary.focus,
.btn-outline-primary:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, .5)
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
    color: #007bff;
    background-color: transparent
}

.btn-outline-primary.active,
.btn-outline-primary:active,
.show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-outline-secondary {
    color: #fff;
    background-color: transparent;
    background-image: none;
    border-color: #fff
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.btn-outline-secondary.focus,
.btn-outline-secondary:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, .5)
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    color: #fff;
    background-color: transparent
}

.btn-outline-secondary.active,
.btn-outline-secondary:active,
.show>.btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.btn-outline-success {
    color: #28a745;
    background-color: transparent;
    background-image: none;
    border-color: #28a745
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-outline-success.focus,
.btn-outline-success:focus {
    box-shadow: 0 0 0 3px rgba(40, 167, 69, .5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
    color: #28a745;
    background-color: transparent
}

.btn-outline-success.active,
.btn-outline-success:active,
.show>.btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-outline-info {
    color: #17a2b8;
    background-color: transparent;
    background-image: none;
    border-color: #17a2b8
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-info.focus,
.btn-outline-info:focus {
    box-shadow: 0 0 0 3px rgba(23, 162, 184, .5)
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
    color: #17a2b8;
    background-color: transparent
}

.btn-outline-info.active,
.btn-outline-info:active,
.show>.btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-warning {
    color: #ffc107;
    background-color: transparent;
    background-image: none;
    border-color: #ffc107
}

.btn-outline-warning:hover {
    color: #fff;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-outline-warning.focus,
.btn-outline-warning:focus {
    box-shadow: 0 0 0 3px rgba(255, 193, 7, .5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    color: #ffc107;
    background-color: transparent
}

.btn-outline-warning.active,
.btn-outline-warning:active,
.show>.btn-outline-warning.dropdown-toggle {
    color: #fff;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-outline-danger {
    color: #dc3545;
    background-color: transparent;
    background-image: none;
    border-color: #dc3545
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-outline-danger.focus,
.btn-outline-danger:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, .5)
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
    color: #dc3545;
    background-color: transparent
}

.btn-outline-danger.active,
.btn-outline-danger:active,
.show>.btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-link {
    color: #a4d0ff;
    border-radius: 0
}

.btn-link,
.btn-link.active,
.btn-link:active,
.btn-link:disabled {
    background-color: transparent
}

.btn-link,
.btn-link:active,
.btn-link:focus {
    border-color: transparent;
    box-shadow: none
}

.btn-link:hover {
    border-color: transparent
}

.btn-link:focus,
.btn-link:hover {
    color: #58a8ff;
    text-decoration: none;
    background-color: transparent
}

.btn-link:disabled {
    color: #868e96
}

.btn-link:disabled:focus,
.btn-link:disabled:hover {
    text-decoration: none
}

.btn-group-lg>.btn,
.btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 2px
}

.btn-group-sm>.btn,
.btn-sm {
    padding: .25rem .55rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: 2px
}

.btn-block {
    display: block
}

.btn-block+.btn-block {
    margin-top: .5rem
}

.fade {
    opacity: 0;
    transition: opacity .15s linear
}

.fade.show {
    opacity: 1
}

.collapse {
    display: none
}

.collapse.show {
    display: block
}

tr.collapse.show {
    display: table-row
}

tbody.collapse.show {
    display: table-row-group
}

.collapsing {
    position: relative;
    height: 0;
    transition: height .35s ease
}

.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-left: .3em solid transparent
}

.dropdown-toggle:empty::after {
    margin-left: 0
}

.dropup .dropdown-menu {
    margin-top: 0;
    margin-bottom: 0
}

.dropup .dropdown-toggle::after {
    border-top: 0;
    border-bottom: .3em solid
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: .8rem 0;
    margin: 0;
    font-size: 1rem;
    color: rgba(255, 255, 255, .85);
    text-align: left;
    background-color: rgba(0, 0, 0, .96);
    background-clip: padding-box;
    border: 0 solid transparent;
    border-radius: 2px
}

.dropdown-divider {
    height: 0;
    margin: .5rem 0;
    overflow: hidden;
    border-top: 1px solid rgba(255, 255, 255, .055)
}

.btn+.dropdown-toggle-split::after,
.btn-group>.btn:first-child {
    margin-left: 0
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: .5rem 1.5rem;
    clear: both;
    color: rgba(255, 255, 255, .85);
    text-align: inherit;
    background: 0 0;
    border: 0
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn,
.btn-group-vertical>.btn:not(:first-child):not(:last-child),
.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn,
.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle),
.input-group .form-control:not(:first-child):not(:last-child),
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.dropdown-item.active,
.dropdown-item:active,
.dropdown-item:focus,
.dropdown-item:hover {
    color: #fff;
    text-decoration: none;
    background-color: rgba(255, 255, 255, .06)
}

.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #868e96;
    background-color: transparent
}

.show>a {
    outline: 0
}

.dropdown-menu.show {
    display: block
}

.dropdown-header {
    display: block;
    margin-bottom: 0;
    color: #fff
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}

.btn-toolbar,
.input-group {
    display: -webkit-box;
    display: -ms-flexbox
}

.btn-group-vertical>.btn,
.btn-group>.btn {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    margin-bottom: 0
}

.btn-group-vertical>.btn.active,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:hover,
.btn-group>.btn.active,
.btn-group>.btn:active,
.btn-group>.btn:focus,
.btn-group>.btn:hover {
    z-index: 2
}

.btn-toolbar {
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn+.dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}

.btn-group-sm>.btn+.dropdown-toggle-split,
.btn-sm+.dropdown-toggle-split {
    padding-right: .4125rem;
    padding-left: .4125rem
}

.btn-group-lg>.btn+.dropdown-toggle-split,
.btn-lg+.dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}

.btn-group-vertical {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-group-vertical .btn,
.btn-group-vertical .btn-group {
    width: 100%
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

[data-toggle=buttons]>.btn input[type=radio],
[data-toggle=buttons]>.btn input[type=checkbox],
[data-toggle=buttons]>.btn-group>.btn input[type=radio],
[data-toggle=buttons]>.btn-group>.btn input[type=checkbox] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.custom-control,
.input-group,
.input-group-btn,
.input-group-btn>.btn {
    position: relative
}

.input-group {
    display: flex;
    width: 100%
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0
}

.input-group .form-control:active,
.input-group .form-control:focus,
.input-group .form-control:hover,
.input-group-btn>.btn:active,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:hover {
    z-index: 3
}

.input-group .form-control,
.input-group-addon,
.input-group-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.input-group-addon,
.input-group-btn {
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25;
    color: rgba(255, 255, 255, .85);
    text-align: center;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, .2);
    border-radius: 0
}

.custom-control-input:active~.custom-control-indicator,
.custom-control-input:checked~.custom-control-indicator {
    color: #fff;
    background-color: transparent
}

.input-group-addon.form-control-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn {
    padding: .25rem .55rem;
    font-size: .875rem;
    border-radius: 0
}

.input-group-addon.form-control-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    border-radius: 0
}

.input-group-addon input[type=radio],
.input-group-addon input[type=checkbox] {
    margin-top: 0
}

.input-group .form-control:not(:last-child),
.input-group-addon:not(:last-child),
.input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.btn,
.input-group-btn:not(:first-child)>.btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:not(:last-child)>.btn,
.input-group-btn:not(:last-child)>.btn-group>.btn,
.input-group-btn:not(:last-child)>.dropdown-toggle {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group-addon:not(:last-child) {
    border-right: 0
}

.input-group .form-control:not(:first-child),
.input-group-addon:not(:first-child),
.input-group-btn:not(:first-child)>.btn,
.input-group-btn:not(:first-child)>.btn-group>.btn,
.input-group-btn:not(:first-child)>.dropdown-toggle,
.input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.btn,
.input-group-btn:not(:last-child)>.btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.form-control+.input-group-addon:not(:first-child) {
    border-left: 0
}

.input-group-btn {
    font-size: 0
}

.input-group-btn>.btn+.btn {
    margin-left: -1px
}

.input-group-btn:not(:last-child)>.btn,
.input-group-btn:not(:last-child)>.btn-group {
    margin-right: -1px
}

.input-group-btn:not(:first-child)>.btn,
.input-group-btn:not(:first-child)>.btn-group {
    z-index: 2;
    margin-left: -1px
}

.input-group-btn:not(:first-child)>.btn-group:active,
.input-group-btn:not(:first-child)>.btn-group:focus,
.input-group-btn:not(:first-child)>.btn-group:hover,
.input-group-btn:not(:first-child)>.btn:active,
.input-group-btn:not(:first-child)>.btn:focus,
.input-group-btn:not(:first-child)>.btn:hover {
    z-index: 3
}

.custom-control {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    padding-left: 2rem;
    margin-right: 1rem
}

.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0
}

.custom-control-input:focus~.custom-control-indicator {
    box-shadow: none
}

.custom-control-input:disabled~.custom-control-indicator {
    background-color: transparent
}

.custom-control-input:disabled~.custom-control-description {
    color: rgba(255, 255, 255, .7)
}

.custom-control-indicator {
    position: absolute;
    left: 0;
    display: block;
    width: 1.308rem;
    height: 1.308rem;
    pointer-events: none;
    user-select: none;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%
}

.custom-file-control,
.data-table table th,
.flatpickr-calendar {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none
}

.custom-checkbox .custom-control-indicator {
    border-radius: 2px
}

.custom-checkbox .custom-control-input:checked~.custom-control-indicator {
    background-image: none
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-indicator {
    background-color: #007bff;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='/www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23ffffff' d='M0 2h4'/%3E%3C/svg%3E")
}

.custom-radio .custom-control-indicator {
    border-radius: 50%
}

.custom-file-control,
.custom-select {
    border: 1px solid rgba(255, 255, 255, .2)
}

.custom-radio .custom-control-input:checked~.custom-control-indicator {
    background-image: none
}

.custom-controls-stacked {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column
}

.custom-file,
.custom-select {
    display: inline-block;
    max-width: 100%
}

.custom-controls-stacked .custom-control {
    margin-bottom: .25rem
}

.custom-controls-stacked .custom-control+.custom-control {
    margin-left: 0
}

.custom-select {
    height: calc(2.45rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    line-height: 1.25;
    color: rgba(255, 255, 255, .85);
    vertical-align: middle;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='/www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") right .75rem center no-repeat #fff;
    background-size: 8px 10px;
    border-radius: 2px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.custom-select:focus {
    border-color: #80bdff;
    outline: 0
}

.custom-select:focus::-ms-value {
    color: rgba(255, 255, 255, .85);
    background-color: transparent
}

.custom-select:disabled {
    color: #868e96;
    background-color: #e9ecef
}

.custom-select::-ms-expand {
    opacity: 0
}

.custom-select-sm {
    height: calc(1.8125rem + 2px);
    padding-top: .375rem;
    padding-bottom: .375rem;
    font-size: 75%
}

.custom-file {
    position: relative;
    height: 2.5rem;
    margin-bottom: 0
}

.custom-file-control,
.custom-file-control::before {
    position: absolute;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #495057
}

.custom-file-input {
    min-width: 14rem;
    max-width: 100%;
    height: 2.5rem;
    margin: 0;
    opacity: 0
}

.custom-file-control {
    top: 0;
    right: 0;
    left: 0;
    z-index: 5;
    pointer-events: none;
    user-select: none;
    background-color: #fff;
    border-radius: 2px
}

.custom-file-control:lang(en):empty::after {
    content: "Choose file..."
}

.custom-file-control::before {
    top: -1px;
    right: -1px;
    bottom: -1px;
    z-index: 6;
    display: block;
    background-color: #e9ecef;
    border: 1px solid rgba(255, 255, 255, .2);
    border-radius: 0 2px 2px 0
}

.nav,
.navbar {
    display: -webkit-box;
    display: -ms-flexbox;
    -ms-flex-wrap: wrap
}

.nav-tabs .dropdown-menu,
.nav-tabs .nav-link {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.custom-file-control:lang(en)::before {
    content: "Browse"
}

.nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.nav-link.disabled {
    color: rgba(255, 255, 255, .7)
}

.nav-tabs {
    border-bottom: 2px solid rgba(255, 255, 255, .08)
}

.nav-tabs .nav-item {
    margin-bottom: -2px
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef rgba(255, 255, 255, .08)
}

.nav-tabs .nav-link.disabled {
    color: rgba(255, 255, 255, .7);
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #fff;
    background-color: transparent;
    border-color: #ddd #ddd transparent
}

.nav-tabs .dropdown-menu {
    margin-top: -2px
}

.nav-pills .nav-link {
    border-radius: 2px
}

.nav-pills .nav-link.active,
.show>.nav-pills .nav-link {
    color: #fff;
    background-color: rgba(255, 255, 255, .08)
}

.nav-fill .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center
}

.nav-justified .nav-item {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    text-align: center
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: .5rem 1rem
}

.navbar>.container,
.navbar>.container-fluid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.navbar-brand {
    display: inline-block;
    padding-top: .8125rem;
    padding-bottom: .8125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit
}

.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none
}

.navbar-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0
}

.navbar-expand,
.navbar-expand .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal
}

.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0
}

.navbar-nav .dropdown-menu {
    position: static;
    float: none
}

.breadcrumb-item,
.column {
    float: left
}

.navbar-text {
    display: inline-block;
    padding-top: 1rem;
    padding-bottom: 1rem
}

.navbar-collapse {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.navbar-toggler {
    padding: .25rem .75rem;
    font-size: 1.25rem;
    line-height: 1;
    background: 0 0;
    border: 1px solid transparent;
    border-radius: 2px
}

.navbar-toggler:focus,
.navbar-toggler:hover {
    text-decoration: none
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: center center no-repeat;
    background-size: 100% 100%
}

@media (max-width:575px) {

    .navbar-expand-sm>.container,
    .navbar-expand-sm>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width:576px) {

    .navbar-expand-sm,
    .navbar-expand-sm .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal
    }

    .navbar-expand-sm {
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-sm .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-sm .navbar-nav .dropdown-menu-right {
        right: 0;
        left: auto
    }

    .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-sm>.container,
    .navbar-expand-sm>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-sm .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .navbar-expand-sm .navbar-toggler {
        display: none
    }
}

@media (max-width:767px) {

    .navbar-expand-md>.container,
    .navbar-expand-md>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width:768px) {

    .navbar-expand-md,
    .navbar-expand-md .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal
    }

    .navbar-expand-md {
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-md .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-md .navbar-nav .dropdown-menu-right {
        right: 0;
        left: auto
    }

    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-md>.container,
    .navbar-expand-md>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-md .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .navbar-expand-md .navbar-toggler {
        display: none
    }
}

@media (max-width:991px) {

    .navbar-expand-lg>.container,
    .navbar-expand-lg>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width:992px) {

    .navbar-expand-lg,
    .navbar-expand-lg .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal
    }

    .navbar-expand-lg {
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-lg .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-lg .navbar-nav .dropdown-menu-right {
        right: 0;
        left: auto
    }

    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-lg>.container,
    .navbar-expand-lg>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-lg .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .navbar-expand-lg .navbar-toggler {
        display: none
    }
}

@media (max-width:1199px) {

    .navbar-expand-xl>.container,
    .navbar-expand-xl>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width:1200px) {

    .navbar-expand-xl,
    .navbar-expand-xl .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal
    }

    .navbar-expand-xl {
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-xl .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-xl .navbar-nav .dropdown-menu-right {
        right: 0;
        left: auto
    }

    .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-xl>.container,
    .navbar-expand-xl>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-xl .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .navbar-expand-xl .navbar-toggler {
        display: none
    }
}

.navbar-expand {
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.navbar-expand>.container,
.navbar-expand>.container-fluid {
    padding-right: 0;
    padding-left: 0
}

.navbar-expand .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row
}

.navbar-expand .navbar-nav .dropdown-menu {
    position: absolute
}

.navbar-expand .navbar-nav .dropdown-menu-right {
    right: 0;
    left: auto
}

.navbar-expand .navbar-nav .nav-link {
    padding-right: .5rem;
    padding-left: .5rem
}

.navbar-expand>.container,
.navbar-expand>.container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}

.card,
.list-group {
    -ms-flex-direction: column
}

.navbar-expand .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
}

.navbar-expand .navbar-toggler {
    display: none
}

.navbar-light .navbar-brand,
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, .5)
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, .7)
}

.navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, .3)
}

.navbar-light .navbar-nav .active>.nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show>.nav-link {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, .5);
    border-color: rgba(0, 0, 0, .1)
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='/www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")
}

.navbar-light .navbar-text {
    color: rgba(0, 0, 0, .5)
}

.navbar-dark .navbar-brand,
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
    color: #fff
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, .5)
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, .75)
}

.navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, .25)
}

.navbar-dark .navbar-nav .active>.nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show>.nav-link {
    color: #fff
}

.navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, .5);
    border-color: rgba(255, 255, 255, .1)
}

.navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='/www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")
}

.navbar-dark .navbar-text {
    color: rgba(255, 255, 255, .5)
}

.card {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: rgba(0, 0, 0, .2);
    background-clip: border-box;
    border: 1px solid transparent;
    border-radius: 2px
}

.card-footer,
.card-header {
    background-color: rgba(0, 0, 0, .075)
}

.card-body {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 2.2rem
}

.card-title {
    margin-bottom: 2.1rem
}

.card-header,
.card-subtitle,
.card-text:last-child {
    margin-bottom: 0
}

.card-link+.card-link {
    margin-left: 2.2rem
}

.card-header-pills,
.card-header-tabs {
    margin-right: -1.1rem;
    margin-left: -1.1rem
}

.card>.list-group:first-child .list-group-item:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px
}

.card>.list-group:last-child .list-group-item:last-child {
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px
}

.card-header {
    border-bottom: 1px solid transparent
}

.card-header:first-child {
    border-radius: 2px 2px 0 0
}

.card-footer {
    padding: 2.1rem 2.2rem;
    border-top: 1px solid transparent
}

.card-footer:last-child {
    border-radius: 0 0 2px 2px
}

.card-header-tabs {
    margin-bottom: -2.1rem;
    border-bottom: 0
}

.card-img-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0
}

.card-img {
    width: 100%;
    border-radius: 2px
}

.card-img-top {
    width: 100%;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px
}

.card-img-bottom {
    width: 100%;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px
}

@media (min-width:576px) {

    .card-deck,
    .card-deck .card {
        -webkit-box-direction: normal
    }

    .card-deck {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        margin-right: -15px;
        margin-left: -15px
    }

    .card-deck .card {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%;
        -webkit-box-orient: vertical;
        -ms-flex-direction: column;
        flex-direction: column;
        margin-right: 15px;
        margin-left: 15px
    }

    .card-group {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap
    }

    .card-group .card {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .card-group .card+.card {
        margin-left: 0;
        border-left: 0
    }

    .card-group .card:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0
    }

    .card-group .card:first-child .card-img-top {
        border-top-right-radius: 0
    }

    .card-group .card:first-child .card-img-bottom {
        border-bottom-right-radius: 0
    }

    .card-group .card:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0
    }

    .card-group .card:last-child .card-img-top {
        border-top-left-radius: 0
    }

    .card-group .card:last-child .card-img-bottom {
        border-bottom-left-radius: 0
    }

    .card-group .card:not(:first-child):not(:last-child),
    .card-group .card:not(:first-child):not(:last-child) .card-img-bottom,
    .card-group .card:not(:first-child):not(:last-child) .card-img-top {
        border-radius: 0
    }

    .card-columns {
        -webkit-column-count: 3;
        column-count: 3;
        -webkit-column-gap: 1.25rem;
        column-gap: 1.25rem
    }

    .card-columns .card {
        display: inline-block;
        width: 100%
    }
}

.list-group,
.modal-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal
}

.card-columns .card {
    margin-bottom: 2.3rem
}

.breadcrumb {
    padding: .75rem .25rem;
    margin-bottom: 1rem;
    background-color: transparent
}

.breadcrumb::after {
    display: block;
    clear: both;
    content: ""
}

.breadcrumb-item+.breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    padding-left: .5rem;
    color: #868e96;
    content: ""
}

.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: none
}

.breadcrumb-item.active {
    color: #fff
}

.pagination {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    border-radius: 2px
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px
}

.page-item:last-child .page-link {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px
}

.page-item.active .page-link {
    z-index: 2;
    color: #000;
    background-color: #fff;
    border-color: #007bff
}

.page-item.disabled .page-link,
.page-link {
    background-color: rgba(255, 255, 255, .08);
    color: rgba(255, 255, 255, .85)
}

.page-item.disabled .page-link {
    pointer-events: none;
    border-color: #ddd
}

.page-link {
    position: relative;
    display: block;
    padding: 0;
    margin-left: -1px;
    border: 0 solid #ddd
}

.pagination-lg .page-item:first-child .page-link,
.pagination-sm .page-item:first-child .page-link {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px
}

.pagination-lg .page-item:last-child .page-link,
.pagination-sm .page-item:last-child .page-link {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px
}

.page-link:focus,
.page-link:hover {
    color: rgba(255, 255, 255, .85);
    text-decoration: none;
    background-color: rgba(255, 255, 255, .2);
    border-color: #ddd
}

.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    line-height: 1.5
}

.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5
}

.badge {
    display: inline-block;
    padding: .4rem .55rem;
    font-size: 85%;
    font-weight: 500;
    line-height: 1;
    color: #fff;
    text-align: center;
    vertical-align: baseline;
    border-radius: 2px
}

.popover,
.tooltip {
    font-family: Nunito, sans-serif;
    font-style: normal;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    word-wrap: break-word;
    font-weight: 400;
    text-shadow: none;
    text-decoration: none
}

.badge:empty {
    display: none
}

.media,
.progress {
    display: -webkit-box;
    display: -ms-flexbox
}

.btn .badge {
    position: relative;
    top: -1px
}

.modal,
.modal-backdrop {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0
}

.badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem
}

.badge-primary {
    color: #fff;
    background-color: #007bff
}

.badge-primary[href]:focus,
.badge-primary[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #0062cc
}

.badge-secondary {
    color: #111;
    background-color: #fff
}

.badge-secondary[href]:focus,
.badge-secondary[href]:hover {
    color: #111;
    text-decoration: none;
    background-color: #e6e6e6
}

.badge-success {
    color: #fff;
    background-color: #28a745
}

.badge-success[href]:focus,
.badge-success[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #1e7e34
}

.badge-info {
    color: #fff;
    background-color: #17a2b8
}

.badge-info[href]:focus,
.badge-info[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #117a8b
}

.badge-warning {
    color: #111;
    background-color: #ffc107
}

.badge-warning[href]:focus,
.badge-warning[href]:hover {
    color: #111;
    text-decoration: none;
    background-color: #d39e00
}

.badge-danger {
    color: #fff;
    background-color: #dc3545
}

.badge-danger[href]:focus,
.badge-danger[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #bd2130
}

.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: rgba(255, 255, 255, .08);
    border-radius: 2px
}

@media (min-width:576px) {
    .jumbotron {
        padding: 4rem 2rem
    }
}

.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0;
    border-radius: 0
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 3px 0
    }

    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 3px 0
    }

    to {
        background-position: 0 0
    }
}

.progress {
    display: flex;
    font-size: .75rem;
    line-height: 3px;
    text-align: center;
    background-color: rgba(255, 255, 255, .1)
}

.progress-bar {
    line-height: 3px;
    color: #fff;
    background-color: #fff;
    transition: width .6s ease
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 3px 3px
}

.progress-bar-animated {
    -webkit-animation: progress-bar-stripes 1s linear infinite;
    animation: progress-bar-stripes 1s linear infinite
}

.media {
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.media-body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.list-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0
}

.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit
}

.list-group-item-action:focus,
.list-group-item-action:hover {
    color: #495057;
    text-decoration: none;
    background-color: rgba(255, 255, 255, .06)
}

.list-group-item-action:active {
    color: rgba(255, 255, 255, .85);
    background-color: #e9ecef
}

.list-group-item {
    position: relative;
    display: block;
    padding: 1rem 2rem;
    margin-bottom: 0;
    background-color: transparent;
    border: 0 solid rgba(0, 0, 0, .125)
}

.list-group-item:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px
}

.list-group-item:focus,
.list-group-item:hover {
    text-decoration: none
}

.list-group-item.disabled,
.list-group-item:disabled {
    color: #868e96;
    background-color: transparent
}

.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0
}

.list-group-flush:first-child .list-group-item:first-child {
    border-top: 0
}

.list-group-flush:last-child .list-group-item:last-child {
    border-bottom: 0
}

.list-group-item-primary {
    color: #004085;
    background-color: #b8daff
}

a.list-group-item-primary,
button.list-group-item-primary {
    color: #004085
}

a.list-group-item-primary:focus,
a.list-group-item-primary:hover,
button.list-group-item-primary:focus,
button.list-group-item-primary:hover {
    color: #004085;
    background-color: #9fcdff
}

a.list-group-item-primary.active,
button.list-group-item-primary.active {
    color: #fff;
    background-color: #004085;
    border-color: #004085
}

.list-group-item-secondary {
    color: #858585;
    background-color: #fff
}

a.list-group-item-secondary,
button.list-group-item-secondary {
    color: #858585
}

a.list-group-item-secondary:focus,
a.list-group-item-secondary:hover,
button.list-group-item-secondary:focus,
button.list-group-item-secondary:hover {
    color: #858585;
    background-color: #f2f2f2
}

a.list-group-item-secondary.active,
button.list-group-item-secondary.active {
    color: #fff;
    background-color: #858585;
    border-color: #858585
}

.list-group-item-success {
    color: #155724;
    background-color: #c3e6cb
}

a.list-group-item-success,
button.list-group-item-success {
    color: #155724
}

a.list-group-item-success:focus,
a.list-group-item-success:hover,
button.list-group-item-success:focus,
button.list-group-item-success:hover {
    color: #155724;
    background-color: #b1dfbb
}

a.list-group-item-success.active,
button.list-group-item-success.active {
    color: #fff;
    background-color: #155724;
    border-color: #155724
}

.list-group-item-info {
    color: #0c5460;
    background-color: #bee5eb
}

a.list-group-item-info,
button.list-group-item-info {
    color: #0c5460
}

a.list-group-item-info:focus,
a.list-group-item-info:hover,
button.list-group-item-info:focus,
button.list-group-item-info:hover {
    color: #0c5460;
    background-color: #abdde5
}

a.list-group-item-info.active,
button.list-group-item-info.active {
    color: #fff;
    background-color: #0c5460;
    border-color: #0c5460
}

.list-group-item-warning {
    color: #856404;
    background-color: #ffeeba
}

a.list-group-item-warning,
button.list-group-item-warning {
    color: #856404
}

a.list-group-item-warning:focus,
a.list-group-item-warning:hover,
button.list-group-item-warning:focus,
button.list-group-item-warning:hover {
    color: #856404;
    background-color: #ffe8a1
}

a.list-group-item-warning.active,
button.list-group-item-warning.active {
    color: #fff;
    background-color: #856404;
    border-color: #856404
}

.list-group-item-danger {
    color: #721c24;
    background-color: #f5c6cb
}

a.list-group-item-danger,
button.list-group-item-danger {
    color: #721c24
}

a.list-group-item-danger:focus,
a.list-group-item-danger:hover,
button.list-group-item-danger:focus,
button.list-group-item-danger:hover {
    color: #721c24;
    background-color: #f1b0b7
}

a.list-group-item-danger.active,
button.list-group-item-danger.active {
    color: #fff;
    background-color: #721c24;
    border-color: #721c24
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1;
    color: #000;
    text-shadow: none
}

.modal-title,
.popover,
.tooltip {
    line-height: 1.5
}

.close:focus,
.close:hover {
    color: #000;
    text-decoration: none;
    opacity: .75
}

button.close {
    padding: 0;
    background: 0 0;
    border: 0;
    -webkit-appearance: none
}

.modal-content,
.popover {
    background-clip: padding-box
}

.modal {
    position: fixed;
    z-index: 1050;
    display: none;
    outline: 0
}

.modal-content,
.modal-header {
    display: -webkit-box;
    display: -ms-flexbox
}

.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out, -webkit-transform .3s ease-out;
    -webkit-transform: translate(0, -25%);
    transform: translate(0, -25%)
}

.modal.show .modal-dialog {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    margin: 10px
}

.modal-content {
    position: relative;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    background-color: rgba(0, 0, 0, .96);
    border: 0 solid rgba(0, 0, 0, .2);
    border-radius: 2px;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    z-index: 1040;
    background-color: #fff
}

.popover,
.tooltip-inner {
    background-color: rgba(0, 0, 0, .96)
}

.modal-backdrop.fade {
    opacity: 0
}

.modal-backdrop.show {
    opacity: .2
}

.modal-header {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 25px 30px 0;
    border-bottom: 0 solid #e9ecef
}

.modal-title {
    margin-bottom: 0
}

.modal-body {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 25px 30px
}

.modal-footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 25px 30px;
    border-top: 0 solid #e9ecef
}

.modal-footer>:not(:first-child) {
    margin-left: .25rem
}

.modal-footer>:not(:last-child) {
    margin-right: .25rem
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

.popover.bs-popover-auto[x-placement^=bottom] .arrow,
.popover.bs-popover-bottom .arrow,
.tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow,
.tooltip.bs-tooltip-bottom .arrow {
    top: 0
}

.popover .arrow,
.tooltip .arrow {
    height: 5px;
    position: absolute;
    display: block
}

@media (min-width:576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 30px auto
    }

    .modal-sm {
        max-width: 300px
    }
}

@media (min-width:992px) {
    .modal-lg {
        max-width: 800px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    text-align: left;
    text-align: start;
    font-size: .875rem;
    opacity: 0
}

.tooltip.show {
    opacity: 1
}

.tooltip .arrow {
    width: 5px
}

.tooltip.bs-tooltip-auto[x-placement^=top],
.tooltip.bs-tooltip-top {
    padding: 5px 0
}

.tooltip.bs-tooltip-auto[x-placement^=top] .arrow,
.tooltip.bs-tooltip-top .arrow {
    bottom: 0
}

.tooltip.bs-tooltip-auto[x-placement^=top] .arrow::before,
.tooltip.bs-tooltip-top .arrow::before {
    margin-left: -3px;
    content: "";
    border-width: 5px 5px 0;
    border-top-color: rgba(0, 0, 0, .96)
}

.tooltip.bs-tooltip-auto[x-placement^=right],
.tooltip.bs-tooltip-right {
    padding: 0 5px
}

.tooltip.bs-tooltip-auto[x-placement^=right] .arrow,
.tooltip.bs-tooltip-right .arrow {
    left: 0
}

.tooltip.bs-tooltip-auto[x-placement^=right] .arrow::before,
.tooltip.bs-tooltip-right .arrow::before {
    margin-top: -3px;
    content: "";
    border-width: 5px 5px 5px 0;
    border-right-color: rgba(0, 0, 0, .96)
}

.tooltip.bs-tooltip-auto[x-placement^=bottom],
.tooltip.bs-tooltip-bottom {
    padding: 5px 0
}

.tooltip.bs-tooltip-auto[x-placement^=bottom] .arrow::before,
.tooltip.bs-tooltip-bottom .arrow::before {
    margin-left: -3px;
    content: "";
    border-width: 0 5px 5px;
    border-bottom-color: rgba(0, 0, 0, .96)
}

.tooltip.bs-tooltip-auto[x-placement^=left],
.tooltip.bs-tooltip-left {
    padding: 0 5px
}

.tooltip.bs-tooltip-auto[x-placement^=left] .arrow,
.tooltip.bs-tooltip-left .arrow {
    right: 0
}

.tooltip.bs-tooltip-auto[x-placement^=left] .arrow::before,
.tooltip.bs-tooltip-left .arrow::before {
    right: 0;
    margin-top: -3px;
    content: "";
    border-width: 5px 0 5px 5px;
    border-left-color: rgba(0, 0, 0, .96)
}

.tooltip .arrow::before {
    position: absolute;
    border-color: transparent;
    border-style: solid
}

.tooltip-inner {
    max-width: 200px;
    padding: .7rem 1.1rem;
    color: #fff;
    text-align: center;
    border-radius: 2px
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: block;
    max-width: 276px;
    padding: 15px 10px;
    text-align: left;
    text-align: start;
    border: 0 solid rgba(0, 0, 0, .2);
    border-radius: 2px
}

.popover .arrow {
    width: 10px
}

.popover .arrow::after,
.popover .arrow::before {
    position: absolute;
    display: block;
    border-color: transparent;
    border-style: solid;
    content: "";
    border-width: 11px
}

.popover.bs-popover-auto[x-placement^=top],
.popover.bs-popover-top {
    margin-bottom: 10px
}

.popover.bs-popover-auto[x-placement^=top] .arrow,
.popover.bs-popover-top .arrow {
    bottom: 0
}

.popover.bs-popover-auto[x-placement^=top] .arrow::after,
.popover.bs-popover-auto[x-placement^=top] .arrow::before,
.popover.bs-popover-top .arrow::after,
.popover.bs-popover-top .arrow::before {
    border-bottom-width: 0
}

.popover.bs-popover-auto[x-placement^=top] .arrow::before,
.popover.bs-popover-top .arrow::before {
    bottom: -11px;
    margin-left: -6px;
    border-top-color: transparent
}

.popover.bs-popover-auto[x-placement^=top] .arrow::after,
.popover.bs-popover-top .arrow::after {
    bottom: -10px;
    margin-left: -6px;
    border-top-color: rgba(0, 0, 0, .96)
}

.popover.bs-popover-auto[x-placement^=right],
.popover.bs-popover-right {
    margin-left: 10px
}

.popover.bs-popover-auto[x-placement^=right] .arrow,
.popover.bs-popover-right .arrow {
    left: 0
}

.popover.bs-popover-auto[x-placement^=right] .arrow::after,
.popover.bs-popover-auto[x-placement^=right] .arrow::before,
.popover.bs-popover-right .arrow::after,
.popover.bs-popover-right .arrow::before {
    margin-top: -8px;
    border-left-width: 0
}

.popover.bs-popover-auto[x-placement^=right] .arrow::before,
.popover.bs-popover-right .arrow::before {
    left: -11px;
    border-right-color: transparent
}

.popover.bs-popover-auto[x-placement^=right] .arrow::after,
.popover.bs-popover-right .arrow::after {
    left: -10px;
    border-right-color: rgba(0, 0, 0, .96)
}

.popover.bs-popover-auto[x-placement^=bottom],
.popover.bs-popover-bottom {
    margin-top: 10px
}

.popover.bs-popover-auto[x-placement^=bottom] .arrow::after,
.popover.bs-popover-auto[x-placement^=bottom] .arrow::before,
.popover.bs-popover-bottom .arrow::after,
.popover.bs-popover-bottom .arrow::before {
    margin-left: -7px;
    border-top-width: 0
}

.popover.bs-popover-auto[x-placement^=bottom] .arrow::before,
.popover.bs-popover-bottom .arrow::before {
    top: -11px;
    border-bottom-color: transparent
}

.popover.bs-popover-auto[x-placement^=bottom] .arrow::after,
.popover.bs-popover-bottom .arrow::after {
    top: -10px;
    border-bottom-color: rgba(0, 0, 0, .96)
}

.popover.bs-popover-auto[x-placement^=bottom] .popover-header::before,
.popover.bs-popover-bottom .popover-header::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 20px;
    margin-left: -10px;
    content: "";
    border-bottom: 1px solid transparent
}

.carousel,
.carousel-inner,
.carousel-item {
    position: relative
}

.popover.bs-popover-auto[x-placement^=left],
.popover.bs-popover-left {
    margin-right: 10px
}

.popover.bs-popover-auto[x-placement^=left] .arrow,
.popover.bs-popover-left .arrow {
    right: 0
}

.popover.bs-popover-auto[x-placement^=left] .arrow::after,
.popover.bs-popover-auto[x-placement^=left] .arrow::before,
.popover.bs-popover-left .arrow::after,
.popover.bs-popover-left .arrow::before {
    margin-top: -8px;
    border-right-width: 0
}

.mt-0,
.my-0 {
    margin-top: 0 !important
}

.popover.bs-popover-auto[x-placement^=left] .arrow::before,
.popover.bs-popover-left .arrow::before {
    right: -11px;
    border-left-color: transparent
}

.popover.bs-popover-auto[x-placement^=left] .arrow::after,
.popover.bs-popover-left .arrow::after {
    right: -10px;
    border-left-color: rgba(0, 0, 0, .96)
}

.popover-header {
    padding: 8px 14px;
    margin-bottom: 0;
    font-size: 1rem;
    color: #fff;
    background-color: transparent;
    border-bottom: 0 solid transparent;
    border-top-left-radius: calc(2px - 0);
    border-top-right-radius: calc(2px - 0)
}

.popover-header:empty {
    display: none
}

.popover-body {
    padding: 9px 14px;
    color: rgba(255, 255, 255, .85)
}

.carousel-inner {
    width: 100%;
    overflow: hidden
}

.carousel-item {
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    transition: -webkit-transform .6s ease;
    transition: transform .6s ease;
    transition: transform .6s ease, -webkit-transform .6s ease;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px
}

.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
    display: block
}

.carousel-item-next,
.carousel-item-prev {
    position: absolute;
    top: 0
}

.carousel-item-next.carousel-item-left,
.carousel-item-prev.carousel-item-right {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {

    .carousel-item-next.carousel-item-left,
    .carousel-item-prev.carousel-item-right {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.active.carousel-item-right,
.carousel-item-next {
    -webkit-transform: translateX(100%);
    transform: translateX(100%)
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {

    .active.carousel-item-right,
    .carousel-item-next {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}

.active.carousel-item-left,
.carousel-item-prev {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%)
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {

    .active.carousel-item-left,
    .carousel-item-prev {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

.carousel-control-next,
.carousel-control-prev {
    position: absolute;
    top: 0;
    bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 15%;
    color: #fff;
    text-align: center;
    opacity: .8
}

.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: .9
}

.carousel-control-prev {
    left: 0
}

.carousel-control-next {
    right: 0
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: center center no-repeat;
    background-size: 100% 100%
}

.btn--action,
.carousel-indicators {
    display: -webkit-box;
    display: -ms-flexbox
}

.carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 10px;
    left: 0;
    z-index: 15;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none
}

.carousel-indicators li {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    width: 30px;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    background-color: rgba(255, 255, 255, .5)
}

.carousel-indicators li::after,
.carousel-indicators li::before {
    left: 0;
    display: inline-block;
    height: 10px;
    position: absolute;
    content: "";
    width: 100%
}

.mr-0,
.mx-0 {
    margin-right: 0 !important
}

.ml-0,
.mx-0 {
    margin-left: 0 !important
}

.carousel-indicators li::before {
    top: -10px
}

.carousel-indicators li::after {
    bottom: -10px
}

.carousel-indicators .active {
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    right: 15%;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    color: rgba(255, 255, 255, .9);
    text-align: center
}

.align-baseline {
    vertical-align: baseline !important
}

.align-top {
    vertical-align: top !important
}

.align-middle {
    vertical-align: middle !important
}

.align-bottom {
    vertical-align: bottom !important
}

.align-text-bottom {
    vertical-align: text-bottom !important
}

.align-text-top {
    vertical-align: text-top !important
}

.bg-primary {
    background-color: #007bff !important
}

a.bg-primary:focus,
a.bg-primary:hover {
    background-color: #0062cc !important
}

.bg-secondary {
    background-color: #fff !important
}

a.bg-secondary:focus,
a.bg-secondary:hover {
    background-color: #e6e6e6 !important
}

.bg-success {
    background-color: #28a745 !important
}

a.bg-success:focus,
a.bg-success:hover {
    background-color: #1e7e34 !important
}

.bg-info {
    background-color: #17a2b8 !important
}

a.bg-info:focus,
a.bg-info:hover {
    background-color: #117a8b !important
}

.bg-warning {
    background-color: #ffc107 !important
}

a.bg-warning:focus,
a.bg-warning:hover {
    background-color: #d39e00 !important
}

.bg-danger {
    background-color: #dc3545 !important
}

a.bg-danger:focus,
a.bg-danger:hover {
    background-color: #bd2130 !important
}

.bg-transparent {
    background-color: transparent !important
}

.border {
    border: 1px solid #e9ecef !important
}

.border-0 {
    border: 0 !important
}

.rounded-right,
.rounded-top {
    border-top-right-radius: 2px !important
}

.rounded-bottom,
.rounded-right {
    border-bottom-right-radius: 2px !important
}

.rounded-left,
.rounded-top {
    border-top-left-radius: 2px !important
}

.rounded-bottom,
.rounded-left {
    border-bottom-left-radius: 2px !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-bottom-0 {
    border-bottom: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.border-primary {
    border-color: #007bff !important
}

.border-secondary {
    border-color: #fff !important
}

.border-success {
    border-color: #28a745 !important
}

.border-info {
    border-color: #17a2b8 !important
}

.border-warning {
    border-color: #ffc107 !important
}

.border-danger {
    border-color: #dc3545 !important
}

.border-white {
    border-color: #fff !important
}

.rounded {
    border-radius: 2px !important
}

.rounded-circle {
    border-radius: 50%
}

.rounded-0 {
    border-radius: 0
}

.clearfix::after {
    display: block;
    clear: both;
    content: ""
}

.d-none {
    display: none !important
}

.d-inline {
    display: inline !important
}

.d-inline-block {
    display: inline-block !important
}

.d-block {
    display: block !important
}

.d-table {
    display: table !important
}

.d-table-cell {
    display: table-cell !important
}

.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
}

.d-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
}

@media (min-width:576px) {
    .d-sm-none {
        display: none !important
    }

    .d-sm-inline {
        display: inline !important
    }

    .d-sm-inline-block {
        display: inline-block !important
    }

    .d-sm-block {
        display: block !important
    }

    .d-sm-table {
        display: table !important
    }

    .d-sm-table-cell {
        display: table-cell !important
    }

    .d-sm-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-sm-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width:768px) {
    .d-md-none {
        display: none !important
    }

    .d-md-inline {
        display: inline !important
    }

    .d-md-inline-block {
        display: inline-block !important
    }

    .d-md-block {
        display: block !important
    }

    .d-md-table {
        display: table !important
    }

    .d-md-table-cell {
        display: table-cell !important
    }

    .d-md-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-md-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width:992px) {
    .d-lg-none {
        display: none !important
    }

    .d-lg-inline {
        display: inline !important
    }

    .d-lg-inline-block {
        display: inline-block !important
    }

    .d-lg-block {
        display: block !important
    }

    .d-lg-table {
        display: table !important
    }

    .d-lg-table-cell {
        display: table-cell !important
    }

    .d-lg-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-lg-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width:1200px) {
    .d-xl-none {
        display: none !important
    }

    .d-xl-inline {
        display: inline !important
    }

    .d-xl-inline-block {
        display: inline-block !important
    }

    .d-xl-block {
        display: block !important
    }

    .d-xl-table {
        display: table !important
    }

    .d-xl-table-cell {
        display: table-cell !important
    }

    .d-xl-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-xl-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

.d-print-block {
    display: none !important
}

@media print {
    .d-print-block {
        display: block !important
    }
}

.d-print-inline {
    display: none !important
}

@media print {
    .d-print-inline {
        display: inline !important
    }
}

.d-print-inline-block {
    display: none !important
}

@media print {
    .d-print-inline-block {
        display: inline-block !important
    }

    .d-print-none {
        display: none !important
    }
}

.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden
}

.embed-responsive::before {
    display: block;
    content: ""
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
}

.fixed-bottom,
.fixed-top {
    position: fixed;
    right: 0;
    z-index: 1030;
    left: 0
}

.embed-responsive-21by9::before {
    padding-top: 42.85714%
}

.embed-responsive-16by9::before {
    padding-top: 56.25%
}

.embed-responsive-4by3::before {
    padding-top: 75%
}

.embed-responsive-1by1::before {
    padding-top: 100%
}

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
}

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
}

.flex-column-reverse,
.flex-row-reverse {
    -webkit-box-direction: reverse !important
}

.flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
}

.flex-column-reverse {
    -webkit-box-orient: vertical !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
}

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
}

.flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
}

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
}

.justify-content-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
}

.justify-content-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
}

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
}

.justify-content-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
}

.justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
}

.align-items-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
}

.align-items-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
}

.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
}

.align-items-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
}

.align-items-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
}

.align-content-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
}

.align-content-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
}

.align-content-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
}

.align-content-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
}

.align-content-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
}

.align-content-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
}

.align-self-auto {
    -ms-flex-item-align: auto !important;
    -ms-grid-row-align: auto !important;
    align-self: auto !important
}

.align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
}

.align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
}

.align-self-center {
    -ms-flex-item-align: center !important;
    -ms-grid-row-align: center !important;
    align-self: center !important
}

.align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
}

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    -ms-grid-row-align: stretch !important;
    align-self: stretch !important
}

@media (min-width:576px) {

    .flex-sm-column,
    .flex-sm-row {
        -webkit-box-direction: normal !important
    }

    .flex-sm-row {
        -webkit-box-orient: horizontal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-sm-column {
        -webkit-box-orient: vertical !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-sm-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-sm-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-sm-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .justify-content-sm-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-sm-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-sm-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-sm-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-sm-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-sm-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-sm-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-sm-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-sm-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-sm-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-sm-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-sm-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-sm-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-sm-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-sm-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-sm-auto {
        -ms-flex-item-align: auto !important;
        -ms-grid-row-align: auto !important;
        align-self: auto !important
    }

    .align-self-sm-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-sm-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-sm-center {
        -ms-flex-item-align: center !important;
        -ms-grid-row-align: center !important;
        align-self: center !important
    }

    .align-self-sm-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-sm-stretch {
        -ms-flex-item-align: stretch !important;
        -ms-grid-row-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width:768px) {

    .flex-md-column,
    .flex-md-row {
        -webkit-box-direction: normal !important
    }

    .flex-md-row {
        -webkit-box-orient: horizontal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-md-column {
        -webkit-box-orient: vertical !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-md-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-md-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-md-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-md-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .justify-content-md-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-md-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-md-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-md-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-md-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-md-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-md-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-md-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-md-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-md-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-md-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-md-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-md-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-md-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-md-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-md-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-md-auto {
        -ms-flex-item-align: auto !important;
        -ms-grid-row-align: auto !important;
        align-self: auto !important
    }

    .align-self-md-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-md-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-md-center {
        -ms-flex-item-align: center !important;
        -ms-grid-row-align: center !important;
        align-self: center !important
    }

    .align-self-md-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-md-stretch {
        -ms-flex-item-align: stretch !important;
        -ms-grid-row-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width:992px) {

    .flex-lg-column,
    .flex-lg-row {
        -webkit-box-direction: normal !important
    }

    .flex-lg-row {
        -webkit-box-orient: horizontal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-lg-column {
        -webkit-box-orient: vertical !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-lg-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-lg-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-lg-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .justify-content-lg-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-lg-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-lg-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-lg-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-lg-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-lg-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-lg-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-lg-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-lg-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-lg-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-lg-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-lg-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-lg-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-lg-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-lg-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-lg-auto {
        -ms-flex-item-align: auto !important;
        -ms-grid-row-align: auto !important;
        align-self: auto !important
    }

    .align-self-lg-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-lg-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-lg-center {
        -ms-flex-item-align: center !important;
        -ms-grid-row-align: center !important;
        align-self: center !important
    }

    .align-self-lg-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-lg-stretch {
        -ms-flex-item-align: stretch !important;
        -ms-grid-row-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width:1200px) {

    .flex-xl-column,
    .flex-xl-row {
        -webkit-box-direction: normal !important
    }

    .flex-xl-row {
        -webkit-box-orient: horizontal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-xl-column {
        -webkit-box-orient: vertical !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-xl-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-xl-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-xl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .justify-content-xl-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-xl-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-xl-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-xl-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-xl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-xl-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-xl-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-xl-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-xl-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-xl-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-xl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-xl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-xl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-xl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-xl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-xl-auto {
        -ms-flex-item-align: auto !important;
        -ms-grid-row-align: auto !important;
        align-self: auto !important
    }

    .align-self-xl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-xl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-xl-center {
        -ms-flex-item-align: center !important;
        -ms-grid-row-align: center !important;
        align-self: center !important
    }

    .align-self-xl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-xl-stretch {
        -ms-flex-item-align: stretch !important;
        -ms-grid-row-align: stretch !important;
        align-self: stretch !important
    }
}

.messages,
.toolbar,
.user__info {
    -webkit-box-orient: horizontal
}

.float-left {
    float: left !important
}

.float-right {
    float: right !important
}

.float-none {
    float: none !important
}

@media (min-width:576px) {
    .float-sm-left {
        float: left !important
    }

    .float-sm-right {
        float: right !important
    }

    .float-sm-none {
        float: none !important
    }
}

@media (min-width:768px) {
    .float-md-left {
        float: left !important
    }

    .float-md-right {
        float: right !important
    }

    .float-md-none {
        float: none !important
    }
}

.fixed-top {
    top: 0
}

.fixed-bottom {
    bottom: 0
}

@supports ((position:-webkit-sticky) or (position:sticky)) {
    .sticky-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    border: 0
}

.pr-0,
.px-0 {
    padding-right: 0 !important
}

.pl-0,
.px-0 {
    padding-left: 0 !important
}

.pt-0,
.py-0 {
    padding-top: 0 !important
}

.pb-0,
.py-0 {
    padding-bottom: 0 !important
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
    -webkit-clip-path: none;
    clip-path: none
}

.w-25 {
    width: 25% !important
}

.w-50 {
    width: 50% !important
}

.w-75 {
    width: 75% !important
}

.w-100 {
    width: 100% !important
}

.h-25 {
    height: 25% !important
}

.h-50 {
    height: 50% !important
}

.h-75 {
    height: 75% !important
}

.h-100 {
    height: 100% !important
}

.mw-100 {
    max-width: 100% !important
}

.mh-100 {
    max-height: 100% !important
}

.m-0 {
    margin: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.mr-1,
.mx-1 {
    margin-right: .25rem !important
}

.ml-1,
.mx-1 {
    margin-left: .25rem !important
}

.my-0 {
    margin-bottom: 0 !important
}

.mt-1,
.my-1 {
    margin-top: .25rem !important
}

.mb-1,
.my-1 {
    margin-bottom: .25rem !important
}

.m-1 {
    margin: .25rem !important
}

.mr-2,
.mx-2 {
    margin-right: .5rem !important
}

.ml-2,
.mx-2 {
    margin-left: .5rem !important
}

.mt-2,
.my-2 {
    margin-top: .5rem !important
}

.mb-2,
.my-2 {
    margin-bottom: .5rem !important
}

.m-2 {
    margin: .5rem !important
}

.mr-3,
.mx-3 {
    margin-right: 1rem !important
}

.ml-3,
.mx-3 {
    margin-left: 1rem !important
}

.mt-3,
.my-3 {
    margin-top: 1rem !important
}

.mb-3,
.my-3 {
    margin-bottom: 1rem !important
}

.m-3 {
    margin: 1rem !important
}

.mr-4,
.mx-4 {
    margin-right: 1.5rem !important
}

.ml-4,
.mx-4 {
    margin-left: 1.5rem !important
}

.mt-4,
.my-4 {
    margin-top: 1.5rem !important
}

.mb-4,
.my-4 {
    margin-bottom: 1.5rem !important
}

.m-4 {
    margin: 1.5rem !important
}

.mr-5,
.mx-5 {
    margin-right: 3rem !important
}

.ml-5,
.mx-5 {
    margin-left: 3rem !important
}

.mt-5,
.my-5 {
    margin-top: 3rem !important
}

.mb-5,
.my-5 {
    margin-bottom: 3rem !important
}

.m-5 {
    margin: 3rem !important
}

.mr-auto,
.mx-auto {
    margin-right: auto !important
}

.ml-auto,
.mx-auto {
    margin-left: auto !important
}

.mt-auto,
.my-auto {
    margin-top: auto !important
}

.mb-auto,
.my-auto {
    margin-bottom: auto !important
}

.p-0 {
    padding: 0 !important
}

.pr-1,
.px-1 {
    padding-right: .25rem !important
}

.pl-1,
.px-1 {
    padding-left: .25rem !important
}

.pt-1,
.py-1 {
    padding-top: .25rem !important
}

.pb-1,
.py-1 {
    padding-bottom: .25rem !important
}

.p-1 {
    padding: .25rem !important
}

.pr-2,
.px-2 {
    padding-right: .5rem !important
}

.pl-2,
.px-2 {
    padding-left: .5rem !important
}

.pt-2,
.py-2 {
    padding-top: .5rem !important
}

.pb-2,
.py-2 {
    padding-bottom: .5rem !important
}

.p-2 {
    padding: .5rem !important
}

.pr-3,
.px-3 {
    padding-right: 1rem !important
}

.pl-3,
.px-3 {
    padding-left: 1rem !important
}

.pt-3,
.py-3 {
    padding-top: 1rem !important
}

.pb-3,
.py-3 {
    padding-bottom: 1rem !important
}

.p-3 {
    padding: 1rem !important
}

.pr-4,
.px-4 {
    padding-right: 1.5rem !important
}

.pl-4,
.px-4 {
    padding-left: 1.5rem !important
}

.pt-4,
.py-4 {
    padding-top: 1.5rem !important
}

.pb-4,
.py-4 {
    padding-bottom: 1.5rem !important
}

.p-4 {
    padding: 1.5rem !important
}

.pr-5,
.px-5 {
    padding-right: 3rem !important
}

.pl-5,
.px-5 {
    padding-left: 3rem !important
}

.pt-5,
.py-5 {
    padding-top: 3rem !important
}

.pb-5,
.py-5 {
    padding-bottom: 3rem !important
}

.p-5 {
    padding: 3rem !important
}

.m-auto {
    margin: auto !important
}

@media (min-width:576px) {

    .mr-sm-0,
    .mx-sm-0 {
        margin-right: 0 !important
    }

    .ml-sm-0,
    .mx-sm-0 {
        margin-left: 0 !important
    }

    .mt-sm-0,
    .my-sm-0 {
        margin-top: 0 !important
    }

    .mb-sm-0,
    .my-sm-0 {
        margin-bottom: 0 !important
    }

    .pr-sm-0,
    .px-sm-0 {
        padding-right: 0 !important
    }

    .pl-sm-0,
    .px-sm-0 {
        padding-left: 0 !important
    }

    .pt-sm-0,
    .py-sm-0 {
        padding-top: 0 !important
    }

    .pb-sm-0,
    .py-sm-0 {
        padding-bottom: 0 !important
    }

    .m-sm-0 {
        margin: 0 !important
    }

    .mr-sm-1,
    .mx-sm-1 {
        margin-right: .25rem !important
    }

    .ml-sm-1,
    .mx-sm-1 {
        margin-left: .25rem !important
    }

    .mt-sm-1,
    .my-sm-1 {
        margin-top: .25rem !important
    }

    .mb-sm-1,
    .my-sm-1 {
        margin-bottom: .25rem !important
    }

    .m-sm-1 {
        margin: .25rem !important
    }

    .mr-sm-2,
    .mx-sm-2 {
        margin-right: .5rem !important
    }

    .ml-sm-2,
    .mx-sm-2 {
        margin-left: .5rem !important
    }

    .mt-sm-2,
    .my-sm-2 {
        margin-top: .5rem !important
    }

    .mb-sm-2,
    .my-sm-2 {
        margin-bottom: .5rem !important
    }

    .m-sm-2 {
        margin: .5rem !important
    }

    .mr-sm-3,
    .mx-sm-3 {
        margin-right: 1rem !important
    }

    .ml-sm-3,
    .mx-sm-3 {
        margin-left: 1rem !important
    }

    .mt-sm-3,
    .my-sm-3 {
        margin-top: 1rem !important
    }

    .mb-sm-3,
    .my-sm-3 {
        margin-bottom: 1rem !important
    }

    .m-sm-3 {
        margin: 1rem !important
    }

    .mr-sm-4,
    .mx-sm-4 {
        margin-right: 1.5rem !important
    }

    .ml-sm-4,
    .mx-sm-4 {
        margin-left: 1.5rem !important
    }

    .mt-sm-4,
    .my-sm-4 {
        margin-top: 1.5rem !important
    }

    .mb-sm-4,
    .my-sm-4 {
        margin-bottom: 1.5rem !important
    }

    .m-sm-4 {
        margin: 1.5rem !important
    }

    .mr-sm-5,
    .mx-sm-5 {
        margin-right: 3rem !important
    }

    .ml-sm-5,
    .mx-sm-5 {
        margin-left: 3rem !important
    }

    .mt-sm-5,
    .my-sm-5 {
        margin-top: 3rem !important
    }

    .mb-sm-5,
    .my-sm-5 {
        margin-bottom: 3rem !important
    }

    .m-sm-5 {
        margin: 3rem !important
    }

    .mr-sm-auto,
    .mx-sm-auto {
        margin-right: auto !important
    }

    .ml-sm-auto,
    .mx-sm-auto {
        margin-left: auto !important
    }

    .mt-sm-auto,
    .my-sm-auto {
        margin-top: auto !important
    }

    .mb-sm-auto,
    .my-sm-auto {
        margin-bottom: auto !important
    }

    .p-sm-0 {
        padding: 0 !important
    }

    .pr-sm-1,
    .px-sm-1 {
        padding-right: .25rem !important
    }

    .pl-sm-1,
    .px-sm-1 {
        padding-left: .25rem !important
    }

    .pt-sm-1,
    .py-sm-1 {
        padding-top: .25rem !important
    }

    .pb-sm-1,
    .py-sm-1 {
        padding-bottom: .25rem !important
    }

    .p-sm-1 {
        padding: .25rem !important
    }

    .pr-sm-2,
    .px-sm-2 {
        padding-right: .5rem !important
    }

    .pl-sm-2,
    .px-sm-2 {
        padding-left: .5rem !important
    }

    .pt-sm-2,
    .py-sm-2 {
        padding-top: .5rem !important
    }

    .pb-sm-2,
    .py-sm-2 {
        padding-bottom: .5rem !important
    }

    .p-sm-2 {
        padding: .5rem !important
    }

    .pr-sm-3,
    .px-sm-3 {
        padding-right: 1rem !important
    }

    .pl-sm-3,
    .px-sm-3 {
        padding-left: 1rem !important
    }

    .pt-sm-3,
    .py-sm-3 {
        padding-top: 1rem !important
    }

    .pb-sm-3,
    .py-sm-3 {
        padding-bottom: 1rem !important
    }

    .p-sm-3 {
        padding: 1rem !important
    }

    .pr-sm-4,
    .px-sm-4 {
        padding-right: 1.5rem !important
    }

    .pl-sm-4,
    .px-sm-4 {
        padding-left: 1.5rem !important
    }

    .pt-sm-4,
    .py-sm-4 {
        padding-top: 1.5rem !important
    }

    .pb-sm-4,
    .py-sm-4 {
        padding-bottom: 1.5rem !important
    }

    .p-sm-4 {
        padding: 1.5rem !important
    }

    .pr-sm-5,
    .px-sm-5 {
        padding-right: 3rem !important
    }

    .pl-sm-5,
    .px-sm-5 {
        padding-left: 3rem !important
    }

    .pt-sm-5,
    .py-sm-5 {
        padding-top: 3rem !important
    }

    .pb-sm-5,
    .py-sm-5 {
        padding-bottom: 3rem !important
    }

    .p-sm-5 {
        padding: 3rem !important
    }

    .m-sm-auto {
        margin: auto !important
    }
}

@media (min-width:768px) {

    .mr-md-0,
    .mx-md-0 {
        margin-right: 0 !important
    }

    .ml-md-0,
    .mx-md-0 {
        margin-left: 0 !important
    }

    .mt-md-0,
    .my-md-0 {
        margin-top: 0 !important
    }

    .mb-md-0,
    .my-md-0 {
        margin-bottom: 0 !important
    }

    .pr-md-0,
    .px-md-0 {
        padding-right: 0 !important
    }

    .pl-md-0,
    .px-md-0 {
        padding-left: 0 !important
    }

    .pt-md-0,
    .py-md-0 {
        padding-top: 0 !important
    }

    .pb-md-0,
    .py-md-0 {
        padding-bottom: 0 !important
    }

    .m-md-0 {
        margin: 0 !important
    }

    .mr-md-1,
    .mx-md-1 {
        margin-right: .25rem !important
    }

    .ml-md-1,
    .mx-md-1 {
        margin-left: .25rem !important
    }

    .mt-md-1,
    .my-md-1 {
        margin-top: .25rem !important
    }

    .mb-md-1,
    .my-md-1 {
        margin-bottom: .25rem !important
    }

    .m-md-1 {
        margin: .25rem !important
    }

    .mr-md-2,
    .mx-md-2 {
        margin-right: .5rem !important
    }

    .ml-md-2,
    .mx-md-2 {
        margin-left: .5rem !important
    }

    .mt-md-2,
    .my-md-2 {
        margin-top: .5rem !important
    }

    .mb-md-2,
    .my-md-2 {
        margin-bottom: .5rem !important
    }

    .m-md-2 {
        margin: .5rem !important
    }

    .mr-md-3,
    .mx-md-3 {
        margin-right: 1rem !important
    }

    .ml-md-3,
    .mx-md-3 {
        margin-left: 1rem !important
    }

    .mt-md-3,
    .my-md-3 {
        margin-top: 1rem !important
    }

    .mb-md-3,
    .my-md-3 {
        margin-bottom: 1rem !important
    }

    .m-md-3 {
        margin: 1rem !important
    }

    .mr-md-4,
    .mx-md-4 {
        margin-right: 1.5rem !important
    }

    .ml-md-4,
    .mx-md-4 {
        margin-left: 1.5rem !important
    }

    .mt-md-4,
    .my-md-4 {
        margin-top: 1.5rem !important
    }

    .mb-md-4,
    .my-md-4 {
        margin-bottom: 1.5rem !important
    }

    .m-md-4 {
        margin: 1.5rem !important
    }

    .mr-md-5,
    .mx-md-5 {
        margin-right: 3rem !important
    }

    .ml-md-5,
    .mx-md-5 {
        margin-left: 3rem !important
    }

    .mt-md-5,
    .my-md-5 {
        margin-top: 3rem !important
    }

    .mb-md-5,
    .my-md-5 {
        margin-bottom: 3rem !important
    }

    .m-md-5 {
        margin: 3rem !important
    }

    .mr-md-auto,
    .mx-md-auto {
        margin-right: auto !important
    }

    .ml-md-auto,
    .mx-md-auto {
        margin-left: auto !important
    }

    .mt-md-auto,
    .my-md-auto {
        margin-top: auto !important
    }

    .mb-md-auto,
    .my-md-auto {
        margin-bottom: auto !important
    }

    .p-md-0 {
        padding: 0 !important
    }

    .pr-md-1,
    .px-md-1 {
        padding-right: .25rem !important
    }

    .pl-md-1,
    .px-md-1 {
        padding-left: .25rem !important
    }

    .pt-md-1,
    .py-md-1 {
        padding-top: .25rem !important
    }

    .pb-md-1,
    .py-md-1 {
        padding-bottom: .25rem !important
    }

    .p-md-1 {
        padding: .25rem !important
    }

    .pr-md-2,
    .px-md-2 {
        padding-right: .5rem !important
    }

    .pl-md-2,
    .px-md-2 {
        padding-left: .5rem !important
    }

    .pt-md-2,
    .py-md-2 {
        padding-top: .5rem !important
    }

    .pb-md-2,
    .py-md-2 {
        padding-bottom: .5rem !important
    }

    .p-md-2 {
        padding: .5rem !important
    }

    .pr-md-3,
    .px-md-3 {
        padding-right: 1rem !important
    }

    .pl-md-3,
    .px-md-3 {
        padding-left: 1rem !important
    }

    .pt-md-3,
    .py-md-3 {
        padding-top: 1rem !important
    }

    .pb-md-3,
    .py-md-3 {
        padding-bottom: 1rem !important
    }

    .p-md-3 {
        padding: 1rem !important
    }

    .pr-md-4,
    .px-md-4 {
        padding-right: 1.5rem !important
    }

    .pl-md-4,
    .px-md-4 {
        padding-left: 1.5rem !important
    }

    .pt-md-4,
    .py-md-4 {
        padding-top: 1.5rem !important
    }

    .pb-md-4,
    .py-md-4 {
        padding-bottom: 1.5rem !important
    }

    .p-md-4 {
        padding: 1.5rem !important
    }

    .pr-md-5,
    .px-md-5 {
        padding-right: 3rem !important
    }

    .pl-md-5,
    .px-md-5 {
        padding-left: 3rem !important
    }

    .pt-md-5,
    .py-md-5 {
        padding-top: 3rem !important
    }

    .pb-md-5,
    .py-md-5 {
        padding-bottom: 3rem !important
    }

    .p-md-5 {
        padding: 3rem !important
    }

    .m-md-auto {
        margin: auto !important
    }
}

.text-justify {
    text-align: justify !important
}

.text-nowrap {
    white-space: nowrap !important
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media (min-width:576px) {
    .text-sm-left {
        text-align: left !important
    }

    .text-sm-right {
        text-align: right !important
    }

    .text-sm-center {
        text-align: center !important
    }
}

@media (min-width:768px) {
    .text-md-left {
        text-align: left !important
    }

    .text-md-right {
        text-align: right !important
    }

    .text-md-center {
        text-align: center !important
    }
}

@media (min-width:992px) {
    .float-lg-left {
        float: left !important
    }

    .float-lg-right {
        float: right !important
    }

    .float-lg-none {
        float: none !important
    }

    .mr-lg-0,
    .mx-lg-0 {
        margin-right: 0 !important
    }

    .ml-lg-0,
    .mx-lg-0 {
        margin-left: 0 !important
    }

    .mt-lg-0,
    .my-lg-0 {
        margin-top: 0 !important
    }

    .mb-lg-0,
    .my-lg-0 {
        margin-bottom: 0 !important
    }

    .pr-lg-0,
    .px-lg-0 {
        padding-right: 0 !important
    }

    .pl-lg-0,
    .px-lg-0 {
        padding-left: 0 !important
    }

    .pt-lg-0,
    .py-lg-0 {
        padding-top: 0 !important
    }

    .pb-lg-0,
    .py-lg-0 {
        padding-bottom: 0 !important
    }

    .m-lg-0 {
        margin: 0 !important
    }

    .mr-lg-1,
    .mx-lg-1 {
        margin-right: .25rem !important
    }

    .ml-lg-1,
    .mx-lg-1 {
        margin-left: .25rem !important
    }

    .mt-lg-1,
    .my-lg-1 {
        margin-top: .25rem !important
    }

    .mb-lg-1,
    .my-lg-1 {
        margin-bottom: .25rem !important
    }

    .m-lg-1 {
        margin: .25rem !important
    }

    .mr-lg-2,
    .mx-lg-2 {
        margin-right: .5rem !important
    }

    .ml-lg-2,
    .mx-lg-2 {
        margin-left: .5rem !important
    }

    .mt-lg-2,
    .my-lg-2 {
        margin-top: .5rem !important
    }

    .mb-lg-2,
    .my-lg-2 {
        margin-bottom: .5rem !important
    }

    .m-lg-2 {
        margin: .5rem !important
    }

    .mr-lg-3,
    .mx-lg-3 {
        margin-right: 1rem !important
    }

    .ml-lg-3,
    .mx-lg-3 {
        margin-left: 1rem !important
    }

    .mt-lg-3,
    .my-lg-3 {
        margin-top: 1rem !important
    }

    .mb-lg-3,
    .my-lg-3 {
        margin-bottom: 1rem !important
    }

    .m-lg-3 {
        margin: 1rem !important
    }

    .mr-lg-4,
    .mx-lg-4 {
        margin-right: 1.5rem !important
    }

    .ml-lg-4,
    .mx-lg-4 {
        margin-left: 1.5rem !important
    }

    .mt-lg-4,
    .my-lg-4 {
        margin-top: 1.5rem !important
    }

    .mb-lg-4,
    .my-lg-4 {
        margin-bottom: 1.5rem !important
    }

    .m-lg-4 {
        margin: 1.5rem !important
    }

    .mr-lg-5,
    .mx-lg-5 {
        margin-right: 3rem !important
    }

    .ml-lg-5,
    .mx-lg-5 {
        margin-left: 3rem !important
    }

    .mt-lg-5,
    .my-lg-5 {
        margin-top: 3rem !important
    }

    .mb-lg-5,
    .my-lg-5 {
        margin-bottom: 3rem !important
    }

    .m-lg-5 {
        margin: 3rem !important
    }

    .mr-lg-auto,
    .mx-lg-auto {
        margin-right: auto !important
    }

    .ml-lg-auto,
    .mx-lg-auto {
        margin-left: auto !important
    }

    .mt-lg-auto,
    .my-lg-auto {
        margin-top: auto !important
    }

    .mb-lg-auto,
    .my-lg-auto {
        margin-bottom: auto !important
    }

    .p-lg-0 {
        padding: 0 !important
    }

    .pr-lg-1,
    .px-lg-1 {
        padding-right: .25rem !important
    }

    .pl-lg-1,
    .px-lg-1 {
        padding-left: .25rem !important
    }

    .pt-lg-1,
    .py-lg-1 {
        padding-top: .25rem !important
    }

    .pb-lg-1,
    .py-lg-1 {
        padding-bottom: .25rem !important
    }

    .p-lg-1 {
        padding: .25rem !important
    }

    .pr-lg-2,
    .px-lg-2 {
        padding-right: .5rem !important
    }

    .pl-lg-2,
    .px-lg-2 {
        padding-left: .5rem !important
    }

    .pt-lg-2,
    .py-lg-2 {
        padding-top: .5rem !important
    }

    .pb-lg-2,
    .py-lg-2 {
        padding-bottom: .5rem !important
    }

    .p-lg-2 {
        padding: .5rem !important
    }

    .pr-lg-3,
    .px-lg-3 {
        padding-right: 1rem !important
    }

    .pl-lg-3,
    .px-lg-3 {
        padding-left: 1rem !important
    }

    .pt-lg-3,
    .py-lg-3 {
        padding-top: 1rem !important
    }

    .pb-lg-3,
    .py-lg-3 {
        padding-bottom: 1rem !important
    }

    .p-lg-3 {
        padding: 1rem !important
    }

    .pr-lg-4,
    .px-lg-4 {
        padding-right: 1.5rem !important
    }

    .pl-lg-4,
    .px-lg-4 {
        padding-left: 1.5rem !important
    }

    .pt-lg-4,
    .py-lg-4 {
        padding-top: 1.5rem !important
    }

    .pb-lg-4,
    .py-lg-4 {
        padding-bottom: 1.5rem !important
    }

    .p-lg-4 {
        padding: 1.5rem !important
    }

    .pr-lg-5,
    .px-lg-5 {
        padding-right: 3rem !important
    }

    .pl-lg-5,
    .px-lg-5 {
        padding-left: 3rem !important
    }

    .pt-lg-5,
    .py-lg-5 {
        padding-top: 3rem !important
    }

    .pb-lg-5,
    .py-lg-5 {
        padding-bottom: 3rem !important
    }

    .p-lg-5 {
        padding: 3rem !important
    }

    .m-lg-auto {
        margin: auto !important
    }

    .text-lg-left {
        text-align: left !important
    }

    .text-lg-right {
        text-align: right !important
    }

    .text-lg-center {
        text-align: center !important
    }
}

@media (min-width:1200px) {
    .float-xl-left {
        float: left !important
    }

    .float-xl-right {
        float: right !important
    }

    .float-xl-none {
        float: none !important
    }

    .mr-xl-0,
    .mx-xl-0 {
        margin-right: 0 !important
    }

    .ml-xl-0,
    .mx-xl-0 {
        margin-left: 0 !important
    }

    .mt-xl-0,
    .my-xl-0 {
        margin-top: 0 !important
    }

    .mb-xl-0,
    .my-xl-0 {
        margin-bottom: 0 !important
    }

    .pr-xl-0,
    .px-xl-0 {
        padding-right: 0 !important
    }

    .pl-xl-0,
    .px-xl-0 {
        padding-left: 0 !important
    }

    .pt-xl-0,
    .py-xl-0 {
        padding-top: 0 !important
    }

    .pb-xl-0,
    .py-xl-0 {
        padding-bottom: 0 !important
    }

    .m-xl-0 {
        margin: 0 !important
    }

    .mr-xl-1,
    .mx-xl-1 {
        margin-right: .25rem !important
    }

    .ml-xl-1,
    .mx-xl-1 {
        margin-left: .25rem !important
    }

    .mt-xl-1,
    .my-xl-1 {
        margin-top: .25rem !important
    }

    .mb-xl-1,
    .my-xl-1 {
        margin-bottom: .25rem !important
    }

    .m-xl-1 {
        margin: .25rem !important
    }

    .mr-xl-2,
    .mx-xl-2 {
        margin-right: .5rem !important
    }

    .ml-xl-2,
    .mx-xl-2 {
        margin-left: .5rem !important
    }

    .mt-xl-2,
    .my-xl-2 {
        margin-top: .5rem !important
    }

    .mb-xl-2,
    .my-xl-2 {
        margin-bottom: .5rem !important
    }

    .m-xl-2 {
        margin: .5rem !important
    }

    .mr-xl-3,
    .mx-xl-3 {
        margin-right: 1rem !important
    }

    .ml-xl-3,
    .mx-xl-3 {
        margin-left: 1rem !important
    }

    .mt-xl-3,
    .my-xl-3 {
        margin-top: 1rem !important
    }

    .mb-xl-3,
    .my-xl-3 {
        margin-bottom: 1rem !important
    }

    .m-xl-3 {
        margin: 1rem !important
    }

    .mr-xl-4,
    .mx-xl-4 {
        margin-right: 1.5rem !important
    }

    .ml-xl-4,
    .mx-xl-4 {
        margin-left: 1.5rem !important
    }

    .mt-xl-4,
    .my-xl-4 {
        margin-top: 1.5rem !important
    }

    .mb-xl-4,
    .my-xl-4 {
        margin-bottom: 1.5rem !important
    }

    .m-xl-4 {
        margin: 1.5rem !important
    }

    .mr-xl-5,
    .mx-xl-5 {
        margin-right: 3rem !important
    }

    .ml-xl-5,
    .mx-xl-5 {
        margin-left: 3rem !important
    }

    .mt-xl-5,
    .my-xl-5 {
        margin-top: 3rem !important
    }

    .mb-xl-5,
    .my-xl-5 {
        margin-bottom: 3rem !important
    }

    .m-xl-5 {
        margin: 3rem !important
    }

    .mr-xl-auto,
    .mx-xl-auto {
        margin-right: auto !important
    }

    .ml-xl-auto,
    .mx-xl-auto {
        margin-left: auto !important
    }

    .mt-xl-auto,
    .my-xl-auto {
        margin-top: auto !important
    }

    .mb-xl-auto,
    .my-xl-auto {
        margin-bottom: auto !important
    }

    .p-xl-0 {
        padding: 0 !important
    }

    .pr-xl-1,
    .px-xl-1 {
        padding-right: .25rem !important
    }

    .pl-xl-1,
    .px-xl-1 {
        padding-left: .25rem !important
    }

    .pt-xl-1,
    .py-xl-1 {
        padding-top: .25rem !important
    }

    .pb-xl-1,
    .py-xl-1 {
        padding-bottom: .25rem !important
    }

    .p-xl-1 {
        padding: .25rem !important
    }

    .pr-xl-2,
    .px-xl-2 {
        padding-right: .5rem !important
    }

    .pl-xl-2,
    .px-xl-2 {
        padding-left: .5rem !important
    }

    .pt-xl-2,
    .py-xl-2 {
        padding-top: .5rem !important
    }

    .pb-xl-2,
    .py-xl-2 {
        padding-bottom: .5rem !important
    }

    .p-xl-2 {
        padding: .5rem !important
    }

    .pr-xl-3,
    .px-xl-3 {
        padding-right: 1rem !important
    }

    .pl-xl-3,
    .px-xl-3 {
        padding-left: 1rem !important
    }

    .pt-xl-3,
    .py-xl-3 {
        padding-top: 1rem !important
    }

    .pb-xl-3,
    .py-xl-3 {
        padding-bottom: 1rem !important
    }

    .p-xl-3 {
        padding: 1rem !important
    }

    .pr-xl-4,
    .px-xl-4 {
        padding-right: 1.5rem !important
    }

    .pl-xl-4,
    .px-xl-4 {
        padding-left: 1.5rem !important
    }

    .pt-xl-4,
    .py-xl-4 {
        padding-top: 1.5rem !important
    }

    .pb-xl-4,
    .py-xl-4 {
        padding-bottom: 1.5rem !important
    }

    .p-xl-4 {
        padding: 1.5rem !important
    }

    .pr-xl-5,
    .px-xl-5 {
        padding-right: 3rem !important
    }

    .pl-xl-5,
    .px-xl-5 {
        padding-left: 3rem !important
    }

    .pt-xl-5,
    .py-xl-5 {
        padding-top: 3rem !important
    }

    .pb-xl-5,
    .py-xl-5 {
        padding-bottom: 3rem !important
    }

    .p-xl-5 {
        padding: 3rem !important
    }

    .m-xl-auto {
        margin: auto !important
    }

    .text-xl-left {
        text-align: left !important
    }

    .text-xl-right {
        text-align: right !important
    }

    .text-xl-center {
        text-align: center !important
    }
}

.text-lowercase {
    text-transform: lowercase !important
}

.text-uppercase {
    text-transform: uppercase !important
}

.text-capitalize {
    text-transform: capitalize !important
}

.avatar-char,
.card-link,
.contacts__btn,
.content__title:not(.content__title--alt)>h1,
.fc th,
.listview__header,
.modal-footer>.btn-link,
.price-table__action,
.price-table__title,
.q-a__stat>span>small,
.view-more {
    text-transform: uppercase
}

.font-weight-normal {
    font-weight: 400
}

.font-weight-bold {
    font-weight: 600
}

.font-italic {
    font-style: italic
}

.text-primary {
    color: #007bff !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #0062cc !important
}

.text-secondary {
    color: #fff !important
}

a.text-secondary:focus,
a.text-secondary:hover {
    color: #e6e6e6 !important
}

.text-success {
    color: #28a745 !important
}

a.text-success:focus,
a.text-success:hover {
    color: #1e7e34 !important
}

.text-info {
    color: #17a2b8 !important
}

a.text-info:focus,
a.text-info:hover {
    color: #117a8b !important
}

.text-warning {
    color: #ffc107 !important
}

a.text-warning:focus,
a.text-warning:hover {
    color: #d39e00 !important
}

.text-danger {
    color: #dc3545 !important
}

a.text-danger:focus,
a.text-danger:hover {
    color: #bd2130 !important
}

.text-muted {
    color: rgba(255, 255, 255, .7) !important
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.card-link,
.card-subtitle {
    color: rgba(255, 255, 255, .7)
}

.visible {
    visibility: visible !important
}

.invisible {
    visibility: hidden !important
}

.dropdown,
.dropup {
    position: relative
}

.dropdown-item {
    transition: background-color .3s, color .3s;
    line-height: 1.5
}

.dropdown-menu {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5)
}

.dropdown-header {
    font-size: 1rem;
    padding: 1.1rem 1.8rem .9rem;
    border-bottom: 1px solid rgba(255, 255, 255, .06)
}

.dropdown-header .actions {
    position: absolute;
    top: .4rem;
    right: .8rem
}

.dropdown-menu--block {
    width: 320px
}

@media (max-width:575px) {
    .dropdown-menu--block {
        width: 100%
    }
}

.dropdown-menu--icon .dropdown-item>i {
    line-height: 100%;
    vertical-align: top;
    font-size: 1.4rem;
    width: 2rem
}

.progress {
    border-radius: 0;
    height: 3px
}

.progress-bar {
    height: 100%
}

.card {
    margin-bottom: 2.3rem;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1)
}

.card:not([class*=border-]) {
    border: 0
}

.card-title {
    font-size: 1.25rem;
    line-height: 140%;
    margin-top: -5px
}

.card-title:last-child {
    margin-bottom: 0
}

.card-subtitle {
    font-size: 1rem;
    font-weight: 400;
    margin-top: -1.45rem;
    line-height: 1.5
}

.card-subtitle:not(:last-child) {
    margin-bottom: 2rem
}

.card-body .actions,
.card>.actions {
    position: absolute;
    right: 15px;
    z-index: 2;
    top: 18px
}

[class*=card-img] {
    width: 100%
}

.card-link {
    font-size: .98rem;
    font-weight: 600;
    display: inline-block;
    margin-top: 1rem;
    transition: color .3s
}

.card-link:hover {
    text-decoration: none;
    color: #fff
}

.card-body .card-body {
    margin-bottom: 0
}

.card-body__title {
    font-size: 1.15rem;
    color: #fff;
    margin-bottom: 1rem;
    font-weight: 400
}

.card--fullscreen {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 101;
    background-color: rgba(0, 0, 0, .96);
    overflow: auto
}

.card-header {
    padding: 1.2rem 2.2rem
}

.btn--action,
.btn--icon {
    padding: 0;
    border-radius: 50%
}

.btn-light {
    background-color: rgba(255, 255, 255, .125);
    color: #fff
}

.btn-light:hover {
    background-color: rgba(255, 255, 255, .25);
    background-image: none;
    color: #fff
}

.btn-light.active,
.btn-light:active,
.show>.btn-light.dropdown-toggle {
    background-color: rgba(255, 255, 255, .35)
}

.btn-dark {
    background-color: rgba(0, 0, 0, .25);
    color: #fff
}

.btn-dark:hover {
    background-color: rgba(0, 0, 0, .35);
    background-image: none;
    color: #fff
}

.btn-dark.active,
.btn-dark:active,
.show>.btn-dark.dropdown-toggle {
    background-color: rgba(0, 0, 0, .45)
}

.btn-outline-light:hover {
    color: #131313
}

.btn--icon {
    width: 3rem;
    height: 3rem;
    line-height: 2.7rem;
    font-size: 1.2rem;
    text-align: center
}

.btn--icon-text>.zmdi {
    font-size: 1.2rem;
    display: inline-block;
    vertical-align: top;
    margin: 1px 5px 0 0
}

.actions>*,
.load-more>a>i {
    vertical-align: middle
}

.btn--action {
    z-index: 2;
    height: 50px;
    width: 50px;
    line-height: 50px;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 3px 7px rgba(0, 0, 0, .15)
}

.btn--fixed {
    position: fixed !important;
    bottom: 30px;
    right: 30px
}

.btn-group-justified {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.btn-group-justified .btn,
.btn-group-justified .btn-group {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.btn-group-justified .btn .btn,
.btn-group-justified .btn-group .btn {
    width: 100%
}

.btn-group--colors>.btn {
    box-shadow: none !important;
    border-radius: 50% !important;
    width: 30px;
    height: 30px;
    margin-right: 4px;
    margin-bottom: 4px;
    position: relative
}

.btn-group--colors>.btn:before {
    font-family: Material-Design-Iconic-Font;
    content: "";
    font-size: 16px;
    transition: opacity .2s, -webkit-transform .2s;
    transition: transform .2s, opacity .2s;
    transition: transform .2s, opacity .2s, -webkit-transform .2s;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    line-height: 28px;
    padding-right: 3px;
    color: #fff;
    font-style: italic;
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0
}

.btn-group--colors>.btn.btn:not([class*=bg-]) {
    border: 1px solid rgba(242, 242, 242, .2)
}

.btn-group--colors>.btn.btn:not([class*=bg-]):before {
    color: rgba(255, 255, 255, .85)
}

.btn-group--colors>.btn.active:before {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group,
.btn-group-vertical .btn+.btn,
.btn-group-vertical .btn+.btn-group,
.btn-group-vertical .btn-group+.btn,
.btn-group-vertical .btn-group+.btn-group {
    margin-left: 0
}

.table th {
    font-weight: 600
}

.table thead>tr>th {
    border-top: 0;
    border-bottom: 0
}

.table thead[class*=thead-]+tbody>tr:first-child>td,
.table thead[class*=thead-]+tbody>tr:first-child>th {
    border-top: 0
}

.data-table table th {
    user-select: none;
    cursor: pointer;
    position: relative
}

.data-table table th>i.fa {
    position: absolute;
    font-style: normal;
    right: .3rem;
    bottom: .6rem;
    font-size: 1.4rem
}

.data-table table th>i.fa:before {
    font-family: Material-Design-Iconic-Font
}

.data-table table th>i.fa.fa-chevron-up:before {
    content: '\f1ce'
}

.data-table table th>i.fa.fa-chevron-down:before {
    content: '\f1cd'
}

.data-table tr>td:first-child,
.data-table tr>th:first-child {
    padding-left: 2.2rem
}

.data-table tr>td:last-child,
.data-table tr>th:last-child {
    padding-right: 2.2rem
}

.data-table__header {
    padding: 0 0 2rem
}

.data-table__filter {
    max-width: 500px
}

.data-table__filter .form-control {
    padding-left: 2rem
}

.data-table__filter:before {
    content: '\f1c3';
    font-family: Material-Design-Iconic-Font;
    font-size: 1.5rem;
    position: absolute;
    left: 0;
    bottom: .263rem
}

.data-table__footer {
    text-align: center;
    padding: 2.1rem 0
}

.form-control {
    border-width: 0 0 1px;
    padding-left: 1px;
    padding-right: 1px;
    resize: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -ms-overflow-style: none
}

.aside-toggled,
.sidebar,
.sidebar--hidden {
    overflow: hidden
}

.form-control:focus~.form-group__bar:before {
    width: 100%
}

.form-control:disabled,
.form-control[readonly] {
    opacity: .6
}

.form-group {
    position: relative
}

select::-ms-expand {
    display: none
}

.select:before {
    content: "";
    position: absolute;
    pointer-events: none;
    z-index: 1;
    right: 1px;
    bottom: 5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 8px 8px;
    border-color: transparent transparent #d1d1d1
}

.form-group__bar {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
    width: 100%
}

.form-group__bar:before {
    content: '';
    position: absolute;
    height: 1px;
    width: 0;
    bottom: 0;
    transition: all .4s;
    transition-timing-function: ease;
    background-color: #fff;
    left: 0
}

.icon-toggle {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 35px;
    cursor: pointer;
    line-height: 35px
}

.icon-toggle .zmdi {
    z-index: 2;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, .25);
    transition: color .3s
}

.icon-toggle input[type=checkbox] {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0
}

.icon-toggle input[type=checkbox]:checked~.zmdi {
    color: #fff
}

.icon-toggle:hover .zmdi {
    color: rgba(255, 255, 255, .5)
}

.input-group .form-group {
    -webkit-box-flex: 2;
    -ms-flex: 2;
    flex: 2;
    margin: 0
}

.input-group .form-group .form-control {
    width: 100%
}

.input-group .form-group .form-group__bar,
.input-group .form-group--float>label {
    z-index: 3
}

.input-group-addon {
    padding: 0 1rem !important
}

.input-group-addon+.form-group .form-control {
    padding-right: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(255, 255, 255, .2)
}

.custom-control {
    margin-bottom: 0;
    min-height: 1.5rem
}

.custom-control-indicator {
    border: 1px solid rgba(255, 255, 255, .6);
    color: #fff;
    top: .03rem;
    transition: border .2s
}

.custom-checkbox .custom-control-indicator:before {
    content: '\f26b';
    background-color: #fff;
    font-family: Material-Design-Iconic-Font;
    color: #131313;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0;
    transition: background-color .2s, opacity .2s;
    line-height: 1.2rem;
    text-align: center;
    font-size: .9rem;
    font-weight: 700
}

.custom-checkbox .custom-control-input:checked~.custom-control-indicator:before {
    opacity: 1
}

.custom-radio .custom-control-indicator:before {
    content: '';
    position: absolute;
    left: 3px;
    top: 3px;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, .9);
    z-index: 1;
    -webkit-transform: scale(0);
    transform: scale(0);
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s
}

.custom-radio .custom-control-input:checked~.custom-control-indicator:before {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.custom-control-input:checked~.custom-control-indicator {
    border-color: rgba(255, 255, 255, .9)
}

.custom-control-input:disabled~.custom-control-indicator {
    opacity: .5
}

.custom-control--char {
    margin: 0;
    font-weight: 400
}

.custom-control--char .custom-control-input {
    width: 3rem;
    height: 3rem;
    z-index: 3
}

.custom-control--char .custom-control-input:checked+.custom-control--char__helper:after {
    opacity: 1;
    color: #000
}

.custom-control--char .custom-control-input:checked+.custom-control--char__helper:before {
    opacity: 0
}

.custom-control--char .custom-control-input:checked+.custom-control--char__helper>i {
    font-size: 0;
    background-color: rgba(255, 255, 255, .75)
}

.custom-control--char:hover .custom-control--char__helper:before {
    opacity: 1
}

.custom-control--char:hover .custom-control--char__helper>i {
    font-size: 0
}

.custom-control--char__helper {
    position: absolute;
    font-style: normal;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    left: 0;
    top: 0
}

.custom-control--char__helper:after,
.custom-control--char__helper:before,
.custom-control--char__helper>i {
    position: absolute;
    text-align: center;
    left: 0;
    width: 100%;
    height: 100%;
    top: 1px;
    line-height: 3rem;
    color: #fff
}

.custom-control--char__helper:after,
.custom-control--char__helper:before {
    font-family: Material-Design-Iconic-Font;
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity .3s
}

.page-item.disabled,
.paginate_button.disabled {
    opacity: .6
}

.custom-control--char__helper:before {
    content: '\f279';
    z-index: 1
}

.custom-control--char__helper:after {
    content: '\f26b';
    z-index: 2
}

.custom-control--char__helper>i {
    font-style: normal;
    border-radius: 50%;
    font-size: 1.2rem;
    line-height: 2.9rem;
    font-weight: 600;
    background-color: rgba(255, 255, 255, .08);
    transition: font-size 150ms, background-color .3s
}

.page-link {
    border-radius: 50% !important;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    z-index: 1;
    cursor: pointer;
    transition: background-color .3s, color .3s;
    margin: 0 2px
}

.page-link>.zmdi {
    font-size: 1.5rem
}

.pagination-first .page-link,
.pagination-last .page-link,
.pagination-next .page-link,
.pagination-prev .page-link {
    font-size: 0
}

.pagination-first .page-link:before,
.pagination-last .page-link:before,
.pagination-next .page-link:before,
.pagination-prev .page-link:before {
    font-family: Material-Design-Iconic-Font;
    font-size: 1rem;
    line-height: 2.55rem
}

.pagination-prev .page-link:before {
    content: '\f2ff'
}

.pagination-next .page-link:before {
    content: '\f301'
}

.pagination-first .page-link:before,
.pagination-last .page-link:before {
    content: '\f302'
}

.pagination-first .page-link:before {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    display: inline-block
}

.alert {
    padding: 18px 24px;
    margin-bottom: 1rem;
    border: 0 solid transparent;
    border-radius: 2px
}

.alert-link {
    font-weight: 400;
    color: #fff;
    border-bottom: 1px dotted rgba(255, 255, 255, .4)
}

.alert-link:focus,
.alert-link:hover {
    opacity: .9;
    color: #fff
}

.alert-primary {
    background-color: rgba(0, 123, 255, .95);
    border-color: #007bff
}

.alert-secondary {
    background-color: rgba(255, 255, 255, .95);
    border-color: #fff
}

.alert-success {
    background-color: rgba(40, 167, 69, .95);
    border-color: #28a745
}

.alert-info {
    background-color: rgba(23, 162, 184, .95);
    border-color: #17a2b8
}

.alert-warning {
    background-color: rgba(255, 193, 7, .95);
    border-color: #ffc107
}

.alert-danger {
    background-color: rgba(220, 53, 69, .95);
    border-color: #dc3545
}

.alert-heading {
    font-weight: 600;
    font-size: 1.1rem;
    margin: .15rem 0 1rem;
    color: inherit
}

.alert-dismissible .close {
    margin-left: 2rem
}

.alert-dismissible .close>span:not(.sr-only) {
    transition: opacity .3s;
    background-color: rgba(255, 255, 255, .2);
    opacity: .8;
    line-height: 17px;
    height: 19px;
    width: 19px;
    border-radius: 50%;
    font-size: 1rem;
    display: block;
    font-weight: 600;
    color: #fff
}

.alert-dismissible .close:focus span:not(.sr-only),
.alert-dismissible .close:hover span:not(.sr-only),
.close,
.close:hover {
    opacity: 1
}

.alert--notify {
    box-shadow: 0 3px 10px rgba(0, 0, 0, .3)
}

.alert--notify:not(.alert-info):not(.alert-success):not(.alert-warning):not(.alert-danger) {
    background-color: rgba(0, 0, 0, .96)
}

.close {
    transition: opacity .3s;
    cursor: pointer
}

.breadcrumb {
    border-bottom: 1px solid rgba(255, 255, 255, .08);
    border-radius: 0
}

.breadcrumb-item+.breadcrumb-item:before {
    font-family: Material-Design-Iconic-Font;
    position: relative;
    top: 1px;
    color: #fff
}

.breadcrumb-item,
.breadcrumb-item>a {
    color: rgba(255, 255, 255, .7)
}

.breadcrumb-item:hover,
.breadcrumb-item>a:hover {
    color: #fff
}

.accordion .card {
    box-shadow: none;
    margin: 0 0 1px
}

.accordion .card-header>a {
    color: #fff;
    display: block
}

.accordion .card-header>a:hover {
    opacity: .9
}

.carousel-item img {
    width: 100%;
    border-radius: 2px
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
    background-image: none;
    position: relative
}

.carousel-control-next-icon:after,
.carousel-control-next-icon:before,
.carousel-control-prev-icon:after,
.carousel-control-prev-icon:before {
    font-family: Material-Design-Iconic-Font;
    font-size: 2rem;
    color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    text-shadow: 0 0 5px rgba(0, 0, 0, .6);
    transition: opacity 250ms linear, -webkit-transform 250ms linear;
    transition: opacity 250ms linear, transform 250ms linear;
    transition: opacity 250ms linear, transform 250ms linear, -webkit-transform 250ms linear
}

.carousel-control-next-icon:after,
.carousel-control-prev-icon:after {
    -webkit-transform: scale(5);
    transform: scale(5);
    opacity: 0
}

.carousel-control-after:hover .carousel-control-next-icon:after,
.carousel-control-after:hover .carousel-control-prev-icon:after,
.carousel-control-prev:hover .carousel-control-next-icon:after,
.carousel-control-prev:hover .carousel-control-prev-icon:after {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
}

.carousel-control-after:hover .carousel-control-next-icon:before,
.carousel-control-after:hover .carousel-control-prev-icon:before,
.carousel-control-prev:hover .carousel-control-next-icon:before,
.carousel-control-prev:hover .carousel-control-prev-icon:before {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0
}

.carousel-control-prev-icon:after,
.carousel-control-prev-icon:before {
    content: '\f2ff'
}

.carousel-control-next-icon:after,
.carousel-control-next-icon:before {
    content: '\f301'
}

.carousel-caption {
    background-color: rgba(0, 0, 0, .5);
    border-radius: 2px 2px 0 0;
    bottom: 0;
    font-weight: 600;
    padding-bottom: 35px
}

.carousel-caption h3 {
    color: #fff;
    font-size: 1.3rem
}

.modal-content {
    text-align: left;
    box-shadow: 0 5px 20px rgba(0, 0, 0, .3)
}

.modal-dialog {
    width: calc(100% - 20px)
}

.modal-footer {
    padding-top: .8rem
}

.modal-footer>.btn-link {
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    border-radius: 2px
}

.nav-link,
.nav-tabs .nav-link {
    color: rgba(255, 255, 255, .7)
}

.modal-footer>.btn-link:focus,
.modal-footer>.btn-link:hover {
    background-color: rgba(255, 255, 255, .08);
    box-shadow: none
}

@media (min-width:576px) {
    .modal {
        text-align: center
    }

    .modal:before {
        content: '';
        height: 100%;
        width: 1px
    }

    .modal .modal-dialog,
    .modal:before {
        display: inline-block;
        vertical-align: middle
    }

    .modal-xl {
        max-width: 1200px
    }
}

.popover {
    font-size: 1rem;
    box-shadow: 0 2px 30px rgba(0, 0, 0, .1)
}

.header,
.header--scrolled,
.search--toggled {
    box-shadow: 0 0 13px rgba(0, 0, 0, .22)
}

.nav-tabs .nav-link {
    border: 0;
    transition: color .3s;
    position: relative;
    font-size: 1.1rem
}

.nav-tabs .nav-link:before {
    content: "";
    height: 2px;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    background-color: #fff;
    -webkit-transform: scale(0);
    transform: scale(0);
    transition: all .3s
}

.content__title,
.main {
    position: relative
}

.nav-tabs .nav-link.active:before {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.tab-content {
    padding: 1.5rem 0
}

.nav-link:not(.disabled):hover {
    color: #fff
}

.nav-link.disabled {
    cursor: default
}

@font-face {
    font-family: Nunito;
    src: url(../fonts/nunito/nunito-regular.eot);
    src: url(../fonts/nunito/nunito-regular?#iefix) format("embedded-opentype"), url(../fonts/nunito/nunito-regular.woff2) format("woff2"), url(../fonts/nunito/nunito-regular.woff) format("woff"), url("../fonts/nunito/nunito-regular.svg#Nunito Regular") format("svg");
    font-weight: 400;
    font-style: normal;
    font-stretch: normal
}

@font-face {
    font-family: Nunito;
    src: url(../fonts/nunito/nunito-semibold.eot);
    src: url(../fonts/nunito/nunito-semibold.eot?#iefix) format("embedded-opentype"), url(../fonts/nunito/nunito-semibold.woff2) format("woff2"), url(../fonts/nunito/nunito-semibold.woff) format("woff"), url("../fonts/nunito/nunito-semibold.svg#Nunito SemiBold") format("svg");
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    unicode-range: U+0020-2212
}

@font-face {
    font-family: Nunito;
    src: url(../fonts/nunito/nunito-bold.eot);
    src: url(../fonts/nunito/nunito-bold.eot?#iefix) format("embedded-opentype"), url(../fonts/nunito/nunito-bold.woff2) format("woff2"), url(../fonts/nunito/nunito-bold.woff) format("woff"), url("../fonts/nunito/nunito-bold.svg#Nunito Bold") format("svg");
    font-weight: 700;
    font-style: normal;
    font-stretch: normal;
    unicode-range: U+0020-2212
}

.bg-blue {
    background-color: #007bff !important
}

.text-blue {
    color: #007bff !important
}

.bg-indigo {
    background-color: #6610f2 !important
}

.text-indigo {
    color: #6610f2 !important
}

.bg-purple {
    background-color: #6f42c1 !important
}

.text-purple {
    color: #6f42c1 !important
}

.bg-pink {
    background-color: #e83e8c !important
}

.text-pink {
    color: #e83e8c !important
}

.bg-red {
    background-color: #dc3545 !important
}

.text-red {
    color: #dc3545 !important
}

.bg-orange {
    background-color: #fd7e14 !important
}

.text-orange {
    color: #fd7e14 !important
}

.bg-yellow {
    background-color: #ffc107 !important
}

.text-yellow {
    color: #ffc107 !important
}

.bg-green {
    background-color: #28a745 !important
}

.text-green {
    color: #28a745 !important
}

.bg-teal {
    background-color: #20c997 !important
}

.text-teal {
    color: #20c997 !important
}

.bg-cyan {
    background-color: #17a2b8 !important
}

.text-cyan {
    color: #17a2b8 !important
}

.bg-white {
    background-color: #fff !important
}

.text-white {
    color: #fff !important
}

.bg-black {
    background-color: #000 !important
}

.text-black {
    color: #000 !important
}

@media (max-width:576px) {

    .hidden-lg-down,
    .hidden-md-down,
    .hidden-sm-down,
    .hidden-unless-lg,
    .hidden-unless-md,
    .hidden-unless-sm,
    .hidden-unless-xl,
    .hidden-xl-down,
    .hidden-xs-down,
    .hidden-xs-up {
        display: none !important
    }
}

@media (min-width:576px) and (max-width:767px) {

    .hidden-lg-down,
    .hidden-md-down,
    .hidden-sm-down,
    .hidden-sm-up,
    .hidden-unless-lg,
    .hidden-unless-md,
    .hidden-unless-xl,
    .hidden-unless-xs,
    .hidden-xl-down,
    .hidden-xs-up {
        display: none !important
    }
}

@media (min-width:768px) and (max-width:991px) {

    .hidden-lg-down,
    .hidden-md-down,
    .hidden-md-up,
    .hidden-sm-up,
    .hidden-unless-lg,
    .hidden-unless-sm,
    .hidden-unless-xl,
    .hidden-unless-xs,
    .hidden-xl-down,
    .hidden-xs-up {
        display: none !important
    }
}

@media (min-width:992px) and (max-width:1199px) {

    .hidden-lg-down,
    .hidden-lg-up,
    .hidden-md-up,
    .hidden-sm-up,
    .hidden-unless-md,
    .hidden-unless-sm,
    .hidden-unless-xl,
    .hidden-unless-xs,
    .hidden-xl-down,
    .hidden-xs-up {
        display: none !important
    }
}

::-moz-selection {
    background: rgba(255, 255, 255, .1)
}

::selection {
    background: rgba(255, 255, 255, .1)
}

* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

:active,
:focus {
    outline: 0 !important
}

html {
    font-size: 13px
}

a {
    cursor: pointer
}

pre {
    background-color: rgba(255, 255, 255, .8);
    border-radius: 2px;
    padding: 1.5rem
}

button,
input,
optgroup,
select,
textarea {
    font-family: Nunito, sans-serif
}

.list {
    padding-left: 0
}

.list>li:before {
    font-family: Material-Design-Iconic-Font;
    margin-right: 1.1rem
}

.list--star>li:before {
    content: '\f27d'
}

.list--check>li:before {
    content: '\f26b'
}

.list--dot>li:before {
    content: '\f26f'
}

.main--alt {
    padding-top: 40px
}

@media (min-width:1200px) {

    .hidden-lg-up,
    .hidden-md-up,
    .hidden-sm-up,
    .hidden-unless-lg,
    .hidden-unless-md,
    .hidden-unless-sm,
    .hidden-unless-xs,
    .hidden-xl-down,
    .hidden-xl-up,
    .hidden-xs-up {
        display: none !important
    }

    .content:not(.content--boxed):not(.content--full) {
        padding: 102px 30px 0 280px
    }

    .logo {
        min-width: 250px
    }
}

@media (min-width:576px) and (max-width:1199px) {
    .content:not(.content--boxed):not(.content--full) {
        padding: 102px 30px 0
    }
}

@media (max-width:767px) {
    .content:not(.content--boxed):not(.content--full) {
        padding: 102px 15px 0
    }
}

@media (min-width:576px) {
    .content--full {
        padding: 102px 30px 0
    }
}

@media (max-width:767px) {
    .content--full {
        padding: 87px 15px 0
    }
}

.content__inner {
    margin: auto
}

.content__inner:not(.content__inner--sm) {
    width: 100%
}

.content__inner--sm {
    max-width: 800px
}

.content__title {
    margin-bottom: 2rem;
    padding: .75rem 2rem
}

.content__title>h1 {
    line-height: 140%;
    font-size: 1.15rem;
    margin: 0;
    color: #fff
}

.content__title .actions {
    position: absolute;
    top: .3rem;
    right: 1rem;
    z-index: 15
}

@media (max-width:991px) {
    .content__title .actions {
        display: none
    }
}

.content__title>small {
    font-size: 1rem;
    display: block;
    margin-top: .75rem;
    color: rgba(255, 255, 255, .8)
}

[data-columns]::after {
    display: block;
    clear: both;
    content: ""
}

@media (min-width:1500px) {
    [data-columns]:before {
        content: '3 .column.size-1of3'
    }
}

@media (min-width:768px) {
    [data-columns] {
        margin: 0 -15px
    }

    [data-columns] .column {
        padding: 0 15px
    }
}

@media (min-width:768px) and (max-width:1499px) {
    [data-columns]:before {
        content: '2 .column.size-1of2'
    }
}

@media screen and (max-width:767px) {
    [data-columns] {
        margin: 0 -10px
    }

    [data-columns] .column {
        padding: 0 10px
    }

    [data-columns]:before {
        content: '1 .column.size-1of1'
    }
}

.size-1of1 {
    width: 100%
}

.size-1of2 {
    width: 50%
}

.size-1of3 {
    width: 33.333%
}

.view-more {
    display: block;
    padding: 1.1rem 0;
    text-align: center;
    margin-top: .5rem;
    font-size: .9rem;
    font-weight: 600;
    transition: color .3s;
    color: rgba(255, 255, 255, .7)
}

.actions>*,
.actions__item>i,
.load-more>a,
.tags__body>a {
    display: inline-block
}

.view-more:hover {
    color: #fff
}

.actions__item,
.load-more>a {
    color: rgba(255, 255, 255, .85)
}

.load-more {
    text-align: center;
    margin-top: 2rem
}

.load-more>a {
    padding: .5rem 1rem;
    border: 2px solid rgba(0, 0, 0, .065);
    border-radius: 2px;
    transition: border-color .3s
}

.load-more>a>i {
    font-size: 1.2rem;
    margin: 0 .3rem 0 -.1rem;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s
}

.load-more>a:hover {
    border-color: rgba(0, 0, 0, .12)
}

.load-more>a:hover>i {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg)
}

.card-body .view-more {
    padding: 1rem 0 0
}

.actions__item {
    line-height: 35px;
    text-align: center;
    font-size: 1.55rem;
    cursor: pointer;
    transition: background-color .3s, color .3s;
    width: 35px;
    height: 35px;
    border-radius: 50%
}

.actions__item.show,
.actions__item:hover,
.avatar-char {
    color: #fff;
    background-color: rgba(255, 255, 255, .08)
}

.actions__item>i {
    width: 100%
}

.actions__item--active {
    background-color: rgba(255, 255, 255, .08)
}

.icon-list {
    padding: 0;
    margin: 0
}

.icon-list>li {
    position: relative;
    padding: .3rem 0
}

.icon-list>li address {
    display: inline-block;
    vertical-align: top
}

.icon-list>li>i {
    width: 2.5rem;
    text-align: center;
    font-size: 1.25rem;
    top: .12rem;
    position: relative;
    margin-left: -.5rem
}

.avatar-char,
.avatar-img {
    border-radius: 2px;
    width: 3rem;
    height: 3rem;
    margin-right: 1.2rem
}

.header,
.sa-backdrop {
    position: fixed;
    width: 100%;
    z-index: 100;
    top: 0;
    left: 0
}

.avatar-char {
    line-height: 2.9rem;
    font-size: 1.2rem;
    text-align: center;
    font-style: normal
}

.header,
.lightbox>a:after {
    background-color: rgba(0, 0, 0, .3)
}

.avatar-char>.zmdi {
    line-height: 3.1rem
}

.sa-backdrop {
    height: 100%;
    cursor: pointer
}

.tags__body>a {
    padding: .65rem 1rem;
    border: 2px solid rgba(255, 255, 255, .08);
    color: rgba(255, 255, 255, .85);
    margin: 0 .3rem .5rem 0;
    transition: border-color .3s
}

.header,
.navigation-trigger {
    display: -webkit-box;
    display: -ms-flexbox;
    -webkit-box-align: center
}

.tags__body>a:hover {
    border-color: rgba(255, 255, 255, .2)
}

.header {
    height: 72px;
    color: #fff;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding-right: 1.5rem;
    transition: background-color 1s, box-shadow 1s
}

.header::after {
    display: block;
    clear: both;
    content: ""
}

.header .sa-backdrop {
    position: absolute
}

.header--scrolled {
    background-color: rgba(0, 0, 0, .96)
}

.navigation-trigger {
    height: 100%;
    width: 50px;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    font-size: 1.65rem;
    transition: opacity .1s
}

.navigation-trigger:hover {
    cursor: pointer;
    opacity: .8
}

.logo {
    padding-left: 2.2rem;
    height: 72px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.logo>h1 {
    line-height: 100%;
    font-size: 1.3rem;
    font-weight: 400;
    margin: 0
}

.logo>h1>a {
    color: #fff
}

.top-nav {
    margin: 0 0 0 auto;
    padding: 0
}

.top-nav>li {
    display: inline-block;
    vertical-align: middle
}

.top-nav>li>a {
    display: block;
    color: #fff;
    border-radius: 2px;
    text-align: center;
    line-height: 100%;
    position: relative;
    transition: background-color .3s
}

.top-nav>li>a:not(.header__nav__text) {
    padding: .65rem .15rem;
    min-width: 50px
}

.top-nav>li>a:not(.header__nav__text)>.zmdi {
    font-size: 1.65rem;
    line-height: 100%
}

.top-nav>li>a.active,
.top-nav>li>a:hover {
    background-color: rgba(255, 255, 255, .2)
}

.top-nav>li .dropdown-menu--block {
    padding: 0
}

@media (max-width:575px) {
    .top-nav>li {
        position: static
    }

    .top-nav>li .dropdown-menu--block {
        left: 20px;
        width: calc(100% - 40px);
        top: 62px
    }
}

.top-nav__notifications .listview {
    position: relative
}

.top-nav__notifications .listview:before {
    font-family: Material-Design-Iconic-Font;
    content: "";
    font-size: 2.5rem;
    transition: opacity .3s, -webkit-transform .3s;
    transition: transform .3s, opacity .3s;
    transition: transform .3s, opacity .3s, -webkit-transform .3s;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 90px;
    height: 90px;
    border: 2px solid rgba(255, 255, 255, .1);
    color: #fff;
    border-radius: 50%;
    -webkit-transform: scale(0) rotate(-360deg);
    transform: scale(0) rotate(-360deg);
    opacity: 0;
    text-align: center;
    line-height: 86px
}

.top-nav__notifications .listview__scroll {
    height: 350px
}

.top-nav__notifications--cleared .listview:before {
    -webkit-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0);
    opacity: 1
}

.top-nav__notify:before {
    content: '';
    width: 5px;
    height: 5px;
    background-color: #fff;
    color: #fff;
    border-radius: 50%;
    position: absolute;
    top: -3px;
    right: 0;
    left: 0;
    margin: auto;
    -webkit-animation-name: flash;
    animation-name: flash;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

.search {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin-right: 2.5rem;
    position: relative
}

@media (max-width:1199px) {
    .search {
        padding: 0 1.5rem;
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background-color: #fff;
        z-index: 101;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        transition: box-shadow .3s, background-color .3s, -webkit-transform .3s;
        transition: transform .3s, box-shadow .3s, background-color .3s;
        transition: transform .3s, box-shadow .3s, background-color .3s, -webkit-transform .3s
    }

    .search:not(.search--toggled) {
        -webkit-transform: translate3d(0, -105%, 0);
        transform: translate3d(0, -105%, 0)
    }

    .search__inner {
        max-width: 600px;
        margin: 0 auto;
        width: 100%
    }
}

.search--toggled {
    background-color: rgba(0, 0, 0, .96)
}

.search__inner {
    position: relative
}

.search__text {
    border: 0;
    border-radius: 2px;
    height: 2.9rem;
    padding-left: 3rem;
    width: 100%;
    transition: background-color .3s, color .3s
}

@media (min-width:992px) {
    .search__text {
        background-color: rgba(255, 255, 255, .08);
        color: rgba(255, 255, 255, .85)
    }

    .search__text::-webkit-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text:-ms-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text::placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text:focus {
        background-color: rgba(0, 0, 0, .2);
        color: rgba(255, 255, 255, .85)
    }

    .search__text:focus::-webkit-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text:focus:-ms-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text:focus::placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search--focus .search__helper {
        color: rgba(255, 255, 255, .85)
    }
}

@media (max-width:1199px) {
    .search__text {
        background-color: rgba(255, 255, 255, .125);
        color: rgba(255, 255, 255, .85)
    }

    .search__text::-webkit-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text:-ms-input-placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__text::placeholder {
        color: rgba(255, 255, 255, .85)
    }

    .search__helper {
        color: rgba(255, 255, 255, .85);
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        line-height: 2.9rem
    }

    .search__helper:before {
        content: '\f301'
    }

    .search__helper:hover {
        opacity: .9
    }
}

.header--scrolled .search__text {
    background-color: rgba(255, 255, 255, .1)
}

.header--scrolled .search__text:focus {
    background-color: rgba(255, 255, 255, .12)
}

.search__helper {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 1.3rem;
    height: 100%;
    width: 3rem;
    text-align: center;
    line-height: 2.9rem;
    cursor: pointer;
    transition: color .3s, -webkit-transform .4s;
    transition: color .3s, transform .4s;
    transition: color .3s, transform .4s, -webkit-transform .4s
}

.search--focus .search__helper {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    line-height: 2.9rem
}

.search--focus .search__helper:before {
    content: '\f301'
}

.app-shortcuts {
    margin: 0;
    padding: 1rem
}

.app-shortcuts__item {
    text-align: center;
    padding: 1rem 0;
    border-radius: 2px;
    transition: background-color .3s
}

.app-shortcuts__item:hover {
    background-color: rgba(255, 255, 255, .06)
}

.app-shortcuts__item:hover>i {
    background-color: rgba(255, 255, 255, .25)
}

.app-shortcuts__item>i {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: #fff;
    line-height: 45px;
    font-size: 1.5rem;
    background-color: rgba(255, 255, 255, .1);
    transition: background-color .3s
}

.clock,
.user,
.user__info {
    border-radius: 2px
}

.app-shortcuts__item>small,
.app-shortcuts__item>small:focus,
.app-shortcuts__item>small:hover,
.top-menu>li>a {
    color: rgba(255, 255, 255, .85)
}

.app-shortcuts__item>small {
    display: block;
    margin-top: .5rem;
    font-size: .95rem
}

.top-menu {
    position: absolute;
    background-color: #fff;
    left: 0;
    top: 100%;
    width: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    white-space: nowrap
}

.top-menu>li.active {
    position: relative;
    box-shadow: 0 0 0 -2px red
}

.top-menu>li>a {
    line-height: 100%;
    font-weight: 600;
    text-transform: uppercase
}

.top-menu>li>a.active {
    color: #fff
}

.clock {
    background: rgba(255, 255, 255, .08);
    line-height: 100%;
    padding: .7rem 1rem;
    font-size: 1.5rem;
    margin-left: 1rem
}

.time>span:not(:last-child):after {
    content: ':';
    width: 10px;
    text-align: center;
    display: inline-block;
    position: relative;
    top: -1px;
    right: -2px
}

.footer__nav .nav-link+.nav-link:before,
.navigation__sub .navigation__active:before {
    font-family: Material-Design-Iconic-Font;
    content: ""
}

.footer {
    text-align: center;
    padding: 4rem 1rem 1rem
}

.footer>p {
    color: rgba(255, 255, 255, .85);
    margin-bottom: 0
}

.footer__nav {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: -5px
}

.footer__nav .nav-link {
    color: rgba(255, 255, 255, .7);
    transition: color .3s
}

.footer__nav .nav-link:focus,
.footer__nav .nav-link:hover {
    color: #fff
}

.navigation li:not(.navigation__active):not(.navigation__sub--active) a,
.user__email {
    color: rgba(255, 255, 255, .7)
}

.footer__nav .nav-link+.nav-link:before {
    font-size: 4px;
    position: relative;
    left: -1.2rem;
    top: -.2rem;
    color: rgba(255, 255, 255, .7)
}

.sidebar {
    width: 250px;
    position: fixed;
    left: 0;
    height: 100%;
    z-index: 101;
    background-color: rgba(0, 0, 0, .125);
    padding: 15px
}

.navigation>li>a,
.navigation__sub .navigation__active,
.user {
    position: relative
}

@media (min-width:1200px) {
    .sidebar:not(.sidebar--hidden) {
        top: 72px;
        height: calc(100% - 72px)
    }
}

@media (max-width:1199px) {
    .sidebar {
        background-color: rgba(0, 0, 0, .96);
        transition: opacity .3s, -webkit-transform .3s;
        transition: transform .3s, opacity .3s;
        transition: transform .3s, opacity .3s, -webkit-transform .3s
    }

    .sidebar:not(.toggled) {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }

    .sidebar.toggled {
        box-shadow: 5px 0 10px rgba(0, 0, 0, .25);
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    .sidebar .user__info {
        background-color: rgba(255, 255, 255, .09)
    }

    .sidebar .user__info:hover {
        background-color: rgba(255, 255, 255, .1)
    }
}

.sidebar .scrollbar-inner>.scroll-element {
    margin-right: 0
}

.sidebar--hidden {
    background-color: rgba(0, 0, 0, .96);
    transition: opacity .3s, -webkit-transform .3s;
    transition: transform .3s, opacity .3s;
    transition: transform .3s, opacity .3s, -webkit-transform .3s;
    top: 0;
    height: 100%
}

.sidebar--hidden:not(.toggled) {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0)
}

.sidebar--hidden.toggled {
    box-shadow: 5px 0 10px rgba(0, 0, 0, .25);
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.sidebar--hidden .user__info {
    background-color: rgba(255, 255, 255, .09)
}

.sidebar--hidden .user__info:hover {
    background-color: rgba(255, 255, 255, .1)
}

.sidebar__inner {
    padding: 30px 2rem .5rem
}

.user {
    margin-bottom: 15px
}

.user .dropdown-menu {
    width: 100%;
    -webkit-transform: none !important;
    transform: none !important
}

.user__info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    font-size: .9rem;
    padding: 1rem;
    background-color: rgba(255, 255, 255, .06);
    transition: background-color .3s
}

.user__info:hover {
    background-color: rgba(255, 255, 255, .1)
}

.user__img {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    margin-right: .8rem
}

.user__name {
    font-weight: 600;
    margin-top: .2rem
}

.navigation {
    padding: 0
}

.navigation li a {
    transition: background-color .3s, color .3s;
    font-weight: 600;
    display: block
}

.navigation li:not(.navigation__active):not(.navigation__sub--active) a:hover {
    background-color: rgba(255, 255, 255, .04);
    color: #fff
}

.navigation>li>a {
    padding: .85rem 1rem;
    border-radius: 2px
}

.navigation>li>a>i {
    vertical-align: top;
    font-size: 1.3rem;
    position: relative;
    top: .1rem;
    width: 1.5rem;
    text-align: center;
    margin-right: .6rem
}

.navigation>.navigation__active,
.navigation>.navigation__sub--active {
    margin: 2px 0
}

.navigation>.navigation__active>a,
.navigation>.navigation__sub--active>a {
    background-color: rgba(255, 255, 255, .06);
    color: #fff
}

.navigation__sub>a {
    margin-bottom: 1px
}

.navigation__sub>ul {
    border-radius: 2px;
    overflow: hidden;
    padding: 0;
    margin: 0
}

.navigation__sub>ul>li>a {
    padding: .6rem 1rem .6rem 3.3rem
}

.navigation__sub>ul>li:last-child {
    padding-bottom: .8rem
}

.navigation__sub:not(.navigation__sub--active)>ul {
    display: none
}

.navigation__sub .navigation__active:before {
    font-size: 6px;
    position: absolute;
    left: 1.5rem;
    top: 1.1rem
}

.navigation__sub .navigation__active>a {
    color: #fff
}

.toggle-switch {
    display: inline-block;
    width: 36px;
    height: 20px;
    position: relative
}

.toggle-switch__helper {
    position: absolute;
    height: 12px;
    width: 100%
}

.toggle-switch__helper:after,
.toggle-switch__helper:before {
    will-change: left, background-color;
    content: '';
    position: absolute;
    left: 0;
    transition: left .2s, background-color, .2s
}

.messages__search .form-group:before,
.results__search:before {
    content: "";
    font-family: Material-Design-Iconic-Font
}

.toggle-switch__helper:before {
    background-color: rgba(0, 0, 0, .4);
    top: 4px;
    height: 100%;
    width: 100%;
    border-radius: 10px
}

.toggle-switch__helper:after {
    width: 19px;
    height: 19px;
    border-radius: 50%;
    background-color: #fff;
    left: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .2);
    z-index: 1
}

.toggle-switch__checkbox {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 2;
    cursor: pointer
}

.toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    left: calc(100% - 19px);
    background-color: #dc3545
}

.toggle-switch__checkbox:disabled {
    cursor: auto
}

.toggle-switch__checkbox:disabled~.toggle-switch__helper {
    opacity: .65
}

.toggle-switch__checkbox:active~.toggle-switch__helper:after {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, .05)
}

.toggle-switch--amber .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #ffc107
}

.toggle-switch--blue .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #007bff
}

.toggle-switch--green .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #28a745
}

.toggle-switch--teal .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #20c997
}

.toggle-switch--purple .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #6f42c1
}

.toggle-switch--cyan .toggle-switch__checkbox:checked~.toggle-switch__helper:after {
    background-color: #17a2b8
}

.listview__header {
    color: #fff;
    padding: 1.2rem 1rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, .04);
    text-align: center
}

.listview__header .actions {
    position: absolute;
    top: .8rem;
    right: 1rem
}

.listview__scroll {
    overflow-y: auto
}

.listview__item {
    padding: 15px 2.2rem;
    transition: background-color .3s, border-color .3s
}

.listview:not(.listview--block) .listview__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

@media (min-width:768px) {
    .listview:not(.listview--block) .listview__item {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }
}

.listview__img {
    height: 3rem;
    border-radius: 2px;
    vertical-align: top;
    margin: -.1rem 1.2rem 0 0
}

.listview__content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0
}

.listview__content>p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(255, 255, 255, .7);
    margin-bottom: 0
}

.listview__heading {
    font-size: 1rem;
    color: #fff;
    position: relative
}

.listview__heading:hover {
    color: #fff
}

.listview__heading>small,
.toolbar__nav>a {
    color: rgba(255, 255, 255, .7);
    font-weight: 600
}

.listview__heading>small {
    float: right;
    font-size: .85rem;
    margin-top: .1rem
}

.listview__heading+p {
    margin: .2rem 0 0;
    font-size: .95rem
}

.listview__attrs {
    -webkit-box-flex: 1;
    -ms-flex: 1 100%;
    flex: 1 100%;
    margin-top: .7rem
}

.listview__attrs>span {
    padding: .55rem .7rem .6rem;
    border: 1px solid rgba(255, 255, 255, .125);
    display: inline-block;
    line-height: 100%;
    font-size: .9rem;
    margin: .2rem .05rem .055rem 0
}

.listview--hover .listview__item:hover {
    border-color: transparent !important;
    background-color: rgba(255, 255, 255, .06)
}

.listview--striped .listview__item:nth-child(even) {
    background-color: rgba(255, 255, 255, .03)
}

.listview__item--active {
    background-color: rgba(0, 0, 0, .035)
}

.listview--bordered .listview__item+.listview__item {
    border-top: 1px solid rgba(255, 255, 255, .05)
}

.listview__actions {
    margin-left: auto;
    -ms-flex-item-align: start;
    align-self: flex-start;
    margin-right: -1rem
}

.toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    height: 4.5rem;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: .05rem 2.2rem 0;
    position: relative
}

.toolbar:not(.toolbar--inner) {
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px;
    margin-bottom: 30px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1)
}

.toolbar .actions {
    margin: .05rem -.8rem 0 auto
}

.toolbar--inner {
    margin-bottom: 1rem;
    border-radius: 2px 2px 0 0;
    background-color: rgba(0, 0, 0, .1)
}

.toolbar__nav {
    white-space: nowrap;
    overflow-x: auto
}

.toolbar__nav>a {
    display: inline-block;
    transition: color .3s
}

.toolbar__nav>a+a {
    padding-left: 1rem
}

.toolbar__nav>a.active,
.toolbar__nav>a:hover {
    color: rgba(255, 255, 255, .85)
}

.toolbar__search {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 2px;
    padding-left: 3rem;
    display: none;
    background-color: rgba(0, 0, 0, .96)
}

.toolbar__search input[type=text] {
    width: 100%;
    height: 100%;
    border: 0;
    padding: 0 1.6rem;
    font-size: 1.2rem;
    background-color: transparent;
    color: rgba(255, 255, 255, .85)
}

.toolbar__search input[type=text]::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.toolbar__search input[type=text]:-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.toolbar__search input[type=text]::-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.toolbar__search input[type=text]:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.toolbar__search__close,
.toolbar__search__close:hover {
    color: rgba(255, 255, 255, .85)
}

.toolbar__search__close {
    transition: color .3s;
    cursor: pointer;
    position: absolute;
    top: 1.5rem;
    left: 1.8rem;
    font-size: 1.5rem
}

.toolbar__label {
    margin: 0;
    font-size: 1.1rem
}

.page-loader {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, .96);
    z-index: 999999999999999999;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.page-loader__spinner {
    position: relative;
    width: 50px;
    height: 50px
}

.page-loader__spinner svg {
    -webkit-animation: rotate 2s linear infinite;
    animation: rotate 2s linear infinite;
    -webkit-transform-origin: center center;
    transform-origin: center center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0
}

.page-loader__spinner svg circle {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    -webkit-animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
    animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
    stroke-linecap: round
}

.login__block,
.select2-dropdown {
    -webkit-animation-duration: .3s;
    -webkit-animation-fill-mode: both
}

@-webkit-keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px
    }

    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px
    }

    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px
    }
}

@-webkit-keyframes color {

    0%,
    100% {
        stroke: #dc3545
    }

    40% {
        stroke: #007bff
    }

    66% {
        stroke: #28a745
    }

    80%,
    90% {
        stroke: #ffc107
    }
}

@keyframes color {

    0%,
    100% {
        stroke: #dc3545
    }

    40% {
        stroke: #007bff
    }

    66% {
        stroke: #28a745
    }

    80%,
    90% {
        stroke: #ffc107
    }
}

@media (min-width:768px) {
    .profile {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }
}

.profile__img {
    padding: 5px;
    position: relative
}

.profile__img img {
    max-width: 200px;
    border-radius: 2px
}

@media (max-width:767px) {
    .profile {
        margin-top: 75px;
        text-align: center
    }

    .profile__img img {
        margin: -55px 0 -10px;
        width: 120px;
        border: 5px solid #fff;
        border-radius: 50%
    }
}

.profile__img__edit {
    position: absolute;
    font-size: 1.2rem;
    top: 15px;
    left: 15px;
    background-color: rgba(0, 0, 0, .4);
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #fff
}

.profile__img__edit:hover {
    background-color: rgba(0, 0, 0, .65);
    color: #fff
}

.profile__info {
    padding: 30px
}

.photos {
    margin: 0 -4px
}

.photos>a {
    padding: 0;
    border: 4px solid transparent
}

.photos>a img {
    border-radius: 2px;
    width: 100%
}

@media (max-width:575px) {
    .contacts {
        margin: 0 -5px
    }

    .contacts>[class*=col-] {
        padding: 0 5px
    }
}

.contacts__item {
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1);
    padding: 1.8rem 1.5rem 1.15rem;
    text-align: center;
    margin-bottom: 30px
}

.contacts__item:hover .contacts__img>img {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

@media (max-width:575px) {
    .contacts__item {
        margin-bottom: 10px
    }
}

.contacts__img {
    display: block;
    margin-bottom: 1.1rem
}

.contacts__img>img {
    max-width: 120px;
    max-height: 120px;
    width: 100%;
    border-radius: 50%;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s
}

.contacts__info small,
.contacts__info strong {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block
}

.contacts__info strong {
    font-weight: 600
}

.contacts__info small {
    font-size: .9rem;
    color: rgba(255, 255, 255, .7)
}

.contacts__btn {
    margin-top: 10px;
    font-weight: 600;
    border: 0;
    line-height: 100%;
    background-color: transparent;
    color: #fff;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 2px;
    transition: background-color .3s, color .3s
}

.contacts__btn:hover {
    background-color: rgba(255, 255, 255, .08)
}

.new-contact__header {
    background-color: rgba(0, 0, 0, .1);
    text-align: center;
    padding: 30px 0;
    border-radius: 2px 2px 0 0
}

.new-contact__img {
    border-radius: 50%;
    box-shadow: 0 0 0 5px rgba(255, 255, 255, .08);
    width: 150px;
    height: 150px
}

@media (max-width:767px) {
    .new-contact__img {
        width: 100px;
        height: 100px
    }
}

.new-contact__upload {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    background-color: rgba(255, 255, 255, .08);
    color: #fff;
    transition: background-color .3s
}

.new-contact__upload:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, .2)
}

.groups__item,
.messages {
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1)
}

@media (max-width:575px) {
    .groups {
        margin: 0 -5px
    }

    .groups [class*=col-] {
        padding: 0 5px
    }

    .groups .groups__item {
        margin-bottom: 10px
    }
}

.groups__item {
    position: relative;
    text-align: center;
    padding: 2rem 1rem 1.5rem;
    margin-bottom: 30px
}

.groups__item:hover .actions {
    opacity: 1
}

.groups__item .actions {
    position: absolute;
    top: .7rem;
    right: .5rem;
    z-index: 1;
    opacity: 0
}

.groups__img {
    width: 6.5rem;
    display: inline-block
}

.groups__img .avatar-img {
    display: inline-block;
    margin: 0 1px 4px 0;
    vertical-align: top
}

.groups__info {
    margin-top: 1rem
}

.groups__info>strong {
    color: #fff;
    display: block;
    font-weight: 600
}

.messages,
.messages__body {
    display: -webkit-box;
    display: -ms-flexbox;
    -webkit-box-direction: normal
}

.groups__info>small {
    font-size: .9rem;
    color: rgba(255, 255, 255, .7)
}

.messages {
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    height: calc(100vh - 180px)
}

.messages__sidebar {
    width: 23rem;
    overflow: hidden
}

@media (min-width:768px) {
    .messages__sidebar {
        border-right: 1px solid rgba(255, 255, 255, .04)
    }
}

@media (max-width:991px) {
    .messages__sidebar {
        display: none
    }
}

.messages__sidebar .listview {
    height: calc(100% - 130px);
    overflow-y: auto
}

.messages__search {
    padding: 0 2.2rem;
    position: relative
}

.messages__search .form-group:before {
    font-size: 1.3rem;
    position: absolute;
    left: 0;
    bottom: .35rem
}

.messages__search .form-control {
    padding-left: 2rem
}

.messages__body {
    -webkit-box-flex: 2;
    -ms-flex: 2;
    flex: 2;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column;
    display: flex
}

.messages__header,
.messages__reply {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto
}

.messages__content {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 2.5rem;
    overflow: hidden;
    height: 100%
}

.messages__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 2rem
}

.messages__item:not(.messages__item--right) {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.messages__item:not(.messages__item--right)>.messages__details {
    margin-left: -.5rem
}

.messages__item:not(.messages__item--right)>.messages__details>p {
    background-color: rgba(255, 255, 255, .08)
}

.messages__details {
    max-width: 500px
}

.messages__details>p {
    border-radius: 2px;
    padding: 1rem 1.3rem;
    margin-bottom: 0;
    display: inline-block;
    text-align: left
}

.messages__details>p+p {
    margin-top: 2px
}

.messages__details>small {
    display: block;
    padding: 0 1.5rem;
    margin-top: 1rem;
    color: rgba(255, 255, 255, .7);
    font-size: .9rem
}

.messages__details>small>.zmdi {
    font-size: 1.2rem;
    vertical-align: top;
    margin-right: .3rem;
    margin-top: .15rem
}

.messages__item--right {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
}

.error,
.widget-pie {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal
}

.messages__item--right .messages__details {
    text-align: right
}

.invoice__attrs__item,
.invoice__footer,
.invoice__header,
.login__block,
.notes__actions,
.price-table {
    text-align: center
}

.messages__item--right .messages__details>p {
    background-color: rgba(255, 255, 255, .75);
    color: #131313;
    margin-left: auto
}

.messages__reply {
    border-top: 1px solid rgba(255, 255, 255, .04);
    position: relative
}

.messages__reply__text {
    height: 50px;
    width: 100%;
    margin-bottom: -5px;
    border: 0;
    border-radius: 2px;
    padding: 1rem 1.5rem;
    resize: none;
    background-color: transparent;
    color: rgba(255, 255, 255, .85)
}

.messages__reply__text::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.messages__reply__text:-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.messages__reply__text::-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.messages__reply__text:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.price-table:not(.price-table--highlight) {
    margin: 0 -10px
}

.price-table:not(.price-table--highlight)>[class*=col-] {
    padding: 0 10px;
    text-align: center
}

.price-table--highlight {
    margin: 0
}

.price-table--highlight>[class*=col-] {
    padding: 0
}

.price-table__item {
    margin-bottom: 20px;
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1)
}

@media (max-width:767px) {
    .price-table__item {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto
    }
}

@media (min-width:768px) {
    .price-table__item--popular {
        padding-bottom: 1rem;
        position: relative;
        z-index: 1;
        margin: -1rem 0 0;
        box-shadow: 0 -5px 30px rgba(0, 0, 0, .4)
    }

    .price-table__item--popular .price-table__header {
        padding: 2.5rem 2rem;
        background-color: rgba(0, 0, 0, .35)
    }
}

.price-table__header {
    color: #fff;
    border-radius: 2px 2px 0 0;
    padding: 2rem;
    margin-bottom: 2rem;
    background-color: rgba(0, 0, 0, .2)
}

.invoice,
.price-table__action {
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px
}

.price-table__title {
    font-weight: 600;
    font-size: 1.3rem
}

.price-table__desc {
    color: rgba(255, 255, 255, .75);
    margin: .3rem 0
}

.price-table__price {
    font-size: 1.8rem
}

.price-table__price>small {
    font-size: 1rem;
    position: relative;
    top: -.4rem
}

.price-table__info {
    padding: 1rem 0
}

.price-table__info>li {
    font-weight: 600;
    padding: 1rem 1.5rem
}

.price-table__info>li+li {
    border-top: 1px solid rgba(255, 255, 255, .04)
}

.price-table__action {
    display: inline-block;
    margin-bottom: 2.5rem;
    padding: .8rem 1.2rem;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 3px 5px rgba(0, 0, 0, .12);
    transition: opacity .3s
}

.invoice,
.login__block {
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1)
}

.notes__item>a,
.todo__labels>a {
    transition: background-color .3s
}

.price-table__action:focus,
.price-table__action:hover {
    opacity: .9;
    color: #fff
}

.invoice {
    min-width: 1100px;
    max-width: 1170px;
    padding: 2.5rem
}

.invoice__header {
    padding: 1.5rem;
    border-radius: 2px 2px 0 0;
    margin-bottom: 1.5rem
}

.invoice__address {
    margin-bottom: 4rem
}

.invoice__address h4 {
    font-weight: 400;
    margin-bottom: 1rem
}

.invoice__attrs {
    margin-bottom: 2.5rem
}

.invoice__attrs__item {
    padding: 1.5rem 2rem;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, .08)
}

.invoice__attrs__item small {
    margin-bottom: .2rem;
    display: block;
    font-size: 1rem
}

.invoice__attrs__item h3 {
    margin: 0;
    line-height: 100%;
    font-weight: 400
}

.invoice__table {
    margin-bottom: 4rem
}

.invoice__footer {
    margin: 4rem 0 1.5rem
}

.invoice__footer>a {
    color: rgba(255, 255, 255, .85)
}

.login__block__btn,
.login__block__btn:focus,
.login__block__btn:hover,
.todo__info>span {
    color: #fff
}

@media print {
    @page {
        margin: 0;
        size: auto
    }

    body {
        margin: 0 !important;
        padding: 0 !important
    }

    .actions,
    .btn--action,
    .chat,
    .content__title,
    .footer,
    .growl-animated,
    .header,
    .navigation,
    .notifications {
        display: none !important
    }

    .invoice {
        padding: 30px !important;
        -webkit-print-color-adjust: exact !important
    }
}

.error,
.login {
    display: -webkit-box
}

.login {
    min-height: 100vh;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-top: 1.2rem
}

.login__block {
    max-width: 330px;
    width: 100%;
    display: none;
    padding: 1rem;
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
    animation-duration: .3s;
    animation-fill-mode: both;
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px
}

@media (min-width:576px) {
    .login__block:hover .login__block__actions .dropdown {
        display: block
    }
}

.login__block.active {
    z-index: 10;
    display: inline-block
}

.login__block__header {
    padding: 1.25rem;
    position: relative;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, .15);
    margin-bottom: 2rem
}

.login__block__header>i,
.login__block__header>img {
    display: block;
    margin-bottom: .6rem
}

.login__block__header>i {
    font-size: 3rem
}

.login__block__header>img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, .1)
}

.login__block__actions {
    position: absolute;
    bottom: .8rem;
    right: .8rem
}

.login__block__actions .dropdown:not(.show) {
    display: none
}

.login__block__body {
    padding: 1rem
}

.login__block__btn {
    margin-top: .5rem;
    background-color: rgba(0, 0, 0, .15)
}

.login__block__btn:hover {
    background-color: rgba(0, 0, 0, .25)
}

.todo__item {
    padding-left: 4.5rem;
    display: block
}

.todo__item small {
    display: block;
    font-size: .95rem;
    margin-top: .2rem
}

.todo__item .custom-control-input:checked~.todo__info {
    text-decoration: line-through
}

.todo__info>small {
    color: rgba(255, 255, 255, .7)
}

.todo__labels>a {
    color: #fff;
    background-color: rgba(255, 255, 255, .08);
    border-radius: 2px;
    padding: .55rem .9rem;
    display: inline-block;
    margin: 0 .1rem .4rem
}

.todo__labels>a:hover {
    background-color: rgba(255, 255, 255, .1)
}

.notes__item {
    margin-bottom: 30px
}

.notes__item>a {
    height: 155px;
    background-color: rgba(0, 0, 0, .2);
    display: block;
    padding: 1.8rem 2rem;
    position: relative;
    color: rgba(255, 255, 255, .75)
}

.dropzone .dz-message,
.dropzone:before,
.notes__actions {
    transition: opacity .3s
}

.notes__item>a,
.notes__item>a:before {
    border-radius: 2px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .08)
}

.notes__item>a:before {
    content: '';
    position: absolute;
    width: calc(100% - 10px);
    bottom: -5px;
    left: 5px;
    z-index: -1;
    height: 5px;
    background-color: rgba(0, 0, 0, .4)
}

.notes__item:hover .notes__actions {
    opacity: 1
}

.notes__title {
    color: #fff;
    margin-bottom: 1.1rem;
    font-weight: 600;
    font-size: 1.1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.notes__actions {
    position: absolute;
    right: 2.2rem;
    bottom: 1rem;
    font-size: 1.1rem;
    width: 2.2rem;
    height: 2.2rem;
    background: rgba(0, 0, 0, .96);
    border-radius: 50%;
    line-height: 2.1rem;
    color: #fff;
    box-shadow: 0 0 4px rgba(0, 0, 0, .5);
    opacity: 0;
    cursor: pointer
}

.notes__actions:hover {
    background: rgba(0, 0, 0, .9)
}

.note-view .trumbowyg-box {
    border: 0;
    background-color: rgba(255, 255, 255, .045);
    margin-bottom: 0
}

.note-view__field {
    border-bottom: 1px solid rgba(255, 255, 255, .06)
}

.note-view__field input {
    border: 0;
    font-size: 1rem;
    padding: 1.7rem 2rem;
    height: auto
}

.note-view__field--color {
    padding: 1.2rem 2rem .8rem
}

.note-view__label {
    float: left;
    margin: .4rem 1.5rem 0 0
}

.results__header {
    padding: 2rem 2rem 0;
    border-radius: 2px 2px 0 0;
    margin-bottom: 2rem;
    background-color: rgba(0, 0, 0, .1)
}

.results__search {
    position: relative
}

.results__search input[type=text] {
    width: 100%;
    border: 0;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, .08);
    color: rgba(255, 255, 255, .85);
    padding: 0 1rem 0 3rem;
    height: 2.9rem;
    margin-bottom: 1rem;
    transition: background-color .3s
}

.results__search input[type=text]::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.results__search input[type=text]:-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.results__search input[type=text]::-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.results__search input[type=text]:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.results__search input[type=text]:focus {
    background-color: rgba(0, 0, 0, .2)
}

.results__search:before {
    font-size: 1.3rem;
    position: absolute;
    top: .45rem;
    left: 1.1rem;
    z-index: 1
}

.ie-warning,
.q-a__vote,
.themes__item:before {
    left: 0;
    text-align: center
}

.results__nav {
    border: 0
}

.issue-tracker .listview__item {
    position: relative
}

.issue-tracker__item:not(.actions) {
    margin-left: 2rem
}

@media (max-width:767px) {
    .issue-tracker .listview__item {
        display: block
    }

    .issue-tracker__item:not(.actions) {
        margin: 1rem 0 .25rem
    }
}

.issue-tracker__item>.zmdi {
    font-size: 1.4rem;
    vertical-align: top;
    position: relative;
    top: .05rem;
    margin-right: .5rem
}

.issue-tracker__item.actions {
    margin-left: 1.25rem
}

.issue-tracker__tag {
    padding: .3rem .75rem .4rem;
    line-height: 100%;
    font-size: .95rem;
    border-radius: 2px
}

.blog__arthur-social>a,
.team__social>a {
    line-height: 35px;
    transition: background-color .3s;
    display: inline-block;
    color: #fff
}

.team {
    margin-top: 7rem
}

.team__item {
    text-align: center;
    margin-bottom: 7rem
}

@media (max-width:767px) {
    .issue-tracker__item.actions {
        position: absolute;
        bottom: 1rem;
        right: .75rem
    }

    .team__item {
        max-width: 365px;
        margin: 0 auto 80px
    }
}

.team__item .card-subtitle {
    margin-bottom: 1rem
}

.team__img {
    display: inline-block;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    border: 5px solid rgba(255, 255, 255, .08);
    margin: -4rem auto -.5rem
}

.team__social {
    margin-top: 2rem
}

.team__social>a {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, .08);
    font-size: 1.2rem;
    margin: 0 1px
}

.team__social>a:hover {
    background-color: rgba(255, 255, 255, .2)
}

.blog__tags {
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, .04);
    border-bottom: 1px solid rgba(255, 255, 255, .04);
    padding: 2rem 1rem 1.5rem;
    margin: 2rem 0 .5rem
}

.blog__arthur {
    padding: 2rem 2rem 2.5rem;
    text-align: center
}

.blog__arthur-img {
    margin-bottom: 1.5rem
}

.blog__arthur-img>img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, .08)
}

.blog__arthur-social {
    margin: 2rem 0 0
}

.blog__arthur-social>a {
    width: 35px;
    height: 35px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, .08);
    font-size: 1.2rem;
    margin: 0 1px
}

.blog__arthur-social>a:hover {
    background-color: rgba(255, 255, 255, .2)
}

.q-a__stat>span,
.q-a__vote__votes {
    background-color: rgba(255, 255, 255, .08);
    border-radius: 2px
}

.q-a__item {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.q-a__stat {
    margin: .35rem 2rem 0 0;
    -ms-flex-item-align: start;
    align-self: flex-start
}

.q-a__stat>span {
    display: inline-block;
    width: 70px;
    text-align: center;
    padding: .9rem .5rem .65rem;
    margin-right: .2rem
}

.q-a__stat>span>strong {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 100%;
    margin-bottom: .25rem
}

.q-a__stat>span>small {
    line-height: 100%
}

.q-a__question {
    position: relative;
    margin-bottom: 3rem
}

@media (min-width:768px) {
    .q-a__question {
        margin-top: 1rem
    }
}

@media (min-width:576px) {
    .q-a__question {
        padding-left: 100px
    }

    [data-sa-theme] {
        background-size: 100% 100%;
        background-attachment: fixed;
        background-position: center
    }
}

.q-a__question>h2 {
    font-size: 1.3rem
}

.q-a__question>h2+p {
    margin-top: 1rem
}

.q-a__question>p {
    font-size: 1.1rem
}

.q-a__vote {
    position: absolute;
    top: 0
}

.q-a__vote>i {
    font-size: 1.25rem;
    cursor: pointer
}

.q-a__vote__votes {
    padding: .5rem 0 .6rem;
    font-weight: 600;
    width: 75px;
    font-size: 1.2rem;
    margin-bottom: .35rem
}

.q-a__info {
    margin-top: 1.5rem;
    padding: 1.25rem 0;
    border-top: 1px solid rgba(255, 255, 255, .04);
    position: relative
}

.q-a__info .actions {
    position: absolute;
    top: 14px;
    right: 0
}

.q-a__op>a>img {
    width: 30px;
    height: 30px;
    border-radius: 2px;
    vertical-align: middle;
    margin-right: .5rem
}

.q-a__answers {
    margin-top: 3rem
}

[data-sa-theme] {
    transition: background .3s
}

[data-sa-theme="1"] {
    background-color: #772036
}

[data-sa-theme="2"] {
    background-color: #273C5B
}

[data-sa-theme="3"] {
    background-color: #174042
}

[data-sa-theme="4"] {
    background-color: #383844
}

[data-sa-theme="5"] {
    background-color: #49423F
}

[data-sa-theme="6"] {
    background-color: #5e3d22
}

[data-sa-theme="7"] {
    background-color: #234d6d
}

[data-sa-theme="8"] {
    background-color: #3b5e5e
}

[data-sa-theme="9"] {
    background-color: #0a4c3e
}

[data-sa-theme="10"] {
    background-color: #202641
}

@media (min-width:576px) {
    [data-sa-theme="1"] {
        background-image: url(../img/bg/1.jpg)
    }

    [data-sa-theme="2"] {
        background-image: url(../img/bg/2.jpg)
    }

    [data-sa-theme="3"] {
        background-image: url(../img/bg/3.jpg)
    }

    [data-sa-theme="4"] {
        background-image: url(../img/bg/4.jpg)
    }

    [data-sa-theme="5"] {
        background-image: url(../img/bg/5.jpg)
    }

    [data-sa-theme="6"] {
        background-image: url(../img/bg/6.jpg)
    }

    [data-sa-theme="7"] {
        background-image: url(../img/bg/7.jpg)
    }

    [data-sa-theme="8"] {
        background-image: url(../img/bg/8.jpg)
    }

    [data-sa-theme="9"] {
        background-image: url(../img/bg/9.jpg)
    }

    [data-sa-theme="10"] {
        background-image: url(../img/bg/10.jpg)
    }
}

.themes {
    position: fixed;
    top: 0;
    right: 0;
    height: 100%;
    width: 250px;
    background-color: rgba(0, 0, 0, .96);
    z-index: 101;
    -webkit-transform: translate3d(260px, 0, 0);
    transform: translate3d(260px, 0, 0);
    transition: box-shadow .3s, opacity .3s, -webkit-transform .3s;
    transition: box-shadow .3s, transform .3s, opacity .3s;
    transition: box-shadow .3s, transform .3s, opacity .3s, -webkit-transform .3s
}

.themes.toggled {
    box-shadow: -5px 0 10px rgba(0, 0, 0, .25);
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.themes__item {
    display: block;
    padding: 1.5rem;
    position: relative;
    transition: background-color .3s, border-color .3s
}

.themes__item:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, .02)
}

.themes__item>img {
    width: 100%;
    height: 100px
}

.themes__item.active,
.themes__item:hover {
    background-color: rgba(255, 255, 255, .02);
    border-bottom-color: transparent
}

.themes__item:before {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, .125);
    top: 0;
    bottom: 0;
    margin: auto;
    right: 0;
    position: absolute;
    color: #fff;
    line-height: 50px;
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
    font-family: Material-Design-Iconic-Font;
    content: "";
    font-size: 2rem;
    transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s;
    transition: opacity .3s, transform .3s, -webkit-transform .3s
}

.quick-stats__item::after,
.widget-pictures__body::after,
.widget-pie::after,
.widget-ratings__item::after,
.widget-visitors__stats::after {
    content: ""
}

.themes__item.active:before {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.ie-warning {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    z-index: 1000000;
    padding: 3rem;
    overflow: auto
}

.data-table-toggled,
.dataTables_filter,
.fc-event .fc-title,
.fc-view,
.fc-view>table,
.stats__chart,
.waves-effect,
.widget-past-days,
.widget-ratings__progress {
    overflow: hidden
}

.ie-warning>h1 {
    font-size: 2rem
}

.ie-warning p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, .7)
}

.ie-warning__downloads {
    background-color: #00f;
    padding: 30px 0;
    margin: 30px 0
}

.ie-warning__downloads>a {
    padding: 0 10px
}

.error {
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100vh;
    width: 100%
}

.error__inner {
    max-width: 600px;
    width: 100%;
    padding: 3rem;
    text-align: center;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, .2)
}

.error__inner>h1 {
    font-size: 8rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 0, 0, .03);
    line-height: 100%;
    margin-bottom: 1.5rem
}

.error__inner>h2 {
    color: rgba(255, 255, 255, .85);
    font-weight: 400;
    margin: 1.3rem 0;
    font-size: 1.5rem
}

.error__inner>p {
    color: rgba(255, 255, 255, .7);
    font-size: 1.1rem
}

.widget-past-days .flot-chart {
    margin: 0 -10px
}

.widget-past-days__chart {
    opacity: .75;
    margin: .55rem 0 0 auto
}

.widget-past-days__info small {
    font-size: 1rem;
    color: rgba(255, 255, 255, .9)
}

.widget-past-days__info h3 {
    margin: 0;
    color: #fff;
    font-weight: 400
}

.widget-visitors__stats {
    margin: 0 -.5rem 2rem
}

.widget-visitors__stats::after {
    display: block;
    clear: both
}

.widget-visitors__stats>div {
    padding: 1.5rem 1.5rem 1.45rem;
    float: left;
    margin: 0 .5rem;
    width: calc(50% - 1rem);
    background-color: rgba(255, 255, 255, .08);
    border-radius: 2px
}

.widget-visitors__stats>div>strong {
    font-size: 1.3rem;
    line-height: 100%;
    color: #fff;
    font-weight: 600
}

.widget-visitors__stats>div>small {
    display: block;
    color: rgba(255, 255, 255, .7);
    font-size: 1rem;
    line-height: 100%;
    margin-top: .45rem
}

.widget-visitors__map {
    width: 100%;
    height: 250px
}

.widget-visitors__country {
    height: .88rem;
    width: auto;
    vertical-align: top;
    position: relative;
    margin-right: .25rem;
    left: -.1rem;
    border-radius: 1px
}

.widget-pie {
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.widget-pie::after {
    display: block;
    clear: both
}

.widget-pie__item {
    padding: 20px 0;
    text-align: center
}

.widget-pie__item:nth-child(2n) {
    background-color: rgba(0, 0, 0, .1)
}

.quick-stats__item,
.stats__item {
    background-color: rgba(0, 0, 0, .2);
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1);
    border-radius: 2px
}

.widget-pie__title {
    color: rgba(255, 255, 255, .85)
}

.quick-stats__item {
    padding: 1.5rem 1.5rem 1.45rem;
    margin-bottom: 30px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.stats,
.stats__chart {
    padding-top: 1rem
}

.quick-stats__item::after {
    display: block;
    clear: both
}

.quick-stats__item>.peity {
    margin-left: auto;
    padding-left: 1.2rem
}

@media (min-width:576px) and (max-width:1199px) {
    .quick-stats__item>.peity {
        display: none
    }
}

.quick-stats__info {
    min-width: 0
}

.quick-stats__info>h2,
.quick-stats__info>small {
    line-height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.quick-stats__info>h2 {
    margin: 0;
    font-size: 1.3rem;
    color: #fff
}

.quick-stats__info>small {
    font-size: 1rem;
    display: block;
    color: rgba(255, 255, 255, .7);
    margin-top: .6rem
}

.stats__item {
    margin-bottom: 30px;
    padding: 1rem
}

#jqstooltip,
.flot-tooltip {
    box-shadow: 0 3px 5px rgba(0, 0, 0, .1)
}

.stats__chart {
    border-radius: 2px
}

.stats__chart .flot-chart {
    margin: 0 -12px -12px
}

.stats__info {
    padding: 1.8rem 1rem .5rem;
    position: relative;
    text-align: center
}

.stats__info h2 {
    font-size: 1.3rem;
    margin: 0
}

.stats__info small {
    display: block;
    font-size: 1rem;
    margin-top: .4rem;
    color: rgba(255, 255, 255, .7)
}

.widget-pictures__body {
    margin: 0;
    padding: 2px;
    text-align: center
}

.widget-pictures__body::after {
    display: block;
    clear: both
}

.widget-pictures__body>a {
    padding: 2px 2px 1px;
    display: block
}

.widget-pictures__body>a img {
    width: 100%;
    border-radius: 2px
}

.widget-pictures__body>a:hover {
    opacity: .9
}

.widget-ratings__star {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, .3);
    margin: -1.7rem 0 0
}

.widget-ratings__star .active {
    color: #ffc107
}

.widget-ratings__item {
    padding: .5rem 0;
    margin-bottom: .5rem
}

.widget-ratings__item::after {
    display: block;
    clear: both
}

.widget-ratings__item .float-left .zmdi {
    font-size: 1.5rem;
    vertical-align: top;
    color: #ffc107;
    position: relative;
    top: -.05rem;
    margin-left: .35rem
}

.widget-ratings__item:last-child {
    padding-bottom: 0
}

.widget-ratings__progress {
    padding: .65rem 1.5rem
}

.widget-profile__img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 1.2rem;
    border: 5px solid rgba(255, 255, 255, .1)
}

.widget-profile__list {
    color: #fff
}

.widget-profile__list .media {
    padding: 1rem 2rem
}

.widget-profile__list .media:nth-child(even) {
    background-color: rgba(255, 255, 255, .02)
}

.widget-profile__list .media-body strong {
    display: block;
    font-weight: 600
}

.widget-profile__list .media-body small {
    color: rgba(255, 255, 255, .7);
    font-size: .92rem
}

.widget-contacts__map {
    display: block;
    padding: 3px
}

.widget-contacts__map img {
    width: 100%;
    border-radius: 2px;
    margin: -20px 0 -1px
}

.widget-signups__list {
    text-align: center
}

.widget-signups__list>a {
    vertical-align: top;
    margin: 4px 2px;
    display: inline-block
}

.widget-signups__list .avatar-char {
    background-color: rgba(255, 255, 255, .08);
    color: #fff
}

.widget-signups__list .avatar-char,
.widget-signups__list .avatar-img {
    margin: 0
}

.widget-time {
    padding: 2rem
}

.widget-time .time {
    font-size: 2rem;
    text-align: center
}

.widget-time .time>span {
    padding: 1rem 1.5rem;
    background-color: rgba(255, 255, 255, .08);
    border-radius: 2px;
    display: inline-block;
    line-height: 50px;
    margin: 0 5px;
    position: relative
}

.widget-time .time>span:after {
    position: absolute;
    right: -13px;
    top: 10px
}

.flot-chart {
    height: 200px;
    display: block
}

.flot-chart--sm {
    height: 100px
}

.flot-chart-legends {
    text-align: center;
    margin: 20px 0 -10px
}

.flot-chart-legends table {
    display: inline-block
}

#jqstooltip .jqsfield>span,
.select2-container--default .select2-selection--single .select2-selection__arrow {
    display: none
}

.flot-chart-legends .legendColorBox>div>div {
    border-radius: 50%
}

.flot-chart-legends .legendLabel {
    padding: 0 8px 0 3px;
    color: rgba(255, 255, 255, .7)
}

#jqstooltip .jqsfield,
.flot-tooltip {
    font-size: .95rem;
    color: rgba(255, 255, 255, .85)
}

.flot-tooltip {
    position: absolute;
    line-height: 100%;
    display: none;
    border-radius: 2px;
    padding: .7rem 1rem;
    background-color: rgba(0, 0, 0, .8);
    z-index: 99999
}

#jqstooltip {
    text-align: center;
    padding: 5px 10px;
    border: 0;
    height: auto !important;
    width: auto !important;
    background: #fff;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, .8)
}

#jqstooltip .jqsfield {
    font-weight: 500;
    font-family: inherit;
    text-align: center
}

.select2-container--default .select2-selection--single {
    border-radius: 0;
    border: 0;
    background-color: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    height: auto
}

.select2-container--default .select2-selection--single:before {
    content: "";
    position: absolute;
    pointer-events: none;
    z-index: 1;
    right: 1px;
    bottom: 5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 8px 8px;
    border-color: transparent transparent #d1d1d1
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: rgba(255, 255, 255, .5)
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 1.25;
    padding: .6rem 13px;
    color: rgba(255, 255, 255, .85)
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: 0
}

.select2-container--default .select2-selection--multiple {
    background-color: transparent;
    border: 0;
    box-shadow: 0 1px 0 0 rgba(255, 255, 255, .2);
    border-radius: 0;
    padding-bottom: 1px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    border-radius: 2px;
    border: 0;
    background-color: rgba(255, 255, 255, .08);
    padding: .4rem .8rem;
    color: rgba(255, 255, 255, .85)
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    margin-right: .5rem;
    color: rgba(255, 255, 255, .85)
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: rgba(255, 255, 255, .85);
    opacity: .75
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0 1px
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: transparent;
    opacity: .5
}

.select2-dropdown {
    background-color: rgba(0, 0, 0, .96);
    border: 0;
    border-radius: 2px;
    padding: .8rem 0;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5);
    z-index: 99;
    margin-top: -1px;
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    animation-duration: .3s;
    animation-fill-mode: both
}

.select2-dropdown .select2-results__option {
    padding: .65rem 1.5rem
}

.select2-dropdown .select2-results__option--highlighted[aria-selected] {
    background-color: rgba(255, 255, 255, .06);
    color: #fff
}

.select2-dropdown .select2-results__option[aria-disabled=true] {
    color: rgba(255, 255, 255, .85);
    opacity: .25
}

.select2-dropdown .select2-results__option[aria-selected=true] {
    position: relative;
    padding-right: 1.5rem;
    background-color: rgba(255, 255, 255, .06);
    color: #fff
}

.select2-dropdown .select2-results__option[aria-selected=true]:before {
    font-family: Material-Design-Iconic-Font;
    content: '\f26b';
    position: absolute;
    top: .45rem;
    right: 1.5rem;
    font-size: 1.3rem;
    color: #fff
}

.select2-dropdown .select2-search--dropdown {
    margin-top: -.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, .08);
    position: relative;
    margin-bottom: 1rem
}

.select2-dropdown .select2-search--dropdown:before {
    font-family: Material-Design-Iconic-Font;
    content: '\f1c3';
    font-size: 1.5rem;
    color: rgba(255, 255, 255, .85);
    position: absolute;
    left: 1.4rem;
    top: .5rem
}

.select2-dropdown .select2-search--dropdown .select2-search__field {
    border: 0;
    background-color: transparent;
    height: 2.8rem;
    padding-left: 3.5rem
}

.select2-search__field {
    color: rgba(255, 255, 255, .85)
}

.select2-search__field::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.select2-search__field:-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.select2-search__field::-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.select2-search__field:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.dropzone {
    border: 0;
    background-color: rgba(0, 0, 0, .2);
    border-radius: 2px;
    transition: border-color .3s, background-color .3s;
    min-height: 50px;
    position: relative
}

.dropzone:before {
    font-family: Material-Design-Iconic-Font;
    content: '\f22a';
    font-size: 2rem;
    color: rgba(255, 255, 255, .85);
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background-color: rgba(255, 255, 255, .08);
    border-radius: 50%;
    opacity: 0
}

.dropzone .dz-preview.dz-file-preview .dz-image,
.dropzone .dz-preview.dz-image-preview .dz-image {
    border-radius: 2px;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .15)
}

.dropzone .dz-preview .dz-remove {
    position: absolute;
    top: -9px;
    right: -9px;
    z-index: 99;
    font-size: 0;
    width: 22px;
    height: 22px;
    background-color: #dc3545;
    border-radius: 50%;
    border: 2px solid #fff
}

.dropzone .dz-preview .dz-remove:hover {
    background-color: #d32535;
    text-decoration: none
}

.dropzone .dz-preview .dz-remove:before {
    content: '\f136';
    font-size: .8rem;
    font-family: Material-Design-Iconic-Font;
    color: #fff;
    font-weight: 700;
    line-height: 19px;
    padding: 0 6px
}

.dropzone .dz-message span,
.dropzone:hover .dz-message span {
    color: rgba(255, 255, 255, .85)
}

.dropzone .dz-message span {
    display: inline-block;
    border-radius: 2px;
    transition: color .3s, box-shadow, .3s;
    padding: .5rem 1.4rem .8rem;
    background-color: rgba(255, 255, 255, .08)
}

.dropzone .dz-message span:before {
    content: '\f21e';
    font-family: Material-Design-Iconic-Font;
    font-size: 1.4rem;
    display: inline-block;
    position: relative;
    top: 2px;
    margin-right: .8rem
}

.dropzone.dz-drag-hover,
.dropzone:hover {
    background-color: rgba(0, 0, 0, .25)
}

.dropzone.dz-drag-hover .dz-message {
    opacity: 0
}

.dropzone.dz-drag-hover:before {
    opacity: 1
}

.noUi-target {
    border-radius: 0;
    box-shadow: none;
    border: 0;
    background: rgba(255, 255, 255, .08);
    margin: 15px 0
}

.noUi-horizontal {
    height: 2px
}

.noUi-horizontal .noUi-handle {
    top: -5px;
    left: -1px
}

.noUi-vertical {
    width: 3px
}

.noUi-connect {
    background: #fff;
    box-shadow: none
}

.noUi-horizontal .noUi-handle,
.noUi-vertical .noUi-handle {
    width: 12px;
    height: 12px;
    border: 0;
    border-radius: 100%;
    box-shadow: none;
    cursor: pointer;
    position: relative;
    background-color: #fff;
    transition: box-shadow .2s, -webkit-transform .2s;
    transition: box-shadow .2s, transform .2s;
    transition: box-shadow .2s, transform .2s, -webkit-transform .2s
}

.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before,
.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
    display: none
}

.noUi-horizontal .noUi-handle.noUi-active,
.noUi-vertical .noUi-handle.noUi-active {
    -webkit-transform: scale(1.3);
    transform: scale(1.3)
}

.noUi-horizontal .noUi-active,
.noUi-vertical .noUi-active {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, .08)
}

.input-slider--blue .noUi-connect {
    background: #007bff
}

.input-slider--blue.noUi-horizontal .noUi-handle,
.input-slider--blue.noUi-vertical .noUi-handle {
    background-color: #007bff
}

.input-slider--red .noUi-connect {
    background: #dc3545
}

.input-slider--red.noUi-horizontal .noUi-handle,
.input-slider--red.noUi-vertical .noUi-handle {
    background-color: #dc3545
}

.input-slider--amber .noUi-connect {
    background: #ffc107
}

.input-slider--amber.noUi-horizontal .noUi-handle,
.input-slider--amber.noUi-vertical .noUi-handle {
    background-color: #ffc107
}

.input-slider--green .noUi-connect {
    background: #28a745
}

.input-slider--green.noUi-horizontal .noUi-handle,
.input-slider--green.noUi-vertical .noUi-handle {
    background-color: #28a745
}

.easy-pie-chart {
    display: inline-block;
    position: relative
}

.easy-pie-chart__value {
    position: absolute;
    left: 0;
    top: 0;
    text-align: center;
    width: 100%;
    height: 100%
}

.easy-pie-chart__value:after {
    content: "%";
    font-size: 12px
}

.easy-pie-chart__title {
    margin-top: -2px;
    line-height: 15px;
    font-size: 11px
}

.dataTable .sorting,
.dataTable .sorting_asc,
.dataTable .sorting_desc {
    cursor: pointer;
    position: relative
}

.dataTable .sorting_asc:before,
.dataTable .sorting_desc:before {
    font-family: Material-Design-Iconic-Font;
    font-size: 1.25rem;
    position: absolute;
    bottom: 8px;
    right: 5px;
    opacity: 0;
    transition: opacity .3s
}

.dataTable .sorting_asc:hover:before,
.dataTable .sorting_desc:hover:before {
    opacity: 1
}

.dataTable .sorting_asc:before {
    content: '\f1cd'
}

.dataTable .sorting_desc:before {
    content: '\f1ce'
}

.dataTables_wrapper {
    margin-top: 20px
}

.dataTables_wrapper .table {
    margin: 40px 0 20px
}

.dataTables_filter,
.dataTables_length {
    font-size: 0;
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.dataTables_filter:after,
.dataTables_length:after {
    font-family: Material-Design-Iconic-Font;
    position: absolute;
    left: 1px;
    bottom: 6px;
    font-size: 1.3rem;
    color: #fff
}

.dataTables_filter>label,
.dataTables_length>label {
    margin: 0;
    width: 100%
}

.dataTables_filter>label input[type=search],
.dataTables_filter>label select,
.dataTables_length>label input[type=search],
.dataTables_length>label select {
    padding-left: 28px;
    font-size: 1rem;
    background: 0 0;
    border: 1px solid rgba(255, 255, 255, .2);
    border-top: 0;
    border-left: 0;
    border-right: 0;
    height: 35px;
    border-radius: 0;
    width: 100%;
    color: rgba(255, 255, 255, .85);
    transition: border-color .3s
}

.dataTables_filter>label input[type=search]:focus,
.dataTables_filter>label select:focus,
.dataTables_length>label input[type=search]:focus,
.dataTables_length>label select:focus {
    border-color: rgba(255, 255, 255, .4)
}

.dataTables_filter>label input[type=search]::-webkit-input-placeholder,
.dataTables_filter>label select::-webkit-input-placeholder,
.dataTables_length>label input[type=search]::-webkit-input-placeholder,
.dataTables_length>label select::-webkit-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.dataTables_filter>label input[type=search]:-moz-placeholder,
.dataTables_filter>label select:-moz-placeholder,
.dataTables_length>label input[type=search]:-moz-placeholder,
.dataTables_length>label select:-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.dataTables_filter>label input[type=search]::-moz-placeholder,
.dataTables_filter>label select::-moz-placeholder,
.dataTables_length>label input[type=search]::-moz-placeholder,
.dataTables_length>label select::-moz-placeholder {
    color: rgba(255, 255, 255, .5)
}

.dataTables_filter>label input[type=search]:-ms-input-placeholder,
.dataTables_filter>label select:-ms-input-placeholder,
.dataTables_length>label input[type=search]:-ms-input-placeholder,
.dataTables_length>label select:-ms-input-placeholder {
    color: rgba(255, 255, 255, .5)
}

.dataTables_length {
    float: right;
    margin-left: 20px
}

.dataTables_length:before {
    content: "";
    position: absolute;
    pointer-events: none;
    z-index: 1;
    right: 1px;
    bottom: 5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 8px 8px;
    border-color: transparent transparent #d1d1d1
}

@media (max-width:575px) {
    .dataTables_length {
        display: none
    }
}

.dataTables_length:after {
    content: '\f197'
}

.dataTables_length select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.dataTables_filter:after {
    content: '\f1c3'
}

.dataTables_filter--toggled>label:after,
.dataTables_filter--toggled>label:before {
    width: 50%
}

.dataTables_paginate {
    text-align: center
}

.paginate_button {
    background-color: rgba(255, 255, 255, .08);
    display: inline-block;
    color: rgba(255, 255, 255, .85);
    vertical-align: top;
    border-radius: 50%;
    margin: 0 1px 0 2px;
    font-size: 1rem;
    cursor: pointer;
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    text-align: center;
    transition: background-color .3s, color .3s
}

.colorpicker:after,
.colorpicker:before,
.dt-buttons,
.flatpickr-calendar:after,
.flatpickr-calendar:before {
    display: none
}

.paginate_button.current {
    background-color: #fff;
    color: #000;
    cursor: default
}

.paginate_button:not(.current):not(.disabled):focus,
.paginate_button:not(.current):not(.disabled):hover {
    background-color: rgba(255, 255, 255, .2);
    color: rgba(255, 255, 255, .85)
}

.paginate_button.current,
.paginate_button.disabled {
    cursor: default
}

.paginate_button.next,
.paginate_button.previous {
    font-size: 0;
    position: relative
}

@media screen and (-ms-high-contrast:active),
(-ms-high-contrast:none) {

    .paginate_button.next,
    .paginate_button.previous {
        font-size: 1rem
    }
}

.paginate_button.next:before,
.paginate_button.previous:before {
    font-family: Material-Design-Iconic-Font;
    font-size: 1rem;
    line-height: 2.55rem
}

.paginate_button.previous:before {
    content: '\F2FF'
}

.paginate_button.next:before {
    content: '\F301'
}

.paginate_button.disabled:focus,
.paginate_button.disabled:hover {
    color: rgba(255, 255, 255, .85)
}

.dataTables_info {
    text-align: center;
    padding: 2.5rem 0 1.5rem;
    font-size: .9rem;
    color: rgba(255, 255, 255, .7)
}

.data-table-toggled .dataTables_buttons [data-table-action=fullscreen]:before {
    content: '\f16c'
}

.flatpickr-calendar {
    border-radius: 2px;
    border: 0;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5);
    width: auto;
    margin-top: -4px;
    user-select: none;
    background-color: rgba(0, 0, 0, .96)
}

.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
    border-color: rgba(255, 255, 255, .04);
    height: 60px
}

.flatpickr-month {
    background-color: rgba(255, 255, 255, .05);
    color: #fff;
    height: 60px;
    border-radius: 2px 2px 0 0;
    margin-bottom: 10px
}

.flatpickr-current-month {
    font-size: 1rem;
    top: 15px;
    font-weight: 600
}

.flatpickr-current-month input.cur-year,
.flatpickr-current-month span.cur-month {
    font-weight: 600
}

.flatpickr-current-month .numInputWrapper:hover,
.flatpickr-current-month span.cur-month:hover {
    background-color: transparent
}

.flatpickr-current-month .numInputWrapper span {
    border: 0;
    right: -5px;
    padding: 0
}

.flatpickr-current-month .numInputWrapper span:after {
    left: 3px
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: #fff
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: #fff
}

.flatpickr-next-month,
.flatpickr-prev-month {
    width: 35px;
    height: 35px;
    line-height: 35px;
    border-radius: 50%;
    font-size: 1.5rem;
    top: 13px;
    padding: 0
}

.flatpickr-next-month:hover,
.flatpickr-prev-month:hover {
    background-color: rgba(255, 255, 255, .05);
    color: rgba(255, 255, 255, .85)
}

.flatpickr-prev-month {
    margin-left: 12px
}

.flatpickr-next-month {
    margin-right: 12px
}

.flatpickr-innerContainer {
    padding: 10px
}

span.flatpickr-weekday {
    font-weight: 600;
    color: #fff
}

.flatpickr-day {
    font-size: .92rem;
    border: 0;
    color: rgba(255, 255, 255, .85)
}

.flatpickr-day.selected,
.flatpickr-day.selected:hover {
    background-color: #fff !important;
    color: #000
}

.flatpickr-day.today,
.flatpickr-day.today:hover {
    background-color: #e2e2e2;
    color: #000
}

.flatpickr-day:hover {
    background-color: rgba(255, 255, 255, .08)
}

.flatpickr-time {
    max-height: 60px;
    height: 60px;
    line-height: 60px
}

.flatpickr-time .numInput {
    color: rgba(255, 255, 255, .85)
}

.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .numInputWrapper {
    height: auto;
    color: rgba(255, 255, 255, .85)
}

.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time .numInputWrapper:focus,
.flatpickr-time .numInputWrapper:hover {
    background-color: rgba(255, 255, 255, .025)
}

.flatpickr-time .numInputWrapper span.arrowDown:after,
.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-top-color: rgba(255, 255, 255, .85);
    border-bottom-color: rgba(255, 255, 255, .85)
}

.flatpickr-day.disabled,
.flatpickr-day.disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
    color: rgba(255, 255, 255, .4)
}

.colorpicker {
    padding: 5px;
    margin-top: -1px
}

.colorpicker div {
    border-radius: 2px
}

.colorpicker-saturation i {
    border: 0;
    box-shadow: 0 0 5px rgba(0, 0, 0, .36)
}

.colorpicker-saturation i,
.colorpicker-saturation i b {
    height: 10px;
    width: 10px
}

.colorpicker-alpha,
.colorpicker-hue {
    width: 20px
}

.colorpicker-color,
.colorpicker-color div {
    height: 20px
}

.color-picker__preview {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, .08)
}

.trumbowyg-box {
    border: 0;
    background-color: rgba(0, 0, 0, .2)
}

.trumbowyg-button-pane {
    background-color: rgba(255, 255, 255, .8);
    border: 0
}

.trumbowyg-button-pane:after {
    height: 0
}

.trumbowyg-button-pane .trumbowyg-button-group:not(:empty)+.trumbowyg-button-group:before {
    background-color: rgba(255, 255, 255, .2)
}

.trumbowyg-button-pane button {
    margin: 0;
    height: 36px
}

.trumbowyg-dropdown {
    border: 0;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5)
}

.trumbowyg-dropdown button {
    font-size: 1rem;
    height: 40px;
    padding: 0 1.5rem
}

.trumbowyg-dropdown button svg {
    margin-top: -3px
}

.trumbowyg-dropdown button:hover {
    background-color: rgba(255, 255, 255, .06)
}

.trumbowyg-modal-box {
    font-size: 1rem;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5);
    background-color: rgba(0, 0, 0, .96);
    border-radius: 2px
}

.trumbowyg-modal-box .trumbowyg-modal-title {
    font-size: 1rem;
    color: #fff;
    font-weight: 600;
    border: 0;
    background-color: transparent;
    margin: 0
}

.trumbowyg-modal-box label {
    margin: 15px 20px;
    font-weight: 400
}

.trumbowyg-modal-box label .trumbowyg-input-infos span {
    color: #fff;
    border-color: rgba(255, 255, 255, .2);
    background-color: rgba(0, 0, 0, .96)
}

.trumbowyg-modal-box label input {
    border-color: rgba(255, 255, 255, .2);
    font-size: 1rem;
    color: rgba(255, 255, 255, .85);
    background-color: rgba(0, 0, 0, .96)
}

.trumbowyg-modal-box label input:focus,
.trumbowyg-modal-box label input:hover {
    border-color: rgba(242, 242, 242, .2);
    background-color: rgba(0, 0, 0, .96)
}

.trumbowyg-modal-box .trumbowyg-modal-button {
    font-size: 1rem;
    height: auto;
    line-height: 100%;
    border-radius: 2px;
    padding: 7px 0;
    margin: 0 20px;
    bottom: 18px
}

.trumbowyg-overlay {
    background-color: transparent
}

.fc-scroller {
    height: auto !important
}

.fc th {
    font-weight: 600;
    padding: 12px 12px 10px
}

.fc table {
    background: 0 0
}

.fc table tr>td:first-child {
    border-left-width: 0
}

.fc table tr>td.fc-widget-header {
    border-top-width: 0
}

.fc div.fc-row {
    margin-right: 0;
    border: 0
}

.fc-unthemed td.fc-today {
    background-color: transparent
}

.fc-unthemed td.fc-today span {
    color: #fd7e14
}

.fc-event {
    padding: 0;
    font-size: .92rem;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, .08);
    border: 0
}

.fc-event .fc-title {
    padding: 4px 8px;
    display: block;
    color: #fff;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600
}

.fc-event .fc-time {
    float: left;
    background: rgba(0, 0, 0, .2);
    padding: 2px 6px;
    margin: 0 0 0 -1px
}

.fc-view,
.fc-view>table {
    border: 0
}

.fc-view>table>tbody>tr .ui-widget-content {
    border-top: 0
}

.fc-icon {
    font-family: Material-Design-Iconic-Font;
    font-size: 1.5rem;
    text-shadow: none;
    color: rgba(255, 255, 255, .85);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    line-height: 3rem;
    transition: background-color .3s
}

.fc-icon:hover {
    background: rgba(255, 255, 255, .06)
}

.fc-button {
    border: 0;
    background: 0 0;
    box-shadow: none
}

.fc-button .fc-icon {
    top: -.5rem !important
}

.fc-highlight {
    background-color: rgba(0, 0, 0, .4)
}

.calendar {
    z-index: 0
}

.calendar td,
.calendar th {
    border-color: rgba(255, 255, 255, .075)
}

.calendar .fc-toolbar {
    height: 250px;
    background-color: transparent;
    border-radius: 2px 2px 0 0;
    position: relative;
    z-index: 2;
    margin-bottom: -5px
}

@media (max-width:575px) {
    .calendar .fc-toolbar {
        height: 135px
    }
}

.calendar .fc-day-number {
    padding: 6px 10px;
    width: 100%;
    box-sizing: border-box
}

@media (min-width:576px) {
    .dataTables_length {
        min-width: 150px
    }

    .calendar .fc-day-number {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, .85)
    }
}

.calendar .fc-day-header {
    text-align: left
}

.calendar .fc-day-grid-event {
    margin: 1px 9px
}

.widget-calendar>.actions {
    top: 30px
}

.widget-calendar td,
.widget-calendar th {
    border-color: transparent;
    text-align: center
}

.widget-calendar .fc-toolbar h2 {
    font-size: 1.2rem;
    padding-top: .3rem
}

.widget-calendar .fc-day-number {
    text-align: center;
    width: 100%;
    padding: 0
}

.widget-calendar__header {
    padding: 2.1rem 2.2rem;
    background-color: rgba(255, 255, 255, .02);
    border-radius: 2px 2px 0 0
}

.widget-calendar__year {
    font-size: 1.2rem;
    line-height: 100%;
    margin-bottom: .6rem;
    color: rgba(255, 255, 255, .8)
}

.widget-calendar__day {
    font-size: 1.5rem;
    line-height: 100%;
    color: #fff
}

.widget-calendar__body {
    padding: 1rem;
    margin-top: 1rem
}

.event-tag>span {
    border-radius: 50%;
    width: 30px;
    height: 30px;
    margin: 0 0 3px;
    position: relative;
    display: inline-block;
    vertical-align: top;
    cursor: pointer
}

.event-tag>span,
.event-tag>span>i {
    transition: all .2s
}

.event-tag>span>input[type=radio] {
    margin: 0;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
    cursor: pointer;
    opacity: 0
}

.event-tag>span>input[type=radio]:checked+i {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.event-tag>span:hover {
    opacity: .8
}

.event-tag>span>i {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 4px 0 0 7px;
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0)
}

.event-tag>span>i:before {
    content: '\f26b';
    font-family: Material-Design-Iconic-Font;
    color: #fff;
    font-size: 1.2rem;
    z-index: 1
}

[data-calendar-month]:before {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    transition: background-image .3s;
    content: '';
    position: absolute;
    left: 5px;
    top: 5px;
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    z-index: 1
}

[data-calendar-month="0"]:before {
    background-image: url(../img/calendar/january.jpg)
}

[data-calendar-month="1"]:before {
    background-image: url(../img/calendar/february.jpg)
}

[data-calendar-month="2"]:before {
    background-image: url(../img/calendar/march.jpg)
}

[data-calendar-month="3"]:before {
    background-image: url(../img/calendar/april.jpg)
}

[data-calendar-month="4"]:before {
    background-image: url(../img/calendar/may.jpg)
}

[data-calendar-month="5"]:before {
    background-image: url(../img/calendar/june.jpg)
}

[data-calendar-month="6"]:before {
    background-image: url(../img/calendar/july.jpg)
}

[data-calendar-month="7"]:before {
    background-image: url(../img/calendar/august.jpg)
}

[data-calendar-month="8"]:before {
    background-image: url(../img/calendar/september.jpg)
}

[data-calendar-month="9"]:before {
    background-image: url(../img/calendar/october.jpg)
}

[data-calendar-month="10"]:before {
    background-image: url(../img/calendar/november.jpg)
}

[data-calendar-month="11"]:before {
    background-image: url(../img/calendar/december.jpg)
}

.swal2-modal {
    border-radius: 2px;
    padding: 2.5rem !important;
    font-family: Nunito, sans-serif;
    box-shadow: 0 4px 18px rgba(0, 0, 0, .5)
}

.swal2-modal .swal2-title {
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
    color: #fff;
    line-height: inherit;
    margin: 0 0 5px;
    font-weight: 400
}

.swal2-modal .swal2-icon,
.swal2-modal .swal2-image {
    margin-top: 0;
    margin-bottom: 1.5rem
}

.swal2-modal .swal2-content {
    color: rgba(255, 255, 255, .7);
    font-size: 1rem;
    font-weight: 400
}

.swal2-modal .swal2-buttonswrapper {
    margin-top: 30px
}

.swal2-modal .swal2-buttonswrapper .btn {
    margin: 0 3px;
    box-shadow: none !important
}

.swal2-container.in {
    background-color: rgba(0, 0, 0, .2)
}

.lg-outer .lg-thumb-outer {
    background-color: rgba(255, 255, 255, .1)
}

.lg-outer .lg-thumb-item {
    border: 0;
    border-radius: 2px
}

.lg-outer .lg-thumb-item:hover {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, .1)
}

.lg-outer .lg-image {
    border-radius: 2px
}

.lg-outer .lg-toogle-thumb {
    border-radius: 50%;
    color: #000;
    height: 51px;
    width: 51px;
    line-height: 41px;
    background-color: #fff;
    transition: all .5s
}

.lg-outer .lg-toogle-thumb:hover {
    color: #000
}

.lg-outer:not(.lg-thumb-open) .lg-toogle-thumb {
    top: -70px
}

.lg-outer.lg-thumb-open .lg-toogle-thumb {
    top: -26px
}

.lg-thumb.group {
    padding: 20px 0
}

.lg-slide em {
    font-style: normal
}

.lg-slide em h3 {
    color: #fff;
    margin-bottom: 5px
}

.lg-slide .video-cont {
    box-shadow: 0 2px 5px rgba(0, 0, 0, .16), 0 2px 10px rgba(0, 0, 0, .12)
}

.lightbox>a {
    position: relative
}

.lightbox>a:after,
.lightbox>a:before {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: all .3s
}

.lightbox>a:before {
    content: '\f1ee';
    font-family: Material-Design-Iconic-Font;
    font-size: 2.3rem;
    color: #fff;
    bottom: 0;
    right: 0;
    margin: auto;
    width: 25px;
    height: 25px;
    line-height: 25px;
    z-index: 2;
    -webkit-transform: scale(2);
    transform: scale(2)
}

.lightbox>a:after {
    content: '';
    width: 100%;
    height: 100%;
    z-index: 1
}

.lightbox>a:hover:after,
.lightbox>a:hover:before {
    opacity: 1
}

.lightbox>a:hover:before {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.scrollbar-inner {
    height: 100%
}

.scrollbar-inner>.scroll-element {
    transition: opacity .3s;
    margin-right: 2px
}

.scrollbar-inner>.scroll-element.scroll-y {
    width: 3px;
    right: 0
}

.scrollbar-inner>.scroll-element.scroll-x {
    height: 3px;
    bottom: 0
}

.scrollbar-inner>.scroll-element .scroll-bar,
.scrollbar-inner>.scroll-element .scroll-element_track {
    transition: background-color .3s
}

.scrollbar-inner>.scroll-element .scroll-element_track {
    background-color: transparent
}

.scrollbar-inner:not(:hover) .scroll-element {
    opacity: 0
}

.waves-effect {
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    opacity: 0;
    transition: all .5s ease-out;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transform: scale(0) translate(0, 0);
    transform: scale(0) translate(0, 0);
    pointer-events: none
}

.waves-effect.btn-link .waves-ripple,
.waves-effect.btn-secondary .waves-ripple,
.waves-effect:not(.waves-light) .waves-ripple {
    background: rgba(0, 0, 0, .08)
}

.waves-effect.btn:not(.btn-secondary):not(.btn-link) .waves-ripple,
.waves-effect.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .5)
}

.waves-effect.waves-classic .waves-ripple {
    background: rgba(0, 0, 0, .08)
}

.waves-effect.waves-classic.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .5)
}

.waves-notransition {
    transition: none !important
}

.waves-button,
.waves-circle {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-mask-image: -webkit-radial-gradient(circle, #fff 100%, #000 100%)
}

.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1;
    border: 0
}

.waves-block {
    display: block
}

ul.jqtree-tree {
    border-top: 1px solid rgba(255, 255, 255, .125)
}

ul.jqtree-tree li.jqtree-selected>.jqtree-element {
    transition: background-color .2s, border-color .2s;
    border-color: transparent
}

ul.jqtree-tree li.jqtree-selected>.jqtree-element,
ul.jqtree-tree li.jqtree-selected>.jqtree-element:hover {
    background: rgba(255, 255, 255, .125);
    text-shadow: none
}

ul.jqtree-tree li:not(.jqtree-selected)>.jqtree-element:hover {
    background-color: rgba(255, 255, 255, .02)
}

ul.jqtree-tree li.jqtree-folder {
    margin-bottom: 0
}

ul.jqtree-tree li.jqtree-folder:not(.jqtree-closed)+li.jqtree_common {
    position: relative
}

ul.jqtree-tree li.jqtree-folder:not(.jqtree-closed)+li.jqtree_common:before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 30px;
    background-color: rgba(255, 255, 255, .125);
    height: 1px
}

ul.jqtree-tree li.jqtree-folder.jqtree-closed {
    margin: 0
}

ul.jqtree-tree li.jqtree-ghost span.jqtree-line {
    background-color: #fff
}

ul.jqtree-tree li.jqtree-ghost span.jqtree-circle {
    border-color: #fff
}

ul.jqtree-tree .jqtree-moving>.jqtree-element .jqtree-title {
    outline: 0
}

ul.jqtree-tree span.jqtree-border {
    border-radius: 0;
    border-color: #fff
}

ul.jqtree-tree .jqtree-toggler {
    position: absolute;
    height: 16px;
    width: 16px;
    background: rgba(255, 255, 255, .75);
    color: #131313;
    padding: 0 0 0 1px;
    font-size: 1rem;
    border-radius: 50%;
    top: 12px;
    left: -8px;
    line-height: 16px;
    text-align: center;
    transition: background-color .3s, color .3s
}

ul.jqtree-tree .jqtree-toggler:hover {
    background-color: #fff;
    color: #000
}

ul.jqtree-tree .jqtree-element {
    position: relative;
    padding: 10px 20px;
    border: 1px solid rgba(255, 255, 255, .125);
    border-top: 0;
    margin-bottom: 0
}

ul.jqtree-tree .jqtree-title {
    color: #fff;
    margin-left: 0
}

ul.jqtree-tree ul.jqtree_common {
    margin-left: 22px;
    padding-left: 8px
}

.jq-ry-container {
    padding: 0;
    display: inline-block
}

.text-count-wrapper {
    position: absolute;
    bottom: -23px;
    height: 20px;
    width: 100%;
    left: 0;
    font-size: .875rem
}

.error-text-min {
    float: right
}