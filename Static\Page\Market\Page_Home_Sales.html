<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!--favicon-->
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
    <link rel="stylesheet" href="/static/CSS/bootstrap-fileinput/fileinput.min.css">
    <link rel="stylesheet" href="/static/CSS/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/CSS/animate/animate.min.css">
    <link rel="stylesheet" href="/static/CSS/jquery/fullcalendar/fullcalendar.min.css">
    <!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />

    <link href="/static/CSS/font-awesome/all.min.css" rel="stylesheet" />
    <!-- 加载 Select2 -->
    <link href="/static/CSS/select2/select2.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="/static/CSS/jquery/jquery.scrollbar.css">
    <!--     dialog   -->
    <link rel="stylesheet" href="/static/CSS/trumbowyg/trumbowyg.min.css">
    <link rel="stylesheet" href="/static/CSS/sweetalert/sweetalert2.min.css">
    <link rel="stylesheet" href="/static/CSS/server_style.css">

    <!-- data-table CSS ============================================ -->
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-table.css">
    <link rel="stylesheet" href="/static/CSS/data-table/bootstrap-editable.css">
    <!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
    <!-- App styles -->
    <link rel="stylesheet" href="/static/CSS/app/app.min.css">
    <!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />

    <style>
        .table {
            table-layout: fixed;
            word-break: break-all;
        }

        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            /* 对于Webkit浏览器如Chrome和Safari */
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
            /* 对于Firefox，隐藏所有默认样式 */
        }

        /* 可选：设置焦点样式 */
        input[type="number"]:focus {
            outline: none;
            /* 移除聚焦时的默认轮廓 */
        }

        .btn-outline-secondary:hover {
            color: #333;
        }

        textarea::selection {
            background-color: #b3d4fc;
            /* 或其他颜色 */
            color: inherit;
            /* 继承文本颜色 */
        }

        .sticky {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            /* 确保在其他内容之上 */
        }

        .accordion .card-header:after {
            content: "";
        }
        
        .nav-link {
            display: block;
            padding: 0rem 1.5rem;
        }

    </style>
    <title>哨兵导控</title>
</head>

<body class="bg-theme bg-theme1">

    <div class="wrapper">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>

        <div class="page-wrapper">
            <div class="page-content-wrapper">
                <div class="page-content">
                    <div id="opera_box" style="width: 100%;z-index: 3;transition: all 0.3s ease;">
                        <div id="operation" class="btn-group" data-toggle="buttons">
                            <label class="btn btn-light active">
                                <input type="radio" name="operation" id="option1" value="account" autocomplete="off" checked>
                                账号支持
                            </label>
                            <label class="btn btn-light">
                                <input type="radio" name="operation" id="marketing_server" value="counter" autocomplete="off">
                                营销服务
                            </label>
                            <label class="btn btn-light">
                                <input type="radio" name="operation" id="serverphone" value="phone" autocomplete="off"> 流量卡
                            </label>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div id="target-url-container" class="form-group"
                                    style="display:none; transition: opacity 0.5s ease-in-out;">
                                    <label for="target_url">目标链接：</label>
                                    <input type="text" class="form-control" id="target_url" placeholder="请输入目标链接"
                                        name="target_url" />
                                </div>
                                <!--        账号购买多选-->
                                <div id="type_account" class="form-group">
                                    <label>账号类型筛选：</label>
                                    <!-- {%
                                    set option =[
                                    { "name": "facebook", "img": "http://www.csc-tech.cn:8746/static/img/web_log/FACEBOOK.gif",
                                    "id": 2 },
                                    { "name": "twitter", "img": "http://www.csc-tech.cn:8746/static/img/web_log/TWITTER.jpg",
                                    "id": 3 },
                                    { "name": "youtube", "img": "http://www.csc-tech.cn:8746/static/img/web_log/YOUTUBE.PNG",
                                    "id": 4 },
                                    { "name": "odnoklassniki", "img": "http://www.csc-tech.cn:8746/static/img/web_log/OK.PNG",
                                    "id": 5 },
                                    { "name": "discuss", "img": "http://www.csc-tech.cn:8746/static/img/web_log/DISCUSS.PNG",
                                    "id": 6 },
                                    { "name": "vkontakte", "img": "http://www.csc-tech.cn:8746/static/img/web_log/VK.PNG", "id":
                                    7 }]
                                    %}

                                    {% for nav in option %}
                                    <label class="custom-control custom-checkbox">
                                        <input type="checkbox" value="{{nav['name']}}" class="custom-control-input" checked>
                                        <span class="custom-control-indicator"></span>
                                        <span class="custom-control-description text-uppercase">{{nav['name']}}</span>
                                    </label>
                                    {% endfor %} -->
                                    <div class="d-flex mt-4">
                                        <button id="restSelect" type="button" class="btn btn-light">重置筛选</button>
                                    </div>
                                </div>
                                <!--        营销服务单选-->
                                <div id="type_counter" style="display: none;" class="form-group">
                                    <label>账号类型筛选：</label>
                                    <!-- {%
                                    set radio_option =[
                                    { "name": "facebook", "img": "http://www.csc-tech.cn:8746/static/img/web_log/FACEBOOK.gif",
                                    "id": 2 },
                                    { "name": "twitter", "img": "http://www.csc-tech.cn:8746/static/img/web_log/TWITTER.jpg",
                                    "id": 3 },
                                    { "name": "youtube", "img": "http://www.csc-tech.cn:8746/static/img/web_log/YOUTUBE.PNG",
                                    "id": 4 },
                                    { "name": "odnoklassniki", "img": "http://www.csc-tech.cn:8746/static/img/web_log/OK.PNG",
                                    "id": 5 },
                                    { "name": "discuss", "img": "http://www.csc-tech.cn:8746/static/img/web_log/DISCUSS.PNG",
                                    "id": 6 },
                                    { "name": "vkontakte", "img": "http://www.csc-tech.cn:8746/static/img/web_log/VK.PNG", "id":
                                    7 }]
                                    %}
                                    {% for nav in radio_option %}
                                    <label class="custom-control custom-radio">
                                        <input type="radio" name="radio-inline" value="{{nav['name']}}"
                                            class="custom-control-input" {{ 'checked' if nav['name']=='facebook' else '' }}>
                                        <span class="custom-control-indicator"></span>
                                        <span class="custom-control-description text-uppercase">{{nav['name']}}</span>
                                    </label>
                                    {% endfor %} -->
                                </div>
                                <div id="type_phone" style="display:none; transition: opacity 0.5s ease-in-out;">
                                    <div class="form-group">
                                        <label for="contact_name">收货人姓名：</label>
                                        <input type="text" class="form-control" id="contact_name" placeholder="请输入收货人姓名"
                                            name="contact_name" />
                                    </div>
                                    <div class="form-group">
                                        <label for="contact">联系方式：</label>
                                        <input type="text" class="form-control" id="contact" placeholder="请输入收货人联系方式"
                                            name="contact" />
                                    </div>
                                    <div class="form-group">
                                        <label for="address">收货地址：</label>
                                        <input type="text" class="form-control" id="address" placeholder="请输入收货地址"
                                            name="address" />
                                    </div>
                                </div>
                                <div>若超过购买限制数量，请加入购物车后再次添加。</div>
                            </div>
                        </div>
                    </div>
                    <div id="block"></div>
                    <div class="accordion" id="accordion" role="tablist">

                    </div>


                    <div style="height: 100px"></div>
                    <!--底部操作按钮-->
                    <div class="fixed-bottom card mb-0 bg-yellow">
                        <div class="card-body p-3 row">
                            <div class="col text-black d-flex align-items-center">
                                <img class="rounded-circle mr-3" data-toggle="modal" data-target="#modal-backdrop-ignore"
                                    style="width: 3rem;height: 3rem" src="/static/Images/User/Face/Examine.jpg" alt="">
                                <button class="btn btn-success btn-lg btn--icon-text d-sm-block" data-toggle="modal"
                                    data-target="#modal-backdrop-ignore"><i class="zmdi zmdi-card"
                                        style="transform: translateY(3px)"></i>信用点：<span id="user_charger">0.00</span></button>
                            </div>
                            <div class="col text-right ">
                                <div class="d-inline-block mr-2 text-red font-weight-bold"
                                    style="font-size: 16px;vertical-align: middle;">
                                    已选购总点数： <sapn id="buy_point">0</sapn>
                                </div>
                                <button type="button" class="btn btn-lg btn-primary text-white" data-toggle="modal"
                                    data-target="#modal-large"><i class="zmdi zmdi-shopping-cart"></i> 立即结算（<sapn
                                        id="buy_number">0</sapn>
                                    ）</button>
                                <button type="button" class="btn btn-danger btn-lg" onclick="add_mission()">加入购物车</button>
                            </div>
                        </div>
                    </div>

                    <!--返回顶部-->
                    <div id="backBtn" class="fixed-bottom border-light p-1 bg-black rounded"
                        style="bottom: 100px;width: fit-content;left: calc(100% - 2rem);cursor: pointer">
                        <i class="zmdi zmdi-format-valign-top" style="font-size: 2rem"></i>
                    </div>

                    <!-- Ignore backdrop click 信用点表单 -->
                    <div class="modal fade" id="modal-backdrop-ignore" style="z-index: 1051;" data-backdrop="static"
                        tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/Images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/Images/App/Sball.gif">
                                        哨兵信用点信息
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">
                                    <form id="Recharge_Points">
                                        <div class="form-row my-4">
                                            <div class="form-inline col">
                                                <label for="username" class="col-form-label mr-2">用户名称</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="username"
                                                    placeholder="用户名称" disabled>
                                            </div>
                                            <div class="form-inline col">
                                                <label for="usertype" class="col-form-label mr-2">用户类型</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="usertype"
                                                    placeholder="用户类型" disabled>
                                            </div>
                                        </div>
                                        <div class="form-row my-4">
                                            <div class="form-inline col">
                                                <label for="phone" class="col-form-label mr-2">联系电话</label>
                                                <input type="text" class="form-control w-75 border rounded p-2" id="phone"
                                                    placeholder="联系电话" disabled>
                                            </div>
                                            <div class="form-inline col">
                                                <label for="email" class="col-form-label mr-2">联系邮箱</label>
                                                <input type="email" class="form-control w-75 border rounded p-2" id="email"
                                                    placeholder="联系邮箱" disabled>
                                            </div>
                                        </div>

                                        <div style="color: #4dd9d5;margin-bottom: 40px;">确定充值金额后等待支付二维码</div>
                                        <div class="d-flex my-4" style="flex-direction: column;align-items: center;">
                                            <div class="form-inline mb-5" style="flex-wrap: nowrap;">
                                                <label for="credit_points" class="col-form-label mr-2"
                                                    style="font-size: 28px;flex-shrink: 0;">信用点数</label>
                                                <input type="text" class="form-control w-75 rounded p-2 border-none"
                                                    id="credit_points" placeholder="信用点数" disabled
                                                    style="font-size: 28px;color: #E67E22;opacity: 1;">
                                                <button class="btn btn-success" type="button" onclick="Get_User_CreditPoints()">刷新</button>
                                            </div>
                                            <div class="form-inline" style="flex-wrap: nowrap;">
                                                <label for="price" class="col-form-label mr-2"
                                                    style="font-size: 28px;flex-shrink: 0;">充值金额</label>
                                                <input type="text" class="form-control w-75  rounded p-2" id="rechange_price"
                                                    placeholder="请输入充值金额" style="font-size: 28px;">
                                                <button type="button" class="btn btn-light" style="opacity: 0;">占位</button>
                                            </div>
                                        </div>
                                        <div class="vpn_flow">
                                            <div class="dv-border-box-1 mx-auto"
                                                style="width: 268px; height: 268px;position: relative">
                                                <style>
                                                    .position-absolute {
                                                        position: absolute;
                                                    }
                                                </style>
                                                <svg width="268" height="268">
                                                    <polygon fill="transparent" points="10, 27 10, 241 13, 244 13, 247 24, 257
                        38, 257 41, 260 73, 260 75, 258 81, 258
                        85, 262 183, 262 187, 258 193, 258
                        195, 260 227, 260 230, 257
                        244, 257 255, 247 255, 244
                        258, 241 258, 27 255, 25 255, 21
                        244, 11 230, 11 227, 8 195, 8 193, 10
                        187, 10 183, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24"></polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="left-top position-absolute"
                                                    style="left: 0;top: 0;">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="right-top  position-absolute"
                                                    style="right: 0;top: 0;transform: rotateZ(180deg) rotateX(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="left-bottom position-absolute"
                                                    style="right: 0;bottom: 0;transform: rotateZ(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <svg width="150px" height="150px" class="right-bottom position-absolute"
                                                    style="left: 0;bottom: 0;transform:  rotateX(180deg)">
                                                    <polygon
                                                        points="6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;#4fd2dd"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
                                                        fill="#235fa7">
                                                        <animate attributeName="fill" values="#235fa7;#4fd2dd;#235fa7"
                                                            dur="0.5s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                    <polygon
                                                        points="9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
                                                        fill="#4fd2dd">
                                                        <animate attributeName="fill" values="#4fd2dd;#235fa7;transparent"
                                                            dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                    </polygon>
                                                </svg>
                                                <div style="position: absolute;top: 0;width: 100%;text-align: center">
                                                    <hr class="hr_top" style="margin-top: 0px; margin-left: 0px; opacity: 0;">
                                                    <p style="margin: 45% 5%; font-size: 18px; color: cyan;">
                                                        <span style="margin: 0px 18%;"> 确认后显示付款码</span>
                                                    <div id="qrcode"
                                                        style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                                                    </div>
                                                    </p>
                                                </div>
                                            </div>
                                            <div id="pay_order_status" class="text-center"></div>
                                        </div>
                                        <div class="modal-footer justify-content-center mt-4">
                                            <button id="qr_submit" class="btn btn-light mx-5" type="submit">确认充值</button>
                                            <button id="cancel_pay" class="btn btn-light mx-5"
                                                data-dismiss="modal">取消退出</button>
                                            <button class="btn btn-light mx-5" style="display: none;" type="button"
                                                id="reflush_points">刷新点数</button>
                                        </div>
                                    </form>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!--订单提交-->
                    <div class="modal fade" id="modal-large" data-backdrop="static" tabindex="-2">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/Images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/Images/App/Sball.gif">
                                        已选服务项目
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">
                                    <table id="shop_list" class="table-no-bordered striped mb-0"></table>

                                    <hr>
                                    <h5 class="text-right mt-3">订单总额：<span id="order_price">0</span>点数</h5>
                                </div>
                                <div class="modal-footer justify-content-center mt-3">
                                    <button type="button" class="btn btn-link btn-light mx-5" id="post_order">提交订单</button>
                                    <button type="button" class="btn btn-link btn-light mx-5" data-dismiss="modal">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--评论编辑-->
                    <div class="modal fade" id="modal-editor" data-backdrop="static" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content" style="background: url('/static/Images/Background/10.jpg');">
                                <div class="modal-header">
                                    <h5 class="modal-title pull-left" style="color: #4dd9d5;">
                                        <img class="d-inline-block rounded-circle border" style="width: 3rem;height: 3rem"
                                            src="/static/Images/App/Sball.gif">
                                        链接评论编辑
                                    </h5>
                                    <button type="button" class="btn btn-link btn-light rounded"
                                        data-dismiss="modal">关闭</button>
                                </div>
                                <div class="modal-body">
                                    <div class="d-flex align-content-center">
                                        <label class="text-cyan font-weight-bold text-right"
                                            style="width:80px;flex-shrink: 0;margin-right: 1rem;font-size: 14px">当前链接</label>
                                        <div class="rounded w-100 text-center" id="current_url"
                                            style="background-color: #00419d;box-shadow: 0 0 15px #00deff inset;line-height: 26px;">

                                        </div>
                                    </div>
                                    <div class="d-flex align-content-center my-3">
                                        <label class="text-cyan font-weight-bold text-right"
                                            style="width:80px;flex-shrink: 0;margin-right: 1rem;font-size: 14px">服务说明</label>
                                        <div class="rounded w-100 text-center"
                                            style="background-color: #00419d;box-shadow: 0 0 15px #00deff inset;line-height: 26px;">
                                            评论条数限定<span id="comment_condition"></span>（min/max）
                                        </div>
                                    </div>
                                    <div class="d-flex align-content-center">
                                        <label for="FormControlTextarea" class="text-cyan font-weight-bold text-right"
                                            style="width:80px;flex-shrink: 0;margin-right: 1rem;font-size: 14px">评论编辑区</label>
                                        <div class="rounded w-100 ">
                                            <textarea spellcheck="false" class="form-control bg-white text-black px-2"
                                                id="FormControlTextarea" rows="10" style="font-size: 14px;"></textarea>
                                        </div>
                                    </div>
                                    <h5 class="text-right mt-3">评论总数：<span id="comment_count">0</span>条</h5>
                                    <hr>
                                    <table id="comment_table" class="table-no-bordered striped mb-0"></table>
                                </div>
                                <div class="modal-footer justify-content-center mt-3">
                                    <button type="button" class="btn btn-link btn-light mx-5" id="submit_comment">加入购物车</button>
                                    <button type="button" class="btn btn-link btn-light mx-5" data-dismiss="modal">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/fileinput.min.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/zh.js"></script>
    <script src="/static/JavaScript/bootstrap-fileinput/theme.min.js"></script>
    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>
    <!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
    <!-- 加载 Select2 -->
    <script src="/static/JavaScript/select2/select2.full.min.js"></script>
    <script src="/static/JavaScript/select2/zh-CN.js"></script>
    <!--     dialog   -->
    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>
    <!-- data-table CSS ============================================ -->
    <script src="/static/JavaScript/data-table/bootstrap-table.js"></script>
    <script src="/static/JavaScript/data-table/bootstrap-table-zh-CN.js"></script>

    <!-- App functions and dialog -->
    <script src="/static/JavaScript/App/clamp.js"></script>
    <script src="/static/JavaScript/App/trumbowyg.min.js"></script>

    <!-- App functions and notify -->
    <script src="/static/JavaScript/App/bootstrap-notify.min.js"></script>

    <!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>

    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <!-- <script src="/static/JavaScript/Config/Server_Tools/Server_Print.js"></script>
    <script src="/static/JavaScript/Config/Server_Tools/Server_Href.js"></script>
    <script src="/static/JavaScript/Config/Server_Function/Server_Data.js"></script>
    <script src="/static/JavaScript/Config/System_BaseConfig.js"></script> -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>

    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/App/jquery.dataTables.min.js"></script>
    <script src="/static/JavaScript/App/dataTables.buttons.min.js"></script>
    <script src="/static/JavaScript/App/buttons.print.min.js"></script>
    <script src="/static/JavaScript/App/jszip.min.js"></script>
    <script src="/static/JavaScript/App/buttons.html5.min.js"></script>
    <script src="/static/JavaScript/decimal/decimal.min.js"></script>
    <script src="/static/JavaScript/dompurify/purify.min.js"></script>

    <!-- Visualization -->
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <!-- QRcode -->
    <script src="/static/JavaScript/QRcode/qrcode.min.js"></script>
    <script src="/static/Page/Market/Page_Home_Sales.js"></script>
    <script>
		var User_Token = window.location.href.split("&")[1] || localStorage.getItem('User_Token');
        console.log('User_Token:',User_Token)
    </script>
    <script>
        window.onload = function () {
            Element_Sidebar_Header();
            Page_Init();
            Get_User_CreditPoints();
        }
    </script>
</body>


</html>