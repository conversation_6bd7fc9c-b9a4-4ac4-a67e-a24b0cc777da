# -*- coding: utf-8 -*-
import os,sys,time,psutil,ctypes,random,qtawesome,threading,requests,configparser,subprocess,pathlib,functools
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QTime, Qt, Slot
import qtawesome as qta
from qtawesome import icon
# ---------------------------------------------------------------------------------------- 获取系统初始化数据
from Bin.Utils.UtilsCenter import *
import numpy as np

Page_Info={
    "Title":"哨兵视频监控主页",
    "Param": {},

}

from Bin.System.OS.Component import Component_Common

PP(Page_Info)


# ---------------------------------------------------------------------------------------- Page_Widget_Home
class Page_Widget_Media_VideoHome(QtWidgets.QWidget):
    Signal_Result = QtCore.Signal(dict)
    def __init__(self, *args, parent=None):
        super().__init__()
        global Page_Info
        try:Page_Info.update(args[0])
        except:pass

        Page_Info["Page_Element_List"]={}
        Page_Info["StyleSheet"]={}
        Page_Info["StyleSheet"]["Value_Color"] ="rgba(0, 255, 136, 255)"

        self.initUI()


    def initUI(self):
        self.Set_Content()


    def Set_Content(self):
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setContentsMargins(3, 3, 3, 3)

        self.QSplitter_Root = QtWidgets.QSplitter(Qt.Vertical)
        # 上部水平分割器（左 + 右）
        self.QSplitter_HBox= QtWidgets.QSplitter(Qt.Horizontal)

        self.QLabel_Right = QtWidgets.QLabel()
        self.QLabel_Right.setStyleSheet("background:#000;color:#fff;")
        self.QLabel_Right.setMinimumWidth(300)  # 确保最小宽度 100px

        # 添加到水平分割器
        self.QSplitter_HBox.addWidget(self.Set_Left())
        self.QSplitter_HBox.addWidget(self.Set_Right())
        self.QSplitter_HBox.setSizes([self.width(), 0])  # 初始右侧宽度 0

        # 添加到垂直分割器
        self.QSplitter_Root.addWidget(self.QSplitter_HBox)
        self.QSplitter_Root.addWidget(self.Set_Bottom())
        self.QSplitter_Root.setSizes([300, 0])  # 初始底部高度 0

        # 主布局
        __QVBoxLayout.addWidget(self.QSplitter_Root)

        # 动画（右侧）
        self.QPropertyAnimation_Right = QtCore.QPropertyAnimation(self.QSplitter_HBox, b"sizes")
        self.right_expanded = False

        # 动画（底部）
        self.QPropertyAnimation_Bottom = QtCore.QPropertyAnimation(self.QSplitter_Root, b"sizes")
        self.bottom_expanded = False

        self.Toggle_Right()
        self.Toggle_Bottom()

    def Set_Left(self):
        # 左侧区域
        self.QLabel_Left = QLabel_Video_Home(self)
        self.QLabel_Left.setStyleSheet("background:rgba(18, 27, 53, 255)")
        self.QHBoxLayout_Left = QtWidgets.QHBoxLayout(self.QLabel_Left.QLabel_Content)
        self.QHBoxLayout_Left.setSpacing(8)
        self.QHBoxLayout_Left.setContentsMargins(8, 20, 8, 20)

        # 创建视频监控网格
        self.Set_Video_Grid()

        return self.QLabel_Left

    def Set_Video_Grid(self):
        """设置视频监控网格"""
        QGridLayout_Content = QtWidgets.QGridLayout()
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(0, 0, 0, 0)

        # 创建2x2的视频通道网格
        for row in range(2):
            for col in range(2):
                channel_id = row * 2 + col + 1
                channel_widget = self.Create_Video_Channel(channel_id)
                QGridLayout_Content.addWidget(channel_widget, row, col)

        self.QHBoxLayout_Left.addLayout(QGridLayout_Content)

    def Create_Video_Channel(self, channel_id):
        """创建视频通道窗口"""
        QLabel_Info = {
            "Title_Name": f"通道 {channel_id}",
            "Channel_ID": channel_id
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(8)
        QVBoxLayout_Content.setContentsMargins(8, 20, 8, 20)

        # 视频显示区域
        video_area = QtWidgets.QLabel("视频显示区域")
        video_area.setAlignment(QtCore.Qt.AlignCenter)
        video_area.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.5);
                border: 1px dashed rgba(0, 180, 255, 50);
                border-radius: 2px;
                color: rgba(255, 255, 255, 0.7);
                min-height: 120px;
            }
        """)
        QVBoxLayout_Content.addWidget(video_area)

        # 状态信息
        status_label = QtWidgets.QLabel("在线 | 1920x1080")
        status_label.setAlignment(QtCore.Qt.AlignCenter)
        status_label.setStyleSheet("color: #00ff88; font-size: 10px; background: transparent; border: none;")
        QVBoxLayout_Content.addWidget(status_label)

        return __QLabel

    def Set_Right(self):
        # 右侧区域
        self.QLabel_Right = QLabel_Function({"Title_Name":"系统状态监控"})
        self.QLabel_Right.setStyleSheet("background:rgba(18, 27, 53, 255)")
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(self.QLabel_Right.QLabel_Content)
        QVBoxLayout_Content.setSpacing(8)
        QVBoxLayout_Content.setContentsMargins(8, 20, 8, 20)

        # 系统状态信息
        self.Set_System_Status(QVBoxLayout_Content)

        return self.QLabel_Right

    def Set_System_Status(self, layout):
        """设置系统状态显示"""
        # 时间显示
        self.QLabel_Show_Time = QtWidgets.QLabel()
        self.QLabel_Show_Time.setStyleSheet("color: white; font-size: 15px; background: transparent; padding: 10px;")
        self.QLabel_Show_Time.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Show_Time.setText(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))
        layout.addWidget(self.QLabel_Show_Time)

        # CPU使用率显示
        self.QLabel_CPU = QtWidgets.QLabel()
        self.QLabel_CPU.setFixedHeight(60)
        self.QLabel_CPU.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_CPU.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        self.QLabel_CPU.setText('''<div style="text-align:center;">
                                 <span style="font-size:13pt;color:rgba(0, 255, 136, 255);font-weight: bold">15%</span><br>
                                 <span style="font-size:8pt;color:#1E90FF;font-weight: bold">CPU使用率</span>
                                 </div>''')
        layout.addWidget(self.QLabel_CPU)

        # 内存使用率显示
        self.QLabel_Memory = QtWidgets.QLabel()
        self.QLabel_Memory.setFixedHeight(60)
        self.QLabel_Memory.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Memory.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 52, 80, 0.3);
                border: 1px solid rgba(0, 180, 255, 60);
                border-radius: 4px;
                padding: 5px;
            }
        """)
        self.QLabel_Memory.setText('''<div style="text-align:center;">
                                   <span style="font-size:13pt;color:rgba(0, 255, 136, 255);font-weight: bold">45%</span><br>
                                   <span style="font-size:8pt;color:#1E90FF;font-weight: bold">内存使用率</span>
                                   </div>''')
        layout.addWidget(self.QLabel_Memory)

        # 设置定时器
        self.QTimer_Count = QtCore.QTimer(self)
        self.QTimer_Count.timeout.connect(self.Page_Update_Timer_Date)
        self.QTimer_Count.start(1000)  # 每秒更新一次

    def Set_Bottom(self):
        # 底部区域
        self.QLabel_Bottom = QtWidgets.QLabel()
        self.QLabel_Bottom.setStyleSheet("background:transparent;")
        self.QLabel_Bottom.setMinimumHeight(400)
        QGridLayout_Content = QtWidgets.QGridLayout(self.QLabel_Bottom)
        QGridLayout_Content.setSpacing(8)
        QGridLayout_Content.setContentsMargins(0,0, 0, 8)

        # 功能模块数据
        Function_List = {
            "1": {"Function_Text": "今日告警", "Function_Value": "3次"},
            "2": {"Function_Text": "设备在线", "Function_Value": "23台"},
            "3": {"Function_Text": "通道活跃", "Function_Value": "16路"},
            "4": {"Function_Text": "存储使用", "Function_Value": "68%"},
            "5": {"Function_Text": "网络状态", "Function_Value": "正常"},
            "6": {"Function_Text": "系统负载", "Function_Value": "低"},
            "7": {"Function_Text": "录像状态", "Function_Value": "正常"},
            "8": {"Function_Text": "报警处理", "Function_Value": "及时"},
        }

        Page_Info["Page_Element_List"]["Function_QLabel_List"]      = {}
        Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"] = {}

        Count = 1
        for Row in range(2):
            for Col in range(4):
                _QLabel =  QtWidgets.QLabel()
                _QHBoxLayout = QtWidgets.QHBoxLayout(_QLabel)
                _QHBoxLayout.setSpacing(0)
                _QHBoxLayout.setContentsMargins(0, 0, 0, 0)
                Page_Info["Page_Element_List"]["Function_QLabel_List"][f"{Count}"]      =  _QLabel
                Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"][f"{Count}"] =  _QHBoxLayout
                QGridLayout_Content.addWidget(_QLabel, Row, Col)
                Count += 1

        Function_QHBoxLayout_List={}
        Function_QHBoxLayout_List["1"] = self.Set_Function_Module("今日告警", "3次")
        Function_QHBoxLayout_List["2"] = self.Set_Function_Module("设备在线", "23台")
        Function_QHBoxLayout_List["3"] = self.Set_Function_Module("通道活跃", "16路")
        Function_QHBoxLayout_List["4"] = self.Set_Function_Module("存储使用", "68%")
        Function_QHBoxLayout_List["5"] = self.Set_Function_Module("网络状态", "正常")
        Function_QHBoxLayout_List["6"] = self.Set_Function_Module("系统负载", "低")
        Function_QHBoxLayout_List["7"] = self.Set_Function_Module("录像状态", "正常")
        Function_QHBoxLayout_List["8"] = self.Set_Function_Module("报警处理", "及时")

        for Name, _QHBoxLayout in Page_Info["Page_Element_List"]["Function_QHBoxLayout_List"].items():
            _QHBoxLayout.addWidget( Function_QHBoxLayout_List[Name])

        return self.QLabel_Bottom

    def Set_Function_Module(self, title, value):
        QLabel_Info = {
            "Title_Name": title
        }
        __QLabel = QLabel_Function(QLabel_Info)
        QVBoxLayout_Content = QtWidgets.QVBoxLayout(__QLabel.QLabel_Content)
        QVBoxLayout_Content.setSpacing(8)
        QVBoxLayout_Content.setContentsMargins(8, 20, 8, 20)

        # 数值显示
        value_label = QtWidgets.QLabel()
        value_label.setAlignment(QtCore.Qt.AlignCenter)
        value_label.setStyleSheet("color: rgba(0, 255, 136, 255); font-size: 18px; font-weight: bold; background: transparent;")
        value_label.setText(value)
        QVBoxLayout_Content.addWidget(value_label)

        return __QLabel

    def Toggle_Right(self):
        total_width = self.QSplitter_HBox.width()
        if self.right_expanded:
            # 收起右侧
            self.QPropertyAnimation_Right.setStartValue([total_width - 100, 100])
            self.QPropertyAnimation_Right.setEndValue([total_width, 0])
            self.QPropertyAnimation_Right.finished.connect(lambda: self.QLabel_Left.hide())
            self.QSplitter_HBox.setSizes([self.width(), 0])  # 初始右侧宽度 0
            self.right_expanded = False
        else:
            # 展开右侧
            self.QLabel_Left.show()
            self.QPropertyAnimation_Right.setStartValue([total_width, 0])
            self.QPropertyAnimation_Right.setEndValue([total_width - 100, 100])
            self.QSplitter_HBox.setSizes([self.width(), 300])  # 初始右侧宽度 0
            self.right_expanded = True

        self.QPropertyAnimation_Right.start()

    def Toggle_Bottom(self):
        total_height = self.QSplitter_Root.height()
        if self.bottom_expanded:
            # 收起底部
            self.QPropertyAnimation_Bottom.setStartValue([total_height - 100, 100])
            self.QPropertyAnimation_Bottom.setEndValue([total_height, 0])
            self.QPropertyAnimation_Bottom.finished.connect(lambda: self.QLabel_Bottom.hide())
            self.QPropertyAnimation_Bottom.setSizes([self.height(), 0])
            self.bottom_expanded = False
        else:
            # 展开底部
            self.QLabel_Bottom.show()
            self.QPropertyAnimation_Bottom.setStartValue([total_height, 0])
            self.QPropertyAnimation_Bottom.setEndValue([total_height - 100, 100])
            self.QSplitter_Root.setSizes([self.height(), 400])
            self.bottom_expanded = True

        self.QPropertyAnimation_Bottom.start()

    def Page_Update_Timer_Date(self):
        """更新时间显示"""
        current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        self.QLabel_Show_Time.setText(current_time)

    def Page_Update(self):
       pass

    def PAGE_HANDLER_EXECUTE(self, CommandParameter):
        if "Self_" in CommandParameter["Type"]:
            PP(CommandParameter)
            self.Open_Processing(CommandParameter)
        else:
            self.Signal_Result.emit(CommandParameter)  # 发送信号并传递数据

    def Open_Processing(self,ProcessingParameter):
        PP(ProcessingParameter)
        exec(f"QLabel_Component= Component_{ProcessingParameter['Processing']}.Component_{ProcessingParameter['Processing']}(self,ProcessingParameter)")
        exec("QLabel_Component.Signal_Result.connect(self.PAGE_HANDLER_EXECUTE)")
        exec("QLabel_Component.move(self.geometry().center() - QLabel_Component.rect().center())")
        exec("QLabel_Component.show()")


class QLabel_Function(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, *args, parent=None):
        super().__init__(parent)
        try:self.QLabel_Info = args[0]
        except:pass

        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 0, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")

        Icon_List = {
            "系统状态监控": "monitor.png",
            "通道": "video_channel.png",
            "今日告警": "alarm.png",
            "设备在线": "device_online.png",
            "通道活跃": "channel_active.png",
            "存储使用": "storage.png",
            "网络状态": "network.png",
            "系统负载": "system_load.png",
            "录像状态": "recording.png",
            "报警处理": "alarm_handle.png",
        }

        Set_Icon = lambda path: QLabel_Icon.setPixmap(
            QtGui.QPixmap(path).scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        Title_Name = self.QLabel_Info.get("Title_Name", "")
        Matched_Icon = next((icon for key, icon in Icon_List.items() if key in Title_Name), None)
        Set_Icon(rf"D:\Sentinel Foundation\Bin\Resource\Icons\{Matched_Icon}" if Matched_Icon
                 else r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")

        QLabel_Title_Name.setText(self.QLabel_Info["Title_Name"])

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 1)
        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")

        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)


class QLabel_Video_Home(QtWidgets.QLabel):
    clicked = QtCore.Signal()

    def __init__(self, parent=None, *args):
        super().__init__(parent)
        self.QWidget_Parent = parent
        try:
            self.Channel_Info = args[0]
            self.Control_Info = {
                "Channel_ID": self.Channel_Info.get("Channel_ID", ""),
            }
        except:
            pass
        self.initUI()

    def initUI(self):
        self.setStyleSheet('''QLabel{background-color: rgba(18, 27, 53, 255);border: 1px solid rgba(0, 180, 255, 60);border-radius: 4px;}''')
        __QVBoxLayout = QtWidgets.QVBoxLayout(self)
        __QVBoxLayout.setSpacing(0)
        __QVBoxLayout.setContentsMargins(0, 0, 0, 0)

        QLabel_Title = QtWidgets.QLabel()
        QLabel_Title.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        QLabel_Title.setMinimumHeight(30)
        QLabel_Title.setMaximumHeight(30)

        QHBoxLayout_Title = QtWidgets.QHBoxLayout(QLabel_Title)
        QHBoxLayout_Title.setSpacing(0)
        QHBoxLayout_Title.setContentsMargins(13, 3, 8, 0)

        QLabel_Icon = QtWidgets.QLabel()
        QLabel_Icon.setFixedSize(28, 30)
        QLabel_Icon.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        QLabel_Icon.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px ; border: 0px; }")
        Pixmap_Logo = QtGui.QPixmap(r"D:\Sentinel Foundation\Bin\Resource\Image\Logo.png")
        Image_Logo = Pixmap_Logo.scaled(18, 18, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        QLabel_Icon.setPixmap(Image_Logo)

        QLabel_Title_Name = QtWidgets.QLabel()
        QLabel_Title_Name.setAlignment(QtCore.Qt.AlignLeft |QtCore.Qt.AlignVCenter)
        QLabel_Title_Name.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px;color:rgba(22, 175, 252, 255);font-weight: bold;font-size: 11px; }")
        QLabel_Title_Name.setText("视频监控主页")

        self.QLabel_Title_Menu = QtWidgets.QLabel()
        self.QLabel_Title_Menu.setVisible(False)
        QHBoxLayout_Menu = QtWidgets.QHBoxLayout(self.QLabel_Title_Menu)
        QHBoxLayout_Menu.setAlignment( QtCore.Qt.AlignRight)
        QHBoxLayout_Menu.setSpacing(8)
        QHBoxLayout_Menu.setContentsMargins(13,3, 14, 3)

        # 创建菜单按钮
        Menu_List=[
            {"Menu_Name":"右侧",  "Menu_Command":{"Command":"Toggle_Right"}},
            {"Menu_Name":"底部", "Menu_Command":{"Command":"Toggle_Bottom"}},
            {"Menu_Name":"刷新",  "Menu_Command":{"Command":"Refresh"}},
        ]

        StyleSheet_QLabel = """
            QLabel {
                background-color:rgba(40, 52, 80, 0.1);
                border: 0px solid rgba(0, 180, 255, 60);
                border-radius: 3px;
                padding: 0px;
                font-size:13px;
                font-weight: bold;
                color:white;
            }
            QLabel:hover {
                background-color: rgba(0, 100, 150, 150);
            }
        """

        def Set_Menu(Menu_Info):
            QLabel_Menu = Component_Common.Component_Common_QLabel_Click()
            QLabel_Menu.setFixedSize(38, 20)
            QLabel_Menu.setStyleSheet(StyleSheet_QLabel)
            QLabel_Menu.setText(Menu_Info["Menu_Name"][:1])  # 使用首字母作为图标
            QLabel_Menu.setAlignment(QtCore.Qt.AlignCenter)
            QLabel_Menu.clicked.connect(lambda :self.Menu_Command(Menu_Info["Menu_Command"]))
            return QLabel_Menu

        for Menu_Info in Menu_List:
            QHBoxLayout_Menu.addWidget(Set_Menu(Menu_Info))

        QHBoxLayout_Title.addWidget(QLabel_Icon, 0, alignment=QtCore.Qt.AlignHCenter | QtCore.Qt.AlignVCenter)
        QHBoxLayout_Title.addWidget(QLabel_Title_Name, 2)
        QHBoxLayout_Title.addStretch()
        QHBoxLayout_Title.addWidget(self.QLabel_Title_Menu,1)

        self.QLabel_Content = QtWidgets.QLabel()
        self.QLabel_Content.setStyleSheet("border: 0px")

        __QVBoxLayout.addWidget(QLabel_Title, )
        __QVBoxLayout.addWidget(self.QLabel_Content)
        __QVBoxLayout.addWidget(self.Set_Bottom())

    def Set_Bottom(self):
        __QLabel = QtWidgets.QLabel()
        __QLabel.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        __QLabel.setMinimumHeight(30)
        __QLabel.setMaximumHeight(30)
        __QHBoxLayout = QtWidgets.QHBoxLayout(__QLabel)
        __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)

        self.QLabel_Status = QtWidgets.QLabel()
        self.QLabel_Status.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status.hide()

        self.QHBoxLayout_Status = QtWidgets.QHBoxLayout(self.QLabel_Status)
        self.QHBoxLayout_Status.setAlignment(QtCore.Qt.AlignLeft)
        self.QHBoxLayout_Status.setSpacing(0)
        self.QHBoxLayout_Status.setContentsMargins(8, 0, 0, 0)

        self.QLabel_Status_Source = QtWidgets.QLabel()
        self.QLabel_Status_Source.setStyleSheet("QLabel {background-color: transparent; padding: 0px; margin: 0px; border: 0px; }")
        self.QLabel_Status_Source.setAlignment(QtCore.Qt.AlignCenter)
        self.QLabel_Status_Source.setText(
            f'''<div style="text-align:center;">
            <span style="font-size:8pt;color:#1E90FF;font-weight: bold">状态：系统正常运行</span>
            </div>'''
        )
        self.QHBoxLayout_Status.addWidget(self.QLabel_Status_Source)

        __QHBoxLayout.addWidget(self.QLabel_Status)
        return __QLabel

    def Menu_Command(self,Command):
        PP(("Menu_Command",Command))
        self.QWidget_Parent.Toggle_Right()
        self.QWidget_Parent.Toggle_Bottom()

    # 鼠标事件
    def enterEvent(self, event):
        self.QLabel_Title_Menu.setVisible(True)
        self.QLabel_Status.setVisible(True)
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.QLabel_Title_Menu.setVisible(False)
        self.QLabel_Status.setVisible(False)
        super().leaveEvent(event)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    Page_Widget = Page_Widget_Media_VideoHome()
    Page_Widget.show()
    sys.exit(app.exec())
