import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Button, 
  Table, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Upload, 
  message,
  Space,
  Popconfirm
} from 'antd';
import { PlusOutlined, ReloadOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { Service_Requests } from '@/Core/Core_Control';
import '../Styles/Page_Platform_Config.css';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// 数据类型定义
interface KeywordData {
  id: string;
  name: string;
  category: string;
  keywords: string;
  featureWords?: string;
  stopWords?: string;
}

interface AlarmData {
  id: string;
  name: string;
  type: string[];
  content: string;
  formula?: string;
}

interface AccountData {
  id: string;
  type: string;
  platform: string;
  name: string;
  url: string;
}

interface SourceData {
  id: string;
  type: string;
  name: string;
  url: string;
}

interface UserData {
  id: string;
  loginAccount: string;
  password: string;
  nickname: string;
  unit: string;
  avatar?: string;
}

const Page_Platform_Config: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('keyword');
  
  // 数据状态
  const [keywordData, setKeywordData] = useState<KeywordData[]>([]);
  const [alarmData, setAlarmData] = useState<AlarmData[]>([]);
  const [accountData, setAccountData] = useState<AccountData[]>([]);
  const [sourceData, setSourceData] = useState<SourceData[]>([]);
  const [userData, setUserData] = useState<UserData[]>([]);

  // 模态框状态
  const [keywordModalVisible, setKeywordModalVisible] = useState(false);
  const [alarmModalVisible, setAlarmModalVisible] = useState(false);
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [sourceModalVisible, setSourceModalVisible] = useState(false);
  const [userModalVisible, setUserModalVisible] = useState(false);

  // 表单实例
  const [keywordForm] = Form.useForm();
  const [alarmForm] = Form.useForm();
  const [accountForm] = Form.useForm();
  const [sourceForm] = Form.useForm();
  const [userForm] = Form.useForm();

  // 编辑状态
  const [editingRecord, setEditingRecord] = useState<any>(null);

  // 初始化数据
  useEffect(() => {
    loadKeywordData();
  }, []);

  // 加载方案数据
  const loadKeywordData = async () => {
    setLoading(true);
    try {
      // 这里可以调用实际的API
      // const serviceRequests = new Service_Requests();
      // const requestData = {
      //   user_id: 'Platform',
      //   user_token: localStorage.getItem('User_Token'),
      //   data_class: 'Sentinel',
      //   data_type: 'Service_Manage',
      //   data_methods: 'get_keyword_list',
      //   data_argument: '{}',
      //   data_kwargs: '{}'
      // };
      // const response = await serviceRequests.Async(requestData);
      // setKeywordData(response.data || []);

      // 模拟数据
      setKeywordData([
        {
          id: '1',
          name: '时事新闻监控',
          category: '时事新闻',
          keywords: '新闻,时事,热点',
          featureWords: '重要,关注',
          stopWords: '广告,推广'
        }
      ]);
    } catch (error) {
      message.error('加载方案数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载预警数据
  const loadAlarmData = async () => {
    setLoading(true);
    try {
      setAlarmData([
        {
          id: '1',
          name: '重要事件预警',
          type: ['平台预警', 'APP预警'],
          content: '发现重要事件：{title}',
          formula: '{title} - 标题\n{content} - 内容\n{time} - 时间'
        }
      ]);
    } catch (error) {
      message.error('加载预警数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载账号数据
  const loadAccountData = async () => {
    setLoading(true);
    try {
      setAccountData([
        {
          id: '1',
          type: '敌对账号',
          platform: '抖音',
          name: '测试账号',
          url: 'https://example.com'
        }
      ]);
    } catch (error) {
      message.error('加载账号数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载数源数据
  const loadSourceData = async () => {
    setLoading(true);
    try {
      setSourceData([
        {
          id: '1',
          type: '敌对网站',
          name: '测试数源',
          url: 'https://example.com'
        }
      ]);
    } catch (error) {
      message.error('加载数源数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载用户数据
  const loadUserData = async () => {
    setLoading(true);
    try {
      setUserData([
        {
          id: '1',
          loginAccount: 'admin',
          password: '******',
          nickname: '管理员',
          unit: '系统管理部'
        }
      ]);
    } catch (error) {
      message.error('加载用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    switch (key) {
      case 'alarm':
        loadAlarmData();
        break;
      case 'account':
        loadAccountData();
        break;
      case 'source':
        loadSourceData();
        break;
      case 'user':
        loadUserData();
        break;
      default:
        break;
    }
  };

  // 方案设置相关
  const keywordColumns = [
    {
      title: '方案名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '所属分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      key: 'keywords',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: KeywordData) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditKeyword(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个方案吗？"
            onConfirm={() => handleDeleteKeyword(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleEditKeyword = (record: KeywordData) => {
    setEditingRecord(record);
    keywordForm.setFieldsValue(record);
    setKeywordModalVisible(true);
  };

  const handleDeleteKeyword = async (id: string) => {
    try {
      setKeywordData(prev => prev.filter(item => item.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleKeywordSubmit = async () => {
    try {
      const values = await keywordForm.validateFields();
      if (editingRecord) {
        setKeywordData(prev => 
          prev.map(item => 
            item.id === editingRecord.id ? { ...item, ...values } : item
          )
        );
        message.success('更新成功');
      } else {
        const newRecord = {
          id: Date.now().toString(),
          ...values
        };
        setKeywordData(prev => [...prev, newRecord]);
        message.success('新增成功');
      }
      setKeywordModalVisible(false);
      keywordForm.resetFields();
      setEditingRecord(null);
    } catch (error) {
      message.error('保存失败');
    }
  };

  return (
    <div className="platform-config-page">
      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="方案设置" key="keyword">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadKeywordData}
                  loading={loading}
                >
                  方案刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingRecord(null);
                    keywordForm.resetFields();
                    setKeywordModalVisible(true);
                  }}
                >
                  新增方案
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={keywordColumns}
              dataSource={keywordData}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="预警设置" key="alarm">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAlarmData}
                  loading={loading}
                >
                  预警刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingRecord(null);
                    alarmForm.resetFields();
                    setAlarmModalVisible(true);
                  }}
                >
                  新增预警
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '预警名称', dataIndex: 'name', key: 'name' },
                { title: '预警方式', dataIndex: 'type', key: 'type', render: (types: string[]) => types.join(', ') },
                { title: '预警内容', dataIndex: 'content', key: 'content', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  render: (_: any, record: AlarmData) => (
                    <Space size="middle">
                      <Button type="link" icon={<EditOutlined />}>编辑</Button>
                      <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={alarmData}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="账号管理" key="account">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAccountData}
                  loading={loading}
                >
                  账号刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingRecord(null);
                    accountForm.resetFields();
                    setAccountModalVisible(true);
                  }}
                >
                  新增账号
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '账号类型', dataIndex: 'type', key: 'type' },
                { title: '账号平台', dataIndex: 'platform', key: 'platform' },
                { title: '账号名称', dataIndex: 'name', key: 'name' },
                { title: '账号主页', dataIndex: 'url', key: 'url', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  render: (_: any, record: AccountData) => (
                    <Space size="middle">
                      <Button type="link" icon={<EditOutlined />}>编辑</Button>
                      <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={accountData}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="数源管理" key="source">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadSourceData}
                  loading={loading}
                >
                  数源刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingRecord(null);
                    sourceForm.resetFields();
                    setSourceModalVisible(true);
                  }}
                >
                  新增数源
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '数源类型', dataIndex: 'type', key: 'type' },
                { title: '数源名称', dataIndex: 'name', key: 'name' },
                { title: '数源主页', dataIndex: 'url', key: 'url', ellipsis: true },
                {
                  title: '操作',
                  key: 'action',
                  render: (_: any, record: SourceData) => (
                    <Space size="middle">
                      <Button type="link" icon={<EditOutlined />}>编辑</Button>
                      <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={sourceData}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="用户管理" key="user">
            <div className="tab-header">
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadUserData}
                  loading={loading}
                >
                  用户刷新
                </Button>
                <Button type="primary">
                  提交设置
                </Button>
              </Space>
            </div>
            <Table
              columns={[
                { title: '登录账号', dataIndex: 'loginAccount', key: 'loginAccount' },
                { title: '用户昵称', dataIndex: 'nickname', key: 'nickname' },
                { title: '用户单位', dataIndex: 'unit', key: 'unit' },
                {
                  title: '操作',
                  key: 'action',
                  render: (_: any, record: UserData) => (
                    <Space size="middle">
                      <Button type="link" icon={<EditOutlined />}>编辑</Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={userData}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 方案编辑模态框 */}
      <Modal
        title={editingRecord ? "方案编辑" : "新增方案"}
        open={keywordModalVisible}
        onOk={handleKeywordSubmit}
        onCancel={() => {
          setKeywordModalVisible(false);
          keywordForm.resetFields();
          setEditingRecord(null);
        }}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={keywordForm} layout="vertical">
          <Form.Item
            label="方案名称"
            name="name"
            rules={[{ required: true, message: '请输入方案名称' }]}
          >
            <Input placeholder="请输入方案名称" />
          </Form.Item>
          <Form.Item
            label="所属分类"
            name="category"
            rules={[{ required: true, message: '请选择所属分类' }]}
          >
            <Select placeholder="请选择所属分类">
              <Option value="时事新闻">时事新闻</Option>
              <Option value="聚焦热点">聚焦热点</Option>
              <Option value="涉政信息">涉政信息</Option>
              <Option value="涉邪消息">涉邪消息</Option>
              <Option value="反动言论">反动言论</Option>
              <Option value="重点人物">重点人物</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="关键词"
            name="keywords"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入关键词，多个关键词使用中英文逗号','隔开。"
            />
          </Form.Item>
          <Form.Item
            label="特征词"
            name="featureWords"
          >
            <TextArea
              rows={3}
              placeholder="请输入特征词，多个特征词使用中英文逗号','隔开，特征词与关键词是与关系，特征词之间是或关系。"
            />
          </Form.Item>
          <Form.Item
            label="停用词"
            name="stopWords"
          >
            <TextArea
              rows={3}
              placeholder="请输入停用词，多个停用词使用中英文逗号','隔开，停用词与关键词是与关系，停用词之间是或关系。"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 预警编辑模态框 */}
      <Modal
        title={editingRecord ? "预警编辑" : "新增预警"}
        open={alarmModalVisible}
        onOk={() => setAlarmModalVisible(false)}
        onCancel={() => setAlarmModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={alarmForm} layout="vertical">
          <Form.Item
            label="预警名称"
            name="name"
            rules={[{ required: true, message: '请输入预警名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="预警方式"
            name="type"
            rules={[{ required: true, message: '请选择预警方式' }]}
          >
            <Select mode="multiple" placeholder="请选择预警方式">
              <Option value="平台预警">平台预警</Option>
              <Option value="APP预警">APP预警</Option>
              <Option value="微信公众号" disabled>微信公众号</Option>
              <Option value="钉钉预警" disabled>钉钉预警</Option>
              <Option value="短信预警" disabled>短信预警</Option>
              <Option value="邮箱预警" disabled>邮箱预警</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="预警内容"
            name="content"
            rules={[{ required: true, message: '请输入预警内容' }]}
          >
            <TextArea rows={3} placeholder="请参照下方的公式 设置预警内容" />
          </Form.Item>
          <Form.Item
            label="公式详解"
            name="formula"
          >
            <TextArea
              rows={5}
              value="{title} - 标题&#10;{content} - 内容&#10;{time} - 时间&#10;{platform} - 平台&#10;{author} - 作者"
              disabled
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 账号编辑模态框 */}
      <Modal
        title={editingRecord ? "账号编辑" : "新增账号"}
        open={accountModalVisible}
        onOk={() => setAccountModalVisible(false)}
        onCancel={() => setAccountModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={accountForm} layout="vertical">
          <Form.Item
            label="账号类型"
            name="type"
            rules={[{ required: true, message: '请选择账号类型' }]}
          >
            <Select>
              <Option value="敌对账号">敌对账号</Option>
              <Option value="仇视言论">仇视言论</Option>
              <Option value="谣言转发">谣言转发</Option>
              <Option value="意见领袖">意见领袖</Option>
              <Option value="政治宣传">政治宣传</Option>
              <Option value="网络水军">网络水军</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="账号平台"
            name="platform"
            rules={[{ required: true, message: '请选择账号平台' }]}
          >
            <Select>
              <Option value="抖音">抖音</Option>
              <Option value="快手">快手</Option>
              <Option value="哔哩哔哩">哔哩哔哩</Option>
              <Option value="知乎">知乎</Option>
              <Option value="微博">微博</Option>
              <Option value="小红书">小红书</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="账号名称"
            name="name"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="账号主页"
            name="url"
            rules={[{ required: true, message: '请输入账号主页' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 数源编辑模态框 */}
      <Modal
        title={editingRecord ? "数源编辑" : "新增数源"}
        open={sourceModalVisible}
        onOk={() => setSourceModalVisible(false)}
        onCancel={() => setSourceModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={sourceForm} layout="vertical">
          <Form.Item
            label="数源类型"
            name="type"
            rules={[{ required: true, message: '请选择数源类型' }]}
          >
            <Select>
              <Option value="敌对网站">敌对网站</Option>
              <Option value="仇视言论">仇视言论</Option>
              <Option value="谣言转发">谣言转发</Option>
              <Option value="意见领袖">意见领袖</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="数源名称"
            name="name"
            rules={[{ required: true, message: '请输入数源名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="数源主页"
            name="url"
            rules={[{ required: true, message: '请输入数源主页' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户编辑模态框 */}
      <Modal
        title="用户编辑"
        open={userModalVisible}
        onOk={() => setUserModalVisible(false)}
        onCancel={() => setUserModalVisible(false)}
        okText="保存"
        cancelText="关闭"
      >
        <Form form={userForm} layout="vertical">
          <Form.Item label="用户头像自定义上传">
            <Upload
              name="file"
              accept=".jpg,.png,.jpeg"
              beforeUpload={() => false}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>
          <Form.Item
            label="登录账号"
            name="loginAccount"
            rules={[{ required: true, message: '请输入登录账号' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            label="登录密码"
            name="password"
            rules={[{ required: true, message: '请输入登录密码' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            label="用户昵称"
            name="nickname"
            rules={[{ required: true, message: '请输入用户昵称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="用户单位"
            name="unit"
            rules={[{ required: true, message: '请输入用户单位' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Page_Platform_Config;
