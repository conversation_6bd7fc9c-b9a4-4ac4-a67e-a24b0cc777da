function market_typeFormatter(row) {
    if (row['MARKET_ADDR'] === 'account') {
        return '账号采购'
    } else if (row['MARKET_ADDR'] === 'counter') {
        return '网络反制'
    } else if (row['MARKET_ADDR'] === 'analysis') {
        return '网络分析引擎'
    } else if (row['MARKET_ADDR'] === 'phone') {
        return '流量卡采购'
    } else {
        return '其他'
    }
}
var status_Emus = {
    'Finish': '已完成',
    'Active': '执行中',
    'Recharge': '已退款',
    'Failed': '异常中',
    'Execute': '执行中',
    'API': '执行中'
}
// 获取用户订单
function Query_Orders(selectedOptions_time = 'week', diy_time_list = []) {
    loaderShow()
    // const __Server_Data = new Server_Data('Service_Requests_Data', {
    //     'Data_Name': 'User_Query_Orders',
    //     'User_Token': User_Token,
    //     'diy_time_list': diy_time_list,
    //     'selectedOptions_time': selectedOptions_time
    // });
    // __Server_Data.async_run().then(res => {
    let Requests_Data = {
        "user_id": 'Market',
        "user_token":User_Token,
        'data_class': 'Market', 
        'data_type': 'Network', 
        'data_methods': 'order_check_list',
        "data_argument": `{}`,
        "data_kwargs":{
            'diy_time_list': diy_time_list,
            'selectedOptions_time': selectedOptions_time
        },
    };
    __Service_Requests = new Service_Requests("Async",Requests_Data);
    __Service_Requests.callMethod()
    .then(res => {
        // console.log(res);
        $table.clear().draw();
        if (res.length > 0) {
            res.forEach(e => {
                e.MARKET_CONTENT = escapeHtml(e.MARKET_CONTENT)
                e.MARKET_ADDR = market_typeFormatter(e)
                e.MARKET_STATUS = status_Emus[e.MARKET_STATUS]
            })
            $table.rows.add(res).draw();

        }
    }).finally(() => {
        loaderHide()
    })
}


// 日期选择
$('#selectedOptions_time').on('change', function () {
    // console.log($(this).val())
    if ($(this).val() == 'other') {
        $('#diy_time_list').parent().show()
        $('#clear_date').show()
    } else {
        $('#diy_time_list').parent().hide()
        $('#clear_date').hide()
        clear_date()
    }
})
$('#diy_time_list').dateRangePicker({
    language: 'cn',
    separator: ' 至 ',

}).bind('datepicker-apply', function (event, obj) {

    console.log(obj);
})

// 订单table
var $table = $('#table').DataTable({
    autoWidth: false,
    "columns": [
        { "title": "序号", width: '80px', "data": null, render: function (data, type, row, meta) { return meta.row + 1; } },
        { "title": "创建时间", "data": "Create_time" },
        { "title": "状态更新时间", "data": "MARKET_UPDATE" },
        { "title": "订单类型", "data": "MARKET_ADDR" },
        { "title": "订单内容", "data": "MARKET_CONTENT" },
        { "title": "订单状态", "data": "MARKET_STATUS" },
    ],
    "columnDefs": [
        { className: 'text-center', targets: '_all' },
        {
            "title": "操作",
            "targets": 6,
            "data": null,
            "defaultContent": "<button class='btn btn-primary btn-check' data-toggle=\"modal\" data-target=\"#modal-detail\">详情</button>"
        }
    ],
    "oLanguage": { //国际化配置
        "sProcessing": "正在获取数据，请稍后...",
        "sLengthMenu": "显示 _MENU_ 条",
        "sZeroRecords": "没有您要搜索的内容",
        "sInfo": "从 _START_ 到  _END_ 条记录 总记录数为 _TOTAL_ 条",
        "sInfoEmpty": "记录数为0",
        "sInfoFiltered": "(全部记录数 _MAX_ 条)",
        "sInfoPostFix": "",
        "sSearch": "搜索",
        "sUrl": "",
        "oPaginate": {
            "sFirst": "第一页",
            "sPrevious": "上一页",
            "sNext": "下一页",
            "sLast": "最后一页"
        }
    },
});



function clear_date() {
    $('#diy_time_list').val('')
}
// 查询
$('#query_table').on('click', function () {
    let diy_time_list = $('#diy_time_list').val().split(' 至 ')
    let selectedOptions_time = $('#selectedOptions_time').val()
    Query_Orders(selectedOptions_time, diy_time_list)
})

// 查看详情
$('#table tbody').on('click', '.btn-check', function () {
    let data = $table.row($(this).parents('tr')).data();
    $orderDetailTable.bootstrapTable('load', data['MARKET_DEAL_INFO'])
    $('[data-toggle="tooltip"]').tooltip()
});

// 订单详情table
var $orderDetailTable = $('#orderDetailTable').bootstrapTable({
    "scrollX": true,
    "columns": [
        { "title": "序号", width: 60, formatter: function (value, row, index) { return index + 1; } },
        { "title": "服务编号", "field": "ORDER_SID", width: 80 },
        { "title": "类型", "field": "NAME" },
        {
            "title": "链接", "field": "ORDER_URL", formatter: function (value, row, index) {
                return `<div class="text-truncate" data-toggle="tooltip" data-placement="top" title="${value}">${value}</div>`
            }
        },
        { "title": "数量", width: 60, "field": "ORDER_NUMBER" },
        { "title": "创建时间", "field": "ORDER_CREATE_TIME" },
        { "title": "开始计数", width: 60, "field": "ORDER_LAST_NUMBER" },
        { "title": "剩余计数", width: 60, "field": "ORDER_NOW_NUMBER" },
        { "title": "更新时间", "field": "ORDER_UPDATE" },
        {
            "title": "状态", width: 60, "field": "ORDER_STATUS", formatter: function (value, row, index) {
                return status_Emus[value]
            }
        },
        {
            field: 'actions',
            title: '操作',
            width: 80,
            formatter: function () {
                return `
                    <button class="btn btn-success edit-btn">查看</button>
                `
            },
            events: {
                'click .edit-btn': function (e, value, row, index) {
                    // console.log(row);
                    let html = detailFormatter(index, row)
                    $('#modal-account').modal('show')
                    let list = $('#modal-account .modal-body')
                    list.empty()
                    list.append(html)
                    // initializeClipboard()
                },
            }
        }
    ],
});
function ORDER_CONTENT_format(list) {
    let type = getType(list)
    if (type == 'Array') {
        if (list.length == 0) {
            return ''
        }
        let lis = list.map(e => e.CONTENT ? `<p>${e.CONTENT}</p>` : '').filter(e => e != '').join('')
        if (!lis) return ''
        return '<h4>评论内容</h4><div style="max-height:300px;overflow:auto">' + lis + '</div>'
    }
    if (type == 'Object') {
        return '<p>执行单号：' + (list.order_status || {}).order || '无' + '</p>'
    }

}
function detailFormatter(index, row) {
    let html = `
        <div class="border-light">
            
            <div>
                <p><b>服务编号：</b>${row.ORDER_SID} </p>
                <p><b>类型：</b> ${row.NAME}</p>
                <p><b>购买数量：</b> ${row.ORDER_NUMBER}</p>
                <p><b>链接：</b> ${row.ORDER_URL}</p>
                ${ORDER_CONTENT_format(row.ORDER_CONTENT)}
                <p class="mt-2"><b>状态：</b>${status_Emus[row.ORDER_STATUS]}</p>
                <p><b>更新时间：</b> ${row.ORDER_UPDATE}</p>
                
            </div>
        </div>
    `
    if (row.ORDER_TYPE == 'phone' && row.ORDER_CONTENT.length > 0) {
        html += `<p><b>快递公司：</b> ${row.ORDER_CONTENT[0].order_company}</p>
        <p><b>快递单号：</b> ${row.ORDER_CONTENT[0].order_no}</p>`
    }
    for (let item of row.ACCOUNT_LIST || []) {
        let temp = `
        <div class="d-flex justify-content-between align-items-center border-light" style="border-bottom: 1px solid">
            <div>
                <p><b>卡密信息：</b>${item} </p>
            </div>
            <div style="flex-shrink: 0;">
                <button type="button" class="btn btn-secondary rounded copy" onClick="copyEvent('${item}')" />复制</button>
                <button type="button" class="btn btn-light rounded" onClick="exportTxt('${item}')" />下载</button>    
            </div>
        </div>`
        html += temp
    }
    // 添加更多你需要在详情中展示的信息
    
    return html;
}

// function initializeClipboard() {
//     var clipboard = new ClipboardJS('.copy');
//     clipboard.on('success', function (e) {
//         console.info('Action:', e.action);
//         console.info('Text:', e.text);
//         console.info('Trigger:', e.trigger);

//         e.clearSelection();
//     });

//     clipboard.on('error', function (e) {
//         console.error('Action:', e.action);
//         console.error('Trigger:', e.trigger);
//     });

// }