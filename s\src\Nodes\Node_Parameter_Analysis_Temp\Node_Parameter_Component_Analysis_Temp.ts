import { ClassicPreset } from "rete";
import { Node_Socket,ParameterControl_Analysis_Temp} from "./Node_Parameter_Control_Analysis_Temp";

export class Node_Parameter_Component_Analysis_Temp extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| ParameterControl_Analysis_Temp}> 
  {
    width  = 388;
    height = 128;
    constructor(Label: string,) {
      super(Label);

      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const ConentControl = new ParameterControl_Analysis_Temp(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    