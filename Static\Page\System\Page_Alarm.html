<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
    <!-- select2 -->
    <link href="/static/CSS/select2/css/select2.min.css" rel="stylesheet" />
    <link href="/static/CSS/select2/css/select2-bootstrap4.css" rel="stylesheet" />
    <!-- table -->
    <link rel="stylesheet" href="/static/CSS/datatable/css/buttons.bootstrap4.min.css" />
    <link rel="stylesheet" href="/static/CSS/datatable/css/dataTables.bootstrap4.min.css" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
    <!-- notifications -->
    <link rel="stylesheet" href="/static/CSS/notifications/css/lobibox.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
</head>
<body class="bg-theme bg-theme1">
	<div class="wrapper">
		<div class="sidebar-wrapper" data-simplebar="true">
            <div class="sidebar-header">
                <div class="">
                    <img src="/static/Images/Logo.ico" class="logo-icon-2" alt="" />
                </div>
                <div>
                    <h4 class="logo-text" style="font-size:16px;">哨兵导控</h4>
                </div>
                <a href="javascript:;" class="toggle-btn ml-auto"> <i class="bx bx-menu"></i>
                </a>
            </div>
			<ul class="metismenu" id="Element_Sidebar"></ul>
		</div>
		<header class="top-header" id="Element_Header"></header>
		<div class="page-wrapper">
			<div class="page-content-wrapper">
				<div class="page-content">
                    <div class="card">
						<div class="card-body">
                            <div class="row">
                                <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">日期筛选:</label>
                                <div class="col-sm-2">
                                    <select class="single-select" id="Sentiment_Alarm_Time_Element">
                                        <option value="Day">今日</option>
                                        <option value="TwoDay">近两日</option>
                                        <option value="Mouth">本月</option>
                                        <option value="HalfYear">近半年</option>
                                        <option value="Year">近一年</option>
                                        <option value="All">所有</option>
                                        <option value="Other">自定义</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-info btn-sm m-1 px-4"  style="float: right;"  onclick="Requests_Sentiment_Alarm_Info()">筛选</button>
                            </div>
                            <div class="row mt-4">
                                <label class="col-form-label" style="padding-left: 15px;font-size: 1rem;">预警方式:</label>
                                <div class="col-sm-2">
                                    <select class="single-select" id="Sentiment_Alert_Class_Element">
                                        <option value="All">全部</option>
                                        <option value="平台">平台</option>
                                        <option value="App">App</option>
                                        <option value="微信">微信</option>
                                        <option value="短信">短信</option>
                                        <option value="钉钉">钉钉</option>
                                        <option value="公众号">公众号</option>
                                    </select>
                                </div>
                                <label class="col-form-label" style="font-size: 1rem;">处置状态:</label>
                                <div class="col-sm-2">
                                    <select class="single-select" id="Sentiment_Alert_Status_Element">
                                        <option value="All">全部</option>
                                        <option value="Active">未处置</option>
                                        <option value="Finish">已处置</option>
                                    </select>
                                </div>
                                <label class="col-form-label" style="font-size: 1rem;">预警内容:</label>
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" placeholder="预警标题/内容查询" id="Sentiment_Alert_Title_Content_Element">
                                </div>
                                <div class="col-sm-2">
                                    <button type="button" class="btn btn-info btn-sm m-1 px-4"  style="float: right;" onclick="Chioce_Cheak_Table()">查询</button>
                                    <button type="button" class="btn btn-warning btn-sm m-1 px-4" style="float: right;" onclick="Init_Show_Table_Params()">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <table
                                id="alarm_table"
                                class="table table-striped table-bordered"
                            ></table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
		<div class="overlay toggle-btn-mobile"></div>
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<div class="footer">
			<p class="mb-0">© CSC Public Sentiment Work Platform Version1.13 All rights 2024</p>
		</div>
	</div>
    <div class="modal fade" id="loadingModal" backdrop="static" keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div style="width: 200px;height:20px; z-index: 20000; position: absolute; text-align: center; left: 50%; top: 50%;margin-left:-100px;margin-top:-10px">
                <div class="text-center">
                    <div class="spinner-border text-info spinner-border-sm" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                    <strong style="color:#198fed">Loading...</strong>
                </div>
            </div>   
        </div>
    </div>
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
    <!-- notifications -->
    <script src="/static/JavaScript/notifications/js/lobibox.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notifications.min.js"></script>
    <script src="/static/JavaScript/notifications/js/notification-custom-script.js"></script>
    <!-- loader-->
    <script src="/static/JavaScript/pace.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
    <script src="/static/JavaScript/select2/js/select2.min.js"></script>
    <script src="/static/JavaScript/datatable/js/jquery.dataTables.min.js"></script>
	<!-- App JS -->
    <script src="/static/JavaScript/app.js"></script>
    <!-- App functions and Server_Tools -->
    <script src="static/JavaScript/Utils/Toolkit/Service_Common.js"></script>
    <script src="static/JavaScript/Utils/Toolkit/Service_Requests.js"></script>
    <script src="/static/Page/System/Page_Alarm.js"></script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------全局变量 -->
    <script>
        var User_Token = localStorage.getItem('User_Token')
        console.log('User_Token:',User_Token)
    </script>
    <!-- ---------------------------------------------------------------------------------------------------------------------------------------------------------Header&Sideber -->
    <script>
        function Page_Init(){
            Element_Sidebar_Header();
            Requests_Sentiment_Alarm_Info();
        };

        window.onload = function(){
            Page_Init();
        };
    </script>
</body>

</html>