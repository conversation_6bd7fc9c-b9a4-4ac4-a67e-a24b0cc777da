console.log('Loading_System_BaseConfig_Element')
class System_BaseConfig {

        constructor(type,data){
            this.type = type
            this.data = data

        }

        run(){

            try {
                return eval(`this.${this.type}()`);
            } catch (error) {
                return {'Error': error, "Type": this.type, "Data": this.data, "Status": "Failed"}

            }
        }

        Resoytr(Redata){
            console.log('Redata:',Redata)
            return Redata
        }

        //  获取侧边和顶部节点
        BaseConfig (){
//        console.log('run Loading_System_BaseConfig')
            var Element_Base = new Object()
            // console.log('Sentinel_BaseConfig:',Sentinel_BaseConfig)
            let Requests_Data = {
                "user_id": "CSC",
                "user_token": User_Token,
                "data_class": "Sentinel",
                "data_type": 'Service_Element',
                "data_methods": "return_element_base",
                "data_argument": `{}`,
                "data_kwargs": this.data
            }
            $.ajax({
                type: "POST",
                url: "http://sccs.csc-tech.cn:8746/Service_Interface",
                contentType: 'application/json',
                data: JSON.stringify(Requests_Data),
                async: false,
                timeout:30000,
                success: function (Element_Base_Info) {
                    //  console.log('Element_Base_Info:',Element_Base_Info)
                    Element_Base = Element_Base_Info
                    // console.log('Sentinel_BaseConfig1:',this.Sentinel_BaseConfig)
                    // this.Resoytr(this.Sentinel_BaseConfig)
                }
            });
            return Element_Base
        }
}

