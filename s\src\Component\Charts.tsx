import React, { useEffect, useRef } from 'react';

// 声明ApexCharts全局变量
declare global {
  interface Window {
    ApexCharts: any;
  }
}

// 检查ApexCharts是否加载的辅助函数
const waitForApexCharts = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    console.log('Waiting for ApexCharts...');

    if (window.ApexCharts) {
      console.log('ApexCharts found immediately');
      resolve(window.ApexCharts);
    } else {
      let attempts = 0;
      const maxAttempts = 50; // 5秒超时

      const checkInterval = setInterval(() => {
        attempts++;
        console.log(`Checking for ApexCharts... attempt ${attempts}`);

        if (window.ApexCharts) {
          console.log('ApexCharts loaded successfully');
          clearInterval(checkInterval);
          resolve(window.ApexCharts);
        } else if (attempts >= maxAttempts) {
          console.error('ApexCharts failed to load after 5 seconds');
          clearInterval(checkInterval);
          reject(new Error('ApexCharts failed to load'));
        }
      }, 100);
    }
  });
};

interface ChartProps {
  id: string;
  type: 'pie' | 'donut' | 'line' | 'area';
  data?: any;
  height?: number;
}

// 平台分布饼图
export const PlatformDistributionChart: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initChart = async () => {
      try {
        const ApexCharts = await waitForApexCharts();

        if (chartRef.current) {
          const options = {
            series: [6737, 5275, 7687, 4219, 827, 574, 963, 790, 435, 193, 2, 859],
            chart: {
              height: 300,
              type: 'pie',
              background: 'transparent'
            },
            labels: ['网页', '微信', '微博', 'App', '论坛', '报刊', '视频', '头条', '搜狐号', '问答', '评论', '其他类型'],
            colors: ['#5B8BFF', '#65DB79', '#FF6E5E', '#5EAAF7', '#6A30F5', '#2192FD', '#7C8EEE', '#D64541', '#F2C81C', '#14CEAA', '#1DC9B7', '#8573DD'],
            legend: {
              position: 'bottom',
              labels: {
                colors: '#e5e9ec'
              }
            },
            dataLabels: {
              enabled: true,
              style: {
                colors: ['#fff']
              }
            },
            tooltip: {
              theme: 'dark'
            }
          };

          const chart = new ApexCharts(chartRef.current, options);
          chart.render();

          return () => {
            chart.destroy();
          };
        }
      } catch (error) {
        console.error('Failed to load ApexCharts:', error);
        // 显示备用内容
        if (chartRef.current) {
          chartRef.current.innerHTML = `
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #e5e9ec;">
              <div style="text-align: center;">
                <div style="font-size: 48px; margin-bottom: 10px;">📊</div>
                <div>平台分布图表</div>
                <div style="font-size: 12px; color: #adb5bd; margin-top: 5px;">图表加载中...</div>
              </div>
            </div>
          `;
        }
      }
    };

    initChart();
  }, []);

  return <div ref={chartRef} id="platform-distribution-chart" style={{ height: '300px' }} />;
};

// 情感属性环形图
export const SentimentAttributeChart: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initChart = async () => {
      try {
        const ApexCharts = await waitForApexCharts();

        if (chartRef.current) {
          const options = {
            series: [85, 4, 11],
            chart: {
              height: 300,
              type: 'donut',
              background: 'transparent'
            },
            labels: ['正面', '负面', '中性'],
            colors: ['#1f77b4', '#d62728', '#2ca02c'],
            legend: {
              position: 'bottom',
              labels: {
                colors: '#e5e9ec'
              }
            },
            dataLabels: {
              enabled: true,
              style: {
                colors: ['#fff']
              }
            },
            tooltip: {
              theme: 'dark'
            },
            plotOptions: {
              pie: {
                donut: {
                  size: '70%'
                }
              }
            }
          };

          const chart = new ApexCharts(chartRef.current, options);
          chart.render();

          return () => {
            chart.destroy();
          };
        }
      } catch (error) {
        console.error('Failed to load ApexCharts:', error);
        if (chartRef.current) {
          chartRef.current.innerHTML = `
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #e5e9ec;">
              <div style="text-align: center;">
                <div style="font-size: 48px; margin-bottom: 10px;">📈</div>
                <div>情感属性图表</div>
                <div style="font-size: 12px; color: #adb5bd; margin-top: 5px;">图表加载中...</div>
              </div>
            </div>
          `;
        }
      }
    };

    initChart();
  }, []);

  return <div ref={chartRef} id="sentiment-attribute-chart" style={{ height: '300px' }} />;
};

// 舆情趋势线图
export const SentimentTrendChart: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initChart = async () => {
      try {
        const ApexCharts = await waitForApexCharts();

        if (chartRef.current) {
          // 生成30天的模拟数据
          const generateData = () => {
            const data = [];
            const categories = [];
            const today = new Date();

            for (let i = 29; i >= 0; i--) {
              const date = new Date(today);
              date.setDate(date.getDate() - i);
              categories.push(`${date.getMonth() + 1}-${date.getDate()}`);
              data.push(Math.floor(Math.random() * 50000) + 10000);
            }

            return { data, categories };
          };

          const { data, categories } = generateData();

          const options = {
            series: [{
              name: '社会',
              data: data
            }],
            chart: {
              height: 400,
              type: 'area',
              background: 'transparent',
              toolbar: {
                show: true,
                tools: {
                  download: true,
                  selection: true,
                  zoom: true,
                  zoomin: true,
                  zoomout: true,
                  pan: true,
                  reset: true
                }
              }
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              curve: 'smooth',
              width: 2
            },
            fill: {
              type: 'gradient',
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
              }
            },
            colors: ['#1f77b4'],
            grid: {
              show: true,
              borderColor: 'rgba(255, 255, 255, 0.12)',
              strokeDashArray: 5,
            },
            xaxis: {
              categories: categories,
              labels: {
                style: {
                  colors: '#e5e9ec'
                }
              }
            },
            yaxis: {
              labels: {
                style: {
                  colors: '#e5e9ec'
                },
                formatter: function (value: number) {
                  return value + "条";
                }
              }
            },
            tooltip: {
              theme: 'dark',
              x: {
                format: 'MM-dd'
              }
            },
            legend: {
              labels: {
                colors: '#e5e9ec'
              }
            }
          };

          const chart = new ApexCharts(chartRef.current, options);
          chart.render();

          return () => {
            chart.destroy();
          };
        }
      } catch (error) {
        console.error('Failed to load ApexCharts:', error);
        if (chartRef.current) {
          chartRef.current.innerHTML = `
            <div style="height: 400px; display: flex; align-items: center; justify-content: center; color: #e5e9ec;">
              <div style="text-align: center;">
                <div style="font-size: 64px; margin-bottom: 15px;">📊</div>
                <div style="font-size: 18px; margin-bottom: 10px;">近30天舆情趋势</div>
                <div style="font-size: 14px; color: #adb5bd;">图表加载中...</div>
              </div>
            </div>
          `;
        }
      }
    };

    initChart();
  }, []);

  return <div ref={chartRef} id="Mouth_Sentiment_Time_Crawl" style={{ height: '400px' }} />;
};

// 中国地图组件
export const ChinaMapChart: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initChart = async () => {
      try {
        const ApexCharts = await waitForApexCharts();

        if (chartRef.current) {
          // 模拟各省份数据
          const provinceData = [
            { name: '北京', value: 15234 },
            { name: '上海', value: 12456 },
            { name: '广东', value: 23567 },
            { name: '浙江', value: 8934 },
            { name: '江苏', value: 11234 },
            { name: '山东', value: 9876 },
            { name: '河南', value: 7654 },
            { name: '四川', value: 6543 },
            { name: '湖北', value: 5432 },
            { name: '湖南', value: 4321 }
          ];

          // 创建一个简单的柱状图来代替地图
          const options = {
            series: [{
              name: '舆情数量',
              data: provinceData.map(item => item.value)
            }],
            chart: {
              height: 350,
              type: 'bar',
              background: 'transparent',
              toolbar: {
                show: true
              }
            },
            plotOptions: {
              bar: {
                horizontal: true,
                borderRadius: 4,
                dataLabels: {
                  position: 'top',
                }
              }
            },
            dataLabels: {
              enabled: true,
              offsetX: -6,
              style: {
                fontSize: '12px',
                colors: ['#fff']
              }
            },
            stroke: {
              show: true,
              width: 1,
              colors: ['transparent']
            },
            xaxis: {
              categories: provinceData.map(item => item.name),
              labels: {
                style: {
                  colors: '#e5e9ec'
                }
              }
            },
            yaxis: {
              labels: {
                style: {
                  colors: '#e5e9ec'
                }
              }
            },
            fill: {
              opacity: 1,
              colors: ['#1f77b4']
            },
            tooltip: {
              theme: 'dark',
              y: {
                formatter: function (val: number) {
                  return val + " 条"
                }
              }
            },
            grid: {
              show: true,
              borderColor: 'rgba(255, 255, 255, 0.12)',
              strokeDashArray: 5,
            },
            title: {
              text: '各地区舆情分布',
              align: 'center',
              style: {
                fontSize: '16px',
                color: '#e5e9ec'
              }
            }
          };

          const chart = new ApexCharts(chartRef.current, options);
          chart.render();

          return () => {
            chart.destroy();
          };
        }
      } catch (error) {
        console.error('Failed to load ApexCharts:', error);
        if (chartRef.current) {
          chartRef.current.innerHTML = `
            <div style="height: 350px; display: flex; align-items: center; justify-content: center; color: #e5e9ec;">
              <div style="text-align: center;">
                <div style="font-size: 48px; margin-bottom: 10px;">🗺️</div>
                <div>地区分布图表</div>
                <div style="font-size: 12px; color: #adb5bd; margin-top: 5px;">图表加载中...</div>
              </div>
            </div>
          `;
        }
      }
    };

    initChart();
  }, []);

  return <div ref={chartRef} id="china-map-chart" style={{ height: '350px' }} />;
};
