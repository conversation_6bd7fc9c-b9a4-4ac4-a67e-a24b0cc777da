<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Not Found</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #2f3640;
            color: #ffffff;
        }

        .container {
            text-align: center;
            width: 400px;
            padding: 50px;
            background-color: rgba(47, 54, 64, 0.9);
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
        }

        h1 {
            font-size: 36px;
            margin-bottom: 20px;
        }

        p {
            font-size: 18px;
            line-height: 1.5;
        }

        a {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4fc3f7;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        a:hover {
            background-color: #29b6f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Error 404</h1>
        <p>对不起，我们找不到你要找的东西。你好像在母体里发现了一个小故障。</p>
        <a href="/Service_Page?Page=Page_Login">返回登录</a>
    </div>

    <script>
        // 添加一些简单的动画效果，例如闪烁的文本或渐变背景
        (function() {
            const elements = document.querySelectorAll('.container *');
            const colors = ['#4fc3f7', '#29b6f6', '#03a9f4'];

            function randomColor() {
                return colors[Math.floor(Math.random() * colors.length)];
            }

            function animateElements() {
                elements.forEach(element => {
                    element.style.color = randomColor();
                });
            }

            setInterval(animateElements, 1500);
        })();
    </script>
</body>
</html>
