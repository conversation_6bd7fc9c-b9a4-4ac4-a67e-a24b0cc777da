import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ButtonControl,Node_Socket,ModelControl_Rumor,ButtonControl_More} from "./Node_Controls";
















  export class Node_Model_Component_Rumor extends ClassicPreset.Node<
  {   
    [key in string]: ClassicPreset.Socket
    },
  {  
    [key in string]: ClassicPreset.Socket

    
  },
  {
     [key in string]:
          | ButtonControl_More
          | ClassicPreset.Control
  }> 
  {
    width  = 388;
    height = 428;
    
      constructor(Label: string,) {
        super(Label);

      this.addInput("Input", new ClassicPreset.Input(Node_Socket, ""),);
      this.addOutput("Output", new ClassicPreset.Output(Node_Socket, ""));

      const textAreaControl = new ModelControl_Rumor(
        '【标题】:未知', // Label for the text area
        '【日期】:未知', // Initial value
        '【数源】:未知', // Initial value
        '【作者】:未知', // Initial value
        '【图片】:未知', // Initial value
        '【链接】:未知', // Initial value
        '【内容】:未知', // Initial value
        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  textAreaControl);

      this.addControl("More", new ButtonControl_More("详情"));
    //   const buttonControl = this.controls.More as ButtonControl_More;
    //   buttonControl.onClick = (Menu:string) => {
    //   console.log("按钮点击",Menu);
    //   };
      
          // this.addOutput("exec", new ClassicPreset.Output(socket, "作者"));

    // 如果需要在类外部操作，可以在类中添加公共方法

    //   this.addInput("a", new ClassicPreset.Input(socket, "A"));
    }

          // 提供公共方法供外部修改回调
      public setButtonAction(callback: () => void) {
          const btn = this.controls.More as ButtonControl_More;
          btn.onClick = callback;
      }
    


      data() {
        return {}; // 根据实际情况返回数据结构
      }
    
      execute(_: never, forward: (output: "exec") => void) {
        forward("exec");
      }
    }
    