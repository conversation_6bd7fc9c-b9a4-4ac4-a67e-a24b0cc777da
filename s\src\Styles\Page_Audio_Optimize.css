
    .audiovideo-container {
        width: '100%';
        margin: '0 auto';
        display: 'flex';
        justify-content: 'center';
        align-items: 'center';
        gap: '12px';
        flex-wrap: 'nowrap';
        position: 'relative';
        
    }
    .audiovideo-container-title-wrapper {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.05);
        border-left: 4px solid #1890ff;
        border-radius: 6px;
        transition: background 0.3s ease;
    }

    .audiovideo-container-title-wrapper:hover {
        background: rgba(255, 255, 255, 0.08);
    }
    
    .audiovideo-container-title-wrapper h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #f0f0f0;
        letter-spacing: 0.3px;
    }
    
    .audiovideo-header-divider {
        border: 0;
        border-top: 1px solid #e8e8e8;
        margin: 20px 0;
    }
    /* 附件上传按钮样式 */
    .audiovideo-batch-upload-btn {
        background: linear-gradient(135deg, #667eea 0%, #dc4446 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        /* margin-right: 20px; */
        margin-right: 10px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }
    
    .audiovideo-batch-upload-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    .audiovideo-batch-upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
    
    .audiovideo-batch-upload-btn:hover::before {
        left: 100%;
    }
    
    .audiovideo-batch-upload-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }
   