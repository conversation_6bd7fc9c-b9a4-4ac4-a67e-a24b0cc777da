import { Presets, ClassicScheme, RenderEmit } from "rete-react-plugin";
import { css } from "styled-components";
import { $fontfamily } from "./Consts";


type Props<S extends ClassicScheme> = {
  data: S["Node"];
  styles?: () => any;
  emit: RenderEmit<S>;
};


const Style_System = css<{ selected?: boolean }>`
  background: #000;
  border: 0px #cfc7ff solid;
 
  border-radius: 3px;
  transition: background 0.4s;
  .title {
    color: white;
    text-align: left;
    border-radius: 20px 20px 0 0;
    border-bottom: 2px  #ccc solid;
    font-family: "Roboto", sans-serif;
    font-weight: 100;
    font-size: 1.2em;
  }
  &:hover {
   background:rgba(24, 25, 27, 1);
   
  }
  .input-title,
  .output-title {
    font-weight: 100;
     font-family:'Roboto', sans-serif;
  }
  .output-socket {
    margin-right: -1px;
  }
  .input-socket {
    margin-left: -1px;
  }
  ${(props) =>
    props.selected &&
    css`
        background:rgba(24, 25, 27, 1);
        box-shadow: 0px 0px 8px rgba(240, 236, 233,1);
      .title {
    
      }
    `}
`;



export function Node_Styles_System<S extends ClassicScheme>(props: Props<S>) {
  return <Presets.classic.Node styles={() => Style_System} {...props} />;
}








const Style_Object = css<{ selected?: boolean }>`
  background:rgba(24, 25, 27, 0.3);
  border: 1px rgb(26, 231, 77) solid;
  border-radius: 8px;
  transition: background 0.4s;
  .title {
    color: white;
    text-align: left;
    border-radius: 20px 20px 0 0;
    border-bottom: 2px #cfc7ff solid;
    font-family: ${$fontfamily};
    font-weight: 100;
    font-size: 1.2em;
  }
  &:hover {
   background:rgba(24, 25, 27, 1);
  }
  .input-title,
  .output-title {
    font-weight: 100;
    font-family: ${$fontfamily};
  }
  .output-socket {
    margin-right: -1px;
  }
  .input-socket {
    margin-left: -1px;
  }
  ${(props) =>
    props.selected &&
    css`
   background:rgba(24, 25, 27, 1);
      border-color: #ff0000c4;
      .title {
        border-color: #ff0000c4;
      }
    `}
`;







export function Node_Styles_Object<S extends ClassicScheme>(props: Props<S>) {
  return <Presets.classic.Node styles={() => Style_Object} {...props} />;
}







const Style_Model = css<{ selected?: boolean }>`
 background:rgba(24, 25, 27, 0.3);
  border: 1px rgb(52, 50, 194) solid;
  border-radius: 8px;
  transition: background 0.4s;
  .title {
    color: white;
    text-align: left;
    border-radius: 20px 20px 0 0;
    border-bottom: 2px #cfc7ff solid;a
    font-family: ${$fontfamily};
    font-weight: 100;
    font-size: 1.2em;
  }
  &:hover {
     background:rgba(24, 25, 27, 1);
  }
  .input-title,
  .output-title {
    font-weight: 100;
    font-family: ${$fontfamily};
  }
  .output-socket {
    margin-right: -1px;
  }
  .input-socket {
    margin-left: -1px;
  }
  ${(props) =>
    props.selected &&
    css`
       background:rgba(24, 25, 27, 1);
      border-color: #ff0000c4;
      .title {
        border-color: #ff0000c4;
      }
    `}
`;







export function Node_Styles_Model<S extends ClassicScheme>(props: Props<S>) {
  return <Presets.classic.Node styles={() => Style_Model} {...props} />;
}

