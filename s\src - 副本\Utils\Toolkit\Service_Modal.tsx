import React, { useState, useEffect } from 'react';
import { Modal, Button, Layout, Form, Checkbox, Input, Space } from 'antd';
import type { GetProps } from 'antd';

type ModalConfig = {
  title: string;
  content: React.ReactNode;
  onOk?: () => void;
  onCancel?: () => void;
};

type OTPProps = GetProps<typeof Input.OTP>;

type FieldType = {
  username?: string;
  password?: string;
  remember?: string;
};

const Form_User: React.FC = () => {
  const [countdown, setCountdown] = useState(0);
  const [isSending, setIsSending] = useState(false);

  const onChange: OTPProps['onChange'] = (text) => {
    console.log('onChange:', text);
  };

  const onInput: OTPProps['onInput'] = (value) => {
    console.log('onInput:', value);
  };

  const sharedProps: OTPProps = {
    onChange,
    onInput,
  };

  const handleSend = () => {
    if (countdown === 0) {
      setCountdown(60);
      setIsSending(true);
      // 模拟发送请求
      console.log('发送验证码');
    }
  };

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setIsSending(false);
    }
  }, [countdown]);

  return (
    <Form
      name="basic"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ marginLeft: -80, marginTop: 40, width: 380 }}
      initialValues={{ remember: true }}
      autoComplete="off"
    >
      <Form.Item<FieldType>
        label="手机"
        name="username"
        rules={[{ required: true, message: '请输入您的手机号' }]}
      >
        <Space.Compact style={{ width: '100%' }}>
          <Input defaultValue="" />
          <Button
            type="primary"
            onClick={handleSend}
            disabled={isSending}
          >
            {isSending ? `${countdown}秒` : '发送'}
          </Button>
        </Space.Compact>
      </Form.Item>

      <Form.Item<FieldType> style={{ marginLeft: -80 }} label={null}>
        <Input.OTP formatter={(str) => str.toUpperCase()} {...sharedProps} />
      </Form.Item>
    </Form>
  );
};

class Service_Modal {
  // 打开普通模态框
  static Open(config: ModalConfig) {
    const { title, content, onOk, onCancel } = config;

    const modal = Modal.info({
      title: <span style={{ color: '#1890ff' }}>{title}</span>,
      content: <div style={{ padding: '16px 0' }}>{content}</div>,
      footer: (
        <Space direction="horizontal" align="end" style={{ float: 'right' }}>
          <Button onClick={() => {
            if (onCancel) onCancel();
            modal.destroy();
          }}>取消</Button>
          <Button type="primary" onClick={() => {
            if (onOk) onOk();
            modal.destroy();
          }}>
            确定
          </Button>
        </Space>
      ),
      maskClosable: true,
      closable: true,
      
    });
  }

  // 打开用户验证模态框
  static User_Verification(config: ModalConfig) {
    const { title, content, onOk, onCancel } = config;
    const Content = <Form_User />;

    const modal = Modal.info({
      title: <span style={{ color: '#1890ff' }}>{"iERM Agent手机验证"}</span>,
      content: Content,
      footer: (
        <Space direction="horizontal" align="end" style={{ float: 'right' }}>
          <Button onClick={() => {
            if (onCancel) onCancel();
            modal.destroy();
          }}>取消</Button>
          <Button type="primary" onClick={() => {
            if (onOk) onOk();
            modal.destroy();
          }}>
            确定
          </Button>
        </Space>
      ),
      maskClosable: true,
      closable: true,
      style: {
        top: '30%',
        left: '-0%',
        // transform: 'translate(0, 50%)',
      },
    });
  }

  // 打开确认对话框
  static confirm(config: ModalConfig) {
    const { title, content, onOk, onCancel } = config;

    const modal = Modal.confirm({
      title: <span style={{ color: '#ff4d4f' }}>{title}</span>,
      content: <div style={{ padding: '16px 0' }}>{content}</div>,
      footer: (
        <Space direction="horizontal" align="end" style={{ float: 'right' }}>
          <Button onClick={() => {
            if (onCancel) onCancel();
            modal.destroy();
          }}>取消</Button>
          <Button type="primary" onClick={() => {
            if (onOk) onOk();
            modal.destroy();
          }}>
            确定
          </Button>
        </Space>
      ),
      maskClosable: true,
      closable: true,
    });
  }
}

export default Service_Modal;