<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="shortcut icon" href="/static/images/App/Logo.ico">
    <link rel="stylesheet" href="/static/material-design-iconic-font/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" href="/static/css/animate/animate.min.css">
    <link rel="stylesheet" href="/static/css/jquery/fullcalendar/fullcalendar.min.css">

    <link href="/static/css/font-awesome/all.min.css" rel="stylesheet" />
    <script src="/static/JavaScript/Jquery/jquery.min.js"></script>


    <script src="/static/JavaScript/App/popper.min.js"></script>
    <script src="/static/JavaScript/App/bootstrap.min.js"></script>

    <link rel="stylesheet" href="/static/css/server_style.css">

    <script src="/static/JavaScript/App/sweetalert2.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery.scrollbar.min.js"></script>
    <script src="/static/JavaScript/Jquery/jquery-scrollLock.min.js"></script>


    <!-- App styles -->
    <link rel="stylesheet" href="/static/css/app/app.min.css">
    <!-- App functions and actions -->
    <script src="/static/JavaScript/App/app.min.js"></script>

    <!-- App functions and Server_Tools -->
    <script src="/static/JavaScript/Config/Server_Tools/Server_Print.js"></script>
    <script src="/static/JavaScript/Config/Server_Tools/Server_Href.js"></script>
    <script src="/static/JavaScript/Config/Server_Function/Server_Data.js"></script>
    <script src="/static/JavaScript/Config/System_BaseConfig.js"></script>


    <style type="text/css">
        .table {
            table-layout: fixed;
            word-break: break-all;
        }
        .product_list img{
            border-radius: 2px;
            width: 100%;
        }
    </style>
    <!-- Vendors: Data tables -->
    <script src="/static/JavaScript/Visualization/common.js"></script>
    <title>{{ title }}</title>
</head>

<body data-sa-theme="1"
    style="background: url('/static/images/Background/10.jpg');background-size:100% 100%;height: 100vh">

    <main class="main">
        <div class="page-loader">
            <div class="page-loader__spinner">
                <svg viewBox="25 25 50 50">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                </svg>
            </div>
        </div>
        <header class="header" id="Web_Header">

        </header>
        <aside class="sidebar sidebar--hidden" id="Web_Asidebar">

        </aside>
        <section class="content content--full">

            <div class="d-flex flex-wrap product_list" id="content">

            
            </div>
        </section>
    </main>


</body>
<script>
    $(document).ready(function () {
        Get_Product_List()
    })
    function Get_Product_List() {
        const __Server_Table_Data = new Server_Data('Service_Requests_Data', {
            'Data_Name': 'Query_Product',
            'User_Token': User_Token,
        });
        loaderShow()
        __Server_Table_Data.async_run()
            .then((res) => {
                if ('Status' in res && res['Status'] == 'Failed') {
                    notify({
                        message: '查询失败！',
                        type: 'danger',
                    })
                    return
                }
                SetView(res)
            }).catch((err) => {
                console.log("初始化请求出错:", err)
            }).finally(() => loaderHide());
    }

    function SetView(list=[]) {
        let html = ''
        let product_list = list.filter(e => e.PL_STATUS == 'Active')
        if (product_list.length == 0) {
            html = `<div class="d-flex flex-column align-items-center"  style="width:100%">
                        <img style="width: 375px;height:375px" src="/static/img/list_none.png" alt="" />
                        <div class="text-center">尚未推出，敬请期待...</div>
                    </div>`
        }
        product_list.forEach(e => {
            html += `<div class="card text-center rounded m-3"
                    style="box-shadow: 0 0 3rem rgb(45 142 165 / 32%) !important;flex-shrink: 0;width: calc(100% / 4);min-width: 300px;">
                    <div class="card-body p-0">
                        <div class="product_item">
                            <img src="${e.PL_IMAGE_URL}" alt="" />
                        </div>
                        <h4 class="card-title mt-2">${e.PL_NAME}</h4>
                        <p class="card-text p-3">${e.PL_DESCRIBE}</p>
                        <a href="${e.PL_TARGET_URL}" class="btn btn-light mb-3">立即体验</a>
                    </div>
                </div>`
        })
        $('#content').html(html)
    }

</script>

</html>