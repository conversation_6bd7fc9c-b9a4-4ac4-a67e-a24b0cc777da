﻿<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>哨兵导控</title>
	<link rel="icon" href="/static/Images/Logo.ico" type="image/png" />
	<!--plugins-->
    <link href="/static/CSS/simplebar/css/simplebar.css" rel="stylesheet" />
    <link href="/static/CSS/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" />
    <link href="/static/CSS/metismenu/css/metisMenu.min.css " rel="stylesheet" />
	<!-- loader-->
    <link href="/static/CSS/pace.min.css" rel="stylesheet" />
	<!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/CSS/bootstrap.min.css" />
	<!-- Icons CSS -->
    <link rel="stylesheet" href="/static/CSS/icons.css" />
	<!-- App CSS -->
    <link rel="stylesheet" href="/static/CSS/app.css" />
</head>

<body class="bg-theme bg-theme1">
	<!-- wrapper -->
	<div class="wrapper">
		
		<!--header-->
		<header class="top-header">
			<nav class="navbar navbar-expand">
				<div class="left-topbar d-flex align-items-center">
					<a href="javascript:;" class="toggle-btn">	<i class="bx bx-menu"></i>
					</a>
				</div>
				<div class="flex-grow-1 search-bar">
					<div class="input-group">
						<div class="input-group-prepend search-arrow-back">
							<button class="btn btn-search-back" type="button"><i class="bx bx-arrow-back"></i>
							</button>
						</div>
						<input type="text" class="form-control" placeholder="search" />
						<div class="input-group-append">
							<button class="btn btn-search" type="button"><i class="lni lni-search-alt"></i>
							</button>
						</div>
					</div>
				</div>
				<div class="right-topbar ml-auto">
					<ul class="navbar-nav">
						<li class="nav-item search-btn-mobile">
							<a class="nav-link position-relative" href="javascript:;">	<i class="bx bx-search vertical-align-middle"></i>
							</a>
						</li>
						<li class="nav-item dropdown dropdown-lg">
							<a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative" href="javascript:;" data-toggle="dropdown">	<span class="msg-count" id="Message_Count"></span>
								<i class="bx bx-comment-detail vertical-align-middle"></i>
							</a>
							<div class="dropdown-menu dropdown-menu-right">
								<a href="javascript:;">
									<div class="msg-header">
										<h6 class="msg-header-title">突出谣言推送</h6>
										<!-- <p class="msg-header-subtitle">突出谣言推送</p> -->
									</div>
								</a>
								<div class="header-message-list" id="Message_List">
											<!-- <div class="media-body">
												<h6 class="msg-info" id="Title_Info"><span class="msg-time float-right" id="Time"></span></h6>
												<p class="Content_Info" id="Content_Info"></p>
											</div> -->
								</div>
								<a href="javascript:;">
									<div class="text-center msg-footer">View All Messages</div>
								</a>
							</div>
						</li>
						<li class="nav-item dropdown dropdown-lg">
							<a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative" href="javascript:;" data-toggle="dropdown">	<i class="bx bx-bell vertical-align-middle"></i>
								<span class="msg-count">8</span>
							</a>
							<div class="dropdown-menu dropdown-menu-right">
								<a href="javascript:;">
									<div class="msg-header">
										<h6 class="msg-header-title">8 New</h6>
										<p class="msg-header-subtitle">Application Notifications</p>
									</div>
								</a>
								<div class="header-notifications-list">
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-primary text-primary"><i class="bx bx-group"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">New Customers<span class="msg-time float-right">14 Sec
													ago</span></h6>
												<p class="msg-info">5 new user registered</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-danger text-danger"><i class="bx bx-cart-alt"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">New Orders <span class="msg-time float-right">2 min
													ago</span></h6>
												<p class="msg-info">You have recived new orders</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-shineblue text-shineblue"><i class="bx bx-file"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">24 PDF File<span class="msg-time float-right">19 min
													ago</span></h6>
												<p class="msg-info">The pdf files generated</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-cyne text-cyne"><i class="bx bx-send"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">Time Response <span class="msg-time float-right">28 min
													ago</span></h6>
												<p class="msg-info">5.1 min avarage time response</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-purple text-purple"><i class="bx bx-home-circle"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">New Product Approved <span
													class="msg-time float-right">2 hrs ago</span></h6>
												<p class="msg-info">Your new product has approved</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-warning text-warning"><i class="bx bx-message-detail"></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">New Comments <span class="msg-time float-right">4 hrs
													ago</span></h6>
												<p class="msg-info">New customer comments recived</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-success text-success"><i class='bx bx-check-square'></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">Your item is shipped <span class="msg-time float-right">5 hrs
													ago</span></h6>
												<p class="msg-info">Successfully shipped your item</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-sinata text-sinata"><i class='bx bx-user-pin'></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">New 24 authors<span class="msg-time float-right">1 day
													ago</span></h6>
												<p class="msg-info">24 new authors joined last week</p>
											</div>
										</div>
									</a>
									<a class="dropdown-item" href="javascript:;">
										<div class="media align-items-center">
											<div class="notify bg-light-mehandi text-mehandi"><i class='bx bx-door-open'></i>
											</div>
											<div class="media-body">
												<h6 class="msg-name">Defense Alerts <span class="msg-time float-right">2 weeks
													ago</span></h6>
												<p class="msg-info">45% less alerts last 4 weeks</p>
											</div>
										</div>
									</a>
								</div>
								<a href="javascript:;">
									<div class="text-center msg-footer">View All Notifications</div>
								</a>
							</div>
						</li>
					
					</ul>
				</div>
			</nav>
		</header>
		<!--end header-->
		<!--page-wrapper-->
		<div class="page-wrapper">
			<!--page-content-wrapper-->
			<div class="page-content-wrapper">
				<div class="page-content" id="Sentiment_Domestic_Body">
					<!--breadcrumb-->
					<div class="page-breadcrumb d-none d-md-flex align-items-center mb-3">
						<div class="breadcrumb-title pr-3">Components</div>
						<div class="pl-3">
							<nav aria-label="breadcrumb">
								<ol class="breadcrumb mb-0 p-0">
									<li class="breadcrumb-item"><a href="javascript:;"><i class='bx bx-home-alt'></i></a>
									</li>
									<li class="breadcrumb-item active" aria-current="page">Cards</li>
								</ol>
							</nav>
						</div>
						<div class="ml-auto">
							<div class="btn-group">
								<button type="button" class="btn btn-light">Settings</button>
								<button type="button" class="btn btn-light dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">	<span class="sr-only">Toggle Dropdown</span>
								</button>
								<div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-left">	<a class="dropdown-item" href="javascript:;">Action</a>
									<a class="dropdown-item" href="javascript:;">Another action</a>
									<a class="dropdown-item" href="javascript:;">Something else here</a>
									<div class="dropdown-divider"></div>	<a class="dropdown-item" href="javascript:;">Separated link</a>
								</div>
							</div>
						</div>
					</div>
					<!--end breadcrumb-->
					<h6 class="mb-0 text-uppercase">今日预警</h6>
					<hr>
					<!-- <div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link" style="font-size:12px;" ><i class="flag-icon flag-icon-in"></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
									<a href="javascript:;" class="card-link"><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link" style="font-size:12px;" ><i class="flag-icon flag-icon-in"></i></a>
									<a href="#" class="card-link" style="font-size:12px;" ></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
										<a class="card-link dropdown-toggle dropdown-toggle-nocaret" href="javascript:;" data-toggle="dropdown"><i class="lni lni-more"></i>
										</a>
										<div class="dropdown-menu dropdown-menu-right">
											<a class="dropdown-item" href="javascript:;"><i
													class="fadeIn animated bx bx-detail"></i><span>详情</span></a>
											<a class="dropdown-item" href="javascript:;"><i
													class="fadeIn animated bx bx-cloud-upload"></i><span>上报</span></a>
							
										</div>
								
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link" style="font-size:12px;" ><i class="flag-icon flag-icon-in"></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
									<a href="javascript:;" class="card-link"><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link"><i class="flag-icon flag-icon-in"></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
									<a href="javascript:;" class=""><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link"><i class="flag-icon flag-icon-in"></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
									<a href="javascript:;" class=""><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>#魏大勋的圈内人缘##魏大勋后援会回应戏份被删##陈飞宇你知道魏大勋为啥跟你老师是同学了吧</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >#各大高校接力为大运会加油#人民日报摘牌# http://t.cn/A606ioLN</h5> 
									<a href="#" class="card-link"><i class="flag-icon flag-icon-in"></i>微信</a>
									<a href="#" class="card-link" style="font-size:5px;">2023-07-27 00:08:08</a> 
									<a href="javascript:;" class=""><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>
					</div> -->

				</div>
				<!--end page content-->
			</div>
			<!--end page-content-wrapper-->
		</div>
		<!--end page-wrapper-->
		<!--start overlay-->
		<div class="overlay toggle-btn-mobile"></div>
		<!--end overlay-->
		<!--Start Back To Top Button--> <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
		<!--End Back To Top Button-->
		<!--footer -->
		<div class="footer">
			<!-- <p class="mb-0">上一页</p><p class="mb-0">刷新</p><p class="mb-0">下一页</p> -->
			<div class="profile-social mt-3">	
				<a href="javascript:;" class=""><i class="fadeIn animated bx bx-chevron-left"></i></a>
				<a href="javascript:;" class=""><i class="fadeIn animated bx bx-revision"></i></a>
				<a href="javascript:;" class=""><i class="fadeIn animated bx bx-chevron-right"></i></a>
			</div>
			
		</div>
		<!-- end footer -->
	</div>
	<!-- end wrapper -->
	<!--start switcher-->
	<div class="switcher-wrapper">
		<div class="switcher-btn"> <i class='bx bx-cog bx-spin'></i>
		</div>
		<div class="switcher-body">
			<h5 class="mb-0 text-uppercase">情报方案</h5>
			<hr/>
			<p class="mb-0">国内情报</p>
			  <hr>
			  
			  <ul class="switcher">
				<li id="theme1">方案一</li>
				<li id="theme2">方案一</li>
				<li id="theme3">方案一</li>
				<li id="theme4">方案一</li>
				<li id="theme5">方案一</li>
				<li id="theme6">方案一</li>
			  </ul>
               <hr>
			  <p class="mb-0">境外情报</p>
			  <hr>
			  
			  <ul class="switcher">
				<li id="theme7"></li>
				<li id="theme8"></li>
				<li id="theme9"></li>
				<li id="theme10"></li>
				<li id="theme11"></li>
				<li id="theme12"></li>
			  </ul>
		</div>
	</div>
	<!--end switcher-->
	<!-- JavaScript -->
    <script src="/static/JavaScript/jquery.min.js"></script>
    <script src="/static/JavaScript/popper.min.js"></script>
    <script src="/static/JavaScript/bootstrap.min.js"></script>
	<!--plugins-->
    <script src="/static/JavaScript/simplebar/js/simplebar.min.js"></script>
    <script src="/static/JavaScript/metismenu/js/metisMenu.min.js"></script>
    <script src="/static/JavaScript/perfect-scrollbar/js/perfect-scrollbar.js"></script>
	<!-- App JS -->
	<script src="/static/JavaScript/app.js"></script>

	<script>
		function Get_Sentiment_Domestic_Info() {
			var Requests_Intelligence_List_Reviewing = {
				"user_id": '129',
				"user_token": "csc13880906610",
				"data_class": "Sentiment",
				"data_type": 'Service',
				"data_methods": 'get_domestic_today_basic_list',
				"data_argument": {},
				"data_kwargs":{}
			}
			$.ajax({
                    type: "POST",
					url: `https://${window.location.host}/System_Interface`,
                    timeout:30000,
                    data: JSON.stringify(Requests_Intelligence_List_Reviewing),
                    contentType: "application/json; charset=utf-8",
                    success: function (Sentinel_System_Status_Info_list) {
						var Vote_all_data = Sentinel_System_Status_Info_list['result']['items']['web_third_data']
						for (var i=0;i<Vote_all_data.length;i++) {
							Sentiment_Domestic_Element(Vote_all_data[i])
						}
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log('请求失败')
                    }
                });
		};

		function Sentiment_Domestic_Element(Info) {
			console.log('Info:',Info)
			var newNode  = document.createElement('div')
			newNode.setAttribute("class",'row')
			newNode.innerHTML = `<div class="col-12 col-lg-6 col-xl-6">
							<div class="card radius-16">
								<div class="card-body">
									<h5 class="card-title" style="font-size:12px;font-weight:bold"><i class="flag-icon flag-icon-in"></i>${Info['TITLE']}</h5>
									<h5 class="card-title" style="font-size:12px;margin-top:-8px;" >${Info['CONTENT'].substring(0,30)}</h5> 
									<a href="#" class="card-link" style="font-size:12px;" ><i class="flag-icon flag-icon-in"></i>${Info['PLATFORM']}</a>
									<a href="#" class="card-link" style="font-size:5px;">${Info['TIME']}</a> 
									<a href="javascript:;" class="card-link"><i class="lni lni-more"></i></a>
								</div>
							</div>
						</div>`
			var divElement = document.getElementById("Sentiment_Domestic_Body"); 
			divElement.appendChild(newNode)
		};

		window.onload =  function() {
			Get_Sentiment_Domestic_Info()
			// Get_Message_Info()
		};
	</script>

	<script>
		// function Get_Message_Info() {
		// 	var Requests_Intelligence_List_Reviewing = {
		// 		"user_id": '129',
		// 		"user_token": "csc13880906610",
		// 		"data_class": "Intelligence",
		// 		"data_type": "Rumor",
		// 		"data_methods": "get_rumor_today_basic_list",
		// 		"data_argument": {},
		// 		"data_kwargs":{}
		// 	}
		// 	$.ajax({
        //             type: "POST",
        //             url: "http://***************:9311/System_Interface",
        //             timeout:30000,
        //             data: JSON.stringify(Requests_Intelligence_List_Reviewing),
        //             contentType: "application/json; charset=utf-8",
        //             success: function (Return_Message_Info) {
		// 				Add_Message_List(Return_Message_Info['result']); 

		// 			}
                    
        //         });
		// }
	</script>
	<script>
		// function Add_Message_List(Message_List){
		// 	console.log("Add_Message_List:",Message_List)
		// 	console.log('长度',Message_List.length)
		// 	var messageCountElement = document.getElementById("Message_Count");
		// 	messageCountElement.textContent = Message_List.length;

		// 	for (var i = 0; i < Message_List.length; i++) {
		// 		// console.log('Message',Message_List[i])
		// 		// 创建新的 <a> 元素
		// 		// 创建新的 <a> 元素
		// 		var newAnchor = document.createElement("a");
		// 		newAnchor.setAttribute("class", "dropdown-item");
		// 		newAnchor.setAttribute("href", "javascript:;");

		// 		// 创建 <div class="media align-items-center">
		// 		var newDivMedia = document.createElement("div");
		// 		newDivMedia.setAttribute("class", "media align-items-center");

		// 		// 创建 <div class="media-body">
		// 		var newDivMediaBody = document.createElement("div");
		// 		newDivMediaBody.setAttribute("class", "media-body");

		// 		// 创建 <h6 class="msg-info" id="Title_Info"><span class="msg-time float-right" id="Time"></span></h6>
		// 		var newH6 = document.createElement("h6");
		// 		newH6.setAttribute("class", "msg-info");
		// 		newH6.setAttribute("id", "Title_Info");
		// 		newH6.textContent = "标题:"+Message_List[i]['SOURCE_TITLE']; // 设置标题内容
		// 		newH6.style.fontSize = "15px";
		// 		// 创建 <span class="msg-time float-right" id="Time"></span>
		// 		var newSpanTime = document.createElement("span");
		// 		newSpanTime.setAttribute("class", "input-group-append" );
		// 		newSpanTime.setAttribute("id", "Time");
		// 		newSpanTime.textContent = Message_List[i]['SOURCE_TIME']; // 设置时间内容

		// 		// 将 <span> 添加到 <h6> 中
		// 		newH6.appendChild(newSpanTime);

		// 		// 创建 <p class="Content_Info" id="Content_Info"></p>
		// 		var newP = document.createElement("p");
		// 		newP.setAttribute("class", "Content_Info");
		// 		newP.setAttribute("id", "Content_Info");
		// 		newP.textContent = "内容:"+Message_List[i]['SOURCE_CONTENT_SHOW']; // 设置内容信息
		// 		newP.setAttribute("style", "white-space: pre-wrap; width: 100%;");


		// 		newDivMediaBody.appendChild(newH6);
		// 		newDivMediaBody.appendChild(newP);

		// 		newDivMedia.appendChild(newDivMediaBody);

		// 		newAnchor.appendChild(newDivMedia);

		// 		var messageList = document.getElementById("Message_List");
		// 		messageList.appendChild(newAnchor);
        // }
		// }
	</script>

</body>

</html>