
import sys
import numpy as np
import librosa
import pyaudio
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QSlider, QLabel, QPushButton
from PySide6.QtCore import Qt, QThread, Signal
from PySide6 import QtWidgets,Qt<PERSON>ore,QtGui


class Component_AudioTranslate(QtWidgets.QWidget):


    def __init__(self, *args):
        super().__init__()

        self.initUI()

    def initUI(self):
        #
        __QHBoxLayout = QtWidgets.QHBoxLayout(self)
        __QHBoxLayout.setSpacing(0)
        __QHBoxLayout.setContentsMargins(0, 0, 0, 0)


        self.QLabel_Original = QtWidgets.QLabel()
        self.QLabel_Original.setStyleSheet("background-color: rgb(0, 0, 0,1);")
        self.QLabel_Translate = QtWidgets.QLabel()
        self.QLabel_Translate.setStyleSheet("background-color: rgb(0, 0, 0,1);")

        __QHBoxLayout.addWidget(self.QLabel_Original)
        __QHBoxLayout.addWidget(self.QLabel_Translate)

        self.Set_Original()

    def Set_Original(self):

        __QHBoxLayout = QtWidgets.QHBoxLayout(self.QLabel_Original)
        __QHBoxLayout.setSpacing(1)
        __QHBoxLayout.setContentsMargins(8,8, 8, 8)
        __KTVLyrics=KTVLyrics()
        __KTVLyrics.setMinimumSize(1200,60)
        __KTVLyrics.setMaximumSize(1200,60)
        # __KTVLyrics=KTVLyrics()/
        __QHBoxLayout.addWidget(__KTVLyrics)





class KTVLyrics(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MTV Lyrics Effect")
        # self.setGeometry(100, 100, 600, 400)

        # 创建一个垂直布局
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setAlignment(Qt.AlignBottom | Qt.AlignLeft)  # 设置布局对齐方式
        self.setLayout(self.layout)

        # 创建歌词数据
        # self.lyrics = [
        #     {"text": "Line 1", "timestamp": 0},
        #     {"text": "Line 2", "timestamp": 2000},
        #     {"text": "Line 3", "timestamp": 4000},
        #     {"text": "Line 4", "timestamp": 6000},
        # ]
        TimeCount=3000
        Count=0
        self.lyrics = [
           {"text": '改编词曲：G.E.M.邓紫棋',"timestamp": 0},

            {"text":'制作人：G.E.M.邓紫棋 / T-Ma马敬恒 编曲：G.E.M.邓紫棋 / T-Ma马敬恒',"timestamp": 0},

            {"text":'你真的懂唯一的定义 并不简单如呼吸',"timestamp": 0},

            {"text":'你真的希望你能厘清 若没交⼼怎么说明',"timestamp": 0},

            {"text":'我真的爱你 句句不轻易 眼神中飘移 总是在关键时刻清楚洞悉',"timestamp": 0},

            {"text":'你的不坚定 配合我颠沛流离 死去中清醒 明⽩你背着我聪明',"timestamp": 0},

            {"text":'那些我 想说的 没说的 话 时我 怀疑呢 只是我 傻瓜',"timestamp": 0},

            {"text":'但如果真的爱 不会算计 爱是不嫉妒 不张狂 不求⾃⼰',"timestamp": 0},

            {"text":'无关你的回应 永不⽌息 你知道我真的爱你 没⼈能比拟',"timestamp": 0},

            {"text":'眼神没肯定 总是在关键时刻清楚洞悉 你的不坚定 配合我颠沛流离',"timestamp": 0},

            {"text":'死去中清醒 明⽩你背着我聪明 我知道爱本质无异 是因为⼈多得拥挤',"timestamp": 0},

            {"text":'你不想证明 证明我是你唯一 证明我是你唯一',"timestamp": 0},

        ]
        self.Lyrics_Translate = [
            {"text": 'Adapted lyrics and music: G.E.M. Deng Ziqi', "timestamp": 0},

            {"text": 'Producer: G.E.M. Deng Ziqi/T-Ma Ma Jingheng Arrangement: G.E.M. Deng Ziqi/T-Ma Ma Jingheng', "timestamp": 0},

            {"text": 'You really understand that the only definition is not simple, like breathing', "timestamp": 0},

            {"text": "Do you really hope you can clarify how to explain if you haven't been intimate", "timestamp": 0},

            {"text": 'I really love you, every word is not easily swayed in my eyes, always clear and insightful in critical moments', "timestamp": 0},

            {"text": 'Your lack of steadfastness and cooperation made me wander and die with clarity, but you were clever behind my back', "timestamp": 0},

            {"text": "I doubt what I wanted to say but didn't say. I'm just a fool", "timestamp": 0},

            {"text": "But if true love doesn't calculate, love is not jealous, not arrogant, not seeking oneself", "timestamp": 0},

            {"text": "It's not about your response, it never stops. You know I really love you, no one can compare", "timestamp": 0},

            {"text": 'Without a certain gaze, I always have a clear understanding of your indecisiveness at critical moments and cooperate with you to navigate through ups and downs', "timestamp": 0},

            {"text": 'In the midst of death, you remain clear headed and bright. You are clever behind my back, and I know that the essence of love is no different from being crowded with too many people', "timestamp": 0},

            {"text": "You don't want to prove that I am your only one, to prove that I am your only one", "timestamp": 0},

        ]

        for Word in self.lyrics:
            Word["timestamp"] = Count*6000
            Count+=1

        for Word in self.Lyrics_Translate:
            Word["timestamp"] = Count * 6000
            Count += 1

        # 创建歌词标签
        self.current_lyric_label = QLabel()
        self.current_lyric_label.setAlignment(Qt.AlignCenter)
        self.current_lyric_label.setStyleSheet("QLabel { font-size: 13px; }")




        self.current_lyric_label_Translate = QLabel()
        self.current_lyric_label_Translate.setAlignment(Qt.AlignCenter)
        self.current_lyric_label_Translate.setStyleSheet("QLabel { font-size: 13px; }")



        self.layout.addWidget(self.current_lyric_label_Translate,alignment=QtCore.Qt.AlignLeft)
        self.layout.addWidget(self.current_lyric_label,alignment=QtCore.Qt.AlignLeft)


        # 初始化当前时间
        self.current_time = 0

        # 初始化当前歌词索引
        self.current_lyric_index = 0

        # 初始化当前歌词的进度
        self.current_lyric_progress = 0

        # 创建定时器
        # self.timer = QtCore.QTimer(self)
        # self.timer.timeout.connect(self.update_lyrics)
        # self.timer.start(100)  # 每100ms更新一次

    def update_lyrics(self):
        self.current_time += 100

        # 检查是否需要切换到下一句歌词
        if self.current_lyric_index < len(self.lyrics) and self.current_time >= self.lyrics[self.current_lyric_index]["timestamp"]:
            self.current_lyric_progress = 0
            self.current_lyric_index += 1

        # 如果当前歌词索引超出范围，停止定时器
        if self.current_lyric_index >= len(self.lyrics):
            self.timer.stop()
            return

        # 获取当前歌词
        current_lyric = self.lyrics[self.current_lyric_index - 1]
        current_lyric_Translate = self.Lyrics_Translate[self.current_lyric_index - 1]
        text = current_lyric["text"]
        text_Translate = current_lyric_Translate["text"]

        # 更新当前歌词的进度
        self.current_lyric_progress += 1

        # 逐字变色
        colored_text = ""
        for i in range(len(text)):
            if i < self.current_lyric_progress:
                colored_text += f"<span style='color:red;'>{text[i]}</span>"
            else:
                colored_text += text[i]
        colored_text_Translate = ""
        for i in range(len(text_Translate)):
            if i < self.current_lyric_progress:
                colored_text_Translate += f"<span style='color:red;'>{text_Translate[i]}</span>"
            else:
                colored_text_Translate += text_Translate[i]
        # 更新标签内容
        self.current_lyric_label.setText(colored_text)
        self.current_lyric_label_Translate.setText(colored_text_Translate)



if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Component_AudioTranslate(r"D:\M800001ziKgJ3o5Ipp.mp3")
    window.show()
    sys.exit(app.exec())