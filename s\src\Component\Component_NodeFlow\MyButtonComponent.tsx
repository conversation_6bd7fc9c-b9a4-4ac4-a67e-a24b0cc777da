// MyButtonComponent.tsx
import React, { useState,useEffect  } from 'react';
import { Button, Progress ,Switch,Input,Image,Table} from "antd";
import { NodeEditor, GetSchemes, ClassicPreset } from "rete";
import { ReactPlugin, Presets, ReactArea2D } from "rete-react-plugin";
import { Select } from 'antd';
import { Select as AntSelect } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import './custom-table.css'; // 确保引入了自定义样式文件


import {  FloatButtonGroupProps} from 'antd';
const { TextArea } = Input;

interface TableData {
  key: string;
  name: string;
  age: number;
  status: string;
}

const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Age',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
  },
];

const data: TableData[] = [
  { key: '1', name: '1', age: 28, status: 'Active' },
  { key: '2', name: '2', age: 32, status: 'Inactive' },
  { key: '3', name: '3', age: 24, status: 'Active' },
  { key: '4', name: '4', age: 35, status: 'Inactive' },
  { key: '5', name: '5', age: 29, status: 'Active' },
  { key: '6', name: '6', age: 29, status: 'Active' },
  { key: '7', name: '7', age: 29, status: 'Active' },
  { key: '8', name: '8', age: 29, status: 'Active' },
];


class Node extends ClassicPreset.Node<
  { [key in string]: ClassicPreset.Socket },
  { [key in string]: ClassicPreset.Socket },
  {
    [key in string]:
      | ButtonControl
      | ProgressControl
      | SwitchControl
      | SelectControl
      | ClassicPreset.Control
      | ClassicPreset.InputControl<"number">
      | ClassicPreset.InputControl<"text">;
  }
> {}
class Connection<
  A extends Node,
  B extends Node
> extends ClassicPreset.Connection<A, B> {}

type Schemes = GetSchemes<Node, Connection<Node, Node>>;
type AreaExtra = ReactArea2D<any>;

class ButtonControl extends ClassicPreset.Control {
  constructor(public label: string, public onClick: () => void) {
    super();
  }
}

class ProgressControl extends ClassicPreset.Control {
  constructor(public percent: number) {
    super();
  }
}

export class Textarea_Rumor_Control extends ClassicPreset.Control {
  constructor(
    public title: string, 
    public date: string, 
    public source: string, 
    public author: string, 
    public image: string, 
    public url: string, 
    public content: string, 
    // public onClick: () => void) 
    public onChange: (value: string) => void)
    {
    super();

    
  }
  setContent(Rumor: Record<string, any>) {
    const safeGet = (key: string) => Rumor?.[key] || "未知";
    this.title       = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
    this.date        = "【日期】:"+safeGet("INTELLIGENCE_WEB_TIME");
    this.source      = "【数源】:"+safeGet("INTELLIGENCE_NAME_CN");
    this.author      = "【作者】:"+safeGet("INTELLIGENCE_AUTHOR");
    this.image       = "【图片】:"+safeGet("INTELLIGENCE_TYPE");
    this.url         = "【链接】:"+safeGet("INTELLIGENCE_URL");
    this.content     = "【内容】:"+safeGet("INTELLIGENCE_CONTENT");
    // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
    // this.update(); // 触发控件更新
  }
  
}
export class TextareaControl extends ClassicPreset.Control {
  constructor(
    public label: string, 
    public value: string, 
    // public onClick: () => void) 
    public onChange: (value: string) => void)
    {
    super();
  }
}




export class Control_Preview_Score extends ClassicPreset.Control {
  constructor(
    public UUID_Source: string, 
    public Method_Type_Score: string, 
    public Direction_Theme: string, 
    public Object: string, 
    public Image: string, 
    public Bidding: string, 
    public Keyword: string, 

    // public onClick: () => void) 
    public onChange: (value: string) => void)
    {
    super();

    
  }
  setContent(Score: Record<string, any>) {
    const Score_Info = (key: string) => Score?.[key] || "未知";
    this.UUID_Source              = `【UUID】:${Score_Info("UUID")}  【数源】:${Score_Info("Source")}`;
    this.Method_Type_Score        = `【规则】:${Score_Info("Method")}  【类型】:${Score_Info("Type")}  【评分】:${Score_Info("Score")}`;
    this.Direction_Theme          = `【方向】:${Score_Info("Direction")}  【主题】:${Score_Info("Theme")}`;
    this.Object                   = `【课题】:${Score_Info("Object")}`;
    this.Image                    = `【图片】:${Score_Info("Image")}`;
    this.Bidding                  = `【中标】:${Score_Info("Bidding")}`;
    this.Keyword                  = `【关键词】:\n${Score_Info("Keyword")}`;
  
    // this.title = "【标题】:"+safeGet("INTELLIGENCE_TITLE");
    // this.update(); // 触发控件更新
  }
  
}










export class SwitchControl extends ClassicPreset.Control {
  constructor(
    public checked: boolean,
    public onChange: (checked: boolean) => void
    // public onChange: (value: number) => void,
  ) {
    super();
    console.log('SwitchControl:', this.checked); // 初始化日志
    // this.onChange(this.checked)
  }
  // 添加状态更新方法
  setChecked = (checked: boolean) => {
    console.log('[SwitchControl] 状态变更:', this.checked, '→', checked);
    this.checked = checked;
  };
  // 使用箭头函数保持this指向
  toggle = (e?: React.MouseEvent) => {
    // this.onChange(7);
    console.log('开关状态变更:')
    e?.stopPropagation();
    console.log('开关状态变更:')
    // e?.stopPropagation();
    // const newValue = !this.checked;
    // console.log('开关状态变更:', this.checked, '→', newValue);
    
    // // 状态更新逻辑
    // this.checked = newValue;
    // this.onChange(newValue); // 必须触发回调
    
    // // 调试日志
    // console.log('当前实例状态:', this.checked);
  };

    //   // 箭头函数保持this绑定
    //   toggle = (e?: React.MouseEvent) => {
    //   // this.onChange(7);
    //   console.log('--- increase方法开始执行 ---');
    //   e?.stopPropagation();
      
     
    //   console.log('--- increase方法执行结束 ---');
    // };
  
  // // 添加切换方法
  // toggle = (e?: React.MouseEvent) => {
  //   console.log('SwitchControl:')
  //   e?.stopPropagation();
  //   const newValue = !this.checked;
  //   console.log('开关状态变更:', this.checked, '→', newValue);
  //   this.checked = newValue;
  //   this.onChange(newValue);
  // };
  
}

export class NumberInputControl extends ClassicPreset.Control {
  constructor(
    public value: number,
    public min: number,
    public max: number,
    public step: number,
    // public onChange: (value: number) => void
    public onChange: (value: number) => void,
  ) {
    super();
    console.log('[INIT] 控件被创建，初始值:', this.value); // 初始化日志
    this.value =6;
  }

// 箭头函数保持this绑定
increase = (e?: React.MouseEvent) => {
  // this.onChange(7);
  console.log('--- increase方法开始执行 ---');
  e?.stopPropagation();
  
  console.log('当前值:', this.value);
  const newValue = Math.min(this.value + this.step, this.max);
  console.log('计算后的新值:', newValue);

  if (newValue !== this.value) {
    console.log('准备更新值');
    this.value = newValue;
    console.log('更新后值:', this.value);
    this.onChange(newValue);
  }
  console.log('--- increase方法执行结束 ---');
};

  decrease = (e?: React.MouseEvent) => {
    // this.onChange(7);
    e?.stopPropagation();
    
    console.log('newValue')
    const newValue = Math.max(this.value - this.step, this.min);
    console.log(newValue)
    if (newValue !== this.value) {
      this.value = newValue;
      this.onChange(newValue);
    }
  };
}



 type SelectOption<T extends string | number> = {
  value: T;
  label: string;
};

export class SelectControl<T extends string | number = string | number> 
  extends ClassicPreset.Control {
  constructor(
    public options: SelectOption<T>[],
    public selected: T,
    public onChange: (value: T) => void
  ) {
    super();
  }

// 添加更新标签的方法
  update(value: T) {
    this.selected = value;
    this.onChange(value);
  }

}



  export  function CustomButton1(props: { data: ButtonControl }) {
    return (
      <Button
        onPointerDown={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
        // onClick={props.data.onClick}
        onClick={props.data.onClick}
        style={{
            backgroundColor: '#1890ff', // 背景颜色
            color: '#fff', // 文字颜色
            border: 'none', // 去掉边框
            borderRadius: '4px', // 圆角
            padding: '8px 16px', // 内边距
            fontSize: '8px', // 字体大小
            fontWeight: 'bold', // 字体粗细
            cursor: 'pointer', // 鼠标指针样式
          }}
      >
        {props.data.label}
      </Button>
    );
  }







  // / 在 CustomProgress 后添加
  export function CustomSwitch(props: { data: SwitchControl }) {
    const [internalChecked, setInternalChecked] = React.useState(props.data.checked);
    return (
      <div style={{ 
        position: 'relative',
        display: 'inline-block',
        width: 40,
        height: 20,
        zIndex:9999,
      }}
      onPointerDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        console.log('[UI] 开关点击事件捕获');
      }}
      
      >


        <Switch
              // checked={props.data.checked}
              checked={internalChecked}
              onChange={(checked) => {
                console.log('[UI] Antd Switch 状态变更:', checked);
                // props.data.onChange(checked);
                // props.data.checked = checked; // 更新控件状态
                // 更新本地状态
                setInternalChecked(checked);
                
                // 更新控件状态
                props.data.setChecked(checked);
                
                // 触发回调
                props.data.onChange(checked);
              }}
              onClick={(e) => {
                // e.stopPropagation();
                console.log('[UI] 点击事件已处理');
              }}
            
              // onChange={(e) => {

              //   props.data.toggle();



              //   // props.data.onChange(checked);
              //   // props.data.checked = checked; // 更新控件状态
              // }}

              // onPointerDown={(e) => {
              //   e.stopPropagation();
              //   e.preventDefault();
              //   console.log('[UI] 开关点击事件触发');
              // }}
              // onClick={(e) => {
              //   // e.stopPropagation();
              //   // e.preventDefault();
              //   console.log('[UI] 开关点击事件触发');
              //   props.data.toggle(e);
              // }}



              // toggle
            />
          {/* ); */}
        {/* <input
          type="checkbox"
          checked={props.data.checked}

          // onPointerDown={(e) => e.stopPropagation()}
          // onClick={(e) => {
          //   console.log('--- CustomSwitch方法执行结束 ---');
          //   e.stopPropagation();
          //   e.preventDefault();
          //   props.data.toggle(e); // 调用正确的方法
          // }}
          // 在容器级别处理点击
          onPointerDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('[UI] 开关点击事件触发');
          }}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('[UI] 开关点击事件触发');
            props.data.toggle(e);
          }}

// Change={(e) => props.data.switched(e.target.checked)}


          style={{
            opacity: 0,
            width: 0,
            height: 0
          }}
        />
        <span style={{
          position: 'absolute',
          cursor: 'pointer',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: props.data.checked ? '#1890ff' : '#ccc',
          transition: '0.4s',
          borderRadius: 20,
          display: 'flex',
          alignItems: 'center',
          padding: 2
        }}>
          <span style={{
            display: 'block',
            width: 16,
            height: 16,
            backgroundColor: '#fff',
            borderRadius: '50%',
            transition: '0.4s',
            transform: props.data.checked ? 'translateX(20px)' : 'translateX(0)'
          }} />
        </span> */}
      </div>
    );
  }


  // await area.translate(b.id, { x: 700, y: 0 });
  

  // export function CustomSelect(props: { 
 
  // }) {
  //   const options = props.data.options.map(opt => ({
  //     value: opt.value,
  //     label: opt.label
  //   }));
  
  //   return (
  //     <Button
  //       onPointerDown={(e) => e.stopPropagation()}
  //       onDoubleClick={(e) => e.stopPropagation()}
  //       // onClick={props.data.onClick}
  //       onClick={props.data.onClick}
  //       style={{
  //           backgroundColor: '#1890ff', // 背景颜色
  //           color: '#fff', // 文字颜色
  //           border: 'none', // 去掉边框
  //           borderRadius: '4px', // 圆角
  //           padding: '8px 16px', // 内边距
  //           fontSize: '8px', // 字体大小
  //           fontWeight: 'bold', // 字体粗细
  //           cursor: 'pointer', // 鼠标指针样式
  //         }}
  //     >
  //       {props.data.label}
  //     </Button>
  //     />
  //   );
  // }


  export  function CustomSelect1<T extends string | number>(props: {  data: SelectControl<T>  }) {
  
      const options = props.data.options.map(opt => ({
        value: opt.value,
        label: opt.label
      }));
    
      return (
        <AntSelect<T>
          value={props.data.selected}
          style={{ width: 120 }}
          onChange={(value: T) => 
            {
              props.data.onChange(value);
              props.data.update(value);
              
            }
          }
          options={options}

        />
      );
  }


  export  function CustomTextArea_Rumor(props: { data: Textarea_Rumor_Control }) {
  
    // const options = props.data.options.map(opt => ({
    //   value: opt.value,
    //   label: opt.label
    // }));
  
    return (
      
      <div  style={{ background:"rgba(0,0,0,0.3)",width: '100%' ,height:'250px'}}>
        <label style={{ fontSize:"8px"}}  >{props.data.title}</label>
        <br></br>
        <label style={{ fontSize:"8px"}} >{props.data.date}</label>
        <br></br>
        <label style={{ fontSize:"8px"}} >{props.data.source}</label>
        <br></br>
        <label style={{ fontSize:"8px"}} >{props.data.author}</label>
        <br></br>
        <label style={{ fontSize:"8px"}} >{props.data.image}</label>
        <br></br>
        <label style={{ fontSize:"8px" ,whiteSpace: "normal",overflowWrap: "break-word",width: "100%",maxHeight:"10px" }} >{props.data.url}</label>
        <TextArea
          value= {props.data.content} 
          onChange={(e) => {
            const newValue = e.target.value;
            // this.value = newValue;
            // this.onChange(newValue);
          }}
          rows={8}
          style={{ background:"rgba(0,0,0,0.8)",width: '100%',color:"white" }}
        />
      </div>
    );
}





  export  function CustomTextArea(props: { data: TextareaControl }) {
  
    // const options = props.data.options.map(opt => ({
    //   value: opt.value,
    //   label: opt.label
    // }));
  
    return (
      
      <div  style={{ background:"red",width: '100%' ,height:'200px'}}>
        <label>{props.data.label}</label>
        <TextArea
          value="{this.value}{this.value}00{this.value}{this.value}{this.value}{111this.value}{this.value}{this.value}{this.value}{this.value}{this.value}{222this.value}{this.value}{this.value}{this.value}{this.value}{this.value}{this.value}{this.value222}222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222"
          onChange={(e) => {
            const newValue = e.target.value;
            // this.value = newValue;
            // this.onChange(newValue);
          }}
          rows={8}
          style={{ background:"red",width: '100%' }}
        />
      </div>
    );
}

export  function CustomImage(props: { data: TextareaControl }) {
  
  // const options = props.data.options.map(opt => ({
  //   value: opt.value,
  //   label: opt.label
  // }));

  return (
    
    <div  style={{ background:"red",width: '100%' ,height:'200px'}}>
      <label>{props.data.label}</label>
      <Image
        src='https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'
        style={{ background:"red",width: '100%' }}


      />
    </div>
  );
}


export  function CustomTable(props: { data: TextareaControl }) {
  
  // const options = props.data.options.map(opt => ({
  //   value: opt.value,
  //   label: opt.labels
  // }));
  const rowClassName = () => {
    return 'custom-table-row';
  };


  return (
    
    <div  style={{ background:"red",width: '100%' ,height:'200px', overflow: 'hidden'}}>
      <label>{props.data.label}</label>
      <Table
        columns={columns}
        dataSource={data}
        bordered
        style={{ background:"red",width: '100%' }}
        scroll={{ y: 50 }} // 设置表格的滚动高度
        rowClassName={rowClassName}
      />
    </div>
  );
}

  
  // 2. 实现React组件
  export function CustomNumberInput(props: { data: NumberInputControl }) {
    return (
      <div style={{ 
        minHeight: 12,
        maxHeight: 12,
        minWidth: 46,
        display: 'flex',
        alignItems: 'center',
        gap: 24,
        padding: 0,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
        zIndex:9999,
      }}>
        <Button
          size="small"
          
          shape="circle"
          icon={<LeftOutlined />}
          // onClick={props.data.decrease}
          onPointerDown={(e) => e.stopPropagation()}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault(); // 新增
            console.log('[UI] 减少按钮点击');
            props.data.decrease(e);
          }}
          disabled={props.data.value === props.data.min}
          style={{ fontSize: 8 ,


          }}
        />
        
        <span style={{
          minHeight: 10,
          minWidth: 46,
          textAlign: 'center',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#1890ff'
        }}>
          {props.data.value}
        </span>
  
        <Button
          size="small"
          shape="circle"
          icon={<RightOutlined />}
          onPointerDown={(e) => e.stopPropagation()}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault(); // 新增
            console.log('[UI] 增加按钮点击');
            props.data.increase(e);
          }}
          // onClick={props.data.increase}
          // onClick
          // onClick={(e) => {
          //   e.stopPropagation(); // 必须调用
          //   e.preventDefault(); // 可选添加
          //   console.log('UI点击事件触发'); // 测试日志
          //   props.data.increase(e);
          // }}
          disabled={props.data.value === props.data.max}
          style={{ fontSize: 10 }}
        />
          {/* disabled={props.data.value === props.data.max} */}
          {/* style={{ fontSize: 10 }} */}
        {/* /> */}
      </div>
    );
  }













  

  
  export  function Preview_Score(props: { data: Control_Preview_Score }) {
  
    // const options = props.data.options.map(opt => ({
    //   value: opt.value,
    //   label: opt.label
    // }));
  
    return (
      
      <div  style={{ background:"rgba(0, 0, 0, 0.1)", borderRadius: "8px",   width: '100%' ,height:'108px'}}>
        <label style={{ fontSize:"8px"}}  >{props.data.UUID_Source}</label>
        <br></br>
        <label style={{ fontSize:"8px"}}  >{props.data.Method_Type_Score}</label>
        <br></br>
        <label style={{ fontSize:"8px"}}  >{props.data.Direction_Theme}</label>
        <br></br>
        <label style={{ fontSize:"8px"}}  >{props.data.Object}</label>
        <br></br>
        <label style={{ fontSize:"8px"}}  >{props.data.Image}</label>
        <br></br>
        <label style={{ fontSize:"8px"}}  >{props.data.Bidding}</label>
        <br></br>
 
     
        <TextArea
          value= {props.data.Keyword} 
          onChange={(e) => {
            const newValue = e.target.value;
            // this.value = newValue;
            // this.onChange(newValue);
          }}
          rows={8}
          style={{ background:"rgba(0, 0, 0, 0.3)", color:"white",width: '100%', borderRadius: "3px", }}
        />
      </div>
    );
}

















  export { ButtonControl, ProgressControl,Node, Connection };


