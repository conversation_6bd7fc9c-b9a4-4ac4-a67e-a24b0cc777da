import React, { useEffect, useRef } from 'react';

// 扩展 window 类型
declare global {
  interface Window {
    onBMapCallback?: () => void;
  }
}

interface BaiduMapProps {
  containerId: string;
  ak: string;
  center?: { lng: number; lat: number };
  zoom?: number;
  onClick?: (point: { lng: number; lat: number }) => void;
}

const BaiduMap: React.FC<BaiduMapProps> = ({
  containerId,
  ak,
  center = { lng: 116.404, lat: 39.915 },
  zoom = 15,
  onClick
}) => {
  const mapRef = useRef<BMap.Map | null>(null);

  // 初始化地图
  useEffect(() => {
    const initMap = () => {
      const mapInstance = new BMap.Map(containerId);
      const point = new BMap.Point(center.lng, center.lat);
      mapInstance.centerAndZoom(point, zoom);
      (mapInstance as any).enableScrollWheelZoom(true); // 启用滚轮缩放

      // 点击事件
      if (onClick) {
        mapInstance.addEventListener('click', (e: any) => {
          onClick({ lng: e.point.lng, lat: e.point.lat });
        });
      }

      mapRef.current = mapInstance;

      return mapInstance;
    };

    if (!window.BMap) {
      const script = document.createElement('script');
      script.src = `https://api.map.baidu.com/api?v=3.0&ak=${ak}&callback=onBMapCallback`;
      script.async = true;

      window.onBMapCallback = () => {
        initMap();
      };

      document.head.appendChild(script);
    } else {
      initMap();
    }

    return () => {
      if (mapRef.current) {
        // mapRef.current.destroy(); // 如果需要销毁地图
      }
      window.onBMapCallback = undefined;
    };
  }, [ak, containerId, center, zoom, onClick]);

  return <div id={containerId} style={{ width: '100%', height: '100%' }} />;
};

export default BaiduMap;