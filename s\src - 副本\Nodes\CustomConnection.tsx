import * as React from "react";
import { ClassicScheme, Presets } from "rete-react-plugin";
import styled, { keyframes } from "styled-components";
const { useConnection } = Presets.classic;




// 定义虚线流动动画
const dashAnimation = keyframes`
  from {
    stroke-dashoffset: 30;
  }
  to {
    stroke-dashoffset: 0;
  }
`;







const Svg = styled.svg`
  overflow: visible !important;
  position: absolute;
  pointer-events: none;
  width: 9999px;
  height: 9999px;
`;


const Path = styled.path<{ styles?: (props: any) => any }>`
  fill: none;
  stroke-width: 2px;
  stroke:rgb(249, 249, 250); /* 蓝色线条 */
  stroke-dasharray: 20, 15; /* 10px虚线，5px间隙 */
  animation: ${dashAnimation} 1s linear infinite;
  pointer-events: auto;
  ${(props) => props.styles && props.styles(props)}
`;

export function CustomConnection(props: {
  data: ClassicScheme["Connection"] & { isLoop?: boolean };
  styles?: () => any;
}) {
  const { path } = useConnection();

  if (!path) return null;

  return (
    <Svg data-testid="connection">
      <Path styles={props.styles} d={path} />
    </Svg>
  );
}