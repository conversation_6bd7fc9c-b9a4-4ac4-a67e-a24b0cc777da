import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Checkbox, 
  Row, 
  Col, 
  Space, 
  Typography, 
  List,
  Pagination,
  message,
  Spin,
  Empty,
  Tooltip
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined,
  ReloadOutlined,
  FireOutlined
} from '@ant-design/icons';
import { useServiceRequests } from '@/Core/Core_Control';
import '../Styles/Page_Search_Display.css';

const { Title, Text } = Typography;
const { Search } = Input;

// 数据类型定义
interface SearchResult {
  id: string | number;
  title?: string;
  TITLE?: string;
  content?: string;
  CONTENT_SHOW?: string;
  source?: string;
  PLATFORM?: string;
  platform?: string;
  PLATFORM_TYPE?: string;
  author?: string;
  AUTHOR?: string;
  publishTime?: string;
  TIME?: string;
  emotion?: string;
  DATA_EMOTION?: string;
  url?: string;
  URL?: string;
  keyword?: string[];
  KEYWORD?: string[];
  region_area?: string;
  REGION_AREA?: string;
}

interface HotNotice {
  id: string;
  content: string;
  type: number;
  url: string;
  level: string;
}

interface EmotionCount {
  all: number;
  positive: number;
  neutral: number;
  negative: number;
}

const Page_Search_Display: React.FC = () => {
  // 初始化请求服务
  const { AsyncTokenRequests } = useServiceRequests();
  
  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [hotNotices, setHotNotices] = useState<HotNotice[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [emotionFilter, setEmotionFilter] = useState<string[]>(['All']);
  const [emotionCounts, setEmotionCounts] = useState<EmotionCount>({
    all: 0,
    positive: 0,
    neutral: 0,
    negative: 0
  });
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  // 初始化数据
  useEffect(() => {
    loadHotNotices();
  }, []);

  // 加载热点动态
  const loadHotNotices = async () => {
    try {
      const requestsData = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_search_hotnotice',
        data_argument: {},
        data_kwargs: {}
      };
      const result = await AsyncTokenRequests(requestsData);
      if (result.Status === 'Success') {
        // 转换API返回的数据格式为组件需要的格式
        const hotNoticesData: HotNotice[] = result.Hot_Notice_List.map((item: any) => ({
          id: item.Id || item.id,
          content: item.Content,
          type: item.Type,
          url: item.Url,
          level: `level${item.Type}`
        }));
        setHotNotices(hotNoticesData);
      } else {
        message.error('加载热点动态失败');
      }
    } catch (error) {
      message.error('加载热点动态失败');
    }
  };

  // 执行搜索
  const handleSearch = async (keyword?: string) => {
    const searchTerm = keyword || searchKeyword;
    if (!searchTerm.trim()) {
      message.warning('请输入搜索关键词');
      return;
    }

    setLoading(true);
    try {
      const requestsData: {
        user_id: string;
        user_token: string;
        data_class: string;
        data_type: string;
        data_methods: string;
        data_argument: Record<string, any>;
        data_kwargs: Record<string, any>;
      } = {
        user_id: '',
        user_token: '',
        data_class: 'Sentiment',
        data_type: 'Service',
        data_methods: 'return_search_sentiment',
        data_argument: {},
        data_kwargs: {
          Search_Info: searchTerm,
          Page_Size: pageSize,
          Page_current: currentPage,
          Emotion_Type: emotionFilter
        }
      };
      const result = await AsyncTokenRequests(requestsData);
      if (result.Status === 'Success') {
        // 解析数据，兼容后端返回字段
        const list = Array.isArray(result.Sentiment_Info) ? result.Sentiment_Info : [];
        setSearchResults(list.map((item: any, idx: number) => ({
          id: item.UUID || item.Id || idx.toString(),
          title: item.TITLE || item.Title || '',
          content: item.CONTENT_SHOW || item.Content || '',
          source: item.PLATFORM || item.Source || '',
          author: item.AUTHOR || item.Author || '',
          publishTime: item.TIME || item.PublishTime || '',
          emotion: item.DATA_EMOTION || item.Emotion || '',
          url: item.URL || item.Url || ''
        })));
        setTotalCount(list.length);
        setEmotionCounts({
          all: result.Table_Condiction_Param_Count?.Emotion_Type_1 || list.length,
          positive: result.Table_Condiction_Param_Count?.Emotion_Type_2 || 0,
          neutral: result.Table_Condiction_Param_Count?.Emotion_Type_3 || 0,
          negative: result.Table_Condiction_Param_Count?.Emotion_Type_4 || 0
        });
        setShowResults(true);
      } else {
        message.error(result.Error || '搜索失败，请重试');
      }
    } catch (error) {
      message.error('搜索失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理情感筛选
  const handleEmotionFilter = (checkedValues: any) => {
    setEmotionFilter(checkedValues);
  };

  // 处理分页
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
    handleSearch();
  };

  // 下载全部结果
  const handleDownloadAll = () => {
    message.success('开始下载全部搜索结果');
  };

  // 刷新热点动态
  const handleRefreshHotNotices = () => {
    loadHotNotices();
    message.success('热点动态已刷新');
  };

  // 点击热点新闻
  const handleHotNoticeClick = (notice: HotNotice) => {
    window.open(notice.url, '_blank');
  };

  return (
    <div className="search-display-page">
      <Row justify="center">
        <Col xs={24} lg={20}>
          {/* 搜索卡片 */}
          <Card className="search-card">
            <div className="search-header">
              <Title level={1} className="search-title">
                元引擎搜索
              </Title>
            </div>
            
            <div className="search-input-container">
              <Search
                placeholder="请输入搜索关键词,多个关键词之间空格隔开(如:杭州 2022 亚运会)"
                size="large"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onSearch={handleSearch}
                enterButton="搜索一下"
                loading={loading}
                className="search-input"
              />
            </div>

            {/* 搜索结果区域 */}
            {showResults && (
              <div className="search-results-section">
                {/* 筛选和统计 */}
                <Row className="results-toolbar">
                  <Col span={16}>
                    <Space align="center">
                      <Text strong>情感属性:</Text>
                      <Checkbox.Group
                        value={emotionFilter}
                        onChange={handleEmotionFilter}
                      >
                        <Space wrap>
                          <Checkbox value="All">
                            全部({emotionCounts.all})
                          </Checkbox>
                          <Checkbox value="正面">
                            正面({emotionCounts.positive})
                          </Checkbox>
                          <Checkbox value="中性">
                            中性({emotionCounts.neutral})
                          </Checkbox>
                          <Checkbox value="负面">
                            负面({emotionCounts.negative})
                          </Checkbox>
                        </Space>
                      </Checkbox.Group>
                    </Space>
                  </Col>
                  <Col span={8}>
                    <div className="results-actions">
                      <Space>
                        <Text>共{totalCount}条消息</Text>
                        <Button 
                          size="small" 
                          icon={<DownloadOutlined />}
                          onClick={handleDownloadAll}
                        >
                          全部下载
                        </Button>
                      </Space>
                    </div>
                  </Col>
                </Row>

                {/* 搜索结果列表 */}
                <div className="search-results-list">
                  {loading ? (
                    <div className="loading-container">
                      <Spin size="large" />
                    </div>
                  ) : (
                    <div>
                      <List
                        dataSource={searchResults}
                        renderItem={(item) => (
                          <List.Item className="search-result-item">
                            <div className="result-card">
                              <div className="result-header">
                                <a 
                                  href={item.url || item.URL} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="result-title"
                                >
                                  {item.title || item.TITLE}
                                </a>
                                <span className={`emotion-tag ${((item.emotion || item.DATA_EMOTION) === '正面') ? 'positive' : ((item.emotion || item.DATA_EMOTION) === '负面') ? 'negative' : 'neutral'}`}>{item.emotion || item.DATA_EMOTION}</span>
                              </div>
                              {(item.keyword || item.KEYWORD) && Array.isArray(item.keyword || item.KEYWORD) && (item.keyword || item.KEYWORD).length > 0 && (
                                <div className="result-keywords">
                                  {(item.keyword || item.KEYWORD).map((kw: string, idx: number) => (
                                    <span className="keyword-tag" key={idx}>{kw}</span>
                                  ))}
                                </div>
                              )}
                              <div className="result-meta">
                                <span>{item.platform || item.PLATFORM}</span>
                                <span>{item.author || item.AUTHOR}</span>
                                <span>{item.region_area || item.REGION_AREA}</span>
                                <span>{item.publishTime || item.TIME}</span>
                              </div>
                              <div className="result-body">
                                <span>{item.content || item.CONTENT_SHOW}</span>
                                <a href={item.url || item.URL} target="_blank" rel="noopener noreferrer" className="detail-link">查看详情</a>
                              </div>
                            </div>
                          </List.Item>
                        )}
                      />
                      <Pagination
                        total={totalCount}
                        pageSize={pageSize}
                        current={currentPage}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                        onChange={handlePageChange}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 热点动态 */}
          <div className="hot-notices-section">
            <div className="hot-notices-header">
              <Title level={5}>热点动态</Title>
              <Button 
                size="small" 
                icon={<ReloadOutlined />}
                onClick={handleRefreshHotNotices}
              >
                换一换
              </Button>
            </div>
            
            <div className="hot-notices-list">
                    <div className="notice-content">
                      <FireOutlined className="notice-icon" />
                      <Text ellipsis={{ tooltip: notice.content }}>
                        {notice.content}
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Page_Search_Display;
