import { NodeEditor, GetSchemes, ClassicPreset} from "rete";
import { Node_Socket,SystemControl_Algorithm_Sampling} from "./Node_System_Control_Algorithm_Sampling";

export class Node_System_Component_Algorithm_Sampling extends ClassicPreset.Node<
   { [key in string]: ClassicPreset.Socket },
   { [key in string]: ClassicPreset.Socket },
   { [key in string]:| SystemControl_Algorithm_Sampling}> 
  {
    width  = 450;
    height = 600
    constructor(Label: string,) {
      super(Label);

      this.addInput("model", new ClassicPreset.Input(Node_Socket, "model"));
      this.addInput("lantent", new ClassicPreset.Input(Node_Socket, "lantent"));
      this.addInput("xyPlot", new ClassicPreset.Input(Node_Socket, "xyPlot"));
      this.addInput("image", new ClassicPreset.Input(Node_Socket, "image"));
        

      this.addOutput("image", new ClassicPreset.Output(Node_Socket, "image"),);
      this.addOutput("vae", new ClassicPreset.Output(Node_Socket, "vae"),);
      this.addOutput("clip", new ClassicPreset.Output(Node_Socket, "clip"),);
      this.addOutput("seed", new ClassicPreset.Output(Node_Socket, "seed"),);

      const ConentControl = new SystemControl_Algorithm_Sampling(
        '【标题】:未知', // Label for the text area
        '0',
        '0',
        '1',

        (title) => {
          console.log('TextArea value changed:', title);
        }
      );
      
      this.addControl("Conent",  ConentControl);

    };
    updateContent(Config:Record<string, any>){
      let ContentControl = this.controls.Conent;
      ContentControl.setContent(Config)
      console.log('Config:', Config);
    }

    data() {
      return {}; // 根据实际情况返回数据结构
    };
  
    execute(_: never, forward: (output: "exec") => void) {
      forward("exec");
    };
  }
    