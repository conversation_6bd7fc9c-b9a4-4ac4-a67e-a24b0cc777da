import React, { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    BMapGL: any;
    L: any;
  }
}

interface MapDataPoint {
  name: string;
  value: number;
  lng: number;
  lat: number;
}

interface SimpleMapChartProps {
  data?: MapDataPoint[];
  height?: string;
  showLabel?: boolean;
}

const SimpleMapChart: React.FC<SimpleMapChartProps> = ({
  data = [],
  height = '400px',
  showLabel = true
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstance = useRef<any>(null);
  const [mapStatus, setMapStatus] = useState<string>('正在加载地图...');

  useEffect(() => {
    // 检测网络连接状态
    const checkNetworkAndLoadMap = () => {
      if (!navigator.onLine) {
        console.log('网络连接不可用，显示备用地图');
        setMapStatus('网络连接不可用，显示简化地图');
        showFallbackMap();
        return;
      }

      console.log('网络连接正常，开始加载地图...');
      setMapStatus('正在加载百度地图...');
      loadMaps();
    };

    const showFallbackMap = () => {
      if (!mapRef.current) return;

      // 清理之前的内容
      mapRef.current.innerHTML = '';

      // 城市位置映射表
      const cityPositions: { [key: string]: { x: number; y: number } } = {
        '北京': { x: 400, y: 120 },
        '上海': { x: 450, y: 180 },
        '广州': { x: 350, y: 280 },
        '深圳': { x: 355, y: 285 },
        '杭州': { x: 430, y: 190 },
        '南京': { x: 420, y: 170 },
        '成都': { x: 280, y: 220 },
        '重庆': { x: 300, y: 240 },
        '武汉': { x: 370, y: 200 },
        '西安': { x: 320, y: 180 },
        '天津': { x: 405, y: 125 },
        '苏州': { x: 435, y: 185 },
        '青岛': { x: 390, y: 150 },
        '大连': { x: 420, y: 110 },
        '厦门': { x: 410, y: 260 },
        '福州': { x: 415, y: 250 },
        '济南': { x: 385, y: 155 },
        '郑州': { x: 365, y: 175 },
        '长沙': { x: 360, y: 230 },
        '昆明': { x: 260, y: 260 },
        '南昌': { x: 395, y: 220 },
        '合肥': { x: 405, y: 185 },
        '太原': { x: 365, y: 145 },
        '石家庄': { x: 375, y: 140 },
        '哈尔滨': { x: 430, y: 80 },
        '长春': { x: 425, y: 95 },
        '沈阳': { x: 415, y: 105 },
        '呼和浩特': { x: 360, y: 110 },
        '乌鲁木齐': { x: 200, y: 120 },
        '兰州': { x: 280, y: 160 },
        '银川': { x: 310, y: 140 },
        '西宁': { x: 260, y: 150 },
        '拉萨': { x: 220, y: 200 },
        '海口': { x: 320, y: 320 },
        '三亚': { x: 320, y: 330 }
      };

      // 使用传入的数据或默认数据
      const cities = data.length > 0 ? data.map(item => ({
        name: item.name,
        count: item.value,
        x: cityPositions[item.name]?.x || (Math.random() * 400 + 100),
        y: cityPositions[item.name]?.y || (Math.random() * 200 + 100)
      })) : [
        { name: '北京', count: 1250, x: 400, y: 120 },
        { name: '成都', count: 890, x: 280, y: 220 },
        { name: '广州', count: 1100, x: 350, y: 280 },
        { name: '上海', count: 1350, x: 450, y: 180 }
      ];

      const maxCount = Math.max(...cities.map(c => c.count));

      mapRef.current.innerHTML = `
        <div style="
          position: relative;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <svg width="600" height="350" viewBox="0 0 600 350" style="max-width: 100%; max-height: 100%;">
            <!-- 中国地图轮廓 -->
            <path d="M150,80 L200,60 L250,70 L300,65 L350,70 L400,75 L450,80 L480,90 L500,110 L510,130 L520,150 L515,170 L500,190 L480,210 L460,230 L440,250 L420,270 L400,280 L380,285 L360,290 L340,285 L320,280 L300,275 L280,270 L260,260 L240,250 L220,240 L200,220 L180,200 L160,180 L150,160 L145,140 L148,120 L150,100 Z"
                  fill="rgba(255,255,255,0.1)"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="2"/>

            <!-- 台湾 -->
            <ellipse cx="480" cy="260" rx="15" ry="25"
                     fill="rgba(255,255,255,0.1)"
                     stroke="rgba(255,255,255,0.3)"
                     stroke-width="1"/>

            <!-- 海南 -->
            <ellipse cx="320" cy="300" rx="12" ry="18"
                     fill="rgba(255,255,255,0.1)"
                     stroke="rgba(255,255,255,0.3)"
                     stroke-width="1"/>

            <!-- 城市标记点 -->
            ${cities.map(city => {
              const size = Math.max(6, (city.count / maxCount) * 16);
              return `
                <g>
                  <circle cx="${city.x}" cy="${city.y}" r="${size}"
                          fill="#1f77b4"
                          stroke="rgba(31, 119, 180, 0.5)"
                          stroke-width="2"
                          opacity="0.9">
                    <animate attributeName="r"
                             values="${size};${size + 4};${size}"
                             dur="2s"
                             repeatCount="indefinite"/>
                  </circle>
                  <text x="${city.x}" y="${city.y + size + 15}"
                        text-anchor="middle"
                        fill="rgba(255,255,255,0.8)"
                        font-size="11"
                        font-family="Arial, sans-serif">
                    ${city.name}
                  </text>
                  <text x="${city.x}" y="${city.y + size + 28}"
                        text-anchor="middle"
                        fill="#1f77b4"
                        font-size="10"
                        font-family="Arial, sans-serif">
                    ${city.count}条
                  </text>
                </g>
              `;
            }).join('')}

            <!-- 标题 -->
            <text x="300" y="30"
                  text-anchor="middle"
                  fill="rgba(255,255,255,0.6)"
                  font-size="14"
                  font-family="Arial, sans-serif">
              舆情发布地区分布
            </text>
          </svg>
        </div>
      `;
    };

    const initMap = () => {
      if (!mapRef.current) return;

      setMapStatus('百度地图加载成功！');

      // 清理之前的地图实例
      if (mapInstance.current) {
        try {
          if (mapInstance.current.destroy) {
            mapInstance.current.destroy();
          }
        } catch (e) {
          console.log('清理地图实例时出错:', e);
        }
        mapInstance.current = null;
      }

      // 检查BMapGL是否可用
      if (!window.BMapGL || !window.BMapGL.Map) {
        console.log('BMapGL未完全加载，使用备用方案');
        showFallbackMap();
        return;
      }

      try {
        // 创建地图实例
        const map = new window.BMapGL.Map(mapRef.current);
        mapInstance.current = map;

        // 初始化地图，设置中心点坐标和地图级别
        map.centerAndZoom(new window.BMapGL.Point(116.404, 39.915), 4);

        // 开启鼠标滚轮缩放
        map.enableScrollWheelZoom(true);

        // 设置地图样式为深色主题（可选）
        try {
          map.setMapStyleV2({
            styleId: 'e5b6b8b5b8b5b8b5b8b5b8b5b8b5b8b5'
          });
        } catch (e) {
          // 如果样式设置失败，使用默认样式
          console.log('地图样式设置失败，使用默认样式');
        }

        // 城市数据
        const cities = [
          { name: '北京市', point: [116.408293, 39.912057], count: 1250 },
          { name: '成都市', point: [104.071595, 30.661785], count: 890 },
          { name: '广州市', point: [113.262652, 23.133429], count: 1100 },
          { name: '上海市', point: [121.474216, 31.23173], count: 1350 }
        ];

        // 创建点标记
        cities.forEach((city) => {
          const marker = new window.BMapGL.Marker(
            new window.BMapGL.Point(city.point[0], city.point[1])
          );

          // 添加标记到地图
          map.addOverlay(marker);

          // 创建信息窗口
          const infoWindow = new window.BMapGL.InfoWindow(
            `<div style="color: #333; padding: 5px;">
              <strong>${city.name}</strong><br/>
              舆情数量: ${city.count} 条
            </div>`,
            {
              width: 200,
              height: 80
            }
          );

          // 点击标记显示信息窗口
          marker.addEventListener('click', () => {
            map.openInfoWindow(infoWindow, new window.BMapGL.Point(city.point[0], city.point[1]));
          });
        });

      } catch (error) {
        console.error('地图初始化失败:', error);
        showFallbackMap();
      }
    };

    // 加载Leaflet地图
    const loadLeafletMap = () => {
      if (window.L) {
        initLeafletMap();
        return;
      }

      // 检查是否已经有Leaflet脚本
      const existingScript = document.querySelector('script[src*="leaflet"]');
      if (existingScript) {
        setTimeout(() => {
          if (window.L) {
            initLeafletMap();
          } else {
            console.log('Leaflet脚本已存在但未完全加载');
          }
        }, 1000);
        return;
      }

      // 加载Leaflet CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      document.head.appendChild(cssLink);

      // 加载Leaflet JS
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
      script.async = true;
      script.onload = () => {
        console.log('Leaflet地图API加载成功');
        setTimeout(() => {
          if (window.L) {
            initLeafletMap();
          } else {
            console.log('Leaflet API加载完成但对象未就绪');
          }
        }, 100);
      };
      script.onerror = () => {
        console.log('Leaflet地图加载失败，可能是网络问题');
      };
      document.head.appendChild(script);
    };

    const initLeafletMap = () => {
      if (!mapRef.current) return;

      setMapStatus('Leaflet地图加载成功！');

      // 清理之前的地图实例
      if (mapInstance.current) {
        try {
          if (mapInstance.current.remove) {
            mapInstance.current.remove();
          }
        } catch (e) {
          console.log('清理Leaflet地图实例时出错:', e);
        }
        mapInstance.current = null;
      }

      // 清理DOM内容
      mapRef.current.innerHTML = '';

      try {
        // 创建地图实例
        const map = window.L.map(mapRef.current, {
          center: [35.8617, 104.1954], // 中国中心点
          zoom: 5,
          zoomControl: true,
          attributionControl: false
        });

        mapInstance.current = map;

        // 添加地图图层 - 优先使用高德地图，备用OpenStreetMap
        try {
          // 尝试使用高德地图（更适合中国）
          window.L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
            subdomains: ['1', '2', '3', '4'],
            attribution: '© 高德地图'
          }).addTo(map);
        } catch (e) {
          // 备用方案：使用OpenStreetMap
          window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          }).addTo(map);
        }

        // 城市数据
        const cities = [
          { name: '北京市', lat: 39.9042, lng: 116.4074, count: 1250 },
          { name: '成都市', lat: 30.5728, lng: 104.0668, count: 890 },
          { name: '广州市', lat: 23.1291, lng: 113.2644, count: 1100 },
          { name: '上海市', lat: 31.2304, lng: 121.4737, count: 1350 }
        ];

        // 添加城市标记
        cities.forEach((city) => {
          const marker = window.L.circleMarker([city.lat, city.lng], {
            radius: Math.max(8, city.count / 100),
            fillColor: '#1f77b4',
            color: '#ffffff',
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8
          }).addTo(map);

          // 添加弹出窗口
          marker.bindPopup(`
            <div style="text-align: center;">
              <strong>${city.name}</strong><br/>
              舆情数量: <span style="color: #1f77b4;">${city.count}</span> 条
            </div>
          `);

          // 添加标签
          window.L.marker([city.lat, city.lng], {
            icon: window.L.divIcon({
              className: 'city-label',
              html: `<div style="
                background: rgba(0,0,0,0.7);
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 11px;
                white-space: nowrap;
                text-align: center;
              ">${city.name}<br/>${city.count}条</div>`,
              iconSize: [60, 30],
              iconAnchor: [30, -10]
            })
          }).addTo(map);
        });

      } catch (error) {
        console.error('Leaflet地图初始化失败:', error);
        showFallbackMap();
      }
    };

    // 动态加载百度地图API
    const loadBaiduMap = () => {
      if (window.BMapGL && window.BMapGL.Map) {
        initMap();
        return;
      }

      // 检查是否已经有百度地图脚本
      const existingScript = document.querySelector('script[src*="api.map.baidu.com"]');
      if (existingScript) {
        setTimeout(() => {
          if (window.BMapGL && window.BMapGL.Map) {
            initMap();
          } else {
            console.log('百度地图API已存在但未完全加载');
          }
        }, 2000);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://api.map.baidu.com/api?type=webgl&v=1.0&ak=omWfiGOTUKQ8m2D9SNjoirWDmGjEOObg';
      script.async = true;
      script.onload = () => {
        console.log('百度地图API加载成功');
        setTimeout(() => {
          if (window.BMapGL && window.BMapGL.Map) {
            initMap();
          } else {
            console.log('百度地图API加载完成但对象未就绪');
          }
        }, 1000);
      };
      script.onerror = () => {
        console.log('百度地图API加载失败，可能是网络问题或API密钥问题');
      };
      document.head.appendChild(script);
    };

    const loadMaps = () => {
      // 优先尝试加载真实地图
      setTimeout(() => {
        // 首先尝试百度地图
        loadBaiduMap();

        // 如果百度地图失败，尝试Leaflet
        setTimeout(() => {
          if (!mapInstance.current) {
            setMapStatus('百度地图加载失败，尝试Leaflet地图...');
            loadLeafletMap();
          }
        }, 3000);

        // 最后的备用方案
        setTimeout(() => {
          if (!mapInstance.current) {
            setMapStatus('地图API加载失败，显示简化地图');
            showFallbackMap();
          }
        }, 6000);
      }, 100);
    };

    // 开始加载地图
    checkNetworkAndLoadMap();

    // 清理函数
    return () => {
      if (mapInstance.current) {
        try {
          if (mapInstance.current.remove) {
            mapInstance.current.remove(); // Leaflet
          } else if (mapInstance.current.destroy) {
            mapInstance.current.destroy(); // 百度地图
          }
        } catch (error) {
          console.log('地图销毁时出错:', error);
        }
        mapInstance.current = null;
      }
    };
  }, [data]);

  return (
    <div style={{ position: 'relative', width: '100%', height: height }}>
      {/* 状态显示 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        zIndex: 1000,
        background: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        {mapStatus}
      </div>

      {/* 地图容器 */}
      <div
        ref={mapRef}
        style={{
          width: '100%',
          height: height,
          backgroundColor: '#1a1a2e',
          borderRadius: '8px'
        }}
      />
    </div>
  );
};

export default SimpleMapChart;
