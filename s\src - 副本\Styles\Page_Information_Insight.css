/* 信息洞察页面样式 */
.information-insight-page {
  padding: 24px;
  min-height: 100vh;
}

.information-insight-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.information-insight-page .ant-tabs-tab {
  font-weight: 500;
}

.information-insight-page .ant-tabs-tab-active {
  font-weight: 600;
}

/* 账号卡片样式 */
.information-insight-page .ant-card-small {
  margin-bottom: 16px;
}

.information-insight-page .ant-card-small:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* 表格样式 */
.information-insight-page .ant-table-thead > tr > th {
  font-weight: 600;
}

.information-insight-page .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 时间线样式 */
.information-insight-page .ant-timeline-item-content {
  margin-left: 16px;
}

.information-insight-page .ant-timeline-item-content .ant-card {
  margin-bottom: 0;
}

/* 标签样式 */
.information-insight-page .ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 搜索框样式 */
.information-insight-page .ant-input-search {
  border-radius: 6px;
}

/* 复选框组样式 */
.information-insight-page .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.information-insight-page .ant-checkbox-wrapper {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .information-insight-page {
    padding: 16px;
  }

  .information-insight-page .ant-col {
    margin-bottom: 16px;
  }
}
